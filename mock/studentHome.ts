import Mock from 'mockjs';
import { MockMethod } from 'vite-plugin-mock';

export default [
  {
    url: '/api/student/HomeGraduate/list',
    method: 'get',
    response: () => ({
      code: 200,
      message:'success',
      data: {
        ...Mock.mock({
          'list|1-1': [
            {
              gid: '1',
              goalsName: '毕业要求1',
              grades: 30,
            },
            {
              gid: '2',
              goalsName: '毕业要求2',
              grades: 69,
            },
            {
              gid: '3',
              goalsName: '毕业要求3',
              grades: 77,
            },
            {
              gid: '4',
              goalsName: '毕业要求4',
              grades: 10,
            },
            {
              gid: '5',
              goalsName: '毕业要求5',
              grades: 89,
            },
            {
              gid: '6',
              goalsName: '毕业要求6',
              grades: 22,
            },
            {
              gid: '7',
              goalsName: '毕业要求7',
              grades: 56,
            },
            {
              gid: '8',
              goalsName: '毕业要求8',
              grades: 100,
            },
            {
              gid: '9',
              goalsName: '毕业要求9',
              grades: 38,
            },
            {
              gid: '10',
              goalsName: '毕业要求10',
              grades: 61,
            },
            {
              gid: '11',
              goalsName: '毕业要求11',
              grades: 88,
            },
            {
              gid: '12',
              goalsName: '毕业要求12',
              grades: 10,
            },
          ],
        }),
      },
    }),
  },
  {
    url: '/api/student/HomeCourse/list',
    method: 'get',
    response: () => ({
      code: 200,
      message:'success',
      data: {
        ...Mock.mock({
          'list|1-1': [
            {
              cid: 1,
              courseName: '道德',
              semester: '第一学期',
              kid:2,
              type:'思想品德',
                sid:1,
            },
            {
              cid: 2,
              courseName: '思想',
              semester: '第二学期',
              kid:0,
              type:'思想品德',
                sid:2,
            },
            {
              cid: 3,
              courseName: 'Java',
              semester: '第三学期',
              kid:0,
              type:'技术',
                sid:3,
            },
            {
              cid: 4,
              courseName: 'C语言',
              semester: '第一学期',
              kid:5,
              type:'技术',
                sid:1,
            },
            {
              cid: 5,
              courseName: 'spark',
              semester: '第二学期',
              kid:3,
              type:'基础技术',
                sid:2
            },
            {
              cid: 6,
              courseName: 'hadoop',
              semester: '第一学期',
              kid:7,
              type:'进阶技术',
                sid:1,
            },
            {
              cid: 7,
              courseName: 'unity',
              semester: '第二学期',
              kid:8,
              type:'实践能力',
                sid:2,
            },
            {
              cid: 8,
              courseName: 'scala',
              semester: '第五学期',
              kid:0,
              type:'进阶技术',
                sid:5
            },

          ],
        }),
      },
    }),
  },
  {
    url: '/api/student/HomeType/list',
    method: 'get',
    response: () => ({
      code: 200,
      message:'success',
      data: {
        ...Mock.mock({
          'list|1-1': [
            {
              type:'思想品德',
            },
            {
              type:'技术'
            },
            {
              type:'基础技术'
            },
            {
              type:'进阶技术'
            },
            {
              type:'实践能力'
            },
          ],
        }),
      },
    }),
  },
  {
    url: '/api/student/HomeInformation/list',
    method: 'get',
    response: () => ({
      code: 200,
      message:'success',
      data: {
        ...Mock.mock({
          'list|1-1': [
            {
              totalCredits:172,
              revisedCredits:79,
              number:32,
              revisedNumber:3,
              requiredCredits:132,
              revisedRequiredCredits:54,
              semester:'第二学期',
              sid:2,
            },
          ],
        }),
      },
    }),
  },
] as MockMethod[];

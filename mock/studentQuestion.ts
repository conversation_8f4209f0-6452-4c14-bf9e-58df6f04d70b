import Mock from 'mockjs';
import { MockMethod } from 'vite-plugin-mock';

export default [
  {
    url: '/api/student/questionList/list',
    method: 'get',
    response: () => {
      let list = [];
      list = Mock.mock({
        'list|1-1': [
          {
            pid: 1,
            paperName: '第一章测试',
            courseName: '道德',
            cid: 1,
            teacher: '王老师',
            released: '2025年04月14号',
            startTime: '2025年04月16号09:00:00',
            endTime: '2025年04月20号23:59:59',
            status: 0
          },
          {
            pid: 2,
            paperName: '第二章测试',
            courseName: '道德',
            cid: 1,
            teacher: '王老师',
            released: '2025年04月10号',
            startTime: '2025年04月12号08:00:00',
            endTime: '2025年04月14号23:59:59',
            status: 0
          },
          {
            pid: 3,
            paperName: '基础数据',
            courseName: 'Java',
            cid: 3,
            teacher: '李老师',
            released: '2025-04-13',
            startTime: '2025-04-15T00:00:00',
            endTime: '2025-04-17T23:59:59',
            status: 1
          },
          {
            pid: 4,
            paperName: '基本数据类型',
            courseName: 'C语言',
            cid: 4,
            teacher: '张老师',
            released: '2025-04-15',
            startTime: '2024-',
            endTime: '',
            status: 1
          },
          {
            pid: 5,
            paperName: '代码作业',
            courseName: 'C语言',
            cid: 4,
            teacher: '张老师',
            released: '2025-04-01',
            startTime: '2025-04-05T00:00:00',
            endTime: '2025-04-14T23:59:59',
            status:1
          },
        ],
      }).list;
      return {
        code: 200,
        message:'success',
        data: { list },
      };
    },
  },
  {
    url: '/api/student/questionnaire/list',
    method: 'get',
    response: ({query}) => {
      const pid = query.pid
      let list = [];
      if (pid==='1'){
        list = Mock.mock({
          'list|1-1': [
            {
              id: 1,
              type: 1,
              title: '您的年龄范围是？',
              options: ['18岁以下', '18-25岁', '26-35岁', '36岁以上'],
              required: true
            },
            {
              id: 4,
              type: 1,
              title: '您目前所在的年级是？',
              options: ['大一', '大二', '大三', '大四', '研究生'],
              required: true
            },
            {
              id: 2,
              type: 2,
              title: '您通常通过哪些渠道获取信息？（多选）',
              options: ['社交媒体', '新闻网站', '朋友推荐', '电视广告', '学术期刊'],
              required: true
            },
            {
              id: 3,
              type: 3,
              title: '您对我们的产品有什么建议？',
              required: false
            },
            {
              id: 5,
              type: 1,
              title: '您平均每天的学习时长是？',
              options: ['1小时以下', '1-3小时', '3-5小时', '5小时以上'],
              required: true
            },
            {
              id: 6,
              type: 2,
              title: '您最希望提升的学术能力有哪些？（多选）',
              options: ['论文写作', '数据分析', '编程技能', '外语能力', '批判性思维'],
              required: false
            },
            {
              id: 7,
              type: 3,
              title: '描述一次您印象深刻的校园活动经历',
              required: true
            },
            {
              id: 8,
              type: 1,
              title: '您更倾向于哪种学习方式？',
              options: ['自主学习', '小组协作', '教师讲授', '实践操作'],
              required: true
            },
            {
              id: 9,
              type: 2,
              title: '您选择课外活动时最看重哪些因素？（多选）',
              options: ['个人兴趣', '职业发展', '社交机会', '学校要求', '活动奖励'],
              required: true
            },
            {
              id: 10,
              type: 3,
              title: '您对未来职业发展的具体规划是什么？',
              required: false
            }
          ],
        }).list;
      }
      return {
        code: 200,
        message:'success',
        data: { list },
      };
    },
  },
  {
    url: '/api/student/replied/list',
    method: 'post',
    response: () => ({
      code: 200,
      message: 'success'
    }),
  },
] as MockMethod[];

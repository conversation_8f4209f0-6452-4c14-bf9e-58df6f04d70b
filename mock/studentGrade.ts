import Mock from 'mockjs';
import { MockMethod } from 'vite-plugin-mock';

export default [
  {
    url: '/api/student/grade/list',
    method: 'get',
    response: () => {;
      let list = [];
      list = Mock.mock({
        'list|1-1': [
          {
            cid: 1,
            name: '高等数学',
            credit: 4,
            usualGrades: 35,
            finalGrades: 58,
            totalScore: 93,
            semester: '第一学期'
          },
          {
            cid: 2,
            name: '大学物理',
            credit: 3,
            usualGrades: 40,
            finalGrades: 46,
            totalScore: 86,
            semester: '第一学期'
          },
          {
            cid: 3,
            name: '数据结构',
            credit: 3,
            usualGrades: 36,
            finalGrades: 44,
            totalScore: 80,
            semester: '第二学期'
          },
          {
            cid: 4,
            name: '操作系统',
            credit: 4,
            usualGrades: 43,
            finalGrades: 45,
            totalScore: 89,
            semester: '第三学期'
          },
          {
            cid: 5,
            name: '计算机网络',
            credit: 3,
            usualGrades: 35,
            finalGrades: 32,
            totalScore: 67,
            semester: '第三学期'
          }
        ],
      }).list;
      return {
        code: 200,
        message: 'success',
        data: { list },
      };
    },
  },
] as MockMethod[];

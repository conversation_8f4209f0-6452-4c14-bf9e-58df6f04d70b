import Mock from 'mockjs';
import { MockMethod } from 'vite-plugin-mock';

export default [
  {
    url: '/api/student/Course/list',
    method: 'get',
    response: ({ query }) => {
      const cid = query.cid;
      let list = [];
      if (cid === '1') {
        list = Mock.mock({
          'list|1-1': [
            {
              courseGoal: '课程目标1',
              raeGrades: 83,
              percentage:80,
              fraction:66.4,
            },
            {
              courseGoal: '课程目标2',
              raeGrades: 80,
              percentage:50,
              fraction:40,
            },
            {
              courseGoal: '课程目标3',
              raeGrades: 40,
              percentage:70,
              fraction:28,
            },
          ],
        }).list;
      }
      if (cid === '4') {
        list = Mock.mock({
          'list|1-1': [
            {
              courseGoal: '课程目标4',
              raeGrades: 69,
              percentage:10,
              fraction:6.9,
            },
            {
              courseGoal: '课程目标5',
              raeGrades: 67,
              percentage:80,
              fraction:53.6,
            },
            {
              courseGoal: '课程目标6',
              raeGrades: 90,
              percentage:90,
              fraction:81,
            },
          ],
        }).list;
      }
      return {
        code: 200,
        message:'success',
        data: { list },
      };
    },
  },
  {
    url: '/api/student/Study/list',
    method: 'get',
    response: ({ query }) => {
      const cid = query.cid;
      let list = [];
      if (cid === '1') {
        list = Mock.mock({
          'list|1-1': [
            {
              studiesType: '实践',
              fraction:8,
            },
            {
              studiesType: '作业',
              fraction:40,
            },
            {
              studiesType: '过程性考核',
              fraction:33,
            },
          ],
        }).list;
      }
      if (cid === '2') {
        list = Mock.mock({
          'list|1-1': [
            {
              studiesType: '实践',
              fraction:27
            },
            {
              studiesType: '作业',
              fraction:22.4,
            },
            {
              studiesType: '过程性考核',
              fraction:12,
            },
          ],
        }).list;
      }
      return {
        code: 200,
        message:'success',
        data: { list },
      };
    },
  },
  {
    url: '/api/student/CourseGraduate/list',
    method: 'get',
    response: ({ query }) => {
      const cid = query.cid;
      let list = [];
      if(cid==='1') {
        list = Mock.mock({
          'list|1-1': [
            {
              graduateName: '毕业目标1',
              courseGoal: '课程目标1',
              percentage: 83,
            },
            {
              graduateName: '毕业目标2',
              courseGoal: '课程目标2',
              percentage: 79,
            },
            {
              graduateName: '毕业目标3',
              courseGoal: '课程目标3',
              percentage: 40,
            },
          ],
        }).list;
        return {
          code: 200,
          message:'success',
          data: {list},
        };
      }
    },
  },
  {
    url: '/api/student/StudentScore/list',
    method: 'get',
    response: ({ query }) => {
      const cid = query.cid;
      let result = {
        code: 200,
        message:'success',
        data: {
          list: [],
          headerConfig: [],
          total:0,
        }
      };
      if (cid === '1') {
        // 修改后的Mock数据生成
        result.data.total=81;
        result.data.headerConfig = [
          {
            category: '阶段性测试（40%）',
            items: ['第一次阶段性测试', '第二次阶段性测试','第三次阶段性测试'],
            colspan: 3
          },
          {
            category: '实践（10%）',
            items: ['第一次实验', '第二次实验'],
            colspan: 2
          },
          {
            category: '作业（50%）',
            items: ['作业1', '作业2','作业3','作业4'],
            colspan: 4
          }
        ];

        result.data.list = Mock.mock({
          'list|1-1': [
            {
              assessmentItems: {
                '第一次阶段性测试': {
                  originalScores: 80,
                  percentage: 10,
                  fraction: 8
                },
                '第二次阶段性测试': {
                  originalScores:90,
                  percentage: 20,
                  fraction: 18
                },
                '第三次阶段性测试': {
                  originalScores: 70,
                  percentage: 10,
                  fraction: 7
                },
                '第一次实验': {
                  originalScores: 80,
                  percentage: 5,
                  fraction: 4
                },
                '第二次实验': {
                  originalScores: 80,
                  percentage: 5,
                  fraction: 4
                },
                '作业1': {
                  originalScores:90,
                  percentage: 10,
                  fraction: 9
                },
                '作业2': {
                  originalScores: 70,
                  percentage: 10,
                  fraction: 7
                },
                '作业3': {
                  originalScores: 80,
                  percentage: 10,
                  fraction: 8
                },
                '作业4': {
                  originalScores: 80,
                  percentage: 20,
                  fraction: 16
                }
              },
            }
          ]
        }).list;
      }
      return result;
    }
  }
] as MockMethod[];

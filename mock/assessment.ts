// 考核管理相关的Mock数据

// 定义趋势类型
interface TrendInfo {
  theme: 'success' | 'danger' | 'default' | 'primary' | 'warning';
  icon: string;
  text: string;
}

// 定义考核任务类型
interface EvaluationTask {
  id: number;
  courseName: string;
  semester: string;
  grade: string;
  majors: string[];
  studentCount: number;
  teachingTeam: { name: string; role: string }[];
  assessmentInputMode?: 'direct' | 'detailed';
  examStatus: '未开始' | '进行中' | '已结束' | '编辑中';
  objectiveScores?: ObjectiveScore[]; // 为直接录入模式存储目标分值
}

// 课程目标分值类型
interface ObjectiveScore {
  id: string;
  identifier: string;
  description: string;
  score: number;
}

// 定义统计数据项类型
interface StatisticItem {
  key: string;
  label: string;
  value: string | number;
  icon: string;
  iconClass: string;
  trend?: TrendInfo;
}

// 学生成绩类型
interface StudentScore {
  id: number;
  studentId: string;
  name: string;
  class: string;
  homework: number;
  midterm: number;
  experiment: number;
  final: number;
  finalScore: number;
}

// 评分规则项类型
interface HomeworkItem {
  id: number;
  name: string;
  description: string;
  percentage: number;
}

// 评分规则表单类型
interface ScoreRuleForm {
  homeworkPercentage: number;
  examPercentage: number;
  excellentThreshold: number;
  goodThreshold: number;
  mediumThreshold: number;
  passThreshold: number;
  homeworkItems: HomeworkItem[];
}

// 课程目标类型
interface CourseObjective {
  id: string;
  identifier: string;
  description: string;
}

// 班级选项类型
interface ClassOption {
  label: string;
  value: string;
}

// 任务统计数据
export const mockTaskStatistics = {
  totalSemesters: 4,
  totalStudents: 473,
  completedExams: 2,
  pendingExams: 2,
  finalExamRate: 50,
  averageStudents: 118
};

// 考核任务数据
export const mockEvaluationTasks: EvaluationTask[] = [
  {
    id: 1,
    courseName: '数据结构',
    semester: '2023-2024学年 第一学期',
    grade: '2023级',
    majors: ['计算机科学与技术', '软件工程'],
    studentCount: 120,
    teachingTeam: [{ name: '王老师', role: '主讲教师' }, { name: '李助教', role: '助教' }],
    assessmentInputMode: 'direct',
    examStatus: '编辑中',
    objectiveScores: [
      { id: 'co1', identifier: 'CO1', description: '掌握Java基本语法、数据类型、运算符等基础知识', score: 20 },
      { id: 'co2', identifier: 'CO2', description: '理解面向对象编程的基本概念，掌握类、对象、继承、封装等核心知识', score: 30 },
    ]
  },
  {
    id: 2,
    courseName: '算法分析',
    semester: '2023-2024学年 第一学期',
    grade: '2023级',
    majors: ['计算机科学与技术'],
    studentCount: 110,
    teachingTeam: [{ name: '王老师', role: '主讲教师' }],
    assessmentInputMode: 'detailed',
    examStatus: '已结束',
  },
  {
    id: 3,
    courseName: '计算机网络',
    semester: '2023-2024学年 第二学期',
    grade: '2022级',
    majors: ['网络工程'],
    studentCount: 135,
    teachingTeam: [{ name: '张老师', role: '主讲教师' }],
    assessmentInputMode: undefined,
    examStatus: '编辑中',
  },
  {
    id: 4,
    courseName: '信息安全',
    semester: '2023-2024学年 第二学期',
    grade: '2022级',
    majors: ['信息安全'],
    studentCount: 108,
    teachingTeam: [{ name: '赵老师', role: '主讲教师' }],
    assessmentInputMode: 'direct',
    examStatus: '编辑中',
    objectiveScores: [
      { id: 'co1', identifier: 'CO1', description: '掌握Java基本语法、数据类型、运算符等基础知识', score: 0 },
      { id: 'co2', identifier: 'CO2', description: '理解面向对象编程的基本概念，掌握类、对象、继承、封装等核心知识', score: 0 },
      { id: 'co3', identifier: 'CO3', description: '掌握Java常用类库的使用方法，能够运用工程思维解决实际编程问题', score: 0 },
      { id: 'co4', identifier: 'CO4', description: '能够分析复杂程序的逻辑结构，识别和解决程序设计中的关键问题', score: 0 },
    ]
  },
  {
    id: 5,
    courseName: '软件工程',
    semester: '2023-2024学年 第二学期',
    grade: '2022级',
    majors: ['软件工程'],
    studentCount: 95,
    teachingTeam: [{ name: '李老师', role: '主讲教师' }],
    assessmentInputMode: 'detailed',
    examStatus: '未开始',
  },
  {
    id: 6,
    courseName: '数据库原理',
    semester: '2024-2025学年 第一学期',
    grade: '2023级',
    majors: ['计算机科学与技术', '软件工程'],
    studentCount: 130,
    teachingTeam: [{ name: '刘老师', role: '主讲教师' }, { name: '陈助教', role: '助教' }],
    assessmentInputMode: 'direct',
    examStatus: '进行中',
  },
  {
    id: 7,
    courseName: '操作系统',
    semester: '2024-2025学年 第一学期',
    grade: '2023级',
    majors: ['计算机科学与技术'],
    studentCount: 118,
    teachingTeam: [{ name: '周老师', role: '主讲教师' }],
    assessmentInputMode: undefined,
    examStatus: '编辑中',
  },
  {
    id: 8,
    courseName: '计算机组成原理',
    semester: '2024-2025学年 第一学期',
    grade: '2023级',
    majors: ['计算机科学与技术', '网络工程'],
    studentCount: 142,
    teachingTeam: [{ name: '吴老师', role: '主讲教师' }],
    assessmentInputMode: 'detailed',
    examStatus: '已结束',
  },
  {
    id: 9,
    courseName: '人工智能',
    semester: '2024-2025学年 第一学期',
    grade: '2022级',
    majors: ['计算机科学与技术'],
    studentCount: 87,
    teachingTeam: [{ name: '杨老师', role: '主讲教师' }, { name: '何助教', role: '助教' }],
    assessmentInputMode: 'direct',
    examStatus: '编辑中',
  },
  {
    id: 10,
    courseName: '编译原理',
    semester: '2024-2025学年 第一学期',
    grade: '2022级',
    majors: ['计算机科学与技术'],
    studentCount: 76,
    teachingTeam: [{ name: '郑老师', role: '主讲教师' }],
    assessmentInputMode: 'detailed',
    examStatus: '未开始',
  },
  {
    id: 11,
    courseName: '机器学习',
    semester: '2024-2025学年 第二学期',
    grade: '2022级',
    majors: ['计算机科学与技术', '人工智能'],
    studentCount: 102,
    teachingTeam: [{ name: '孙老师', role: '主讲教师' }],
    assessmentInputMode: 'direct',
    examStatus: '编辑中',
  },
];

// 学生成绩数据
export const mockStudentScores: StudentScore[] = [
  { 
    id: 1, 
    studentId: '2022001001', 
    name: '张三', 
    class: '计算机2022-1班', 
    homework: 85, 
    midterm: 78, 
    experiment: 92, 
    final: 86, 
    finalScore: 85 
  },
  { 
    id: 2, 
    studentId: '2022001002', 
    name: '李四', 
    class: '计算机2022-1班', 
    homework: 92, 
    midterm: 85, 
    experiment: 95, 
    final: 88, 
    finalScore: 89 
  },
  { 
    id: 3, 
    studentId: '2022001003', 
    name: '王五', 
    class: '计算机2022-1班', 
    homework: 75, 
    midterm: 70, 
    experiment: 80, 
    final: 72, 
    finalScore: 74 
  },
  { 
    id: 4, 
    studentId: '2022001004', 
    name: '赵六', 
    class: '计算机2022-1班', 
    homework: 60, 
    midterm: 55, 
    experiment: 65, 
    final: 58, 
    finalScore: 59 
  }
];

// 班级选项
export const mockClassOptions: ClassOption[] = [
  { label: '全部班级', value: 'all' },
  { label: '计算机2022-1班', value: 'cs-2022-1' },
  { label: '计算机2022-2班', value: 'cs-2022-2' },
  { label: '软件2022-1班', value: 'se-2022-1' }
];

// 评分规则表单数据
export const mockScoreRuleForm: ScoreRuleForm = {
  homeworkPercentage: 40,
  examPercentage: 60,
  excellentThreshold: 90,
  goodThreshold: 80,
  mediumThreshold: 70,
  passThreshold: 60,
  homeworkItems: [
    { id: 1, name: '课堂出勤', description: '课堂考勤情况', percentage: 10 },
    { id: 2, name: '作业完成度', description: '平时作业完成情况', percentage: 40 },
    { id: 3, name: '课堂表现', description: '课堂互动与回答问题', percentage: 20 },
    { id: 4, name: '实验报告', description: '实验课实验报告质量', percentage: 30 }
  ]
};

// 课程目标数据
export const mockCourseObjectives: CourseObjective[] = [
  { id: 'co1', identifier: 'CO1', description: '掌握Java基本语法、数据类型、运算符等基础知识' },
  { id: 'co2', identifier: 'CO2', description: '理解面向对象编程的基本概念，掌握类、对象、继承、封装等核心知识' },
  { id: 'co3', identifier: 'CO3', description: '掌握Java常用类库的使用方法，能够运用工程思维解决实际编程问题' },
  { id: 'co4', identifier: 'CO4', description: '能够分析复杂程序的逻辑结构，识别和解决程序设计中的关键问题' },
];

// 考核任务表格列配置
export const mockTaskColumns = [
  { colKey: 'semester', title: '学年学期', width: 180 },
  { colKey: 'grade', title: '年级', width: 100 },
  { colKey: 'majors', title: '专业', width: 200 },
  { colKey: 'studentCount', title: '学生人数', width: 100 },
  { colKey: 'teachingTeam', title: '教学团队', width: 200 },
  { colKey: 'examConfig', title: '考核配置', width: 140 },
  { colKey: 'examStatus', title: '考核状态', width: 100 },
  { colKey: 'operation', title: '操作', width: 280, fixed: 'right' as const }
];

// 成绩管理表格列配置
export const mockScoreColumns = [
  { colKey: 'studentId', title: '学号', width: 120 },
  { colKey: 'name', title: '姓名', width: 100 },
  { colKey: 'class', title: '班级', width: 150 },
  { colKey: 'homework', title: '平时成绩', width: 100 },
  { colKey: 'midterm', title: '期中成绩', width: 100 },
  { colKey: 'experiment', title: '实验成绩', width: 100 },
  { colKey: 'final', title: '期末成绩', width: 100 },
  { colKey: 'finalScore', title: '总评成绩', width: 100 },
  { colKey: 'operation', title: '操作', width: 180, fixed: 'right' as const }
];

// 平时成绩组成表格列配置
export const mockHomeworkColumns = [
  { colKey: 'name', title: '评分项名称', width: 180 },
  { colKey: 'description', title: '描述', width: 250 },
  { colKey: 'percentage', title: '权重', width: 150 },
  { colKey: 'operation', title: '操作', width: 100 }
];

// 生成格式化的考核任务统计数据的函数
export const generateTaskStatisticsData = (taskStatistics: typeof mockTaskStatistics): StatisticItem[] => [
  {
    key: 'totalSemesters',
    label: '总学期数',
    value: taskStatistics.totalSemesters,
    icon: 'calendar',
    iconClass: 'icon-blue'
  },
  {
    key: 'totalStudents',
    label: '总学生数',
    value: taskStatistics.totalStudents,
    icon: 'user-circle',
    iconClass: 'icon-green',
    trend: {
      theme: 'success' as const,
      icon: 'arrow-up',
      text: '学生规模稳定'
    }
  },
  {
    key: 'completedExams',
    label: '已完成考核',
    value: taskStatistics.completedExams,
    icon: 'check-circle',
    iconClass: 'icon-orange',
    trend: {
      theme: 'success' as const,
      icon: 'check',
      text: '考核进展良好'
    }
  },
  {
    key: 'finalExamRate',
    label: '期末考核率',
    value: `${taskStatistics.finalExamRate}%`,
    icon: 'chart-pie',
    iconClass: 'icon-purple',
    trend: {
      theme: 'primary' as const,
      icon: 'chart-line',
      text: '考核方式均衡'
    }
  }
];

// 导出类型定义
export type {
  TrendInfo,
  EvaluationTask,
  ObjectiveScore,
  StatisticItem,
  StudentScore,
  HomeworkItem,
  ScoreRuleForm,
  CourseObjective,
  ClassOption
};

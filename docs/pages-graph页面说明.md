# Graph模块 Vue页面说明

## 全局目录结构

```
/graph/
├── GraphManagement.vue       # 知识图谱管理主页
├── GraphIndex.vue            # 知识图谱首页
├── GraphIndexPro.vue         # 知识图谱专业版首页
├── AcademicStyle.vue         # 学术风格页面
├── nodes/                    # 节点管理
│   └── NodeManagement.vue    # 节点管理
└── links/                    # 链接管理
    └── LinkManagement.vue    # 链接管理
```

## 模块概述

Graph模块（知识图谱模块）负责管理专业知识体系的可视化展示和关系构建，通过图形化的方式展现知识点之间的关联关系，支持知识体系的构建、维护和分析。

### 数据库表对应关系

- `graph_nodes` - 知识图谱节点表
- `graph_links` - 知识图谱关系链接表

## 功能分类

### 1. 图谱管理
负责知识图谱的整体管理和展示。

### 2. 节点管理 (nodes/)
管理知识图谱中的知识点节点。

### 3. 链接管理 (links/)
管理知识点之间的关联关系。

## 详细页面说明

### 根目录页面

#### /graph/GraphManagement.vue
- **功能**: 知识图谱管理主页
- **描述**: 知识图谱模块的主入口页面，提供图谱管理的综合功能
- **主要功能**:
  - 专业知识图谱管理
  - 图谱概览和统计
  - 图谱配置和设置
  - 快速操作入口

#### /graph/GraphIndex.vue
- **功能**: 知识图谱首页
- **描述**: 知识图谱的展示首页，提供图谱的可视化浏览功能
- **主要功能**:
  - 知识图谱可视化展示
  - 图谱交互浏览
  - 节点搜索和定位
  - 关系路径分析

#### /graph/GraphIndexPro.vue
- **功能**: 知识图谱专业版首页
- **描述**: 知识图谱的高级展示页面，提供更丰富的可视化功能
- **主要功能**:
  - 高级可视化效果
  - 多维度图谱展示
  - 复杂关系分析
  - 专业图谱工具

#### /graph/AcademicStyle.vue
- **功能**: 学术风格页面
- **描述**: 提供学术风格的图谱展示模式，适合学术研究和展示
- **主要功能**:
  - 学术风格图谱渲染
  - 专业术语标准化
  - 学术关系展示
  - 研究导向分析

### Nodes目录 - 节点管理

#### /graph/nodes/NodeManagement.vue
- **功能**: 节点管理
- **描述**: 管理知识图谱中的知识点节点，包括节点的创建、编辑和分类
- **主要功能**:
  - 节点列表管理
  - 节点信息编辑
  - 节点分类和标签
  - 节点层次结构
  - 批量操作功能

### Links目录 - 链接管理

#### /graph/links/LinkManagement.vue
- **功能**: 链接管理
- **描述**: 管理知识点之间的关联关系，定义和维护知识体系的结构
- **主要功能**:
  - 关系链接管理
  - 关系类型定义
  - 关系强度设置
  - 关系验证和检查
  - 关系统计分析

## 页面功能详细说明

### 知识图谱可视化功能特点
1. **多层次展示**: 支持不同层次的知识结构展示
2. **交互式浏览**: 支持拖拽、缩放、搜索等交互操作
3. **动态布局**: 自动优化节点布局，提供多种布局算法
4. **关系分析**: 分析知识点之间的关联路径和依赖关系
5. **主题定制**: 支持不同的视觉主题和风格设置

### 节点管理功能特点
1. **多类型支持**: 支持概念、技能、理论等多种节点类型
2. **层次分类**: 支持知识点的层次化分类和组织
3. **属性管理**: 管理节点的各种属性和元数据
4. **版本控制**: 跟踪节点信息的修改历史
5. **标签系统**: 灵活的标签系统便于节点分类和检索

### 链接管理功能特点
1. **关系类型**: 支持前置、包含、关联等多种关系类型
2. **权重设置**: 设置关系的强度和重要程度
3. **方向性**: 支持有向和无向关系的定义
4. **约束检查**: 检测和避免循环依赖等逻辑错误
5. **路径分析**: 分析知识点之间的学习路径

## 与其他模块的关系

### 与Training模块的关系
- 基于Training模块的课程体系构建知识图谱
- 知识图谱为培养方案提供知识结构支撑

### 与Assessment模块的关系
- 为Assessment模块提供知识点的层次结构
- 支持基于知识图谱的能力评估

### 与Base模块的关系
- 使用Base模块的专业、课程等基础数据
- 按专业构建专门的知识图谱

## 目录结构规范

### 文件命名规范
- 所有Vue文件使用PascalCase命名法
- 文件名应体现页面功能
- 组件名称应与文件名保持一致

### 功能分类体系
1. **图谱展示**: 知识图谱的可视化展示和浏览
2. **节点管理**: 知识点的管理和维护
3. **关系管理**: 知识点关联关系的定义和管理

## 技术特点

### 可视化技术
- 使用D3.js或类似图形库进行可视化渲染
- 支持SVG和Canvas两种渲染模式
- 响应式设计，适配不同屏幕尺寸

### 性能优化
- 大规模图谱的分层加载
- 视口裁剪优化渲染性能
- 节点和关系的懒加载

## 注意事项

1. **性能考虑**: 大规模知识图谱的渲染和交互性能优化
2. **数据一致性**: 确保节点和关系数据的一致性
3. **用户体验**: 提供流畅的图谱浏览和编辑体验
4. **可扩展性**: 支持知识图谱规模的动态扩展
5. **数据导入**: 支持从外部数据源导入知识图谱数据 
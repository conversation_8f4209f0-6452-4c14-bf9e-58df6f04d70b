# System模块 Vue页面说明

## 全局目录结构

```
/system/
├── BackupManagement.vue      # 数据备份管理
├── DictManagement.vue        # 字典管理
├── DocumentFinalReview.vue   # 文档最终审核
├── DocumentView.vue          # 文档查看
├── ExcelManagement.vue       # Excel管理
├── LogManagement.vue         # 日志管理
├── MenuManagement.vue        # 菜单管理
├── NoticeManagement.vue      # 通知管理
├── RoleManagement.vue        # 角色管理
├── UserManagement.vue        # 用户管理
├── document/                 # 文档管理
│   ├── DirectorsDocument.vue # 主管文档管理
│   ├── DocumentManagement.vue # 文档管理
│   └── DocumentReview.vue    # 文档审核
├── home/                     # 首页管理
│   ├── HomeConfig.vue        # 首页配置
│   └── HomeManagement.vue    # 首页管理
├── monitor/                  # 系统监控
│   └── SystemMonitor.vue     # 系统监控
├── profile/                  # 个人资料
│   ├── ProfileDemo.vue       # 个人资料演示
│   ├── ProfileManagement.vue # 个人资料管理
│   ├── StudentProfile.vue    # 学生个人资料
│   └── TeacherProfile.vue    # 教师个人资料

```

## 模块概述

System模块（系统管理模块）负责管理系统的基础功能和配置，包括用户权限、菜单配置、系统监控、数据备份等核心系统功能。

### 数据库表对应关系

- `sys_menu` - 菜单表
- `sys_role` - 角色表
- `sys_base_user` - 用户表
- `sys_dict_type` - 字典类型表
- `sys_dict_data` - 字典数据表
- `sys_role_menu` - 角色菜单关联表
- `sys_role_user` - 角色用户关联表

## 功能分类

### 1. 权限管理
负责系统的用户、角色、菜单权限管理。

### 2. 系统配置
管理系统的基础配置和参数设置。

### 3. 数据管理
负责数据备份、导入导出等数据管理功能。

### 4. 监控管理
系统运行状态监控和日志管理。

### 5. 内容管理
文档、通知等内容的管理。

## 详细页面说明

### 根目录页面

#### /system/BackupManagement.vue
- **功能**: 数据备份管理
- **描述**: 管理系统数据的备份和恢复，确保数据安全
- **主要功能**:
  - 数据库备份创建
  - 备份文件管理
  - 数据恢复操作
  - 备份计划设置
  - 备份状态监控

#### /system/DictManagement.vue
- **功能**: 字典管理
- **描述**: 管理系统中使用的数据字典，维护下拉选项等配置数据
- **主要功能**:
  - 字典类型管理
  - 字典数据维护
  - 字典项排序
  - 字典状态控制
  - 字典数据导入导出

#### /system/DocumentFinalReview.vue
- **功能**: 文档最终审核
- **描述**: 对系统中的文档进行最终审核和批准
- **主要功能**:
  - 文档审核列表
  - 审核状态管理
  - 审核意见记录
  - 审核流程控制
  - 审核结果通知

#### /system/DocumentView.vue
- **功能**: 文档查看
- **描述**: 提供文档的查看和预览功能
- **主要功能**:
  - 文档在线预览
  - 文档下载
  - 文档版本查看
  - 文档权限控制
  - 文档访问记录

#### /system/ExcelManagement.vue
- **功能**: Excel管理
- **描述**: 管理Excel文件的导入导出和处理
- **主要功能**:
  - Excel文件上传
  - 数据导入处理
  - 模板管理
  - 导入结果查看
  - 错误数据处理

#### /system/LogManagement.vue
- **功能**: 日志管理
- **描述**: 管理系统操作日志和错误日志
- **主要功能**:
  - 操作日志查询
  - 错误日志分析
  - 日志统计报表
  - 日志清理设置
  - 日志导出功能

#### /system/MenuManagement.vue
- **功能**: 菜单管理
- **描述**: 管理系统菜单结构和权限配置
- **主要功能**:
  - 菜单树形结构管理
  - 菜单权限设置
  - 菜单图标配置
  - 菜单排序调整
  - 菜单状态控制

#### /system/NoticeManagement.vue
- **功能**: 通知管理
- **描述**: 管理系统通知和公告的发布
- **主要功能**:
  - 通知创建和编辑
  - 通知发布管理
  - 通知对象设置
  - 通知状态跟踪
  - 通知统计分析

#### /system/RoleManagement.vue
- **功能**: 角色管理
- **描述**: 管理系统角色和权限分配
- **主要功能**:
  - 角色创建和编辑
  - 权限分配管理
  - 角色用户关联
  - 角色状态控制
  - 权限继承设置

#### /system/UserManagement.vue
- **功能**: 用户管理
- **描述**: 管理系统用户账号和基本信息
- **主要功能**:
  - 用户账号管理
  - 用户信息维护
  - 密码重置
  - 用户状态控制
  - 用户权限查看

### Document目录 - 文档管理

#### /system/document/DirectorsDocument.vue
- **功能**: 主管文档管理
- **描述**: 主管级别的文档管理功能，处理重要文档的审批
- **主要功能**:
  - 重要文档审批
  - 文档流程管理
  - 审批历史记录
  - 文档分类管理
  - 权限级别控制

#### /system/document/DocumentManagement.vue
- **功能**: 文档管理
- **描述**: 系统文档的统一管理平台
- **主要功能**:
  - 文档上传和存储
  - 文档分类管理
  - 文档版本控制
  - 文档权限设置
  - 文档搜索功能

#### /system/document/DocumentReview.vue
- **功能**: 文档审核
- **描述**: 文档的审核和评审流程管理
- **主要功能**:
  - 文档审核流程
  - 审核意见管理
  - 审核状态跟踪
  - 审核人员分配
  - 审核结果统计

### Home目录 - 首页管理

#### /system/home/<USER>
- **功能**: 首页配置
- **描述**: 配置系统首页的布局和内容
- **主要功能**:
  - 首页布局设置
  - 组件配置管理
  - 内容区域设置
  - 样式主题配置
  - 个性化设置

#### /system/home/<USER>
- **功能**: 首页管理
- **描述**: 管理系统首页的内容和展示
- **主要功能**:
  - 首页内容编辑
  - 轮播图管理
  - 快捷入口设置
  - 通知公告管理
  - 统计数据配置

### Monitor目录 - 系统监控

#### /system/monitor/SystemMonitor.vue
- **功能**: 系统监控
- **描述**: 监控系统运行状态和性能指标
- **主要功能**:
  - 系统性能监控
  - 资源使用统计
  - 在线用户监控
  - 系统健康检查
  - 告警信息管理

### Profile目录 - 个人资料

#### /system/profile/ProfileDemo.vue
- **功能**: 个人资料演示
- **描述**: 个人资料功能的演示和示例页面
- **主要功能**:
  - 功能演示展示
  - 操作指导说明
  - 示例数据展示
  - 交互效果演示
  - 帮助文档链接

#### /system/profile/ProfileManagement.vue
- **功能**: 个人资料管理
- **描述**: 用户个人资料的管理和维护
- **主要功能**:
  - 个人信息编辑
  - 头像上传管理
  - 密码修改
  - 安全设置
  - 偏好设置

#### /system/profile/StudentProfile.vue
- **功能**: 学生个人资料
- **描述**: 学生用户的个人资料管理
- **主要功能**:
  - 学生信息维护
  - 学籍信息查看
  - 成绩信息查询
  - 课程安排查看
  - 个人设置管理

#### /system/profile/TeacherProfile.vue
- **功能**: 教师个人资料
- **描述**: 教师用户的个人资料管理
- **主要功能**:
  - 教师信息维护
  - 教学信息管理
  - 工作量统计
  - 教学评价查看
  - 个人设置管理



## 与其他模块的关系

### 与Base模块的关系
- System模块提供用户权限管理
- Base模块使用System模块的用户和角色数据
- 基础数据的权限控制依赖System模块

### 与所有业务模块的关系
- 提供统一的权限控制和菜单管理
- 所有模块的操作日志记录
- 系统级配置和参数管理

## 目录结构规范

### 文件命名规范
- 所有Vue文件使用PascalCase命名法
- 文件名应体现页面功能
- 组件名称应与文件名保持一致

### 功能分类体系
1. **权限管理**: 用户、角色、菜单权限控制
2. **系统配置**: 字典、参数、配置管理
3. **数据管理**: 备份、导入导出、Excel处理
4. **监控管理**: 系统监控、日志管理
5. **内容管理**: 文档、通知管理

## 注意事项

1. **安全性**: 系统管理功能涉及敏感操作，需要严格的权限控制
2. **稳定性**: 系统配置变更需要谨慎，避免影响系统正常运行
3. **性能**: 监控和日志功能需要考虑性能影响
4. **备份**: 重要配置和数据需要定期备份
5. **审计**: 所有系统管理操作都应有详细的审计日志 
# Assessment模块 Vue页面说明

## 全局目录结构

```
/assessment/
├── AssessmentManagement.vue   # 考核评价管理主页
├── AssessmentXudong.vue       # 考核评价特定版本页面
├── exam/                      # 考核管理
│   ├── ExamPaper.vue          # 试卷管理
│   └── TeacherExam.vue        # 教师考试管理
├── score/                     # 成绩管理
│   └── (待添加)               # 成绩详情管理页面
├── question/                  # 题库管理
│   ├── QuestionBank.vue       # 题库管理
│   ├── QuestionDetail.vue     # 题目详情
│   ├── CourseQuestion.vue     # 课程题库管理
│   └── TeacherQuestionBank.vue # 教师题库管理
├── answer/                    # 答案管理
│   └── (待添加)               # 答案管理页面
└── questionnaire/            # 问卷管理
    ├── QuestionnaireCreate.vue # 问卷创建
    ├── QuestionnaireDetail.vue # 问卷详情
    ├── QuestionnaireList.vue   # 问卷列表
    ├── QuestionnaireManagement.vue # 问卷管理
    └── StudentQuestionnaire.vue # 学生问卷
```

## 模块概述

Assessment模块（考核评价模块）负责管理教学过程中的考核评价相关功能，包括题库管理、试卷管理、成绩管理和答案管理等核心功能。

### 数据库表对应关系

- `assessment` - 教学任务考核表
- `assessment_score_detail` - 考核结果详情表
- `assessment_score_target` - 考核结果目标表
- `repository_question` - 题库表
- `repository_answer` - 题目答案表
- `repository_path` - 题目存储路径表

## 功能分类

### 1. 考核管理 (exam/)
负责考核任务的创建、试卷管理和考核执行。

### 2. 成绩管理 (score/)
管理学生的考核成绩和评价结果。

### 3. 题库管理 (question/)
管理考核题目库，包括题目的增删改查和分类管理。

### 4. 答案管理 (answer/)
管理题目的标准答案和评分标准。

### 5. 问卷管理 (questionnaire/)
管理教学评价问卷，包括课程评价、教学效果调查等。

## 详细页面说明

### 根目录页面

#### /assessment/AssessmentManagement.vue
- **功能**: 考核评价管理主页
- **描述**: 考核评价模块的主入口页面，提供各子功能模块的导航和概览
- **主要功能**:
  - 考核评价概览统计
  - 各子模块快速导航
  - 最新考核动态展示
  - 待处理任务提醒

#### /assessment/AssessmentXudong.vue
- **功能**: 考核评价特定版本页面
- **描述**: 针对特定需求定制的考核评价页面，可能包含特殊的业务逻辑
- **主要功能**:
  - 定制化考核流程
  - 特殊评价规则
  - 专门的数据展示

### Exam目录 - 考核管理

#### /assessment/exam/ExamPaper.vue
- **功能**: 试卷管理
- **描述**: 管理考核试卷的创建、编辑和组织，支持从题库选题组卷
- **主要功能**:
  - 试卷创建和编辑
  - 从题库选题组卷
  - 试卷预览和打印
  - 试卷模板管理
  - 考核权重设置

#### /assessment/exam/TeacherExam.vue
- **功能**: 教师考试管理
- **描述**: 教师端的考试管理功能，包括考试创建、监考和成绩录入
- **主要功能**:
  - 考试安排和创建
  - 考试监控和管理
  - 在线考试支持
  - 考试结果统计
  - 异常情况处理

### Score目录 - 成绩管理

#### 待开发页面
- **ScoreDetail.vue**: 成绩详情管理
- **ScoreStatistics.vue**: 成绩统计分析
- **ScoreImport.vue**: 成绩批量导入

### Question目录 - 题库管理

#### /assessment/question/QuestionBank.vue
- **功能**: 题库管理
- **描述**: 管理考核题目库，支持题目的分类、检索和维护
- **主要功能**:
  - 题目列表展示和管理
  - 题目分类和标签
  - 题目检索和筛选
  - 批量导入导出
  - 题目使用统计

#### /assessment/question/QuestionDetail.vue
- **功能**: 题目详情管理
- **描述**: 单个题目的详细信息管理，包括题干、选项、答案和评分标准
- **主要功能**:
  - 题目详细信息编辑
  - 题目类型设置
  - 答案和评分标准
  - 题目预览功能
  - 题目版本管理

#### /assessment/question/CourseQuestion.vue
- **功能**: 课程题库管理
- **描述**: 针对特定课程的题库管理，支持按课程组织和管理题目
- **主要功能**:
  - 课程专属题库管理
  - 按课程目标分类题目
  - 课程题目统计分析
  - 题目难度分布
  - 与课程大纲关联

#### /assessment/question/TeacherQuestionBank.vue
- **功能**: 教师题库管理
- **描述**: 教师端的题库管理功能，支持教师创建和管理自己的题目
- **主要功能**:
  - 个人题库管理
  - 题目创建和编辑
  - 题目分享和协作
  - 题目使用记录
  - 题目质量评估

### Answer目录 - 答案管理

#### 待开发页面
- **AnswerManagement.vue**: 答案管理主页
- **AnswerDetail.vue**: 答案详情编辑
- **ScoringRules.vue**: 评分规则设置

### Questionnaire目录 - 问卷管理

#### /assessment/questionnaire/QuestionnaireCreate.vue
- **功能**: 问卷创建
- **描述**: 创建和设计教学评价问卷，支持多种题型和逻辑设置
- **主要功能**:
  - 问卷结构设计
  - 题目类型选择（单选、多选、填空、评分等）
  - 逻辑跳转设置
  - 问卷预览功能
  - 发布设置和权限控制

#### /assessment/questionnaire/QuestionnaireDetail.vue
- **功能**: 问卷详情
- **描述**: 查看和管理问卷的详细信息，包括统计分析和数据导出
- **主要功能**:
  - 问卷详情展示
  - 问卷编辑修改
  - 回收数据查看和分析
  - 统计分析报告生成
  - 问卷状态管理（草稿/发布/关闭）

#### /assessment/questionnaire/QuestionnaireList.vue
- **功能**: 问卷列表
- **描述**: 显示所有教学评价问卷的列表视图，支持筛选和批量操作
- **主要功能**:
  - 问卷列表展示
  - 问卷搜索筛选（按类型、状态、时间等）
  - 批量操作功能
  - 状态批量更新
  - 问卷复制和模板化

#### /assessment/questionnaire/QuestionnaireManagement.vue
- **功能**: 问卷管理
- **描述**: 问卷系统的综合管理平台，提供完整的问卷生命周期管理
- **主要功能**:
  - 问卷全生命周期管理
  - 问卷模板管理
  - 分发渠道管理（邮件、链接、二维码）
  - 数据统计分析
  - 权限控制和用户管理

#### /assessment/questionnaire/StudentQuestionnaire.vue
- **功能**: 学生问卷
- **描述**: 学生参与教学评价问卷的填写界面
- **主要功能**:
  - 问卷填写界面
  - 答题进度显示
  - 答案自动保存功能
  - 提交确认和验证
  - 历史记录查看

## 页面功能详细说明

### 题库管理功能特点
1. **多题型支持**: 支持单选题、多选题、填空题、简答题等多种题型
2. **智能分类**: 支持按课程、章节、难度等多维度分类
3. **标签系统**: 灵活的标签系统便于题目管理和检索
4. **版本控制**: 题目修改历史记录和版本管理
5. **统计分析**: 题目使用频率、得分率等统计信息

### 试卷管理功能特点
1. **智能组卷**: 支持按条件自动组卷和手动选题
2. **权重设置**: 支持不同题目的分值和权重设置
3. **模板管理**: 预设试卷模板，提高组卷效率
4. **预览打印**: 支持试卷预览和多种格式导出
5. **难度控制**: 自动计算试卷难度并提供调整建议

### 成绩管理功能特点
1. **多维分析**: 支持按课程目标、毕业要求等维度分析
2. **达成度计算**: 自动计算课程目标达成度
3. **统计报表**: 丰富的成绩统计和分析报表
4. **数据导入**: 支持从Excel等格式批量导入成绩
5. **异常检测**: 自动检测异常成绩并提醒

### 教师端功能特点
1. **个性化管理**: 教师可以管理自己的题库和考试
2. **协作功能**: 支持题目分享和团队协作
3. **统计分析**: 提供教师个人的教学数据分析
4. **快速操作**: 简化的操作流程，提高工作效率
5. **权限控制**: 基于角色的功能权限控制

### 问卷管理功能特点
1. **多类型支持**: 支持课程评价、教学效果、毕业生调查等多种问卷类型
2. **灵活设计**: 支持多种题型和逻辑跳转，满足复杂评价需求
3. **数据分析**: 提供丰富的统计分析和可视化图表
4. **匿名保护**: 支持匿名问卷，保护填写者隐私
5. **多渠道发布**: 支持邮件、链接、二维码等多种发布方式
6. **实时监控**: 实时监控问卷回收情况和数据质量

## 目录结构规范

### 文件命名规范
- 所有Vue文件使用PascalCase命名法
- 文件名应体现页面功能，避免使用通用名称
- 组件名称应与文件名保持一致

### 目录组织规范
- 按功能模块进行目录划分
- 每个子目录最多保持一层深度
- 相关功能的页面放在同一目录下

### 功能分类体系
1. **考核管理**: 试卷创建、考核执行相关功能
2. **题库管理**: 题目的增删改查和分类管理
3. **成绩管理**: 成绩录入、统计和分析功能
4. **答案管理**: 标准答案和评分规则管理
5. **问卷管理**: 教学评价问卷的创建、发布和数据分析

## 与其他模块的关系

### 与Training模块的关系
- Training模块负责课程体系和培养目标设置
- Assessment模块基于Training模块的课程目标进行考核设计
- 考核结果用于验证Training模块设定的培养目标达成情况

### 与Task模块的关系
- Task模块负责教学任务的分配和管理
- Assessment模块为Task模块中的教学任务提供考核支持
- 考核成绩数据流向Task模块进行汇总统计

### 与Base模块的关系
- 使用Base模块的学生、教师、班级等基础数据
- 考核结果按Base模块的组织结构进行统计分析

## 注意事项

1. **数据安全**: 考核题目和答案属于敏感数据，需要严格的权限控制
2. **版本管理**: 题目和试卷的版本管理很重要，避免考核过程中的数据不一致
3. **性能优化**: 大量题目和成绩数据的处理需要注意性能优化
4. **用户体验**: 考核过程中的用户体验要流畅，避免操作卡顿
5. **数据备份**: 重要的考核数据需要及时备份，防止数据丢失 
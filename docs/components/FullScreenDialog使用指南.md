# FullScreenDialog 全屏弹窗组件使用指南

## 概述

FullScreenDialog 是一个功能强大的全屏弹窗组件，基于 TDesign Dialog 封装，专为复杂内容展示和配置场景设计。本指南将详细介绍组件的各种使用方式和最佳实践。

## 快速开始

### 1. 基础导入

```typescript
import FullScreenDialog from '@/components/FullScreenDialog/index.vue'
```

### 2. 基础使用

```vue
<template>
  <div>
    <t-button @click="openDialog">打开全屏弹窗</t-button>
    
    <FullScreenDialog
      v-model:visible="visible"
      header="基础全屏弹窗"
      @confirm="handleConfirm"
      @cancel="handleCancel"
    >
      <div style="padding: 20px;">
        <h3>弹窗内容</h3>
        <p>这里是弹窗的主要内容区域</p>
      </div>
    </FullScreenDialog>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import FullScreenDialog from '@/components/FullScreenDialog/index.vue'

const visible = ref(false)

const openDialog = () => {
  visible.value = true
}

const handleConfirm = () => {
  console.log('确认操作')
  visible.value = false
}

const handleCancel = () => {
  console.log('取消操作')
  visible.value = false
}
</script>
```

## 实际应用案例

### 案例1：考核内容详细配置

这是一个实际的教育系统中考核内容配置的案例，展示了如何在复杂表单场景中使用全屏弹窗。

```vue
<template>
  <div class="assessment-management">
    <t-button theme="primary" @click="openDetailedConfig">
      配置考核内容
    </t-button>

    <FullScreenDialog
      v-model:visible="detailedConfigVisible"
      header="考核内容详细配置"
      :animation="{
        type: 'zoom',
        duration: 400,
        easing: 'ease-out'
      }"
      :overlay="{
        blur: true,
        opacity: 0.7,
        color: 'rgba(0, 0, 0, 0.7)'
      }"
      :confirm-btn="{ 
        content: '保存配置', 
        loading: saving,
        disabled: !isFormValid 
      }"
      @confirm="saveConfiguration"
      @cancel="cancelConfiguration"
      @animation-start="onConfigAnimationStart"
      @animation-end="onConfigAnimationEnd"
    >
      <div class="config-content">
        <!-- 课程目标概览 -->
        <section class="objectives-section">
          <h3>课程目标概览</h3>
          <div class="objectives-grid">
            <div 
              v-for="objective in courseObjectives" 
              :key="objective.id"
              class="objective-card"
            >
              <div class="objective-header">
                <span class="objective-number">{{ objective.number }}</span>
                <span class="objective-weight">{{ objective.weight }}%</span>
              </div>
              <div class="objective-content">
                <h4>{{ objective.name }}</h4>
                <p>{{ objective.description }}</p>
              </div>
            </div>
          </div>
        </section>

        <!-- 题目配置表格 -->
        <section class="questions-section">
          <h3>题目配置</h3>
          <div class="questions-table">
            <t-table
              :data="questionData"
              :columns="questionColumns"
              row-key="id"
              max-height="400px"
            >
              <!-- 自定义列内容 -->
              <template #questionType="{ row, rowIndex }">
                <t-select
                  v-model="questionData[rowIndex].type"
                  placeholder="选择题目类型"
                  @change="updateQuestionType(rowIndex, $event)"
                >
                  <t-option 
                    v-for="type in questionTypes" 
                    :key="type.value"
                    :value="type.value" 
                    :label="type.label" 
                  />
                </t-select>
              </template>
              
              <template #score="{ row, rowIndex }">
                <t-input-number
                  v-model="questionData[rowIndex].score"
                  :min="0"
                  :max="100"
                  @change="calculateTotalScore"
                />
              </template>
            </t-table>
          </div>
        </section>

        <!-- 汇总信息 -->
        <section class="summary-section">
          <h3>配置汇总</h3>
          <t-descriptions :column="3">
            <t-descriptions-item label="总题目数">{{ totalQuestions }}</t-descriptions-item>
            <t-descriptions-item label="总分值">{{ totalScore }}</t-descriptions-item>
            <t-descriptions-item label="平均分值">{{ averageScore }}</t-descriptions-item>
          </t-descriptions>
        </section>
      </div>
    </FullScreenDialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { MessagePlugin } from 'tdesign-vue-next'

const detailedConfigVisible = ref(false)
const saving = ref(false)

// 模拟数据
const courseObjectives = ref([
  {
    id: 1,
    number: '目标1',
    name: '掌握基础知识',
    description: '理解和掌握课程的基础理论知识',
    weight: 40
  },
  {
    id: 2,
    number: '目标2', 
    name: '应用能力',
    description: '能够运用所学知识解决实际问题',
    weight: 60
  }
])

const questionData = ref([
  { id: 1, title: '基础概念题', type: 'single', score: 10 },
  { id: 2, title: '应用分析题', type: 'essay', score: 20 },
  { id: 3, title: '综合设计题', type: 'design', score: 30 }
])

const questionTypes = [
  { value: 'single', label: '单选题' },
  { value: 'multiple', label: '多选题' },
  { value: 'essay', label: '论述题' },
  { value: 'design', label: '设计题' }
]

const questionColumns = [
  { colKey: 'title', title: '题目标题', width: 200 },
  { colKey: 'questionType', title: '题目类型', width: 150 },
  { colKey: 'score', title: '分值', width: 100 }
]

// 计算属性
const totalQuestions = computed(() => questionData.value.length)
const totalScore = computed(() => 
  questionData.value.reduce((sum, q) => sum + (q.score || 0), 0)
)
const averageScore = computed(() => 
  totalQuestions.value > 0 ? (totalScore.value / totalQuestions.value).toFixed(1) : 0
)
const isFormValid = computed(() => 
  questionData.value.every(q => q.type && q.score > 0)
)

// 事件处理
const openDetailedConfig = () => {
  detailedConfigVisible.value = true
}

const saveConfiguration = async () => {
  saving.value = true
  try {
    // 模拟保存操作
    await new Promise(resolve => setTimeout(resolve, 2000))
    MessagePlugin.success('配置保存成功')
    detailedConfigVisible.value = false
  } catch (error) {
    MessagePlugin.error('保存失败，请重试')
  } finally {
    saving.value = false
  }
}

const cancelConfiguration = () => {
  detailedConfigVisible.value = false
  MessagePlugin.info('已取消配置')
}

const updateQuestionType = (index: number, type: string) => {
  questionData.value[index].type = type
}

const calculateTotalScore = () => {
  // 触发响应式更新
  console.log('总分更新:', totalScore.value)
}

const onConfigAnimationStart = () => {
  console.log('配置弹窗动画开始')
}

const onConfigAnimationEnd = () => {
  console.log('配置弹窗动画结束')
}
</script>

<style lang="less" scoped>
.config-content {
  padding: 24px;
  
  section {
    margin-bottom: 32px;
    
    h3 {
      font-size: 18px;
      color: var(--td-text-color-primary);
      margin-bottom: 16px;
      border-bottom: 2px solid var(--td-brand-color);
      padding-bottom: 8px;
    }
  }
  
  .objectives-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 16px;
    
    .objective-card {
      padding: 16px;
      border: 1px solid var(--td-border-level-1-color);
      border-radius: 8px;
      background: var(--td-bg-color-container);
      
      .objective-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12px;
        
        .objective-number {
          font-weight: 600;
          color: var(--td-brand-color);
        }
        
        .objective-weight {
          background: var(--td-brand-color-light);
          color: var(--td-brand-color);
          padding: 2px 8px;
          border-radius: 4px;
          font-size: 12px;
        }
      }
      
      .objective-content {
        h4 {
          font-size: 14px;
          margin-bottom: 8px;
          color: var(--td-text-color-primary);
        }
        
        p {
          font-size: 13px;
          color: var(--td-text-color-secondary);
          line-height: 1.5;
          margin: 0;
        }
      }
    }
  }
}
</style>
```

### 案例2：数据表格编辑器

适用于需要编辑大量数据的场景：

```vue
<template>
  <div class="data-editor">
    <t-button theme="primary" @click="openEditor">
      打开数据编辑器
    </t-button>

    <FullScreenDialog
      v-model:visible="editorVisible"
      header="数据表格编辑器"
      :animation="{
        type: 'slide',
        duration: 350,
        easing: 'ease-out'
      }"
      :overlay="{
        blur: false,
        opacity: 0.5,
        color: 'rgba(0, 0, 0, 0.5)'
      }"
      :confirm-btn="{ content: '保存数据', loading: saving }"
      @confirm="saveData"
      @cancel="cancelEdit"
    >
      <div class="editor-content">
        <!-- 工具栏 -->
        <div class="toolbar">
          <t-space>
            <t-button theme="primary" variant="outline" @click="addRow">
              <template #icon><t-icon name="add" /></template>
              添加行
            </t-button>
            <t-button theme="danger" variant="outline" @click="deleteSelected">
              <template #icon><t-icon name="delete" /></template>
              删除选中
            </t-button>
            <t-button theme="default" @click="exportData">
              <template #icon><t-icon name="download" /></template>
              导出数据
            </t-button>
          </t-space>
        </div>

        <!-- 数据表格 -->
        <div class="table-container">
          <t-table
            :data="tableData"
            :columns="editableColumns"
            :selected-row-keys="selectedRowKeys"
            row-key="id"
            :pagination="pagination"
            select-on-row-click
            @select-change="handleSelectChange"
            @page-change="handlePageChange"
          >
            <!-- 可编辑单元格 -->
            <template #name="{ row, rowIndex }">
              <t-input
                v-model="tableData[rowIndex].name"
                placeholder="请输入姓名"
                @blur="validateRow(rowIndex)"
              />
            </template>
            
            <template #email="{ row, rowIndex }">
              <t-input
                v-model="tableData[rowIndex].email"
                placeholder="请输入邮箱"
                @blur="validateEmail(rowIndex)"
              />
            </template>
            
            <template #status="{ row, rowIndex }">
              <t-select
                v-model="tableData[rowIndex].status"
                placeholder="选择状态"
              >
                <t-option value="active" label="激活" />
                <t-option value="inactive" label="停用" />
                <t-option value="pending" label="待审核" />
              </t-select>
            </template>
          </t-table>
        </div>

        <!-- 批量操作面板 -->
        <div v-if="selectedRowKeys.length > 0" class="batch-panel">
          <t-alert theme="info" :message="`已选中 ${selectedRowKeys.length} 条记录`">
            <template #operation>
              <t-space>
                <t-button size="small" @click="batchUpdate">批量更新</t-button>
                <t-button size="small" theme="danger" @click="batchDelete">批量删除</t-button>
              </t-space>
            </template>
          </t-alert>
        </div>
      </div>
    </FullScreenDialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { MessagePlugin } from 'tdesign-vue-next'

const editorVisible = ref(false)
const saving = ref(false)
const selectedRowKeys = ref<string[]>([])

// 表格数据
const tableData = ref([
  { id: '1', name: '张三', email: '<EMAIL>', status: 'active' },
  { id: '2', name: '李四', email: '<EMAIL>', status: 'inactive' },
  { id: '3', name: '王五', email: '<EMAIL>', status: 'pending' }
])

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 100
})

// 可编辑列配置
const editableColumns = [
  { colKey: 'id', title: 'ID', width: 80 },
  { colKey: 'name', title: '姓名', width: 150 },
  { colKey: 'email', title: '邮箱', width: 200 },
  { colKey: 'status', title: '状态', width: 120 }
]

// 事件处理
const openEditor = () => {
  editorVisible.value = true
}

const saveData = async () => {
  saving.value = true
  try {
    await new Promise(resolve => setTimeout(resolve, 1500))
    MessagePlugin.success('数据保存成功')
    editorVisible.value = false
  } finally {
    saving.value = false
  }
}

const cancelEdit = () => {
  editorVisible.value = false
}

const addRow = () => {
  const newId = String(Date.now())
  tableData.value.push({
    id: newId,
    name: '',
    email: '',
    status: 'pending'
  })
}

const deleteSelected = () => {
  tableData.value = tableData.value.filter(
    row => !selectedRowKeys.value.includes(row.id)
  )
  selectedRowKeys.value = []
  MessagePlugin.success('删除成功')
}

const handleSelectChange = (value: string[]) => {
  selectedRowKeys.value = value
}

const handlePageChange = (pageInfo: any) => {
  pagination.current = pageInfo.current
  pagination.pageSize = pageInfo.pageSize
}

const validateRow = (index: number) => {
  const row = tableData.value[index]
  if (!row.name.trim()) {
    MessagePlugin.warning('姓名不能为空')
  }
}

const validateEmail = (index: number) => {
  const row = tableData.value[index]
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  if (row.email && !emailRegex.test(row.email)) {
    MessagePlugin.warning('邮箱格式不正确')
  }
}

const batchUpdate = () => {
  MessagePlugin.info('批量更新功能')
}

const batchDelete = () => {
  deleteSelected()
}

const exportData = () => {
  MessagePlugin.info('导出数据功能')
}
</script>

<style lang="less" scoped>
.editor-content {
  padding: 20px;
  height: 100%;
  display: flex;
  flex-direction: column;
  
  .toolbar {
    margin-bottom: 16px;
    padding-bottom: 16px;
    border-bottom: 1px solid var(--td-border-level-1-color);
  }
  
  .table-container {
    flex: 1;
    overflow: hidden;
  }
  
  .batch-panel {
    margin-top: 16px;
    padding-top: 16px;
    border-top: 1px solid var(--td-border-level-1-color);
  }
}
</style>
```

### 案例3：图表数据分析

适用于数据可视化和分析场景：

```vue
<template>
  <div class="chart-analysis">
    <t-button theme="primary" @click="openAnalysis">
      打开数据分析
    </t-button>

    <FullScreenDialog
      v-model:visible="analysisVisible"
      header="数据分析仪表板"
      :animation="{
        type: 'fade',
        duration: 300,
        easing: 'ease-in-out'
      }"
      :overlay="{
        blur: true,
        opacity: 0.8,
        color: 'rgba(0, 0, 0, 0.8)'
      }"
      :confirm-btn="{ content: '导出报告' }"
      :cancel-btn="{ content: '关闭' }"
      @confirm="exportReport"
      @cancel="closeAnalysis"
    >
      <div class="analysis-content">
        <!-- 筛选控件 -->
        <div class="filters">
          <t-space>
            <t-date-picker
              v-model="dateRange"
              mode="range"
              placeholder="选择日期范围"
              @change="updateCharts"
            />
            <t-select
              v-model="selectedCategory"
              placeholder="选择分类"
              @change="updateCharts"
            >
              <t-option value="all" label="全部" />
              <t-option value="sales" label="销售" />
              <t-option value="marketing" label="营销" />
              <t-option value="support" label="客服" />
            </t-select>
            <t-button theme="primary" @click="refreshData">
              <template #icon><t-icon name="refresh" /></template>
              刷新数据
            </t-button>
          </t-space>
        </div>

        <!-- 图表网格 -->
        <div class="charts-grid">
          <!-- 趋势图 -->
          <div class="chart-card">
            <h4>趋势分析</h4>
            <div class="chart-placeholder">
              <div class="mock-chart trend-chart">
                <div class="chart-line"></div>
                <div class="chart-points">
                  <span v-for="i in 7" :key="i" class="point"></span>
                </div>
              </div>
            </div>
          </div>

          <!-- 饼图 -->
          <div class="chart-card">
            <h4>分布分析</h4>
            <div class="chart-placeholder">
              <div class="mock-chart pie-chart">
                <div class="pie-slice slice-1"></div>
                <div class="pie-slice slice-2"></div>
                <div class="pie-slice slice-3"></div>
                <div class="pie-slice slice-4"></div>
              </div>
            </div>
          </div>

          <!-- 柱状图 -->
          <div class="chart-card">
            <h4>对比分析</h4>
            <div class="chart-placeholder">
              <div class="mock-chart bar-chart">
                <div class="bar" style="height: 60%"></div>
                <div class="bar" style="height: 80%"></div>
                <div class="bar" style="height: 45%"></div>
                <div class="bar" style="height: 90%"></div>
                <div class="bar" style="height: 70%"></div>
              </div>
            </div>
          </div>

          <!-- 数据表格 -->
          <div class="chart-card full-width">
            <h4>详细数据</h4>
            <t-table
              :data="analyticsData"
              :columns="analyticsColumns"
              :pagination="{ pageSize: 5 }"
              size="small"
            />
          </div>
        </div>
      </div>
    </FullScreenDialog>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { MessagePlugin } from 'tdesign-vue-next'

const analysisVisible = ref(false)
const dateRange = ref([])
const selectedCategory = ref('all')

// 模拟分析数据
const analyticsData = ref([
  { id: 1, metric: '页面访问量', value: 1234, change: '+12%' },
  { id: 2, metric: '用户注册量', value: 567, change: '+8%' },
  { id: 3, metric: '转化率', value: '3.45%', change: '-2%' },
  { id: 4, metric: '平均停留时间', value: '2:34', change: '+15%' }
])

const analyticsColumns = [
  { colKey: 'metric', title: '指标', width: 150 },
  { colKey: 'value', title: '数值', width: 100 },
  { colKey: 'change', title: '变化', width: 80 }
]

const openAnalysis = () => {
  analysisVisible.value = true
}

const closeAnalysis = () => {
  analysisVisible.value = false
}

const updateCharts = () => {
  MessagePlugin.info('图表数据已更新')
}

const refreshData = () => {
  MessagePlugin.loading('正在刷新数据...')
  setTimeout(() => {
    MessagePlugin.close()
    MessagePlugin.success('数据刷新完成')
  }, 1500)
}

const exportReport = () => {
  MessagePlugin.success('报告导出成功')
  analysisVisible.value = false
}
</script>

<style lang="less" scoped>
.analysis-content {
  padding: 20px;
  height: 100%;
  
  .filters {
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 1px solid var(--td-border-level-1-color);
  }
  
  .charts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    
    .chart-card {
      padding: 16px;
      border: 1px solid var(--td-border-level-1-color);
      border-radius: 8px;
      background: var(--td-bg-color-container);
      
      &.full-width {
        grid-column: 1 / -1;
      }
      
      h4 {
        margin: 0 0 16px 0;
        font-size: 16px;
        color: var(--td-text-color-primary);
      }
      
      .chart-placeholder {
        height: 200px;
        display: flex;
        align-items: center;
        justify-content: center;
        
        .mock-chart {
          width: 100%;
          height: 100%;
          position: relative;
          
          &.trend-chart {
            .chart-line {
              position: absolute;
              top: 50%;
              left: 10%;
              right: 10%;
              height: 2px;
              background: linear-gradient(90deg, var(--td-brand-color), var(--td-success-color));
              transform: translateY(-50%);
            }
            
            .chart-points {
              position: absolute;
              top: 0;
              left: 10%;
              right: 10%;
              height: 100%;
              display: flex;
              align-items: center;
              justify-content: space-between;
              
              .point {
                width: 8px;
                height: 8px;
                border-radius: 50%;
                background: var(--td-brand-color);
              }
            }
          }
          
          &.pie-chart {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            position: relative;
            margin: 0 auto;
            
            .pie-slice {
              position: absolute;
              width: 100%;
              height: 100%;
              border-radius: 50%;
              
              &.slice-1 {
                background: conic-gradient(var(--td-brand-color) 0deg 90deg, transparent 90deg);
              }
              &.slice-2 {
                background: conic-gradient(transparent 0deg 90deg, var(--td-success-color) 90deg 180deg, transparent 180deg);
              }
              &.slice-3 {
                background: conic-gradient(transparent 0deg 180deg, var(--td-warning-color) 180deg 270deg, transparent 270deg);
              }
              &.slice-4 {
                background: conic-gradient(transparent 0deg 270deg, var(--td-error-color) 270deg 360deg);
              }
            }
          }
          
          &.bar-chart {
            display: flex;
            align-items: flex-end;
            justify-content: space-around;
            height: 150px;
            padding: 0 20px;
            
            .bar {
              width: 30px;
              background: var(--td-brand-color);
              border-radius: 4px 4px 0 0;
              min-height: 20px;
            }
          }
        }
      }
    }
  }
}
</style>
```

## 动画效果选择指南

### 根据使用场景选择动画

1. **表单配置类**：使用 `zoom` 或 `scale`
   - 强调重要性，适合复杂配置界面
   - 时长：300-500ms

2. **数据展示类**：使用 `fade` 或 `slide`
   - 平滑过渡，不干扰数据阅读
   - 时长：200-400ms

3. **确认操作类**：使用 `bounce`
   - 引起注意，适合重要操作确认
   - 时长：500-700ms

4. **创新展示类**：使用 `flip`
   - 科技感强，适合现代化界面
   - 时长：400-600ms

### 遮罩效果搭配建议

1. **专业应用**：
   ```javascript
   overlay: {
     blur: true,
     opacity: 0.7,
     color: 'rgba(0, 0, 0, 0.7)'
   }
   ```

2. **轻量展示**：
   ```javascript
   overlay: {
     blur: false,
     opacity: 0.4,
     color: 'rgba(0, 0, 0, 0.4)'
   }
   ```

3. **品牌化界面**：
   ```javascript
   overlay: {
     blur: true,
     opacity: 0.6,
     color: 'rgba(59, 130, 246, 0.3)'
   }
   ```

## 性能优化建议

1. **大数据量场景**：
   - 使用虚拟滚动
   - 分页加载
   - 懒加载非关键内容

2. **移动端适配**：
   - 简化动画效果
   - 减少动画时长
   - 优先使用 `fade` 或 `slide`

3. **低性能设备**：
   - 提供动画开关选项
   - 响应 `prefers-reduced-motion`
   - 使用 `none` 动画类型

## 常见问题解决

### 1. 下拉组件层级问题

```vue
<template>
  <FullScreenDialog>
    <t-select attach="body">
      <!-- 选项内容 -->
    </t-select>
  </FullScreenDialog>
</template>
```

### 2. 表单验证集成

```javascript
const isFormValid = computed(() => {
  // 验证逻辑
  return formData.value.every(field => field.valid)
})

// 在确认按钮中使用
:confirm-btn="{ 
  content: '保存', 
  disabled: !isFormValid 
}"
```

### 3. 数据缓存处理

```javascript
// 打开弹窗时备份数据
const openDialog = () => {
  backupData.value = JSON.parse(JSON.stringify(formData.value))
  dialogVisible.value = true
}

// 取消时恢复数据
const cancelDialog = () => {
  formData.value = backupData.value
  dialogVisible.value = false
}
```

## 总结

FullScreenDialog 组件提供了丰富的配置选项和动画效果，能够满足各种复杂场景的需求。通过合理选择动画类型和遮罩配置，可以为用户提供优秀的交互体验。在实际使用中，建议根据具体的业务场景和用户群体来选择合适的配置方案。 
# 前端开发规范

## 目录结构规范

### 页面目录结构 (pages/)

页面目录按照数据库主表进行组织，每个主表对应一个目录：

```
pages/
├── system/           # 系统管理模块 (sys_*)
│   ├── menu/         # 菜单管理 (sys_menu)
│   ├── role/         # 角色管理 (sys_role)
│   ├── user/         # 用户管理 (sys_base_user)
│   ├── dict/         # 字典管理 (sys_dict_type, sys_dict_data)
│   ├── log/          # 日志管理
│   ├── backup/       # 数据备份
│   ├── document/     # 文档管理
│   └── monitor/      # 系统监控
├── base/             # 基础数据模块 (base_*)
│   ├── academy/      # 学院管理 (base_academy)
│   ├── major/        # 专业管理 (base_major)
│   ├── classes/      # 班级管理 (base_classes)
│   ├── student/      # 学生管理 (base_student)
│   ├── teacher/      # 教师管理 (base_teacher)
│   └── standard/     # 标准管理 (base_standard)
├── training/         # 培养方案模块 (tp_*)
│   ├── plan/         # 培养计划 (tp_plan)
│   ├── course/       # 课程体系 (tp_course)
│   ├── goal/         # 培养目标 (tp_eo)
│   ├── requirement/  # 毕业要求 (tp_po)
│   ├── matrix/       # 支撑矩阵 (tp_po_matrix)
│   └── route/        # 课程路线 (tp_course_route)
├── task/             # 教学任务模块 (task_*)
│   ├── worklist/     # 教学任务 (task_worklist)
│   └── score/        # 成绩管理 (task_score)
├── assessment/       # 考核评价模块 (assessment_*, repository_*)
│   ├── exam/         # 考核管理 (assessment)
│   ├── score/        # 成绩详情 (assessment_score_detail)
│   ├── question/     # 题库管理 (repository_question)
│   └── answer/       # 答案管理 (repository_answer)
├── graph/            # 知识图谱模块 (graph_*)
│   ├── nodes/        # 节点管理 (graph_nodes)
│   └── links/        # 链接管理 (graph_links)
├── etl/              # 数据管理模块 (etl_*)
│   └── data/         # 数据推送管理 (暂无对应表结构)
└── dashboard/        # 工作台页面
    ├── CourseLeader.vue    # 课程负责人工作台
    ├── MajorDashboard.vue  # 专业工作台
    ├── TeacherDashboard.vue # 教师工作台
    ├── StudentDashboard.vue # 学生工作台
    └── AdminDashboard.vue   # 管理员工作台
```

### API目录结构 (api/)

API文件按照对应的数据库主表进行组织：

```
api/
├── system/           # 系统管理API
│   ├── menu.ts       # 菜单API (sys_menu)
│   ├── role.ts       # 角色API (sys_role)
│   ├── user.ts       # 用户API (sys_base_user)
│   ├── dict.ts       # 字典API (sys_dict_type, sys_dict_data)
│   ├── log.ts        # 日志API
│   ├── backup.ts     # 备份API
│   ├── document.ts   # 文档API
│   └── monitor.ts    # 监控API
├── base/             # 基础数据API
│   ├── academy.ts    # 学院API (base_academy)
│   ├── major.ts      # 专业API (base_major)
│   ├── classes.ts    # 班级API (base_classes)
│   ├── student.ts    # 学生API (base_student)
│   ├── teacher.ts    # 教师API (base_teacher)
│   └── standard.ts   # 标准API (base_standard)
├── training/         # 培养方案API
│   ├── plan.ts       # 培养计划API (tp_plan)
│   ├── course.ts     # 课程API (tp_course)
│   ├── goal.ts       # 培养目标API (tp_eo)
│   ├── requirement.ts # 毕业要求API (tp_po)
│   ├── matrix.ts     # 支撑矩阵API (tp_po_matrix)
│   └── route.ts      # 课程路线API (tp_course_route)
├── task/             # 教学任务API
│   ├── worklist.ts   # 教学任务API (task_worklist)
│   └── score.ts      # 成绩API (task_score)
├── assessment/       # 考核评价API
│   ├── exam.ts       # 考核API (assessment)
│   ├── score.ts      # 成绩详情API (assessment_score_detail)
│   ├── question.ts   # 题库API (repository_question)
│   └── answer.ts     # 答案API (repository_answer)
├── graph/            # 知识图谱API
│   ├── nodes.ts      # 节点API (graph_nodes)
│   └── links.ts      # 链接API (graph_links)
└── etl/              # 数据管理API
    └── data.ts       # 数据推送API (暂无对应表结构)
```

## 命名规范

### API 方法命名规范

1. 查询类方法以 `get` 开头
   ```typescript
   // 获取菜单树
   export function getMenuTree() { ... }
   
   // 获取角色列表
   export function getRoleList() { ... }
   ```

2. 更新类方法以 `update` 开头
   ```typescript
   // 更新菜单
   export function updateMenu() { ... }
   
   // 更新角色状态
   export function updateRoleStatus() { ... }
   ```

3. 添加类方法以 `add` 开头
   ```typescript
   // 添加菜单
   export function addMenu() { ... }
   
   // 添加角色
   export function addRole() { ... }
   ```

4. 删除类方法以 `delete` 开头
   ```typescript
   // 删除菜单
   export function deleteMenu() { ... }
   
   // 删除角色
   export function deleteRole() { ... }
   ```

### 组件方法命名规范

1. 获取数据方法以 `fetch` 开头
   ```typescript
   // 获取菜单列表
   const fetchMenuList = async () => { ... }
   
   // 获取角色列表
   const fetchRoleList = async () => { ... }
   ```

2. 操作处理方法以 `handle` 开头
   ```typescript
   // 保存菜单
   const handleSave = async () => { ... }
   
   // 删除角色
   const handleDelete = async () => { ... }
   ```

## 路由配置规范

### 路由模式配置

系统支持两种路由配置模式，可以通过 `src/config/setting.config.ts` 中的配置进行切换：

#### 1. 前端路由模式 (intelligence)

路由配置完全在前端定义，适用于路由结构相对固定的场景。

```typescript
// src/config/setting.config.ts
export const settingConfig = {
  // intelligence(前端导出路由)和all(后端导出路由)两种方式
  authentication: 'intelligence',
  // 是否开启roles字段进行角色权限控制
  rolesControl: true,
}
```

**特点**：
- 路由在 `src/router/index.ts` 中的 `asyncRoutes` 数组中定义
- 前端直接控制路由结构和权限
- 适合路由结构相对固定的小型项目
- 权限控制通过路由meta中的roles字段实现

#### 2. 后端路由模式 (all) - 推荐

路由配置从后端API动态获取，适用于需要灵活配置路由和权限的场景。

```typescript
// src/config/setting.config.ts
export const settingConfig = {
  // intelligence(前端导出路由)和all(后端导出路由)两种方式
  authentication: 'all',
  // 后端完全处理角色权限时可设置为false
  rolesControl: false,
}
```

**特点**：
- 路由结构从后端API (`/router`) 动态获取
- 后端可以根据用户角色返回不同的路由结构
- 支持动态权限控制和菜单配置
- 适合大型项目和多角色权限管理
- 路由变更无需重新部署前端

#### 3. 权限控制配置

```typescript
// src/config/setting.config.ts
export const settingConfig = {
  // 路由模式
  authentication: 'all', // 或 'intelligence'
  
  // 是否开启roles字段进行角色权限控制
  // all模式：如果后端完全处理角色并进行json组装，可设置false
  // intelligence模式：建议设置true，通过前端路由meta.roles控制
  rolesControl: true,
  
  // 是否开启登录拦截
  loginInterception: true,
}
```

#### 4. 后端路由API格式

当使用后端路由模式时，后端需要返回符合以下格式的路由数据：

```typescript
// 后端路由API响应格式
interface BackendRoute {
  path: string;
  name: string;
  component: string; // 'Layout' | 'HeaderOnly' | 'DynamicSidebar' | 页面组件路径
  redirect?: string;
  meta: {
    title: {
      zh_CN: string;
      en_US: string;
    };
    icon?: string;
    hidden?: boolean;
    orderNo?: number;
    // 其他meta属性...
  };
  children?: BackendRoute[];
}
```

#### 5. 模式切换注意事项

1. **开发环境切换**：修改配置后需要重启开发服务器
2. **生产环境部署**：确保后端路由API正常可用
3. **权限一致性**：前后端路由模式的权限控制逻辑要保持一致
4. **路由缓存**：切换模式时可能需要清除浏览器缓存

### 后端路由配置说明

当使用后端路由模式（`authentication: 'all'`）时，路由配置通过系统管理中的菜单管理页面进行配置和维护。

#### 1. 菜单管理页面访问

- **访问路径**：`/system/menu`
- **页面功能**：配置和管理系统的路由结构、菜单显示和权限控制
- **权限要求**：需要管理员权限才能访问

#### 2. 菜单类型说明

系统支持三种菜单类型，每种类型有不同的用途和配置要求：

##### 目录类型 (type = 0)
- **用途**：作为路由容器，通常包含多个子菜单
- **组件类型**：使用布局组件
- **可选布局**：
  - `Layout`：标准布局（包含侧边栏、顶部导航）
  - `HeaderOnly`：仅头部布局（适合独立页面）
  - `DynamicSidebar`：动态侧边栏布局（适合模块化管理）
- **路由特点**：通常有 `redirect` 属性指向默认子路由

##### 菜单类型 (type = 1)
- **用途**：指向具体的页面组件
- **组件类型**：页面组件路径
- **路径格式**：相对路径，如 `pages/system/user/index`
- **路由特点**：实际的页面路由，用户可以直接访问

##### 按钮类型 (type = 2)
- **用途**：权限控制，不产生实际路由
- **组件类型**：无需设置组件路径
- **权限控制**：通过权限标识控制按钮显示和功能访问
- **路由特点**：不会在菜单中显示，仅用于权限判断

#### 3. 配置字段说明

| 字段名 | 类型 | 必填 | 说明 | 示例 |
|--------|------|------|------|------|
| `title` | string | 是 | 菜单显示名称 | "用户管理" |
| `name` | string | 是* | 路由名称（目录和菜单必填） | "SystemUser" |
| `path` | string | 是* | 路由路径（目录和菜单必填） | "/system/user" |
| `component` | string | 是* | 组件路径（目录和菜单必填） | "Layout" 或 "pages/system/user/index" |
| `code` | string | 否 | 权限标识 | "system:user:list" |
| `icon` | string | 否 | 菜单图标 | "user-avatar" |
| `sort` | number | 否 | 排序号 | 10 |
| `hidden` | boolean | 否 | 是否隐藏菜单 | false |
| `redirect` | string | 否 | 重定向路径 | "/system/user/list" |

*注：按钮类型不需要填写 `name`、`path`、`component` 字段

#### 4. 配置示例

##### 目录配置示例
```json
{
  "title": "系统管理",
  "name": "System",
  "path": "/system",
  "component": "Layout",
  "type": 0,
  "icon": "setting",
  "sort": 1,
  "hidden": false,
  "redirect": "/system/home"
}
```

##### 菜单配置示例
```json
{
  "title": "用户管理",
  "name": "SystemUser",
  "path": "user",
  "component": "pages/system/user/index",
  "type": 1,
  "icon": "user-avatar",
  "sort": 10,
  "hidden": false,
  "code": "system:user:list"
}
```

##### 按钮配置示例
```json
{
  "title": "新增用户",
  "type": 2,
  "code": "system:user:add",
  "sort": 1,
  "hidden": false
}
```

#### 5. 配置流程

1. **访问菜单管理**：登录系统后访问 `/system/menu` 页面
2. **选择上级菜单**：为新菜单选择合适的父级菜单
3. **设置菜单类型**：根据需要选择目录、菜单或按钮类型
4. **填写基本信息**：输入菜单名称、路由信息等
5. **设置权限标识**：配置权限控制代码（可选）
6. **调整显示设置**：设置图标、排序、是否隐藏等
7. **保存配置**：提交表单保存配置

#### 6. 层级关系

- **一级目录**：通常使用 `Layout` 布局，作为模块的容器
- **二级菜单**：指向具体页面，设置相对路径
- **三级按钮**：权限控制，挂载在具体菜单下

**示例层级结构**：
```
系统管理 (目录, Layout)
├── 用户管理 (菜单, pages/system/user/index)
│   ├── 新增用户 (按钮, system:user:add)
│   ├── 编辑用户 (按钮, system:user:edit)
│   └── 删除用户 (按钮, system:user:delete)
└── 角色管理 (菜单, pages/system/role/index)
    ├── 新增角色 (按钮, system:role:add)
    └── 编辑角色 (按钮, system:role:edit)
```

#### 7. 注意事项

1. **路径规范**：
   - 一级目录路径以 `/` 开头，如 `/system`
   - 二级菜单路径为相对路径，如 `user`（最终路径为 `/system/user`）
   - 按钮类型不需要设置路径

2. **组件路径**：
   - 目录类型选择布局组件：`Layout`、`HeaderOnly`、`DynamicSidebar`
   - 菜单类型输入页面组件路径：`pages/system/user/index`
   - 按钮类型无需设置组件路径

3. **权限控制**：
   - 权限标识建议使用层级格式：`模块:功能:操作`
   - 如：`system:user:list`、`system:user:add`、`system:user:edit`

4. **数据同步**：
   - 菜单配置保存后会自动同步到前端路由
   - 修改菜单配置后，用户需要重新登录或刷新页面才能看到变化

5. **数据库存储**：
   - 菜单配置数据存储在 `sys_menu` 表中
   - 通过 `/menu/route/list` API 接口获取路由数据

#### 8. 常见问题

**Q: 新增菜单后前端看不到？**
A: 检查菜单的 `hidden` 字段是否设置为 `true`，以及当前用户是否有相应权限。

**Q: 页面路由无法访问？**
A: 检查组件路径是否正确，确保对应的 Vue 组件文件存在。

**Q: 按钮权限不生效？**
A: 检查按钮的权限标识是否正确，以及后端权限验证逻辑是否正确实现。

**Q: 菜单排序混乱？**
A: 检查 `sort` 字段的设置，数值越小排序越靠前。

### 页面操作指南

本节从页面操作角度详细说明如何在菜单管理页面中添加和配置菜单。

#### 1. 页面访问和布局

##### 访问菜单管理页面
1. **登录系统**：使用管理员账号登录系统
2. **导航到菜单管理**：侧边栏选择"系统管理" → "菜单管理"
3. **页面URL**：`/system/menu`

##### 页面布局说明
- **顶部操作区**：包含"新增"、"批量删除"、"刷新"按钮
- **主体表格区**：树形表格显示现有菜单结构
- **表单对话框**：用于新增/编辑菜单的弹窗表单
- **图标选择器**：可视化选择菜单图标的弹窗

#### 2. 添加菜单的三种方式

##### 方式一：新增根级菜单
1. **点击顶部"新增"按钮**
   - 位置：页面顶部操作区的蓝色"新增"按钮
   - 图标：加号(+)图标
   - 用途：创建一级目录菜单

2. **表单默认设置**
   - 菜单类型：默认为"目录"
   - 组件路径：默认为"Layout"
   - 上级菜单：默认为"根目录"

##### 方式二：添加子菜单（推荐）
1. **定位父菜单**：在表格中找到要添加子菜单的父菜单
2. **点击"添加"按钮**
   - 位置：表格每行右侧操作列的绿色"添加"按钮
   - 功能：为当前菜单添加子菜单

3. **智能类型推荐**
   - 父菜单是目录 → 子菜单默认为"菜单"
   - 父菜单是菜单 → 子菜单默认为"按钮"
   - 系统会自动设置合适的菜单类型

##### 方式三：编辑现有菜单
1. **点击"编辑"按钮**
   - 位置：表格每行右侧操作列的蓝色"编辑"按钮
   - 功能：修改现有菜单配置

2. **表单预填充**：自动填充当前菜单的所有配置信息

#### 3. 表单填写详细步骤

##### 第一步：选择上级菜单
- **字段名称**：上级菜单
- **控件类型**：树形选择器
- **操作说明**：
  1. 点击下拉箭头展开菜单树
  2. 选择合适的父级菜单
  3. 根目录表示创建一级菜单
- **注意事项**：按钮类型必须选择最底层菜单作为父级

##### 第二步：设置菜单类型
- **字段名称**：菜单类型
- **控件类型**：单选按钮组
- **选项说明**：
  - **目录**：用于组织菜单结构，通常包含子菜单
  - **菜单**：指向具体页面，用户可以点击访问
  - **按钮**：权限控制，不显示在菜单中
- **选择建议**：
  - 一级菜单通常选择"目录"
  - 二级菜单通常选择"菜单"
  - 三级权限控制选择"按钮"

##### 第三步：填写基本信息
- **菜单名称**：
  - 显示在菜单中的文字
  - 支持中英文，建议简洁明了
  - 示例：`用户管理`、`系统设置`

- **路由名称**（目录和菜单类型必填）：
  - Vue Router 的路由名称，必须唯一
  - 使用PascalCase命名法
  - 示例：`SystemUser`、`BaseStudent`

- **路由地址**（目录和菜单类型必填）：
  - URL访问路径
  - 一级菜单以`/`开头，二级菜单使用相对路径
  - 示例：`/system`、`user`

##### 第四步：配置组件路径
- **目录类型**：
  - 控件：下拉选择器
  - 选项：`Layout`（标准布局）、`HeaderOnly`（简洁布局）
  - 推荐：大部分情况选择`Layout`

- **菜单类型**：
  - 控件：文本输入框
  - 格式：`pages/模块/功能/index`
  - 示例：`pages/system/user/index`
  - 提示：对应`src/pages/system/user/index.vue`文件

- **按钮类型**：
  - 控件：禁用的输入框
  - 说明：按钮类型不需要组件路径

##### 第五步：选择菜单图标
1. **点击图标输入框**：点击"菜单图标"字段的输入框
2. **打开图标选择器**：弹出图标选择对话框
3. **搜索图标**：
   - 在搜索框中输入关键词
   - 实时过滤显示匹配的图标
4. **选择图标**：
   - 点击心仪的图标
   - 图标名称自动填入表单
5. **预览效果**：输入框右侧显示选中的图标

##### 第六步：设置权限和排序
- **权限标识**：
  - 格式：`模块:功能:操作`
  - 示例：`system:user:list`、`system:user:add`
  - 用途：后端权限验证

- **排序**：
  - 数值类型，范围0-999
  - 数值越小排序越靠前
  - 建议间隔10，便于后续插入

##### 第七步：高级设置
- **是否隐藏**：
  - 开关控件，默认关闭
  - 隐藏的菜单不在导航中显示
  - 但路由仍然可以访问

- **重定向**（仅目录类型）：
  - 设置默认跳转的子路由
  - 示例：`/system/home`
  - 用户访问目录时自动跳转

- **单层菜单**（仅目录类型）：
  - 开关控件，默认关闭
  - 开启后该目录下的菜单平铺显示

#### 4. 表单字段动态变化

##### 菜单类型切换效果
- **选择"目录"**：
  - 显示：路由名称、路由地址、组件路径（下拉选择）、图标
  - 显示：重定向、单层菜单设置
  - 组件路径默认设置为`Layout`

- **选择"菜单"**：
  - 显示：路由名称、路由地址、组件路径（文本输入）、图标
  - 隐藏：重定向、单层菜单设置
  - 组件路径清空，需要手动输入

- **选择"按钮"**：
  - 隐藏：路由名称、路由地址、组件路径、图标
  - 显示：权限标识（重要）
  - 标签变为"按钮名称"

##### 上级菜单联动效果
- **选择一级目录**：
  - 路由地址自动添加父级路径前缀
  - 组件路径自动格式化

- **选择按钮父级**：
  - 限制只能选择菜单类型作为父级
  - 自动切换到按钮类型

#### 5. 操作技巧和最佳实践

##### 快速操作技巧
1. **键盘导航**：
   - Tab键在表单字段间切换
   - Enter键提交表单
   - Esc键关闭对话框

2. **批量操作**：
   - 勾选多个菜单项
   - 点击"批量删除"按钮
   - 确认删除操作

3. **表格展开/折叠**：
   - 点击菜单名称前的箭头图标
   - 展开或折叠子菜单
   - 系统会记住展开状态

##### 配置最佳实践
1. **路径命名规范**：
   ```
   一级目录：/system, /base, /training
   二级菜单：user, role, menu (相对路径)
   组件路径：pages/system/user/index
   ```

2. **权限标识规范**：
   ```
   查看权限：system:user:list
   新增权限：system:user:add
   编辑权限：system:user:edit
   删除权限：system:user:delete
   ```

3. **排序号分配**：
   ```
   一级菜单：10, 20, 30, 40...
   二级菜单：10, 20, 30, 40...
   按钮权限：1, 2, 3, 4...
   ```

4. **图标选择建议**：
   ```
   系统管理：setting, tools, gear
   用户管理：user-avatar, user-group
   数据管理：database, folder
   报表统计：chart, analytics
   ```

#### 6. 常见页面操作问题

##### 表单验证问题
**Q: 提交时提示"请输入路由名称"？**
A: 
1. 检查菜单类型是否为"目录"或"菜单"
2. 确保路由名称不为空且符合命名规范
3. 路由名称必须唯一，不能与现有菜单重复

**Q: 组件路径验证失败？**
A:
1. 目录类型：必须选择布局组件（Layout/HeaderOnly）
2. 菜单类型：确保路径格式正确（pages/模块/文件名）
3. 按钮类型：组件路径应为空

##### 菜单显示问题
**Q: 新增菜单后在导航中看不到？**
A:
1. 检查"是否隐藏"开关是否开启
2. 确认当前用户是否有相应权限
3. 尝试刷新页面或重新登录

**Q: 菜单图标不显示？**
A:
1. 确认图标名称是否正确
2. 检查是否选择了有效的图标
3. 某些图标可能需要特定的图标库支持

##### 路由访问问题
**Q: 点击菜单无法跳转？**
A:
1. 检查路由地址是否正确
2. 确认组件文件是否存在
3. 检查组件路径格式是否正确

**Q: 页面跳转到404？**
A:
1. 验证组件路径对应的文件是否存在
2. 检查文件路径是否与配置一致
3. 确认Vue组件是否正确导出

#### 7. 页面操作流程总结

```
1. 访问菜单管理页面 (/system/menu)
   ↓
2. 选择添加方式
   ├─ 新增根菜单：点击顶部"新增"按钮
   ├─ 添加子菜单：点击行操作"添加"按钮  [推荐]
   └─ 编辑菜单：点击行操作"编辑"按钮
   ↓
3. 填写表单信息
   ├─ 选择上级菜单
   ├─ 设置菜单类型 (目录/菜单/按钮)
   ├─ 填写基本信息 (名称、路由等)
   ├─ 配置组件路径
   ├─ 选择菜单图标
   ├─ 设置权限标识
   └─ 调整排序和显示设置
   ↓
4. 验证和提交
   ├─ 系统自动验证表单
   ├─ 修正验证错误
   └─ 点击"确定"保存
   ↓
5. 验证结果
   ├─ 检查菜单是否正确显示
   ├─ 测试路由跳转功能
   └─ 验证权限控制效果
```

通过以上详细的页面操作指南，开发者可以轻松掌握菜单管理页面的使用方法，高效地配置系统的路由和菜单结构。

### 路由组织原则

1. **集中管理**: 前端路由配置统一写在 `router/index.ts` 中，后端路由通过API统一管理
2. **模块化分组**: 按照业务模块进行路由分组
3. **路径一致性**: 路由路径与页面目录结构保持一致

### 路由配置示例

```typescript
// 系统管理模块路由
{
  path: '/system',
  name: 'System',
  component: Layout,
  meta: {
    title: {
      zh_CN: '系统管理',
      en_US: 'System Management',
    },
    icon: 'setting',
  },
  children: [
    {
      path: 'menu',
      name: 'SystemMenu',
      component: () => import('@/pages/system/menu/index.vue'),
      meta: {
        title: {
          zh_CN: '菜单管理',
          en_US: 'Menu Management',
        },
      },

    },
    // ... 其他子路由
  ],
}
```

### 路由命名规范

1. **路由名称**: 使用 PascalCase，格式为 `{模块名}{功能名}`
   - 例: `SystemMenu`, `BaseStudent`, `TrainingCourse`

2. **路由路径**: 使用 kebab-case，与目录结构保持一致
   - 例: `/system/menu`, `/base/student`, `/training/course`

3. **组件导入**: 使用相对路径，指向对应的页面组件
   - 例: `() => import('@/pages/system/menu/index.vue')`

## 路由参数使用规范

### 参数类型说明

#### 1. 路径参数 (params)
用于路由路径中的动态部分，作为URL的一部分。

```typescript
// 路由配置
{
  path: '/teachers/course/:courseId/homework/:homeworkId?',
  name: 'TeacherHomework',
  component: () => import('@/pages/teachers/homework/Index.vue')
}

// 访问示例
// /teachers/course/123/homework/456
// /teachers/course/123/homework (homeworkId可选)
```

#### 2. 查询参数 (query)
用于传递可选的过滤条件、状态信息等，以查询字符串形式附加在URL后。

```typescript
// 访问示例
// /teachers/course/123/homework?classId=456&type=exam&page=1
```

### 参数获取方式

#### 在组件中获取路由参数

```typescript
<script setup lang="ts">
import { computed, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';

const route = useRoute();
const router = useRouter();

// 获取路径参数
const courseId = computed(() => route.params.courseId as string);
const homeworkId = computed(() => route.params.homeworkId as string);

// 获取查询参数
const classId = computed(() => route.query.classId as string);
const examType = computed(() => route.query.type as string);
const currentPage = computed(() => Number(route.query.page) || 1);

// 处理数组类型的参数
const courseIdValue = computed(() => {
  const id = route.params.courseId;
  return Array.isArray(id) ? id[0] : id;
});

// 使用示例
console.log('课程ID:', courseId.value);
console.log('班级ID:', classId.value);
</script>
```

### 路由跳转与参数传递

#### 1. 传递路径参数

```typescript
// 基础跳转
router.push(`/teachers/course/${courseId}/homework`);

// 对象形式跳转
router.push({
  name: 'TeacherHomework',
  params: {
    courseId: '123',
    homeworkId: '456'
  }
});

// 路径模板跳转
router.push({
  path: `/teachers/course/${courseId}/homework/${homeworkId}`
});
```

#### 2. 传递查询参数

```typescript
// 仅查询参数
router.push({
  path: '/teachers/homework',
  query: {
    classId: '456',
    type: 'exam',
    page: 1
  }
});

// 路径参数 + 查询参数
router.push({
  path: `/teachers/course/${courseId}/homework`,
  query: {
    classId: selectedClassId.value,
    status: 'pending'
  }
});

// 保留现有查询参数，添加新参数
router.push({
  path: route.path,
  query: {
    ...route.query,
    page: newPage
  }
});
```

#### 3. 动态路由跳转示例

```typescript
// 课程模块导航示例
const navigateToModule = (module: string) => {
  const courseId = route.params.courseId as string;
  
  if (!courseId) {
    proxy.$baseMessage('课程ID不存在', 'error');
    return;
  }
  
  // 保留当前查询参数
  const query = route.query.classId ? { classId: route.query.classId } : {};
  
  router.push({
    path: `/teachers/course/${courseId}/${module}`,
    query
  });
};

// 学生详情页跳转
const viewStudentDetail = (studentId: string) => {
  router.push({
    path: `/teachers/student/${studentId}`,
    query: {
      courseId: route.params.courseId,
      returnPath: route.path
    }
  });
};
```

### 参数监听与响应

#### 1. 监听路径参数变化

```typescript
// 监听单个参数
watch(() => route.params.courseId, (newCourseId, oldCourseId) => {
  if (newCourseId && newCourseId !== oldCourseId) {
    console.log('课程ID变化:', newCourseId);
    // 重新加载课程数据
    fetchCourseData(newCourseId as string);
  }
}, { immediate: true });

// 监听多个参数
watch([
  () => route.params.courseId,
  () => route.params.homeworkId
], ([courseId, homeworkId]) => {
  if (courseId && homeworkId) {
    loadHomeworkDetail(courseId as string, homeworkId as string);
  }
}, { immediate: true });
```

#### 2. 监听查询参数变化

```typescript
// 监听查询参数
watch(() => route.query, (newQuery) => {
  // 处理分页变化
  if (newQuery.page) {
    currentPage.value = Number(newQuery.page);
  }
  
  // 处理筛选条件变化
  if (newQuery.classId) {
    selectedClassId.value = newQuery.classId as string;
    fetchFilteredData();
  }
}, { immediate: true, deep: true });

// 监听特定查询参数
watch(() => route.query.classId, (newClassId) => {
  if (newClassId) {
    selectedClassId.value = newClassId as string;
    refreshClassData();
  }
}, { immediate: true });
```

### 参数验证与处理

#### 1. 参数有效性验证

```typescript
// 路径参数验证
const validateParams = () => {
  const courseId = route.params.courseId as string;
  
  if (!courseId || !courseId.match(/^\d+$/)) {
    proxy.$baseMessage('无效的课程ID', 'error');
    router.push('/teachers');
    return false;
  }
  
  return true;
};

// 查询参数验证和默认值
const getValidatedQuery = () => {
  const page = Number(route.query.page) || 1;
  const pageSize = Number(route.query.pageSize) || 20;
  const classId = route.query.classId as string || 'all';
  
  return { page, pageSize, classId };
};
```

#### 2. 参数类型转换

```typescript
// 数字类型参数处理
const getNumericParam = (param: string | string[] | undefined, defaultValue = 0): number => {
  if (Array.isArray(param)) {
    return Number(param[0]) || defaultValue;
  }
  return Number(param) || defaultValue;
};

// 字符串数组参数处理
const getArrayParam = (param: string | string[] | undefined): string[] => {
  if (Array.isArray(param)) {
    return param;
  }
  return param ? [param] : [];
};

// 布尔类型参数处理
const getBooleanParam = (param: string | string[] | undefined): boolean => {
  const value = Array.isArray(param) ? param[0] : param;
  return value === 'true' || value === '1';
};

// 使用示例
const courseId = getNumericParam(route.params.courseId);
const tags = getArrayParam(route.query.tags);
const isActive = getBooleanParam(route.query.active);
```

### 最佳实践

#### 1. 参数命名规范

```typescript
// ✅ 推荐的参数命名
/users/:userId/courses/:courseId          // 使用ID后缀
?page=1&pageSize=20&sortBy=name          // 驼峰命名
?classId=123&studentId=456               // 保持一致性

// ❌ 不推荐的参数命名
/users/:user_id/courses/:course-id       // 混合命名风格
?Page=1&page_size=20&sort-by=name       // 不一致的命名
```

#### 2. 参数使用建议

```typescript
// ✅ 推荐做法
// 1. 使用computed获取参数，便于响应式更新
const courseId = computed(() => route.params.courseId as string);

// 2. 参数变化时使用watch监听
watch(() => route.params.courseId, (newId) => {
  if (newId) fetchCourseData(newId);
}, { immediate: true });

// 3. 跳转时保留必要的查询参数
const navigateWithContext = (path: string) => {
  router.push({
    path,
    query: {
      classId: route.query.classId,
      returnPath: route.path
    }
  });
};

// ❌ 不推荐做法
// 1. 直接使用route.params可能不会响应变化
const courseId = route.params.courseId;

// 2. 在setup中同步获取参数可能为空
const homeworkId = route.params.homeworkId; // 可能undefined

// 3. 跳转时丢失上下文信息
router.push('/new-page'); // 丢失了当前的查询参数
```

#### 3. 错误处理

```typescript
// 参数获取失败时的处理
const handleParamError = (paramName: string, expectedType: string) => {
  console.error(`路由参数 ${paramName} 类型错误，期望 ${expectedType}`);
  proxy.$baseMessage(`页面参数错误，请重新访问`, 'error');
  router.push('/'); // 返回首页或合适的默认页面
};

// 带错误处理的参数获取
const getSafeParam = (param: string | string[] | undefined, required = false): string | null => {
  try {
    if (Array.isArray(param)) {
      return param[0] || null;
    }
    return param || null;
  } catch (error) {
    if (required) {
      handleParamError('param', 'string');
    }
    return null;
  }
 };
 ```

## Mock数据使用规范

### Mock概述

项目使用 `vite-plugin-mock` 插件配合 `Mock.js` 库来模拟后端API接口，支持前端独立开发和测试。

### Mock配置

#### 1. Vite配置

在 `vite.config.ts` 中配置mock插件：

```typescript
import { viteMockServe } from 'vite-plugin-mock';

export default ({ mode }: ConfigEnv): UserConfig => {
  return {
    plugins: [
      // 其他插件...
      viteMockServe({
        mockPath: 'mock',           // mock文件存放目录
        enable: false,              // 是否启用mock（开发时设为true）
        logger: false,              // 是否显示请求日志
        watchFiles: false,          // 是否监听文件变化
      }),
    ],
  };
};
```

#### 2. 环境变量控制

通过环境变量控制mock的启用：

```typescript
// 根据环境自动启用mock
viteMockServe({
  mockPath: 'mock',
  enable: mode === 'development',  // 开发环境启用
  logger: true,
  watchFiles: true,
}),
```

### 文件组织规范

#### 1. 目录结构

Mock目录结构应与API目录结构保持完全一致，按照业务模块进行组织：

```
mock/
├── index.ts              # 主入口文件（通用mock接口）
├── system/               # 系统管理模块mock
│   ├── menu.ts           # 菜单mock (sys_menu)
│   ├── role.ts           # 角色mock (sys_role)
│   ├── user.ts           # 用户mock (sys_base_user)
│   ├── dict.ts           # 字典mock (sys_dict_type, sys_dict_data)
│   ├── log.ts            # 日志mock
│   ├── backup.ts         # 备份mock
│   ├── document.ts       # 文档mock
│   └── monitor.ts        # 监控mock
├── base/                 # 基础数据模块mock
│   ├── academy.ts        # 学院mock (base_academy)
│   ├── major.ts          # 专业mock (base_major)
│   ├── classes.ts        # 班级mock (base_classes)
│   ├── student.ts        # 学生mock (base_student)
│   ├── teacher.ts        # 教师mock (base_teacher)
│   └── standard.ts       # 标准mock (base_standard)
├── training/             # 培养方案模块mock
│   ├── plan.ts           # 培养计划mock (tp_plan)
│   ├── course.ts         # 课程mock (tp_course)
│   ├── goal.ts           # 培养目标mock (tp_eo)
│   ├── requirement.ts    # 毕业要求mock (tp_po)
│   ├── matrix.ts         # 支撑矩阵mock (tp_po_matrix)
│   └── route.ts          # 课程路线mock (tp_course_route)
├── task/                 # 教学任务模块mock
│   ├── worklist.ts       # 教学任务mock (task_worklist)
│   └── score.ts          # 成绩mock (task_score)
├── assessment/           # 考核评价模块mock
│   ├── exam.ts           # 考核mock (assessment)
│   ├── score.ts          # 成绩详情mock (assessment_score_detail)
│   ├── question.ts       # 题库mock (repository_question)
│   └── answer.ts         # 答案mock (repository_answer)
├── graph/                # 知识图谱模块mock
│   ├── nodes.ts          # 节点mock (graph_nodes)
│   └── links.ts          # 链接mock (graph_links)
└── etl/                  # 数据管理模块mock
    └── data.ts           # 数据推送mock (暂无对应表结构)
```

#### 2. 文件命名规范

```typescript
// ✅ 推荐的文件命名（与API目录结构保持一致）
system/menu.ts          // 系统菜单管理接口
base/student.ts         // 基础学生管理接口
training/course.ts      // 培养方案课程接口

// ❌ 不推荐的文件命名
system-menu.ts          // 不使用连字符
SystemMenu.ts           // 不使用大驼峰
system_menu.ts          // 不使用下划线
studentHome.ts          // 不按模块分类
```

### Mock文件编写规范

#### 1. 基础文件结构

```typescript
import Mock from 'mockjs';
import { MockMethod } from 'vite-plugin-mock';

export default [
  {
    url: '/base/student/grade/list',
    method: 'get',
    response: () => ({
      code: 200,
      message: 'success',
      data: {
        // mock数据
      }
    })
  }
] as MockMethod[];
```

#### 2. 接口定义规范

```typescript
// 基础GET接口
{
  url: '/base/student/grade/list',
  method: 'get',
  response: () => ({
    code: 200,
    message: 'success',
    data: mockData
  })
}

// 带参数的GET接口
{
  url: '/training/course/detail',
  method: 'get',
  response: ({ query }) => {
    const { courseId } = query;
    return {
      code: 200,
      message: 'success',
      data: getCourseById(courseId)
    };
  }
}

// POST接口
{
  url: '/task/homework/submit',
  method: 'post',
  response: ({ body }) => {
    const { homeworkId, answers } = body;
    return {
      code: 200,
      message: '提交成功',
      data: { id: homeworkId, status: 'submitted' }
    };
  }
}

// 带路径参数的接口
{
  url: '/training/course/:courseId/homework/:homeworkId',
  method: 'get',
  response: ({ params }) => {
    const { courseId, homeworkId } = params;
    return {
      code: 200,
      message: 'success',
      data: getHomeworkDetail(courseId, homeworkId)
    };
  }
}
```

#### 3. 响应格式规范

```typescript
// 统一的响应格式
interface ApiResponse<T = any> {
  code: number;           // 状态码：200成功，其他为错误
  message: string;        // 响应消息
  data: T;               // 响应数据
}

// 成功响应
{
  code: 200,
  message: 'success',
  data: {
    list: [...],
    total: 100,
    page: 1,
    pageSize: 20
  }
}

// 错误响应
{
  code: 400,
  message: '参数错误',
  data: null
}

// 分页数据响应
{
  code: 200,
  message: 'success',
  data: {
    list: [...],
    pagination: {
      current: 1,
      pageSize: 20,
      total: 100,
      totalPages: 5
    }
  }
}
```

### Mock.js 数据生成

#### 1. 基础语法

```typescript
import Mock from 'mockjs';

// 生成随机数据
const mockData = Mock.mock({
  'list|1-10': [              // 生成1-10个数组元素
    {
      'id|+1': 1,             // 自增id，从1开始
      'name': '@cname',        // 中文姓名
      'age|18-60': 1,         // 18-60之间的随机数
      'email': '@email',       // 随机邮箱
      'phone': /^1[3-9]\d{9}$/, // 手机号正则
      'status|1': [0, 1],     // 从数组中随机选择
      'createTime': '@datetime', // 随机日期时间
      'avatar': '@image("200x200")', // 随机图片
    }
  ]
});
```

#### 2. 常用Mock.js占位符

```typescript
// 文本类
'@ctitle(5, 10)'           // 中文标题，5-10个字
'@csentence(10, 20)'       // 中文句子，10-20个字
'@cname'                   // 中文姓名
'@name'                    // 英文姓名

// 数字类
'@natural(1, 100)'         // 1-100的自然数
'@integer(-100, 100)'      // -100到100的整数
'@float(1, 10, 2, 5)'      // 1-10之间，小数点后2-5位

// 日期时间类
'@date("yyyy-MM-dd")'      // 日期格式
'@time("HH:mm:ss")'        // 时间格式
'@datetime'                // 日期时间
'@now'                     // 当前时间

// 图片类
'@image("200x100")'        // 指定尺寸图片
'@dataImage("200x100")'    // base64图片

// 网络类
'@url'                     // 随机URL
'@domain'                  // 随机域名
'@email'                   // 随机邮箱
'@ip'                      // 随机IP地址

// 地址类
'@region'                  // 随机区域
'@province'                // 随机省份
'@city'                    // 随机城市
'@county'                  // 随机县
```

#### 3. 业务数据示例

```typescript
// 学生成绩数据
const gradeData = Mock.mock({
  'list|5-20': [
    {
      'id|+1': 1,
      'studentId': /^202[0-4]\d{6}$/,
      'studentName': '@cname',
      'courseName': '@ctitle(3, 8)',
      'credit|1-6': 1,
      'usualScore|60-100': 1,
      'finalScore|60-100': 1,
      'totalScore|60-100': 1,
      'semester': '@pick(["第一学期", "第二学期", "第三学期", "第四学期"])',
      'status|1': ['已完成', '进行中', '未开始']
    }
  ]
});

// 课程信息数据
const courseData = Mock.mock({
  'list|10-30': [
    {
      'id|+1': 1,
      'courseCode': /^[A-Z]{2}\d{6}$/,
      'courseName': '@ctitle(4, 12)',
      'teacher': '@cname',
      'credit|1-6': 1,
      'hours|16-64': 1,
      'semester|1-8': 1,
      'type': '@pick(["必修", "选修", "限选"])',
      'description': '@csentence(20, 50)',
      'createTime': '@datetime',
      'status|1': ['启用', '停用']
    }
  ]
});
```

### 动态Mock数据

#### 1. 参数化响应

```typescript
// 根据查询参数返回不同数据
{
  url: '/base/student/grade/list',
  method: 'get',
  response: ({ query }) => {
    const { semester, courseId, page = 1, pageSize = 20 } = query;
    
    let filteredData = gradeList;
    
    // 按学期筛选
    if (semester) {
      filteredData = filteredData.filter(item => item.semester === semester);
    }
    
    // 按课程筛选
    if (courseId) {
      filteredData = filteredData.filter(item => item.courseId === courseId);
    }
    
    // 分页处理
    const total = filteredData.length;
    const start = (page - 1) * pageSize;
    const end = start + parseInt(pageSize);
    const list = filteredData.slice(start, end);
    
    return {
      code: 200,
      message: 'success',
      data: {
        list,
        pagination: {
          current: parseInt(page),
          pageSize: parseInt(pageSize),
          total,
          totalPages: Math.ceil(total / pageSize)
        }
      }
    };
  }
}
```

#### 2. 状态管理Mock

```typescript
// 模拟数据状态变化
let studentList = [...]; // 初始数据

export default [
  // 获取学生列表
  {
    url: '/base/student/list',
    method: 'get',
    response: () => ({
      code: 200,
      message: 'success',
      data: { list: studentList }
    })
  },
  
  // 添加学生
  {
    url: '/base/student/add',
    method: 'post',
    response: ({ body }) => {
      const newStudent = {
        ...body,
        id: Date.now(),
        createTime: new Date().toISOString()
      };
      studentList.push(newStudent);
      
      return {
        code: 200,
        message: '添加成功',
        data: newStudent
      };
    }
  },
  
  // 更新学生
  {
    url: '/base/student/update/:id',
    method: 'put',
    response: ({ params, body }) => {
      const index = studentList.findIndex(s => s.id === parseInt(params.id));
      if (index > -1) {
        studentList[index] = { ...studentList[index], ...body };
        return {
          code: 200,
          message: '更新成功',
          data: studentList[index]
        };
      }
      return {
        code: 404,
        message: '学生不存在',
        data: null
      };
    }
  },
  
  // 删除学生
  {
    url: '/base/student/delete/:id',
    method: 'delete',
    response: ({ params }) => {
      const index = studentList.findIndex(s => s.id === parseInt(params.id));
      if (index > -1) {
        studentList.splice(index, 1);
        return {
          code: 200,
          message: '删除成功',
          data: null
        };
      }
      return {
        code: 404,
        message: '学生不存在',
        data: null
      };
    }
  }
];
```

### 错误场景模拟

#### 1. 模拟网络错误

```typescript
{
  url: '/base/student/upload',
  method: 'post',
  timeout: 1000,              // 延迟1秒响应
  response: ({ body }) => {
    // 模拟随机错误
    if (Math.random() < 0.3) {
      return {
        code: 500,
        message: '服务器内部错误',
        data: null
      };
    }
    
    return {
      code: 200,
      message: '上传成功',
      data: { fileId: Date.now() }
    };
  }
}
```

#### 2. 模拟权限错误

```typescript
{
  url: '/system/user/list',
  method: 'get',
  response: ({ headers }) => {
    const token = headers.authorization;
    
    // 模拟token验证
    if (!token || token !== 'Bearer admin-token') {
      return {
        code: 401,
        message: '未授权访问',
        data: null
      };
    }
    
    return {
      code: 200,
      message: 'success',
      data: { list: adminUserList }
    };
  }
}
```

#### 3. 模拟业务错误

```typescript
{
  url: '/training/course/enroll',
  method: 'post',
  response: ({ body }) => {
    const { courseId, studentId } = body;
    
    // 模拟课程已满
    if (courseId === 'COURSE_001') {
      return {
        code: 400,
        message: '课程人数已满，无法选课',
        data: null
      };
    }
    
    // 模拟时间冲突
    if (courseId === 'COURSE_002') {
      return {
        code: 400,
        message: '课程时间冲突，请检查课表',
        data: null
      };
    }
    
    return {
      code: 200,
      message: '选课成功',
      data: { enrollId: Date.now() }
    };
  }
}
```

### Mock数据维护

#### 1. 数据一致性

```typescript
// 使用共享数据源确保一致性
// mock/data/shared.ts
export const sharedStudentList = [
  { id: 1, name: '张三', classId: 1 },
  { id: 2, name: '李四', classId: 1 },
  // ...
];

export const sharedClassList = [
  { id: 1, name: '软件工程2021级1班', studentCount: 30 },
  // ...
];

// 在多个mock文件中使用
// mock/student.ts
import { sharedStudentList } from './data/shared';

export default [
  {
    url: '/base/student/list',
    method: 'get',
    response: () => ({
      code: 200,
      data: { list: sharedStudentList }
    })
  }
];
```

#### 2. 类型定义

```typescript
// 定义接口类型
interface Student {
  id: number;
  name: string;
  studentNumber: string;
  classId: number;
  major: string;
  grade: number;
  status: 'active' | 'inactive';
  createTime: string;
}

interface ApiResponse<T = any> {
  code: number;
  message: string;
  data: T;
}

// 使用类型定义
{
  url: '/base/student/detail/:id',
  method: 'get',
  response: ({ params }): ApiResponse<Student> => {
    const student = studentList.find(s => s.id === parseInt(params.id));
    if (!student) {
      return {
        code: 404,
        message: '学生不存在',
        data: null
      };
    }
    return {
      code: 200,
      message: 'success',
      data: student
    };
  }
}
```

### 调试和测试

#### 1. Mock日志

```typescript
// 开启详细日志
viteMockServe({
  mockPath: 'mock',
  enable: true,
  logger: true,           // 显示请求日志
  watchFiles: true,       // 监听文件变化
}),

// 自定义日志
{
  url: '/base/student/login',
  method: 'post',
  response: ({ body }) => {
    console.log('Mock Login Request:', body);
    
    const result = {
      code: 200,
      message: '登录成功',
      data: { token: 'mock-token-' + Date.now() }
    };
    
    console.log('Mock Login Response:', result);
    return result;
  }
}
```

#### 2. 条件Mock

```typescript
// 根据环境变量控制mock行为
const isDev = import.meta.env.MODE === 'development';
const isMockError = import.meta.env.VITE_MOCK_ERROR === 'true';

{
  url: '/base/student/list',
  method: 'get',
  response: () => {
    // 开发环境模拟错误
    if (isDev && isMockError && Math.random() < 0.2) {
      return {
        code: 500,
        message: '模拟服务器错误',
        data: null
      };
    }
    
    return {
      code: 200,
      message: 'success',
      data: { list: studentList }
    };
  }
}
```

### 最佳实践

#### 1. Mock数据规范

```typescript
// ✅ 推荐做法
// 1. 使用有意义的数据
const mockData = Mock.mock({
  'list|10': [
    {
      'id|+1': 1,
      'name': '@cname',                    // 中文姓名更符合业务场景
      'studentNumber': /^202[0-4]\d{6}$/,  // 符合学号格式的正则
      'email': '@email',
      'phone': /^1[3-9]\d{9}$/,           // 符合手机号格式
      'createTime': '@datetime("yyyy-MM-dd HH:mm:ss")'
    }
  ]
});

// 2. 保持数据结构一致
interface StudentListResponse {
  list: Student[];
  pagination: {
    current: number;
    pageSize: number;
    total: number;
  };
}

// 3. 使用枚举值
'status|1': ['active', 'inactive', 'suspended'],  // 明确的状态值

// ❌ 不推荐做法
// 1. 使用无意义的数据
'name': 'test user',              // 所有记录都是相同值
'id': 123,                        // 固定ID

// 2. 数据格式不一致
// 有时返回数组，有时返回对象

// 3. 使用模糊的值
'status|1': [0, 1, 2],           // 不明确的数字状态
```

#### 2. 性能优化

```typescript
// 大数据量处理
let cachedData = null;

{
  url: '/base/student/list',
  method: 'get',
  response: ({ query }) => {
    // 缓存大数据集
    if (!cachedData) {
      cachedData = Mock.mock({
        'list|1000': [
          {
            'id|+1': 1,
            'name': '@cname',
            // ...其他字段
          }
        ]
      }).list;
    }
    
    // 分页处理
    const { page = 1, pageSize = 20 } = query;
    const start = (page - 1) * pageSize;
    const end = start + parseInt(pageSize);
    
    return {
      code: 200,
      data: {
        list: cachedData.slice(start, end),
        pagination: {
          current: parseInt(page),
          pageSize: parseInt(pageSize),
          total: cachedData.length
        }
      }
    };
  }
}
```

#### 3. 维护建议

```typescript
// 1. 模块化组织
// mock/base/student.ts
export const studentMockList = [...];
export const studentMockMethods = [...];

// mock/training/course.ts  
export const courseMockList = [...];
export const courseMockMethods = [...];

// mock/index.ts
import { studentMockMethods } from './base/student';
import { courseMockMethods } from './training/course';

export default [
  ...studentMockMethods,
  ...courseMockMethods,
];

// 2. 版本控制
// 在mock文件中添加版本注释
/**
 * 学生管理Mock接口
 * @version 1.2.0
 * <AUTHOR>
 * @date 2024-01-01
 * @description 包含学生的增删改查接口
 */

// 3. 文档说明
// 为复杂的mock逻辑添加注释
{
  url: '/base/student/grade/statistics',
  method: 'get',
  response: ({ query }) => {
    // 根据学期和课程计算成绩统计
    // 支持参数：semester, courseId, classId
    // 返回：平均分、及格率、优秀率等统计数据
    
    const { semester, courseId } = query;
    // ... 具体实现
  }
}
```

### 注意事项

1. **数据真实性**: Mock数据应该尽可能接近真实业务数据的格式和内容
2. **性能考虑**: 大数据量时注意内存使用，适当使用缓存
3. **版本管理**: Mock接口变更时要及时更新，保持与后端接口同步
4. **错误模拟**: 适当模拟各种错误场景，提高前端容错性
5. **团队协作**: Mock数据结构变更要及时通知团队成员
6. **生产环境**: 确保生产环境不会启用Mock功能

## 错误处理规范

<<<<<<< HEAD
1. API 请求错误统一在 `request.ts` 中处理，组件中无需重复处理
2. 组件中只处理业务逻辑相关的错误提示
3. 使用全局消息提示方法：
   ```typescript
   import { getCurrentInstance } from 'vue';
   const { proxy } = getCurrentInstance();
   // 成功提示
   proxy.$baseMessage('操作成功', 'success');
   
   // 警告提示
   proxy.$baseMessage('请注意', 'warning');
   
   // 错误提示
   proxy.$baseMessage('操作失败', 'error');
   ```
=======
### 基本原则

1. **全局错误处理**: API 请求错误统一在 `request.ts` 中处理，组件中无需重复处理
2. **业务逻辑错误**: 组件中只处理业务逻辑相关的错误提示
3. **错误信息输出**: 所有错误信息都应该在 catch 块中输出到控制台
4. **用户友好提示**: 使用全局消息提示方法向用户显示操作结果

### Proxy 创建代码

在 Vue 组件的 `setup()` 函数中创建 proxy：

```typescript
<script setup lang="ts">
import { getCurrentInstance } from 'vue';

// 获取当前组件实例的 proxy
const { proxy } = getCurrentInstance();

// 现在可以使用 proxy.$baseMessage 等全局方法
</script>
```

### 全局消息提示方法

```typescript
// 成功提示
proxy.$baseMessage('操作成功', 'success');

// 警告提示
proxy.$baseMessage('请注意', 'warning');

// 错误提示
proxy.$baseMessage('操作失败', 'error');

// 信息提示
proxy.$baseMessage('提示信息', 'info');
```

### 错误处理最佳实践

#### 1. API 请求错误处理

```typescript
// 正确的错误处理方式
const fetchData = async () => {
  try {
    loading.value = true;
    const { data } = await getUserList(params);
    // 处理成功的数据
    userList.value = data;
    // 成功的提示就是具体情况具体分析，不是所有的都需要成功的提示信息
    proxy.$baseMessage('数据加载成功', 'success');
  } catch (error) {
    // 错误信息输出到控制台
    console.error('获取用户列表失败:', error);
  } finally {
    loading.value = false;
  }
};
```

#### 2. 业务操作错误处理

```typescript
// 删除操作示例
const handleDelete = async (id: number) => {
  try {
    await deleteUser(id);
    proxy.$baseMessage('删除成功', 'success');
    // 刷新列表
    await fetchData();
  } catch (error) {
    console.error('删除用户失败:', error);
  }
};

// 表单提交示例
const handleSubmit = async () => {
  try {
    // 表单验证
    const valid = await formRef.value?.validate();
    if (!valid) {
      proxy.$baseMessage('请检查表单输入', 'warning');
      return;
    }
    
    // 提交数据
    if (isEdit.value) {
      await updateUser(formData);
      proxy.$baseMessage('更新成功', 'success');
    } else {
      await createUser(formData);
      proxy.$baseMessage('创建成功', 'success');
    }
    
    // 关闭对话框并刷新列表
    dialogVisible.value = false;
    await fetchData();
  } catch (error) {
    console.error('保存用户失败:', error);
  }
};
```

#### 3. 文件上传错误处理

```typescript
const handleFileUpload = async (file: File) => {
  try {
    // 文件类型验证
    if (!file.name.endsWith('.xlsx') && !file.name.endsWith('.xls')) {
      proxy.$baseMessage('只能上传Excel文件', 'error');
      return;
    }
    
    // 文件大小验证
    if (file.size > 10 * 1024 * 1024) {
      proxy.$baseMessage('文件大小不能超过10MB', 'error');
      return;
    }
    
    // 上传文件
    const response = await uploadFile(file);
    proxy.$baseMessage('文件上传成功', 'success');
    
    // 处理上传结果
    handleUploadResult(response);
  } catch (error) {
    console.error('文件上传失败:', error);
  }
};
```

#### 4. 批量操作错误处理

```typescript
const handleBatchDelete = async () => {
  try {
    if (!selectedIds.value.length) {
      proxy.$baseMessage('请选择要删除的项目', 'warning');
      return;
    }
    
    await batchDeleteUsers(selectedIds.value);
    proxy.$baseMessage('批量删除成功', 'success');
    
    // 清空选择并刷新列表
    selectedIds.value = [];
    await fetchData();
  } catch (error) {
    console.error('批量删除失败:', error);
  }
};
```

### 错误处理规范要求

1. **必须使用 try-catch**: 所有异步操作都必须包装在 try-catch 块中
2. **控制台输出**: 每个 catch 块都必须使用 `console.error()` 输出错误信息
3. **禁止在 catch 中显示用户提示**: catch 块中不得调用 `proxy.$baseMessage()` 等用户提示方法
4. **用户提示仅在成功时显示**: 只在操作成功时使用 `proxy.$baseMessage()` 向用户显示提示
5. **资源清理**: 在 finally 块中进行资源清理（如关闭 loading 状态），确保无论成功失败都能正确清理

### 禁止的错误处理方式

```typescript
// ❌ 错误：不处理异常
const badExample1 = async () => {
  const data = await fetchData(); // 可能抛出异常但未处理
};

// ❌ 错误：空的 catch 块
const badExample2 = async () => {
  try {
    await fetchData();
  } catch (error) {
    // 空的 catch 块，没有任何处理
  }
};

// ❌ 错误：在 catch 中显示用户提示
const badExample3 = async () => {
  try {
    await fetchData();
  } catch (error) {
    console.error('操作失败:', error);
    proxy.$baseMessage('操作失败', 'error'); // ❌ 不应该在 catch 中显示用户提示
  }
};

// ❌ 错误：loading 在 catch 中关闭而不是 finally 中
const badExample4 = async () => {
  const loading = proxy.$baseLoading('加载中...');
  try {
    await fetchData();
    loading.close(); // ❌ 应该在 finally 中关闭
  } catch (error) {
    loading.close(); // ❌ 应该在 finally 中关闭
    console.error('操作失败:', error);
  }
};
```

## 消息提示方法大全

### 基础消息提示 ($baseMessage)

用于显示简短的操作反馈信息，会自动消失（默认3秒）。

```typescript
// 成功提示
proxy.$baseMessage('操作成功', 'success');

// 错误提示
proxy.$baseMessage('操作失败', 'error');

// 警告提示
proxy.$baseMessage('请注意检查输入', 'warning');

// 信息提示（默认类型）
proxy.$baseMessage('数据已更新', 'info');

// 问题提示
proxy.$baseMessage('确认要继续吗？', 'question');

// 加载提示
proxy.$baseMessage('正在处理...', 'loading');

// 仅传递消息内容（默认为info类型）
proxy.$baseMessage('这是一条默认消息');
```

**参数说明**:
- `message` (必填): 消息文字内容
- `type` (可选): 消息类型，可选值：`'info'` | `'success'` | `'warning'` | `'error'` | `'question'` | `'loading'`，默认为 `'info'`
- 显示时长：固定3秒自动消失

### 右侧通知提示 ($baseNotify)

用于显示重要的系统通知，支持自定义位置和持续时间。

```typescript
// 仅消息内容（默认success类型，右上角，3秒消失）
proxy.$baseNotify('操作完成');

// 消息内容 + 标题
proxy.$baseNotify('数据同步完成', '操作结果');

// 消息内容 + 标题 + 类型
proxy.$baseNotify('网络连接失败', '错误提示', 'error');

// 消息内容 + 标题 + 类型 + 位置
proxy.$baseNotify('磁盘空间不足', '系统警告', 'warning', 'top-left');

// 完整参数：消息内容 + 标题 + 类型 + 位置 + 持续时间
proxy.$baseNotify('重要数据备份完成', '备份通知', 'success', 'top-right', 10000);

// 信息通知（左下角）
proxy.$baseNotify('系统将在5分钟后维护', '维护通知', 'info', 'bottom-left');
```

**参数说明**:
- `message` (必填): 说明文字内容
- `title` (可选): 通知标题
- `type` (可选): 主题样式，可选值：`'success'` | `'warning'` | `'info'` | `'error'`，默认为 `'success'`
- `position` (可选): 弹出位置，可选值：`'top-right'` | `'top-left'` | `'bottom-right'` | `'bottom-left'`，默认为 `'top-right'`
- `duration` (可选): 显示时间（毫秒），默认为3000ms（3秒）

### 对话框提示

#### 警告对话框 ($baseAlert)

用于显示重要信息，需要用户确认。

```typescript
// 仅消息内容（默认标题"温馨提示"）
proxy.$baseAlert('数据已保存成功！');

// 消息内容 + 自定义标题
proxy.$baseAlert('请检查网络连接后重试', '网络错误');

// 消息内容 + 回调函数（使用默认标题）
proxy.$baseAlert('操作完成', () => {
  console.log('用户点击了确定');
  // 执行后续操作
});

// 消息内容 + 标题 + 回调函数
proxy.$baseAlert('数据导入完成，共导入100条记录', '导入结果', () => {
  // 刷新页面数据
  fetchDataList();
});
```

**参数说明**:
- `content` (必填): 消息正文内容
- `title` (可选): 对话框标题，默认为 `'温馨提示'`
- `callback` (可选): 点击确定按钮后的回调函数

**注意**: 如果第二个参数是函数，则会被当作回调函数处理，标题使用默认值。

#### 确认对话框 ($baseConfirm)

用于需要用户确认的操作，返回Promise<boolean>。

```typescript
// 仅消息内容（默认标题"温馨提示"，按钮文本"确定"/"取消"）
const handleDelete = async () => {
  try {
    const confirmed = await proxy.$baseConfirm('确认删除该记录？');
    if (confirmed) {
      await deleteRecord();
      proxy.$baseMessage('删除成功', 'success');
    }
  } catch (error) {
    console.error('删除操作失败:', error);
  }
};

// 消息内容 + 自定义标题
const handleSubmit = async () => {
  const confirmed = await proxy.$baseConfirm(
    '提交后将无法修改，确认提交吗？',
    '提交确认'
  );
  
  if (confirmed) {
    await submitForm();
    proxy.$baseMessage('提交成功', 'success');
  }
};

// 完整参数：消息内容 + 标题 + 确认按钮文本 + 取消按钮文本
const handleReset = async () => {
  const confirmed = await proxy.$baseConfirm(
    '重置后所有配置将恢复默认，是否继续？',
    '重置确认',
    '确认重置',
    '取消'
  );
  
  if (confirmed) {
    resetConfiguration();
    proxy.$baseMessage('重置成功', 'success');
  }
};

// 危险操作确认
const handleClearData = async () => {
  const confirmed = await proxy.$baseConfirm(
    '此操作将清空所有数据且无法恢复，请谨慎操作！',
    '危险操作警告',
    '确认清空',
    '取消操作'
  );
  
  if (confirmed) {
    await clearAllData();
    proxy.$baseNotify('数据清空完成', '操作结果', 'warning');
  }
};
```

**参数说明**:
- `content` (必填): 消息正文内容
- `title` (可选): 对话框标题，默认为 `'温馨提示'`
- `confirmButtonText` (可选): 确认按钮文本，默认为 `'确定'`
- `cancelButtonText` (可选): 取消按钮文本，默认为 `'取消'`
- **返回值**: Promise<boolean>，true表示用户点击确认，false表示用户点击取消或关闭对话框

### 加载提示 ($baseLoading)

用于显示全屏加载状态，返回包含close方法的对象。

```typescript
// 默认加载提示（显示"加载中..."）
const loading = proxy.$baseLoading();

// 自定义加载文本
const customLoading = proxy.$baseLoading('正在上传文件...');

// 使用示例
const uploadFile = async () => {
  const loading = proxy.$baseLoading('正在上传文件，请稍候...');
  
  try {
    await uploadFileAPI();
    proxy.$baseMessage('文件上传成功', 'success');
  } catch (error) {
    console.error('文件上传失败:', error);
  } finally {
    loading.close(); // 关闭加载提示
  }
};

// 多阶段操作加载示例
const processData = async () => {
  let loading = proxy.$baseLoading('正在处理数据...');
  let newLoading = null;
  
  try {
    // 第一阶段：数据处理
    await processDataStep1();
    
    // 更新加载提示
    loading.close();
    newLoading = proxy.$baseLoading('正在保存结果...');
    
    // 第二阶段：保存结果
    await saveResults();
    
    proxy.$baseNotify('数据处理完成', '处理结果', 'success');
  } catch (error) {
    console.error('数据处理失败:', error);
  } finally {
    // 确保所有loading都被正确关闭
    if (loading) loading.close();
    if (newLoading) newLoading.close();
  }
};
```

**参数说明**:
- `text` (可选): 显示在加载图标下方的加载文案，默认为 `'加载中...'`
- **返回值**: 包含 `close()` 方法的对象，用于关闭加载提示

**特性**:
- 全屏显示，覆盖整个页面
- 自动添加到body元素
- 不阻止页面滚动
- 必须手动调用 `close()` 方法关闭

### 表格高度计算 ($baseTableHeight)

用于计算表格的合适高度，确保表格在不同屏幕尺寸下都能良好显示。

```typescript
// 基础表格高度（无表单）
const tableHeight = proxy.$baseTableHeight();

// 带表单的表格高度（表单行数为1）
const tableHeightWithForm = proxy.$baseTableHeight(1);

// 带多行表单的表格高度（表单行数为2）
const tableHeightWithMultiForm = proxy.$baseTableHeight(2);

// 使用示例
const tableConfig = {
  height: proxy.$baseTableHeight(1), // 考虑一行表单的高度
  // 其他表格配置...
};
```

**参数说明**:
- `formType` (可选): 表单行数，用于计算需要减去的表单高度
- 返回值: 计算后的表格高度（像素值）

**计算规则**:
- 基础高度 = 窗口高度 - 291px（页面边距等固定高度）
- 如果有表单，每行表单减去60px高度

### 事件总线 (跨组件通信)

用于组件间的消息传递和通信，基于mitt库实现。

```typescript
// 发布事件 ($pub)
const publishEvent = () => {
  // 发布简单事件
  proxy.$pub('refreshData');
  
  // 发布带数据的事件
  proxy.$pub('dataUpdated', { id: 123, name: '新数据' });
  
  // 发布复杂数据事件
  proxy.$pub('systemNotification', {
    type: 'success',
    message: '数据同步完成',
    timestamp: Date.now()
  });
};

// 订阅事件 ($sub)
const subscribeEvents = () => {
  // 订阅简单事件
  proxy.$sub('refreshData', () => {
    console.log('收到刷新数据事件');
    fetchDataList();
  });
  
  // 订阅带数据的事件
  proxy.$sub('dataUpdated', (data) => {
    console.log('收到数据更新:', data);
    proxy.$baseMessage(`数据 ${data.name} 已更新`, 'info');
  });
  
  // 订阅系统通知事件
  proxy.$sub('systemNotification', (notification) => {
    proxy.$baseNotify(
      notification.message,
      '系统通知',
      notification.type
    );
  });
};

// 取消订阅事件 ($unsub)
const unsubscribeEvents = () => {
  // 取消特定事件的特定处理器
  proxy.$unsub('dataUpdated', dataUpdateHandler);
  
  // 取消特定事件的所有处理器
  proxy.$unsub('systemNotification');
};

// 在组件中的完整使用示例
<script setup lang="ts">
import { onMounted, onUnmounted } from 'vue';
import { getCurrentInstance } from 'vue';

const { proxy } = getCurrentInstance();

// 事件处理器
const dataUpdateHandler = (data) => {
  console.log('数据更新:', data);
};

const systemNotifyHandler = (notification) => {
  proxy.$baseNotify(notification.message, '系统通知', notification.type);
};

// 组件挂载时订阅事件
onMounted(() => {
  proxy.$sub('dataUpdated', dataUpdateHandler);
  proxy.$sub('systemNotification', systemNotifyHandler);
});

// 组件卸载时取消订阅
onUnmounted(() => {
  proxy.$unsub('dataUpdated', dataUpdateHandler);
  proxy.$unsub('systemNotification', systemNotifyHandler);
});

// 发布事件的方法
const handleDataChange = () => {
  proxy.$pub('dataUpdated', { id: 1, name: '测试数据' });
};
</script>
```

**方法说明**:

#### $pub (发布事件)
- `eventName` (必填): 事件名称
- `data` (可选): 要传递的数据

#### $sub (订阅事件)  
- `eventName` (必填): 要订阅的事件名称
- `handler` (必填): 事件处理函数

#### $unsub (取消订阅)
- `eventName` (必填): 要取消订阅的事件名称
- `handler` (可选): 要取消的特定处理函数，如果不提供则取消该事件的所有处理器

**使用建议**:
- 在组件的 `onMounted` 中订阅事件
- 在组件的 `onUnmounted` 中取消订阅，避免内存泄漏
- 事件名称建议使用驼峰命名法
- 复杂数据建议使用对象格式传递

### 组合使用示例

复杂操作中组合使用多种提示方法：

```typescript
const complexOperation = async () => {
  // 1. 确认操作
  const confirmed = await proxy.$baseConfirm(
    '此操作将同步所有数据，可能需要几分钟时间，是否继续？',
    '数据同步确认'
  );
  
  if (!confirmed) return;
  
  // 2. 显示加载状态
  const loading = proxy.$baseLoading('正在同步数据，请稍候...');
  
  try {
    // 3. 执行同步操作
    await syncAllData();
    
    // 4. 显示成功通知
    proxy.$baseNotify('数据同步完成', '同步结果', 'success');
    
    // 5. 发布事件通知其他组件
    proxy.$pub('dataSynced', { timestamp: Date.now() });
    
    // 6. 显示详细结果
    proxy.$baseAlert('所有数据已成功同步到服务器', '同步完成');
    
  } catch (error) {
    // 7. 错误处理 - 只输出到控制台
    console.error('数据同步失败:', error);
  } finally {
    // 8. 确保资源清理
    loading.close();
  }
};
```

### 使用场景建议

| 全局方法 | 使用场景 | 持续时间/特点 |
|---------|----------|----------|
| `$baseMessage` | 简单操作反馈、表单验证提示 | 3秒自动消失 |
| `$baseNotify` | 重要系统通知、后台任务完成 | 默认3秒，可自定义 |
| `$baseAlert` | 重要信息告知、操作结果确认 | 用户手动关闭 |
| `$baseConfirm` | 删除确认、危险操作确认 | 用户选择后关闭，返回boolean |
| `$baseLoading` | 数据加载、文件上传、长时间操作 | 手动调用close()关闭 |
| `$baseTableHeight` | 计算表格高度、响应式布局 | 返回计算后的高度值 |
| `$pub/$sub/$unsub` | 跨组件通信、状态同步 | 即时传递，需手动管理订阅 |

### 全局方法总览

所有全局方法都通过 `proxy` 对象调用，来源于 `src/utils/gp.ts` 文件：

```typescript
// 在组件中获取proxy
import { getCurrentInstance } from 'vue';
const { proxy } = getCurrentInstance();

// 可用的全局方法
proxy.$baseMessage()     // 消息提示
proxy.$baseNotify()      // 通知提示  
proxy.$baseAlert()       // 警告对话框
proxy.$baseConfirm()     // 确认对话框
proxy.$baseLoading()     // 加载提示
proxy.$baseTableHeight() // 表格高度计算
proxy.$pub()             // 发布事件
proxy.$sub()             // 订阅事件
proxy.$unsub()           // 取消订阅
```
>>>>>>> 89c2314e41f7df87e382928e1825d0295209abf8

## 代码组织规范

1. API 文件组织
   - 按模块分类存放
   - 相关 API 方法放在同一个文件中
   - 文件命名使用小写，如 `menu.ts`, `role.ts`

2. 组件文件组织
   - 按功能模块分类存放
   - 组件文件使用 PascalCase 命名，如 `Menu.vue`, `Role.vue`
   - 组件内部方法按功能分组

3. 工具类文件组织
   - 通用工具方法放在 `utils` 目录
   - 按功能分类存放，如 `request.ts`, `auth.ts`

## 注释规范

1. API 方法注释
   ```typescript
   /**
    * 获取菜单树
    * @returns {Promise} 返回菜单树数据
    */
   export function getMenuTree() { ... }
   ```

2. 组件方法注释
   ```typescript
   /**
    * 获取菜单列表
    * 从服务器获取菜单数据并更新到本地状态
    */
   const fetchMenuList = async () => { ... }
   ```

## 类型定义规范

1. 接口定义使用 `interface`
   ```typescript
   interface Menu {
     id: number;
     title: string;
     path: string;
     // ...
   }
   ```

2. 类型定义使用 `type`
   ```typescript
   type MenuType = 'menu' | 'button';
   ```

## 布局使用规范

### 布局组件类型

系统提供三种主要布局组件，通过后端路由配置 `component` 字段指定：

#### 1. Layout - 标准布局
- **使用场景**: 系统主要功能页面，需要完整的侧边栏导航
- **组件名**: `Layout`
- **特点**: 包含顶部导航、左侧菜单、内容区域
- **后端配置示例**:
  ```json
  {
    "path": "/system",
    "component": "Layout",
    "name": "System",
    "meta": {
      "title": {"zh_CN": "系统管理", "en_US": "System"},
      "icon": "setting"
    },
    "children": [...]
  }
  ```

#### 2. HeaderOnly - 仅头部布局
- **使用场景**: 独立页面，如登录页、工作台首页等
- **组件名**: `HeaderOnly`
- **特点**: 仅包含顶部导航，内容区域全屏显示
- **后端配置示例**:
  ```json
  {
    "path": "/dashboard",
    "component": "HeaderOnly",
    "name": "Dashboard",
    "meta": {
      "title": {"zh_CN": "工作台", "en_US": "Dashboard"},
      "icon": "dashboard"
    },
    "children": [...]
  }
  ```

#### 3. DynamicSidebar - 动态侧边栏布局
- **使用场景**: 需要根据父级路由动态生成侧边栏菜单的页面
- **组件名**: `DynamicSidebar`
- **特点**: 包含顶部导航、动态左侧菜单、底部Footer、内容区域
- **后端配置示例**:
  ```json
  {
    "path": "/module-management",
    "component": "DynamicSidebar",
    "name": "ModuleManagement",
    "meta": {
      "title": {"zh_CN": "模块管理", "en_US": "Module Management"},
      "icon": "view-module",
      "parentRoute": "/module-management"
    },
    "children": [
      {
        "path": "overview",
        "component": "pages/module/overview",
        "name": "ModuleOverview",
        "meta": {
          "title": {"zh_CN": "总览", "en_US": "Overview"},
          "icon": "dashboard",
          "description": "模块总览页面"
        }
      },
      {
        "path": "settings",
        "component": "pages/module/settings",
        "name": "ModuleSettings",
        "meta": {
          "title": {"zh_CN": "设置", "en_US": "Settings"},
          "icon": "setting",
          "description": "模块设置页面"
        }
      }
    ]
  }
  ```

### DynamicSidebar 特殊配置

#### parentRoute 配置
DynamicSidebar 布局需要指定 `parentRoute` 来确定动态菜单的数据源，有三种配置方式：

1. **在路由 meta 中配置（推荐）**:
   ```json
   {
     "meta": {
       "parentRoute": "/module-management"
     }
   }
   ```

2. **通过页面跳转传递**:
   后端可以在重定向或页面跳转时通过 query 参数传递：
   ```
   /target-page?parentRoute=/module-management
   ```

3. **自动推断**:
   如果未指定 parentRoute，系统会根据当前路由路径自动推断父级路由

#### 菜单项配置要求
- 子路由必须配置 `title` 和 `icon`
- 隐藏的菜单项设置 `"hidden": true`
- 支持多层级嵌套（二级、三级路由）
- `description` 字段用于显示页面描述

### 布局选择指南

| 场景 | 推荐布局 | 原因 |
|------|----------|------|
| 系统主要功能模块 | Layout | 需要完整的导航体系 |
| 独立工作台页面 | HeaderOnly | 需要全屏展示内容 |
| 模块内部管理页面 | DynamicSidebar | 需要动态的子功能导航 |

### 注意事项

1. **布局一致性**: 同一功能模块下的页面应使用相同的布局类型
2. **路由层级**: DynamicSidebar 适用于需要展示子功能菜单的场景
3. **权限控制**: 所有布局都支持基于角色的权限控制
4. **响应式**: 所有布局都支持移动端适配
5. **主题适配**: 支持明暗主题切换

## 其他规范

1. 使用 TypeScript 进行开发
2. 使用 ESLint 进行代码检查
3. 使用 Prettier 进行代码格式化
4. 遵循 Vue3 组合式 API 的最佳实践
5. 使用 TDesign 组件库进行开发
6. 使用 Less 进行样式开发

## 提交规范

1. 提交信息格式
   ```
   feat: 添加新功能
   fix: 修复问题
   docs: 修改文档
   style: 修改代码格式
   refactor: 代码重构
   test: 添加测试
   chore: 修改构建过程或辅助工具
   ```

2. 分支命名规范
   - 主分支：`main`
   - 开发分支：`dev`
   - 功能分支：`feature/功能名称`
   - 修复分支：`hotfix/问题描述` 
   - 修复分支：`hotfix/问题描述` 
# OBE系统前端路由设计方案

## 路由设计原则

### 1. 模块化组织
按照业务模块进行路由分组：
- **system**：系统管理模块
- **base**：基础数据模块  
- **training**：培养方案模块
- **assessment**：考核评价模块
- **task**：教学任务模块
- **graph**：知识图谱模块
- **dashboard**：工作台模块

### 2. 布局策略
- **Layout**：标准布局，包含顶部导航和侧边栏，适用于大部分管理页面
- **HeaderOnly**：简洁布局，仅包含顶部导航，适用于工作台首页

### 3. 权限控制
- 使用层级权限标识：`模块:功能:操作`
- 支持多角色权限：管理员、院系领导、专业负责人、教师、学生
- 菜单类型：0-目录，1-菜单，2-按钮

## 完整路由结构

### 一级路由（主模块）

```
/dashboard          # 工作台首页 (HeaderOnly布局)
/system             # 系统管理 (Layout布局)
/base               # 基础数据管理 (Layout布局)  
/training           # 培养方案管理 (Layout布局)
/assessment         # 考核评价管理 (Layout布局)
/task               # 教学任务管理 (Layout布局)
/graph              # 知识图谱管理 (Layout布局)
```

### 详细路由配置

#### 1. 工作台模块 (/dashboard)
```
/dashboard          # HeaderOnly布局，工作台首页
```

#### 2. 系统管理模块 (/system)
```
/system             # Layout布局，重定向到 /system/user
├── user            # 用户管理
├── role            # 角色管理  
├── menu            # 菜单管理
├── dict            # 字典管理
├── log             # 日志管理
├── backup          # 数据备份
├── monitor         # 系统监控
├── document        # 文档管理
└── notice          # 系统通知
```

#### 3. 基础数据模块 (/base)
```
/base               # Layout布局，重定向到 /base/academy
├── academy         # 学院管理
├── major           # 专业管理
├── classes         # 班级管理
├── student         # 学生管理
├── teacher         # 教师管理
└── standard        # 标准管理
```

#### 4. 培养方案模块 (/training)
```
/training           # Layout布局，重定向到 /training/plan
├── plan            # 培养计划
├── course          # 课程体系
├── goal            # 培养目标
├── requirement     # 毕业要求
├── matrix          # 支撑矩阵
└── route           # 课程路线
```

#### 5. 考核评价模块 (/assessment)
```
/assessment         # Layout布局，重定向到 /assessment/management
├── management      # 考核管理
├── exam            # 考试管理
├── score           # 成绩管理
├── question        # 题库管理
├── answer          # 答案管理
└── questionnaire   # 问卷管理
```

#### 6. 教学任务模块 (/task)
```
/task               # Layout布局，重定向到 /task/worklist
├── worklist        # 教学任务
└── score           # 成绩管理
```

#### 7. 知识图谱模块 (/graph)
```
/graph              # Layout布局，重定向到 /graph/nodes
├── nodes           # 节点管理
└── links           # 关系管理
```

## 权限标识设计

### 模块级权限
- `dashboard:view` - 工作台访问权限
- `system:manage` - 系统管理权限
- `base:manage` - 基础数据管理权限
- `training:manage` - 培养方案管理权限
- `assessment:manage` - 考核评价管理权限
- `task:manage` - 教学任务管理权限
- `graph:manage` - 知识图谱管理权限

### 功能级权限
以用户管理为例：
- `system:user:list` - 查看用户列表
- `system:user:add` - 新增用户
- `system:user:edit` - 编辑用户
- `system:user:delete` - 删除用户
- `system:user:export` - 导出用户
- `system:user:import` - 导入用户

## 菜单排序规则

### 一级菜单排序
1. dashboard (10) - 工作台
2. base (20) - 基础数据
3. training (30) - 培养方案
4. assessment (40) - 考核评价
5. task (50) - 教学任务
6. graph (60) - 知识图谱
7. system (70) - 系统管理

### 二级菜单排序
以10为间隔递增：10, 20, 30, 40...

### 按钮权限排序
以1为间隔递增：1, 2, 3, 4...

## 路由命名规范

### 路由名称 (name)
使用PascalCase命名：
- `Dashboard` - 工作台
- `SystemUser` - 系统用户管理
- `BaseAcademy` - 基础学院管理
- `TrainingPlan` - 培养计划管理

### 路径 (path)
使用kebab-case命名：
- `/dashboard` - 工作台
- `/system/user` - 系统用户管理  
- `/base/academy` - 基础学院管理
- `/training/plan` - 培养计划管理

### 组件路径 (component)
- 目录类型：`Layout`、`HeaderOnly`
- 菜单类型：`pages/模块/功能`

## 页面组件映射

| 路由路径 | 组件路径 | 页面文件 |
|---------|----------|----------|
| `/dashboard` | `pages/dashboard/index` | `dashboard/index.vue` |
| `/system/user` | `pages/system/UserManagement` | `system/UserManagement.vue` |
| `/base/academy` | `pages/base/AcademyManagement` | `base/AcademyManagement.vue` |
| `/training/plan` | `pages/training/plan/index` | `training/plan/index.vue` |

此设计方案确保了路由结构的清晰性、可维护性和扩展性，同时支持灵活的权限控制和多角色访问。 
# Training模块 Vue页面说明

## 全局目录结构

```
/training/
├── course/                    # 课程管理
│   ├── CourseConfig.vue       # 课程配置
│   ├── CourseObjective.vue    # 课程目标
│   ├── CourseDegree.vue       # 课程学位
│   ├── CourseDetail.vue       # 课程详情
│   ├── CourseHome.vue         # 课程首页
│   ├── CourseManagement.vue   # 课程管理
│   └── CourseList.vue         # 课程列表
├── goal/                      # 培养目标
│   └── index.vue              # 培养目标管理
├── matrix/                    # 支撑矩阵
│   └── index.vue              # 支撑矩阵管理
└── requirement/               # 毕业要求
    └── index.vue              # 毕业要求管理
```

## 模块概述

Training模块（培养方案模块）负责管理专业培养方案的各个组成部分，包括课程体系、培养目标、毕业要求和支撑矩阵等核心功能。

### 数据库表对应关系

- `tp_course` - 课程体系表
- `tp_eo` - 培养目标表  
- `tp_po` - 毕业要求表
- `tp_po_matrix` - 支撑矩阵表
- `tp_plan` - 培养计划表
- `tp_course_route` - 课程路线表

## 功能分类

### 1. 课程管理 (course/)
负责培养方案中的课程体系管理，包括课程信息、目标、配置等。

### 2. 培养目标管理 (goal/)
管理专业的培养目标设置和编辑。

### 3. 毕业要求管理 (requirement/)
管理专业毕业要求的设置和指标点分解。

### 4. 支撑矩阵管理 (matrix/)
管理课程体系对毕业要求的支撑关系矩阵。

## 详细页面说明

### Course目录 - 课程管理

#### /training/course/CourseConfig.vue
- **功能**: 课程配置管理
- **描述**: 管理课程的基本信息配置，包括课程代码、名称、学分、学时等基础信息设置
- **主要功能**: 
  - 课程基本信息编辑
  - 课程目标设置
  - 考核方式配置
  - 教学大纲上传

#### /training/course/CourseObjective.vue
- **功能**: 课程目标管理
- **描述**: 设置和管理课程的具体教学目标，支持目标的增删改查操作
- **主要功能**:
  - 课程目标列表展示
  - 目标内容编辑
  - 目标与毕业要求的映射关系

#### /training/course/CourseDegree.vue
- **功能**: 课程学位管理
- **描述**: 管理课程的学位层次和要求设置
- **主要功能**:
  - 学位层次设置
  - 学位要求配置
  - 课程学位关联

#### /training/course/CourseDetail.vue
- **功能**: 课程详情展示
- **描述**: 展示课程的详细信息，包括基本信息、目标、考核方式等综合信息
- **主要功能**:
  - 课程信息综合展示
  - 课程目标详情
  - 考核方式说明
  - 教学安排展示

#### /training/course/CourseHome.vue
- **功能**: 课程首页
- **描述**: 课程模块的入口页面，提供课程管理的概览和快速导航
- **主要功能**:
  - 课程统计信息
  - 快速操作入口
  - 最新动态展示

#### /training/course/CourseManagement.vue
- **功能**: 课程管理主页
- **描述**: 课程管理的主要页面，提供课程的增删改查等核心管理功能
- **主要功能**:
  - 课程列表管理
  - 课程信息维护
  - 批量操作
  - 导入导出功能

#### /training/course/CourseList.vue
- **功能**: 课程列表展示
- **描述**: 以列表形式展示所有课程信息，支持筛选、排序和搜索
- **主要功能**:
  - 课程列表展示
  - 多条件筛选
  - 排序和搜索
  - 分页显示

### Goal目录 - 培养目标

#### /training/goal/index.vue
- **功能**: 培养目标管理
- **描述**: 管理专业的培养目标，支持目标的制定、修改和维护
- **主要功能**:
  - 培养目标列表
  - 目标内容编辑
  - 目标版本管理
  - 目标审核流程

### Matrix目录 - 支撑矩阵

#### /training/matrix/index.vue
- **功能**: 支撑矩阵管理
- **描述**: 管理课程体系对毕业要求的支撑关系矩阵，直观展示支撑关系
- **主要功能**:
  - 支撑矩阵展示
  - 支撑关系编辑
  - 支撑强度设置
  - 矩阵导出功能

### Requirement目录 - 毕业要求

#### /training/requirement/index.vue
- **功能**: 毕业要求管理
- **描述**: 管理专业的毕业要求，包括要求内容和指标点分解
- **主要功能**:
  - 毕业要求列表
  - 要求内容编辑
  - 指标点管理
  - 要求层次结构

## 目录结构规范

### 文件命名规范
- 所有Vue文件使用PascalCase命名法
- 文件名应体现页面功能，避免使用index.vue等通用名称
- 组件名称应与文件名保持一致

### 目录组织规范
- 按功能模块进行目录划分
- 每个子目录最多保持一层深度
- 相关功能的页面放在同一目录下

### 功能分类体系
1. **课程管理**: 核心的课程体系管理功能
2. **目标管理**: 培养目标的设置和维护
3. **要求管理**: 毕业要求的管理和指标点分解
4. **矩阵管理**: 支撑关系的可视化管理

## 与其他模块的关系

### 与Assessment模块的关系
- Training模块负责课程体系和培养目标设置
- Assessment模块基于Training模块的课程目标进行考核设计
- 考核结果用于验证Training模块设定的培养目标达成情况

### 与Task模块的关系
- Training模块提供课程体系基础
- Task模块基于Training模块的课程创建具体的教学任务
- 教学任务的执行验证培养方案的可行性

### 与Base模块的关系
- 使用Base模块的专业、学院等基础数据
- 培养方案按Base模块的组织结构进行管理

## 注意事项

1. 本模块与base模块的区别：training模块专注于培养方案的业务逻辑，base模块专注于基础数据管理
2. 课程管理功能与assessment模块的题库功能已分离，避免功能重叠
3. 所有页面都应遵循统一的设计规范和交互模式
4. 注意与后端API接口的对应关系，确保数据操作的一致性
5. 课程体系的变更需要考虑对其他模块的影响 
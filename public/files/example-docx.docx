数据结构教学计划
==============

课程信息
-------
课程名称：数据结构
课程代码：CS2001
学    分：4学分
总 学 时：48学时（理论32学时，实践16学时）
开课学期：第三学期
先修课程：C语言程序设计、离散数学

课程目标
-------
1. 掌握数据结构的基本概念和理论
2. 了解数据结构在实际应用中的重要性
3. 培养学生的实践能力和创新思维

教学内容
-------
第1周：绪论、算法分析
第2周：线性表（顺序表）
第3周：线性表（链表）
第4周：栈
第5周：队列
第6周：串
第7周：数组与广义表
第8周：树与二叉树（基本概念）
第9周：树与二叉树（遍历算法）
第10周：树与二叉树（应用）
第11周：图（基本概念）
第12周：图（遍历算法）
第13周：图（应用）
第14周：查找（基本查找算法）
第15周：查找（哈希表）
第16周：排序

考核方式
-------
- 平时成绩（30%）：出勤、课堂表现、作业
- 实验成绩（30%）：实验报告、项目完成情况
- 期末考试（40%）：闭卷笔试

教材及参考资料
-----------
1. 《数据结构》(C语言版)，严蔚敏，清华大学出版社
2. 《数据结构与算法分析》，Mark Allen Weiss，机械工业出版社
3. 《算法导论》，Thomas H.Cormen等，机械工业出版社 
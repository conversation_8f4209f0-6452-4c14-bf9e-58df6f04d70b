{"title": "数据结构课程大纲", "author": "张教授", "department": "计算机科学与技术学院", "created": "2023-09-01", "content": [{"type": "heading1", "text": "课程目标"}, {"type": "paragraph", "text": "本课程旨在使学生掌握数据结构的基本概念、基本原理和基本方法，培养学生的抽象思维能力、逻辑推理能力和解决实际问题的能力。"}, {"type": "heading1", "text": "教学内容"}, {"type": "paragraph", "text": "本课程分为理论教学和实践教学两部分，具体内容如下："}, {"type": "paragraph", "text": "理论部分：基础知识、线性表、栈与队列、树与二叉树、图、查找、排序"}, {"type": "paragraph", "text": "实践部分：编程实现各类数据结构及其操作、算法分析与优化"}, {"type": "heading1", "text": "教学安排"}, {"type": "paragraph", "text": "总学时：48学时（理论32学时，实践16学时）"}, {"type": "paragraph", "text": "教学进度：每周2学时，共24周"}]}
// 通用声明

// Vue
declare module '*.vue' {
  import { DefineComponent } from 'vue';

  const component: DefineComponent<{}, {}, any>;
  export default component;
}

declare type ClassName = { [className: string]: any } | ClassName[] | string;

declare module '*.svg' {
  const CONTENT: string;
  export default CONTENT;
}

declare type Recordable<T = any> = Record<string, T>;

export interface globalPropertiesType {
  $baseLoading: (text?: string) => any
  $baseMessage: (
    message: string,
    type?: 'info' | 'success' | 'warning' | 'error' | 'question' | 'loading'
  ) => void
  $baseAlert: (
    content: string,
    title?: string,
    callback?: () => void
  ) => void
  $baseConfirm: (
    content: string,
    title?: string,
    callback1?: () => void,
    callback2?: () => void,
    confirmButtonText?: string,
    cancelButtonText?: string
  ) => void
  $baseNotify: (
    message: string,
    title?: string,
    type?: 'success' | 'warning' | 'info' | 'error',
    position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left',
    duration?: number
  ) => void
  $baseTableHeight: (formType?: number) => number
  $pub: (...args: any[]) => void
  $sub: () => void
  $unsub: () => void
}

import { App } from 'vue'

declare module '@vue/runtime-core' {
  interface ComponentCustomProperties {
    $baseMessage: (
      message: string,
      type?: 'info' | 'success' | 'warning' | 'error' | 'question' | 'loading'
    ) => void
    $baseLoading: (text?: string) => { close: () => void }
    $baseAlert: (content: string, title?: string, callback?: () => void) => void
    $baseConfirm: (
      content: string,
      title?: string,
      confirmButtonText?: string,
      cancelButtonText?: string,
      callback1?: () => void,
      callback2?: () => void,
    ) => void
    $baseNotify: (
      message: string,
      title?: string,
      type?: 'success' | 'warning' | 'info' | 'error',
      position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left',
      duration?: number
    ) => void
    $baseTableHeight: (formType?: number) => number
    $pub: (event: string, data?: any) => void
    $sub: (event: string, callback: (...args: any[]) => void) => void
    $unsub: (event: string, callback: (...args: any[]) => void) => void
  }
}

import { defineStore } from "pinia";

import {useSettingStore, useTabsRouterStore} from "@/store";
import {getToken, removeToken, setToken} from "@/utils/token";
import { gp } from "@/utils/gp";
import {getUserInfo, login, logout} from "@/api/system/auth";
import {tokenName} from "@/config";
import {isArray, isString} from "@/utils/validate";
import {useAclStore} from "@/store/modules/acl";
import {useRoutesStore} from "@/store/modules/routes";
import {resetRouter} from "@/router";
import { initializeGlobalDict, resetGlobalDictState } from "@/utils/dictUtil";

// 定义API响应类型
interface ApiResponse<T = any> {
  code: number;
  message: string;
  data: T;
}

interface LoginResponse {
  token: string;
}


// 用户信息接口
export interface UserInfo {
  id: number;
  type: number;
  username: string;
  avatar: string;
  roles: string[];
  permissions: string[];
}

// 根据角色代码获取角色名数组
function getRolesByCode(roleCode: number): string[] {
  const roleMap: Record<number, string[]> = {
    0: ["admin"], // 管理员
    1: ["director"], // 专业负责人
    2: ["subject"], // 课题负责人
    3: ["teacher"], // 教师
    4: ["student"], // 学生
    5: ["college_admin"], // 学院管理员
  };

  return roleMap[roleCode] || ["student"]; // 默认返回学生角色
}

const InitUserInfo: UserInfo = {
  id: 0,
  type: -1,
  username: "",
  avatar: "",
  roles: [],
  permissions: [],
};

export const useUserStore = defineStore("user", {
  state: () => ({
    token: getToken() as string,
    userInfo: { ...InitUserInfo },
    isResetting: false, // 防止重复重置的标志
  }),
  getters: {
    getToken: (state) => state.token,
    getUsername: (state) => state.userInfo?.username,
    getAvatar: (state) => state.userInfo?.avatar,
    roles: (state) => {
      return state.userInfo?.roles;
    },
  },
  actions: {
    /**
     * @description 设置token
     * @param {*} token
     */
    setToken(token: string) {
      this.token = token
      setToken(token)
    },
    /**
     * @description 设置用户数据
     * @param {*} username
     */
    setUsername(username: string) {
      this.userInfo.username = username
    },
    /**
     * @description 设置头像
     * @param {*} avatar
     */
    setAvatar(avatar: string) {
      this.userInfo.avatar = avatar
    },
    /**
     * @description 设置token并发送提醒
     * @param {string} token 更新令牌
     * @param {string} tokenName 令牌名称
     */
    afterLogin(token: string, tokenName: string) {
      const settingsStore = useSettingStore()
      if (token) {
        this.setToken(token)
        const hour = new Date().getHours()
        const thisTime = hour < 8 ? '早上好' : hour <= 11 ? '上午好' : hour <= 13 ? '中午好' : hour < 18 ? '下午好' : '晚上好'
        gp.$baseNotify(`欢迎登录${settingsStore.title}`, `${thisTime}！`)
      } else {
        const err = `登录接口异常，未正确返回${tokenName}...`
        gp.$baseMessage(err, 'error')
        throw err
      }
    },
    /**
     * @description 登录
     * @param {*} userInfo
     */
    async login(userInfo: any) {
      const { data } = await login(userInfo)
      const token = data.token
      this.afterLogin(token, tokenName)
    },
    /**
     * @description 获取用户信息接口 这个接口非常非常重要，如果没有明确底层前逻辑禁止修改此方法，错误的修改可能造成整个框架无法正常使用
     * @returns
     */
    async getUserInfo() {
      const {
        data: { username, avatar, roles, permissions },
      } = await getUserInfo()
      /**
       * 检验返回数据是否正常，无对应参数，将使用默认用户名,头像,Roles和Permissions
       * username {String}
       * avatar {String}
       * roles {List}
       * ability {List}
       */
      if (
        (username && !isString(username)) ||
        (avatar && !isString(avatar)) ||
        (roles && !isArray(roles)) ||
        (permissions && !isArray(permissions))
      ) {
        const err = 'getUserInfo核心接口异常，请检查返回JSON格式是否正确'
        gp.$baseMessage(err, 'error')
        throw err
      } else {
        const aclStore = useAclStore()
        // 如不使用username用户名,可删除以下代码
        if (username) this.setUsername(username)
        // 如不使用avatar头像,可删除以下代码
        if (avatar) this.setAvatar(avatar)
        // 如不使用roles权限控制,可删除以下代码
        if (roles) aclStore.setRole(roles)
        // 如不使用permissions权限控制,可删除以下代码
        if (permissions) aclStore.setPermission(permissions)

        // 初始化全局字典数据
        try {
          await initializeGlobalDict()
        } catch (error) {
          console.error('❌ 字典数据初始化失败:', error)
          // 字典初始化失败不影响用户登录流程
        }
      }
    },
    /**
     * @description 退出登录
     */
    async logout() {
      try {
        await logout()
      } catch (error) {
        console.error('Logout API call failed:', error)
      } finally {
        await this.resetAll()
        // 解决横向布局退出登录显示不全的bug
        location.reload()
      }
    },
    /**
     * @description 重置token、roles、permission、router、tabsBar等
     */
    async resetAll() {
      // 防止重复执行
      if (this.isResetting) {
        console.log('resetAll already in progress, skipping...');
        return;
      }

      this.isResetting = true;
      try {
        this.setToken('')
        this.setUsername('')

        const aclStore = useAclStore()
        const routesStore = useRoutesStore()
        const tabsStore = useTabsRouterStore()

        // 清除权限相关状态
        aclStore.setPermission([])
        aclStore.setFull(false)
        aclStore.setRole([])

        // 清除路由相关状态
        tabsStore.removeTabRouterList()
        routesStore.clearRoutes()
        await resetRouter()

        // 重置字典工具全局状态
        resetGlobalDictState()

        // 最后清除token
        removeToken()
      } catch (error) {
        console.error('Reset all failed:', error)
        // 确保token被清除
        removeToken()
      } finally {
        this.isResetting = false;
      }
    },
  },
});

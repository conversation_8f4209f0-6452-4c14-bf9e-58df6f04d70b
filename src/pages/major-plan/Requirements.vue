<template>
  <div class="min-h-screen bg-gray-50">
    <!-- 骨架屏 -->
    <div v-if="loading" class="min-h-screen bg-gray-50">
      <!-- 顶部骨架屏 -->
      <div class="bg-white shadow-sm border-b">
        <div class="px-6 py-4">
          <div class="h-8 bg-gray-200 rounded animate-pulse w-64"></div>
        </div>
      </div>

      <!-- 主内容区域骨架屏 -->
      <div class="px-6 py-6">
        <!-- 欢迎信息骨架屏 -->
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
          <div class="h-5 bg-blue-200 rounded animate-pulse"></div>
        </div>

        <!-- 统计卡片骨架屏 -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
          <div v-for="i in 3" :key="i" class="bg-white rounded-lg shadow-sm p-6">
            <div class="h-4 bg-gray-200 rounded animate-pulse mb-2"></div>
            <div class="h-8 bg-gray-200 rounded animate-pulse w-20"></div>
          </div>
        </div>

        <!-- 培养计划与标准信息骨架屏 -->
        <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
          <div class="h-6 bg-gray-200 rounded animate-pulse mb-4 w-48"></div>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div v-for="i in 2" :key="i" class="space-y-2">
              <div class="h-4 bg-gray-200 rounded animate-pulse w-24"></div>
              <div class="h-4 bg-gray-200 rounded animate-pulse"></div>
              <div class="h-4 bg-gray-200 rounded animate-pulse w-3/4"></div>
            </div>
          </div>
        </div>

        <!-- 毕业要求骨架屏 -->
        <div class="bg-white rounded-lg shadow-sm p-6">
          <div class="h-6 bg-gray-200 rounded animate-pulse mb-4 w-32"></div>
          <div class="space-y-4">
            <div v-for="i in 3" :key="i" class="border border-gray-200 rounded-lg p-4">
              <div class="h-4 bg-gray-200 rounded animate-pulse mb-2"></div>
              <div class="h-20 bg-gray-200 rounded animate-pulse mb-2"></div>
              <div class="h-4 bg-gray-200 rounded animate-pulse w-2/3"></div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 实际内容 -->
    <div v-else>
      <!-- 顶部信息 -->
      <div class="bg-white shadow-sm border-b">
        <div class="px-6 py-4">
          <h1 class="text-2xl font-bold text-gray-900">毕业要求</h1>
        </div>
      </div>

      <!-- 主内容区域 -->
      <div class="px-6 py-6">
        <!-- 培养方案欢迎信息 -->
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
          <p class="text-blue-700">
            欢迎来到培养方案
            <strong>{{ planData.name }}</strong>
            的毕业要求管理页面，您可以在这里管理毕业要求和指标点。
          </p>
        </div>

        <!-- 统计信息卡片 -->
        <RequirementStats :total-requirements="poTree.length" :total-indicators="totalIndicators"
          :last-updated="lastUpdated" />

        <!-- 培养计划与标准信息 -->
        <PlanInfoCard :plan-data="planData" :standard-data="standardData" :enum-data="enumData" />

        <!-- 毕业要求列表 -->
        <RequirementsList :po-tree="poTree" :search-text="searchText" @update:search-text="searchText = $event"
          @add-requirement="handleAddRequirement" @add-indicator="handleAdd"
          @edit-indicator="(parent, item) => handleEdit(item, parent)"
          @delete-indicator="(parent, item) => handleDelete(item)" />
      </div>
    </div>

    <!-- 指标点编辑弹窗 -->
    <IndicatorFormDialog v-model:visible="poDialogVisible" :form-data="poForm" :is-edit="isEdit"
      :current-requirement="currentEditingRequirement" :on-regenerate-number="handleRegenerateNumber"
      @update:form-data="poForm = $event" @submit="handlePoFormSubmit" />

    <!-- 删除确认弹窗 -->
    <t-dialog v-model:visible="deleteDialogVisible" header="确认删除" :on-confirm="confirmDelete"
      :on-cancel="() => (deleteDialogVisible = false)" :confirm-btn="{ content: '确认删除', theme: 'danger' }"
      :cancel-btn="{ content: '取消', variant: 'outline' }">
      <p v-if="deleteTarget">
        确定要删除"{{ deleteTarget.poTitle }}"吗？此操作不可撤销！
      </p>
    </t-dialog>
  </div>
</template>

<script setup lang="ts">
import { getGraduationStandardDetail } from "@/api/base/standard";
import { getPlanDetail } from "@/api/major/plan";
import { getEnum } from "@/api/system/enum";
import { addPo, deletePo, getPoTree, updatePo } from "@/api/training/po";
import { MessagePlugin } from "tdesign-vue-next";
import { computed, onMounted, ref } from "vue";
import { useRoute } from "vue-router";

// 引入子组件
import IndicatorFormDialog from "./components/IndicatorFormDialog.vue";
import PlanInfoCard from "./components/PlanInfoCard.vue";
import RequirementsList from "./components/RequirementsList.vue";
import RequirementStats from "./components/RequirementStats.vue";

const route = useRoute();
const planId = ref(route.params.planId as string);

// 状态管理
const loading = ref(false);
const searchText = ref("");
const poDialogVisible = ref(false);
const deleteDialogVisible = ref(false);
const currentEditingRequirement = ref<any>(null);
const isEdit = ref(false);
const deleteTarget = ref<any>(null);

// 数据管理
const planData = ref<any>({});
const standardData = ref<any>({});
const enumData = ref<any>(null);

const poTree = ref<any[]>([]);

// 表单数据
const emptyPoForm = {
  planId: Number(planId.value),
  poNumber: "",
  poTitle: "",
  poDescription: "",
  isRequirement: false,
  parentId: null as string | null,
  id: null as number | null,
};

const poForm = ref<any>({ ...emptyPoForm });

// 计算属性
const totalIndicators = computed(() => {
  return poTree.value.reduce((total, po) => {
    return total + po.children.length;
  }, 0);
});

const lastUpdated = computed(() => {
  return poTree.value.reduce((last, po) => {
    // 先收集全部的时间
    const times = [];
    po.children.forEach((child: any) => {
      times.push(new Date(child.modifyTime));
    });
    times.push(new Date(po.modifyTime));

    // 不要排序 直接遍历取最后一个
    for (let i = 0; i < times.length; i++) {
      if (times[i] > last) {
        last = times[i];
      }
    }
    return last;
  }, new Date("1970-01-01T00:00:00Z"));
});

// 获取培养计划数据
const getPlanData = async () => {
  const res = await getPlanDetail(Number(planId.value));
  planData.value = res.data;

  // 获取关联的标准信息
  if (planData.value.standardId) {
    const standardRes = await getGraduationStandardDetail(
      planData.value.standardId,
    );
    standardData.value = standardRes.data;
  }
};

// 获取枚举数据
const getEnumData = async () => {
  const res = await getEnum();
  enumData.value = res.data;
};

// 加载所有指标点
const loadAllPo = async () => {
  if (!planData.value?.id) return;
  const res = await getPoTree(planData.value.id);
  poTree.value = res.data;
};

// 统一编辑处理
const handleEdit = (item: any, parent: any = null) => {
  isEdit.value = true;
  poForm.value = { ...item };
  console.log(poForm);
  currentEditingRequirement.value = parent;
  poDialogVisible.value = true;
};

// 生成下一个编号
const generateNextNumber = (parent: any) => {
  if (!parent) {
    // 一级指标编号：找到最大的一级编号 + 1
    const maxNumber = poTree.value.reduce((max, po) => {
      const num = parseInt(po.poNumber) || 0;
      return Math.max(max, num);
    }, 0);
    return (maxNumber + 1).toString();
  } else {
    // 二级指标编号：找到同一父级下最大的二级编号 + 1
    if (!parent.children || parent.children.length === 0) {
      return `1`;
    }
    const maxSubNumber = parent.children.reduce((max: number, child: any) => {
      const num = parseInt(child.poNumber) || 0;
      return Math.max(max, num);
    }, 0);
    return `${maxSubNumber + 1}`;
  }
};

// 统一添加处理
const handleAdd = (parent: any) => {
  isEdit.value = false;
  const nextNumber = generateNextNumber(parent);
  poForm.value = {
    ...emptyPoForm,
    parentId: parent ? parent.id : null,
    poNumber: nextNumber,
  };
  currentEditingRequirement.value = parent;
  poDialogVisible.value = true;
};

// 添加一级指标（毕业要求）
const handleAddRequirement = () => {
  handleAdd(null);
};

// 重新生成编号
const handleRegenerateNumber = () => {
  const nextNumber = generateNextNumber(currentEditingRequirement.value);
  poForm.value = { ...poForm.value, poNumber: nextNumber };
};

// 统一删除处理
const handleDelete = (item: any) => {
  deleteTarget.value = item;
  deleteDialogVisible.value = true;
};

// 确认删除
const confirmDelete = async () => {
  if (deleteTarget.value) {
    await deletePo(deleteTarget.value.id);
    MessagePlugin.success("删除成功");
    await loadAllPo();
    deleteDialogVisible.value = false;
    deleteTarget.value = null;
  }
};

// 提交指标点表单
const handlePoFormSubmit = async (context: any) => {
  if (context.validateResult === true) {
    if (poForm.value.id) {
      await updatePo(poForm.value);
      MessagePlugin.success("编辑成功");
    } else {
      await addPo(poForm.value);
      MessagePlugin.success("添加成功");
    }
    poDialogVisible.value = false;
    await loadAllPo();
  }
};

// 页面初始化
onMounted(async () => {
  try {
    loading.value = true;
    await Promise.all([getPlanData(), getEnumData()]);
    await loadAllPo();
  } finally {
    loading.value = false;
  }
});
</script>

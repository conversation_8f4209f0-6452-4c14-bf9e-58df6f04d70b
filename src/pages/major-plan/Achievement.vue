<template>
  <div class="min-h-screen bg-gray-50">
    <!-- Skeleton Loading State -->
    <div v-if="loading" class="p-6">
      <!-- Header Skeleton -->
      <div class="h-10 bg-gray-200 rounded animate-pulse w-1/3 mb-6"></div>
      <!-- Filter Skeleton -->
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <div v-for="i in 3" :key="i" class="h-10 bg-gray-200 rounded animate-pulse"></div>
      </div>
      <!-- Stats Skeleton -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div v-for="i in 4" :key="i" class="bg-white rounded-lg shadow-sm p-6 h-28 animate-pulse"></div>
      </div>
      <!-- Table Skeleton -->
      <div class="bg-white rounded-lg shadow-sm p-6">
        <div class="h-8 bg-gray-200 rounded animate-pulse w-1/4 mb-4"></div>
        <div class="h-40 bg-gray-200 rounded animate-pulse"></div>
      </div>
    </div>

    <!-- Actual Content -->
    <div v-else>
      <!-- Header -->
      <div class="bg-white shadow-sm border-b">
        <div class="px-6 py-4">
          <h1 class="text-2xl font-bold text-gray-900">专业达成度管理</h1>
        </div>
      </div>

      <!-- Main Content -->
      <div class="p-6">
        <!-- Filters -->
        <t-card :bordered="false" class="mb-6">
          <div class="filter-section">
            <t-row :gutter="16">
              <t-col :span="4">
                <t-select v-model="filters.evaluationYear" placeholder="评价年份" :options="yearOptions" clearable
                  @change="handleFilterChange" />
              </t-col>
              <t-col :span="4">
                <t-select v-model="filters.semester" placeholder="学期" :options="semesterOptions" clearable
                  @change="handleFilterChange" />
              </t-col>
              <t-col :span="4">
                <t-select v-model="filters.graduationYear" placeholder="毕业年份" :options="graduationYearOptions" clearable
                  @change="handleFilterChange" />
              </t-col>
            </t-row>
          </div>
        </t-card>

        <!-- Overview Stats -->
        <PlanStats :stats="achievementStats" />

        <!-- Requirements Table -->
        <t-card :bordered="false" class="mb-6 shadow-sm">
          <template #header>
            <h3 class="text-lg font-semibold text-gray-900">毕业要求达成度</h3>
          </template>
          <t-table :data="achievementList" :columns="achievementColumns" :loading="tableLoading" row-key="id">
            <template #order="{ rowIndex }">
              {{ rowIndex + 1 }}
            </template>
            <template #achievement="{ row }">
              <div class="flex items-center gap-2">
                <t-progress :percentage="row.achievement" :color="getAchievementColor(row.achievement)"
                  :show-info="false" size="small" class="flex-grow" />
                <span class="font-medium min-w-[40px]">{{ row.achievement }}%</span>
              </div>
            </template>
            <template #status="{ row }">
              <t-tag :variant="row.achievement >= 60 ? 'outline' : 'light-outline'"
                :theme="row.achievement >= 60 ? 'success' : 'danger'">
                {{ row.achievement >= 60 ? '达标' : '未达标' }}
              </t-tag>
            </template>
            <template #actions="{ row }">
              <t-space>
                <t-button theme="primary" variant="text" @click="handleViewDetail(row)">
                  详情
                </t-button>
                <t-button theme="default" variant="text" @click="handleViewTrend(row)">
                  趋势
                </t-button>
              </t-space>
            </template>
          </t-table>
        </t-card>

        <!-- Trend Chart -->
        <t-card :bordered="false" class="shadow-sm">
          <template #header>
            <h3 class="text-lg font-semibold text-gray-900">达成度趋势</h3>
          </template>
          <div class="w-full h-96 flex items-center justify-center border border-gray-200 rounded-md bg-gray-50">
            <canvas ref="chartCanvas" class="w-full h-full"></canvas>
          </div>
        </t-card>
      </div>
    </div>

    <!-- Dialog remains the same -->
    <t-dialog v-model:visible="detailVisible" header="达成度详情" :width="1000" :footer="false" destroy-on-close>
      <div v-if="currentRequirement" class="detail-content">
        <t-descriptions :column="2" :bordered="true">
          <t-descriptions-item label="毕业要求">
            {{ currentRequirement.code }} - {{ currentRequirement.name }}
          </t-descriptions-item>
          <t-descriptions-item label="达成度">
            {{ currentRequirement.achievement }}%
          </t-descriptions-item>
          <t-descriptions-item label="目标值">
            {{ currentRequirement.target }}%
          </t-descriptions-item>
          <t-descriptions-item label="状态">
            <t-tag :variant="currentRequirement.achievement >= currentRequirement.target ? 'outline' : 'light-outline'"
              :theme="currentRequirement.achievement >= currentRequirement.target ? 'success' : 'danger'">
              {{ currentRequirement.achievement >= currentRequirement.target ? '达标' : '未达标' }}
            </t-tag>
          </t-descriptions-item>
          <t-descriptions-item label="评价学生数" :span="2">
            {{ currentRequirement.studentCount }}人
          </t-descriptions-item>
        </t-descriptions>

        <div class="detail-table">
          <h5>指标点达成度</h5>
          <t-table :data="currentRequirement.indicators" :columns="indicatorColumns" :loading="false" row-key="id">
            <template #order="{ rowIndex }">
              {{ rowIndex + 1 }}
            </template>
            <template #achievement="{ row }">
              <div class="achievement-cell">
                <t-progress :percentage="row.achievement" :color="getAchievementColor(row.achievement)"
                  :show-info="false" size="small" />
                <span class="achievement-value">{{ row.achievement }}%</span>
              </div>
            </template>
          </t-table>
        </div>
      </div>
    </t-dialog>
  </div>
</template>

<script setup lang="ts">
import { MessagePlugin } from 'tdesign-vue-next';
import { computed, onMounted, reactive, ref } from 'vue';
import { useRoute } from 'vue-router';
import PlanStats from './components/PlanStats.vue';

const route = useRoute();
const majorId = ref(route.params.majorId as string);
const planId = ref(route.params.planId as string);

const loading = ref(false);
const tableLoading = ref(false);
const detailVisible = ref(false);
const chartCanvas = ref();

const achievementList = ref([]);
const currentRequirement = ref();
const filters = reactive({
  evaluationYear: '',
  semester: '',
  graduationYear: ''
});

const yearOptions = computed(() => {
  const currentYear = new Date().getFullYear();
  return Array.from({ length: 5 }, (_, i) => ({
    label: `${currentYear - i}年`,
    value: currentYear - i
  }));
});

const semesterOptions = [
  { label: '第一学期', value: '1' },
  { label: '第二学期', value: '2' }
];

const graduationYearOptions = computed(() => {
  const currentYear = new Date().getFullYear();
  return Array.from({ length: 5 }, (_, i) => ({
    label: `${currentYear - i}届`,
    value: currentYear - i
  }));
});

const achievementColumns = [
  { colKey: 'order', title: '序号', width: 80 },
  { colKey: 'code', title: '毕业要求编号', width: 120 },
  { colKey: 'name', title: '毕业要求名称', width: 200 },
  { colKey: 'target', title: '目标值(%)', width: 100 },
  { colKey: 'achievement', title: '达成度(%)', width: 180 },
  { colKey: 'status', title: '状态', width: 100 },
  { colKey: 'studentCount', title: '学生数', width: 100 },
  { colKey: 'actions', title: '操作', width: 150, fixed: 'right' as const }
];

const indicatorColumns = [
  { colKey: 'order', title: '序号', width: 80 },
  { colKey: 'code', title: '指标点编号', width: 120 },
  { colKey: 'name', title: '指标点名称', width: 200 },
  { colKey: 'target', title: '目标值(%)', width: 100 },
  { colKey: 'achievement', title: '达成度(%)', width: 180 },
  { colKey: 'weight', title: '权重', width: 100 }
];

const overallAchievement = computed(() => {
  if (achievementList.value.length === 0) return 0;
  const total = achievementList.value.reduce((sum: number, item: any) => sum + item.achievement, 0);
  return Math.round(total / achievementList.value.length);
});

const qualifiedRequirements = computed(() => {
  return achievementList.value.filter((item: any) => item.achievement >= 60).length;
});

const totalRequirements = computed(() => {
  return achievementList.value.length;
});

const evaluatedStudents = computed(() => {
  // 假设所有要求的学生数相同，取第一个
  return achievementList.value.length > 0 ? achievementList.value[0].studentCount : 0;
});

const getAchievementColor = (achievement: number) => {
  if (achievement >= 80) return '#52c41a';
  if (achievement >= 60) return '#faad14';
  return '#f5222d';
};

const getAchievementColorName = (achievement: number) => {
  if (achievement >= 80) return 'green';
  if (achievement >= 60) return 'orange';
  return 'red';
};

const achievementStats = computed(() => [
  {
    title: '总体达成度',
    value: overallAchievement.value,
    unit: '%',
    icon: 'chart-pie',
    color: getAchievementColorName(overallAchievement.value),
  },
  {
    title: '达标毕业要求',
    value: qualifiedRequirements.value,
    unit: '项',
    icon: 'check-circle',
    color: 'green',
  },
  {
    title: '总毕业要求',
    value: totalRequirements.value,
    unit: '项',
    icon: 'target',
    color: 'blue',
  },
  {
    title: '评价学生数',
    value: evaluatedStudents.value,
    unit: '人',
    icon: 'usergroup',
    color: 'purple',
  },
]);

const fetchAchievementData = async () => {
  try {
    tableLoading.value = true;
    // TODO: 调用API获取达成度数据
    // const response = await getAchievementData(majorId.value, planId.value, filters);
    // achievementList.value = response.data;
  } catch (error) {
    console.error('获取达成度数据失败:', error);
    MessagePlugin.error('获取达成度数据失败');
  } finally {
    tableLoading.value = false;
  }
};

const handleFilterChange = () => {
  fetchAchievementData();
};

const handleCalculate = async () => {
  try {
    loading.value = true;
    // TODO: 调用API计算达成度
    // await calculateAchievement(majorId.value, planId.value, filters);
    MessagePlugin.success('计算完成');
    fetchAchievementData();
  } catch (error) {
    console.error('计算达成度失败:', error);
    MessagePlugin.error('计算达成度失败');
  } finally {
    loading.value = false;
  }
};

const handleExport = async () => {
  try {
    // TODO: 调用API导出报告
    // await exportAchievementReport(majorId.value, planId.value, filters);
    MessagePlugin.success('导出成功');
  } catch (error) {
    console.error('导出失败:', error);
    MessagePlugin.error('导出失败');
  }
};

const handleViewDetail = (row: any) => {
  currentRequirement.value = row;
  detailVisible.value = true;
};

const handleViewTrend = (row: any) => {
  // TODO: 实现趋势查看功能
  MessagePlugin.info('趋势查看功能待实现');
};

const initChart = () => {
  // TODO: 初始化图表
  // 使用Chart.js或其他图表库绘制达成度趋势图
  const canvas = chartCanvas.value;
  if (canvas) {
    const ctx = canvas.getContext('2d');
    // 绘制图表逻辑
  }
};

onMounted(() => {
  fetchAchievementData();
  initChart();
});
</script>

<style scoped>
/* Scoped styles can be removed or adjusted if Tailwind covers all needs */
.achievement-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.achievement-value {
  font-weight: 500;
  min-width: 40px;
}

.detail-content {
  padding: 20px;
}

.detail-table {
  margin-top: 20px;
}

.detail-table h5 {
  margin-bottom: 12px;
  color: #333;
}
</style>

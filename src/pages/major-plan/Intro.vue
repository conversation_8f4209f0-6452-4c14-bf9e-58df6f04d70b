<template>
  <div class="min-h-screen bg-gray-50">
    <!-- 骨架屏 -->
    <div v-if="loading" class="min-h-screen bg-gray-50">
      <!-- 顶部骨架屏 -->
      <div class="bg-white shadow-sm border-b">
        <div class="px-6 py-4">
          <div class="flex items-center justify-between">
            <div>
              <div class="h-8 bg-gray-200 rounded animate-pulse w-64"></div>
            </div>
            <div>
              <div class="h-8 bg-gray-200 rounded animate-pulse w-64"></div>
            </div>
          </div>
        </div>
      </div>

      <!-- 主内容区域骨架屏 -->
      <div class="px-6 py-6">
        <!-- 欢迎信息骨架屏 -->
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
          <div class="h-5 bg-blue-200 rounded animate-pulse"></div>
        </div>

        <!-- 内容骨架屏 -->
        <div class="bg-white rounded-lg shadow-sm p-6">
          <div class="h-6 bg-gray-200 rounded animate-pulse mb-4 w-48"></div>
          <div class="h-4 bg-gray-200 rounded animate-pulse mb-6 w-32"></div>
          <div class="h-4 bg-gray-200 rounded animate-pulse mb-4"></div>
          <div class="h-4 bg-gray-200 rounded animate-pulse mb-6 w-3/4"></div>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div v-for="i in 6" :key="i" class="bg-gray-50 rounded-lg p-3">
              <div class="h-4 bg-gray-200 rounded animate-pulse mb-2"></div>
              <div class="h-3 bg-gray-200 rounded animate-pulse w-2/3"></div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 实际内容 -->
    <div v-else>
      <!-- 顶部培养方案信息 -->
      <div class="bg-white shadow-sm border-b">
        <div class="px-6 py-4">
          <div class="flex items-center justify-between">
            <div>
              <h1 class="text-2xl font-bold text-gray-900">培养方案简介</h1>
            </div>
            <div>
              <t-button theme="primary" @click="handleEdit">
                <template #icon>
                  <t-icon name="edit" />
                </template>
                编辑培养方案
              </t-button>
            </div>
          </div>
        </div>
      </div>

      <!-- 主内容区域 -->
      <div class="px-6 py-6">
        <!-- 培养方案欢迎信息 -->
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
          <p class="text-blue-700">
            欢迎来到培养方案 <strong>{{ planData.name }}</strong> 的简介页面，这里展示了培养方案的详细信息和概述。
          </p>
        </div>

        <!-- 主要内容区域 -->
        <div class="bg-white rounded-lg shadow-sm p-6">
          <!-- 培养方案标题 -->
          <div class="mb-4">
            <h2 class="text-xl font-semibold text-gray-900 mb-2">{{ planData.name }}</h2>
            <div class="text-sm text-gray-600">培养方案版本：{{ planData.version }}</div>
          </div>

          <!-- 培养方案概述 -->
          <div class="mb-6">
            <div class="flex items-center mb-3">
              <t-icon name="info-circle" class="text-blue-500 mr-2" />
              <span class="font-medium text-gray-900">培养方案概述</span>
            </div>
            <!-- todo fix me， 没有概述 -->
            <p class="text-gray-700 leading-relaxed">{{ '暂无概述' }}</p>
          </div>

          <!-- 详细信息 -->
          <div class="mb-6">
            <h3 class="font-medium text-gray-900 mb-4">详细信息</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <!-- 适用年级 -->
              <div class="flex items-center p-3 bg-gray-50 rounded-lg">
                <t-icon name="calendar" class="text-blue-500 mr-3" />
                <div>
                  <div class="text-sm text-gray-600">适用年级</div>
                  <div class="font-medium text-gray-900">{{ planData.version }}</div>
                </div>
              </div>

              <!-- 方案状态 -->
              <div class="flex items-center p-3 bg-gray-50 rounded-lg">
                <t-icon name="check-circle" class="text-green-500 mr-3" />
                <div>
                  <div class="text-sm text-gray-600">方案状态</div>
                  <t-tag :theme="planData.status === 1 ? 'success' : 'warning'" size="small">
                    {{ enumData?.map?.planStatus?.[planData.status] || (planData.status === 1 ? '启用' : '禁用') }}
                  </t-tag>
                </div>
              </div>

              <!-- 所属专业 -->
              <div class="flex items-center p-3 bg-gray-50 rounded-lg">
                <t-icon name="education" class="text-purple-500 mr-3" />
                <div>
                  <div class="text-sm text-gray-600">所属专业</div>
                  <div class="font-medium text-gray-900">{{ planData.majorName }}</div>
                </div>
              </div>

              <!-- 认证标准 -->
              <div class="flex items-center p-3 bg-gray-50 rounded-lg">
                <t-icon name="verified" class="text-orange-500 mr-3" />
                <div>
                  <div class="text-sm text-gray-600">认证标准</div>
                  <div class="font-medium text-gray-900">{{ planData.standardName }}</div>
                </div>
              </div>

              <!-- 创建日期 -->
              <div class="flex items-center p-3 bg-gray-50 rounded-lg">
                <t-icon name="calendar" class="text-blue-500 mr-3" />
                <div>
                  <div class="text-sm text-gray-600">创建日期</div>
                  <div class="font-medium text-gray-900">{{ planData.createTime?.split(' ')[0] }}</div>
                </div>
              </div>

              <!-- 最后更新 -->
              <div class="flex items-center p-3 bg-gray-50 rounded-lg">
                <t-icon name="time" class="text-gray-500 mr-3" />
                <div>
                  <div class="text-sm text-gray-600">最后更新</div>
                  <div class="font-medium text-gray-900">{{ planData.modifyTime?.split(' ')[0] }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 培养方案编辑弹窗 -->
    <PlanFormDialog :major="currentMajor" :standard-list="standardList" v-model:visible="planDialog.visible"
      :form-data="planFormData" @update:form-data="planFormData = $event" :mode="planDialog.mode" :enum-data="enumData"
      :submit-loading="planDialog.loading" @submit="handlePlanSubmit" @switch-to-edit="handleSwitchToEdit"
      @close="handleDialogClose" />
  </div>
</template>

<script setup lang="ts">
import { getMyMajors } from '@/api/base/major';
import { getPlanDetail, updatePlan } from '@/api/major/plan';
import { getEnum } from '@/api/system/enum';
import { getGraduationStandardList } from '@/api/system/graduation-standard';
import { MessagePlugin } from 'tdesign-vue-next';
import { onMounted, ref } from 'vue';
import { useRoute } from 'vue-router';
import PlanFormDialog from '../dashboard/major-leader-components/PlanFormDialog.vue';
import { Major, PlanFormData, Standard } from '../dashboard/major-leader-components/types';

const route = useRoute();
const majorId = ref(route.params.majorId as string);
const planId = ref(route.params.planId as string);

const planData = ref({} as any);
const enumData = ref<any>(null);
const currentMajor = ref<Major | null>(null);
const standardList = ref<Standard[]>([]);
const loading = ref(true);

// 编辑对话框状态
const planDialog = ref<{
  visible: boolean;
  loading: boolean;
  mode: "edit" | "view";
}>({
  visible: false,
  loading: false,
  mode: "edit",
});

const planFormData = ref<PlanFormData>({
  planName: "",
  planVersion: new Date().getFullYear(),
  standardId: null,
  status: 1,
});

const getPlanData = async () => {
  try {
    loading.value = true;
    const res = await getPlanDetail(Number(planId.value));
    planData.value = res.data;
  } finally {
    loading.value = false;
  }
};

const getEnumData = async () => {
  const res = await getEnum();
  enumData.value = res.data;
};

const getCurrentMajor = async () => {
  const res = await getMyMajors();
  const majors = res.data;
  currentMajor.value = majors.find((item: Major) => item.id == Number(majorId.value)) || null;
};

const getStandardList = async () => {
  if (!currentMajor.value) return;

  const params = {
    disciplineType: currentMajor.value.discipline,
    current: 1,
    size: 100,
    status: 0,
  };
  const res = await getGraduationStandardList(params);
  standardList.value = res.data.records;
};

const handleEdit = () => {
  // 准备表单数据
  planFormData.value = {
    id: planData.value.id,
    planName: planData.value.name,
    planVersion: planData.value.version,
    standardId: planData.value.standardId,
    status: Number(planData.value.status),
  };

  // 打开编辑对话框
  planDialog.value = {
    visible: true,
    loading: false,
    mode: "edit",
  };
};

const handlePlanSubmit = async (formData: PlanFormData) => {
  planDialog.value.loading = true;
  try {
    await updatePlan(formData.id, formData);
    MessagePlugin.success("培养方案更新成功");
    handleDialogClose();
    await getPlanData(); // 重新获取数据
  } finally {
    planDialog.value.loading = false;
  }
};

const handleSwitchToEdit = () => {
  planDialog.value.mode = "edit";
};

const handleDialogClose = () => {
  planDialog.value.visible = false;
  planFormData.value = {
    planName: "",
    planVersion: new Date().getFullYear(),
    standardId: null,
    status: 1,
  };
};

onMounted(async () => {
  await getEnumData();
  await getCurrentMajor();
  await getStandardList();
  await getPlanData();
});
</script>

<template>
  <div class="min-h-screen bg-gray-50">
    <!-- Skeleton Loader -->
    <div v-if="loading">
      <!-- Title Skeleton -->
      <div class="bg-white shadow-sm border-b">
        <div class="px-6 py-4">
          <div class="h-8 bg-gray-200 rounded animate-pulse w-64"></div>
        </div>
      </div>

      <!-- Main Content Skeleton -->
      <div class="p-6">
        <!-- Welcome Message Skeleton -->
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
          <div class="h-5 bg-blue-100 rounded animate-pulse w-full mb-2"></div>
        </div>

        <!-- Matrix Content Skeleton -->
        <div class="bg-white rounded-lg shadow-sm p-6">
          <!-- Legend Skeleton -->
          <div class="mb-4">
            <div class="h-6 bg-gray-200 rounded animate-pulse w-1/4 mb-3"></div>
            <div class="flex flex-wrap gap-x-4 gap-y-2">
              <div v-for="i in 4" :key="i" class="flex items-center space-x-2">
                <div class="w-6 h-6 bg-gray-200 rounded animate-pulse"></div>
                <div class="h-4 bg-gray-200 rounded animate-pulse w-20"></div>
              </div>
            </div>
          </div>

          <!-- Table Skeleton -->
          <div class="border border-gray-200 rounded-lg overflow-hidden">
            <!-- Table Header Skeleton -->
            <div class="bg-gray-100 p-3">
              <div class="flex items-center space-x-2 mb-2">
                <div class="h-4 bg-gray-200 rounded animate-pulse w-16"></div>
                <div v-for="i in 6" :key="i" class="h-4 bg-gray-200 rounded animate-pulse w-12"></div>
              </div>
              <div class="flex items-center space-x-2">
                <div class="h-4 bg-gray-200 rounded animate-pulse w-20"></div>
                <div v-for="i in 12" :key="i" class="h-4 bg-gray-200 rounded animate-pulse w-8"></div>
              </div>
            </div>

            <!-- Table Body Skeleton -->
            <div class="p-2">
              <div v-for="i in 8" :key="i"
                class="flex items-center space-x-2 py-2 border-b border-gray-100 last:border-b-0">
                <div class="h-8 bg-gray-200 rounded animate-pulse w-32"></div>
                <div v-for="j in 12" :key="j" class="h-6 bg-gray-200 rounded animate-pulse w-8"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Actual Content -->
    <div v-else>
      <!-- Title -->
      <div class="bg-white shadow-sm border-b">
        <div class="px-6 py-4">
          <h1 class="text-2xl font-bold text-gray-900">
            {{ plan.planName }} 支撑矩阵
          </h1>
        </div>
      </div>


      <!-- Main Content -->
      <div class="p-6">

        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
          <p class="text-blue-700">
            欢迎来到培养方案 <strong>{{ plan?.planName }}</strong>
            的课程-毕业要求矩阵页面，您可以在这里管理课程-毕业要求支撑矩阵。
          </p>
        </div>

        <div class="bg-white rounded-lg shadow-sm p-6">
          <MatrixLegend />
          <MatrixTable :courses="courses" :requirements="requirements" :matrix-data="matrixData"
            @update-level="handleSupportLevelChange" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { getCourseList } from '@/api/training/course';
import { getPlan } from '@/api/training/plan';
import { getPoTree } from '@/api/training/po';
import { addPoMatrix, getPoMatrixByPlanId, PoMatrix, updatePoMatrix } from '@/api/training/po-matrix';
import { MessagePlugin } from 'tdesign-vue-next';
import { computed, onMounted, ref } from 'vue';
import { useRoute } from 'vue-router';
import MatrixLegend from './components/MatrixLegend.vue';
import MatrixTable from './components/MatrixTable.vue';

const route = useRoute();
const planId = ref(route.params.planId as string);

const loading = ref(true);
const plan = ref<any>({});
const courses = ref<any[]>([]);
const requirements = ref<any[]>([]);
const matrixData = ref<PoMatrix[]>([]);

const allIndicators = computed(() => {
  return requirements.value.flatMap((req: any) => req.children || []);
});

const matrixMap = computed(() => {
  const map = new Map<string, PoMatrix>();
  matrixData.value.forEach(item => {
    map.set(`${item.courseId}-${item.poId}`, item);
  });
  return map;
});

const handleSupportLevelChange = async ({ courseId, poId, value }: { courseId: number; poId: number; value: any }) => {
  const newWeight = value ? parseFloat(value) : 0;
  const existingRel = matrixMap.value.get(`${courseId}-${poId}`);
  const indicator = allIndicators.value.find((ind: any) => ind.id === poId);

  if (!indicator) {
    MessagePlugin.error('指标信息不存在');
    return;
  }

  const payload: PoMatrix = {
    planId: Number(planId.value),
    standardId: indicator.requirementId,
    courseId,
    poId,
    weight: newWeight,
  };

  try {
    if (existingRel && existingRel.id) {
      await updatePoMatrix(existingRel.id, payload);
    } else {
      await addPoMatrix(payload);
    }
    await fetchMatrixData(); // Refresh data
    MessagePlugin.success('更新成功');
  } catch (error) {
    MessagePlugin.error('更新失败');
    console.error(error);
  }
};

const fetchMatrixData = async () => {
  const pId = Number(planId.value);
  const res = await getPoMatrixByPlanId(pId);
  matrixData.value = res.data || [];
};

onMounted(async () => {
  loading.value = true;
  try {
    const pId = Number(planId.value);
    const [planRes, courseRes, poRes] = await Promise.all([
      getPlan(pId),
      getCourseList({ planId: pId, size: 1000 }),
      getPoTree(pId),
    ]);

    plan.value = planRes.data;
    courses.value = courseRes.data.records || [];
    requirements.value = poRes.data || [];

    await fetchMatrixData();
  } catch (error) {
    MessagePlugin.error('数据加载失败');
    console.error(error);
  } finally {
    loading.value = false;
  }
});
</script>

<style scoped>
/* Scoped styles can be added here if needed */
</style>

<template>
  <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
    <div class="text-lg font-semibold text-gray-900 mb-6">培养计划与标准信息</div>
    <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
      <!-- 培养计划信息 -->
      <div>
        <div class="text-base font-medium text-gray-900 mb-4">培养计划信息</div>
        <div v-if="planData.name" class="space-y-3">
          <div class="flex items-center">
            <span class="text-sm text-gray-600 w-20">计划名称:</span>
            <span class="text-sm text-gray-900">{{ planData.name }}</span>
          </div>
          <div class="flex items-center">
            <span class="text-sm text-gray-600 w-20">计划版本:</span>
            <span class="text-sm text-gray-900">{{ planData.version }}</span>
          </div>
          <div class="flex items-center">
            <span class="text-sm text-gray-600 w-20">创建时间:</span>
            <span class="text-sm text-gray-900">{{
              planData.createTime?.split(" ")[0]
            }}</span>
          </div>
        </div>
        <div v-else class="text-gray-500 text-sm">暂无培养计划信息</div>
      </div>

      <!-- 关联标准信息 -->
      <div>
        <div class="text-base font-medium text-gray-900 mb-4">关联标准信息</div>
        <div v-if="standardData.standardName" class="space-y-3">
          <div class="flex items-center">
            <span class="text-sm text-gray-600 w-20">标准名称:</span>
            <span class="text-sm text-gray-900">{{
              standardData.standardName
            }}</span>
          </div>
          <div class="flex items-center">
            <span class="text-sm text-gray-600 w-20">标准版本:</span>
            <span class="text-sm text-gray-900">{{
              standardData.standardVersion
            }}</span>
          </div>
          <div class="flex items-center">
            <span class="text-sm text-gray-600 w-20">学科类型:</span>
            <span class="text-sm text-gray-900">{{
              enumData?.map?.disciplineType?.[standardData.disciplineType]
            }}</span>
          </div>
          <div class="flex items-center">
            <span class="text-sm text-gray-600 w-20">发布日期:</span>
            <span class="text-sm text-gray-900">{{
              standardData.releaseDate
            }}</span>
          </div>
        </div>
        <div v-else class="text-gray-500 text-sm">暂无关联标准信息</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  planData: any;
  standardData: any;
  enumData: any;
}

defineProps<Props>();
</script>

<template>
  <td class="relative border-l border-neutral-200 px-1 py-1 text-center text-xs text-neutral-700"
    style="min-width: 85px; width: 85px" @mouseenter="handleMouseEnter" @mouseleave="handleMouseLeave"
    @click="handleCellClick">
    <t-select v-if="isHovered || isSelectOpen" :model-value="value" class="block w-full text-xs" size="small"
      style="min-width: 75px; width: 75px" @change="handleSelectChange" @visible-change="handleVisibleChange"
      @focus="handleSelectFocus" @blur="handleSelectBlur">
      <t-option :value="undefined" label="- 未设置 -"></t-option>
      <t-option v-for="scoreValue in scoreOptions" :key="scoreValue" :label="`${scoreValue.toFixed(1)}`"
        :value="scoreValue.toFixed(1)"></t-option>
    </t-select>
    <div v-else class="flex h-full min-h-[24px] w-full cursor-pointer items-center justify-center">
      <template v-if="!displayInfo.isSet">
        <span class="text-neutral-400">-</span>
      </template>
      <template v-else>
        <span :class="[
          'flex items-center justify-center rounded px-1.5 py-0.5 text-xs font-medium',
          displayInfo.badgeClass,
        ]">
          <t-icon v-if="displayInfo.icon" :name="displayInfo.icon" class="mr-1 h-3 w-3" />
          <span>{{ displayInfo.scoreText }}</span>
        </span>
      </template>
    </div>
  </td>
</template>

<script setup lang="ts">
import { Icon as TIcon } from "tdesign-icons-vue-next";
import { ref } from "vue";

interface DisplayInfo {
  isSet: boolean;
  scoreText: string;
  badgeClass?: string;
  icon?: string;
}

defineProps({
  value: { type: String, default: undefined },
  displayInfo: { type: Object as () => DisplayInfo, required: true },
  scoreOptions: { type: Array as () => number[], required: true },
});

const emit = defineEmits(["update-level"]);

const isHovered = ref(false);
const isSelectOpen = ref(false);
let leaveTimer: any = null;

const handleMouseEnter = () => {
  if (leaveTimer) {
    clearTimeout(leaveTimer);
    leaveTimer = null;
  }
  isHovered.value = true;
};

const handleMouseLeave = () => {
  // 大幅增加延时，基本确保用户有足够时间操作
  leaveTimer = setTimeout(() => {
    // 只有在选择框没有打开时才隐藏
    if (!isSelectOpen.value) {
      isHovered.value = false;
    }
  }, 200); // 增加到1秒
};

// 点击单元格时，确保选择框显示并打开
const handleCellClick = () => {
  if (leaveTimer) {
    clearTimeout(leaveTimer);
    leaveTimer = null;
  }
  isHovered.value = true;
  // 下一个tick后触发选择框打开
  setTimeout(() => {
    isSelectOpen.value = true;
  }, 10);
};

// 处理选择框可见性变化
const handleVisibleChange = (visible: boolean) => {
  isSelectOpen.value = visible;

  // 如果选择框打开，清除隐藏定时器
  if (visible && leaveTimer) {
    clearTimeout(leaveTimer);
    leaveTimer = null;
  }

  // 如果选择框关闭了，立即隐藏整个组件
  if (!visible) {
    isHovered.value = false;
    isSelectOpen.value = false;
  }
};

// 处理选择变化
const handleSelectChange = (val: any) => {
  emit("update-level", val);
  // 选择完成后，选择框会自动关闭，触发 handleVisibleChange
  isHovered.value = false;
  isSelectOpen.value = false;
};

// 处理选择框获得焦点
const handleSelectFocus = () => {
  if (leaveTimer) {
    clearTimeout(leaveTimer);
    leaveTimer = null;
  }
  isSelectOpen.value = true;
};

// 处理选择框失去焦点
const handleSelectBlur = () => {
  // 延时隐藏，给用户时间点击选项
  setTimeout(() => {
    if (!isSelectOpen.value) {
      isHovered.value = false;
    }
  }, 200);
};
</script>

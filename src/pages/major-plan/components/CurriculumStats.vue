<template>
  <div class="grid grid-cols-1 md:grid-cols-5 gap-6 mb-6">
    <StatCard v-for="(stat, index) in curriculumStats" :key="index" :title="stat.title" :value="stat.value"
      :icon="stat.icon" :color="stat.color" />
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import StatCard from './StatCard.vue';

const props = defineProps({
  stats: {
    type: Object,
    required: true,
    default: () => ({
      requiredCourses: 0,
      totalCredits: 0,
      totalHours: 0,
      practiceHours: 0,
      totalCourses: 0,
    }),
  },
});

const curriculumStats = computed(() => [
  {
    title: '必修课数',
    value: props.stats.requiredCourses,
    icon: 'education',
    color: 'blue',
  },
  {
    title: '总学分',
    value: props.stats.totalCredits,
    icon: 'chart-bar',
    color: 'green',
  },
  {
    title: '总学时',
    value: props.stats.totalHours,
    icon: 'time',
    color: 'orange',
  },
  {
    title: '实践学时',
    value: props.stats.practiceHours,
    icon: 'tools',
    color: 'purple',
  },
  {
    title: '课程数',
    value: props.stats.totalCourses,
    icon: 'layers',
    color: 'indigo',
  },
]);
</script>

<template>
  <div>
    <!-- 顶部信息 -->
    <div class="bg-white shadow-sm border-b">
      <div class="px-6 py-4">
        <h1 class="text-2xl font-bold text-gray-900">教学执行计划管理</h1>
      </div>
    </div>

    <!-- 主内容区域 -->
    <div class="px-6 py-6">
      <!-- 欢迎信息 -->
      <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
        <div class="flex items-center">
          <t-icon name="info-circle" class="text-blue-500 mr-2" />
          <p class="text-blue-700">
            管理各学期的教学计划，分配教学任务和相关的支撑
          </p>
        </div>
      </div>

      <!-- 学期选择 -->
      <div class="bg-white rounded-lg shadow-sm p-6">
        <h2 class="text-lg font-semibold text-gray-900 mb-6">选择学期</h2>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <!-- 学期卡片 -->
          <SemesterCard v-for="semester in sortedSemesterList" :key="semester.id" :semester="semester"
            @manage="handleManageSemester" @manage-task="handleManageTask" @view-statistics="handleViewStatistics" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import SemesterCard from './ScheduleSemesterCard.vue';

// Props
interface Props {
  semesterList: any[];
}

const props = defineProps<Props>();

// 计算属性：按学期正序排序
const sortedSemesterList = computed(() => {
  return [...props.semesterList].sort((a, b) => {
    // 按学期编号正序排序
    if (a.semesterNumber && b.semesterNumber) {
      return a.semesterNumber - b.semesterNumber;
    }
    // 如果没有学期编号，按ID排序
    return a.id - b.id;
  });
});

// Events
interface Emits {
  (e: 'manage-semester', semester: any): void;
  (e: 'manage-task', semester: any): void;
  (e: 'view-statistics', semester: any): void;
}

const emit = defineEmits<Emits>();

// Methods
const handleManageSemester = (semester: any) => {
  emit('manage-semester', semester);
};

const handleManageTask = (semester: any) => {
  emit('manage-task', semester);
};

const handleViewStatistics = (semester: any) => {
  emit('view-statistics', semester);
};
</script>

<style scoped>
/* 使用 Tailwind CSS，无需自定义样式 */
</style>

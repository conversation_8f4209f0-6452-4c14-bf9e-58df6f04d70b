<template>
  <div class="bg-white rounded-lg shadow-sm p-6">
    <div class="flex flex-col space-y-4 mb-6">
      <div class="flex justify-between items-center">
        <h2 class="text-lg font-semibold text-gray-900 flex items-center">
          <t-icon name="tag" class="text-blue-500 mr-2" />
          毕业要求列表
        </h2>
      </div>
      <div class="flex justify-end">
        <t-input :model-value="searchText" @update:model-value="$emit('update:searchText', $event)"
          placeholder="搜索毕业要求..." clearable class="w-64">
          <template #prefix-icon>
            <t-icon name="search" />
          </template>
        </t-input>
      </div>
    </div>

    <div v-if="filteredPoTree.length > 0" class="space-y-4">
      <RequirementCard v-for="(req, idx) in filteredPoTree" :key="req.id" :requirement="req"
        @add-indicator="(requirement) => $emit('add-indicator', requirement)" @edit-indicator="
          (requirement, indicator) =>
            $emit('edit-indicator', requirement, indicator)
        " @delete-indicator="
          (requirement, indicator) =>
            $emit('delete-indicator', requirement, indicator)
        " />
    </div>
    <div v-else class="text-center py-16">
      <t-icon name="inbox" class="text-gray-400 text-6xl mb-4" />
      <p class="text-gray-500 text-lg">当前培养方案关联的标准中无数据</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from "vue";
import RequirementCard from "./RequirementCard.vue";

interface Props {
  poTree: any[];
  searchText: string;
}

const props = defineProps<Props>();

const emit = defineEmits<{
  "update:searchText": [value: string];
  "add-requirement": [];
  "add-indicator": [requirement: any];
  "edit-indicator": [requirement: any, indicator: any];
  "delete-indicator": [requirement: any, indicator: any];
}>();

const filteredPoTree = computed(() => {
  if (!props.searchText) return props.poTree;
  return props.poTree.filter(
    (po) =>
      po.poTitle.toLowerCase().includes(props.searchText.toLowerCase()) ||
      po.poDescription.toLowerCase().includes(props.searchText.toLowerCase()),
  );
});
</script>

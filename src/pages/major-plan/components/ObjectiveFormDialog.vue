<template>
  <t-dialog :visible="visible" @update:visible="$emit('update:visible', $event)" header="编辑培养目标与调研题目" :width="800"
    :footer="false" @close="handleClose" class="objective-dialog" :close-on-esc-keydown="false">
    <div class="h-[700px] flex flex-col">
      <!-- 培养目标信息区域 -->
      <div class="bg-white rounded-lg shadow-md p-6 mb-4">
        <h3 class="text-lg font-semibold text-gray-900 mb-6">培养目标信息</h3>
        <t-form label-width="100px">
          <t-form-item label="目标编号">
            <t-input v-model="eoTitle" placeholder="请输入培养目标编号" clearable class="w-full" />
          </t-form-item>
          <t-form-item label="目标描述">
            <t-textarea v-model="eoDescription" placeholder="请输入培养目标描述" :autosize="{ minRows: 3, maxRows: 5 }"
              class="w-full" />
          </t-form-item>
        </t-form>
      </div>

      <!-- 关联调研题目区域 -->
      <div class="flex-1 bg-white rounded-lg border border-gray-200 overflow-hidden">
        <div class="h-full flex">
          <!-- 左侧：题目列表 -->
          <div class="w-50 border-r border-gray-200 bg-gray-50 flex flex-col">
            <!-- 添加新题目按钮 -->
            <div class="p-4 border-b border-gray-200 bg-white">
              <t-button theme="primary" size="medium" @click="addQuestion" class="w-full">
                <template #icon>
                  <t-icon name="add" />
                </template>
                添加新调研题目
              </t-button>
            </div>

            <!-- 题目导航列表 -->
            <div class="flex-1 overflow-y-auto">
              <div v-if="questions.length > 0" class="p-3 space-y-2">
                <div v-for="(question, index) in questions" :key="question.id" :class="[
                  'p-3 rounded-lg border-2 cursor-pointer transition-all duration-200',
                  selectedQuestionIndex === index
                    ? 'border-blue-400 bg-blue-50 shadow-sm'
                    : 'border-gray-200 bg-white hover:border-gray-300 hover:shadow-sm',
                ]" @click="selectQuestion(index)">
                  <div class="flex justify-between items-start">
                    <div class="flex-1">
                      <div class="font-medium text-gray-900 text-sm mb-1">
                        题目 {{ index + 1 }}:
                        {{ question.title || "未命名题目" }}
                      </div>
                      <div class="text-xs text-gray-500">
                        {{ question.options?.length || 0 }} 个选项
                      </div>
                    </div>
                    <t-button theme="danger" size="small" variant="text" @click.stop="deleteQuestion(index)">
                      <t-icon name="delete" />
                    </t-button>
                  </div>
                </div>
              </div>

              <div v-else class="flex items-center justify-center h-full text-gray-500 text-sm">
                暂无调研题目
              </div>
            </div>
          </div>

          <!-- 右侧：题目编辑区域 -->
          <div class="flex-1 flex flex-col">
            <div v-if="
              selectedQuestionIndex !== null &&
              questions[selectedQuestionIndex]
            " class="h-full flex flex-col">
              <!-- 编辑标题 -->
              <div class="p-4 border-b border-gray-200 bg-white">
                <h4 class="font-medium text-blue-600">编辑调研题目</h4>
              </div>

              <!-- 编辑内容 -->
              <div class="flex-1 p-4 space-y-4 overflow-y-auto">
                <!-- 题目内容 -->
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">题目内容</label>
                  <t-textarea v-model="questions[selectedQuestionIndex].title" placeholder="请输入题目内容"
                    :autosize="{ minRows: 2, maxRows: 4 }" />
                </div>

                <!-- 题目类型 -->
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">题目类型</label>
                  <t-select v-model="questions[selectedQuestionIndex].type" placeholder="请选择题目类型">
                    <t-option value="single" label="单选题" />
                    <t-option value="multiple" label="多选题" />
                    <t-option value="text" label="文本题" />
                  </t-select>
                </div>

                <!-- 选项设置 -->
                <div v-if="questions[selectedQuestionIndex].type !== 'text'">
                  <label class="block text-sm font-medium text-gray-700 mb-2">选项设置</label>
                  <div class="space-y-3">
                    <div v-for="(option, optionIndex) in questions[
                      selectedQuestionIndex
                    ].options" :key="optionIndex" class="flex items-center space-x-3">
                      <div class="w-8 h-8 bg-gray-100 rounded flex items-center justify-center text-sm font-medium">
                        {{ String.fromCharCode(65 + optionIndex) }}.
                      </div>
                      <t-input v-model="questions[selectedQuestionIndex].options[optionIndex]
                        .option
                        " :placeholder="`选项 ${String.fromCharCode(65 + optionIndex)}`" class="flex-1" />
                      <t-input-number v-model="questions[selectedQuestionIndex].options[optionIndex]
                        .weight
                        " placeholder="0" type="number" :min="0" style="width: 120px" />
                      <t-button theme="danger" size="small" variant="text"
                        @click="removeOptionFromSelected(optionIndex)">
                        <t-icon name="delete" />
                      </t-button>
                    </div>

                    <t-button theme="primary" variant="text" size="small" @click="addOptionToSelected" class="mt-2">
                      <template #icon>
                        <t-icon name="add" />
                      </template>
                      添加选项
                    </t-button>
                  </div>
                </div>

                <!-- 必填设置 -->
                <div>
                  <t-checkbox v-model="questions[selectedQuestionIndex].required">
                    必填题目
                  </t-checkbox>
                </div>
              </div>

              <!-- 题目操作按钮 -->
              <div class="p-4 border-t border-gray-200 bg-gray-50 flex justify-end space-x-2">
                <t-button theme="default" variant="outline" @click="cancelEditQuestion">
                  取消编辑
                </t-button>
                <t-button theme="primary" @click="saveCurrentQuestion"
                  :disabled="!questions[selectedQuestionIndex].title">
                  保存此调研题目
                </t-button>
              </div>
            </div>

            <div v-else class="flex-1 flex items-center justify-center text-gray-500">
              请选择左侧题目进行编辑，或添加新的调研题目
            </div>
          </div>
        </div>
      </div>

      <!-- 底部操作按钮 -->
      <div class="mt-4 flex items-center justify-between">
        <div class="text-orange-500 font-medium flex items-center">
          <t-icon name="info-circle-filled" class="mr-1" />
          <span>提示：修改调研题目后最终需要保存所有更改</span>
        </div>
        <div class="flex space-x-2">
          <t-button theme="default" variant="base" @click="handleClose">
            关闭
          </t-button>
          <t-button theme="primary" @click="saveAllChanges" :loading="submitLoading">
            保存所有更改
          </t-button>
        </div>
      </div>
    </div>
  </t-dialog>
</template>

<script setup lang="ts">
import { MessagePlugin } from "tdesign-vue-next";
import { computed, ref, watch } from "vue";

// 定义Question接口
interface Question {
  id: string;
  title: string;
  type: "single" | "multiple" | "text";
  options?: Option[];
  required: boolean;
}

interface Option {
  option: string;
  weight: number;
}

// 定义EoVO接口
interface EoVO {
  id?: number;
  eoTitle: string;
  eoDescription: string;
  status: number;
  createTime?: string;
  modifyTime?: string;
  relatedQuestionsArray?: Question[]; // 存储题目数组
}

interface Props {
  visible: boolean;
  formData: EoVO;
  isEdit: boolean;
  submitLoading: boolean;
  planName: string | undefined;
}

const props = defineProps<Props>();

const emit = defineEmits<{
  "update:visible": [value: boolean];
  "update:formData": [value: EoVO];
  submit: [context: any];
  cancel: [];
}>();

// 题目管理相关状态
const questions = ref<Question[]>([]);
const selectedQuestionIndex = ref<number | null>(null);

// 计算属性处理表单字段的双向绑定
const eoTitle = computed({
  get: () => props.formData.eoTitle,
  set: (value: string) => {
    updateFormData("eoTitle", value);
  },
});

const eoDescription = computed({
  get: () => props.formData.eoDescription,
  set: (value: string) => {
    updateFormData("eoDescription", value);
  },
});

// 初始化题目数据
const initQuestions = () => {
  // 直接使用数组格式的 relatedQuestions
  questions.value = props.formData.relatedQuestionsArray || [];
};

// 监听props变化
watch(
  () => props.formData.relatedQuestionsArray,
  (newQuestions) => {
    // 比较数组内容是否变化
    const currentQuestionsStr = JSON.stringify(questions.value);
    const newQuestionsStr = JSON.stringify(newQuestions || []);
    if (currentQuestionsStr !== newQuestionsStr) {
      initQuestions();
    }
  },
  { immediate: true },
);

// 监听题目类型变化，重置选项
watch(
  () => {
    if (
      selectedQuestionIndex.value !== null &&
      questions.value[selectedQuestionIndex.value]
    ) {
      return questions.value[selectedQuestionIndex.value].type;
    }
    return null;
  },
  (newType) => {
    if (selectedQuestionIndex.value !== null && newType) {
      const question = questions.value[selectedQuestionIndex.value];
      if (newType === "text") {
        question.options = [];
      } else if (!question.options || question.options.length === 0) {
        question.options = [
          { option: "", weight: 0 },
          { option: "", weight: 0 },
        ];
      }
      updateQuestions();
    }
  },
);

// 更新题目数据到formData
const updateQuestions = () => {
  updateFormData("relatedQuestionsArray", questions.value);
};

// 选择题目
const selectQuestion = (index: number) => {
  selectedQuestionIndex.value = index;
};

// 添加题目
const addQuestion = () => {
  const newQuestion: Question = {
    id: Date.now().toString(),
    title: "",
    type: "single",
    options: [
      { option: "", weight: 0 },
      { option: "", weight: 0 },
    ],
    required: false,
  };
  questions.value.push(newQuestion);
  selectedQuestionIndex.value = questions.value.length - 1;
  updateQuestions();
};

// 删除题目
const deleteQuestion = (index: number) => {
  questions.value.splice(index, 1);
  if (selectedQuestionIndex.value === index) {
    selectedQuestionIndex.value = null;
  } else if (
    selectedQuestionIndex.value !== null &&
    selectedQuestionIndex.value > index
  ) {
    selectedQuestionIndex.value--;
  }
  updateQuestions();
};

// 为选中题目添加选项
const addOptionToSelected = () => {
  if (selectedQuestionIndex.value !== null) {
    const question = questions.value[selectedQuestionIndex.value];
    if (!question.options) {
      question.options = [];
    }
    question.options.push({ option: "", weight: 0 });
    updateQuestions();
  }
};

// 删除选中题目的选项
const removeOptionFromSelected = (optionIndex: number) => {
  if (selectedQuestionIndex.value !== null) {
    const question = questions.value[selectedQuestionIndex.value];
    if (question.options) {
      question.options.splice(optionIndex, 1);
      updateQuestions();
    }
  }
};

// 取消编辑题目
const cancelEditQuestion = () => {
  selectedQuestionIndex.value = null;
};

// 保存当前题目
const saveCurrentQuestion = () => {
  updateQuestions();
  MessagePlugin.success("题目数据更新成功");
};

// 保存所有更改
const saveAllChanges = () => {
  if (validateForm()) {
    updateQuestions();
    emit("submit", { validateResult: true });
  } else {
    emit("submit", { validateResult: false });
  }
};

// 关闭弹窗
const handleClose = () => {
  selectedQuestionIndex.value = null;
  emit("update:visible", false);
};

// 验证表单数据
const validateForm = (): boolean => {
  if (!props.formData.eoTitle?.trim()) {
    MessagePlugin.warning("培养目标标题不能为空");
    return false;
  }
  if (!props.formData.eoDescription?.trim()) {
    MessagePlugin.warning("培养目标描述不能为空");
    return false;
  }
  return true;
};

const updateFormData = (field: keyof EoVO, value: any) => {
  const updatedFormData = { ...props.formData, [field]: value };
  emit("update:formData", updatedFormData);
};
</script>

<style scoped>
.objective-dialog :deep(.t-dialog__body) {
  padding: 0;
}

.objective-dialog :deep(.t-dialog__content) {
  padding: 16px;
}
</style>

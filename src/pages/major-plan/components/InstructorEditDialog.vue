<template>
  <t-dialog :visible="visible" header="编辑课程负责人" width="500px" :on-confirm="handleSubmit"
    @update:visible="(val) => $emit('update:visible', val)" :confirm-btn="{ content: '提交', loading: submitLoading }"
    :cancel-btn="{ content: '取消' }">
    <t-form ref="formRef" :data="formData" :rules="rules" label-align="top">
      <t-form-item label="课程名称">
        <t-input :value="course.courseName" readonly />
      </t-form-item>
      <t-form-item label="课程负责人" name="courseLeader">
        <t-select v-model="formData.courseLeader" :options="teacherList" placeholder="请选择课程负责人" clearable filterable
          style="width: 100%" />
      </t-form-item>
    </t-form>
  </t-dialog>
</template>

<script setup lang="ts">
import type { FormInstanceFunctions, FormRule } from "tdesign-vue-next";
import { ref, watch } from "vue";

const props = defineProps<{
  visible: boolean;
  course: any;
  teacherList: any[];
}>();

const emit = defineEmits(["update:visible", "submit"]);

const formRef = ref<FormInstanceFunctions | null>(null);
const formData = ref<{ courseLeader: number | null }>({ courseLeader: null });
const submitLoading = ref(false);

const rules: Record<string, FormRule[]> = {
  courseLeader: [
    { required: true, message: "请选择课程负责人", trigger: "change" },
  ],
};

watch(
  () => props.visible,
  (val) => {
    if (val) {
      formData.value.courseLeader = props.course.courseLeader || null;
    }
  }
);

const handleSubmit = async () => {
  const result = await formRef.value?.validate();
  if (result === true) {
    submitLoading.value = true;
    try {
      await emit(
        "submit",
        { ...props.course, courseLeader: formData.value.courseLeader },
        (loading: boolean) => {
          submitLoading.value = loading;
        }
      );
    } finally {
      // Parent will handle loading state
    }
  }
};
</script>

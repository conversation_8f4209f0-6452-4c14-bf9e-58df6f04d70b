<template>
  <div
    class="rounded-lg p-6 border transition-all duration-200"
    :class="semester.current
      ? 'bg-green-50 border-green-300 shadow-md ring-2 ring-green-200 hover:border-green-400 hover:shadow-lg'
      : 'bg-gray-50 border-gray-200 hover:border-blue-300 hover:shadow-md'">
    <!-- 学期标题 -->
    <div class="mb-4">
      <h3 class="font-semibold text-gray-900 mb-1">
        第{{ semesterNumber }}学期 {{ semester.name }}
      </h3>
      <div class="flex items-center">
        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium"
          :class="semester.current
            ? 'bg-green-100 text-green-800 border border-green-200'
            : 'bg-blue-100 text-blue-800 border border-blue-200'">
          <span class="inline-block w-2 h-2 rounded-full mr-1"
            :class="semester.current ? 'bg-green-500' : 'bg-blue-500'"></span>
          {{ semester.current ? '当前学期' : '非当前学期' }}
        </span>
      </div>
    </div>

    <!-- 统计信息 -->
    <div class="grid grid-cols-2 gap-4 mb-4">
      <div class="text-center">
        <div class="text-2xl font-bold text-blue-600">
          {{ semester.doorCount }}
        </div>
        <div class="text-xs text-gray-500">门课程</div>
      </div>
      <div class="text-center">
        <div class="text-2xl font-bold text-green-600">
          {{ semester.studentCount }}
        </div>
        <div class="text-xs text-gray-500">名学生</div>
      </div>
    </div>

    <div class="grid grid-cols-2 gap-4 mb-4">
      <div class="text-center">
        <div class="text-2xl font-bold text-orange-600">
          {{ semester.taskCount }}
        </div>
        <div class="text-xs text-gray-500">个任务</div>
      </div>
      <div class="text-center">
        <div class="text-2xl font-bold text-purple-600">
          {{ semester.completedCount }}
        </div>
        <div class="text-xs text-gray-500">已完成</div>
      </div>
    </div>

    <!-- 操作按钮 -->
    <div class="flex flex-col gap-2">
      <div class="flex gap-2">
        <t-button theme="primary" size="small" class="flex-1" @click="handleManage">
          管理课程计划
        </t-button>
        <t-button theme="default" variant="outline" size="small" class="flex-1" @click="handleViewStats">
          查看统计
        </t-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';

// Props
interface Props {
  semester: any;
}

const props = defineProps<Props>();

// 计算学期编号
const semesterNumber = computed(() => {
  // 优先使用 semesterNumber 字段
  if (props.semester.semesterNumber) {
    return props.semester.semesterNumber;
  }
  // 如果没有，尝试从 ID 或其他字段推导
  if (props.semester.id) {
    return props.semester.id;
  }
  // 默认返回 1
  return 1;
});

// Events
interface Emits {
  (e: 'manage', semester: any): void;
  (e: 'manage-task', semester: any): void;
  (e: 'view-statistics', semester: any): void;
}

const emit = defineEmits<Emits>();

// Methods
const handleManage = () => {
  emit('manage', props.semester);
};

const handleViewStats = () => {
  emit('view-statistics', props.semester);
};
</script>

<style scoped>
/* 使用 Tailwind CSS，无需自定义样式 */
</style>

<template>
  <div class="bg-white rounded-lg shadow-sm p-6">
    <div class="flex items-center">
      <div class="flex-shrink-0">
        <div :class="['w-12 h-12 rounded-lg flex items-center justify-center', colorClass.bg]">
          <t-icon :name="icon" :class="['text-xl', colorClass.text]" />
        </div>
      </div>
      <div class="ml-4 flex-1 overflow-hidden">
        <p class="text-sm text-gray-500 truncate" :title="title">{{ title }}</p>
        <div class="flex items-baseline">
          <p class="text-2xl font-bold text-gray-900">{{ value }}</p>
          <p v-if="unit" class="ml-1 text-sm text-gray-500 truncate">{{ unit }}</p>
        </div>
        <p v-if="subtitle" class="text-xs text-gray-500 truncate">{{ subtitle }}</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';

const props = defineProps({
  title: { type: String, required: true },
  value: { type: [String, Number], required: true },
  unit: { type: String, default: '' },
  subtitle: { type: String, default: '' },
  icon: { type: String, required: true },
  color: { type: String, default: 'blue' },
});

const colorMap: Record<string, { bg: string, text: string }> = {
  blue: { bg: 'bg-blue-100', text: 'text-blue-600' },
  green: { bg: 'bg-green-100', text: 'text-green-600' },
  orange: { bg: 'bg-orange-100', text: 'text-orange-600' },
  purple: { bg: 'bg-purple-100', text: 'text-purple-600' },
  indigo: { bg: 'bg-indigo-100', text: 'text-indigo-600' },
  red: { bg: 'bg-red-100', text: 'text-red-600' },
  yellow: { bg: 'bg-yellow-100', text: 'text-yellow-600' },
};

const colorClass = computed(() => colorMap[props.color] || colorMap.blue);
</script>

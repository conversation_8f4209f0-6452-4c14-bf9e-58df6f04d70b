<template>
  <div class="bg-white rounded-lg shadow-sm p-6">
    <div class="flex items-center justify-between mb-4">
      <h3 class="text-lg font-semibold text-gray-900">课程管理</h3>
      <div class="flex items-center space-x-2">
        <t-button @click="$emit('export')" variant="outline" :disabled="!planId">
          <template #icon><t-icon name="download" /></template>
          导出Excel
        </t-button>

        <t-upload accept=".xlsx,.xls" :before-upload="(file) => { $emit('import', file); return false; }"
          :show-upload-progress="false" :disabled="!planId">
          <t-button theme="danger" variant="outline">
            <template #icon><t-icon name="upload" /></template>
            导入课程
          </t-button>
        </t-upload>

        <t-button @click="$emit('add')" theme="primary" :disabled="!planId">
          <template #icon><t-icon name="add" /></template>
          添加新课程
        </t-button>
      </div>
    </div>

    <!-- 搜索区域 -->
    <div class="flex items-center space-x-4 mb-4">
      <t-input :model-value="searchValue.keyword" @update:model-value="updateSearchValue({ keyword: $event })"
        placeholder="搜索课程名、名称、开课单位..." clearable class="w-64" @enter="$emit('search')">
        <template #prefix-icon>
          <t-icon name="search" />
        </template>
      </t-input>

      <t-select :model-value="searchValue.semester" @update:model-value="updateSearchValue({ semester: $event })"
        placeholder="所有学期" :options="enumData?.list?.semesterPeriod" clearable class="w-32" @change="$emit('search')" />

      <t-select :model-value="searchValue.courseType" @update:model-value="updateSearchValue({ courseType: $event })"
        placeholder="课程类别" :options="enumData?.list?.courseCategory" clearable class="w-32" @change="$emit('search')" />
    </div>

    <!-- 课程表格 -->
    <t-table :data="courses" :columns="columns" row-key="courseId" :hover="true" :loading="tableLoading"
      :pagination="pagination" @page-change="(pageInfo) => $emit('page-change', pageInfo)" size="small"
      :show-pagination="true">
      <template #courseCore="{ row }">
        <t-tag :theme="row.courseCore ? 'success' : 'default'" shape="round" size="small">
          {{ row.courseCore ? "是" : "否" }}
        </t-tag>
      </template>
      <template #courseExam="{ row }">
        <t-tag :theme="row.courseExam ? 'warning' : 'default'" shape="round" size="small">
          {{ row.courseExam ? "是" : "否" }}
        </t-tag>
      </template>
      <template #courseType1Display="{ row }">
        {{ enumData?.map?.courseCategory?.[row.courseType1] }}
      </template>
      <template #courseNatureDisplay="{ row }">
        {{ enumData?.map?.courseNature?.[row.courseNature] }}
      </template>
      <template #courseSemester="{ row }">
        {{
          enumData?.map?.semesterPeriod?.[String(row.courseSemester)] ||
          row.courseSemester
        }}
      </template>
      <template #courseLeaderDisplay="{ row }">
        {{ teacherMap[row.courseLeader] || "未设置" }}
      </template>
      <template #operation="{ row }">
        <t-space :size="4">
          <t-button variant="text" @click="$emit('edit', row)" theme="primary" size="small">
            <template #icon><t-icon name="edit" /></template>
          </t-button>
          <t-popconfirm content="确认删除该课程吗？此操作不可逆！" @confirm="$emit('delete', row)">
            <t-button variant="text" theme="danger" size="small">
              <template #icon><t-icon name="delete" /></template>
            </t-button>
          </t-popconfirm>
        </t-space>
      </template>
    </t-table>
  </div>
</template>

<script setup lang="tsx">
import { Tag as TTag } from "tdesign-vue-next";
import { h } from "vue";

const props = defineProps<{
  courses: any[];
  pagination: any;
  tableLoading: boolean;
  planId?: string | number;
  searchValue: any;
  enumData: any;
  teacherMap: any;
}>();

const emit = defineEmits([
  "export",
  "import",
  "add",
  "update:searchValue",
  "search",
  "page-change",
  "edit",
  "delete",
]);

const updateSearchValue = (payload: any) => {
  emit("update:searchValue", { ...props.searchValue, ...payload });
};

const columns: any[] = [
  { colKey: "courseId", title: "ID", width: 70, align: "center" as const },
  { colKey: "courseCode", title: "课程编码", width: 120, ellipsis: true },
  { colKey: "courseName", title: "课程名称", width: 200, ellipsis: true },
  {
    colKey: "courseCredit",
    title: "学分",
    width: 80,
    align: "center" as const,
  },
  {
    colKey: "courseCore",
    title: "核心",
    width: 80,
    align: "center" as const,
    cell: "courseCore",
  },
  {
    colKey: "courseExam",
    title: "考试",
    width: 80,
    align: "center" as const,
    cell: "courseExam",
  },
  {
    colKey: "courseHoursTotal",
    title: "总学时",
    width: 90,
    align: "center" as const,
  },
  {
    colKey: "courseSemester",
    title: "学期",
    width: 90,
    align: "center" as const,
    cell: "courseSemester",
  },
  {
    colKey: "courseType1",
    title: "课程类别",
    width: 150,
    cell: "courseType1Display",
    ellipsis: true,
  },
  {
    colKey: "courseNature",
    title: "课程性质",
    width: 120,
    cell: "courseNatureDisplay",
    ellipsis: true,
  },
  {
    colKey: "courseLeader",
    title: "课程负责人",
    width: 120,
    cell: "courseLeaderDisplay",
    ellipsis: true,
  },
  {
    colKey: "status",
    title: "状态",
    width: 80,
    align: "center" as const,
    cell: (cellH: typeof h, { row }: { row: any }) => {
      const statusMap: Record<
        string | number,
        {
          text: string;
          theme: "success" | "danger" | "default" | "warning" | "primary";
        }
      > = {
        "0": { text: "正常", theme: "success" },
        "1": { text: "停用", theme: "warning" },
        "-1": { text: "已删除", theme: "danger" },
      };
      const statusText =
        props.enumData?.map?.courseStatus?.[String(row.status)] ||
        statusMap[String(row.status)]?.text ||
        "未知";
      const theme =
        row.status === 0
          ? "success"
          : row.status === -1
            ? "danger"
            : "default";
      return cellH(
        TTag,
        { theme: theme, size: "small", shape: "round" },
        () => statusText,
      );
    },
  },
  { colKey: "createTime", title: "创建时间", width: 160 },
  {
    colKey: "operation",
    title: "操作",
    width: 150,
    fixed: "right" as const,
    align: "center" as const,
  },
];
</script>

<template>
  <div>
    <!-- 学期信息和操作栏 -->
    <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
      <div class="flex items-center justify-between mb-4">
        <div class="flex items-center">
          <t-button theme="default" variant="text" @click="handleBack">
            <template #icon>
              <t-icon name="chevron-left" class="text-blue-500" />
            </template>
            <span class="text-blue-500"> 返回学期列表 </span>
          </t-button>
          <h2 class="text-lg font-semibold text-gray-900 mr-4">
            {{ selectedSemester?.name }} - 课程计划管理
          </h2>
          <span
            class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
            {{ selectedSemester?.statusText }}
          </span>
        </div>
      </div>
    </div>

    <!-- 当前学期执行计划 -->
    <div class="bg-white rounded-lg shadow-sm p-6">
      <div class="flex items-center justify-between mb-6">
        <h3 class="text-lg font-semibold text-gray-900">
          {{ selectedSemester?.name }} 执行计划
        </h3>
        <div class="flex items-center space-x-4 text-sm text-gray-600">
          <span>共 {{ `999` }} 门课程</span>
          <span>{{ selectedSemester?.studentCount }} 名学生</span>
        </div>
      </div>

      <!-- 统计卡片 -->
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <div class="bg-blue-50 rounded-lg p-4 text-center">
          <div class="text-2xl font-bold text-blue-600">{{ selectedSemester?.doorCount }}</div>
          <div class="text-sm text-gray-600">门课程</div>
        </div>
        <div class="bg-green-50 rounded-lg p-4 text-center">
          <div class="text-2xl font-bold text-green-600">{{ selectedSemester?.studentCount }}</div>
          <div class="text-sm text-gray-600">名学生</div>
        </div>
        <div class="bg-orange-50 rounded-lg p-4 text-center">
          <div class="text-2xl font-bold text-orange-600">{{ selectedSemester?.taskCount }}</div>
          <div class="text-sm text-gray-600">个任务</div>
        </div>
        <div class="bg-purple-50 rounded-lg p-4 text-center">
          <div class="text-2xl font-bold text-purple-600">{{ selectedSemester?.completedCount }}</div>
          <div class="text-sm text-gray-600">已分配教师</div>
        </div>
      </div>

      <TaskList :plan-id="planId" :major-id="majorId" :selected-semester="selectedSemester"
        :course-list="courseList" :class-list="classesList"
        @view="handleViewTask" @edit="handleEditTask" @create="handleCreateTask" />
    </div>
  </div>
</template>

<script setup lang="ts">
import TaskList from '@/pages/major-plan/components/ScheduleTaskList.vue';

// Props
interface Props {
  selectedSemester: any;
  loading?: boolean;
  pagination: any;
  planId: string;
  majorId: string;
  courseList: any[];
  classesList: any[];
}

const props = withDefaults(defineProps<Props>(), {
  courseList: () => [],
  classesList: () => [],
  loading: false
});

console.log('ScheduleCourseManagement props:', props);
console.log('courseList:', props.courseList);
console.log('classesList:', props.classesList);

// Events
interface Emits {
  (e: 'back'): void;
  (e: 'view-task', task: any): void;
  (e: 'edit-task', task: any): void;
  (e: 'create-task'): void;
}

const emit = defineEmits<Emits>();

// Methods
const handleBack = () => {
  emit('back');
};

// 教学任务相关方法
const handleViewTask = (task: any) => {
  emit('view-task', task);
};

const handleEditTask = (task: any) => {
  emit('edit-task', task);
};

const handleCreateTask = () => {
  emit('create-task');
};
</script>

<style scoped>
/* 使用 Tailwind CSS，无需自定义样式 */
</style>

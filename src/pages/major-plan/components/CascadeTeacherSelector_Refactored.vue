<template>
  <div class="cascade-teacher-selector">
    <!-- 级联筛选模式 -->
    <div v-if="!useTeacherManagement" class="grid grid-cols-1 md:grid-cols-2 gap-4">
      <!-- 院系选择 -->
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-2">
          <span v-if="required" class="text-red-500">*</span> 选择院系
        </label>
        <t-select
          v-model="selectedAcademyId"
          :options="academyOptions"
          placeholder="请先选择院系"
          clearable
          filterable
          @change="handleAcademyChange"
        />
      </div>

      <!-- 教师选择 -->
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-2">
          <span v-if="required" class="text-red-500">*</span> 选择教师
        </label>
        <t-select
          v-model="selectedTeacherId"
          :options="teacherOptions"
          :placeholder="selectedAcademyId ? (multiple ? '请选择教师（可多选）' : '请选择教师') : '请先选择院系'"
          :disabled="!selectedAcademyId"
          clearable
          filterable
          :loading="teacherLoading"
          :multiple="multiple"
          @change="handleTeacherChange"
        />
      </div>
    </div>

    <!-- 选中教师信息预览 -->
    <div v-if="!useTeacherManagement && showPreview && selectedTeacherInfo" class="mt-4 p-4 bg-blue-50 rounded-lg">
      <h4 class="text-sm font-medium text-gray-700 mb-2">选中教师信息</h4>
      <div class="grid grid-cols-2 gap-4 text-sm">
        <div>
          <span class="text-gray-600">姓名：</span>
          <span class="font-medium">{{ formatTeacherDisplayName(selectedTeacherInfo) }}</span>
        </div>
        <div>
          <span class="text-gray-600">职称：</span>
          <span>{{ selectedTeacherInfo.title || selectedTeacherInfo.teacher_title || '未知' }}</span>
        </div>
        <div>
          <span class="text-gray-600">学院：</span>
          <span>{{ selectedTeacherInfo.academy?.academyName || selectedTeacherInfo.academy_name || '未知' }}</span>
        </div>
        <div>
          <span class="text-gray-600">工号：</span>
          <span>{{ selectedTeacherInfo.number || selectedTeacherInfo.teacher_number || '未知' }}</span>
        </div>
      </div>
    </div>

    <!-- 教师管理模式 -->
    <div v-if="useTeacherManagement" class="teacher-management">
      <!-- 标题和添加按钮 -->
      <div class="flex items-center justify-between mb-4">
        <h4 class="text-sm font-medium text-gray-700">教师管理 ({{ teacherList.length }})</h4>
        <t-button
          theme="primary"
          variant="outline"
          size="small"
          @click="addTeacherRow"
        >
          <template #icon>
            <t-icon name="add" />
          </template>
          添加教师
        </t-button>
      </div>

      <!-- 教师选择行列表 -->
      <div v-if="teacherRows.length > 0" class="space-y-3">
        <div
          v-for="(row, index) in teacherRows"
          :key="row.id"
          class="flex items-center gap-3 p-3 bg-blue-50 rounded-lg teacher-row"
        >
          <!-- 院系选择 -->
          <div class="flex-1">
            <t-select
              v-model="row.selectedAcademyId"
              :options="academyOptions"
              placeholder="选择院系"
              clearable
              filterable
              size="small"
              @change="(value) => handleRowAcademyChange(index, value)"
            />
          </div>
          
          <!-- 教师选择 -->
          <div class="flex-1">
            <t-select
              v-model="row.selectedTeacherId"
              :options="row.teacherOptions"
              placeholder="选择教师"
              :disabled="!row.selectedAcademyId"
              clearable
              filterable
              size="small"
              :loading="row.loading"
              @change="(value) => handleRowTeacherChange(index, value)"
            />
          </div>

          <!-- 角色标签 -->
          <div class="w-24">
            <t-tag
              :theme="row.role === 1 ? 'primary' : 'success'"
              size="small"
            >
              {{ row.role === 1 ? '主讲教师' : '辅讲教师' }}
            </t-tag>
          </div>

          <!-- 删除按钮 -->
          <t-button
            theme="danger"
            variant="text"
            size="small"
            @click="removeTeacherRow(index)"
          >
            <template #icon>
              <t-icon name="delete" />
            </template>
          </t-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue';
import { MessagePlugin } from 'tdesign-vue-next';
import { getTeacherList, type TeacherItem } from '@/api/base/teacher';
import { getAcademyOptions, type AcademyOptionsVO } from '@/api/base/academy';

// ==================== 接口定义 ====================

interface Props {
  modelValue?: string | number | (string | number)[] | null;
  required?: boolean;
  multiple?: boolean;
  showPreview?: boolean;
  placeholder?: string;
  useTeacherManagement?: boolean;
  initialTeachers?: TeacherItemWithRole[];
}

const props = withDefaults(defineProps<Props>(), {
  required: false,
  multiple: false,
  showPreview: true,
  placeholder: '请选择教师',
  useTeacherManagement: true,
  initialTeachers: () => []
});

const emit = defineEmits<{
  'update:modelValue': [value: string | number | (string | number)[] | null];
  'change': [value: string | number | (string | number)[] | null, teacherInfo?: TeacherItem | TeacherItem[]];
  'academy-change': [academyId: string | number | null];
}>();

interface TeacherRow {
  id: string;
  selectedAcademyId: string | number | null;
  selectedTeacherId: string | number | null;
  teacherOptions: TeacherOption[];
  loading: boolean;
  role: number;
}

interface TeacherItemWithRole extends TeacherItem {
  role: number;
}

interface TeacherOption {
  label: string;
  value: string | number;
  title?: string;
  academy?: string;
}

// ==================== 响应式数据 ====================

// 级联选择模式数据
const selectedAcademyId = ref<string | number | null>(null);
const selectedTeacherId = ref<string | number | (string | number)[] | null>(null);
const academyOptions = ref<AcademyOptionsVO[]>([]);
const teachersByAcademy = ref<TeacherItem[]>([]);
const teacherLoading = ref(false);

// 教师管理模式数据
const teacherRows = ref<TeacherRow[]>([]);
const teacherList = ref<TeacherItemWithRole[]>([]);
let rowIdCounter = 0;

// ==================== 计算属性 ====================

const teacherOptions = computed(() => {
  return teachersByAcademy.value.map(teacher => ({
    label: formatTeacherDisplayName(teacher),
    value: teacher.id,
    title: teacher.title || teacher.teacher_title,
    academy: teacher.academy?.academyName || teacher.academy_name
  }));
});

const selectedTeacherInfo = computed(() => {
  if (props.multiple || !selectedTeacherId.value) return null;
  return teachersByAcademy.value.find(teacher =>
    String(teacher.id) === String(selectedTeacherId.value)
  );
});

// ==================== 工具函数 ====================

/**
 * 格式化教师显示名称
 */
const formatTeacherDisplayName = (teacher: TeacherItem): string => {
  const name = teacher.user?.name || teacher.teacher_name || `教师${teacher.id}`;
  const number = teacher.number || teacher.teacher_number;
  return number ? `${name} (${number})` : name;
};

/**
 * 标准化教师数据格式
 */
const normalizeTeacherData = (teacher: any): TeacherItemWithRole => {
  return {
    ...teacher,
    user: teacher.user || {
      name: teacher.teacher_name || teacher.teacherName || teacher.name,
      id: teacher.id
    },
    number: teacher.number || teacher.teacher_number,
    academy: teacher.academy || {
      academyId: teacher.academy_id || teacher.academyId,
      academyName: teacher.academy_name || teacher.academyName
    }
  };
};

// ==================== 数据加载 ====================

/**
 * 加载院系选项
 */
const loadAcademyOptions = async () => {
  try {
    const response = await getAcademyOptions();
    academyOptions.value = response.data || [];
  } catch (error) {
    MessagePlugin.error('加载院系选项失败');
  }
};

/**
 * 根据院系ID加载教师列表
 */
const loadTeachersByAcademy = async (academyId: string | number) => {
  teacherLoading.value = true;
  try {
    const params = {
      current: 1,
      size: 1000,
      academyId: academyId
    };
    const response = await getTeacherList(params);
    teachersByAcademy.value = response.data?.records || [];
  } catch (error) {
    MessagePlugin.error('加载教师列表失败');
    teachersByAcademy.value = [];
  } finally {
    teacherLoading.value = false;
  }
};

// ==================== 级联选择事件处理 ====================

/**
 * 院系变化处理
 */
const handleAcademyChange = (value: any) => {
  const academyId = value as string | number | null;
  emit('academy-change', academyId);

  selectedTeacherId.value = props.multiple ? [] : null;
  if (academyId) {
    loadTeachersByAcademy(academyId);
  } else {
    teachersByAcademy.value = [];
  }
};

/**
 * 教师选择变化处理
 */
const handleTeacherChange = (value: any) => {
  // 值已通过 v-model 自动更新，这里处理额外逻辑
};

// ==================== 教师管理模式功能 ====================

/**
 * 添加教师选择行
 */
const addTeacherRow = () => {
  const hasMainTeacher = teacherList.value.some(t => t.role === 1);

  const newRow: TeacherRow = {
    id: `row_${++rowIdCounter}`,
    selectedAcademyId: null,
    selectedTeacherId: null,
    teacherOptions: [],
    loading: false,
    role: hasMainTeacher ? 3 : 1
  };

  teacherRows.value.push(newRow);
};

/**
 * 删除教师选择行
 */
const removeTeacherRow = (index: number) => {
  const row = teacherRows.value[index];
  if (row && row.selectedTeacherId) {
    const teacherIndex = teacherList.value.findIndex(t => t.id === row.selectedTeacherId);
    if (teacherIndex > -1) {
      teacherList.value.splice(teacherIndex, 1);
      updateExternalValue();
    }
  }

  teacherRows.value.splice(index, 1);
};

/**
 * 教师行院系变化处理
 */
const handleRowAcademyChange = async (rowIndex: number, academyId: any) => {
  const row = teacherRows.value[rowIndex];
  if (!row) return;

  row.selectedAcademyId = academyId;
  row.selectedTeacherId = null;
  row.teacherOptions = [];

  if (academyId) {
    row.loading = true;
    try {
      const params = {
        current: 1,
        size: 1000,
        academyId: academyId
      };
      const response = await getTeacherList(params);
      const teachers = response.data?.records || [];
      row.teacherOptions = teachers.map((teacher: TeacherItem) => ({
        label: formatTeacherDisplayName(teacher),
        value: teacher.id,
        title: teacher.title || teacher.teacher_title,
        academy: teacher.academy?.academyName || teacher.academy_name
      }));
    } catch (error) {
      MessagePlugin.error('加载教师列表失败');
    } finally {
      row.loading = false;
    }
  }
};

/**
 * 教师行教师选择变化处理
 */
const handleRowTeacherChange = (rowIndex: number, teacherId: any) => {
  const row = teacherRows.value[rowIndex];
  if (!row || !teacherId) return;

  // 检查是否重复选择
  const existingTeacher = teacherList.value.find(t => t.id === teacherId);
  if (existingTeacher) {
    MessagePlugin.warning('该教师已经添加过了');
    row.selectedTeacherId = null;
    return;
  }

  // 从选项中找到教师信息并添加到列表
  const teacherOption = row.teacherOptions.find(opt => opt.value === teacherId);
  if (teacherOption) {
    const teacher: TeacherItemWithRole = {
      id: teacherId,
      role: row.role,
      user: { name: teacherOption.label.split(' (')[0], id: teacherId },
      number: teacherOption.label.match(/\(([^)]+)\)/)?.[1] || '',
      title: teacherOption.title || '',
      academy: { academyName: teacherOption.academy || '', academyId: row.selectedAcademyId || '' }
    } as TeacherItemWithRole;

    teacherList.value.push(teacher);
    updateExternalValue();

    MessagePlugin.success(`已添加${row.role === 1 ? '主讲教师' : '辅讲教师'}`);
  }
};

/**
 * 更新外部值
 */
const updateExternalValue = () => {
  const teacherIds = teacherList.value.map(t => t.id);
  emit('update:modelValue', teacherIds);
  emit('change', teacherIds, teacherList.value);
};

/**
 * 初始化教师数据
 */
const initializeTeachers = () => {
  if (props.initialTeachers && props.initialTeachers.length > 0) {
    teacherList.value = props.initialTeachers.map(normalizeTeacherData);

    props.initialTeachers.forEach((teacher) => {
      const newRow: TeacherRow = {
        id: `row_${++rowIdCounter}`,
        selectedAcademyId: teacher.academy?.academyId || teacher.academy_id || null,
        selectedTeacherId: teacher.id,
        teacherOptions: [
          {
            label: formatTeacherDisplayName(teacher),
            value: teacher.id,
            title: teacher.title || teacher.teacher_title,
            academy: teacher.academy?.academyName || teacher.academy_name
          }
        ],
        loading: false,
        role: teacher.role
      };

      teacherRows.value.push(newRow);

      // 异步加载完整的教师列表
      if (newRow.selectedAcademyId) {
        handleRowAcademyChange(teacherRows.value.length - 1, newRow.selectedAcademyId).then(() => {
          newRow.selectedTeacherId = teacher.id;
        });
      }
    });
  }
};

// ==================== 监听器 ====================

// 监听外部值变化
watch(() => props.modelValue, (newValue) => {
  selectedTeacherId.value = newValue;
}, { immediate: true });

// 监听内部值变化，同步到外部
watch(selectedTeacherId, (newValue) => {
  emit('update:modelValue', newValue);

  if (props.useTeacherManagement && props.multiple) {
    emit('change', newValue, teacherList.value);
  } else if (props.multiple && Array.isArray(newValue)) {
    const teachers = teachersByAcademy.value.filter(teacher =>
      newValue.includes(teacher.id)
    );
    emit('change', newValue, teachers);
  } else if (!props.multiple && newValue) {
    const teacher = teachersByAcademy.value.find(teacher =>
      String(teacher.id) === String(newValue)
    );
    emit('change', newValue, teacher);
  } else {
    emit('change', newValue);
  }
});

// 监听初始教师数据变化
watch(() => props.initialTeachers, () => {
  if (teacherList.value.length === 0) {
    initializeTeachers();
  }
}, { immediate: true });

// ==================== 生命周期 ====================

onMounted(() => {
  loadAcademyOptions();
});
</script>

<style scoped>
.teacher-row {
  transition: all 0.2s ease;
}

.teacher-row:hover {
  background-color: #f0f8ff;
}
</style>

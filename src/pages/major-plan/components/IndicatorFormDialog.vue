<template>
  <t-dialog :visible="visible" :header="dialogTitle" :footer="false" width="500px"
    @update:visible="$emit('update:visible', $event)">
    <t-alert v-if="currentRequirement" theme="info" class="mb-4">
      当前毕业要求：{{ currentRequirement.poTitle }}
    </t-alert>

    <div class="p-2">
      <t-form :data="formData" :rules="formRules" @submit="handleSubmit" :label-width="50">
        <t-form-item v-if="!formData.isRequirement" label="编号" name="poNumber">
          <div class="flex space-x-2">
            <t-input v-model="poNumber" placeholder="请输入编号" class="flex-1" />
            <t-button theme="primary" variant="outline" @click="onRegenerateNumber">
              重新生成
            </t-button>
          </div>
        </t-form-item>
        <t-form-item label="名称" name="poTitle">
          <t-input v-model="poTitle" placeholder="请输入指标点名称" />
        </t-form-item>
        <t-form-item label="描述" name="poDescription">
          <t-textarea v-model="poDescription" placeholder="请输入描述" :autosize="{ minRows: 5, maxRows: 10 }" />
        </t-form-item>
        <t-form-item>
          <t-space>
            <t-button theme="primary" type="submit">确认</t-button>
            <t-button theme="default" variant="base" @click="$emit('update:visible', false)">取消</t-button>
          </t-space>
        </t-form-item>
      </t-form>
    </div>
  </t-dialog>
</template>

<script setup lang="ts">
import { computed } from "vue";

interface Requirement {
  id: string;
  poTitle: string;
  poDescription: string;
}

interface FormData {
  planId: number;
  poNumber: string;
  poTitle: string;
  poDescription: string;
  isRequirement: boolean;
  parentId: string | null;
  id: number | null;
}

interface Props {
  visible: boolean;
  formData: FormData;
  isEdit: boolean;
  currentRequirement: Requirement | null;
  onRegenerateNumber?: () => void;
}

const props = defineProps<Props>();

const emit = defineEmits<{
  "update:visible": [value: boolean];
  "update:formData": [value: FormData];
  submit: [context: any];
}>();

const dialogTitle = computed(() => {
  return props.isEdit ? "编辑" : "添加";
});

const formRules = {
  poNumber: [
    { required: true, message: "请输入编号", trigger: "blur" as const },
  ],
  // poTitle: [
  //   { required: true, message: "请输入名称", trigger: "blur" as const },
  // ],
  poDescription: [
    { required: true, message: "请输入描述", trigger: "blur" as const },
  ],
};

// 使用计算属性处理双向绑定
const poNumber = computed({
  get: () => props.formData.poNumber,
  set: (value: string) => {
    const updatedFormData = { ...props.formData, poNumber: value };
    emit("update:formData", updatedFormData);
  },
});

const poTitle = computed({
  get: () => props.formData.poTitle,
  set: (value: string) => {
    const updatedFormData = { ...props.formData, poTitle: value };
    emit("update:formData", updatedFormData);
  },
});

const poDescription = computed({
  get: () => props.formData.poDescription,
  set: (value: string) => {
    const updatedFormData = { ...props.formData, poDescription: value };
    emit("update:formData", updatedFormData);
  },
});

const handleSubmit = (context: any) => {
  emit("submit", context);
};
</script>

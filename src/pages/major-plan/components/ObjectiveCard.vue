<template>
  <div class="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow duration-200">
    <div class="flex items-center justify-between mb-3">
      <t-tag theme="primary" size="medium">
        <template #icon><t-icon name="tag" class="mr-2" /></template>
        {{ objective.eoTitle }}
      </t-tag>
      <div class="flex items-center space-x-2">
        <t-button size="small" variant="text" theme="primary" @click="$emit('edit', objective)">
          <template #icon>
            <t-icon name="edit" class="mr-1" />
          </template>
          编辑
        </t-button>
        <t-button size="small" variant="text" theme="danger" @click="$emit('delete', objective)">
          <template #icon>
            <t-icon name="delete" class="mr-1" />
          </template>
          删除
        </t-button>
      </div>
    </div>

    <div class="mb-3">
      <div class="text-gray-700 text-sm leading-relaxed">
        {{ objective.eoDescription }}
      </div>
    </div>

    <div class="mb-3 flex items-center">
      <t-tag theme="success" size="small" class="mr-2">
        <template #icon>
          <t-icon name="task" class="mr-1" />
        </template>
        {{ getQuestionCount(objective?.relatedQuestionsArray) }} 题
      </t-tag>
    </div>

    <div class="text-xs text-gray-500">
      <span>最后更新: {{ objective.modifyTime || objective.createTime }}</span>
    </div>
  </div>
</template>

<script setup lang="ts">

interface Props {
  objective: any;
}

defineProps<Props>();

defineEmits<{
  edit: [objective: any];
  delete: [objective: any];
}>();

// 获取题目数量
const getQuestionCount = (relatedQuestions: any): number => {
  if (!relatedQuestions) return 0;
  return relatedQuestions.length;
};
</script>

<template>
  <div class="min-h-screen bg-gray-50">
    <!-- 顶部骨架屏 -->
    <div class="bg-white shadow-sm border-b">
      <div class="px-6 py-4">
        <div class="h-8 bg-gray-200 rounded animate-pulse w-64"></div>
      </div>
    </div>

    <!-- 主内容区域骨架屏 -->
    <div class="px-6 py-6">
      <!-- 欢迎信息骨架屏 -->
      <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
        <div class="h-5 bg-blue-200 rounded animate-pulse w-3/4"></div>
      </div>

      <!-- 统计卡片骨架屏 -->
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
        <div v-for="i in 3" :key="i" class="bg-white rounded-lg shadow-sm p-6">
          <div class="flex items-center">
            <div class="h-8 w-8 bg-gray-200 rounded-full animate-pulse mr-3"></div>
            <div class="flex-1">
              <div class="h-6 bg-gray-200 rounded animate-pulse w-1/2 mb-2"></div>
              <div class="h-4 bg-gray-200 rounded animate-pulse w-1/4"></div>
            </div>
          </div>
        </div>
      </div>

      <!-- 课程负责人管理骨架屏 -->
      <div class="bg-white rounded-lg shadow-sm p-6">
        <!-- 搜索区域骨架屏 -->
        <div class="flex items-center space-x-4 mb-4">
          <div class="h-10 bg-gray-200 rounded animate-pulse w-80"></div>
          <div class="h-10 bg-gray-200 rounded animate-pulse w-48"></div>
        </div>

        <!-- 表格骨架屏 -->
        <div class="space-y-2">
          <div v-for="i in 10" :key="i"
            class="flex items-center space-x-4 py-3 border-b border-gray-100 last:border-b-0">
            <div v-for="j in 9" :key="j" class="h-4 bg-gray-200 rounded animate-pulse flex-1"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

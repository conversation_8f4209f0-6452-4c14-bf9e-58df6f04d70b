<template>
  <div class="max-h-[70vh] overflow-y-auto px-1">
    <t-form ref="formRef" :data="formData" :rules="formRules" label-width="100px" @submit="handleSubmit"
      class="space-y-6">

      <!-- 学期信息 -->
      <div class="bg-gray-50 p-4 rounded-md">
        <div class="grid grid-cols-2 gap-6">
          <t-form-item label="授课年份">
            <t-input :value="getSemesterYear()" disabled />
          </t-form-item>

          <t-form-item label="授课学期">
            <t-input :value="getSemesterName()" disabled />
          </t-form-item>
        </div>
        <p class="text-xs text-gray-600 mt-2 flex items-center">
          <t-icon name="info-circle" class="mr-1" />
          学期信息与当前选择的学期保持一致
        </p>
      </div>
      <!-- 基本信息 -->
      <div class="grid grid-cols-2 gap-6">
        <t-form-item label="课程" name="courseId">
          <t-select
            v-model="formData.courseId"
            :options="courseOptions"
            placeholder="请选择课程"
            filterable
            :disabled="isPrefilledData"
          />
          <div v-if="isPrefilledData" class="text-xs text-gray-500 mt-1 flex items-center">
            <t-icon name="lock" class="mr-1" />
            课程信息已预设，不可修改
          </div>
        </t-form-item>

        <t-form-item label="任务名称" name="taskName">
          <t-input v-model="formData.taskName" placeholder="请输入任务名称" />
          <div v-if="isPrefilledData" class="text-xs text-gray-500 mt-1 flex items-center">
            <t-icon name="info-circle" class="mr-1" />
            默认使用课程名称，可修改
          </div>
        </t-form-item>
      </div>



      <!-- 教学安排 -->
      <div class="grid grid-cols-3 gap-4">
        <t-form-item label="授课周数" name="teachWeek">
          <t-input-number v-model="formData.teachWeek" :min="1" :max="20" />
          <!-- <div class="text-xs text-red-500 mt-1">* 必填</div> -->
        </t-form-item>

        <t-form-item label="周学时" name="weekHours">
          <t-input-number v-model="formData.weekHours" :min="1" :max="10" @change="calculateTotalHours" />
          <!-- <div class="text-xs text-gray-500 mt-1">选填</div> -->
        </t-form-item>

        <t-form-item label="总学时" name="totalHours">
          <t-input-number v-model="formData.totalHours" :min="1" />
          <!-- <div class="text-xs text-gray-500 mt-1">选填</div> -->
        </t-form-item>
      </div>

      <!-- 人员安排 -->
      <div class="space-y-4">
        <t-form-item label="授课班级" name="classIds">
          <t-select
            v-model="formData.classIds"
            :options="classOptions"
            placeholder="请选择班级"
            multiple
            filterable
            :disabled="isPrefilledData"
          />
          <div v-if="isPrefilledData" class="text-xs text-gray-500 mt-1 flex items-center">
            <t-icon name="lock" class="mr-1" />
            班级信息已预设，不可修改
          </div>
        </t-form-item>

        <!-- 教师管理 -->
        <t-form-item label="选择教师" name="teacherIds">
          <CascadeTeacherSelector
            v-model="formData.teacherIds"
            :multiple="true"
            :use-teacher-management="true"
            :required="true"
            :show-preview="false"
            :initial-teachers="formData.teacherDetails"
            placeholder="请选择教师"
            @change="handleTeachersChange"
          />
        </t-form-item>
      </div>

      <!-- 其他信息 -->
      <div class="space-y-4">
        <!-- <div class="grid grid-cols-2 gap-6">
          <t-form-item label="上课时间">
            <t-input v-model="formData.classTime" placeholder="如：周一1-2节" />
          </t-form-item>

          <t-form-item label="教室">
            <t-input v-model="formData.classroom" placeholder="请输入教室" />
          </t-form-item>
        </div> -->

        <t-form-item label="备注">
          <t-textarea v-model="formData.remark" placeholder="请输入备注信息" :maxlength="200" rows="2" />
        </t-form-item>
      </div>

    </t-form>
  </div>

  <!-- 操作按钮 -->
  <div class="flex justify-end gap-3 pt-6 mt-6">
    <t-button theme="default" @click="handleCancel">取消</t-button>
    <t-button theme="primary" type="submit" :loading="loading" @click="handleSubmit">
      {{ isEdit ? '保存修改' : '创建任务' }}
    </t-button>
  </div>
</template>

<script setup>
import { ref, reactive, watch, computed } from 'vue';
import CascadeTeacherSelector from './CascadeTeacherSelector_Refactored.vue';

// Props
const props = defineProps({
  selectedSemester: Object,
  courseOptions: Array,
  classOptions: Array,
  taskDetail: Object,
  loading: Boolean,
  isEdit: Boolean,
  majorId: String,
  planId: String
});

// 计算属性：判断是否是预填充数据（从行数据创建任务）
const isPrefilledData = computed(() => {
  //return Boolean(!props.isEdit && props.taskDetail && (props.taskDetail.courseId || props.taskDetail.classIds?.length > 0));
  return false;
});

// Emits
const emit = defineEmits(['submit', 'cancel']);

// Form ref
const formRef = ref(null);

// Form data
const formData = reactive({
  id: undefined,
  courseId: undefined,
  taskName: '',
  taskYear: getCurrentYear(),
  taskTerm: getCurrentTerm(),
  teachWeek: 16,
  weekHours: 2,
  totalHours: 32,
  classIds: [],
  teacherIds: [], // 教师ID数组
  teacherDetails: [], // 教师详细信息（含角色）
  classTime: '',
  classroom: '',
  remark: ''
});

// Form rules
const formRules = {
  courseId: [{ required: true, message: '请选择课程' }],
  taskName: [{ required: true, message: '请输入任务名称' }],
  teachWeek: [{ required: true, message: '请输入授课周数' }],
  // weekHours 和 totalHours 改为非必填
  classIds: [{ required: true, message: '请选择授课班级' }],
  teacherIds: [
    {
      required: true,
      message: '请至少选择一个教师',
      validator: (val) => Array.isArray(val) && val.length > 0
    }
  ]
};

// 获取当前年份
function getCurrentYear() {
  return getSemesterYear()
}

// 获取当前学期
function getCurrentTerm() {
  return props.selectedSemester.id
}

// 获取学期年份
function getSemesterYear() {
  return props.selectedSemester.startYear;
}

// 获取学期名称
function getSemesterName() {
  return props.selectedSemester.semester;
}

// 计算总学时
function calculateTotalHours() {
  if (formData.weekHours && formData.teachWeek) {
    formData.totalHours = formData.weekHours * formData.teachWeek;
  }
}

// 教师选择变化处理
const handleTeachersChange = (teacherIds, teacherDetails) => {
  console.log('教师选择变化:', teacherIds, teacherDetails);

  // 更新教师详细信息
  if (Array.isArray(teacherDetails)) {
    formData.teacherDetails = teacherDetails;

    // 输出教师信息到控制台，便于调试
    teacherDetails.forEach((teacher, index) => {
      const name = teacher.user?.name || teacher.teacher_name;
      const role = teacher.role === 1 ? '主讲教师' : '辅讲教师';
      console.log(`  ${index + 1}. ${name} (${role})`);
    });
  }
};

// 监听taskDetail变化，填充表单
watch(() => props.taskDetail, (newVal) => {
  if (newVal) {
    console.log('TaskForm: 接收到taskDetail:', newVal);
    console.log('TaskForm: 当前classOptions:', props.classOptions);

    // 填充基本信息
    formData.id = newVal.id;
    formData.courseId = newVal.courseId;

    // 如果是预填充数据（从行创建任务），设置任务名称为课程名称
    if (isPrefilledData.value && newVal.courseName) {
      formData.taskName = newVal.courseName;
    } else {
      formData.taskName = newVal.taskName || '';
    }

    formData.teachWeek = newVal.teachWeek || 16; // 默认16周
    formData.weekHours = newVal.weekHours || null; // 非必填，默认为空
    formData.totalHours = newVal.totalHours || null; // 非必填，默认为空
    // 处理班级ID数据
    if (newVal.classIds && Array.isArray(newVal.classIds)) {
      // 如果已经有 classIds 数组，直接使用
      formData.classIds = newVal.classIds;
    } else if (newVal.className && Array.isArray(newVal.className)) {
      // 如果班级信息在 className 数组中，提取每个班级的 id
      formData.classIds = newVal.className.map(cls => cls.id).filter(id => id !== null && id !== undefined);
    } else {
      formData.classIds = [];
    }
    console.log('TaskForm: 设置后的formData.classIds:', formData.classIds);
    console.log('TaskForm: isPrefilledData:', isPrefilledData.value);

    // 处理教师信息
    if (newVal.teachers && Array.isArray(newVal.teachers)) {
      // 提取教师ID数组
      formData.teacherIds = newVal.teachers.map(t => t.teacherId);

      // 构建教师详细信息（需要包含角色信息）
      formData.teacherDetails = newVal.teachers.map(t => ({
        id: t.teacherId,
        role: t.role,
        // 保存教师的详细信息，包括 user 对象
        teacher_name: t.teacherName || '',
        teacher_number: t.teacherNumber || '',
        number: t.teacherNumber || '',
        // 创建 user 对象以符合 TeacherItem 接口
        user: {
          name: t.teacherName || '',
          id: t.teacherId
        },
        // 创建 academy 对象
        academy: t.academy || {
          academyId: t.academyId,
          academyName: t.academyName
        }
      }));
    } else {
      formData.teacherIds = [];
      formData.teacherDetails = [];
    }



    // 其他信息
    formData.classTime = newVal.classTime || '';
    formData.classroom = newVal.classroom || '';
    formData.remark = newVal.remark || '';
  }
}, { immediate: true });

// 提交表单
function handleSubmit() {
  formRef.value.validate().then(result => {
    if (!result) return;

    // 构建教师列表（从教师详细信息中获取）
    const teachers = formData.teacherDetails.map(teacher => ({
      teacherId: teacher.id,
      role: teacher.role,
      teacherName: teacher.user?.name || teacher.teacher_name,
      teacherNumber: teacher.number || teacher.teacher_number
    }));

    // 获取主讲教师ID（用于向后兼容）
    const mainTeacher = formData.teacherDetails.find(t => t.role === 1);
    const courseLeaderId = mainTeacher ? mainTeacher.id : null;

    const taskData = {
      id: formData.id, // 编辑时需要
      courseId: formData.courseId,
      taskName: formData.taskName,
      taskNumber: 1, // 默认序号
      taskYear: getCurrentYear(),
      taskTerm: getCurrentTerm(),
      teachWeek: formData.teachWeek,
      weekHours: formData.weekHours,
      totalHours: formData.totalHours,
      courseLeaderId: courseLeaderId, // 主讲教师ID（向后兼容）
      classIds: formData.classIds,
      teachers: teachers, // 完整的教师列表（含角色）
      majorId: props.majorId,
      planId: props.planId,
      status: 0,
      classTime: formData.classTime,
      classroom: formData.classroom,
      remark: formData.remark
    };

    console.log('提交的任务数据:', taskData);
    emit('submit', taskData);
  });
}

// 取消
function handleCancel() {
  emit('cancel');
}
</script>

<template>
  <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
    <StatCard v-for="(stat, index) in instructorStats" :key="index" :title="stat.title" :value="stat.value"
      :icon="stat.icon" :color="stat.color" />
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import StatCard from './StatCard.vue';

const props = defineProps({
  stats: {
    type: Object,
    required: true,
    default: () => ({
      totalCourses: 0,
      assignedCount: 0,
      unassignedCount: 0,
    }),
  },
});

const instructorStats = computed(() => [
  {
    title: '总课程数',
    value: props.stats.totalCourses,
    icon: 'folder-open',
    color: 'blue',
  },
  {
    title: '已分配负责人',
    value: props.stats.assignedCount,
    icon: 'user-checked',
    color: 'green',
  },
  {
    title: '未分配负责人',
    value: props.stats.unassignedCount,
    icon: 'user-add',
    color: 'yellow',
  },
]);
</script>

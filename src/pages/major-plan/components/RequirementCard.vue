<template>
  <div class="border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow">
    <div class="flex justify-between items-start mb-4">
      <div class="flex-1">
        <h3 class="text-lg font-semibold text-gray-900 mb-2">
          {{ requirement.poNumber }}. {{ requirement.poTitle }}
        </h3>
      </div>
      <div class="flex space-x-2">
        <t-button theme="primary" size="small" variant="text" @click="$emit('edit-indicator', null, requirement)">
          <template #icon>
            <t-icon name="edit" />
          </template>
          编辑
        </t-button>
        <t-button theme="danger" size="small" variant="text" @click="$emit('delete-indicator', null, requirement)"
          v-if="!requirement.isRequirement">
          <template #icon>
            <t-icon name="delete" />
          </template>
          删除
        </t-button>
      </div>
    </div>

    <!-- 本专业要求描述 -->
    <div class="mb-4">
      <p class="text-gray-600 text-sm mb-4">
        {{ requirement.poDescription }}
      </p>
    </div>

    <!-- 指标点列表 -->
    <div>
      <div class="flex justify-between items-center mb-3">
        <h4 class="text-sm font-medium text-gray-700">分解指标点</h4>
        <t-button theme="primary" size="small" variant="dashed" @click="$emit('add-indicator', requirement)">
          <template #icon><t-icon name="add" /></template>
          添加指标点
        </t-button>
      </div>

      <div v-if="requirement.children && requirement.children.length > 0" class="space-y-3">
        <div v-for="indicator in requirement.children" :key="indicator.id"
          class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
          <div class="flex-1">
            <h2 class="font-semibold text-gray-900 text-base leading-6">
              {{ requirement.poNumber }}.{{ indicator.poNumber }}
              {{ indicator.poTitle }}
            </h2>
            <p class="text-sm text-gray-600 mt-2 leading-5">
              {{ indicator.poDescription }}
            </p>
          </div>
          <div class="flex space-x-2">
            <t-button theme="primary" size="small" variant="text"
              @click="$emit('edit-indicator', requirement, indicator)">
              <template #icon>
                <t-icon name="edit" />
              </template>
              编辑
            </t-button>
            <t-button theme="danger" size="small" variant="text"
              @click="$emit('delete-indicator', requirement, indicator)">
              <template #icon>
                <t-icon name="delete" />
              </template>
              删除
            </t-button>
          </div>
        </div>
      </div>
      <div v-else class="text-center py-8 text-gray-500">暂无分解指标点</div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  requirement: any;
}

defineProps<Props>();

defineEmits<{
  "add-indicator": [requirement: any];
  "edit-indicator": [requirement: any, indicator: any];
  "delete-indicator": [requirement: any, indicator: any];
}>();
</script>

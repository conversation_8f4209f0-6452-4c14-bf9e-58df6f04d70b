<template>
  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
    <StatCard v-for="(stat, index) in stats" :key="index" :title="stat.title" :subtitle="stat.subtitle"
      :value="stat.value" :unit="stat.unit" :color="stat.color" :icon="stat.icon" />
  </div>
</template>

<script setup lang="ts">
import StatCard from './StatCard.vue';
import type { PropType } from 'vue';

interface Stat {
  title: string;
  subtitle?: string;
  value: string | number;
  unit?: string;
  color?: string;
  icon: string;
}

defineProps({
  stats: {
    type: Array as PropType<Stat[]>,
    required: true,
  },
});
</script>

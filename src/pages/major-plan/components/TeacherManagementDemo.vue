<template>
  <div class="teacher-management-demo">
    <div class="max-w-4xl mx-auto p-6">
      <h1 class="text-2xl font-bold mb-6">教师管理功能演示</h1>
      
      <!-- 功能说明卡片 -->
      <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
        <h2 class="text-lg font-semibold text-blue-800 mb-3">功能特性</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-blue-700">
          <div>
            <h3 class="font-medium mb-2">✨ 核心功能</h3>
            <ul class="space-y-1">
              <li>• 点击"添加教师"按钮新增选择行</li>
              <li>• 级联选择：院系 → 教师</li>
              <li>• 自动角色分配和标签显示</li>
              <li>• 防重复添加机制</li>
            </ul>
          </div>
          <div>
            <h3 class="font-medium mb-2">🎯 角色管理</h3>
            <ul class="space-y-1">
              <li>• 第一个教师：<t-tag theme="primary" size="small">主讲教师</t-tag></li>
              <li>• 其他教师：<t-tag theme="success" size="small">辅讲教师</t-tag></li>
              <li>• 删除主讲教师时自动角色提升</li>
              <li>• 智能角色重分配逻辑</li>
            </ul>
          </div>
        </div>
      </div>

      <!-- 教师选择器 -->
      <div class="bg-white border border-gray-200 rounded-lg p-6 mb-6">
        <h2 class="text-lg font-semibold mb-4">教师选择器</h2>
        <CascadeTeacherSelector
          ref="teacherSelectorRef"
          v-model="selectedTeachers"
          :multiple="true"
          :use-teacher-management="true"
          @change="handleTeacherChange"
          @academy-change="handleAcademyChange"
        />
      </div>

      <!-- 数据展示区域 -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- 选中的教师ID -->
        <div class="bg-gray-50 border border-gray-200 rounded-lg p-4">
          <h3 class="font-medium mb-3">选中的教师ID</h3>
          <div class="bg-white p-3 rounded border">
            <code class="text-sm">{{ selectedTeachers || '[]' }}</code>
          </div>
        </div>

        <!-- 教师详细信息 -->
        <div class="bg-gray-50 border border-gray-200 rounded-lg p-4">
          <h3 class="font-medium mb-3">教师详细信息</h3>
          <div class="space-y-2">
            <div v-if="teacherDetails.length === 0" class="text-gray-500 text-sm">
              暂无选中的教师
            </div>
            <div
              v-for="(teacher, index) in teacherDetails"
              :key="teacher.id"
              class="bg-white p-3 rounded border flex items-center justify-between"
            >
              <div>
                <div class="font-medium">
                  {{ teacher.user?.name || teacher.teacher_name }}
                  <t-tag
                    :theme="teacher.role === 1 ? 'primary' : 'success'"
                    size="small"
                    class="ml-2"
                  >
                    {{ teacher.role === 1 ? '主讲教师' : '辅讲教师' }}
                  </t-tag>
                </div>
                <div class="text-sm text-gray-500 mt-1">
                  工号: {{ teacher.number || teacher.teacher_number }} | 
                  职称: {{ teacher.title || teacher.teacher_title || '未设置' }}
                </div>
              </div>
              <div class="text-xs text-gray-400">
                #{{ index + 1 }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="mt-6 flex flex-wrap gap-3">
        <t-button theme="primary" @click="logCurrentState">
          <template #icon>
            <t-icon name="view-list" />
          </template>
          查看当前状态
        </t-button>
        <t-button theme="default" @click="resetSelector">
          <template #icon>
            <t-icon name="refresh" />
          </template>
          重置选择器
        </t-button>
        <t-button theme="warning" @click="simulateApiCall">
          <template #icon>
            <t-icon name="api" />
          </template>
          模拟API调用
        </t-button>
      </div>

      <!-- 日志区域 -->
      <div class="mt-6 bg-gray-900 text-green-400 rounded-lg p-4">
        <h3 class="font-medium mb-3 text-white">操作日志</h3>
        <div class="text-sm font-mono max-h-40 overflow-y-auto">
          <div v-if="logs.length === 0" class="text-gray-500">
            暂无日志记录...
          </div>
          <div v-for="(log, index) in logs" :key="index" class="mb-1">
            <span class="text-gray-400">[{{ log.time }}]</span>
            <span class="ml-2">{{ log.message }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { MessagePlugin } from 'tdesign-vue-next';
import CascadeTeacherSelector from './CascadeTeacherSelector.vue';
import type { TeacherItem } from '@/api/base/teacher';

// 扩展的教师项接口
interface TeacherItemWithRole extends TeacherItem {
  role: number; // 1:主讲教师 3:辅讲教师
}

// 响应式数据
const selectedTeachers = ref<(string | number)[]>([]);
const teacherDetails = ref<TeacherItemWithRole[]>([]);
const logs = ref<Array<{ time: string; message: string }>>([]);

// 组件引用
const teacherSelectorRef = ref();

// 添加日志
const addLog = (message: string) => {
  const time = new Date().toLocaleTimeString();
  logs.value.unshift({ time, message });
  if (logs.value.length > 50) {
    logs.value = logs.value.slice(0, 50);
  }
};

// 事件处理
const handleTeacherChange = (value: any, teacherInfo?: TeacherItem | TeacherItem[]) => {
  addLog(`教师选择变化: IDs=${JSON.stringify(value)}`);
  if (Array.isArray(teacherInfo)) {
    teacherDetails.value = teacherInfo as TeacherItemWithRole[];
    addLog(`更新教师详情: ${teacherInfo.length} 个教师`);
    teacherInfo.forEach((teacher, index) => {
      const name = teacher.user?.name || teacher.teacher_name;
      const role = teacher.role === 1 ? '主讲教师' : '辅讲教师';
      addLog(`  ${index + 1}. ${name} (${role})`);
    });
  }
};

const handleAcademyChange = (academyId: string | number | null) => {
  addLog(`院系选择变化: ${academyId || '未选择'}`);
};

// 操作方法
const logCurrentState = () => {
  addLog('=== 当前状态 ===');
  addLog(`选中教师数量: ${selectedTeachers.value.length}`);
  addLog(`教师详情数量: ${teacherDetails.value.length}`);
  console.log('当前状态:', {
    selectedTeachers: selectedTeachers.value,
    teacherDetails: teacherDetails.value
  });
  MessagePlugin.success('状态已输出到控制台');
};

const resetSelector = () => {
  if (teacherSelectorRef.value) {
    teacherSelectorRef.value.reset();
    addLog('选择器已重置');
    MessagePlugin.success('选择器已重置');
  }
};

const simulateApiCall = () => {
  addLog('模拟API调用...');
  // 模拟API调用逻辑
  setTimeout(() => {
    const data = {
      teacherIds: selectedTeachers.value,
      teachers: teacherDetails.value.map(t => ({
        id: t.id,
        name: t.user?.name || t.teacher_name,
        role: t.role,
        roleText: t.role === 1 ? '主讲教师' : '辅讲教师'
      }))
    };
    addLog(`API调用完成: ${JSON.stringify(data)}`);
    MessagePlugin.success('API调用模拟完成');
  }, 1000);
};

// 初始化日志
addLog('教师管理功能演示页面已加载');
</script>

<style scoped>
.teacher-management-demo {
  min-height: 100vh;
  background-color: #f5f7fa;
}
</style>

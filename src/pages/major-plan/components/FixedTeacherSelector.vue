<template>
  <div class="teacher-selector">
    <!-- 教师管理区域 -->
    <div class="mb-4">
      <!-- 添加教师按钮 -->
      <div class="flex items-center justify-between mb-4">
        <h4 class="text-sm font-medium text-gray-700">教师管理 ({{ teacherList.length }})</h4>
        <div class="space-x-2">
          <t-button
            theme="warning"
            variant="outline"
            size="small"
            @click="loadTestTeachers"
          >
            加载测试数据
          </t-button>
          <t-button
            theme="primary"
            variant="outline"
            size="small"
            @click="addTeacherRow"
          >
            <template #icon>
              <t-icon name="add" />
            </template>
            添加教师
          </t-button>
        </div>
      </div>

      <!-- 教师选择行列表 -->
      <div v-if="teacherRows.length > 0" class="space-y-3">
        <div
          v-for="(row, index) in teacherRows"
          :key="row.id"
          class="flex items-center gap-3 p-3 bg-blue-50 rounded-lg teacher-row"
        >
          <!-- 院系选择 -->
          <div class="flex-1">
            <t-select
              v-model="row.selectedAcademyId"
              :options="academyOptions"
              placeholder="选择院系"
              clearable
              filterable
              size="small"
              @change="(value) => handleRowAcademyChange(index, value)"
            />
          </div>
          
          <!-- 教师选择 -->
          <div class="flex-1">
            <t-select
              v-model="row.selectedTeacherId"
              :options="row.teacherOptions"
              placeholder="选择教师"
              :disabled="!row.selectedAcademyId"
              clearable
              filterable
              size="small"
              :loading="row.loading"
              @change="(value) => handleRowTeacherChange(index, value)"
            />
          </div>

          <!-- 角色显示 -->
          <div class="w-24">
            <t-tag
              :theme="row.role === 1 ? 'primary' : 'success'"
              size="small"
              class="role-tag"
            >
              {{ row.role === 1 ? '主讲教师' : '辅讲教师' }}
            </t-tag>
          </div>

          <!-- 删除按钮 -->
          <t-button
            theme="danger"
            variant="text"
            size="small"
            @click="removeTeacherRow(index)"
          >
            <template #icon>
              <t-icon name="delete" />
            </template>
          </t-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted } from 'vue';
import { MessagePlugin } from 'tdesign-vue-next';
import { getTeacherList, type TeacherItem } from '@/api/base/teacher';
import { getAcademyOptions, type AcademyOptionsVO } from '@/api/base/academy';

// Props
interface Props {
  modelValue?: (string | number)[];
  initialTeachers?: any[];
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: () => [],
  initialTeachers: () => []
});

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: (string | number)[]];
  'change': [teacherIds: (string | number)[], teachers: any[]];
}>();

// 教师行接口
interface TeacherRow {
  id: string;
  selectedAcademyId: string | number | null;
  selectedTeacherId: string | number | null;
  teacherOptions: any[];
  loading: boolean;
  role: number;
}

// 响应式数据
const academyOptions = ref<AcademyOptionsVO[]>([]);
const teacherRows = ref<TeacherRow[]>([]);
const teacherList = ref<any[]>([]);
let rowIdCounter = 0;

// 获取教师显示名称的辅助函数
const getTeacherDisplayName = (teacher: any) => {
  // 根据 TeacherItem 接口，教师姓名在 user.name 字段
  const name = teacher.user?.name ||
               teacher.teacher_name ||
               teacher.teacherName ||
               teacher.name ||
               `教师${teacher.id}`; // 如果没有名称，显示教师ID

  // 根据 TeacherItem 接口，工号在 number 字段
  const number = teacher.number ||
                 teacher.teacher_number ||
                 teacher.teacherNumber;

  console.log('getTeacherDisplayName - teacher:', teacher);
  console.log('getTeacherDisplayName - name:', name, 'number:', number);

  return number ? `${name} (${number})` : name;
};

// 添加教师行
const addTeacherRow = () => {
  const hasMainTeacher = teacherList.value.some(t => t.role === 1);
  
  const newRow: TeacherRow = {
    id: `row_${++rowIdCounter}`,
    selectedAcademyId: null,
    selectedTeacherId: null,
    teacherOptions: [],
    loading: false,
    role: hasMainTeacher ? 3 : 1
  };
  
  teacherRows.value.push(newRow);
};

// 删除教师行
const removeTeacherRow = (index: number) => {
  const row = teacherRows.value[index];
  if (row && row.selectedTeacherId) {
    // 从教师列表中移除
    const teacherIndex = teacherList.value.findIndex(t => t.id === row.selectedTeacherId);
    if (teacherIndex > -1) {
      teacherList.value.splice(teacherIndex, 1);
      updateExternalValue();
    }
  }
  
  teacherRows.value.splice(index, 1);
};

// 院系变化处理
const handleRowAcademyChange = async (rowIndex: number, academyId: any) => {
  const row = teacherRows.value[rowIndex];
  if (!row) return;

  row.selectedAcademyId = academyId;
  row.selectedTeacherId = null;
  row.teacherOptions = [];

  if (academyId) {
    row.loading = true;
    try {
      const response = await getTeacherList({
        current: 1,
        size: 1000,
        academyId: academyId
      });
      
      const teachers = response.data?.records || [];
      row.teacherOptions = teachers.map((teacher: TeacherItem) => ({
        label: `${teacher.user?.name || teacher.teacher_name} (${teacher.number || teacher.teacher_number})`,
        value: teacher.id
      }));
    } catch (error) {
      console.error('加载教师列表失败:', error);
      MessagePlugin.error('加载教师列表失败');
    } finally {
      row.loading = false;
    }
  }
};

// 教师选择变化处理
const handleRowTeacherChange = (rowIndex: number, teacherId: any) => {
  const row = teacherRows.value[rowIndex];
  if (!row || !teacherId) return;

  // 检查是否重复
  const existingTeacher = teacherList.value.find(t => t.id === teacherId);
  if (existingTeacher) {
    MessagePlugin.warning('该教师已经添加过了');
    row.selectedTeacherId = null;
    return;
  }

  // 添加到教师列表
  const teacher = {
    id: teacherId,
    role: row.role,
    // 这里可以添加更多教师信息
  };
  
  teacherList.value.push(teacher);
  updateExternalValue();
  
  MessagePlugin.success(`已添加${row.role === 1 ? '主讲教师' : '辅讲教师'}`);
};

// 更新外部值
const updateExternalValue = () => {
  const teacherIds = teacherList.value.map(t => t.id);
  emit('update:modelValue', teacherIds);
  emit('change', teacherIds, teacherList.value);
};

// 初始化教师数据
const initializeTeachers = () => {
  if (props.initialTeachers && props.initialTeachers.length > 0) {
    console.log('FixedTeacherSelector: 初始化教师数据:', props.initialTeachers);
    console.log('FixedTeacherSelector: 第一个教师的详细结构:', JSON.stringify(props.initialTeachers[0], null, 2));

    teacherList.value = [...props.initialTeachers];

    // 为每个教师创建选择行
    props.initialTeachers.forEach((teacher) => {
      const newRow: TeacherRow = {
        id: `row_${++rowIdCounter}`,
        selectedAcademyId: teacher.academy?.academyId || teacher.academy_id || null,
        selectedTeacherId: teacher.id,
        teacherOptions: [
          // 为当前教师创建选项，确保显示正确的名称
          {
            label: getTeacherDisplayName(teacher),
            value: teacher.id
          }
        ],
        loading: false,
        role: teacher.role
      };

      teacherRows.value.push(newRow);

      // 如果有院系信息，异步加载完整的教师列表
      if (newRow.selectedAcademyId) {
        handleRowAcademyChange(teacherRows.value.length - 1, newRow.selectedAcademyId).then(() => {
          // 确保当前选中的教师仍然被选中
          newRow.selectedTeacherId = teacher.id;
        });
      }
    });
  }
};

// 加载院系选项
const loadAcademyOptions = async () => {
  try {
    const response = await getAcademyOptions();
    academyOptions.value = response.data || [];
  } catch (error) {
    console.error('加载院系选项失败:', error);
  }
};

// 监听初始教师数据变化
watch(() => props.initialTeachers, () => {
  if (teacherList.value.length === 0) {
    initializeTeachers();
  }
}, { immediate: true });

// 测试用：创建一些模拟教师数据
const createTestTeachers = () => {
  return [
    {
      id: 1,
      role: 1,
      teacherName: '张教授',
      teacherNumber: 'T001',
      user: { name: '张教授', id: 1 },
      number: 'T001',
      academy: { academyId: 1, academyName: '计算机学院' }
    },
    {
      id: 2,
      role: 3,
      teacherName: '李老师',
      teacherNumber: 'T002',
      user: { name: '李老师', id: 2 },
      number: 'T002',
      academy: { academyId: 1, academyName: '计算机学院' }
    }
  ];
};

// 加载测试教师数据
const loadTestTeachers = () => {
  console.log('加载测试教师数据');
  const testTeachers = createTestTeachers();

  // 清空现有数据
  teacherList.value = [];
  teacherRows.value = [];

  // 设置教师列表
  teacherList.value = [...testTeachers];

  // 为每个教师创建选择行
  testTeachers.forEach((teacher) => {
    const newRow: TeacherRow = {
      id: `row_${++rowIdCounter}`,
      selectedAcademyId: teacher.academy?.academyId || null,
      selectedTeacherId: teacher.id,
      teacherOptions: [
        {
          label: getTeacherDisplayName(teacher),
          value: teacher.id
        }
      ],
      loading: false,
      role: teacher.role
    };

    teacherRows.value.push(newRow);
  });

  updateExternalValue();
  MessagePlugin.success('测试数据加载成功！');
};

// 组件挂载
onMounted(() => {
  loadAcademyOptions();
});
</script>

<style scoped>
.teacher-row {
  transition: all 0.2s ease;
}

.teacher-row:hover {
  background-color: #f0f8ff;
}

.role-tag {
  min-width: 60px;
  text-align: center;
}
</style>

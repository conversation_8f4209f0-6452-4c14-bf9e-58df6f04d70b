<template>
  <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
    <StatCard v-for="(stat, index) in requirementStats" :key="index" :title="stat.title" :value="stat.value"
      :icon="stat.icon" :color="stat.color" />
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import StatCard from './StatCard.vue';

const props = defineProps({
  totalRequirements: { type: Number, default: 0 },
  totalIndicators: { type: Number, default: 0 },
  lastUpdated: { type: Date, default: () => new Date() },
});

// 格式化时间差
const formatTimeAgo = (date: Date) => {
  if (!(date instanceof Date) || isNaN(date.getTime())) {
    return '从未';
  }
  const now = new Date();
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

  if (diffInSeconds < 60) {
    return '刚刚';
  }

  const diffInMinutes = Math.floor(diffInSeconds / 60);
  if (diffInMinutes < 60) {
    return `${diffInMinutes}分钟前`;
  }

  const diffInHours = Math.floor(diffInMinutes / 60);
  if (diffInHours < 24) {
    return `${diffInHours}小时前`;
  }

  const diffInDays = Math.floor(diffInHours / 24);
  if (diffInDays < 30) {
    return `${diffInDays}天前`;
  }

  const diffInMonths = Math.floor(diffInDays / 30);
  if (diffInMonths < 12) {
    return `${diffInMonths}个月前`;
  }

  const diffInYears = Math.floor(diffInMonths / 12);
  return `${diffInYears}年前`;
};

const requirementStats = computed(() => [
  {
    title: '总毕业要求',
    value: props.totalRequirements,
    icon: 'file',
    color: 'blue',
  },
  {
    title: '指标点总数',
    value: props.totalIndicators,
    icon: 'list',
    color: 'green',
  },
  {
    title: '最后更新',
    value: formatTimeAgo(props.lastUpdated),
    icon: 'time',
    color: 'purple',
  },
]);
</script>

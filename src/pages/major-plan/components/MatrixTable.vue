<template>
  <div class="overflow-x-auto border border-neutral-200 rounded-lg shadow-sm" style="max-height: 70vh">
    <table class="min-w-full divide-y divide-neutral-200 border-collapse">
      <thead class="bg-neutral-100">
        <tr>
          <th scope="col"
            class="sticky left-0 bg-neutral-100 z-30 px-3 py-2 text-left text-xs font-semibold text-neutral-600 uppercase tracking-wider border-r border-neutral-300"
            style="min-width: 200px">
            课程
          </th>
          <th v-for="req in requirements" :key="req.id" :colspan="req.children.length" scope="colgroup"
            class="px-3 py-2 text-center text-xs font-semibold text-neutral-600 uppercase tracking-wider border-b border-l border-neutral-300">
            GR{{ req.poNumber }}: {{ req.poTitle }}
          </th>
        </tr>
        <tr>
          <th scope="col"
            class="sticky left-0 bg-neutral-100 z-30 px-3 py-2 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider border-r border-neutral-300">
            代码 / 名称
          </th>
          <th v-for="indicator in allIndicators" :key="indicator.id" scope="col"
            class="px-2 py-2 text-center text-xs font-medium text-neutral-500 tracking-wider border-l border-neutral-300"
            :title="indicator.poDescription" style="min-width: 60px">
            {{ indicator.poNumber }} {{ indicator.poDescription }}
          </th>
        </tr>
      </thead>
      <tbody class="bg-white divide-y divide-neutral-200">
        <tr v-for="course in courses" :key="course.courseId" class="hover:bg-neutral-50 transition-colors group">
          <td
            class="sticky left-0 bg-white group-hover:bg-neutral-50 z-20 px-3 py-2 whitespace-nowrap border-r border-neutral-300">
            <div class="text-xs font-medium text-neutral-800" :title="course.courseName">
              {{ course.courseName }}
            </div>
            <div class="text-xs text-neutral-500">{{ course.courseCode }}</div>
          </td>
          <MatrixCell v-for="indicator in allIndicators" :key="indicator.id"
            :value="getRelationshipValue(course.courseId, indicator.id)" :display-info="getLevelDisplayInfo(
              getRelationshipValue(course.courseId, indicator.id),
            )
              " :score-options="scoreOptions" @update-level="
                (val) =>
                  handleSupportLevelChange(course.courseId, indicator.id, val)
              " />
        </tr>
      </tbody>
    </table>
  </div>
</template>

<script setup lang="ts">
import { PoMatrix } from "@/api/training/po-matrix";
import { computed } from "vue";
import MatrixCell from "./MatrixCell.vue";

interface Course {
  courseId: number;
  courseName: string;
  courseCode: string;
}

interface Requirement {
  id: number;
  poNumber: string;
  poTitle: string;
  children: any[]; // Define more specifically if possible
}

const props = defineProps({
  courses: { type: Array as () => Course[], required: true },
  requirements: { type: Array as () => Requirement[], required: true },
  matrixData: { type: Array as () => PoMatrix[], required: true },
});

const emit = defineEmits(["update-level"]);

const scoreOptions = [0.0, 0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1.0];

interface DisplayCategory {
  threshold: number;
  badgeClass: string;
  icon?: string;
}

const SCORE_DISPLAY_CATEGORIES: DisplayCategory[] = [
  { threshold: 0.0, badgeClass: "bg-gray-100 text-neutral-500 ", icon: "uncomfortable-1" }, // for score == 0
  { threshold: 0.3, badgeClass: "bg-orange-100 text-orange-700", icon: "calm" }, // for 0 < score <= 0.3
  { threshold: 0.7, badgeClass: "bg-blue-100 text-blue-700", icon: "smile" }, // for 0.3 < score <= 0.7
  { threshold: 1.0, badgeClass: "bg-green-100 text-green-700", icon: "happy" }, // for 0.7 < score <= 1.0
];

const allIndicators = computed(() => {
  return props.requirements.flatMap((req) => req.children || []);
});

const matrixMap = computed(() => {
  const map = new Map<string, PoMatrix>();
  props.matrixData.forEach((item) => {
    map.set(`${item.courseId}-${item.poId}`, item);
  });
  return map;
});

const getRelationship = (courseId: number, poId: number) => {
  return matrixMap.value.get(`${courseId}-${poId}`);
};
const getRelationshipValue = (courseId: number, poId: number) => {
  return getRelationship(courseId, poId)?.weight?.toFixed(1);
};

const getLevelDisplayInfo = (level: string | undefined) => {
  if (level === undefined || level === null) {
    return { isSet: false, scoreText: "-" };
  }

  const score = parseFloat(level);

  if (isNaN(score)) {
    return { isSet: false, scoreText: "-" };
  }

  let category: DisplayCategory = SCORE_DISPLAY_CATEGORIES[0];
  if (score > 0) {
    // Find from high to low
    for (let i = SCORE_DISPLAY_CATEGORIES.length - 1; i >= 1; i--) {
      const lowerBound = SCORE_DISPLAY_CATEGORIES[i - 1]?.threshold ?? 0;
      if (
        score > lowerBound &&
        score <= SCORE_DISPLAY_CATEGORIES[i].threshold
      ) {
        category = SCORE_DISPLAY_CATEGORIES[i];
        break;
      }
    }
  } else {
    // score is 0
    category = SCORE_DISPLAY_CATEGORIES[0];
  }

  return {
    isSet: true, // It is set, even if 0.
    scoreText: score.toFixed(1),
    badgeClass: category.badgeClass,
    icon: category.icon,
  };
};

const handleSupportLevelChange = (
  courseId: number,
  poId: number,
  value: any,
) => {
  emit("update-level", { courseId, poId, value });
};
</script>

<style scoped>
.sticky {
  position: sticky;
}
</style>

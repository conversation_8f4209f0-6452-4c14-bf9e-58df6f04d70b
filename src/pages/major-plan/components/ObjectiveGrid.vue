<template>
  <div class="bg-white rounded-lg shadow-sm p-6">
    <div class="flex items-center justify-between mb-6">
      <div class="flex items-center">
        <t-icon name="tag" class="text-blue-500 mr-2" />
        <h3 class="text-lg font-semibold text-gray-900">培养目标</h3>
      </div>
      <t-tag theme="default" size="small" class="ml-3">
        <template #icon><t-icon name="list" class="text-blue-500 mr-2" /></template>
        总数: {{ objectiveList.length }}
      </t-tag>
    </div>

    <!-- 培养目标网格 -->
    <div v-if="objectiveList.length > 0" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      <!-- 培养目标卡片 -->
      <ObjectiveCard v-for="objective in objectiveList" :key="objective.id" :objective="objective"
        @edit="$emit('edit', objective)" @delete="$emit('delete', objective)" />

      <!-- 添加新目标卡片 -->
      <div
        class="bg-white border-2 border-dashed border-blue-300 rounded-lg p-4 cursor-pointer hover:border-blue-400 hover:bg-blue-50 transition-colors duration-200 flex items-center justify-center"
        @click="$emit('add')">
        <div class="text-center">
          <t-icon name="add" size="24" class="text-blue-500 mb-2" />
          <div class="text-blue-600 font-medium">添加新目标</div>
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-else class="text-center py-12">
      <t-empty description="暂无培养目标">
        <template #action>
          <t-button theme="primary" @click="$emit('add')">
            <template #icon><t-icon name="add" /></template>
            添加一个目标
          </t-button>
        </template>
      </t-empty>
    </div>
  </div>
</template>

<script setup lang="ts">
import ObjectiveCard from "./ObjectiveCard.vue";

interface Props {
  objectiveList: any[];
}

defineProps<Props>();

defineEmits<{
  add: [];
  edit: [objective: any];
  delete: [objective: any];
}>();
</script>

<template>
  <div class="cascade-teacher-selector">
    <!-- 级联筛选 - 仅在非教师管理模式下显示 -->
    <div v-if="!useTeacherManagement" class="grid grid-cols-1 md:grid-cols-2 gap-4">
      <!-- 第一级：院系筛选 -->
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-2">
          <span v-if="required" class="text-red-500">*</span> 选择院系
          <span class="text-xs text-gray-500 ml-2">({{ academyOptions.length }} 个选项)</span>
        </label>
        <t-select
          v-model="selectedAcademyId"
          :options="academyOptions"
          placeholder="请先选择院系"
          clearable
          filterable
          @change="handleAcademyChange"
        />
      </div>

      <!-- 第二级：教师筛选 -->
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-2">
          <span v-if="required" class="text-red-500">*</span> 选择教师
          <span class="text-xs text-gray-500 ml-2">
            (院系ID: {{ selectedAcademyId || '未选择' }}, {{ teacherOptions.length }} 个教师)
          </span>
        </label>
        <t-select
          v-model="selectedTeacherId"
          :options="teacherOptions"
          :placeholder="selectedAcademyId ? (multiple ? '请选择教师（可多选）' : '请选择教师') : '请先选择院系'"
          :disabled="!selectedAcademyId"
          clearable
          filterable
          :loading="teacherLoading"
          :multiple="multiple"
          @change="handleTeacherChange"
        />
      </div>
    </div>

    <!-- 选中教师信息预览 - 仅在非教师管理模式下显示 -->
    <div v-if="!useTeacherManagement && showPreview && selectedTeacherInfo" class="mt-4 p-4 bg-blue-50 rounded-lg">
      <h4 class="text-sm font-medium text-gray-700 mb-2">选中教师信息</h4>
      <div class="grid grid-cols-2 gap-4 text-sm">
        <div>
          <span class="text-gray-600">姓名：</span>
          <span class="font-medium">{{ selectedTeacherInfo.user?.name || selectedTeacherInfo.teacher_name }}</span>
        </div>
        <div>
          <span class="text-gray-600">工号：</span>
          <span class="font-medium">{{ selectedTeacherInfo.number || selectedTeacherInfo.teacher_number }}</span>
        </div>
        <div>
          <span class="text-gray-600">职称：</span>
          <span class="font-medium">{{ selectedTeacherInfo.title || selectedTeacherInfo.teacher_title || '未设置' }}</span>
        </div>
        <div>
          <span class="text-gray-600">院系：</span>
          <span class="font-medium">{{ selectedTeacherInfo.academy?.academyName || selectedTeacherInfo.academy_name }}</span>
        </div>
      </div>
    </div>

    <!-- 教师管理区域 -->
    <div v-if="multiple && useTeacherManagement">
      <!-- 添加教师按钮 -->
      <div class="flex items-center justify-between mb-4">
        <h4 class="text-sm font-medium text-gray-700">教师管理 ({{ teacherList.length }})</h4>
        <t-button
          theme="primary"
          variant="outline"
          size="small"
          @click="addTeacherRow"
        >
          <template #icon>
            <t-icon name="add" />
          </template>
          添加教师
        </t-button>
      </div>

      <!-- 教师选择行列表 -->
      <div v-if="teacherRows.length > 0" class="space-y-3">
        <div
          v-for="(row, index) in teacherRows"
          :key="row.id"
          class="flex items-center gap-3 p-3 bg-blue-50 rounded-lg teacher-row"
        >
          <!-- 院系选择 -->
          <div class="flex-1">
            <t-select
              v-model="row.selectedAcademyId"
              :options="academyOptions"
              placeholder="选择院系"
              clearable
              filterable
              size="small"
              @change="(value) => handleRowAcademyChange(index, value)"
            />
          </div>

          <!-- 教师选择 -->
          <div class="flex-1">
            <t-select
              v-model="row.selectedTeacherId"
              :options="row.teacherOptions"
              placeholder="选择教师"
              :disabled="!row.selectedAcademyId"
              clearable
              filterable
              size="small"
              :loading="row.loading"
              @change="(value) => handleRowTeacherChange(index, value)"
            />
          </div>

          <!-- 角色显示 -->
          <div class="w-24">
            <t-tag
              :theme="row.role === 1 ? 'primary' : 'success'"
              size="small"
              class="role-tag"
            >
              {{ row.role === 1 ? '主讲教师' : '辅讲教师' }}
            </t-tag>
          </div>

          <!-- 删除按钮 -->
          <t-button
            theme="danger"
            variant="text"
            size="small"
            @click="removeTeacherRow(index)"
          >
            <template #icon>
              <t-icon name="delete" />
            </template>
          </t-button>
        </div>
      </div>
    </div>

    <!-- 原有的多选模式下的已选教师列表（保持向后兼容） -->
    <div v-if="multiple && selectedTeachers.length > 0 && !useTeacherManagement" class="mt-4">
      <h4 class="text-sm font-medium text-gray-700 mb-2">已选教师 ({{ selectedTeachers.length }})</h4>
      <div class="space-y-2">
        <div
          v-for="teacher in selectedTeachers"
          :key="teacher.id"
          class="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
        >
          <div class="flex items-center space-x-3">
            <div class="flex-1">
              <div class="font-medium text-gray-900">
                {{ teacher.user?.name || teacher.teacher_name }}
              </div>
              <div class="text-sm text-gray-500">
                {{ teacher.number || teacher.teacher_number }} |
                {{ teacher.title || teacher.teacher_title || '未设置' }} |
                {{ teacher.academy?.academyName || teacher.academy_name }}
              </div>
            </div>
          </div>
          <t-button
            theme="default"
            variant="text"
            size="small"
            @click="removeTeacher(teacher.id)"
          >
            <template #icon>
              <t-icon name="close" />
            </template>
          </t-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue';
import { MessagePlugin } from 'tdesign-vue-next';
import { getTeacherList, type TeacherItem } from '@/api/base/teacher';
import { getAcademyOptions, type AcademyOptionsVO } from '@/api/base/academy';

// ==================== 接口定义 ====================

interface Props {
  modelValue?: string | number | (string | number)[] | null;
  required?: boolean;
  multiple?: boolean;
  showPreview?: boolean;
  placeholder?: string;
  useTeacherManagement?: boolean;
  initialTeachers?: TeacherItemWithRole[];
}

const props = withDefaults(defineProps<Props>(), {
  required: false,
  multiple: false,
  showPreview: true,
  placeholder: '请选择教师',
  useTeacherManagement: true,
  initialTeachers: () => []
});

const emit = defineEmits<{
  'update:modelValue': [value: string | number | (string | number)[] | null];
  'change': [value: string | number | (string | number)[] | null, teacherInfo?: TeacherItem | TeacherItem[]];
  'academy-change': [academyId: string | number | null];
}>();

// 教师行接口（用于教师管理模式）
interface TeacherRow {
  id: string;
  selectedAcademyId: string | number | null;
  selectedTeacherId: string | number | null;
  teacherOptions: TeacherOption[];
  teachersByAcademy: TeacherItem[];
  loading: boolean;
  role: number; // 1:主讲教师 3:辅讲教师
}

// 扩展的教师项接口
interface TeacherItemWithRole extends TeacherItem {
  role: number;
}

// 教师选项接口
interface TeacherOption {
  label: string;
  value: string | number;
  title?: string;
  academy?: string;
}

// ==================== 响应式数据 ====================

// 级联选择模式数据
const selectedAcademyId = ref<string | number | null>(null);
const selectedTeacherId = ref<string | number | (string | number)[] | null>(null);
const academyOptions = ref<AcademyOptionsVO[]>([]);
const teachersByAcademy = ref<TeacherItem[]>([]);
const teacherLoading = ref(false);

// 教师管理模式数据
const teacherRows = ref<TeacherRow[]>([]);
const teacherList = ref<TeacherItemWithRole[]>([]);
let rowIdCounter = 0;
const isInitialized = ref(false);

// 计算属性：教师选项
const teacherOptions = computed(() => {
  return teachersByAcademy.value.map(teacher => ({
    label: `${teacher.user?.name || teacher.teacher_name} (${teacher.number || teacher.teacher_number})`,
    value: teacher.id,
    title: teacher.title || teacher.teacher_title,
    academy: teacher.academy?.academyName || teacher.academy_name
  }));
});

// 计算属性：选中的教师信息（单选模式）
const selectedTeacherInfo = computed(() => {
  if (props.multiple || !selectedTeacherId.value) return null;
  return teachersByAcademy.value.find(teacher =>
    String(teacher.id) === String(selectedTeacherId.value)
  );
});

// 计算属性：选中的教师列表（多选模式）
const selectedTeachers = computed(() => {
  if (!props.multiple || !Array.isArray(selectedTeacherId.value)) return [];
  return teachersByAcademy.value.filter(teacher =>
    (selectedTeacherId.value as (string | number)[]).includes(teacher.id)
  );
});

// 监听外部值变化
watch(() => props.modelValue, (newValue) => {
  selectedTeacherId.value = newValue;
}, { immediate: true });

// 监听初始教师数据变化
watch(() => props.initialTeachers, (newTeachers) => {
  if (newTeachers && newTeachers.length > 0 && props.useTeacherManagement && !isInitialized.value) {
    isInitialized.value = true;

    // 设置教师列表
    teacherList.value = [...newTeachers];

    // 为每个教师创建选择行
    newTeachers.forEach((teacher) => {
      const newRow: TeacherRow = {
        id: `row_${++rowIdCounter}`,
        selectedAcademyId: teacher.academy?.academyId || teacher.academy_id || null,
        selectedTeacherId: teacher.id,
        teacherOptions: [],
        teachersByAcademy: [],
        loading: false,
        role: teacher.role
      };

      teacherRows.value.push(newRow);

      // 异步加载院系教师数据，但不等待
      if (newRow.selectedAcademyId) {
        const rowIndex = teacherRows.value.length - 1;
        handleRowAcademyChange(rowIndex, newRow.selectedAcademyId).then(() => {
          // 设置选中的教师
          newRow.selectedTeacherId = teacher.id;
        });
      }
    });

    console.log('CascadeTeacherSelector: 初始化完成，teacherList.length:', teacherList.value.length);
  }
}, { immediate: true, deep: true });

// 监听内部值变化，同步到外部
watch(selectedTeacherId, (newValue) => {
  emit('update:modelValue', newValue);

  // 触发 change 事件
  if (props.useTeacherManagement && props.multiple) {
    // 使用教师管理模式时，返回带角色信息的教师列表
    emit('change', newValue, teacherList.value);
  } else if (props.multiple && Array.isArray(newValue)) {
    const teachers = teachersByAcademy.value.filter(teacher =>
      newValue.includes(teacher.id)
    );
    emit('change', newValue, teachers);
  } else if (!props.multiple && newValue) {
    const teacher = teachersByAcademy.value.find(teacher =>
      String(teacher.id) === String(newValue)
    );
    emit('change', newValue, teacher);
  } else {
    emit('change', newValue);
  }
});

// 院系变化处理
const handleAcademyChange = (value: any) => {
  const academyId = value as string | number | null;
  console.log('CascadeTeacherSelector: 院系选择变化:', academyId);

  // 发射院系变化事件
  emit('academy-change', academyId);

  selectedTeacherId.value = props.multiple ? [] : null;
  if (academyId) {
    console.log('CascadeTeacherSelector: 开始加载院系教师:', academyId);
    loadTeachersByAcademy(academyId);
  } else {
    console.log('CascadeTeacherSelector: 清空教师列表');
    teachersByAcademy.value = [];
  }
};

// 教师变化处理
const handleTeacherChange = (value: any) => {
  // 值已经通过 v-model 自动更新，这里可以添加额外逻辑
  console.log('CascadeTeacherSelector: 教师选择变化:', value);
};

// 移除教师（多选模式）
const removeTeacher = (teacherId: string | number) => {
  if (Array.isArray(selectedTeacherId.value)) {
    selectedTeacherId.value = selectedTeacherId.value.filter(id => id !== teacherId);
  }
};

// 新增教师管理方法
const addTeacherRow = () => {
  // 检查是否已有主讲教师
  const hasMainTeacher = teacherList.value.some(t => t.role === 1);

  const newRow: TeacherRow = {
    id: `row_${++rowIdCounter}`,
    selectedAcademyId: null,
    selectedTeacherId: null,
    teacherOptions: [],
    teachersByAcademy: [],
    loading: false,
    role: hasMainTeacher ? 3 : 1 // 如果没有主讲教师，新行为主讲教师，否则为辅讲教师
  };
  teacherRows.value.push(newRow);
};

const removeTeacherRow = (index: number) => {
  const row = teacherRows.value[index];
  if (row && row.selectedTeacherId) {
    // 如果该行已选择了教师，需要从教师列表中移除
    const teacherIndex = teacherList.value.findIndex(t => String(t.id) === String(row.selectedTeacherId));
    if (teacherIndex > -1) {
      const removedTeacher = teacherList.value.splice(teacherIndex, 1)[0];

      // 如果删除的是主讲教师，需要重新分配角色
      if (removedTeacher.role === 1 && teacherList.value.length > 0) {
        teacherList.value[0].role = 1; // 第一个教师变为主讲教师
        // 其他教师保持辅讲教师角色
        for (let i = 1; i < teacherList.value.length; i++) {
          teacherList.value[i].role = 3;
        }
      }

      // 更新所有待选择行的角色预设
      updateTeacherRowsRole();

      // 更新外部值
      updateExternalValue();

      MessagePlugin.success(`已移除教师：${removedTeacher.user?.name || removedTeacher.teacher_name}`);
    }
  }

  // 移除选择行
  teacherRows.value.splice(index, 1);
};

const handleRowAcademyChange = async (rowIndex: number, academyId: any) => {
  const row = teacherRows.value[rowIndex];
  if (!row) return;

  const selectedAcademyId = academyId as string | number | null;
  row.selectedAcademyId = selectedAcademyId;
  row.selectedTeacherId = null;
  row.teacherOptions = [];

  if (selectedAcademyId) {
    row.loading = true;
    try {
      const params = {
        current: 1,
        size: 1000,
        academyId: selectedAcademyId
      };
      const res = await getTeacherList(params);
      if (res && res.data && res.data.records) {
        row.teachersByAcademy = res.data.records;
        row.teacherOptions = res.data.records.map((teacher: TeacherItem) => ({
          label: `${teacher.user?.name || teacher.teacher_name} (${teacher.number || teacher.teacher_number})`,
          value: teacher.id,
          title: teacher.title || teacher.teacher_title,
          academy: teacher.academy?.academyName || teacher.academy_name
        }));
      } else {
        row.teachersByAcademy = [];
        row.teacherOptions = [];
      }
    } catch (error) {
      console.error('获取教师列表失败:', error);
      MessagePlugin.error('获取教师列表失败');
      row.teachersByAcademy = [];
      row.teacherOptions = [];
    } finally {
      row.loading = false;
    }
  } else {
    row.teachersByAcademy = [];
  }
};

const handleRowTeacherChange = (rowIndex: number, teacherId: any) => {
  const row = teacherRows.value[rowIndex];
  if (!row || !teacherId) return;

  const selectedTeacherId = teacherId as string | number;
  const teacher = row.teachersByAcademy.find(t => String(t.id) === String(selectedTeacherId));
  if (teacher) {
    // 检查是否已经添加过该教师
    const existingTeacher = teacherList.value.find(t => String(t.id) === String(selectedTeacherId));
    if (existingTeacher) {
      MessagePlugin.warning('该教师已经添加过了');
      row.selectedTeacherId = null;
      return;
    }

    // 添加教师到列表
    const teacherWithRole: TeacherItemWithRole = {
      ...teacher,
      role: row.role
    };
    teacherList.value.push(teacherWithRole);

    // 保持当前行的选择状态，不清空
    // 这样用户可以看到已选择的教师信息

    // 更新外部值
    updateExternalValue();

    MessagePlugin.success(`已添加${row.role === 1 ? '主讲教师' : '辅讲教师'}：${teacher.user?.name || teacher.teacher_name}`);
  }
};



// 更新教师选择行的角色预设
const updateTeacherRowsRole = () => {
  const hasMainTeacher = teacherList.value.some(t => t.role === 1);
  teacherRows.value.forEach(row => {
    row.role = hasMainTeacher ? 3 : 1;
  });
};

const updateExternalValue = () => {
  if (props.useTeacherManagement) {
    // 使用新的教师管理模式，返回教师ID数组
    const teacherIds = teacherList.value.map(t => t.id);
    selectedTeacherId.value = teacherIds;
  }
};

// 加载院系选项
const loadAcademyOptions = async () => {
  try {
    console.log('CascadeTeacherSelector: 开始加载院系选项...');
    const res = await getAcademyOptions();
    console.log('CascadeTeacherSelector: 院系API响应:', res);

    if (res && res.data) {
      // 处理不同的数据格式
      let academyData = res.data;
      if (Array.isArray(academyData)) {
        academyOptions.value = academyData.map(academy => ({
          label: academy.academyName || academy.name || academy.label || `院系${academy.id}`,
          value: academy.id || academy.value,
          ...academy
        }));
      } else {
        console.warn('CascadeTeacherSelector: 院系数据不是数组格式:', academyData);
        academyOptions.value = [];
      }
      console.log('CascadeTeacherSelector: 处理后的院系选项:', academyOptions.value);
    } else {
      console.warn('CascadeTeacherSelector: 院系API返回数据为空:', res);
      academyOptions.value = [];
    }
  } catch (error) {
    console.error('CascadeTeacherSelector: 加载院系数据失败:', error);
    MessagePlugin.error(`加载院系数据失败: ${error.message || '未知错误'}`);
    academyOptions.value = [];
  }
};

// 根据院系加载教师
const loadTeachersByAcademy = async (academyId: string | number) => {
  try {
    console.log('CascadeTeacherSelector: 开始加载教师列表，院系ID:', academyId);
    teacherLoading.value = true;
    const params = {
      current: 1,
      size: 1000,
      academyId: academyId
    };

    const res = await getTeacherList(params);
    console.log('CascadeTeacherSelector: 教师API响应:', res);

    if (res && res.data && res.data.records) {
      teachersByAcademy.value = res.data.records;
      console.log('CascadeTeacherSelector: 加载到教师数量:', teachersByAcademy.value.length);
    } else {
      teachersByAcademy.value = [];
      console.warn('CascadeTeacherSelector: 该院系暂无教师信息');
      MessagePlugin.warning('该院系暂无教师信息');
    }
  } catch (error) {
    console.error('CascadeTeacherSelector: 获取教师列表失败:', error);
    MessagePlugin.error('获取教师列表失败');
    teachersByAcademy.value = [];
  } finally {
    teacherLoading.value = false;
  }
};

// 组件挂载时加载院系数据
onMounted(() => {
  loadAcademyOptions();
});

// 暴露方法供父组件调用
defineExpose({
  reset: () => {
    selectedAcademyId.value = null;
    selectedTeacherId.value = props.multiple ? [] : null;
    teachersByAcademy.value = [];

    // 重置教师管理相关数据
    if (props.useTeacherManagement) {
      teacherRows.value = [];
      teacherList.value = [];
      rowIdCounter = 0;
    }
  },
  setAcademy: (academyId: string | number) => {
    selectedAcademyId.value = academyId;
    loadTeachersByAcademy(academyId);
  },
  // 获取教师列表（包含角色信息）
  getTeacherListWithRoles: () => {
    return teacherList.value;
  },
  // 设置教师列表（用于编辑模式）
  setTeacherListWithRoles: (teachers: TeacherItemWithRole[]) => {
    teacherList.value = [...teachers];
    updateExternalValue();
  }
});
</script>

<style scoped>
.cascade-teacher-selector {
  width: 100%;
}

.teacher-row {
  transition: all 0.3s ease;
}

.teacher-row:hover {
  background-color: #f8fafc;
}

.role-tag {
  min-width: 80px;
  text-align: center;
}
</style>

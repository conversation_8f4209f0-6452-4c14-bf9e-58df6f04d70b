<template>
  <t-dialog :visible="visible" :header="dialogTitle" width="calc(100vw - 500px)" top="5vh" :on-confirm="handleSubmit"
    @update:visible="(val) => $emit('update:visible', val)" destroy-on-close
    :confirm-btn="{ content: '提交', loading: submitLoading }" :cancel-btn="{ content: '取消' }" :closeOnEscKeydown="false">
    <div style="width: 98%; max-height: 75vh; overflow-y: auto">
      <t-form ref="formRef" :data="formData" :rules="rules" label-align="top">
        <t-divider align="left">基本信息</t-divider>
        <!-- 第一行：课程基本信息 -->
        <t-row :gutter="[16, 16]">
          <t-col :span="3">
            <t-form-item label="课程编码" name="courseCode">
              <t-input v-model="formData.courseCode" placeholder="请输入课程编码" />
            </t-form-item>
          </t-col>
          <t-col :span="3">
            <t-form-item label="课程名称" name="courseName">
              <t-input v-model="formData.courseName" placeholder="请输入课程名称" />
            </t-form-item>
          </t-col>
          
          <t-col :span="3">
            <t-form-item label="课程负责人" name="courseLeader">
              <t-select v-model="formData.courseLeader" :options="safeTeacherList" placeholder="请选择课程负责人" clearable
                style="width: 100%" />
            </t-form-item>
          </t-col>
          <t-col :span="3">
            <t-form-item label="上课学期" name="courseSemester">
              <t-select v-model="formData.courseSemester" :options="safeEnumOptions.semesterPeriod"
                placeholder="请选择上课学期" clearable style="width: 100%" />
            </t-form-item>
          </t-col>
        </t-row>
        <t-divider align="left">课程分类</t-divider>
        <!-- 第二行：课程属性分类 -->
        <t-row :gutter="[16, 16]">
          <t-col :span="3">
            <t-form-item label="课程类别" name="courseType1">
              <t-select v-model="formData.courseType1" :options="safeEnumOptions.courseCategory" placeholder="请选择课程类别"
                clearable style="width: 100%" />
            </t-form-item>
          </t-col>
          <t-col :span="3">
            <t-form-item label="课程性质" name="courseNature">
              <t-select v-model="formData.courseNature" :options="safeEnumOptions.courseNature" placeholder="请选择课程性质"
                clearable style="width: 100%" />
            </t-form-item>
          </t-col>
          <t-col :span="3">
            <t-form-item label="专业认证课程类型" name="courseType2">
              <t-select v-model="formData.courseType2" :options="dictOptions.engineeringEducationAccreditation"
                placeholder="请选择专业认证课程类型" clearable style="width: 100%" />
            </t-form-item>
          </t-col>
          <t-col :span="3">
            <t-form-item label="国标课程类别" name="courseType3">
              <t-select v-model="formData.courseType3" :options="dictOptions.nationalStandardClassification"
                placeholder="请选择国标课程类别" clearable style="width: 100%" />
            </t-form-item>
          </t-col>
        </t-row>
        <t-divider align="left">课程性质</t-divider>
        <!-- 第三行：课程设置和管理 -->
        <t-row :gutter="[16, 16]">
          <t-col :span="2">
            <t-form-item label="是否核心" name="courseCore">
              <t-radio-group v-model="formData.courseCore">
                <t-radio :value="true">是</t-radio>
                <t-radio :value="false">否</t-radio>
              </t-radio-group>
            </t-form-item>
          </t-col>
          <t-col :span="2">
            <t-form-item label="是否考试" name="courseExam">
              <t-radio-group v-model="formData.courseExam">
                <t-radio :value="true">是</t-radio>
                <t-radio :value="false">否</t-radio>
              </t-radio-group>
            </t-form-item>
          </t-col>
          <t-col :span="2">
            <t-form-item label="课程版本" name="courseVersion">
              <t-input-number v-model="formData.courseVersion" placeholder="年份" :min="2000" style="width: 100%" />
            </t-form-item>
          </t-col>
          <t-col :span="3">
            <t-form-item label="学分" name="courseCredit">
              <t-input-number v-model="formData.courseCredit" :min="0" :step="0.5" placeholder="请输入学分"
                style="width: 100%" />
            </t-form-item>
          </t-col>
          <t-col :span="3" >
            <t-form-item label="总学时" name="courseHoursTotal">
              <t-input-number v-model="formData.courseHoursTotal" :min="0" placeholder="总学时" style="width: 100%" />
            </t-form-item>
          </t-col>
        </t-row>
        <t-divider align="left">学时信息</t-divider>
        <t-row :gutter="[16, 16]">
          
          <t-col :span="3">
            <t-form-item label="理教学时" name="courseHoursTheory">
              <t-input-number v-model="formData.courseHoursTheory" :min="0" placeholder="理教学时" style="width: 100%" />
            </t-form-item>
          </t-col>
          <t-col :span="3">
            <t-form-item label="实验学时" name="courseHoursExperiment">
              <t-input-number v-model="formData.courseHoursExperiment" :min="0" placeholder="实验学时"
                style="width: 100%" />
            </t-form-item>
          </t-col>
          <t-col :span="3">
            <t-form-item label="课外学时" name="courseHoursExtracurricular">
              <t-input-number v-model="formData.courseHoursExtracurricular" :min="0" placeholder="课外学时"
                style="width: 100%" />
            </t-form-item>
          </t-col>
          <t-col :span="3">
            <t-form-item label="其他学时" name="courseHoursOther">
              <t-input-number v-model="formData.courseHoursOther" :min="0" placeholder="其他学时" style="width: 100%" />
            </t-form-item>
          </t-col>
        </t-row>
        
      </t-form>
    </div>
  </t-dialog>
</template>

<script setup lang="ts">
import {
  MessagePlugin,
  type DialogInstance,
  type FormInstanceFunctions,
  type FormRule,
} from "tdesign-vue-next";
import { computed, ref, watch, onMounted } from "vue";
import { getDictOptionsByTypeTitle } from "@/utils/dictUtil";

const props = defineProps<{
  visible: boolean;
  dialogType: "add" | "edit";
  courseData: any;
  enumData: any;
  teacherList: any[];
}>();

const emit = defineEmits(["update:visible", "submit"]);

const formRef = ref<FormInstanceFunctions | null>(null);
const formData = ref<any>({});
const submitLoading = ref(false);

// 字典数据
const dictOptions = ref({
  engineeringEducationAccreditation: [] as Array<{label: string, value: string | number}>, // 工程教育认证分类
  nationalStandardClassification: [] as Array<{label: string, value: string | number}>,    // 国标分类
  courseNature: [] as Array<{label: string, value: string | number}>,                      // 课程性质
  courseCategory: [] as Array<{label: string, value: string | number}>                     // 课程类别
});

// 加载字典数据
const loadDictData = async () => {
  try {
    console.log('开始加载字典数据...');

    // 并行加载所有字典数据
    const [
      engineeringEducationAccreditation,
      nationalStandardClassification,
      courseNature,
      courseCategory
    ] = await Promise.all([
      getDictOptionsByTypeTitle('工程教育认证分类'),
      getDictOptionsByTypeTitle('国标分类'),
      getDictOptionsByTypeTitle('课程性质'),
      getDictOptionsByTypeTitle('课程类别')
    ]);

    dictOptions.value = {
      engineeringEducationAccreditation,
      nationalStandardClassification,
      courseNature,
      courseCategory
    };

    console.log('字典数据加载完成:', dictOptions.value);
  } catch (error) {
    console.error('加载字典数据失败:', error);
    MessagePlugin.error('加载字典数据失败');
  }
};

// 安全的教师列表，过滤掉 null 值
const safeTeacherList = computed(() => {
  return Array.isArray(props.teacherList)
    ? props.teacherList.filter((item: any) => item != null && item.value != null)
    : [];
});

// 安全的枚举选项（混合使用原有枚举和字典数据）
const safeEnumOptions = computed(() => {
  const defaultOptions = {
    semesterPeriod: [] as any[],
    courseCategory: [] as any[],
    courseNature: [] as any[]
  };

  if (!props.enumData) return defaultOptions;

  return {
    // 学期周期仍使用原有枚举数据
    semesterPeriod: Array.isArray(props.enumData.list?.semesterPeriod)
      ? props.enumData.list.semesterPeriod.filter((item: any) => item != null)
      : [],
    // 课程类别使用字典数据
    courseCategory: dictOptions.value.courseCategory.length > 0
      ? dictOptions.value.courseCategory
      : (Array.isArray(props.enumData.list?.courseCategory)
          ? props.enumData.list.courseCategory.filter((item: any) => item != null)
          : []),
    // 课程性质使用字典数据
    courseNature: dictOptions.value.courseNature.length > 0
      ? dictOptions.value.courseNature
      : (Array.isArray(props.enumData.list?.courseNature)
          ? props.enumData.list.courseNature.filter((item: any) => item != null)
          : [])
  };
});

// 组件挂载时加载字典数据
onMounted(() => {
  loadDictData();
});

watch(
  () => props.courseData,
  (newVal) => {
    formData.value = { ...newVal };
  },
  { immediate: true, deep: true }
);

const dialogTitle = computed(() =>
  props.dialogType === "add" ? "新增课程" : "编辑课程"
);

const rules: Record<string, FormRule[]> = {
  courseCode: [{ required: true, message: "课程编码必填", trigger: "blur" }],
  courseName: [{ required: true, message: "课程名称必填", trigger: "blur" }],
  courseCredit: [
    { required: true, message: "学分必填且为数字", trigger: "blur" },
  ],
  courseSemester: [
    { required: true, message: "上课学期必选", trigger: "change" },
  ],
  courseType1: [{ required: true, message: "课程类别必选", trigger: "change" }],
  courseNature: [
    { required: true, message: "课程性质必选", trigger: "change" },
  ],
  courseHoursTotal: [
    { required: true, message: "总学时必填", trigger: "blur" },
  ],
  courseLeader: [
    { required: true, message: "课程负责人必选", trigger: "change" },
  ],
};

const handleSubmit = async (
  context?: { e?: MouseEvent | KeyboardEvent } | DialogInstance,
) => {
  if (
    context &&
    typeof context === "object" &&
    "trigger" in context &&
    context.trigger !== "confirm"
  ) {
    return;
  }

  const form = formRef.value;
  if (!form) return;

  const valid = await form.validate();
  if (valid === true) {
    submitLoading.value = true;
    try {
      await emit("submit", formData.value, (loading: boolean) => {
        submitLoading.value = loading;
      });
    } finally {
      // The parent component is responsible for closing the dialog and stopping loading
    }
  } else {
    MessagePlugin.warning("请检查表单必填项或错误信息");
  }
};
</script>

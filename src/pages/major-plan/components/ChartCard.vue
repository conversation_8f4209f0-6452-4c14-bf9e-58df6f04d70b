<template>
  <div class="bg-gray-50 rounded-lg p-4">
    <h4 class="text-base font-medium text-gray-900 mb-4 text-center">
      {{ title }}
    </h4>

    <!-- 饼图模拟 -->
    <div v-if="type === 'pie'" class="flex justify-center">
      <div class="w-64 h-64 bg-white rounded-lg flex items-center justify-center border">
        <div class="text-center">
          <div class="w-48 h-48 mx-auto relative">
            <!-- 模拟饼图 -->
            <div class="absolute inset-0 rounded-full bg-gradient-conic from-blue-400 via-orange-400 to-gray-400"></div>
            <div class="absolute inset-4 rounded-full bg-white flex items-center justify-center">
              <div class="text-center">
                <div class="text-2xl font-bold text-blue-600">
                  {{ mainPercentage }}%
                </div>
                <div class="text-xs text-gray-500">{{ mainLabel }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 柱状图模拟 -->
    <div v-else-if="type === 'bar'" class="h-64 bg-white rounded-lg flex items-end justify-center p-4 border">
      <div class="flex items-end space-x-2 h-full">
        <div v-for="(item, index) in data.labels" :key="index" class="flex flex-col items-center space-y-1">
          <!-- 多数据集柱状图 -->
          <div v-if="data.datasets.length > 1" class="flex flex-col-reverse space-y-reverse space-y-1">
            <div v-for="(dataset, datasetIndex) in data.datasets" :key="datasetIndex" class="w-8" :style="{
              height: `${(dataset.data[index] || 0) * 3}px`,
              backgroundColor: Array.isArray(dataset.backgroundColor)
                ? dataset.backgroundColor[0]
                : dataset.backgroundColor,
            }"></div>
          </div>
          <!-- 单数据集柱状图 -->
          <div v-else class="w-12" :style="{
            height: `${(data.datasets[0]?.data[index] || 0) * 3}px`,
            backgroundColor: Array.isArray(data.datasets[0]?.backgroundColor)
              ? data.datasets[0]?.backgroundColor[0]
              : data.datasets[0]?.backgroundColor,
          }"></div>
          <span class="text-xs text-center leading-tight">{{ item }}</span>
        </div>
      </div>
    </div>

    <!-- 图例 -->
    <div class="mt-4 flex justify-center flex-wrap gap-2 text-xs">
      <div v-for="(dataset, index) in data.datasets" :key="index" class="flex items-center">
        <div class="w-3 h-3 rounded mr-1" :style="{
          backgroundColor: Array.isArray(dataset.backgroundColor)
            ? dataset.backgroundColor[0]
            : dataset.backgroundColor,
        }"></div>
        <span>{{ dataset.label || data.labels[index] }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from "vue";

interface ChartData {
  labels: string[];
  datasets: {
    label?: string;
    data: number[];
    backgroundColor: string | string[];
  }[];
}

interface Props {
  title: string;
  type: "pie" | "bar";
  data: ChartData;
}

const props = defineProps<Props>();

// 计算主要百分比（用于饼图中心显示）
const mainPercentage = computed(() => {
  if (props.type === "pie" && props.data.datasets[0]) {
    const dataset = props.data.datasets[0];
    const maxValue = Math.max(...dataset.data);
    const total = dataset.data.reduce((sum, val) => sum + val, 0);
    return Math.round((maxValue / total) * 100);
  }
  return 0;
});

// 计算主要标签
const mainLabel = computed(() => {
  if (props.type === "pie" && props.data.datasets[0]) {
    const dataset = props.data.datasets[0];
    const maxValue = Math.max(...dataset.data);
    const maxIndex = dataset.data.indexOf(maxValue);
    return props.data.labels[maxIndex];
  }
  return "";
});
</script>

<style scoped>
.bg-gradient-conic {
  background: conic-gradient(from 0deg,
      #3b82f6 0deg 144deg,
      #f97316 144deg 252deg,
      #6b7280 252deg 324deg,
      #fbbf24 324deg 360deg);
}
</style>

<template>
  <div class="task-list">
    <!-- 搜索筛选区域 -->
    <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
        <t-input v-model="queryParams.taskName" placeholder="任务名称" clearable @enter="handleSearch">
          <template #prefix-icon>
            <t-icon name="search" />
          </template>
        </t-input>

        <t-select
          v-model="queryParams.teacherId"
          :options="teacherOptions"
          placeholder="选择教师"
          clearable
          filterable
        />

        <t-select
          v-model="queryParams.classId"
          :options="classOptions"
          placeholder="选择班级"
          clearable
          filterable
        />
      </div>

      <div class="flex justify-end items-center">
        <div class="flex space-x-1">
          <t-button theme="primary" @click="handleSearch">
            <template #icon>
              <t-icon name="search" />
            </template>
            搜索
          </t-button>
          <t-button theme="default" @click="handleReset">
            <template #icon>
              <t-icon name="refresh" />
            </template>
            重置
          </t-button>
         <!-- <t-button theme="primary" @click="handleCreate">
            <template #icon>
              <t-icon name="add" />
            </template>
            新建任务
          </t-button>
          -->
          <t-button theme="warning" :disabled="selectedRowKeys.length < 2" @click="handleMergeClasses">
            <template #icon>
              <t-icon name="add" />
            </template>
            合并班级 ({{ selectedRowKeys.length }})
          </t-button>

        </div>
      </div>
    </div>

    <!-- 选中任务信息提示 -->
    <div v-if="selectedRowKeys.length > 0" class="mb-4 p-4 task-selection-info rounded-lg shadow-sm">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
          <div class="flex items-center space-x-2">
            <t-icon name="check-circle" class="text-blue-600" />
            <span class="font-medium text-blue-800">已选中 {{ selectedRowKeys.length }} 个教学任务</span>
          </div>
          <div v-if="selectedCourseInfo" class="flex items-center space-x-2">
            <t-icon name="book" class="text-purple-600" />
            <span class="text-purple-700 font-medium">{{ selectedCourseInfo.courseName }}</span>
          </div>
          <div v-if="selectedRows.length > 0" class="flex items-center space-x-2">
            <t-icon name="layers" class="text-green-600" />
            <span class="text-green-700 text-sm">
              班级：{{ selectedRows.map(row => row.classes).join('、') }}
            </span>
          </div>
        </div>
        <div class="flex items-center space-x-2">
          <span class="text-xs text-gray-500">只能选择相同课程的任务</span>
          <t-button theme="default" size="small" variant="outline" @click="clearSelection">
            <template #icon>
              <t-icon name="close" />
            </template>
            清空选择
          </t-button>
        </div>
      </div>

      <!-- 详细的选中任务列表 -->
      <div v-if="selectedRows.length > 1" class="mt-3 pt-3 border-t border-gray-200">
        <div class="text-sm text-gray-600 mb-2">选中的教学任务详情：</div>
        <div class="flex flex-wrap gap-2">
          <div v-for="(row, index) in selectedRows" :key="row.id"
               class="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm">
            {{ index + 1 }}. {{ row.classes }} ({{ row.totalStudents || 0 }}人)
          </div>
        </div>
      </div>
    </div>

    <!-- 任务列表表格 -->
    <div class="bg-white rounded-lg shadow-sm">
      <t-table
        :data="taskList"
        :columns="columns"
        :loading="loading"
        :pagination="pagination"
        row-key="id"
        stripe
        hover
        :selected-row-keys="selectedRowKeys"
        :row-class-name="getRowClassName"
        @select-change="handleSelectChange"
        @page-change="handlePageChange">
        <template #name="{ row }">
          <div class="flex items-center space-x-2">
            <div class="font-medium text-gray-900">{{ row.name }}</div>
            <!-- 合并班级任务标识 -->
            <t-tag v-if="row.isMergedClasses" theme="primary" size="small" variant="light">
              <template #icon>
                <t-icon name="layers" />
              </template>
              合并班级
            </t-tag>
          </div>
        </template>

        <!-- 授课班级列模板 -->
        <template #classes="{ row }">
          <div class="flex flex-wrap gap-1">
            <template v-if="Array.isArray(row.classes) && row.classes.length > 0">
              <!-- 新数据格式：直接使用 classes 数组 -->
              <t-tag
                v-for="(classItem, index) in row.classes"
                :key="classItem.id || index"
                theme="default"
                size="small"
                variant="light"
              >
                {{ classItem.name }}
              </t-tag>
            </template>
            <template v-else-if="Array.isArray(row.classesArray) && row.classesArray.length > 0">
              <!-- 兼容旧数据格式：classesArray -->
              <t-tag
                v-for="(classItem, index) in row.classesArray"
                :key="index"
                theme="default"
                size="small"
                variant="light"
              >
                {{ classItem.className || classItem.name || classItem }}
              </t-tag>
            </template>
            <template v-else-if="typeof row.classes === 'string' && row.classes">
              <!-- 兼容字符串格式 -->
              <t-tag
                v-for="(className, index) in row.classes.split(/[,、;]/).map((name: string) => name.trim()).filter((name: string) => name)"
                :key="index"
                theme="default"
                size="small"
                variant="light"
              >
                {{ className }}
              </t-tag>
            </template>
            <template v-else>
              <span class="text-gray-400">暂无班级</span>
            </template>
          </div>
        </template>

        <!-- 授课教师列模板 -->
        <template #teachers="{ row }">
          <div class="flex flex-wrap gap-1">
            <template v-if="Array.isArray(row.teachers) && row.teachers.length > 0">
              <!-- 新数据格式：直接使用 teachers 数组 -->
              <t-tag
                v-for="(teacher, index) in row.teachers"
                :key="teacher.teacherId || teacher.id || index"
                theme="success"
                size="small"
                variant="light"
              >
                {{ teacher.teacherName || teacher.name || teacher.userName || teacher.realName || teacher }}
              </t-tag>
            </template>
            <template v-else-if="Array.isArray(row.teachersArray) && row.teachersArray.length > 0">
              <!-- 兼容旧数据格式：teachersArray -->
              <t-tag
                v-for="(teacher, index) in row.teachersArray"
                :key="index"
                theme="success"
                size="small"
                variant="light"
              >
                {{ teacher.teacherName || teacher.name || teacher.userName || teacher.realName || teacher }}
              </t-tag>
            </template>
            <template v-else-if="typeof row.teachers === 'string' && row.teachers && row.teachers !== '暂无教师'">
              <!-- 兼容字符串格式 -->
              <t-tag
                v-for="(teacherName, index) in row.teachers.split(/[,、;]/).map((name: string) => name.trim()).filter((name: string) => name)"
                :key="index"
                theme="success"
                size="small"
                variant="light"
              >
                {{ teacherName }}
              </t-tag>
            </template>
            <template v-else>
              <span class="text-gray-400">暂无教师</span>
            </template>
          </div>
        </template>



        <template #totalStudents="{ row }">
          <div class="text-center">
            <div class="text-lg font-bold text-blue-600">{{ row.totalStudents || 0 }}</div>
            <div class="text-xs text-gray-500">名学生</div>
          </div>
        </template>

        <template #actions="{ row }">
          <t-space>
            <!-- 调试信息：显示当前行的id和taskId -->
            <!-- {{ `Debug: id=${row.id}, taskId=${row.taskId}` }} -->

            <!-- 如果id为空，只显示创建新任务 -->
            <template v-if="!row.id">
              <t-link theme="success" @click="handleCreate(row)">创建新任务</t-link>
            </template>
            <!-- 如果id不为空，显示分配教师、编辑、删除 -->
            <template v-else>
              <!-- <t-link theme="primary" @click="handleAssignTeacher(row)">分配教师</t-link> -->
              <t-link theme="primary" @click="handleEdit(row)">编辑任务</t-link>
              <t-popconfirm content="确认删除此教学任务吗？" @confirm="handleDelete(row)">
                <t-link theme="danger">删除</t-link>
              </t-popconfirm>
            </template>

            <!-- 通用操作：拆分班级（仅在有多个班级时显示） -->
            <t-link v-if="hasMultipleClasses(row)" theme="warning" @click="handleSplitClasses(row)">拆分班级</t-link>
          </t-space>
        </template>
      </t-table>
    </div>

    <!-- 创建/编辑教学任务弹窗 -->
    <t-dialog v-model:visible="dialogVisible" :title="isEdit ? '编辑教学任务' : '创建教学任务'" width="900px" :confirm-btn="null" :cancel-btn="null">
      <TaskForm 
        :selected-semester="selectedSemester" 
        :course-options="courseOptions" 
        :class-options="classOptions"
        :task-detail="currentTaskDetail" 
        :loading="formLoading" 
        :is-edit="isEdit" 
        :major-id="majorId" 
        :plan-id="planId" 
        @submit="handleFormSubmit" 
        @cancel="handleFormCancel"
      />
    </t-dialog>

    <!-- 教师分配对话框 -->
    <!-- <t-dialog v-model:visible="teacherAssignDialogVisible" title="分配教师" width="700px" :confirm-btn="null" :cancel-btn="null">
      <div class="space-y-6">

       <div class="bg-gray-50 p-4 rounded-lg">
          <label class="block text-sm font-medium text-gray-700 mb-2">任务名称</label>
          <div class="text-lg font-semibold text-gray-900">{{ teacherAssignmentData.taskName }}</div>
        </div>

  
        <CascadeTeacherSelector
          v-model="teacherAssignmentData.selectedTeacherId"
          :required="true"
          :show-preview="true"
          @change="handleTeacherSelectionChange"
          @academy-change="handleAcademySelectionChange"
        />
      </div>

      <template #footer>
        <t-space>
          <t-button theme="default" @click="handleCancelAssignTeacher">取消</t-button>
          <t-button
            theme="primary"
            @click="handleConfirmAssignTeacher"
            :disabled="!teacherAssignmentData.selectedTeacherId"
            :loading="assignLoading"
          >
            确认分配
          </t-button>
        </t-space>
      </template>
    </t-dialog> -->
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, watch } from 'vue';
import { MessagePlugin, DialogPlugin } from 'tdesign-vue-next';
import {  createTask, updateTask, deleteTask, getTaskDetail } from '@/api/major/plan';
import { getSemesterTasks } from '@/api/teaching/task';
import { getTeacherList, type TeacherItem } from '@/api/base/teacher';
import { getAcademyOptions, type AcademyOptionsVO } from '@/api/base/academy';
import TaskForm from './ScheduleTaskForm.vue';
//import CascadeTeacherSelector from './CascadeTeacherSelector.vue';

// 教师树形选择数据结构
interface TeacherTreeNode {
  label: string;
  value: string | number;
  children?: TeacherTreeNode[];
}

// 教师分配对话框数据
interface TeacherAssignmentData {
  taskId: string | number;
  taskName: string;
  selectedAcademyId: string | number | null; // 选中的院系ID
  selectedTeacherId: string | number | null; // 选中的教师ID
}

// 后端期望的教师数据结构
interface TaskTeacherDTO {
  teacherId: number;
  role: number; // 1:主讲教师 2:辅导教师 3:助教
}

// 后端期望的教学任务数据结构 (TaskWorkDTO)
interface TaskWorkDTO {
  id?: number;                    // 教学任务ID（更新时必需）
  courseId: number;               // 课程ID（必需）
  taskNumber: number;             // 课程序号（必需，正数）
  taskName: string;               // 教学任务名称（必需）
  taskYear: number;               // 授课年份（必需，如2023）
  taskTerm: number;               // 授课学期（必需，1-8）
  teachWeek: number;              // 授课周数（必需，正数）
  weekHours: number;              // 周学时（必需，正数）
  totalHours: number;             // 总学时（必需，正数）
  courseLeaderId: number;         // 课程负责人ID（必需）
  planId?: number;                // 计划ID（新增时必需）
  majorId?: number;               // 专业ID（新增时必需）
  classIds: number[];             // 关联班级ID列表（必需，非空）
  teachers: TaskTeacherDTO[];     // 关联教师信息列表（必需，非空）
  status?: number;                // 记录状态（可选）
}

// 类型定义
interface Course {
  id: string | number;
  name?: string;
  courseName?: string;
  code?: string;
  courseCode?: string;
}

interface Class {
  id: string | number;
  name?: string;
  className?: string;
  code?: string;
  classCode?: string;
}

// 教学任务数据接口（后端返回的处理好的数据）
interface TaskItem {
  id: string | number;
  name: string; // 任务名称
  courseId: string | number;
  courseName?: string;
  courseCode?: string;
  classes: string; // 授课班级（字符串格式）
  teachers: string; // 授课教师（字符串格式）
  totalStudents: number; // 学生人数
  classesArray?: any[]; // 班级数组
  teachersArray?: any[]; // 教师数组
  isMergedClasses?: boolean; // 是否为合并班级任务
  [key: string]: any; // 允许其他属性
}

// Props
const props = defineProps({
  planId: String,
  majorId: String,
  selectedSemester: Object,
  courseList: {
    type: Array,
    default: (): any[] => []
  },
  classList: {
    type: Array,
    default: (): any[] => []
  }
});

// 响应式数据
const loading = ref(false);
const taskList = ref([]);

// 表格多选相关
const selectedRowKeys = ref<(string | number)[]>([]);
const selectedRows = ref<any[]>([]);
const baseCourseId = ref<string | number | null>(null); // 基准课程ID，用于限制选择

// 院系相关数据
const academyOptions = ref<AcademyOptionsVO[]>([]);

// 教师相关数据
const allTeachers = ref<TeacherItem[]>([]);
const teachersByAcademy = ref<TeacherItem[]>([]); // 按院系筛选的教师列表
const teacherTreeData = ref<TeacherTreeNode[]>([]);

// 教师分配对话框
const teacherAssignDialogVisible = ref(false);
const teacherLoading = ref(false);
const assignLoading = ref(false);
const teacherAssignmentData = ref<TeacherAssignmentData>({
  taskId: '',
  taskName: '',
  selectedAcademyId: null,
  selectedTeacherId: null
});

// 计算属性：教师选项（保留用于筛选功能）
const teacherOptions = computed(() => {
  return allTeachers.value.map(teacher => ({
    label: formatTeacherDisplayName(teacher),
    value: teacher.id,
    title: teacher.title || teacher.teacher_title,
    academy: teacher.academy?.academyName || teacher.academy_name
  }));
});

// 格式化教师显示名称的工具函数
const formatTeacherDisplayName = (teacher: any): string => {
  // 支持多种数据结构的教师姓名字段
  const name = teacher.name || teacher.user?.name || teacher.teacher_name || teacher.teacherName || `教师${teacher.id}`;
  const number = teacher.number || teacher.teacher_number || teacher.teacherNumber;
  return number ? `${name} (${number})` : name;
};

// 教师选择变化处理
const handleTeacherSelectionChange = (value: string | number | (string | number)[] | null, teacherInfo?: TeacherItem | TeacherItem[]) => {
  console.log('教师选择变化:', value, teacherInfo);
  // 这里可以添加额外的处理逻辑
};

// 院系选择变化处理
const handleAcademySelectionChange = (academyId: string | number | null) => {
  console.log('院系选择变化:', academyId);
  teacherAssignmentData.value.selectedAcademyId = academyId;
  // 当院系变化时，清空教师选择
  teacherAssignmentData.value.selectedTeacherId = null;
};

// 选中课程信息计算属性
const selectedCourseInfo = computed(() => {
  if (selectedRows.value.length === 0) return null;
  const firstRow = selectedRows.value[0];
  return {
    courseId: firstRow.courseId,
    courseName: firstRow.courseName || firstRow.name
  };
});

// 计算属性：将 props 数据转换为表单选项格式
const courseOptions = computed(() => {
  const options = (props.courseList as Course[]).map(course => ({
    id: course.id,
    name: course.name || course.courseName || '',
    label: course.name || course.courseName || '',
    value: course.id
  }));
  console.log('课程选项计算属性更新:', options);
  return options;
});

const classOptions = computed(() => {
  const options = (props.classList as Class[]).map(cls => ({
    id: cls.id,
    name: cls.name || cls.className || '',
    label: cls.name || cls.className || '',
    value: cls.id
  }));
  console.log('班级选项计算属性更新:', options);
  return options;
});

// 弹窗相关
const dialogVisible = ref(false);
const formLoading = ref(false);
const isEdit = ref(false);
const currentTaskDetail = ref(null);



// 查询参数
const queryParams = reactive({
  current: 1,
  size: 10,
  semester: props.selectedSemester?.id,
  taskName: '',
  teacherId: undefined,
  classId: undefined,
  taskYear: undefined,
  taskTerm: undefined
});

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showJumper: true,
  showSizer: true
});

// 表格列配置
const columns = [
  {
    colKey: 'row-select',
    type: 'multiple' as const,
    width: 50,
    fixed: 'left' as const
  },
  {
    colKey: 'name',
    title: '任务名称',
    width: 200,
    fixed: 'left' as const
  },
  {
    colKey: 'classes',
    title: '授课班级',
    width: 150
  },
  {
    colKey: 'teachers',
    title: '授课教师',
    width: 150
  },
  {
    colKey: 'totalStudents',
    title: '学生人数',
    width: 100,
    align: 'center' as const
  },
  {
    colKey: 'actions',
    title: '操作',
    width: 250,
    fixed: 'right' as const
  }
];



// 方法
const loadTaskList = async () => {
  try {
    loading.value = true;

    // 使用新的API接口获取学期教学任务静态数据
    const termType = props.selectedSemester?.id;
    if (!termType) {
      console.warn('未选择学期，无法加载任务数据');
      return;
    }

    const response = await getSemesterTasks(props.planId, termType, queryParams);
    console.log('API响应数据结构:', response);

    // 处理任务列表数据，为每行添加 classIds 数组
    const processedTaskList = (response.data.data || []).map((task: any) => {
      let classIds: (string | number)[] = [];

      // 如果已经有 classIds 数组，直接使用
      if (task.classes && Array.isArray(task.classes)) {
        console.log('使用现有的 classIds:', task.classes);
        classIds = task.classes.map((cls: any) => cls.id);
      }

      return {
        ...task,
        classIds: classIds  // 确保每个任务都有 classIds 数组
      };
    });

    taskList.value = processedTaskList;
    pagination.total = response.data.total || taskList.value.length;


    console.log('处理后的任务列表数据:', taskList.value);
    console.log('分页信息:', pagination);

  } catch (error) {
    console.error('加载任务数据失败:', error);
    MessagePlugin.error('加载任务数据失败');
    taskList.value = [];
    pagination.total = 0;
  } finally {
    loading.value = false;
  }
};

const handleSearch = () => {
  queryParams.current = 1;
  pagination.current = 1;
  loadTaskList();
};

const handleReset = () => {
  Object.assign(queryParams, {
    current: 1,
    size: 10,
    semester: props.selectedSemester?.id,
    taskName: '',
    teacherId: undefined,
    classId: undefined,
    taskYear: undefined,
    taskTerm: undefined
  });
  pagination.current = 1;
  loadTaskList();
};

const handlePageChange = (pageInfo: any) => {
  queryParams.current = pageInfo.current;
  queryParams.size = pageInfo.pageSize;
  pagination.current = pageInfo.current;
  pagination.pageSize = pageInfo.pageSize;
  loadTaskList();
};

// 创建任务
const handleCreate = async (row?: any) => {
  isEdit.value = false;
  currentTaskDetail.value = null;

  // 如果传入了行数据，预填充一些信息
  if (row) {


    // 根据行数据预填充课程、班级等信息
    // 注意：classIds 已经在 loadTaskList 时处理好了
    currentTaskDetail.value = {
      courseId: row.courseId || null,
      classIds: row.classIds || [], // 直接使用已处理的 classIds
      className: row.className || [], // 传递 className 数组给表单组件
      courseName: row.name || '', // 用于设置默认任务名称
      // 设置默认值
      teachWeek: 16, // 默认16周
      weekHours: null, // 非必填
      totalHours: null, // 非必填
    };

    console.log('设置的currentTaskDetail:', currentTaskDetail.value);
  }

  // 课程和班级选项通过计算属性自动获取，无需额外加载
  dialogVisible.value = true;
};

// 编辑任务
const handleEdit = async (task: any) => {
  try {
    isEdit.value = true;
    formLoading.value = true;

    // 获取任务详情
    const response = await getTaskDetail(task.id);
    currentTaskDetail.value = {
      ...response.data,
    };
    console.log('编辑中获取到的任务详情:', currentTaskDetail.value);
    dialogVisible.value = true;
  } catch (error) {
    console.error('获取任务详情失败:', error);
    MessagePlugin.error('获取任务详情失败');
  } finally {
    formLoading.value = false;
  }
};

// 删除任务
const handleDelete = async (task: any) => {
  try {
    await deleteTask(task.id.toString());
    MessagePlugin.success(`删除任务 ${task.name} 成功`);
    loadTaskList();
  } catch (error) {
    console.error('删除任务失败:', error);
    MessagePlugin.error('删除任务失败');
  }
};

// 查看任务
const handleView = (task: any) => {
  MessagePlugin.info(`查看任务：${task.name}`);
};

// 课程选项现在通过计算属性从 props.courseList 获取，无需额外加载

// 表单提交
const handleFormSubmit = async (taskData: any) => {
  try {
    formLoading.value = true;


    // 转换为后端期望的格式
    const convertedData = convertToTaskWorkDTO(taskData);


    if (isEdit.value) {
      await updateTask(convertedData);
      MessagePlugin.success('更新教学任务成功');
    } else {
      await createTask(convertedData);
      MessagePlugin.success('创建教学任务成功');
    }

    dialogVisible.value = false;
    loadTaskList();
  } catch (error) {
    console.error('操作失败:', error);
    MessagePlugin.error(`${isEdit.value ? '更新' : '创建'}教学任务失败: ${error.message || '未知错误'}`);
  } finally {
    formLoading.value = false;
  }
};

// 表单取消
const handleFormCancel = () => {
  dialogVisible.value = false;
  currentTaskDetail.value = null;
};



// 辅助方法已移除，现在直接使用字符串字段显示





// 加载院系选项
const loadAcademyOptions = async () => {
  try {

    const res = await getAcademyOptions();


    if (res && res.data) {
      academyOptions.value = res.data.map((academy: any) => ({
        ...academy,
        label: academy.academyName || academy.name || academy.label,
        value: academy.id || academy.value
      }));

    } else {
      console.warn('ScheduleTaskList: 院系API返回数据为空:', res);
      academyOptions.value = [];
    }
  } catch (error) {
    console.error('ScheduleTaskList: 加载院系选项失败:', error);
    MessagePlugin.error('加载院系选项失败');
    academyOptions.value = [];
  }
};

// 加载教师数据
const loadTeacherData = async () => {
  try {
    // 使用大页码获取所有教师数据
    const params = {
      current: 1,
      size: 1000
    };

    const res = await getTeacherList(params);
    if (res && res.data && res.data.records) {
      allTeachers.value = res.data.records;
      buildTeacherTreeData();

    }
  } catch (error) {
    console.error('加载教师数据失败:', error);
    MessagePlugin.error('加载教师数据失败');
  }
};

// 构建教师树形数据
const buildTeacherTreeData = () => {
  const academyMap = new Map<string, TeacherTreeNode>();

  allTeachers.value.forEach(teacher => {
    const academyId = teacher.academyId?.toString() || 'unknown';
    const academyName = teacher.academy?.academyName || teacher.academy_name || '未知学院';
    const teacherName = formatTeacherDisplayName(teacher);

    if (!academyMap.has(academyId)) {
      academyMap.set(academyId, {
        label: academyName,
        value: academyId,
        children: []
      });
    }

    const academy = academyMap.get(academyId)!;
    academy.children!.push({
      label: teacherName,
      value: teacher.id
    });
  });

  teacherTreeData.value = Array.from(academyMap.values());

};


// 监听学期变化，重新加载任务数据
watch(() => props.selectedSemester, () => {
  if (props.selectedSemester?.id) {
    console.log('学期变化，重新加载任务数据');
    loadTaskList();
  }
}, { deep: true });



// 表格多选处理 - 智能选择限制
const handleSelectChange = (keys: (string | number)[], options: { selectedRowData: any[] }) => {


  // 如果清空选择，重置基准课程ID
  if (keys.length === 0) {
    selectedRowKeys.value = [];
    selectedRows.value = [];
    baseCourseId.value = null;

    return;
  }

  const newSelectedRows = options.selectedRowData;

  // 如果是第一次选择，设置基准课程ID
  if (baseCourseId.value === null && newSelectedRows.length > 0) {
    baseCourseId.value = newSelectedRows[0].courseId;

    // 第一次选择，直接更新状态
    selectedRowKeys.value = keys;
    selectedRows.value = newSelectedRows;

    return;
  }

  // 分析选择变化：找出新增和移除的选择
  const previousKeys = selectedRowKeys.value;
  const addedKeys = keys.filter(key => !previousKeys.includes(key));
  const removedKeys = previousKeys.filter(key => !keys.includes(key));



  // 如果只是移除选择，直接更新状态
  if (addedKeys.length === 0 && removedKeys.length > 0) {
    selectedRowKeys.value = keys;
    selectedRows.value = newSelectedRows;

    return;
  }

  // 检查新增的选择是否都是相同课程
  const addedRows = newSelectedRows.filter((row: any) => addedKeys.includes(row.id));
  const invalidAddedRows = addedRows.filter((row: any) => row.courseId !== baseCourseId.value);

  if (invalidAddedRows.length > 0) {
    // 有不同课程的任务被新增选中，阻止这些选择
    console.log('阻止选择不同课程的任务:', invalidAddedRows.map(row => row.name));

    // 显示警告提示
    MessagePlugin.warning(`只能选择相同课程的教学任务进行合并。当前基准课程：${getCourseName(baseCourseId.value)}`);

    // 保持原有的选择状态，不添加无效的选择
    // 注意：这里不更新 selectedRowKeys.value，让表格保持原有选择状态
    return;
  }

  // 所有新增选择都是相同课程的任务，正常更新选择状态
  selectedRowKeys.value = keys;
  selectedRows.value = newSelectedRows;

};

// 辅助函数：根据课程ID获取课程名称
const getCourseName = (courseId: string | number | null): string => {
  if (!courseId) return '未知课程';
  const course = taskList.value.find((task: any) => task.courseId === courseId);
  return course ? (course.courseName || course.name || '未知课程') : '未知课程';
};

// 数据转换函数：将前端数据转换为后端期望的 TaskWorkDTO 格式
const convertToTaskWorkDTO = (frontendData: any): TaskWorkDTO => {
  console.log('开始转换前端数据为 TaskWorkDTO 格式:', frontendData);

  // 解析班级信息
  let classIds: number[] = [];
  if (frontendData.classIds && Array.isArray(frontendData.classIds)) {
    // 如果已经是数组格式
    classIds = frontendData.classIds.map((id: any) => Number(id));
  } 

  // 解析教师信息
  let teachers: TaskTeacherDTO[] = [];
  teachers = frontendData.teachers
      .filter((teacher: any) => teacher.teacherId && teacher.teacherId !== '' && teacher.teacherId !== 0)
      .map((teacher: any) => ({
        academyId: Number(teacher.academyId),
        teacherId: Number(teacher.teacherId),
        role: Number(teacher.role || 1) // 默认为主讲教师
      }));
  // if (frontendData.teachers && Array.isArray(frontendData.teachers) && frontendData.teachers.length > 0) {
  //   // 如果已经是正确的教师数组格式且不为空
  //   teachers = frontendData.teachers
  //     .filter((teacher: any) => teacher.teacherId && teacher.teacherId !== '' && teacher.teacherId !== 0)
  //     .map((teacher: any) => ({
  //       teacherId: Number(teacher.teacherId),
  //       role: Number(teacher.role || 1) // 默认为主讲教师
  //     }));
  // } else if (frontendData.courseLeaderId && frontendData.courseLeaderId !== 0 && frontendData.courseLeaderId !== '') {
  //   // 如果只有课程负责人ID且不为空
  //   teachers.push({
  //     teacherId: Number(frontendData.courseLeaderId),
  //     role: 1 // 主讲教师
  //   });
  // } else if (frontendData.teacherId && frontendData.teacherId !== 0 && frontendData.teacherId !== '') {
  //   // 如果有教师ID字段且不为空
  //   teachers.push({
  //     teacherId: Number(frontendData.teacherId),
  //     role: 1 // 主讲教师
  //   });
  // }

  // 如果没有有效的教师信息，保持空数组（允许暂无教师分配）


  // 构建 TaskWorkDTO
  const taskWorkDTO: TaskWorkDTO = {
    id: Number(frontendData.id),
    courseId: Number(frontendData.courseId),
    taskNumber: Number(frontendData.taskNumber || 1),
    taskName: String(frontendData.taskName || frontendData.name || ''),
    taskYear: Number(props.selectedSemester?.startYear),
    taskTerm: Number(props.selectedSemester?.id),
    teachWeek: Number(frontendData.teachWeek || 16),
    weekHours: Number(frontendData.weekHours || 2),
    totalHours: Number(frontendData.totalHours || frontendData.teachWeek * frontendData.weekHours || 32),
    courseLeaderId: Number(frontendData.courseLeaderId || frontendData.teacherId || teachers[0]?.teacherId || 0),
    classIds: classIds,
    teachers: teachers,
    status: Number(frontendData.status || 0)
  };

  // 添加可选字段
  if (frontendData.id) {
    taskWorkDTO.id = Number(frontendData.id);
  }
  if (frontendData.planId || props.planId) {
    taskWorkDTO.planId = Number(frontendData.planId || props.planId);
  }
  if (frontendData.majorId || props.majorId) {
    taskWorkDTO.majorId = Number(frontendData.majorId || props.majorId);
  }

  console.log('转换后的 TaskWorkDTO:', taskWorkDTO);

  // 验证必需字段
  validateTaskWorkDTO(taskWorkDTO);

  return taskWorkDTO;
};

// 验证 TaskWorkDTO 数据的完整性
const validateTaskWorkDTO = (dto: TaskWorkDTO): void => {
  const errors: string[] = [];

  if (!dto.courseId || dto.courseId <= 0) errors.push('课程ID必须是正数');
  if (!dto.taskNumber || dto.taskNumber <= 0) errors.push('课程序号必须是正数');
  if (!dto.taskName || dto.taskName.trim() === '') errors.push('教学任务名称不能为空');
  if (!dto.taskYear || dto.taskYear <= 0) errors.push('授课年份必须是正数');
  if (!dto.taskTerm || dto.taskTerm < 1 || dto.taskTerm > 8) errors.push('授课学期必须在1-8之间');
  if (!dto.teachWeek || dto.teachWeek <= 0) errors.push('授课周数必须是正数');
  if (!dto.weekHours || dto.weekHours <= 0) errors.push('周学时必须是正数');
  if (!dto.totalHours || dto.totalHours <= 0) errors.push('总学时必须是正数');
  if (!dto.classIds || dto.classIds.length === 0) errors.push('关联班级ID列表不能为空');

  // 移除教师信息的强制验证，允许合并/拆分后的任务没有教师信息
  // 课程负责人ID允许为0（表示暂无分配）
  // 教师列表允许为空（表示暂无分配）

  if (errors.length > 0) {
    console.error('TaskWorkDTO 验证失败:', errors);
    throw new Error(`数据验证失败: ${errors.join('; ')}`);
  }
};

// 辅助函数：从学期信息获取学期编号
const getCurrentTermFromSemester = (semester: any): number => {
  if (!semester) return 1;

  // 根据学期名称或其他信息推断学期编号
  const semesterName = semester.name || semester.semesterName || '';
  if (semesterName.includes('1') || semesterName.includes('春') || semesterName.includes('上')) {
    return 1;
  } else if (semesterName.includes('2') || semesterName.includes('秋') || semesterName.includes('下')) {
    return 2;
  }

  return 1; // 默认第一学期
};


// 注意：TDesign的新版本多选功能已经通过列配置和事件处理实现
// 不再需要单独的 getRowCheckProps 函数

// 清空选择
const clearSelection = () => {
  selectedRowKeys.value = [];
  selectedRows.value = [];
  baseCourseId.value = null;
  MessagePlugin.success('已清空所有选择');
};


// 获取行样式类名
const getRowClassName = (row: any) => {
  // 移除灰色禁用效果，让所有行正常显示
  // 选择限制通过选择逻辑处理，不需要视觉禁用效果
  return '';
};

// 判断是否有多个班级
const hasMultipleClasses = (row: any): boolean => {
  // 如果明确标记为合并班级任务，直接返回 true
  if (row.isMergedClasses === true) {
    return true;
  }

  // 否则通过班级字符串判断
  const classes = row.classes || '';
  return classes.includes(',') || classes.includes('、') || classes.includes(';');
};

// 教师筛选函数
const teacherFilter = (filterWords: string, option: any): boolean => {
  return option.label.toLowerCase().includes(filterWords.toLowerCase());
};

// 分配教师
const handleAssignTeacher = (row: any) => {
  teacherAssignmentData.value = {
    taskId: row.id,
    taskName: row.name,
    selectedAcademyId: null,
    selectedTeacherId: null
  };
  // 重置教师列表
  teachersByAcademy.value = [];
  teacherAssignDialogVisible.value = true;
};

// 院系变化处理
const handleAcademyChange = async (academyId: any) => {
  console.log('院系变化:', academyId);

  // 清空教师选择
  teacherAssignmentData.value.selectedTeacherId = null;
  teachersByAcademy.value = [];

  if (!academyId) {
    return;
  }

  try {
    teacherLoading.value = true;

    // 根据院系ID获取教师列表
    const params = {
      current: 1,
      size: 1000,
      academyId: academyId
    };

    const res = await getTeacherList(params);
    if (res && res.data && res.data.records) {
      teachersByAcademy.value = res.data.records;
      console.log('获取到的教师列表:', teachersByAcademy.value);
    } else {
      teachersByAcademy.value = [];
      MessagePlugin.warning('该院系暂无教师信息');
    }
  } catch (error) {
    console.error('获取教师列表失败:', error);
    MessagePlugin.error('获取教师列表失败');
    teachersByAcademy.value = [];
  } finally {
    teacherLoading.value = false;
  }
};

// 取消分配教师
const handleCancelAssignTeacher = () => {
  teacherAssignDialogVisible.value = false;
  // 重置数据
  teacherAssignmentData.value = {
    taskId: '',
    taskName: '',
    selectedAcademyId: null,
    selectedTeacherId: null
  };
  teachersByAcademy.value = [];
};

// 确认分配教师
const handleConfirmAssignTeacher = async () => {
  // 验证必填字段
  // if (!teacherAssignmentData.value.selectedAcademyId) {
  //   MessagePlugin.warning('请先选择院系');
  //   return;
  // }

  // if (!teacherAssignmentData.value.selectedTeacherId) {
  //   MessagePlugin.warning('请选择要分配的教师');
  //   return;
  // }

  try {
    assignLoading.value = true;
    if (!teacherAssignmentData.value.selectedTeacherId) {
      MessagePlugin.warning('请选择教师');
      return;
    }

    // 获取当前任务的详细信息
    const currentTask = taskList.value.find(task => task.id === teacherAssignmentData.value.taskId);
    if (!currentTask) {
      MessagePlugin.error('找不到对应的教学任务');
      return;
    }

    console.log('当前任务信息:', currentTask);
    console.log('任务 taskId:', currentTask.taskId);
    console.log('任务 isFromDatabase:', currentTask.isFromDatabase);

    // 根据任务的数据来源和状态分成两种情况处理
    if (!currentTask.taskId || currentTask.taskId === -1 || currentTask.isFromDatabase === false) {
      // 情况1：动态生成的任务，需要创建新的教学任务
      console.log('处理动态生成任务：创建新的教学任务并分配教师');

      const createData = {
        ...currentTask,
        courseLeaderId: teacherAssignmentData.value.selectedTeacherId,
        teachers: [{
          teacherId: teacherAssignmentData.value.selectedTeacherId,
          role: 1 // 主讲教师
        }],
        // 确保标记为动态生成任务
        isFromDatabase: false
      };

      console.log('创建任务数据:', createData);

      // 转换为后端期望的格式
      const convertedCreateData = convertToTaskWorkDTO(createData);
      console.log('创建任务转换后数据:', convertedCreateData);

      await createTask(convertedCreateData);
      MessagePlugin.success('教学任务创建成功，教师分配完成');

    } else {
      // 情况2：数据库中已存在的任务，更新教师信息
      console.log('处理数据库任务：更新现有任务的教师信息');

      const updateData = {
        ...currentTask,
        id: currentTask.taskId, // 使用数据库中的任务ID
        courseLeaderId: teacherAssignmentData.value.selectedTeacherId,
        teachers: [{
          teacherId: teacherAssignmentData.value.selectedTeacherId,
          role: 1 // 主讲教师
        }],
        // 保持数据库任务标识
        isFromDatabase: true
      };

      console.log('更新任务数据:', updateData);

      // 转换为后端期望的格式
      const convertedUpdateData = convertToTaskWorkDTO(updateData);
      console.log('更新任务转换后数据:', convertedUpdateData);

      await updateTask(convertedUpdateData);
      MessagePlugin.success('教师分配成功');
    }

    // 关闭对话框并刷新数据
    handleCancelAssignTeacher(); // 使用统一的取消函数
    loadTaskList(); // 刷新表格数据

  } catch (error) {
    console.error('分配教师失败:', error);
    MessagePlugin.error(`分配教师失败: ${error.message || '未知错误'}`);
  } finally {
    assignLoading.value = false;
  }
};

// 合并班级
const handleMergeClasses = async () => {
  if (selectedRowKeys.value.length < 2) {
    MessagePlugin.warning('请至少选择两个教学任务进行合并');
    return;
  }

  // 验证选中的任务是否都是相同课程
  if (baseCourseId.value === null) {
    MessagePlugin.error('选择状态异常，请重新选择');
    return;
  }

  const courseName = getCourseName(baseCourseId.value);
  const classNames = selectedRows.value.map(row => row.classes).join('、');
  const totalStudents = selectedRows.value.reduce((sum, row) => sum + (row.totalStudents || 0), 0);

  try {
    await DialogPlugin.confirm({
      header: '确认合并班级',
      body: `确定要将以下教学任务合并为一个吗？

课程：${courseName}
班级：${classNames}
总学生数：${totalStudents}人
任务数量：${selectedRowKeys.value.length}个

⚠️ 注意：合并后的任务将清空教师信息，需要重新分配教师。`,
      confirmBtn: '确认合并',
      cancelBtn: '取消'
    });

    // 如果到达这里，说明用户点击了确认
      // 获取选中的任务数据
      const selectedTasks = selectedRows.value;
      console.log('准备合并的任务:', selectedTasks);

      // 生成统一的临时taskId（使用时间戳 + 随机数确保唯一性）
      //const unifiedTaskId = `merged_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

      // 合并班级信息
      const mergedClasses = selectedTasks.map(task => task.classes).filter(cls => cls).join(',');
      const mergedStudents = selectedTasks.reduce((sum, task) => sum + (task.totalStudents || 0), 0);

      // 创建合并后的任务数据
      const mergedTaskData = {
        // 基础任务信息（使用第一个任务作为模板）
        courseId: selectedTasks[0].courseId,
        taskName: `${selectedTasks[0].courseName || selectedTasks[0].name}`,
        taskNumber: selectedTasks[0].taskNumber || 1,
        taskYear: selectedTasks[0].taskYear || new Date().getFullYear(),
        taskTerm: selectedTasks[0].taskTerm || getCurrentTermFromSemester(props.selectedSemester),
        teachWeek: selectedTasks[0].teachWeek || 16,
        weekHours: selectedTasks[0].weekHours || 2,
        totalHours: selectedTasks[0].totalHours || 32,
        // 合并后的班级信息
        classes: mergedClasses, // 用于转换函数识别班级
        totalStudents: mergedStudents,

        // 清空教师信息，需要后续手动分配
        teachers: [] as TaskTeacherDTO[],
        courseLeaderId: 0, // 设置为0表示暂无分配

        // 计划和专业信息
        majorId: props.majorId,
        planId: props.planId,
        status: 0,

        // 标记为合并任务（用于追踪，不会传给后端）
        isMerged: true,
        originalTaskIds: selectedRowKeys.value
      };

      console.log('合并任务原始数据:', mergedTaskData);

      // 转换为后端期望的格式
      const convertedMergedData = convertToTaskWorkDTO(mergedTaskData);
      console.log('合并任务转换后数据:', convertedMergedData);

      // 调用API创建合并任务
      await createTask(convertedMergedData);
      console.log('合并任务创建成功');

      // 删除原有的单独任务
      for (const taskId of selectedRowKeys.value) {
        try {
          await deleteTask(taskId.toString());
          console.log(`删除原任务成功: ${taskId}`);
        } catch (deleteError) {
          console.error(`删除原任务失败: ${taskId}`, deleteError);
          // 继续删除其他任务，不中断流程
        }
      }

    MessagePlugin.success(`班级合并成功！合并了 ${selectedTasks.length} 个教学任务`);
    clearSelection(); // 清空选择并重置基准课程ID
    loadTaskList(); // 刷新表格数据
  } catch (error) {
    console.error('合并班级失败:', error);
    // 如果是用户取消操作，不显示错误信息
    if (error && error.message && !error.message.includes('cancel')) {
      MessagePlugin.error(`合并班级失败: ${error.message}`);
    }
  }
};

// 拆分班级
const handleSplitClasses = async (row: any) => {
  try {
    await DialogPlugin.confirm({
      header: '确认拆分班级',
      body: `确定要将教学任务"${row.name}"拆分为多个单班级任务吗？

拆分后每个班级将有独立的任务ID。

⚠️ 注意：拆分后的任务将清空教师信息，需要重新分配教师。`,
      confirmBtn: '确认拆分',
      cancelBtn: '取消'
    });

    // 如果到达这里，说明用户点击了确认
      const classes = row.classes.split(/[,、;]/).map((cls: string) => cls.trim()).filter((cls: string) => cls);
      console.log('准备拆分的班级:', classes);

      // 为每个班级创建单独的任务
      const splitTasks = [];
      for (let i = 0; i < classes.length; i++) {
        const className = classes[i];

        // 生成新的taskId（每个拆分的任务都有独立的ID）
        const newTaskId = `split_${Date.now()}_${i}_${Math.random().toString(36).substring(2, 11)}`;

        const splitTaskData = {
          // 基础任务信息
          courseId: row.courseId,
          taskName: row.name.replace('(合并)', '').replace(/\s*\(.*?\)\s*$/, ''), // 清理名称
          taskNumber: row.taskNumber || 1,
          taskYear: row.taskYear || new Date().getFullYear(),
          taskTerm: row.taskTerm || getCurrentTermFromSemester(props.selectedSemester),
          teachWeek: row.teachWeek || 16,
          weekHours: row.weekHours || 2,
          totalHours: row.totalHours || 32,
          // 单班级数据
          classes: className, // 用于转换函数识别班级
          totalStudents: Math.floor((row.totalStudents || 0) / classes.length), // 平均分配学生数

          // 清空教师信息，需要后续手动分配
          teachers: [] as TaskTeacherDTO[],
          courseLeaderId: 0, // 设置为0表示暂无分配

          // 计划和专业信息
          majorId: props.majorId,
          planId: props.planId,
          status: 0,

          // 标记为拆分任务（用于追踪，不会传给后端）
          isSplit: true,
          originalTaskId: row.id,
          splitIndex: i
        };

        splitTasks.push(splitTaskData);
        console.log(`创建拆分任务 ${i + 1}:`, splitTaskData);
      }

      // 批量创建拆分任务
      for (const taskData of splitTasks) {
        try {
          console.log(`准备创建拆分任务 ${taskData.splitIndex + 1}:`, taskData);

          // 转换为后端期望的格式
          const convertedTaskData = convertToTaskWorkDTO(taskData);
          console.log(`拆分任务 ${taskData.splitIndex + 1} 转换后数据:`, convertedTaskData);

          await createTask(convertedTaskData);
          console.log(`拆分任务创建成功: ${taskData.classes}`);
        } catch (createError) {
          console.error(`创建拆分任务失败: ${taskData.classes}`, createError);
          throw createError; // 如果任何一个创建失败，中断流程
        }
      }

      // 删除原合并任务
      await deleteTask(row.id.toString());
      console.log('原任务删除成功');

    MessagePlugin.success(`班级拆分成功！拆分为 ${classes.length} 个独立的教学任务`);
    loadTaskList(); // 刷新表格数据
  } catch (error) {
    console.error('拆分班级失败:', error);
    // 如果是用户取消操作，不显示错误信息
    if (error && error.message && !error.message.includes('cancel')) {
      MessagePlugin.error(`拆分班级失败: ${error.message}`);
    }
  }
};

// 生命周期
onMounted(() => {
  loadAcademyOptions(); // 加载院系选项
  loadTeacherData(); // 加载教师数据
  loadTaskList(); // 加载任务列表
});

// 暴露方法给父组件
defineExpose({
  refresh: loadTaskList
});
</script>

<style scoped>
/* 禁用行的样式 */
:deep(.row-disabled) {
  opacity: 0.5;
  background-color: #f5f5f5 !important;
}

:deep(.row-disabled:hover) {
  background-color: #f5f5f5 !important;
}

/* 选中任务信息提示样式 */
.task-selection-info {
  background: linear-gradient(90deg, #e3f2fd 0%, #f3e5f5 100%);
  border-left: 4px solid #2196f3;
}
</style>

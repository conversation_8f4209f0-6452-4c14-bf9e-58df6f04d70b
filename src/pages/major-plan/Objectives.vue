<template>
  <div class="min-h-screen bg-gray-50">
    <!-- 骨架屏 -->
    <div v-if="loading" class="min-h-screen bg-gray-50">
      <!-- 顶部骨架屏 -->
      <div class="bg-white shadow-sm border-b">
        <div class="px-6 py-4">
          <div class="h-8 bg-gray-200 rounded animate-pulse w-64"></div>
        </div>
      </div>

      <!-- 主内容区域骨架屏 -->
      <div class="px-6 py-6">
        <!-- 欢迎信息骨架屏 -->
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
          <div class="h-5 bg-blue-200 rounded animate-pulse"></div>
        </div>

        <!-- 培养计划与标准信息骨架屏 -->
        <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
          <div class="h-6 bg-gray-200 rounded animate-pulse mb-4 w-48"></div>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div v-for="i in 2" :key="i" class="space-y-2">
              <div class="h-4 bg-gray-200 rounded animate-pulse w-24"></div>
              <div class="h-4 bg-gray-200 rounded animate-pulse"></div>
              <div class="h-4 bg-gray-200 rounded animate-pulse w-3/4"></div>
            </div>
          </div>
        </div>

        <!-- 培养目标网格骨架屏 -->
        <div class="bg-white rounded-lg shadow-sm p-6">
          <div class="h-6 bg-gray-200 rounded animate-pulse mb-4 w-32"></div>
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div v-for="i in 6" :key="i" class="bg-gray-50 rounded-lg p-4">
              <div class="h-4 bg-gray-200 rounded animate-pulse mb-2"></div>
              <div class="h-20 bg-gray-200 rounded animate-pulse mb-2"></div>
              <div class="h-3 bg-gray-200 rounded animate-pulse w-2/3"></div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 实际内容 -->
    <div v-else>
      <!-- 顶部信息 -->
      <div class="bg-white shadow-sm border-b">
        <div class="px-6 py-4">
          <h1 class="text-2xl font-bold text-gray-900">培养目标</h1>
        </div>
      </div>

      <!-- 主内容区域 -->
      <div class="px-6 py-6">
        <!-- 培养方案欢迎信息 -->
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
          <p class="text-blue-700">
            欢迎来到培养方案 <strong>{{ planData.name }}</strong> 的培养目标管理页面，您可以在这里管理和编辑培养目标。
          </p>
        </div>

        <!-- 培养计划与标准信息 -->
        <PlanInfoCard :plan-data="planData" :standard-data="standardData" :enum-data="enumData" />

        <!-- 培养目标管理 -->
        <ObjectiveGrid :objective-list="objectiveList" @add="handleAdd" @edit="handleEdit" @delete="handleDelete" />
      </div>
    </div>

    <!-- 培养目标编辑弹窗 -->
    <ObjectiveFormDialog v-model:visible="dialogVisible" :form-data="formData" @update:formData="formData = $event"
      :is-edit="isEdit" :submit-loading="submitLoading" :plan-name="planData.name" @submit="handleSubmit"
      @cancel="handleCancel" />

    <!-- 删除确认弹窗 -->
    <t-dialog v-model:visible="deleteDialogVisible" header="确认删除" :on-confirm="handleDeleteConfirm"
      :on-cancel="() => deleteDialogVisible = false" :confirm-btn="{
        content: '确认删除',
        theme: 'danger'
      }" :cancel-btn="{ content: '取消', variant: 'outline' }">
      <p>确定要删除培养目标"{{ targetToDelete?.eoTitle }}"吗？此操作不可撤销！</p>
    </t-dialog>
  </div>
</template>

<script setup lang="ts">
import { getPlanDetail } from '@/api/major/plan';
import { getEnum } from '@/api/system/enum';
import { getGraduationStandardDetail } from '@/api/system/graduation-standard';
import { addEo, deleteEo, getEoList, updateEo } from "@/api/training/eo";
import { MessagePlugin } from 'tdesign-vue-next';
import { onMounted, ref } from 'vue';
import { useRoute } from 'vue-router';

// 引入子组件
import ObjectiveFormDialog from './components/ObjectiveFormDialog.vue';
import ObjectiveGrid from './components/ObjectiveGrid.vue';
import PlanInfoCard from './components/PlanInfoCard.vue';

// 定义EoVO接口
interface EoVO {
  id?: number;
  eoTitle: string;
  eoDescription: string;
  status: number;
  createTime?: string;
  modifyTime?: string;
  relatedQuestions?: string; // 存储题目数组
  relatedQuestionsArray?: any[]; // 存储题目JSON字符串(用于保存)
}

const route = useRoute();
const planId = ref(route.params.planId as string);

// 状态管理
const loading = ref(false);
const submitLoading = ref(false);
const dialogVisible = ref(false);
const deleteDialogVisible = ref(false);
const isEdit = ref(false);

// 数据管理
const planData = ref<any>({});
const standardData = ref<any>({});
const enumData = ref<any>(null);
const objectiveList = ref<EoVO[]>([]);
const targetToDelete = ref<EoVO | null>(null);

const formData = ref<EoVO>({
  eoTitle: '',
  eoDescription: '',
  status: 0,
  relatedQuestions: '[]',
});

// 获取培养计划数据
const getPlanData = async () => {
  const res = await getPlanDetail(Number(planId.value));
  planData.value = res.data;

  // 获取关联的标准信息
  if (planData.value.standardId) {
    const standardRes = await getGraduationStandardDetail(planData.value.standardId);
    standardData.value = standardRes.data;
  }
};

// 获取枚举数据
const getEnumData = async () => {
  const res = await getEnum();
  enumData.value = res.data;
};

// 获取培养目标列表
const loadObjectiveList = async () => {
  const res = await getEoList(Number(planId.value));
  objectiveList.value = res.data;
  // 将JSON字符串转换为数组供使用
  objectiveList.value.forEach((item: any) => {
    try {
      item.relatedQuestionsArray = JSON.parse((item.relatedQuestions as string) || '[]');
    } catch (error) {
      console.error('解析关联题目失败:', error);
      item.relatedQuestions = [];
    }
  });
};

// 添加培养目标
const handleAdd = () => {
  isEdit.value = false;
  formData.value = {
    eoTitle: '',
    eoDescription: '',
    status: 0,
    relatedQuestions: '[]',
  };
  dialogVisible.value = true;
};

// 编辑培养目标
const handleEdit = (objective: EoVO) => {
  isEdit.value = true;
  formData.value = {
    ...objective,
    relatedQuestionsArray: objective.relatedQuestionsArray || []
  };
  dialogVisible.value = true;
};

// 删除培养目标
const handleDelete = (objective: EoVO) => {
  targetToDelete.value = objective;
  deleteDialogVisible.value = true;
};

// 确认删除
const handleDeleteConfirm = async () => {
  if (targetToDelete.value?.id) {
    await deleteEo(targetToDelete.value.id);
    MessagePlugin.success('删除成功');
    await loadObjectiveList();
    deleteDialogVisible.value = false;
    targetToDelete.value = null;
  }
};

// 提交表单
const handleSubmit = async (context: any) => {
  if (context.validateResult === true) {
    try {
      submitLoading.value = true;
      const submitData = {
        ...formData.value,
        planId: Number(planId.value),
        // 将关联题目数组转换为JSON字符串存储
        relatedQuestions: JSON.stringify(formData.value.relatedQuestionsArray || [])
      };

      if (isEdit.value && formData.value.id) {
        await updateEo(submitData);
        MessagePlugin.success('更新成功');
      } else {
        await addEo(submitData);
        MessagePlugin.success('添加成功');
      }

      dialogVisible.value = false;
      await loadObjectiveList();
    } finally {
      submitLoading.value = false;
    }
  }
};

// 取消操作
const handleCancel = () => {
  dialogVisible.value = false;
  formData.value = {
    eoTitle: '',
    eoDescription: '',
    status: 0,
    relatedQuestions: '[]',
  };
};

// 页面初始化
onMounted(async () => {
  try {
    loading.value = true;
    await Promise.all([
      getPlanData(),
      getEnumData(),
      loadObjectiveList()
    ]);
  } finally {
    loading.value = false;
  }
});
</script>

<style scoped>
/* 自定义样式 */
</style>

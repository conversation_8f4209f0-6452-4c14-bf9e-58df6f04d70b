<template>
  <div class="min-h-screen bg-gray-50">
    <!-- 骨架屏 -->
    <div v-if="loading" class="min-h-screen bg-gray-50">
      <!-- 顶部骨架屏 -->
      <div class="bg-white shadow-sm border-b">
        <div class="px-6 py-4">
          <div class="flex items-center justify-between">
            <div>
              <div class="h-8 bg-gray-200 rounded animate-pulse w-64"></div>
            </div>
            <div class="text-right">
              <div class="h-4 bg-gray-200 rounded animate-pulse w-32 mb-2"></div>
              <div class="h-3 bg-gray-200 rounded animate-pulse w-40"></div>
            </div>
          </div>
        </div>
      </div>

      <!-- 主内容区域骨架屏 -->
      <div class="px-6 py-6">
        <!-- 欢迎信息骨架屏 -->
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
          <div class="h-5 bg-blue-200 rounded animate-pulse"></div>
        </div>

        <!-- 统计卡片骨架屏 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div v-for="i in 8" :key="i" class="bg-white rounded-lg shadow-sm p-6">
            <div class="h-4 bg-gray-200 rounded animate-pulse mb-2"></div>
            <div class="h-3 bg-gray-200 rounded animate-pulse mb-4 w-2/3"></div>
            <div class="h-8 bg-gray-200 rounded animate-pulse mb-2"></div>
            <div class="h-3 bg-gray-200 rounded animate-pulse w-1/2"></div>
          </div>
        </div>

        <!-- 图表骨架屏 -->
        <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
          <div class="h-6 bg-gray-200 rounded animate-pulse mb-6 w-32"></div>
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <div v-for="i in 6" :key="i" class="bg-gray-50 rounded-lg p-4">
              <div class="h-5 bg-gray-200 rounded animate-pulse mb-4"></div>
              <div class="h-48 bg-gray-200 rounded animate-pulse"></div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 实际内容 -->
    <div v-else>
      <!-- 顶部培养方案信息 -->
      <div class="bg-white shadow-sm border-b">
        <div class="px-6 py-4">
          <div class="flex items-center justify-between">
            <div>
              <h1 class="text-2xl font-bold text-gray-900">{{ plan.name }}</h1>
            </div>
            <div class="text-right">
              <div class="text-sm text-gray-600">
                {{ `${plan.leaderName} · 专业负责人` }}
              </div>
              <div class="text-xs text-gray-500 mt-1">
                {{ `${plan.academyName} ${plan.majorName}` }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 主内容区域 -->
      <div class="px-6 py-6">
        <!-- 培养方案欢迎信息 -->
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
          <p class="text-blue-700">
            欢迎来到培养方案
            <strong>{{ plan.name }}</strong>
            的首页，这里展示了关于该方案的关键指标和统计数据。
          </p>
        </div>

        <!-- 统计数据卡片网格 -->
        <PlanStats :stats="mainStats" />

        <!-- 数据图表分析 -->
        <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
          <h3 class="text-lg font-semibold text-gray-900 mb-6">数据图表分析</h3>

          <!-- 图表网格 -->
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- 不同平台课程占比分析 -->
            <ChartCard title="不同平台课程数占比分析" type="pie" :data="pieChartData1" />

            <!-- 各类课程学分占比统计 -->
            <ChartCard title="各类课程学分占比统计" type="pie" :data="pieChartData2" />

            <!-- 各类必修课理论教学与实践学分分配 -->
            <ChartCard title="各类必修课理论教学与实践学分分配" type="bar" :data="barChartData1" />

            <!-- 实践课程与集中实践课程 -->
            <ChartCard title="实践课程与集中实践课程" type="bar" :data="barChartData2" />

            <!-- 各学期期课程数量统计 -->
            <ChartCard title="各学期期课程数量统计（红考试，黄考查）" type="bar" :data="barChartData3" />

            <!-- 各学期不同类别课程统计 -->
            <ChartCard title="各学期不同类别课程统计" type="bar" :data="barChartData4" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { getPlanDetail } from "@/api/major/plan";
import { onMounted, ref } from "vue";
import { useRoute } from "vue-router";
import ChartCard from "./components/ChartCard.vue";
import PlanStats from "./components/PlanStats.vue";

const route = useRoute();

const majorId = ref(route.params.majorId as string);
const planId = ref(route.params.planId as string);

const plan = ref({} as any);
const loading = ref(false);

const mainStats = ref([
  { title: "不同类型课程与各类课程", subtitle: "", value: 67, unit: "门", color: "blue", icon: "book" },
  { title: "实践教学平台(必修门)", subtitle: "", value: 30, unit: "门", color: "green", icon: "layers" },
  { title: "学科基础+专业课程", subtitle: "", value: 33, unit: "门", color: "orange", icon: "education" },
  { title: "专业选修 (选选课门)", subtitle: "", value: 4, unit: "门", color: "purple", icon: "user-add" },
  { title: "基础课程数", subtitle: "", value: 9, unit: "门", color: "indigo", icon: "home" },
  { title: "开设课程覆盖数", subtitle: "", value: 25, unit: "门", color: "red", icon: "chart-bubble" },
  { title: "开课学期数", subtitle: "", value: 2240, unit: "", color: "yellow", icon: "calendar" },
  { title: "课程目标支持毕业要求指标点覆盖率", subtitle: "", value: 912, unit: "", color: "blue", icon: "chart-pie" },
]);

// 饼图数据1
const pieChartData1 = ref({
  labels: ["公共基础平台课程", "学科基础平台课程", "专业课程", "专业实践课程"],
  datasets: [
    {
      data: [40, 31, 12, 17],
      backgroundColor: ["#3b82f6", "#60a5fa", "#93c5fd", "#dbeafe"],
    },
  ],
});

// 饼图数据2
const pieChartData2 = ref({
  labels: ["专业必修课程", "学科基础课程", "通识必修", "专业选修"],
  datasets: [
    {
      data: [41, 29, 18, 12],
      backgroundColor: ["#f97316", "#3b82f6", "#6b7280", "#fbbf24"],
    },
  ],
});

// 柱状图数据1
const barChartData1 = ref({
  labels: ["通识课程", "学科基础", "专业课程", "专业实践"],
  datasets: [
    {
      label: "理论",
      data: [15, 30, 25, 35],
      backgroundColor: "#3b82f6",
    },
    {
      label: "实践",
      data: [10, 20, 15, 5],
      backgroundColor: "#f97316",
    },
  ],
});

// 柱状图数据2
const barChartData2 = ref({
  labels: ["基础课程实践", "学科基础实践", "专业课程实践"],
  datasets: [
    {
      label: "实践学分",
      data: [4, 2, 18],
      backgroundColor: "#3b82f6",
    },
  ],
});

// 柱状图数据3
const barChartData3 = ref({
  labels: ["第一学期", "第二学期", "第三学期", "第四学期"],
  datasets: [
    {
      label: "考试课程",
      data: [8, 10, 9, 7],
      backgroundColor: "#ef4444",
    },
    {
      label: "考查课程",
      data: [4, 6, 5, 3],
      backgroundColor: "#eab308",
    },
  ],
});

// 柱状图数据4
const barChartData4 = ref({
  labels: ["第一学期", "第二学期", "第三学期", "第四学期"],
  datasets: [
    {
      label: "通识课程",
      data: [12, 12, 10, 8],
      backgroundColor: "#3b82f6",
    },
    {
      label: "学科基础课程",
      data: [4, 5, 6, 4],
      backgroundColor: "#06b6d4",
    },
    {
      label: "专业选修课程",
      data: [2, 3, 4, 2],
      backgroundColor: "#6b7280",
    },
    {
      label: "专业课程",
      data: [3, 4, 3, 5],
      backgroundColor: "#f97316",
    },
  ],
});

const getPlanDetailData = async () => {
  if (!planId.value) return;

  const res = await getPlanDetail(Number(planId.value));
  plan.value = res.data;
};

onMounted(async () => {
  try {
    loading.value = true;
    await getPlanDetailData();
  } finally {
    loading.value = false;
  }
});
</script>

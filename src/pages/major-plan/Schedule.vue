<template>
  <div class="min-h-screen bg-gray-50">
    <!-- 骨架屏 -->
    <div v-if="loading" class="min-h-screen bg-gray-50">
      <!-- 顶部骨架屏 -->
      <div class="bg-white shadow-sm border-b">
        <div class="px-6 py-4">
          <div class="h-8 bg-gray-200 rounded animate-pulse w-64"></div>
        </div>
      </div>

      <!-- 主内容区域骨架屏 -->
      <div class="px-6 py-6">
        <!-- 欢迎信息骨架屏 -->
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
          <div class="h-5 bg-blue-200 rounded animate-pulse"></div>
        </div>

        <!-- 学期选择骨架屏 -->
        <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
          <div class="h-6 bg-gray-200 rounded animate-pulse mb-4 w-32"></div>
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div v-for="i in 8" :key="i" class="bg-gray-50 rounded-lg p-6">
              <div class="h-4 bg-gray-200 rounded animate-pulse mb-4 w-3/4"></div>
              <div class="grid grid-cols-2 gap-4 mb-4">
                <div class="text-center">
                  <div class="h-8 bg-blue-200 rounded animate-pulse mb-2"></div>
                  <div class="h-3 bg-gray-200 rounded animate-pulse"></div>
                </div>
                <div class="text-center">
                  <div class="h-8 bg-green-200 rounded animate-pulse mb-2"></div>
                  <div class="h-3 bg-gray-200 rounded animate-pulse"></div>
                </div>
              </div>
              <div class="grid grid-cols-2 gap-4 mb-4">
                <div class="text-center">
                  <div class="h-8 bg-orange-200 rounded animate-pulse mb-2"></div>
                  <div class="h-3 bg-gray-200 rounded animate-pulse"></div>
                </div>
                <div class="text-center">
                  <div class="h-8 bg-purple-200 rounded animate-pulse mb-2"></div>
                  <div class="h-3 bg-gray-200 rounded animate-pulse"></div>
                </div>
              </div>
              <div class="flex gap-2">
                <div class="h-8 bg-blue-200 rounded animate-pulse flex-1"></div>
                <div class="h-8 bg-gray-200 rounded animate-pulse flex-1"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 实际内容 -->
    <div v-else>
      <!-- 学期选择视图 -->
      <SemesterList v-if="currentView === 'semester-list'" :semester-list="semesterList" :plan-detail="planDetail"
        @manage-semester="handleManageSemester" @manage-task="handleManageTask"
        @view-statistics="handleViewStatistics" />

      <!-- 课程管理视图（包含教学任务管理） -->
      <CourseManagement v-else-if="currentView === 'course-management'" :selected-semester="selectedSemester"
        :course-list="courseList" :classes-list="classesList" :loading="tableLoading" :pagination="pagination"
        :plan-id="planId" :major-id="majorId" @back="backToSemesterList" @page-change="handlePageChange"
        @view-task="handleViewTask" @edit-task="handleEditTask" @create-task="handleCreateTask" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { MessagePlugin } from "tdesign-vue-next";
import { onMounted, ref } from "vue";
import { useRoute } from "vue-router";
import { getPlanDetail, getPlanSemesters, getSemesterCourses, getClassesBySemester } from '@/api/major/plan';
import SemesterList from './components/ScheduleSemesterList.vue';
import CourseManagement from './components/ScheduleCourseManagement.vue';

const route = useRoute();
const planId = ref(route.params.planId as string);
const majorId = ref(route.params.majorId as string);

// 状态管理
const loading = ref(false);
const tableLoading = ref(false);
const currentView = ref<"semester-list" | "course-management">("semester-list");
const selectedSemester = ref(null);

// 学期数据
const semesterList = ref([]);
//培养方案
const planDetail = ref(null);
// 课程数据
const courseList = ref([]);

const classesList = ref([]);

// 分页配置
const pagination = ref({
  current: 1,
  pageSize: 10,
  total: courseList.value.length,
});

// 管理学期课程计划 - 传入真实数据
const handleManageSemester = async (semester: any) => {
  selectedSemester.value = semester;
  currentView.value = "course-management";

  console.log('切换到学期管理，学期信息:', semester);
  console.log('当前 classesList:', classesList.value);

  // 如果班级数据为空，重新加载
  if (!classesList.value || classesList.value.length === 0) {
    console.log('班级数据为空，重新加载...');
    await loadClassesData();
  }

  // 根据选中的学期加载对应的课程数据
  await loadCourseData(semester.id);

  console.log('加载课程数据后，courseList:', courseList.value);
  console.log('加载课程数据后，classesList:', classesList.value);
};

// 管理学期教学任务 - 现在合并到课程管理中
const handleManageTask = async (semester: any) => {
  selectedSemester.value = semester;
  currentView.value = "course-management";

  console.log('切换到任务管理，学期信息:', semester);

  // 如果班级数据为空，重新加载
  if (!classesList.value || classesList.value.length === 0) {
    console.log('班级数据为空，重新加载...');
    await loadClassesData();
  }

  // 根据选中的学期加载对应的课程数据
  await loadCourseData(semester.id);

  console.log('任务管理 - courseList:', courseList.value);
  console.log('任务管理 - classesList:', classesList.value);

  MessagePlugin.success(`已切换到 ${semester.name} 的课程计划管理`);
};

// 返回学期列表
const backToSemesterList = () => {
  currentView.value = "semester-list";
  selectedSemester.value = null;
  // 清空课程数据（班级数据保留，因为它是全局的）
  courseList.value = [];
  // classesList.value = []; // 不清空班级数据，因为它不依赖于特定学期
};

// 查看学期统计
const handleViewStatistics = (semester: any) => {
  MessagePlugin.info(`正在查看 ${semester.name} 的统计信息...`);
  // 这里可以打开统计弹窗或跳转到统计页面
};

// 分页变化处理
const handlePageChange = (pageInfo: any) => {
  pagination.value.current = pageInfo.current;
  pagination.value.pageSize = pageInfo.pageSize;
};

const loadClassesData = async () => {
  const res = await getClassesBySemester(majorId.value, planId.value);
  classesList.value = res.data;
}

// 加载课程数据 - 根据学期ID获取真实数据
const loadCourseData = async (semesterId: string) => {
  const res = await getSemesterCourses(majorId.value, planId.value, semesterId);
  courseList.value = res.data;
};
// 加载培养计划信息
const loadPlanData = async () => {
  const res = await getPlanDetail( Number(planId.value));
  planDetail.value = res.data;
};

// 获取学期数据
const loadSemesterData = async () => {
  try {
    loading.value = true;

    await loadPlanData();
    // 获取学期列表
    const res = await getPlanSemesters(Number(planId.value));
    semesterList.value = res.data;

    await loadClassesData();

  } finally {
    loading.value = false;
  }
};

// 教学任务相关方法
const handleViewTask = (task: any) => {
  MessagePlugin.info(`查看教学任务：${task.name}`);
  // 这里可以打开任务详情弹窗或跳转到详情页面
};

const handleEditTask = (task: any) => {
  MessagePlugin.info(`编辑教学任务：${task.name}`);
  // 这里可以打开任务编辑弹窗或跳转到编辑页面
};

const handleCreateTask = () => {
  MessagePlugin.info('创建新的教学任务');
  // 这里可以打开任务创建弹窗或跳转到创建页面
};

// 页面初始化
onMounted(async () => {
  await loadSemesterData();
});
</script>

<style scoped>
/* 使用 Tailwind CSS，无需自定义样式 */
</style>

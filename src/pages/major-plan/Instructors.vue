<template>
  <div class="min-h-screen bg-gray-50">
    <InstructorSkeleton v-if="loading" />
    <div v-else>
      <!-- Title -->
      <div class="bg-white shadow-sm border-b">
        <div class="px-6 py-4">
          <h1 class="text-2xl font-bold text-gray-900">
            {{ plan?.planName }} 课程负责人管理
          </h1>
        </div>
      </div>

      <div class="p-6">
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
          <p class="text-blue-700">
            欢迎来到培养方案 <strong>{{ plan?.planName }}</strong>
            的课程负责人管理页面，您可以在这里管理课程负责人。
          </p>
        </div>

        <!-- Stats -->
        <InstructorStats :stats="stats" />

        <!-- Main Content -->
        <div class="bg-white rounded-lg shadow-sm p-6">
          <div class="flex items-center space-x-4 mb-4">
            <!-- Filters -->
            <t-input v-model="filters.keyword" placeholder="搜索课程代码、名称或负责人姓名..." clearable class="w-80"
              @enter="handleSearch">
              <template #prefix-icon>
                <t-icon name="search" />
              </template>
            </t-input>
            <t-select v-model="filters.status" placeholder="负责人状态" :options="statusOptions" clearable class="w-48"
              @change="handleSearch" />
          </div>

          <!-- Table -->
          <t-table :data="courseList" :columns="columns" row-key="courseId" :loading="tableLoading"
            :pagination="pagination" @page-change="handlePageChange">
            <template #courseLeaderInfo="{ row }">
              <div v-if="row.courseLeader">
                {{ teacherMap[row.courseLeader]?.label || "N/A" }}
              </div>
              <t-tag v-else theme="warning" variant="light">未分配</t-tag>
            </template>
            <template #teacherTitle="{ row }">
              {{ teacherMap[row.courseLeader]?.title || "-" }}
            </template>
            <template #teacherDepartment="{ row }">
              {{ teacherMap[row.courseLeader]?.department || "-" }}
            </template>
            <template #teacherEmail="{ row }">
              {{ teacherMap[row.courseLeader]?.email || "-" }}
            </template>
            <template #operation="{ row }">
              <t-button variant="text" theme="primary" @click="handleEdit(row)">
                <template #icon><t-icon name="edit" /></template>
                编辑
              </t-button>
            </template>
          </t-table>
        </div>
      </div>
    </div>

    <!-- Edit Dialog -->
    <InstructorEditDialog v-model:visible="dialogVisible" :course="currentCourse" :teacher-list="safeTeacherList"
      @submit="handleSubmit" />
  </div>
</template>

<script setup lang="ts">
import { getTeacherOptionsListByAcademyId } from "@/api/base/teacher";
import {
  getCourseList,
  getInstructorStats,
  updateCourseLeader,
} from "@/api/training/course";
import { getPlan } from "@/api/training/plan";
import { MessagePlugin, type PageInfo } from "tdesign-vue-next";
import { onMounted, reactive, ref, computed } from "vue";
import { useRoute } from "vue-router";
import InstructorEditDialog from "./components/InstructorEditDialog.vue";
import InstructorSkeleton from "./components/InstructorSkeleton.vue";
import InstructorStats from "./components/InstructorStats.vue";

const route = useRoute();
const majorId = ref(route.params.majorId as string);
const planId = ref(route.params.planId as string);

const loading = ref(true);
const tableLoading = ref(false);
const dialogVisible = ref(false);

const plan = ref<any>(null);
const courseList = ref<any[]>([]);
const teacherList = ref<any[]>([]);
const teacherMap = ref<any>({});
const currentCourse = ref<any>({});

// 安全的教师列表，用于传递给子组件
const safeTeacherList = computed(() => {
  return Array.isArray(teacherList.value) 
    ? teacherList.value.filter((item: any) => item != null && item.value != null)
    : [];
});

const stats = ref({
  totalCourses: 0,
  assignedCount: 0,
  unassignedCount: 0,
});

const filters = reactive({
  keyword: "",
  status: null as "assigned" | "unassigned" | null,
});

const statusOptions = [
  { label: "全部课程", value: null },
  { label: "已分配", value: "assigned" },
  { label: "未分配", value: "unassigned" },
];

const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
});

const columns = [
  { colKey: "courseCode", title: "代码", width: 100 },
  { colKey: "courseName", title: "名称", ellipsis: true },
  {
    colKey: "courseCredit",
    title: "学分",
    width: 80,
    align: "center" as const,
  },
  {
    colKey: "courseSemester",
    title: "学期",
    width: 80,
    align: "center" as const,
  },
  {
    colKey: "courseLeader",
    title: "负责人",
    width: 120,
    cell: "courseLeaderInfo",
  },
  { colKey: "teacherTitle", title: "职称", width: 120, cell: "teacherTitle" },
  {
    colKey: "teacherDepartment",
    title: "系部",
    width: 150,
    cell: "teacherDepartment",
    ellipsis: true,
  },
  {
    colKey: "teacherEmail",
    title: "邮箱",
    width: 200,
    cell: "teacherEmail",
    ellipsis: true,
  },
  {
    colKey: "operation",
    title: "操作",
    width: 120,
    fixed: "right" as const,
    align: "center" as const,
  },
];

const fetchInitialData = async () => {
  loading.value = true;
  try {
    const planRes = await getPlan(Number(planId.value));
    plan.value = planRes.data;

    if (plan.value.academyId) {
      const teacherRes = await getTeacherOptionsListByAcademyId(
        plan.value.academyId,
      );
      // 过滤掉 null 值，确保数据安全
      teacherList.value = Array.isArray(teacherRes.data) 
        ? teacherRes.data.filter((item: any) => item != null) 
        : [];
      teacherMap.value = teacherList.value.reduce((acc: any, curr: any) => {
        if (curr && curr.value != null) {
          acc[curr.value] = curr;
        }
        return acc;
      }, {});
    }

    await fetchCourseData();
  } finally {
    loading.value = false;
  }
};

const fetchStats = async () => {
  const res = await getInstructorStats(
    Number(majorId.value),
    Number(planId.value),
  );
  stats.value = res.data;
};

const fetchCourseData = async () => {
  tableLoading.value = true;
  try {
    const params: any = {
      current: pagination.current,
      size: pagination.pageSize,
      planId: planId.value,
      keyword: filters.keyword || undefined,
    };
    if (filters.status) {
      params.hasLeader = filters.status === "assigned";
    }
    const res = await getCourseList(params); // Assuming getCourseList can handle these filters
    courseList.value = res.data.records || [];
    pagination.total = res.data.total || 0;
    await fetchStats();
  } finally {
    tableLoading.value = false;
  }
};

const handleSearch = () => {
  pagination.current = 1;
  fetchCourseData();
};

const handlePageChange = (page: PageInfo) => {
  pagination.current = page.current;
  pagination.pageSize = page.pageSize;
  fetchCourseData();
};

const handleEdit = (row: any) => {
  currentCourse.value = { ...row };
  dialogVisible.value = true;
};

const handleSubmit = async (
  updatedCourse: any,
  setLoading: (loading: boolean) => void,
) => {
  try {
    await updateCourseLeader({
      courseId: updatedCourse.courseId,
      courseLeader: updatedCourse.courseLeader,
    });
    MessagePlugin.success("更新成功");
    dialogVisible.value = false;
    await fetchCourseData();
  } finally {
    setLoading(false);
  }
};

onMounted(fetchInitialData);
</script>

<style scoped>
/* Add your styles here */
</style>

<template>
  <div class="min-h-screen bg-gray-50">
    <CurriculumSkeleton v-if="loading" />
    <div v-else>
      <!-- 顶部信息 -->
      <div class="bg-white shadow-sm border-b">
        <div class="px-6 py-4">
          <h1 class="text-2xl font-bold text-gray-900">课程体系管理</h1>
        </div>
      </div>

      <!-- 主内容区域 -->
      <div class="px-6 py-6">
        <!-- 培养方案欢迎信息 -->
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
          <p class="text-blue-700">
            欢迎来到培养方案
            <strong>{{ plan?.planName }}</strong>
            的课程体系管理页面，您可以在这里管理所有课程信息。
          </p>
        </div>

        <!-- 统计信息卡片 -->
        <CurriculumStats :stats="stats" />

        <!-- 课程管理 -->
        <CourseList :courses="courses" :pagination="pagination" :table-loading="tableLoading" :plan-id="planId"
          v-model:searchValue="searchValue" :enum-data="enumData" :teacher-map="teacherMap" @export="exportTable"
          @import="beforeImport" @add="openDialog('add')" @edit="(row) => openDialog('edit', row)"
          @delete="confirmDeleteCourse" @search="fetchCourses" @page-change="handlePageChange" />
      </div>
    </div>

    <CourseFormDialog v-model:visible="showDialog" :dialog-type="dialogType" :course-data="currentCourse"
      :enum-data="enumData" :teacher-list="teacherList" @submit="handleSubmit" />
  </div>
</template>

<script lang="tsx" setup>
import { getTeacherOptionsListByAcademyId } from "@/api/base/teacher";
import { getEnum } from "@/api/system/enum";
import {
  addCourse,
  deleteCourse,
  getCourseList,
  getCourseStatistics,
  updateCourse,
} from "@/api/training/course";
import { getPlan } from "@/api/training/plan";
import {
  MessagePlugin,
  type PageInfo,
  type UploadProps,
} from "tdesign-vue-next";
import { onMounted, reactive, ref } from "vue";
import { useRoute } from "vue-router";
import * as XLSX from "xlsx";
import CourseFormDialog from "./components/CourseFormDialog.vue";
import CourseList from "./components/CourseList.vue";
import CurriculumSkeleton from "./components/CurriculumSkeleton.vue";
import CurriculumStats from "./components/CurriculumStats.vue";

const route = useRoute();
const majorId = ref(route.params.majorId as string);
const planId = ref(route.params.planId as string);

const plan = ref<any>(null);

const courses = ref<any[]>([]);
const loading = ref(true);
const tableLoading = ref(false);
const showDialog = ref(false);

const emptyCourse = {
  courseCode: "",
  courseName: "",
  courseCredit: 0,
  courseCore: false,
  courseExam: false,
  courseHoursTotal: 0,
  courseHoursTheory: 0,
  courseHoursExperiment: 0,
  courseHoursOther: 0,
  courseHoursExtracurricular: 0,
  courseSemester: null as number | null,
  courseType1: null as number | null,
  courseType2: null as number | null,
  courseType3: null as number | null,
  courseNature: null as number | null,
  courseVersion: new Date().getFullYear(),
  planId: null as number | null,
  majorId: null as number | null,
  courseLeader: null as number | null,
  status: 0,
};
const currentCourse = ref({ ...emptyCourse });
const dialogType = ref<"add" | "edit">("add");
const searchValue = reactive({
  keyword: "",
  semester: null as number | null,
  courseType: null as number | null,
});

const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  pageSizeOptions: [10, 20, 50, 100],
});

const enumData = ref<any>(null);
const teacherList = ref<any[]>([]);
const teacherMap = ref<any>({});

const stats = ref({
  requiredCourses: 0,
  totalCredits: 0,
  totalHours: 0,
  practiceHours: 0,
  totalCourses: 0,
});

onMounted(async () => {
  try {
    loading.value = true;
    const planRes = await getPlan(Number(planId.value));
    plan.value = planRes.data;

    await fetchCourses();

    const teacherRes = await getTeacherOptionsListByAcademyId(
      Number(majorId.value),
    );
    // 过滤掉 null 值，确保数据安全
    teacherList.value = Array.isArray(teacherRes.data) 
      ? teacherRes.data.filter((item: any) => item != null) 
      : [];
    teacherMap.value = teacherList.value.reduce((acc: any, curr: any) => {
      if (curr && curr.value != null) {
        acc[curr.value] = curr.label;
      }
      return acc;
    }, {});

    const res = await getEnum();
    // 确保 enumData 的安全性
    enumData.value = res.data || null;
  } finally {
    loading.value = false;
  }
});

const fetchCourses = async () => {
  if (!planId.value) {
    courses.value = [];
    pagination.total = 0;
    return;
  }
  tableLoading.value = true;
  try {
    const params = {
      current: pagination.current,
      size: pagination.pageSize,
      planId: planId.value,
      courseName: searchValue.keyword || undefined,
      courseCode: searchValue.keyword || undefined,
      courseSemester: searchValue.semester || undefined,
      courseType1: searchValue.courseType || undefined,
    };
    const response = await getCourseList(params);
    courses.value = response.data.records || [];
    pagination.total = response.data.total || 0;
    await updateStats();
  } finally {
    tableLoading.value = false;
  }
};

const updateStats = async () => {
  try {
    const res = await getCourseStatistics(
      Number(majorId.value),
      Number(planId.value),
    );
    stats.value = res.data;
  } catch (error) {
    console.error("Failed to fetch statistics:", error);
  }
};

const openDialog = (type: "add" | "edit", row?: any) => {
  dialogType.value = type;
  if (type === "add") {
    currentCourse.value = {
      ...emptyCourse,
      planId: Number(planId.value),
      majorId: Number(majorId.value),
      courseVersion: plan.value?.planVersion || new Date().getFullYear(),
    };
  } else {
    currentCourse.value = { ...row, planId: Number(planId.value) };
  }
  showDialog.value = true;
};

const handleSubmit = async (
  dataToSubmit: any,
  setLoading: (loading: boolean) => void,
) => {
  try {
    dataToSubmit.majorId = Number(majorId.value);
    dataToSubmit.planId = Number(planId.value);

    dataToSubmit.courseSemester = dataToSubmit.courseSemester
      ? Number(dataToSubmit.courseSemester)
      : null;
    dataToSubmit.courseType1 = dataToSubmit.courseType1
      ? Number(dataToSubmit.courseType1)
      : null;
    dataToSubmit.courseNature = dataToSubmit.courseNature
      ? Number(dataToSubmit.courseNature)
      : null;
    dataToSubmit.courseLeader = dataToSubmit.courseLeader
      ? Number(dataToSubmit.courseLeader)
      : null;

    if (dialogType.value === "add") {
      await addCourse(dataToSubmit);
      MessagePlugin.success("新增课程成功");
    } else {
      await updateCourse(dataToSubmit);
      MessagePlugin.success("更新课程成功");
    }
    showDialog.value = false;
    await fetchCourses();
  } finally {
    setLoading(false);
  }
};

const confirmDeleteCourse = async (row: any) => {
  if (!row.courseId) {
    MessagePlugin.error("课程ID无效");
    return;
  }
  tableLoading.value = true;
  try {
    await deleteCourse(String(row.courseId));
    MessagePlugin.success("删除课程成功");
    await fetchCourses();
  } finally {
    tableLoading.value = false;
  }
};

const exportTable = () => {
  if (courses.value.length === 0) {
    MessagePlugin.info("没有数据可导出");
    return;
  }
  try {
    const dataToExport = courses.value.map((c: any) => ({
      课程编码: c.courseCode,
      课程名称: c.courseName,
      学分: c.courseCredit,
      核心课程: c.courseCore ? "是" : "否",
      考试课程: c.courseExam ? "是" : "否",
      总学时: c.courseHoursTotal,
      理教学时: c.courseHoursTheory,
      实验学时: c.courseHoursExperiment,
      课外学时: c.courseHoursExtracurricular,
      其他学时: c.courseHoursOther,
      上课学期:
        enumData.value?.map?.semesterPeriod?.[String(c.courseSemester)] ||
        c.courseSemester,
      课程类别:
        enumData.value?.map?.courseCategory?.[String(c.courseType1)] ||
        c.courseType1,
      课程性质:
        enumData.value?.map?.courseNature?.[String(c.courseNature)] ||
        c.courseNature,
      课程负责人: teacherMap.value[c.courseLeader] || "未设置",
      课程版本: c.courseVersion,
      所属培养方案ID: c.planId,
      所属专业ID: c.majorId,
      状态:
        enumData.value?.map?.courseStatus?.[String(c.status)] ||
        (c.status === 0 ? "正常" : c.status === -1 ? "已删除" : "未知"),
    }));
    const worksheet = XLSX.utils.json_to_sheet(dataToExport);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(
      workbook,
      worksheet,
      `课程表-${plan.value?.planName || planId.value}`,
    );
    XLSX.writeFile(
      workbook,
      `课程表_${plan.value?.planName || planId.value
      }_${new Date().toISOString().slice(0, 10)}.xlsx`,
    );
    MessagePlugin.success("导出成功");
  } catch (error: any) {
    console.error("导出失败:", error);
    MessagePlugin.error(String(error?.message || "导出失败"));
  }
};

const beforeImport: UploadProps["beforeUpload"] = (file) => {
  const reader = new FileReader();
  reader.onload = async (e) => {
    try {
      const fileData = new Uint8Array(e.target?.result as ArrayBuffer);
      const workbook = XLSX.read(fileData, { type: "array" });
      const worksheet = workbook.Sheets[workbook.SheetNames[0]];
      const json: any[] = XLSX.utils.sheet_to_json(worksheet);

      if (!planId.value) {
        MessagePlugin.error("培养方案ID未知，无法导入课程。");
        return;
      }

      const importedCourses = json.map((item: any) => ({
        courseCode: String(item["课程编码"] || ""),
        courseName: String(item["课程名称"] || ""),
        courseCredit: Number(item["学分"] || 0),
        courseCore: String(item["核心课程"]).toLowerCase() === "是",
        courseExam: String(item["考试课程"]).toLowerCase() === "是",
        courseHoursTotal: Number(item["总学时"] || 0),
        courseHoursTheory: Number(item["理教学时"] || 0),
        courseHoursExperiment: Number(item["实验学时"] || 0),
        courseHoursExtracurricular: Number(item["课外学时"] || 0),
        courseHoursOther: Number(item["其他学时"] || 0),
        courseSemester: Number(item["上课学期"]) || null,
        courseType1: Number(item["课程类别"]) || null,
        courseNature: Number(item["课程性质"]) || null,
        courseVersion:
          Number(item["课程版本"]) ||
          plan.value?.planVersion ||
          new Date().getFullYear(),
        planId: Number(planId.value),
        majorId: Number(majorId.value),
        courseLeader: Number(item["课程负责人"]) || null,
        status: 0,
      }));

      if (importedCourses.length === 0) {
        MessagePlugin.info("没有可导入的有效课程数据。");
        return;
      }

      tableLoading.value = true;
      let successCount = 0;
      let errorCount = 0;

      for (const course of importedCourses) {
        if (!course.courseCode || !course.courseName) {
          errorCount++;
          continue;
        }

        try {
          await addCourse(course);
          successCount++;
        } catch (error: any) {
          errorCount++;
        }
      }

      tableLoading.value = false;

      if (errorCount > 0) {
        MessagePlugin.warning(
          `成功导入 ${successCount} 条课程，${errorCount} 条导入失败。`,
        );
      } else {
        MessagePlugin.success(`成功导入 ${successCount} 条课程数据`);
      }
      await fetchCourses();
    } catch (error: any) {
      console.error("文件解析或导入过程出错:", error);
      MessagePlugin.error(String(error?.message || "文件解析失败"));
      tableLoading.value = false;
    }
  };
  reader.readAsArrayBuffer(file.raw as File);
  return false;
};

const handlePageChange = (pageInfo: PageInfo) => {
  pagination.current = pageInfo.current;
  pagination.pageSize = pageInfo.pageSize;
  fetchCourses();
};
</script>

<style scoped>
/* 响应式样式 */
@media (max-width: 768px) {
  .t-dialog {
    width: 95vw !important;
    max-width: 95vw !important;
    top: 2.5vh !important;
  }

  .t-dialog :deep(.t-dialog__body > div) {
    max-height: 85vh !important;
    overflow-y: auto !important;
  }
}
</style>

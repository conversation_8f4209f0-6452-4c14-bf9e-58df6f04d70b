# 字典工具使用说明

## 概述

字典工具是一个用于管理系统字典数据的工具类，提供了统一的字典数据获取、缓存和转换功能。支持根据字典类型名称或ID获取字典数据，以及字典值与标签之间的转换。

## 核心功能

1. **全局字典初始化** - 一次性加载所有字典数据到内存
2. **缓存管理** - 自动缓存字典数据，提高查询效率
3. **类型映射** - 支持根据类型名称或ID获取字典数据
4. **值标签转换** - 字典值与显示标签之间的双向转换
5. **表单组件集成** - 与TDesign组件库无缝集成

## 1. 如何获取全部的字典数据

### 方法一：使用全局初始化
```typescript
import { initializeGlobalDict, getGlobalDictState } from '@/utils/dictUtil'

// 初始化全局字典数据
await initializeGlobalDict(true) // true表示强制刷新

// 获取全局状态
const globalState = getGlobalDictState()
console.log('字典类型数量:', globalState.dictTypes.length)
console.log('总数据条数:', globalState.totalDictCount)
```

### 方法二：使用Composition API Hook
```typescript
import { useDict } from '@/hooks/useDict'

const { globalState } = useDict()

// 自动获取全局字典状态
console.log('初始化状态:', globalState.value.initialized)
console.log('字典类型:', globalState.value.dictTypes)
```

## 2. 字典类型数据如何获取

### 获取所有字典类型
```typescript
import { getDictTypes } from '@/utils/dictUtil'

// 获取所有字典类型
const dictTypes = getDictTypes()
console.log('字典类型列表:', dictTypes)
```

### 获取全局状态中的字典类型
```typescript
import { getGlobalDictState } from '@/utils/dictUtil'

const globalState = getGlobalDictState()
const dictTypes = globalState.dictTypes

// 字典类型结构
interface DictType {
  id: number;        // 类型ID
  title: string;     // 类型标题
  status: number;    // 状态 (0:启用, 1:禁用)
  remark?: string;   // 备注
}
```

## 3. 如何根据字典类型的名称获取该类型的所有字典数据

### 方法一：获取原始数据
```typescript
import { getDictDataByTypeTitle } from '@/utils/dictUtil'

// 根据类型名称获取字典数据
const courseNatureData = await getDictDataByTypeTitle('课程性质')
console.log('课程性质数据:', courseNatureData)

// 数据结构
interface DictData {
  id: number;           // 字典数据ID
  label: string;        // 显示标签
  value: string | number; // 字典值
  typeId: number;       // 所属类型ID
  sort: number;         // 排序
  status: number;       // 状态
}
```

### 方法二：获取选项格式数据
```typescript
import { getDictOptionsByTypeTitle } from '@/utils/dictUtil'

// 获取选项格式的字典数据（适用于表单组件）
const courseNatureOptions = await getDictOptionsByTypeTitle('课程性质')
console.log('课程性质选项:', courseNatureOptions)

// 选项格式
interface DictOption {
  label: string;        // 显示标签
  value: string | number; // 字典值
}
```

### 方法三：使用Composition API Hook
```typescript
import { useDictByTypeTitle } from '@/hooks/useDict'

const { 
  dictData,           // 原始数据
  dictOptions,        // 选项格式数据
  loading,            // 加载状态
  error              // 错误信息
} = useDictByTypeTitle('课程性质')

// 自动获取数据，支持响应式更新
console.log('字典数据:', dictData.value)
console.log('选项数据:', dictOptions.value)
```

## 4. 如何根据字典类型的ID获取该类型的所有字典数据

### 方法一：根据类型ID获取数据
```typescript
import { getDictData } from '@/utils/dictUtil'

// 根据类型ID获取字典数据
const dictData = await getDictData(1) // 1为字典类型ID
console.log('字典数据:', dictData)
```

### 方法二：获取选项格式数据
```typescript
import { getDictOptions } from '@/utils/dictUtil'

// 根据类型ID获取选项格式数据
const dictOptions = await getDictOptions(1)
console.log('选项数据:', dictOptions)
```

### 方法三：获取多个类型的数据
```typescript
import { getMultipleDictData } from '@/utils/dictUtil'

// 批量获取多个类型的字典数据
const multipleData = await getMultipleDictData([1, 2, 3])
console.log('多类型数据:', multipleData)

// 返回格式: { [typeId]: DictData[] }
```

## 5. 在其他Vue页面中如何使用

### Composition API 方式（推荐）
```vue
<template>
  <div>
    <!-- 使用字典数据 -->
    <t-select v-model="selectedValue" placeholder="选择课程性质">
      <t-option 
        v-for="option in courseNatureOptions" 
        :key="option.value" 
        :value="option.value" 
        :label="option.label" 
      />
    </t-select>
    
    <!-- 显示转换后的标签 -->
    <p>选中值: {{ selectedValue }}</p>
    <p>显示标签: {{ selectedLabel }}</p>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useDictByTypeTitle } from '@/hooks/useDict'

// 使用Hook获取字典数据
const { dictOptions: courseNatureOptions } = useDictByTypeTitle('课程性质')

// 响应式数据
const selectedValue = ref('')

// 计算属性：根据值获取标签
const selectedLabel = computed(() => {
  const option = courseNatureOptions.value.find(opt => opt.value === selectedValue.value)
  return option?.label || selectedValue.value
})
</script>
```

### Options API 方式
```vue
<template>
  <div>
    <t-select v-model="selectedValue" placeholder="选择课程性质">
      <t-option 
        v-for="option in courseNatureOptions" 
        :key="option.value" 
        :value="option.value" 
        :label="option.label" 
      />
    </t-select>
  </div>
</template>

<script>
import { getDictOptionsByTypeTitle } from '@/utils/dictUtil'

export default {
  data() {
    return {
      courseNatureOptions: [],
      selectedValue: ''
    }
  },
  async mounted() {
    // 获取字典选项
    this.courseNatureOptions = await getDictOptionsByTypeTitle('课程性质')
  },
  methods: {
    // 值转标签方法
    async getNatureLabel(value) {
      const option = this.courseNatureOptions.find(opt => opt.value === value)
      return option?.label || value
    }
  }
}
</script>
```

## 6. 如何在单选/复选中使用，如何在下拉中使用

### 单选组件使用
```vue
<template>
  <div>
    <h3>单选组件</h3>
    <t-radio-group v-model="radioValue">
      <t-radio 
        v-for="option in courseNatureOptions" 
        :key="option.value" 
        :value="option.value"
      >
        {{ option.label }}
      </t-radio>
    </t-radio-group>
    <p>选中值: {{ radioValue }}</p>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useDictByTypeTitle } from '@/hooks/useDict'

const { dictOptions: courseNatureOptions } = useDictByTypeTitle('课程性质')
const radioValue = ref('')
</script>
```

### 复选组件使用
```vue
<template>
  <div>
    <h3>复选组件</h3>
    <t-checkbox-group v-model="checkboxValues">
      <t-checkbox 
        v-for="option in teacherRoleOptions" 
        :key="option.value" 
        :value="option.value"
      >
        {{ option.label }}
      </t-checkbox>
    </t-checkbox-group>
    <p>选中值: {{ checkboxValues }}</p>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useDictByTypeTitle } from '@/hooks/useDict'

const { dictOptions: teacherRoleOptions } = useDictByTypeTitle('教师角色')
const checkboxValues = ref<string[]>([])
</script>
```

### 下拉选择组件使用
```vue
<template>
  <div>
    <h3>下拉选择组件</h3>
    <t-select v-model="selectValue" placeholder="选择课程性质">
      <t-option 
        v-for="option in courseNatureOptions" 
        :key="option.value" 
        :value="option.value" 
        :label="option.label" 
      />
    </t-select>
    <p>选中值: {{ selectValue }}</p>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useDictByTypeTitle } from '@/hooks/useDict'

const { dictOptions: courseNatureOptions } = useDictByTypeTitle('课程性质')
const selectValue = ref('')
</script>
```

### 表格中使用字典数据
```vue
<template>
  <div>
    <h3>表格中使用</h3>
    <t-table :data="tableData" :columns="tableColumns" />
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useDictByTypeTitle } from '@/hooks/useDict'

const { dictOptions: courseNatureOptions } = useDictByTypeTitle('课程性质')
const { dictOptions: teacherRoleOptions } = useDictByTypeTitle('教师角色')

// 表格数据
const tableData = ref([
  { id: 1, name: '高等数学', nature: '必修', role: '1' },
  { id: 2, name: '线性代数', nature: '必修', role: '2' },
  { id: 3, name: '概率论', nature: '选修', role: '3' }
])

// 表格列定义
const tableColumns = ref([
  { colKey: 'name', title: '课程名称' },
  { 
    colKey: 'nature', 
    title: '课程性质',
    cell: (h: any, { row }: any) => {
      const option = courseNatureOptions.value.find(opt => opt.value === row.nature)
      return h('span', option?.label || row.nature)
    }
  },
  { 
    colKey: 'role', 
    title: '教师角色',
    cell: (h: any, { row }: any) => {
      const option = teacherRoleOptions.value.find(opt => opt.value === row.role)
      return h('span', option?.label || row.role)
    }
  }
])
</script>
```

## 7. 字典数据的value和类型如何拿到对应的label

### 方法一：使用转换函数
```typescript
import { getDictLabelByTypeTitle } from '@/utils/dictUtil'

// 根据类型名称和值获取标签
const label = await getDictLabelByTypeTitle('课程性质', '必修')
console.log('标签:', label) // 输出: 必修

// 根据类型ID和值获取标签
const label2 = await getDictLabel(1, '必修')
console.log('标签:', label2) // 输出: 必修
```

### 方法二：使用计算属性
```vue
<script setup lang="ts">
import { computed } from 'vue'
import { useDictByTypeTitle } from '@/hooks/useDict'

const { dictOptions: courseNatureOptions } = useDictByTypeTitle('课程性质')

// 计算属性：根据值获取标签
const getNatureLabel = computed(() => {
  return (value: string) => {
    const option = courseNatureOptions.value.find(opt => opt.value === value)
    return option?.label || value
  }
})

// 使用
const label = getNatureLabel.value('必修')
console.log('标签:', label)
</script>
```

### 方法三：使用工具函数
```typescript
// 创建通用的转换函数
const createLabelConverter = (options: Array<{label: string, value: string | number}>) => {
  return (value: string | number) => {
    const option = options.find(opt => opt.value === value)
    return option?.label || String(value)
  }
}

// 使用
const courseNatureOptions = await getDictOptionsByTypeTitle('课程性质')
const convertNatureLabel = createLabelConverter(courseNatureOptions)

const label = convertNatureLabel('必修')
console.log('标签:', label)
```

## 8. 最佳实践

### 1. 初始化时机
```typescript
// 在应用启动时初始化字典数据
// main.ts 或 App.vue 中
import { initializeGlobalDict } from '@/utils/dictUtil'

// 应用启动时初始化
await initializeGlobalDict()
```

### 2. 错误处理
```typescript
import { useDictByTypeTitle } from '@/hooks/useDict'

const { dictOptions, loading, error } = useDictByTypeTitle('课程性质')

// 在模板中处理加载和错误状态
<template>
  <div v-if="loading">加载中...</div>
  <div v-else-if="error">加载失败: {{ error }}</div>
  <div v-else>
    <!-- 使用字典数据 -->
  </div>
</template>
```

### 3. 性能优化
```typescript
// 使用缓存，避免重复请求
const { dictOptions } = useDictByTypeTitle('课程性质')

// 字典数据会被自动缓存，后续访问直接从缓存获取
```

### 4. 类型安全
```typescript
// 定义字典值的类型
type CourseNature = '必修' | '选修' | '限选'

// 在组件中使用
const selectedNature = ref<CourseNature>('必修')
```

## 9. 常见问题

### Q: 字典数据没有加载怎么办？
A: 检查是否调用了 `initializeGlobalDict()`，或者使用 `useDictByTypeTitle` Hook 自动加载。

### Q: 如何刷新字典数据？
A: 调用 `initializeGlobalDict(true)` 强制刷新缓存。

### Q: 字典值转标签返回空字符串？
A: 检查字典类型名称是否正确，以及该值是否存在于字典数据中。

### Q: 如何在多个组件中共享字典数据？
A: 使用全局初始化，所有组件都可以访问缓存的字典数据。

## 10. API 参考

### 核心函数
- `initializeGlobalDict(forceRefresh?: boolean)` - 初始化全局字典
- `getDictDataByTypeTitle(typeTitle: string)` - 根据类型名称获取数据
- `getDictData(typeId: number)` - 根据类型ID获取数据
- `getDictLabelByTypeTitle(typeTitle: string, value: string | number)` - 值转标签
- `getDictOptionsByTypeTitle(typeTitle: string)` - 获取选项格式数据

### Hook
- `useDictByTypeTitle(typeTitle: string)` - 响应式字典数据Hook
- `useDict()` - 全局字典状态Hook

### 状态管理
- `getGlobalDictState()` - 获取全局状态
- `clearDictCache(typeId?: number)` - 清除缓存
- `getDictCacheInfo()` - 获取缓存信息 
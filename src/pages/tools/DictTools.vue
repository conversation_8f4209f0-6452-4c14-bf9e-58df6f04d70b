<template>
  <div class="dict-tools-page">
    <div class="page-header">
      <h1>字典工具使用样例</h1>
      <p>展示字典工具的各种使用方法和场景</p>
    </div>

    <!-- 全局状态展示 -->
    <div class="section">
      <h2>1. 全局字典状态</h2>
      <div class="status-grid">
        <div class="status-item">
          <span class="label">初始化状态:</span>
          <t-tag :theme="globalState.initialized ? 'success' : 'warning'">
            {{ globalState.initialized ? '已初始化' : '未初始化' }}
          </t-tag>
        </div>
        <div class="status-item">
          <span class="label">字典类型数:</span>
          <span>{{ globalState.dictTypes.length }}</span>
        </div>
        <div class="status-item">
          <span class="label">总数据条数:</span>
          <span>{{ globalState.totalDictCount }}</span>
        </div>
        <div class="status-item">
          <span class="label">初始化耗时:</span>
          <span>{{ globalState.initDuration }}ms</span>
        </div>
      </div>
      
      <div class="actions">
        <t-button @click="initGlobalDict" :loading="loading">初始化字典</t-button>
        <t-button @click="refreshState" theme="default">刷新状态</t-button>
      </div>
    </div>

    <!-- 字典类型列表 -->
    <div class="section">
      <h2>2. 字典类型列表</h2>
      <div class="dict-types">
        <div v-for="type in globalState.dictTypes" :key="type.id" class="dict-type-item">
          <div class="type-info">
            <span class="type-id">ID: {{ type.id }}</span>
            <span class="type-title">标题: {{ type.title }}</span>
            <span class="type-status">状态: {{ type.status === 0 ? '启用' : '禁用' }}</span>
          </div>
          <div class="type-actions">
            <t-button size="small" @click="loadTypeData(type.title)">查看数据</t-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 根据类型名称获取数据 -->
    <div class="section">
      <h2>3. 根据类型名称获取字典数据</h2>
      <div class="demo-form">
        <t-input v-model="typeTitle" placeholder="输入字典类型标题" style="width: 200px;" />
        <t-button @click="getDataByTitle" :loading="titleLoading">获取数据</t-button>
      </div>
      
      <div v-if="titleResult" class="result-display">
        <h3>结果: {{ typeTitle }}</h3>
        <div class="data-list">
          <div v-for="item in titleResult" :key="item.id" class="data-item">
            <span>ID: {{ item.id }}</span>
            <span>标签: {{ item.label }}</span>
            <span>值: {{ item.value }}</span>
            <span>排序: {{ item.sort }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 根据类型ID获取数据 -->
    <div class="section">
      <h2>4. 根据类型ID获取字典数据</h2>
      <div class="demo-form">
        <t-input-number v-model="typeId" placeholder="输入字典类型ID" style="width: 200px;" />
        <t-button @click="getDataById" :loading="idLoading">获取数据</t-button>
      </div>
      
      <div v-if="idResult" class="result-display">
        <h3>结果: 类型ID {{ typeId }}</h3>
        <div class="data-list">
          <div v-for="item in idResult" :key="item.id" class="data-item">
            <span>ID: {{ item.id }}</span>
            <span>标签: {{ item.label }}</span>
            <span>值: {{ item.value }}</span>
            <span>排序: {{ item.sort }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 标签转换示例 -->
    <div class="section">
      <h2>5. 字典值转标签示例</h2>
      <div class="demo-form">
        <t-select v-model="selectedType" placeholder="选择字典类型" style="width: 200px;">
          <t-option v-for="type in globalState.dictTypes" :key="type.id" :value="type.title" :label="type.title" />
        </t-select>
        <t-input v-model="inputValue" placeholder="输入字典值" style="width: 200px;" />
        <t-button @click="convertValue" :loading="convertLoading">转换</t-button>
      </div>
      
      <div v-if="convertedLabel" class="result-display">
        <h3>转换结果</h3>
        <p><strong>类型:</strong> {{ selectedType }}</p>
        <p><strong>值:</strong> {{ inputValue }}</p>
        <p><strong>标签:</strong> {{ convertedLabel }}</p>
      </div>
    </div>

    <!-- 表单组件使用示例 -->
    <div class="section">
      <h2>6. 表单组件使用示例</h2>
      
      <!-- 单选示例 -->
      <div class="form-example">
        <h3>单选组件</h3>
        <t-radio-group v-model="radioValue">
          <t-radio 
            v-for="option in courseNatureOptions" 
            :key="option.value" 
            :value="option.value"
          >
            {{ option.label }}
          </t-radio>
        </t-radio-group>
        <p>选中值: {{ radioValue }}</p>
      </div>

      <!-- 复选示例 -->
      <div class="form-example">
        <h3>复选组件</h3>
        <t-checkbox-group v-model="checkboxValues">
          <t-checkbox 
            v-for="option in teacherRoleOptions" 
            :key="option.value" 
            :value="option.value"
          >
            {{ option.label }}
          </t-checkbox>
        </t-checkbox-group>
        <p>选中值: {{ checkboxValues }}</p>
      </div>

      <!-- 下拉选择示例 -->
      <div class="form-example">
        <h3>下拉选择组件</h3>
        <t-select v-model="selectValue" placeholder="选择课程性质" style="width: 300px;">
          <t-option 
            v-for="option in courseNatureOptions" 
            :key="option.value" 
            :value="option.value" 
            :label="option.label" 
          />
        </t-select>
        <p>选中值: {{ selectValue }}</p>
      </div>

      <!-- 表格示例 -->
      <div class="form-example">
        <h3>表格中使用</h3>
        <t-table :data="tableData" :columns="tableColumns" />
      </div>
    </div>

    <!-- 代码示例 -->
    <div class="section">
      <h2>7. 代码示例</h2>
      <div class="code-examples">
        <div class="code-block">
          <h3>在Vue组件中使用字典工具</h3>
          <pre><code>{{ vueComponentExample }}</code></pre>
        </div>
        
        <div class="code-block">
          <h3>在Composition API中使用</h3>
          <pre><code>{{ compositionApiExample }}</code></pre>
        </div>
        
        <div class="code-block">
          <h3>在Options API中使用</h3>
          <pre><code>{{ optionsApiExample }}</code></pre>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import {
  Button as TButton,
  Input as TInput,
  InputNumber as TInputNumber,
  Select as TSelect,
  Option as TOption,
  Radio as TRadio,
  RadioGroup as TRadioGroup,
  Checkbox as TCheckbox,
  CheckboxGroup as TCheckboxGroup,
  Table as TTable,
  Tag as TTag,
  MessagePlugin
} from 'tdesign-vue-next'
import { 
  getGlobalDictState, 
  initializeGlobalDict, 
  getDictDataByTypeTitle,
  getDictData,
  getDictLabelByTypeTitle,
  getDictOptionsByTypeTitle
} from '@/utils/dictUtil'

// 响应式数据
const globalState = ref(getGlobalDictState())
const loading = ref(false)
const titleLoading = ref(false)
const idLoading = ref(false)
const convertLoading = ref(false)

// 表单数据
const typeTitle = ref('')
const typeId = ref<number>()
const titleResult = ref<any[]>([])
const idResult = ref<any[]>([])

// 标签转换
const selectedType = ref('')
const inputValue = ref('')
const convertedLabel = ref('')

// 表单组件示例
const radioValue = ref('')
const checkboxValues = ref<string[]>([])
const selectValue = ref('')

// 字典选项
const courseNatureOptions = ref<Array<{label: string, value: string}>>([])
const teacherRoleOptions = ref<Array<{label: string, value: string}>>([])

// 表格数据
const tableData = ref([
  { id: 1, name: '高等数学', nature: '必修', role: '1' },
  { id: 2, name: '线性代数', nature: '必修', role: '2' },
  { id: 3, name: '概率论', nature: '选修', role: '3' }
])

const tableColumns = ref([
  { colKey: 'name', title: '课程名称' },
  { 
    colKey: 'nature', 
    title: '课程性质',
    cell: (h: any, { row }: any) => {
      const option = courseNatureOptions.value.find(opt => opt.value === row.nature)
      return h('span', option?.label || row.nature)
    }
  },
  { 
    colKey: 'role', 
    title: '教师角色',
    cell: (h: any, { row }: any) => {
      const option = teacherRoleOptions.value.find(opt => opt.value === row.role)
      return h('span', option?.label || row.role)
    }
  }
])

// 代码示例
const vueComponentExample = `// 在Vue组件中使用字典工具
import { ref, onMounted } from 'vue'
import { getDictOptionsByTypeTitle, getDictLabelByTypeTitle } from '@/utils/dictUtil'

// 获取字典选项
const courseNatureOptions = ref([])

onMounted(async () => {
  // 获取课程性质选项
  courseNatureOptions.value = await getDictOptionsByTypeTitle('课程性质')
})

// 值转标签
const getNatureLabel = async (value: string) => {
  return await getDictLabelByTypeTitle('课程性质', value)
}`

const compositionApiExample = `// Composition API中使用
import { useDictByTypeTitle } from '@/hooks/useDict'

const { dictOptions: courseNatureOptions, loading, error } = useDictByTypeTitle('课程性质')

// 自动获取字典数据，支持响应式更新
const selectedValue = ref('')
const selectedLabel = computed(() => {
  const option = courseNatureOptions.value.find(opt => opt.value === selectedValue.value)
  return option?.label || selectedValue.value
})`

const optionsApiExample = `// Options API中使用
export default {
  data() {
    return {
      courseNatureOptions: [],
      selectedValue: ''
    }
  },
  async mounted() {
    // 获取字典选项
    this.courseNatureOptions = await getDictOptionsByTypeTitle('课程性质')
  },
  methods: {
    async getNatureLabel(value) {
      return await getDictLabelByTypeTitle('课程性质', value)
    }
  }
}`

// 方法
const initGlobalDict = async () => {
  loading.value = true
  try {
    await initializeGlobalDict(true)
    refreshState()
    await loadDictOptions()
    MessagePlugin.success('字典初始化成功')
  } catch (error) {
    MessagePlugin.error('字典初始化失败')
    console.error('初始化失败:', error)
  } finally {
    loading.value = false
  }
}

const refreshState = () => {
  globalState.value = getGlobalDictState()
}

const loadTypeData = async (title: string) => {
  typeTitle.value = title
  await getDataByTitle()
}

const getDataByTitle = async () => {
  if (!typeTitle.value) {
    MessagePlugin.warning('请输入字典类型标题')
    return
  }
  
  titleLoading.value = true
  try {
    titleResult.value = await getDictDataByTypeTitle(typeTitle.value)
    MessagePlugin.success(`获取到 ${titleResult.value.length} 条数据`)
  } catch (error) {
    MessagePlugin.error('获取数据失败')
    console.error('获取数据失败:', error)
  } finally {
    titleLoading.value = false
  }
}

const getDataById = async () => {
  if (!typeId.value) {
    MessagePlugin.warning('请输入字典类型ID')
    return
  }
  
  idLoading.value = true
  try {
    idResult.value = await getDictData(typeId.value)
    MessagePlugin.success(`获取到 ${idResult.value.length} 条数据`)
  } catch (error) {
    MessagePlugin.error('获取数据失败')
    console.error('获取数据失败:', error)
  } finally {
    idLoading.value = false
  }
}

const convertValue = async () => {
  if (!selectedType.value || !inputValue.value) {
    MessagePlugin.warning('请选择类型并输入值')
    return
  }
  
  convertLoading.value = true
  try {
    convertedLabel.value = await getDictLabelByTypeTitle(selectedType.value, inputValue.value)
    if (convertedLabel.value) {
      MessagePlugin.success('转换成功')
    } else {
      MessagePlugin.warning('未找到对应的标签')
    }
  } catch (error) {
    MessagePlugin.error('转换失败')
    console.error('转换失败:', error)
  } finally {
    convertLoading.value = false
  }
}

const loadDictOptions = async () => {
  try {
    courseNatureOptions.value = await getDictOptionsByTypeTitle('课程性质')
    teacherRoleOptions.value = await getDictOptionsByTypeTitle('教师角色')
  } catch (error) {
    console.error('加载字典选项失败:', error)
  }
}

// 页面初始化
onMounted(async () => {
  console.log('字典工具页面初始化')
  refreshState()
  
  // 如果字典未初始化，自动初始化
  if (!globalState.value.initialized) {
    await initGlobalDict()
  } else {
    await loadDictOptions()
  }
})
</script>

<style lang="less" scoped>
.dict-tools-page {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;

  .page-header {
    text-align: center;
    margin-bottom: 32px;

    h1 {
      font-size: 28px;
      font-weight: 600;
      color: var(--td-text-color-primary);
      margin-bottom: 8px;
    }

    p {
      color: var(--td-text-color-secondary);
      margin: 0;
    }
  }

  .section {
    background: var(--td-bg-color-container);
    border-radius: 8px;
    padding: 24px;
    margin-bottom: 24px;
    box-shadow: var(--td-shadow-1);

    h2 {
      color: var(--td-text-color-primary);
      margin-bottom: 16px;
      font-size: 18px;
    }

    h3 {
      color: var(--td-text-color-primary);
      margin-bottom: 12px;
      font-size: 16px;
    }
  }

  .status-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
    margin-bottom: 16px;

    .status-item {
      display: flex;
      align-items: center;
      gap: 8px;

      .label {
        color: var(--td-text-color-secondary);
        font-weight: 500;
      }
    }
  }

  .actions {
    display: flex;
    gap: 12px;
  }

  .dict-types {
    display: grid;
    gap: 12px;
  }

  .dict-type-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px;
    background: var(--td-bg-color-page);
    border-radius: 6px;
    border: 1px solid var(--td-border-level-1-color);

    .type-info {
      display: flex;
      gap: 16px;

      .type-id {
        font-weight: 600;
        color: var(--td-brand-color);
      }

      .type-title {
        color: var(--td-text-color-primary);
      }

      .type-status {
        color: var(--td-text-color-secondary);
      }
    }
  }

  .demo-form {
    display: flex;
    gap: 12px;
    align-items: center;
    margin-bottom: 16px;
  }

  .result-display {
    background: var(--td-bg-color-page);
    padding: 16px;
    border-radius: 6px;
    margin-top: 16px;

    .data-list {
      display: grid;
      gap: 8px;
    }

    .data-item {
      display: flex;
      gap: 16px;
      padding: 8px;
      background: var(--td-bg-color-container);
      border-radius: 4px;
      font-size: 14px;
      color: var(--td-text-color-secondary);
    }
  }

  .form-example {
    margin-bottom: 24px;
    padding: 16px;
    background: var(--td-bg-color-page);
    border-radius: 6px;

    h3 {
      margin-bottom: 12px;
    }

    p {
      margin-top: 8px;
      color: var(--td-text-color-secondary);
    }
  }

  .code-examples {
    display: grid;
    gap: 24px;
  }

  .code-block {
    h3 {
      margin-bottom: 12px;
    }

    pre {
      background: var(--td-bg-color-page);
      padding: 16px;
      border-radius: 6px;
      overflow-x: auto;
      font-family: 'Monaco', 'Consolas', monospace;
      font-size: 14px;
      line-height: 1.5;
      color: var(--td-text-color-primary);
      border: 1px solid var(--td-border-level-1-color);
    }
  }
}
</style>

<template>
  <div class="teaching-data-container">
    <!-- 头部信息 -->
    <div class="page-header">
      <div class="header-content">
        <div class="title-info">
          <h1 class="page-title">教学数据管理</h1>
          <p class="page-subtitle">管理课程教学数据与统计分析</p>
        </div>
        <div class="header-actions">
          <t-button theme="primary" @click="handleOpenGradeManagement(123, 0)">
            测试按课程目标录入
          </t-button>
          <t-button theme="default" @click="handleOpenGradeManagement(456, 1)">
            测试按考核题目录入
          </t-button>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="page-content">
      <!-- 统计指标区域 -->
      <div class="statistics-section">
        <div class="stats-grid">
          <div class="stat-card" v-for="(stat, index) in statisticsData" :key="stat.key" :style="{ '--delay': `${index * 0.1}s` }">
            <div class="stat-card-inner">
              <div class="stat-icon-wrapper" :class="stat.iconClass">
                <t-icon :name="stat.icon" size="28px" />
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ stat.value }}</div>
                <div class="stat-label">{{ stat.label }}</div>
                <div class="stat-trend" v-if="stat.trend">
                  <t-tag :theme="stat.trend.theme" size="small">
                    <template #icon>
                      <t-icon :name="stat.trend.icon" />
                    </template>
                    {{ stat.trend.text }}
                  </t-tag>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      



      <!-- 教学任务展示区域 - 全新设计 -->
      <div class="teaching-tasks-section">
        <!-- 区域标题 -->
        <div class="section-header" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px; padding: 20px; background: white; border-radius: 12px; box-shadow: 0 2px 8px rgba(0,0,0,0.04); border: 1px solid #e7e7e7;">
          <h3 class="section-title" style="display: flex; align-items: center; gap: 8px; margin: 0; font-size: 18px; font-weight: 600; color: #333;">
            <t-icon name="layers" class="title-icon" style="font-size: 20px; color: #0052d9;" />
            教学任务详情
          </h3>
          <div class="section-meta">
            <span class="task-count" style="font-size: 14px; color: #666; background: #f5f5f5; padding: 6px 12px; border-radius: 16px; font-weight: 500;">共 {{ taskDetailsList.length }} 个教学任务</span>
          </div>
        </div>

        <!-- 加载状态 -->
        <div v-if="loading" class="loading-state">
          <t-loading size="large" text="正在加载教学任务数据..." />
        </div>

        <!-- 空状态 -->
        <div v-else-if="!taskDetailsList || taskDetailsList.length === 0" class="empty-state">
          <div class="empty-content">
            <t-icon name="inbox" class="empty-icon" />
            <h4 class="empty-title">暂无教学任务数据</h4>
            <p class="empty-description">当前课程还没有配置教学任务信息</p>
          </div>
        </div>

        <!-- 教学任务卡片列表 -->
        <div v-else class="tasks-grid" style="display: grid; grid-template-columns: repeat(auto-fill, minmax(300px, 1fr)); gap: 20px; padding: 16px 0;">
          <div
            v-for="task in taskDetailsList"
            :key="task.taskId"
            class="task-card"
            style="background: white; border: 2px solid #e7e7e7; border-radius: 12px; box-shadow: 0 4px 12px rgba(0,0,0,0.1); margin-bottom: 16px; display: flex; flex-direction: column; min-height: 400px; transition: all 0.3s ease; cursor: pointer;"
          >
            <!-- 卡片头部 -->
            <div class="card-header" style="padding: 20px 20px 16px 20px; border-bottom: 1px solid #e7e7e7;">
              <div class="task-info" style="margin-bottom: 12px;">
                <h4 class="task-name" style="margin: 0 0 8px 0; font-size: 16px; font-weight: 600; color: #333;">{{ task.taskName }}</h4>
                <div class="task-badges" style="display: flex; gap: 6px; flex-wrap: wrap;">
                  <t-tag theme="primary" size="small">任务{{ task.taskNumber }}</t-tag>
                  <t-tag v-if="task.academicYear" theme="default" size="small">{{ task.academicYear }}</t-tag>
                </div>
              </div>
              <div class="task-schedule" style="display: flex; gap: 16px;">
                <span class="schedule-item" style="display: flex; align-items: center; gap: 4px; font-size: 13px; color: #666;">
                  <t-icon name="time" />
                  {{ task.teachWeek }}周
                </span>
                <span class="schedule-item" style="font-size: 13px; color: #666;">
                  {{ task.weekHours }}学时
                </span>
              </div>
            </div>

            <!-- 卡片内容 -->
            <div class="card-body" style="padding: 16px 20px; flex: 1; display: flex; flex-direction: column;">
              <!-- 教师信息 -->
              <div class="info-group" style="margin-bottom: 16px;">
                <div class="group-label" style="display: flex; align-items: center; gap: 6px; margin-bottom: 8px; font-size: 13px; font-weight: 500; color: #666;">
                  <t-icon name="user" />
                  授课教师
                </div>
                <div class="teachers-content" style="padding-left: 18px;">
                  <div v-if="task.teachers && task.teachers.length > 0" class="teachers-list">
                    <div
                      v-for="teacher in task.teachers"
                      :key="teacher.teacherId"
                      class="teacher-item"
                      style="display: flex; align-items: center; gap: 6px; margin-bottom: 4px;"
                    >
                      <t-tag
                        :theme="teacher.role === 1 ? 'primary' : 'default'"
                        size="small"
                      >
                        {{ teacher.teacherName }}
                      </t-tag>
                      <span class="teacher-role" style="font-size: 11px; color: #999;">{{ teacher.roleName }}</span>
                    </div>
                  </div>
                  <span v-else class="no-data" style="font-size: 12px; color: #999; font-style: italic;">暂无教师信息</span>
                </div>
              </div>

              <!-- 班级信息 -->
              <div class="info-group" style="margin-bottom: 16px;">
                <div class="group-label" style="display: flex; align-items: center; gap: 6px; margin-bottom: 8px; font-size: 13px; font-weight: 500; color: #666;">
                  <t-icon name="user-group" />
                  授课班级
                  <t-tag v-if="task.classCount" theme="success" size="small">{{ task.classCount }}个班级</t-tag>
                </div>
                <div class="classes-content" style="padding-left: 18px;">
                  <div v-if="task.classes && task.classes.length > 0" class="classes-list">
                    <div
                      v-for="classInfo in task.classes"
                      :key="classInfo.classId"
                      class="class-item"
                      style="display: flex; justify-content: space-between; align-items: center; padding: 4px 8px; margin-bottom: 4px; background: #f5f5f5; border-radius: 6px; font-size: 12px;"
                    >
                      <span class="class-name" style="font-weight: 500; color: #333;">{{ classInfo.className }}</span>
                      <span class="student-count" style="color: #666;">{{ classInfo.studentNumber }}人</span>
                    </div>
                  </div>
                  <span v-else class="no-data" style="font-size: 12px; color: #999; font-style: italic;">暂无班级信息</span>
                </div>
              </div>

              <!-- 统计信息 -->
              <div class="stats-bar" style="padding-top: 12px; border-top: 1px solid #e7e7e7; margin-top: auto;">
                <div class="stat-item" style="display: flex; justify-content: space-between; align-items: center;">
                  <span class="stat-label" style="font-size: 12px; color: #666;">总学生数</span>
                  <span class="stat-value" style="font-size: 14px; font-weight: 600; color: #0052d9;">{{ task.studentCount }}人</span>
                </div>
              </div>
            </div>

            <!-- 卡片底部操作 -->
            <div class="card-footer" style="padding: 16px 20px 20px 20px; display: flex; gap: 8px;">
              <t-button
                theme="primary"
                variant="outline"
                size="small"
                style="flex: 1; font-size: 12px;"
                @click="handleAssessmentDetail(task)"
              >
                <template #icon><t-icon name="edit-1" /></template>
                考核详情管理
              </t-button>
              <!-- <t-button
                theme="success"
                variant="outline"
                size="small"
                style="flex: 1; font-size: 12px;"
                @click="handleScoreDetail(task)"
              >
                <template #icon><t-icon name="chart-bubble" /></template>
                考核成绩管理
              </t-button> -->
            </div> 
          </div>
        </div>
      </div>
    </div>

    <!-- 成绩管理对话框 -->
    <!-- 按课程目标录入成绩对话框 (scoreType = 0) -->
    <DirectGradeEntryDialog
      v-model:visible="directGradeDialogVisible"
      :assessment-id="assessmentId || 0"
      @close="handleDirectGradeDialogClose"
    />

    <!-- 按考核题目详情录入成绩对话框 (scoreType = 1) -->
    <!-- <GradeManagementDialog
      :visible="detailedGradeDialogVisible"
      :assessment-content="currentAssessmentContent"
      :class-info="currentClassInfo"
      :score-status="1"
      :visible-question-types="[]"
      :all-question-types="[]"
      :all-question-type-names="[]"
      :score-params="{
        taskId: currentTaskId || 0,
        assessmentId: assessmentId || 0
      }"
      @close="handleDetailedGradeDialogClose"
      @update:visible-question-types="handleVisibleQuestionTypesChange"
    /> -->
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { MessagePlugin } from 'tdesign-vue-next';
import {
  getTaskWorkListByCourseId,
  type TaskWorkDetailVO,
  type TaskWorkStatisticsVO,
  type TaskStatisticsSummary
} from '@/api/teaching/task';

import GradeManagementDialog from '@/pages/assessment/components/GradeManagementDialog.vue';
import DirectGradeEntryDialog from '@/pages/assessment/components/DirectGradeEntryDialog.vue';

import { storage } from '@/utils/storage';

// 定义趋势类型
interface TrendInfo {
  theme: 'success' | 'danger' | 'default' | 'primary' | 'warning';
  icon: string;
  text: string;
}

// 定义统计数据项类型
interface StatisticItem {
  key: string;
  label: string;
  value: string | number;
  icon: string;
  iconClass: string;
  trend?: TrendInfo;
}

const route = useRoute();
const router = useRouter();
const courseId = ref(Number(route.params.courseId) || 1); // 确保有默认值
const courseName = ref(route.query.courseName as string || '未知课程');
const loading = ref(false);

// 成绩管理相关参数
const assessmentId = ref<number | null>(null);
const scoreType = ref<number | null>(null);

// 成绩管理对话框状态
const directGradeDialogVisible = ref(false);
const detailedGradeDialogVisible = ref(false);

// 成绩管理对话框所需数据
const currentAssessmentContent = ref<any>(null);
const currentClassInfo = ref<any>(null);
const currentTaskId = ref<number>(0);

// 处理可见题型变化
const handleVisibleQuestionTypesChange = (types: string[]) => {
  console.log('可见题型变化:', types);
  // 这里可以根据需要处理可见题型变化的逻辑
};



// 统计数据
const statistics = ref<TaskStatisticsSummary>({
  totalTasks: 0,
  taskGrowth: 0,
  totalTeachers: 0,
  teacherGrowth: 0,
  totalStudents: 0,
  studentGrowth: 0,
  activeClasses: 0,
  classGrowth: 0
});


// 格式化统计数据用于显示
const statisticsData = computed((): StatisticItem[] => [
  {
    key: 'totalTasks',
    label: '课程执行次数',
    value: statistics.value.totalTasks,
    icon: 'layers',
    iconClass: 'icon-blue',
    trend: statistics.value.taskGrowth > 0 ? {
      theme: 'success' as const,
      icon: 'arrow-up',
      text: `同比增长${statistics.value.taskGrowth}个`
    } : statistics.value.taskGrowth < 0 ? {
      theme: 'danger' as const,
      icon: 'arrow-down',
      text: `同比减少${Math.abs(statistics.value.taskGrowth)}个`
    } : undefined
  },
  {
    key: 'totalTaskCount',
    label: '教学任务总数',
    value: taskData.value.reduce((sum, task) => sum + (task.totalTaskCount || 0), 0),
    icon: 'task',
    iconClass: 'icon-purple',
    trend: undefined // 暂时没有历史数据计算趋势
  },
  {
    key: 'totalStudents',
    label: '学生总数',
    value: statistics.value.totalStudents,
    icon: 'user-avatar',
    iconClass: 'icon-orange',
    trend: {
      theme: statistics.value.studentGrowth > 0 ? 'success' as const : 
             statistics.value.studentGrowth < 0 ? 'danger' as const : 'default' as const,
      icon: statistics.value.studentGrowth > 0 ? 'arrow-up' : 
            statistics.value.studentGrowth < 0 ? 'arrow-down' : 'minus',
      text: statistics.value.studentGrowth > 0 ? 
        `同比增长${statistics.value.studentGrowth}%` : 
        statistics.value.studentGrowth < 0 ?
        `同比下降${Math.abs(statistics.value.studentGrowth)}%` :
        '与上期持平'
    }
  },
  {
    key: 'activeClasses',
    label: '班级总数',
    value: statistics.value.activeClasses,
    icon: 'view-module',
    iconClass: 'icon-green',
    trend: statistics.value.classGrowth === 0 ? {
      theme: 'default' as const,
      icon: 'minus',
      text: '与上期持平'
    } : statistics.value.classGrowth > 0 ? {
      theme: 'success' as const,
      icon: 'arrow-up',
      text: `同比增长${statistics.value.classGrowth}个`
    } : {
      theme: 'danger' as const,
      icon: 'arrow-down',
      text: `同比减少${Math.abs(statistics.value.classGrowth)}个`
    }
  }
]);

// 教学任务数据
const taskData = ref<TaskWorkStatisticsVO[]>([]);

// 教学任务详情数据（用于卡片展示）
const taskDetailsList = ref<TaskWorkDetailVO[]>([]);


// 加载教学任务列表数据
const loadTaskData = async () => {
  try {
    loading.value = true;
    console.log('开始加载教学任务列表数据');
    
    // 验证课程ID
    if (!courseId.value || courseId.value <= 0) {
      console.error('无效的课程ID:', courseId.value);
      MessagePlugin.error('无效的课程ID');
      return;
    }

    const response = await getTaskWorkListByCourseId(courseId.value);
    
    const responseData = response.data || {};
    console.log('API响应数据:', responseData);

    // 处理不同的响应格式
    let taskDetails = [];
    if (Array.isArray(responseData)) {
      // 如果直接返回数组
      taskDetails = responseData;
    } else if (responseData.records && Array.isArray(responseData.records)) {
      // 如果是分页格式
      taskDetails = responseData.records;
    } else if (responseData.data && Array.isArray(responseData.data)) {
      // 如果数据在data字段中
      taskDetails = responseData.data;
    }

    if (taskDetails && Array.isArray(taskDetails) && taskDetails.length > 0) {
      // getTaskWorkListByCourseId 直接返回 TaskWorkDetailVO[] 数组
      // 不需要从 TaskWorkStatisticsVO 中提取数据
      taskDetailsList.value = taskDetails.map((task: any) => {
        const classes = task.classes || [];
        const teachers = task.teachers || [];

        // 从班级数组中计算学生总数
        const calculatedStudentCount = classes.reduce((sum: number, cls: any) => {
          return sum + (cls.studentNumber || 0);
        }, 0);

        // 班级数量
        const calculatedClassCount = classes.length;

        console.log(`任务 ${task.taskName} - 班级:`, classes);
        console.log(`任务 ${task.taskName} - 计算的学生数:`, calculatedStudentCount);
        console.log(`任务 ${task.taskName} - 计算的班级数:`, calculatedClassCount);

        return {
          academicYear: task.academicYear || '',
          semester: task.semester || '',
          taskId: task.taskId || task.id,
          taskName: task.taskName || '未知任务',
          taskNumber: task.taskNumber || 0,
          teachWeek: task.teachWeek || 16,
          weekHours: task.weekHours || 4,
          classes: classes,
          teachers: teachers,
          studentCount: task.studentCount || calculatedStudentCount,
          classCount: task.classCount || calculatedClassCount
        };
      });

      console.log('教学任务详情数据:', taskDetailsList.value);

      // 为了保持兼容性，也设置 taskData（虽然现在不使用表格了）
      taskData.value = [];

      //taskWorkStatisticsData.value = taskDetails || null;

      // 计算统计数据
      calculateStatisticsSummary();
    } else {
      console.warn('响应数据格式不正确:', response);
      taskData.value = [];
      taskDetailsList.value = [];
    }
    
  } catch (error) {
    console.error('加载教学任务数据失败:', error);
    MessagePlugin.error(`加载教学任务数据失败: ${error.message || '网络错误'}`);
    taskData.value = [];
    taskDetailsList.value = [];
  } finally {
    loading.value = false;
  }
};

// 计算统计汇总信息
const calculateStatisticsSummary = () => {
  console.log('计算统计数据，基于 taskDetailsList:', taskDetailsList.value);

  // 增强健壮性检查
  if (!taskDetailsList.value || !Array.isArray(taskDetailsList.value)) {
    console.log('统计数据为空或无效，使用默认值');
    statistics.value = {
      totalTasks: 0,
      taskGrowth: 0,
      totalTeachers: 0,
      teacherGrowth: 0,
      totalStudents: 0,
      studentGrowth: 0,
      activeClasses: 0,
      classGrowth: 0
    };
    return;
  }

  // 从任务数组中计算统计信息
  const totalTasks = taskDetailsList.value.length;
  console.log('总任务数:', totalTasks);

  const totalStudents = taskDetailsList.value.reduce((sum, task) => {
    console.log(`任务 ${task.taskName} 学生数:`, task.studentCount);
    return sum + (task.studentCount || 0);
  }, 0);
  console.log('总学生数:', totalStudents);

  const totalClasses = taskDetailsList.value.reduce((sum, task) => {
    console.log(`任务 ${task.taskName} 班级数:`, task.classCount);
    return sum + (task.classCount || 0);
  }, 0);
  console.log('总班级数:', totalClasses);

  // 计算教师总数（去重）
  const allTeachers = new Set();
  taskDetailsList.value.forEach(task => {
    console.log(`任务 ${task.taskName} 教师:`, task.teachers);
    if (task.teachers && Array.isArray(task.teachers)) {
      task.teachers.forEach(teacher => {
        allTeachers.add(teacher.teacherId);
      });
    }
  });
  const totalTeachers = allTeachers.size;
  console.log('总教师数（去重）:', totalTeachers);
  
  statistics.value = {
    totalTasks: totalTasks,
    taskGrowth: 0, // 无法计算增长，因为没有历史数据
    totalTeachers: totalTeachers,
    teacherGrowth: 0,
    totalStudents: totalStudents,
    studentGrowth: 0, // 无法计算增长，因为没有历史数据
    activeClasses: totalClasses,
    classGrowth: 0
  };
  
  console.log('统计数据计算完成:', statistics.value);
};



// 卡片操作：考核详情管理
const handleAssessmentDetail = (task: TaskWorkDetailVO) => {
  try {
    // 跳转到考核详情管理页面
    router.push({
      name: 'LeaderCourseAssessmentContent',
      params: {
        courseId: courseId.value.toString(),
        taskId: task.taskId.toString()
      },
      query: {
        taskName: task.taskName,
        courseName: courseName.value,
        academicYear: task.academicYear,
        semester: task.semester
      }
    });
  } catch (error) {
    console.error('跳转考核详情管理页面失败:', error);
    MessagePlugin.error('跳转失败，请稍后重试');
  }
};

// 卡片操作：考核成绩管理
const handleScoreDetail = (task: TaskWorkDetailVO) => {
  try {
    // 跳转到考核成绩管理页面
    router.push({
      name: 'TaskScoreManagement',
      params: {
        courseId: courseId.value.toString(),
        taskId: task.taskId.toString()
      },
      query: {
        taskName: task.taskName,
        courseName: courseName.value,
        academicYear: task.academicYear,
        semester: task.semester
      }
    });
  } catch (error) {
    console.error('跳转考核成绩管理页面失败:', error);
    MessagePlugin.error('跳转失败，请稍后重试');
  }
};

// 成绩管理相关方法
const handleOpenGradeManagement = (assessmentIdParam: number, scoreTypeParam: number) => {
  assessmentId.value = assessmentIdParam;
  scoreType.value = scoreTypeParam;

  // 设置模拟的考核内容和班级信息
  currentAssessmentContent.value = {
    id: assessmentIdParam,
    title: `考核内容 - ID: ${assessmentIdParam}`,
    inputMode: scoreTypeParam === 0 ? 'direct' : 'detailed'
  };

  currentClassInfo.value = {
    className: '计算机科学与技术2021-1班',
    studentCount: 30
  };

  if (scoreTypeParam === 0) {
    // 直接录入模式 - 按课程目标录入
    directGradeDialogVisible.value = true;
  } else if (scoreTypeParam === 1) {
    // 详细录入模式 - 按考核题目录入
    detailedGradeDialogVisible.value = true;
  }
};

const handleDirectGradeDialogClose = () => {
  directGradeDialogVisible.value = false;
  assessmentId.value = null;
  scoreType.value = null;
  currentAssessmentContent.value = null;
  currentClassInfo.value = null;
};

const handleDetailedGradeDialogClose = () => {
  detailedGradeDialogVisible.value = false;
  assessmentId.value = null;
  scoreType.value = null;
  currentAssessmentContent.value = null;
  currentClassInfo.value = null;
};

// 检查路由参数并自动打开成绩管理对话框
const checkRouteParams = () => {
  const routeAssessmentId = route.query.assessmentId;
  const routeScoreType = route.query.scoreType;

  if (routeAssessmentId && routeScoreType) {
    const assessmentIdNum = Number(routeAssessmentId);
    const scoreTypeNum = Number(routeScoreType);

    if (!isNaN(assessmentIdNum) && !isNaN(scoreTypeNum)) {
      console.log('检测到成绩管理参数:', { assessmentId: assessmentIdNum, scoreType: scoreTypeNum });
      handleOpenGradeManagement(assessmentIdNum, scoreTypeNum);
    }
  }
};

onMounted(() => {
  console.log('教学数据管理页面加载，课程ID:', courseId.value);
  console.log('缓存中的数据是:', storage.get('courseCacheInfo'));

  // 加载统计数据和教学任务数据
  loadTaskData();

  // 检查路由参数
  checkRouteParams();
});
</script>

<style lang="less" scoped>
.teaching-data-container {
  padding: 20px;
  
  .page-header {
    background: white;
    border-radius: 12px;
    padding: 24px;
    margin-bottom: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    border: 1px solid rgba(0, 0, 0, 0.06);
    
    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .title-info {
        .page-title {
          font-size: 24px;
          font-weight: 600;
          margin: 0 0 8px 0;
          color: var(--td-text-color-primary);
        }
        
        .page-subtitle {
          font-size: 14px;
          color: var(--td-text-color-secondary);
          margin: 0;
        }
      }

      .header-actions {
        display: flex;
        gap: 12px;
        align-items: center;
      }
    }
  }
  
  .page-content {    
    // 统计指标区域
    .statistics-section {
      margin-bottom: 24px;
      
      .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
        gap: 20px;
        
        .stat-card {
          opacity: 0;
          transform: translateY(30px);
          animation: slideInUp 0.6s ease-out var(--delay) both;
          
          .stat-card-inner {
            background: white;
            border: 1px solid rgba(0, 0, 0, 0.08);
            border-radius: 16px;
            padding: 24px;
            display: flex;
            align-items: center;
            gap: 16px;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            height: 100%;
            
            &:hover {
              transform: translateY(-4px);
              box-shadow: 0 12px 32px rgba(0, 0, 0, 0.12);
              border-color: var(--td-brand-color-3);
            }
            
            &::before {
              content: '';
              position: absolute;
              top: 0;
              left: 0;
              right: 0;
              height: 3px;
              background: linear-gradient(90deg, var(--td-brand-color), var(--td-brand-color-6));
              opacity: 0;
              transition: opacity 0.3s ease;
            }
            
            &:hover::before {
              opacity: 1;
            }
            
            .stat-icon-wrapper {
              width: 56px;
              height: 56px;
              border-radius: 14px;
              display: flex;
              align-items: center;
              justify-content: center;
              flex-shrink: 0;
              transition: all 0.3s ease;
              
              &.icon-blue {
                background: linear-gradient(135deg, var(--td-brand-color-1), var(--td-brand-color-2));
                color: var(--td-brand-color);
              }
              
              &.icon-green {
                background: linear-gradient(135deg, var(--td-success-color-1), var(--td-success-color-2));
                color: var(--td-success-color);
              }
              
              &.icon-orange {
                background: linear-gradient(135deg, var(--td-warning-color-1), var(--td-warning-color-2));
                color: var(--td-warning-color);
              }
              
              &.icon-purple {
                background: linear-gradient(135deg, rgba(114, 46, 209, 0.1), rgba(114, 46, 209, 0.2));
                color: #722ed1;
              }
              
              :deep(.t-icon) {
                transition: transform 0.3s ease;
              }
            }
            
            &:hover .stat-icon-wrapper {
              transform: scale(1.1);
              
              :deep(.t-icon) {
                transform: rotate(5deg);
              }
            }
            
            .stat-content {
              flex: 1;
              
              .stat-number {
                font-size: 28px;
                font-weight: 700;
                color: var(--td-text-color-primary);
                margin-bottom: 4px;
                line-height: 1.2;
              }
              
              .stat-label {
                font-size: 14px;
                color: var(--td-text-color-secondary);
                margin-bottom: 8px;
                font-weight: 500;
              }
              
              .stat-trend {
                :deep(.t-tag) {
                  font-size: 12px;
                  border-radius: 12px;
                  padding: 4px 8px;
                  font-weight: 600;
                }
              }
            }
          }
        }
      }
    }
    
    .stat-card {
      position: relative;
      overflow: hidden;
      
      .stat-content {
        position: relative;
        z-index: 1;
        
        .stat-number {
          font-size: 28px;
          font-weight: 700;
          color: var(--td-text-color-primary);
        }
        
        .stat-label {
          font-size: 14px;
          color: var(--td-text-color-secondary);
          margin: 4px 0;
        }
        
        .stat-trend {
          margin-top: 8px;
        }
      }
      
      .stat-icon {
        position: absolute;
        right: 16px;
        top: 16px;
        font-size: 32px;
        opacity: 0.2;
        color: var(--td-brand-color);
      }
    }
    
    .card-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;
      
      .title-icon {
        margin-right: 8px;
        color: var(--td-brand-color);
      }
      
      .header-right {
        display: flex;
        align-items: center;
      }
    }
    
    // 数字显示样式
    .number-display {
      display: inline-flex;
      align-items: baseline;
      gap: 4px;
      font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      
      .number-value {
        font-size: 18px;
        font-weight: 600;
        color: var(--td-text-color-primary);
        position: relative;
        transition: all 0.3s ease;
      }
      
      .number-unit {
        font-size: 12px;
        color: var(--td-text-color-secondary);
        font-weight: 500;
      }
      
      &.task-count {
        .number-value {
          color: var(--td-brand-color);
          
          &::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, var(--td-brand-color), transparent);
            border-radius: 1px;
            opacity: 0;
            transition: opacity 0.3s ease;
          }
        }
        
        &:hover .number-value::after {
          opacity: 1;
        }
      }
      
      &.class-count {
        .number-value {
          color: var(--td-success-color);
          
          &::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, var(--td-success-color), transparent);
            border-radius: 1px;
            opacity: 0;
            transition: opacity 0.3s ease;
          }
        }
        
        &:hover .number-value::after {
          opacity: 1;
        }
      }
      
      &.student-count {
        .number-value {
          color: var(--td-warning-color);
          
          &::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, var(--td-warning-color), transparent);
            border-radius: 1px;
            opacity: 0;
            transition: opacity 0.3s ease;
          }
        }
        
        &:hover .number-value::after {
          opacity: 1;
        }
      }
      
      &.teacher-count {
        .number-value {
          color: #722ed1; // 紫色主题，与统计卡片保持一致
          
          &::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, #722ed1, transparent);
            border-radius: 1px;
            opacity: 0;
            transition: opacity 0.3s ease;
          }
        }
        
        &:hover .number-value::after {
          opacity: 1;
        }
      }
    }
  }
}

// 动画效果
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 表格单元格支持换行
:deep(.t-table) {
  td {
    white-space: normal;
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .teaching-data-container {
    .page-content {
      .statistics-section {
        .stats-grid {
          grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
          gap: 16px;
          
          .stat-card {
            .stat-card-inner {
              padding: 20px;
              gap: 14px;
              
              .stat-icon-wrapper {
                width: 48px;
                height: 48px;
                
                :deep(.t-icon) {
                  font-size: 24px;
                }
              }
              
              .stat-content {
                .stat-number {
                  font-size: 24px;
                }
                
                .stat-label {
                  font-size: 13px;
                }
              }
            }
          }
        }
      }
      
      // 响应式数字显示
      .number-display {
        .number-value {
          font-size: 16px;
        }
        
        .number-unit {
          font-size: 11px;
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .teaching-data-container {
    padding: 16px;
    
    .page-content {
      .statistics-section {
        .stats-grid {
          grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
          gap: 12px;
          
          .stat-card {
            .stat-card-inner {
              padding: 16px;
              gap: 12px;
              
              .stat-icon-wrapper {
                width: 40px;
                height: 40px;
                
                :deep(.t-icon) {
                  font-size: 20px;
                }
              }
              
              .stat-content {
                .stat-number {
                  font-size: 20px;
                }
                
                .stat-label {
                  font-size: 12px;
                }
                
                .stat-trend {
                  :deep(.t-tag) {
                    font-size: 11px;
                    padding: 2px 6px;
                  }
                }
              }
            }
          }
        }
      }
      
      // 移动端数字显示调整
      .number-display {
        .number-value {
          font-size: 14px;
        }
        
        .number-unit {
          font-size: 10px;
        }
      }
    }

  // 教学任务展示区域 - 全新设计
  .page-content {
    .teaching-tasks-section {
      margin-top: 24px;

      // 区域标题
      .section-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        padding-bottom: 16px;
        border-bottom: 1px solid var(--td-border-level-1-color);

        .section-title {
          display: flex;
          align-items: center;
          gap: 8px;
          margin: 0;
          font-size: 18px;
          font-weight: 600;
          color: var(--td-text-color-primary);

          .title-icon {
            font-size: 20px;
            color: var(--td-brand-color);
          }
        }

        .section-meta {
          .task-count {
            font-size: 14px;
            color: var(--td-text-color-secondary);
            background: var(--td-bg-color-container-hover);
            padding: 4px 12px;
            border-radius: 12px;
          }
        }
      }

      // 加载状态
      .loading-state {
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: 200px;
        padding: 40px 0;
      }

      // 空状态
      .empty-state {
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: 200px;
        padding: 40px 0;

        .empty-content {
          text-align: center;

          .empty-icon {
            font-size: 48px;
            color: var(--td-text-color-disabled);
            margin-bottom: 16px;
          }

          .empty-title {
            margin: 0 0 8px 0;
            font-size: 16px;
            font-weight: 500;
            color: var(--td-text-color-secondary);
          }

          .empty-description {
            margin: 0;
            font-size: 14px;
            color: var(--td-text-color-placeholder);
          }
        }
      }

      // 卡片网格布局
      .tasks-grid {
        display: grid !important;
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr)) !important;
        gap: 20px !important;
        padding: 16px 0;

        // 单个教学任务卡片
        .task-card {
          background: #ffffff !important;
          border: 2px solid #e7e7e7 !important;
          border-radius: 12px !important;
          overflow: hidden;
          transition: all 0.3s ease !important;
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
          margin-bottom: 16px;
          display: flex !important;
          flex-direction: column !important;
          min-height: 400px !important;
          cursor: pointer !important;

          &:hover {
            border-color: #0052d9 !important;
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15) !important;
            transform: translateY(-4px) !important;
          }

          // 卡片头部
          .card-header {
            padding: 20px 20px 16px 20px;
            border-bottom: 1px solid var(--td-border-level-1-color);

            .task-info {
              margin-bottom: 12px;

              .task-name {
                margin: 0 0 8px 0;
                font-size: 16px;
                font-weight: 600;
                color: var(--td-text-color-primary);
                line-height: 1.4;
              }

              .task-badges {
                display: flex;
                gap: 6px;
                flex-wrap: wrap;
              }
            }

            .task-schedule {
              display: flex;
              gap: 16px;

              .schedule-item {
                display: flex;
                align-items: center;
                gap: 4px;
                font-size: 13px;
                color: var(--td-text-color-secondary);

                :deep(.t-icon) {
                  font-size: 12px;
                }
              }
            }
          }

          // 卡片主体内容
          .card-body {
            padding: 16px 20px;

            .info-group {
              margin-bottom: 16px;

              &:last-child {
                margin-bottom: 0;
              }

              .group-label {
                display: flex;
                align-items: center;
                gap: 6px;
                margin-bottom: 8px;
                font-size: 13px;
                font-weight: 500;
                color: var(--td-text-color-secondary);

                :deep(.t-icon) {
                  font-size: 12px;
                }
              }

              .teachers-content,
              .classes-content {
                padding-left: 18px;

                .no-data {
                  font-size: 12px;
                  color: var(--td-text-color-placeholder);
                  font-style: italic;
                }
              }

              .teachers-list {
                .teacher-item {
                  display: flex;
                  align-items: center;
                  gap: 6px;
                  margin-bottom: 4px;

                  .teacher-role {
                    font-size: 11px;
                    color: var(--td-text-color-placeholder);
                  }
                }
              }

              .classes-list {
                .class-item {
                  display: flex;
                  justify-content: space-between;
                  align-items: center;
                  padding: 4px 8px;
                  margin-bottom: 4px;
                  background: var(--td-bg-color-container-hover);
                  border-radius: 6px;
                  font-size: 12px;

                  .class-name {
                    font-weight: 500;
                    color: var(--td-text-color-primary);
                  }

                  .student-count {
                    color: var(--td-text-color-secondary);
                  }
                }
              }
            }

            .stats-bar {
              padding-top: 12px;
              border-top: 1px solid var(--td-border-level-1-color);

              .stat-item {
                display: flex;
                justify-content: space-between;
                align-items: center;

                .stat-label {
                  font-size: 12px;
                  color: var(--td-text-color-secondary);
                }

                .stat-value {
                  font-size: 14px;
                  font-weight: 600;
                  color: var(--td-brand-color);
                }
              }
            }
          }

          // 卡片底部操作
          .card-footer {
            padding: 16px 20px 20px 20px;
            display: flex;
            gap: 8px;

            :deep(.t-button) {
              flex: 1;
              font-size: 12px;
            }
          }
        }
      }

    }

    // 响应式布局
    // 大屏幕 - 4列布局
    @media (min-width: 1400px) {
      .teaching-tasks-section {
        .tasks-grid {
          grid-template-columns: repeat(4, 1fr);
          gap: 24px;
        }
      }
    }

    // 中大屏幕 - 3列布局
    @media (min-width: 1200px) and (max-width: 1399px) {
      .teaching-tasks-section {
        .tasks-grid {
          grid-template-columns: repeat(3, 1fr);
          gap: 20px;
        }
      }
    }

    // 中等屏幕 - 2列布局
    @media (min-width: 769px) and (max-width: 1199px) {
      .teaching-tasks-section {
        .tasks-grid {
          grid-template-columns: repeat(2, 1fr);
          gap: 16px;
        }
      }
    }

    // 小屏幕 - 1列布局
    @media (max-width: 768px) {
      .teaching-tasks-section {
        .tasks-grid {
          grid-template-columns: 1fr;
          gap: 16px;

          .task-card {
            .card-footer {
              flex-direction: column;
              gap: 8px;

              :deep(.t-button) {
                width: 100%;
              }
            }
          }
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .teaching-data-container {
    .page-content {
      .statistics-section {
        .stats-grid {
          grid-template-columns: 1fr;
          gap: 12px;
        }
      }
    }
  }

  // 响应式布局
  // 大屏幕 - 4列布局
  @media (min-width: 1400px) {
    .teaching-tasks-section {
      .tasks-grid {
        grid-template-columns: repeat(4, 1fr);
        gap: 24px;
      }
    }
  }

  // 中大屏幕 - 3列布局
  @media (min-width: 1200px) and (max-width: 1399px) {
    .teaching-tasks-section {
      .tasks-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 20px;
      }
    }
  }

  // 中等屏幕 - 2列布局
  @media (min-width: 769px) and (max-width: 1199px) {
    .teaching-tasks-section {
      .tasks-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 16px;
      }
    }
  }

  // 小屏幕 - 1列布局
  @media (max-width: 768px) {
    .teaching-tasks-section {
      .tasks-grid {
        grid-template-columns: 1fr;
        gap: 16px;

        .task-card {
          .card-footer {
            flex-direction: column;
            gap: 8px;

            :deep(.t-button) {
              width: 100%;
            }
          }
        }
      }
    }
  }
  }
}
</style>

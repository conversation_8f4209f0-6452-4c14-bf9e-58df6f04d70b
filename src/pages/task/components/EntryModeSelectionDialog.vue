<template>
  <t-dialog
    v-model:visible="internalVisible"
    header="选择录入方式"
    width="400px"
    :on-confirm="handleConfirm"
    :on-close="handleCancel"
    :confirm-btn="{ content: '确定', theme: 'primary' }"
    :cancel-btn="{ content: '取消' }"
  >
    <div class="entry-mode-selection">
      <t-radio-group v-model="selectedMode" class="mode-options">
        <t-radio value="direct">
          <div class="mode-option">
            <t-icon name="edit-1" class="mode-icon" />
            <div class="mode-info">
              <div class="mode-title">直接录入</div>
              <div class="mode-description">按课程目标直接录入成绩</div>
            </div>
          </div>
        </t-radio>
        <t-radio value="detailed">
          <div class="mode-option">
            <t-icon name="list" class="mode-icon" />
            <div class="mode-info">
              <div class="mode-title">详细录入</div>
              <div class="mode-description">按考核题目详细录入成绩</div>
            </div>
          </div>
        </t-radio>
      </t-radio-group>
    </div>
  </t-dialog>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['update:visible', 'confirm']);

const internalVisible = ref(false);
const selectedMode = ref('direct');

watch(() => props.visible, (val) => {
  internalVisible.value = val;
  if (val) {
    selectedMode.value = 'direct';
  }
});

watch(internalVisible, (val) => {
  if (!val) {
    emit('update:visible', false);
  }
});

const handleConfirm = () => {
  emit('confirm', selectedMode.value);
  internalVisible.value = false;
};

const handleCancel = () => {
  internalVisible.value = false;
  emit('update:visible', false);
};
</script>

<style scoped>
.entry-mode-selection {
  padding: 20px 0;
}

.mode-options {
  width: 100%;
}

.mode-option {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.mode-option:hover {
  background-color: var(--td-bg-color-container-hover);
}

.mode-icon {
  font-size: 20px;
  margin-right: 12px;
  color: var(--td-brand-color);
}

.mode-info {
  flex: 1;
}

.mode-title {
  font-weight: 500;
  margin-bottom: 4px;
  color: var(--td-text-color-primary);
}

.mode-description {
  font-size: 12px;
  color: var(--td-text-color-secondary);
}

:deep(.t-radio) {
  width: 100%;
  margin-bottom: 8px;
}

:deep(.t-radio:last-child) {
  margin-bottom: 0;
}
</style>
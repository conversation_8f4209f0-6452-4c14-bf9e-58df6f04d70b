<template>
  <div class="teaching-data-container">
    <!-- 头部信息 -->
    <div class="page-header">
      <div class="header-content">
        <div class="title-info">
          <h1 class="page-title">教学数据管理</h1>
          <p class="page-subtitle">管理课程教学数据与统计分析</p>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="page-content">
      <!-- 统计指标区域 -->
      <div class="statistics-section">
        <div class="stats-grid">
          <div class="stat-card" v-for="(stat, index) in statisticsData" :key="stat.key" :style="{ '--delay': `${index * 0.1}s` }">
            <div class="stat-card-inner">
              <div class="stat-icon-wrapper" :class="stat.iconClass">
                <t-icon :name="stat.icon" size="28px" />
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ stat.value }}</div>
                <div class="stat-label">{{ stat.label }}</div>
                <div class="stat-trend" v-if="stat.trend">
                  <t-tag :theme="stat.trend.theme" size="small">
                    <template #icon>
                      <t-icon :name="stat.trend.icon" />
                    </template>
                    {{ stat.trend.text }}
                  </t-tag>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 教学任务列表 -->
      <t-card>
        <template #title>
          <div class="flex items-center w-full">
            <t-icon name="layers" class="mr-2 text-blue-500" />
            <span class="flex-1">教学任务管理</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
            <div class="header-right">
              <t-space>
                <t-select v-model="filterOptions.year" :options="yearOptions" placeholder="选择学年" style="width: 150px;"></t-select>
                <t-select v-model="filterOptions.term" :options="termOptions" placeholder="选择学期" style="width: 120px;"></t-select>
                <t-button theme="primary" @click="handleRefresh">
                  <template #icon>
                    <t-icon name="refresh" />
                  </template>
                  刷新
                </t-button>
              </t-space>
            </div>
          </div>
        </template>
        
        <t-table
          :data="taskData"
          :columns="taskColumns"
          :bordered="true"
          :hover="true"
          :loading="loading"
          row-key="taskId"
          :pagination="pagination"
          @page-change="handlePageChange"
        >
          <template #academicYear="{ row }">
            {{ row.academicYear || '-' }}学年
          </template>
          <template #semester="{ row }">
            {{ row.semester || '-' }}
          </template>
          <template #courseName="{ row }">
            {{ row.courseName || '-' }}
          </template>
          <template #totalTaskCount="{ row }">
            <div class="number-display task-count">
              <span class="number-value">{{ row.totalTaskCount || 0 }}</span>
              <span class="number-unit">个</span>
            </div>
          </template>
          <template #classCount="{ row }">
            <div class="number-display class-count">
              <span class="number-value">{{ row.totalClassCount || 0 }}</span>
              <span class="number-unit">个班</span>
            </div>
          </template>
          <template #totalStudentCount="{ row }">
            <div class="number-display student-count">
              <span class="number-value">{{ row.totalStudentCount || 0 }}</span>
              <span class="number-unit">人</span>
            </div>
          </template>
          <template #totalTeacherCount="{ row }">
            <div class="number-display teacher-count">
              <span class="number-value">{{ row.totalTeacherCount || 0 }}</span>
              <span class="number-unit">人</span>
            </div>
          </template>
          <template #teachers="{ row }">
            <div class="flex flex-col gap-1">
              <div v-for="(teacher, index) in row.teachers" :key="index" class="flex items-center gap-2">
                <t-tag size="small" :theme="teacher.roleName === '主讲教师' ? 'primary' : 'default'">
                  {{ teacher.teacherName }}
                </t-tag>
                <span class="text-xs text-gray-500">{{ teacher.roleName }}</span>
              </div>
            </div>
          </template>
          <template #operation="{ row }">
            <t-space size="small">
              <t-button theme="primary" variant="text" size="small" @click="handleViewTaskDetail(row)">
                <template #icon><t-icon name="view-module" /></template>
                教学任务详情
              </t-button>
              <t-button theme="primary" variant="text" size="small" @click="handleAssessmentManagement(row)">
                <template #icon><t-icon name="edit-1" /></template>
                考核管理
              </t-button>
              <t-button theme="success" variant="text" size="small" @click="handleScoreManagement(row)">
                <template #icon><t-icon name="chart-bubble" /></template>
                成绩管理
              </t-button>
              <t-button theme="warning" variant="text" size="small" @click="handleAchievementManagement(row)">
                <template #icon><t-icon name="chart-pie" /></template>
                达成度管理
              </t-button>
            </t-space>
          </template>
        </t-table>
      </t-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { MessagePlugin } from 'tdesign-vue-next';
import {
  getTaskWorkList,
  getTaskWorkListByTaskYear,
  type TaskWorkDetailVO,
  type TaskWorkStatisticsVO,
  type TaskStatisticsSummary
} from '@/api/teaching/task';

// 定义趋势类型
interface TrendInfo {
  theme: 'success' | 'danger' | 'default' | 'primary' | 'warning';
  icon: string;
  text: string;
}

// 定义统计数据项类型
interface StatisticItem {
  key: string;
  label: string;
  value: string | number;
  icon: string;
  iconClass: string;
  trend?: TrendInfo;
}

const route = useRoute();
const router = useRouter();
const courseId = ref(Number(route.params.courseId) || 1); // 确保有默认值
const courseName = ref(route.query.courseName as string || '未知课程');
const loading = ref(false);

// 分页配置
const pagination = ref({
  current: 1,
  pageSize: 10,
  total: 0,
  showJumper: true,
  showSizeChanger: true,
});

// 教学任务表格列配置
const taskColumns = computed(() => [
  { colKey: 'academicYear', title: '学年', width: 140 },
  { colKey: 'semester', title: '学期', width: 100 },
  { colKey: 'courseName', title: '课程名称', width: 200 },
  { colKey: 'totalTaskCount', title: '教学任务总数', width: 140, align: 'center' as const },
  { colKey: 'classCount', title: '班级数量', width: 120, align: 'center' as const },
  { colKey: 'totalStudentCount', title: '学生总数', width: 120, align: 'center' as const },
  { colKey: 'totalTeacherCount', title: '授课教师人数',width: 140, align: 'center' as const },
  { colKey: 'operation', title: '操作', width: 350, fixed: 'right' as const }
]);

// 统计数据
const statistics = ref<TaskStatisticsSummary>({
  totalTasks: 0,
  taskGrowth: 0,
  totalTeachers: 0,
  teacherGrowth: 0,
  totalStudents: 0,
  studentGrowth: 0,
  activeClasses: 0,
  classGrowth: 0
});


// 格式化统计数据用于显示
const statisticsData = computed((): StatisticItem[] => [
  {
    key: 'totalTasks',
    label: '课程执行次数',
    value: statistics.value.totalTasks,
    icon: 'layers',
    iconClass: 'icon-blue',
    trend: statistics.value.taskGrowth > 0 ? {
      theme: 'success' as const,
      icon: 'arrow-up',
      text: `同比增长${statistics.value.taskGrowth}个`
    } : statistics.value.taskGrowth < 0 ? {
      theme: 'danger' as const,
      icon: 'arrow-down',
      text: `同比减少${Math.abs(statistics.value.taskGrowth)}个`
    } : undefined
  },
  {
    key: 'totalTaskCount',
    label: '教学任务总数',
    value: taskData.value.reduce((sum, task) => sum + (task.totalTaskCount || 0), 0),
    icon: 'task',
    iconClass: 'icon-purple',
    trend: undefined // 暂时没有历史数据计算趋势
  },
  {
    key: 'totalStudents',
    label: '学生总数',
    value: statistics.value.totalStudents,
    icon: 'user-avatar',
    iconClass: 'icon-orange',
    trend: {
      theme: statistics.value.studentGrowth > 0 ? 'success' as const : 
             statistics.value.studentGrowth < 0 ? 'danger' as const : 'default' as const,
      icon: statistics.value.studentGrowth > 0 ? 'arrow-up' : 
            statistics.value.studentGrowth < 0 ? 'arrow-down' : 'minus',
      text: statistics.value.studentGrowth > 0 ? 
        `同比增长${statistics.value.studentGrowth}%` : 
        statistics.value.studentGrowth < 0 ?
        `同比下降${Math.abs(statistics.value.studentGrowth)}%` :
        '与上期持平'
    }
  },
  {
    key: 'activeClasses',
    label: '班级总数',
    value: statistics.value.activeClasses,
    icon: 'view-module',
    iconClass: 'icon-green',
    trend: statistics.value.classGrowth === 0 ? {
      theme: 'default' as const,
      icon: 'minus',
      text: '与上期持平'
    } : statistics.value.classGrowth > 0 ? {
      theme: 'success' as const,
      icon: 'arrow-up',
      text: `同比增长${statistics.value.classGrowth}个`
    } : {
      theme: 'danger' as const,
      icon: 'arrow-down',
      text: `同比减少${Math.abs(statistics.value.classGrowth)}个`
    }
  }
]);

// 学年选项
const yearOptions = ref([
  { label: '全部学年', value: null },
  { label: '2024-2025', value: '2024-2025' },
  { label: '2023-2024', value: '2023-2024' },
  { label: '2022-2023', value: '2022-2023' }
]);

// 学期选项
const termOptions = [
  { label: '全部学期', value: null },
  { label: '春季学期', value: 1 },
  { label: '秋季学期', value: 2 }
];

// 筛选选项
const filterOptions = ref({
  year: null,
  term: null
});

// 教学任务数据
const taskData = ref<TaskWorkStatisticsVO[]>([]);

// 处理分页变化
const handlePageChange = (pageInfo: any) => {
  pagination.value.current = pageInfo.current;
  pagination.value.pageSize = pageInfo.pageSize;
  loadTaskData();
};


// 加载教学任务列表数据
const loadTaskData = async () => {
  try {
    loading.value = true;
    console.log('开始加载教学任务列表数据');
    
    // 验证课程ID
    if (!courseId.value || courseId.value <= 0) {
      console.error('无效的课程ID:', courseId.value);
      MessagePlugin.error('无效的课程ID');
      return;
    }
    
    // 构建API参数，只有当筛选条件不为空时才传递
    const apiParams: any = {
      current: pagination.value.current,
      pageSize: pagination.value.pageSize
    };
    
    // 只有当年份不为空时才传递taskYear参数
    if (filterOptions.value.year) {
      apiParams.taskYear = convertYearStringToNumber(filterOptions.value.year);
    }
    
    // 只有当学期不为空时才传递taskTerm参数
    if (filterOptions.value.term !== null) {
      apiParams.taskTerm = filterOptions.value.term;
    }
    
    console.log('API调用参数:', apiParams);
    
    const response = await getTaskWorkList(courseId.value, apiParams);
    
    
    const responseData = response.data || {};
    const taskDetails = responseData.records || {};
    if (taskDetails  && Array.isArray(taskDetails)) {
      taskData.value = taskDetails.map((task: TaskWorkStatisticsVO) => ({
        ...task,
        // courseName: task.courseName || '未知课程名称', // 从后端获取或设置默认值
        // classCount: task.classIds?.length || 0,
        // semester: task.taskTerm === 1 ? '春季学期' : '秋季学期'
      }));
      console.log('任务列表响应:', taskData.value);
      pagination.value.total = responseData.total || 0;
      pagination.value.current = responseData.current || 1;
      pagination.value.pageSize = responseData.size || 10;
      //taskWorkStatisticsData.value = taskDetails || null;
      
      // 计算统计数据
      calculateStatisticsSummary();
      
      // 生成学年选项
      generateYearOptions();
    } else {
      console.warn('响应数据格式不正确:', response);
      taskData.value = [];
      pagination.value.total = 0;
    }
    
  } catch (error) {
    console.error('加载教学任务数据失败:', error);
    MessagePlugin.error(`加载教学任务数据失败: ${error.message || '网络错误'}`);
    taskData.value = [];
    pagination.value.total = 0;
  } finally {
    loading.value = false;
  }
};

// 计算统计汇总信息
const calculateStatisticsSummary = () => {
  console.log('计算统计数据，基于 taskData:', taskData.value);
  
  // 增强健壮性检查
  if (!taskData.value || !Array.isArray(taskData.value)) {
    console.log('统计数据为空或无效，使用默认值');
    statistics.value = {
      totalTasks: 0,
      taskGrowth: 0,
      totalTeachers: 0,
      teacherGrowth: 0,
      totalStudents: 0,
      studentGrowth: 0,
      activeClasses: 0,
      classGrowth: 0
    };
    return;
  }
  
  // 从任务数组中计算统计信息
  const totalTasks = taskData.value.length;
  const totalStudents = taskData.value.reduce((sum, task) => sum + (task.totalStudentCount || 0), 0);
  const totalClasses = taskData.value.reduce((sum, task) => sum + (task.totalClassCount || 0), 0);
  const totalTeachers = taskData.value.reduce((sum, task) => sum + (task.totalTeacherCount || 0), 0);
  
  statistics.value = {
    totalTasks: totalTasks,
    taskGrowth: 0, // 无法计算增长，因为没有历史数据
    totalTeachers: totalTeachers,
    teacherGrowth: 0,
    totalStudents: totalStudents,
    studentGrowth: 0, // 无法计算增长，因为没有历史数据
    activeClasses: totalClasses,
    classGrowth: 0
  };
  
  console.log('统计数据计算完成:', statistics.value);
};

// 动态生成学年选项
const generateYearOptions = () => {
  const years = new Set<string>();
  
  // 从当前统计数据中获取学年
  if (Array.isArray(taskData.value) && taskData.value.length > 0) {
    taskData.value.forEach(item => {
      if (item && item.academicYear) {
        years.add(item.academicYear);
      }
    });
  }
  
  // 如果没有从数据中获取到学年，使用默认的学年选项
  if (years.size === 0) {
    years.add('2024-2025');
    years.add('2023-2024');
    years.add('2022-2023');
  }
  
  yearOptions.value = [
    { label: '全部学年', value: null },
    ...Array.from(years).map(year => ({
      label: year,
      value: year
    })).sort((a, b) => b.value.localeCompare(a.value))
  ];
  
  // 保持默认值为空（全部）
  if (!filterOptions.value.year) {
    filterOptions.value.year = null;
  }
};
// 学年字符串转换为数字（如 "2023-2024" -> 2023）
const convertYearStringToNumber = (yearString: string | null): number | undefined => {
  if (!yearString) return undefined;
  const yearMatch = yearString.match(/^(\d{4})/);
  return yearMatch ? parseInt(yearMatch[1], 10) : undefined;
};

// 学期字符串转换为数字（如 "第一学期" -> 1, "第二学期" -> 2）
const convertTermStringToNumber = (termString: string): number => {
  if (!termString) return 1;
  if (termString.includes('春季') || termString.includes('1')) return 1;
  if (termString.includes('秋季') || termString.includes('2')) return 2;
  return 1; // 默认返回第一学期
};


// 刷新数据
const handleRefresh = () => {
  loadTaskData();
};

// 查看教学任务详情
const handleViewTaskDetail = (row: TaskWorkStatisticsVO) => {
  try {
    // 获取必要的参数
    //const courseId = row.courseId;
    //const courseName = row.courseName || '未知课程';
    
    // 处理学年和学期参数
    let year = row.taskYear;
    let term = row.taskTerm;
    
    // 如果行数据中没有这些参数，使用当前筛选条件或默认值
    if (!year) {
      // 如果当前筛选的年份为空，使用默认的当前学年
      if (filterOptions.value.year) {
        year = convertYearStringToNumber(filterOptions.value.year);
      } else {
        // 使用当前年份作为默认值
        year = new Date().getFullYear();
      }
    }
    if (!term && term !== 0) {
      // 如果当前筛选的学期为空，使用默认的第一学期
      if (filterOptions.value.term !== null) {
        term = filterOptions.value.term;
      } else {
        // 使用当前月份判断学期（1-8月为春季学期，9-12月为秋季学期）
        const currentMonth = new Date().getMonth() + 1;
        term = currentMonth <= 8 ? 1 : 2;
      }
    }
    
    console.log('跳转到教学任务详情页面:', {
      courseId,
      courseName,
      year,
      term
    });
    
    // 跳转到教学任务详情页面
    router.push({
      name: 'CourseSemesterTaskList',
      params: {
        courseId: courseId.value,
      },
      query: {
        courseName: courseName.value,
        year: year.toString(),
        term: term.toString()
      }
    });
  } catch (error) {
    console.error('跳转到教学任务详情页面失败:', error);
    MessagePlugin.error('跳转到教学任务详情页面失败');
  }
};



// 考核管理
const handleAssessmentManagement = (row: TaskWorkStatisticsVO) => {
  try {
    // 获取必要的参数
    const rowCourseId = row.courseId;
    const rowCourseName = row.courseName || '未知课程';
    const year = row.taskYear || '';
    const term = row.taskTerm || '';

    console.log('跳转到考核管理页面，参数:', {
      courseId: rowCourseId,
      courseName: rowCourseName,
      year: year,
      term: term,
      fromModel:'task',
      taskId: -1
    });

    // 跳转到考核管理页面
    router.push({
      name: 'Evaluation',
      params: {
        courseId: rowCourseId,
        taskId: -1 // 表示参与达成度计算的考核
      },
      query: {
        courseName: rowCourseName,
        year: year,
        term: term,
        isReturn: 'true'
      }
    });

    MessagePlugin.success(`正在跳转到课程"${rowCourseName}"的考核管理页面`);
  } catch (error) {
    console.error('跳转到考核管理页面失败:', error);
    MessagePlugin.error('跳转到考核管理页面失败');
  }
};

// 成绩管理
const handleScoreManagement = (row: TaskWorkDetailVO) => {
  MessagePlugin.info(`成绩管理: ${row.taskName}`);
  // 这里可以跳转到成绩管理页面
  // router.push(`/teaching/score/${row.id}`);
};

// 达成度管理
const handleAchievementManagement = (row: TaskWorkDetailVO) => {
  MessagePlugin.info(`达成度管理: ${row.taskName}`);
  // 这里可以跳转到达成度管理页面
  // router.push(`/teaching/achievement/${row.id}`);
};

// 监听筛选条件变化
watch(
  () => [filterOptions.value.year, filterOptions.value.term],
  () => {
    pagination.value.current = 1; // 重置页码
    loadTaskData();
  },
  { deep: true }
);

onMounted(() => {
  console.log('教学数据管理页面加载，课程ID:', courseId.value);
  
  // 加载统计数据和教学任务数据
  loadTaskData();
});
</script>

<style lang="less" scoped>
.teaching-data-container {
  padding: 20px;
  
  .page-header {
    background: white;
    border-radius: 12px;
    padding: 24px;
    margin-bottom: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    border: 1px solid rgba(0, 0, 0, 0.06);
    
    .header-content {
      .title-info {
        .page-title {
          font-size: 24px;
          font-weight: 600;
          margin: 0 0 8px 0;
          color: var(--td-text-color-primary);
        }
        
        .page-subtitle {
          font-size: 14px;
          color: var(--td-text-color-secondary);
          margin: 0;
        }
      }
    }
  }
  
  .page-content {    
    // 统计指标区域
    .statistics-section {
      margin-bottom: 24px;
      
      .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
        gap: 20px;
        
        .stat-card {
          opacity: 0;
          transform: translateY(30px);
          animation: slideInUp 0.6s ease-out var(--delay) both;
          
          .stat-card-inner {
            background: white;
            border: 1px solid rgba(0, 0, 0, 0.08);
            border-radius: 16px;
            padding: 24px;
            display: flex;
            align-items: center;
            gap: 16px;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            height: 100%;
            
            &:hover {
              transform: translateY(-4px);
              box-shadow: 0 12px 32px rgba(0, 0, 0, 0.12);
              border-color: var(--td-brand-color-3);
            }
            
            &::before {
              content: '';
              position: absolute;
              top: 0;
              left: 0;
              right: 0;
              height: 3px;
              background: linear-gradient(90deg, var(--td-brand-color), var(--td-brand-color-6));
              opacity: 0;
              transition: opacity 0.3s ease;
            }
            
            &:hover::before {
              opacity: 1;
            }
            
            .stat-icon-wrapper {
              width: 56px;
              height: 56px;
              border-radius: 14px;
              display: flex;
              align-items: center;
              justify-content: center;
              flex-shrink: 0;
              transition: all 0.3s ease;
              
              &.icon-blue {
                background: linear-gradient(135deg, var(--td-brand-color-1), var(--td-brand-color-2));
                color: var(--td-brand-color);
              }
              
              &.icon-green {
                background: linear-gradient(135deg, var(--td-success-color-1), var(--td-success-color-2));
                color: var(--td-success-color);
              }
              
              &.icon-orange {
                background: linear-gradient(135deg, var(--td-warning-color-1), var(--td-warning-color-2));
                color: var(--td-warning-color);
              }
              
              &.icon-purple {
                background: linear-gradient(135deg, rgba(114, 46, 209, 0.1), rgba(114, 46, 209, 0.2));
                color: #722ed1;
              }
              
              :deep(.t-icon) {
                transition: transform 0.3s ease;
              }
            }
            
            &:hover .stat-icon-wrapper {
              transform: scale(1.1);
              
              :deep(.t-icon) {
                transform: rotate(5deg);
              }
            }
            
            .stat-content {
              flex: 1;
              
              .stat-number {
                font-size: 28px;
                font-weight: 700;
                color: var(--td-text-color-primary);
                margin-bottom: 4px;
                line-height: 1.2;
              }
              
              .stat-label {
                font-size: 14px;
                color: var(--td-text-color-secondary);
                margin-bottom: 8px;
                font-weight: 500;
              }
              
              .stat-trend {
                :deep(.t-tag) {
                  font-size: 12px;
                  border-radius: 12px;
                  padding: 4px 8px;
                  font-weight: 600;
                }
              }
            }
          }
        }
      }
    }
    
    .stat-card {
      position: relative;
      overflow: hidden;
      
      .stat-content {
        position: relative;
        z-index: 1;
        
        .stat-number {
          font-size: 28px;
          font-weight: 700;
          color: var(--td-text-color-primary);
        }
        
        .stat-label {
          font-size: 14px;
          color: var(--td-text-color-secondary);
          margin: 4px 0;
        }
        
        .stat-trend {
          margin-top: 8px;
        }
      }
      
      .stat-icon {
        position: absolute;
        right: 16px;
        top: 16px;
        font-size: 32px;
        opacity: 0.2;
        color: var(--td-brand-color);
      }
    }
    
    .card-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;
      
      .title-icon {
        margin-right: 8px;
        color: var(--td-brand-color);
      }
      
      .header-right {
        display: flex;
        align-items: center;
      }
    }
    
    // 数字显示样式
    .number-display {
      display: inline-flex;
      align-items: baseline;
      gap: 4px;
      font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      
      .number-value {
        font-size: 18px;
        font-weight: 600;
        color: var(--td-text-color-primary);
        position: relative;
        transition: all 0.3s ease;
      }
      
      .number-unit {
        font-size: 12px;
        color: var(--td-text-color-secondary);
        font-weight: 500;
      }
      
      &.task-count {
        .number-value {
          color: var(--td-brand-color);
          
          &::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, var(--td-brand-color), transparent);
            border-radius: 1px;
            opacity: 0;
            transition: opacity 0.3s ease;
          }
        }
        
        &:hover .number-value::after {
          opacity: 1;
        }
      }
      
      &.class-count {
        .number-value {
          color: var(--td-success-color);
          
          &::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, var(--td-success-color), transparent);
            border-radius: 1px;
            opacity: 0;
            transition: opacity 0.3s ease;
          }
        }
        
        &:hover .number-value::after {
          opacity: 1;
        }
      }
      
      &.student-count {
        .number-value {
          color: var(--td-warning-color);
          
          &::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, var(--td-warning-color), transparent);
            border-radius: 1px;
            opacity: 0;
            transition: opacity 0.3s ease;
          }
        }
        
        &:hover .number-value::after {
          opacity: 1;
        }
      }
      
      &.teacher-count {
        .number-value {
          color: #722ed1; // 紫色主题，与统计卡片保持一致
          
          &::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, #722ed1, transparent);
            border-radius: 1px;
            opacity: 0;
            transition: opacity 0.3s ease;
          }
        }
        
        &:hover .number-value::after {
          opacity: 1;
        }
      }
    }
  }
}

// 动画效果
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 表格单元格支持换行
:deep(.t-table) {
  td {
    white-space: normal;
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .teaching-data-container {
    .page-content {
      .statistics-section {
        .stats-grid {
          grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
          gap: 16px;
          
          .stat-card {
            .stat-card-inner {
              padding: 20px;
              gap: 14px;
              
              .stat-icon-wrapper {
                width: 48px;
                height: 48px;
                
                :deep(.t-icon) {
                  font-size: 24px;
                }
              }
              
              .stat-content {
                .stat-number {
                  font-size: 24px;
                }
                
                .stat-label {
                  font-size: 13px;
                }
              }
            }
          }
        }
      }
      
      // 响应式数字显示
      .number-display {
        .number-value {
          font-size: 16px;
        }
        
        .number-unit {
          font-size: 11px;
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .teaching-data-container {
    padding: 16px;
    
    .page-content {
      .statistics-section {
        .stats-grid {
          grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
          gap: 12px;
          
          .stat-card {
            .stat-card-inner {
              padding: 16px;
              gap: 12px;
              
              .stat-icon-wrapper {
                width: 40px;
                height: 40px;
                
                :deep(.t-icon) {
                  font-size: 20px;
                }
              }
              
              .stat-content {
                .stat-number {
                  font-size: 20px;
                }
                
                .stat-label {
                  font-size: 12px;
                }
                
                .stat-trend {
                  :deep(.t-tag) {
                    font-size: 11px;
                    padding: 2px 6px;
                  }
                }
              }
            }
          }
        }
      }
      
      // 移动端数字显示调整
      .number-display {
        .number-value {
          font-size: 14px;
        }
        
        .number-unit {
          font-size: 10px;
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .teaching-data-container {
    .page-content {
      .statistics-section {
        .stats-grid {
          grid-template-columns: 1fr;
          
          .stat-card {
            .stat-card-inner {
              padding: 20px;
              
              .stat-icon-wrapper {
                width: 48px;
                height: 48px;
                
                :deep(.t-icon) {
                  font-size: 24px;
                }
              }
              
              .stat-content {
                .stat-number {
                  font-size: 24px;
                }
                
                .stat-label {
                  font-size: 14px;
                }
              }
            }
          }
        }
      }
    }
  }
}
</style>

<template>
  <div class="grade-management-container">
    <!-- 头部操作区域 - 更新样式 -->
    <div class="header-actions">
      <div class="action-area">
        <t-button variant="outline" @click="exportTemplate">
          <template #icon><t-icon name="file-excel" /></template>
          下载成绩模板
        </t-button>
        <t-button variant="outline" @click="handleExportProblemTemplate">
          <template #icon><t-icon name="file-excel" /></template>
          下载题目模板
        </t-button>
        <t-button variant="outline" @click="exportTargetScoreTemplate">
          <template #icon><t-icon name="file-excel" /></template>
          下载目标成绩模板
        </t-button>
        <t-button theme="primary" @click="openAssessmentEditor">
          <template #icon><t-icon name="setting" /></template>
          考核项管理
        </t-button>
      </div>
    </div>

    <!-- 分类考核方式展示 - 更新为卡片式布局 -->
    <div
      v-for="(category) in assessmentCategories"
      :key="category.id"
      class="category-container"
    >
      <div class="category-header">
        <div class="category-title-section">
          <h3>{{ category.name }}</h3>
          <t-tag theme="warning" shape="round">
            权重：{{ category.weight }}%
          </t-tag>
        </div>
      </div>

      <div class="indicator-list">
        <div v-for="indicator in category.indicators" :key="indicator.id" class="indicator-card">
          <div class="indicator-header">
            <div class="indicator-title">
              <span class="indicator-name">{{ indicator.name }}</span>
              <div class="indicator-tags">
                <t-tag v-if="indicator.importedFile" theme="success" size="small">
                  已导入
                </t-tag>
                <t-tag v-if="indicator.importedProblemFiles && indicator.importedProblemFiles.length > 0" theme="primary" size="small">
                  {{ indicator.importedProblemFiles.length }}个题目文件
                </t-tag>
                <t-tag theme="warning" size="small">
                  权重: {{ indicator.weight || 0 }}%
                </t-tag>
              </div>
            </div>
            <div class="indicator-actions">
              <!-- 预览成绩按钮 -->
              <t-button
                v-if="!indicator.isImportNode && indicator.scores && indicator.scores.length > 0"
                variant="text"
                theme="default"
                size="small"
                @click="previewIndicatorScores(indicator, category.id)"
              >
                <template #icon><t-icon name="browse" /></template>
                预览成绩
              </t-button>
              
              <!-- 导入试题按钮 -->
              <t-upload
                v-if="!indicator.isImportNode"
                action=""
                :before-upload="(file) => handleImportProblems(file, indicator)"
                accept=".xls,.xlsx"
                :showUploadProgress="false"
                theme="custom"
              >
                <t-button variant="outline" theme="warning" size="small" class="import-btn">
                  <template #icon><t-icon name="code" /></template>
                  导入试题
                </t-button>
              </t-upload>
              
              <!-- 导入成绩按钮 -->
              <t-upload
                v-if="!indicator.isImportNode"
                action=""
                :before-upload="(file) => directImportScore(file, indicator.name)"
                accept=".xls,.xlsx"
                :showUploadProgress="false"
                theme="custom"
              >
                <t-button variant="outline" theme="primary" size="small" class="import-btn">
                  <template #icon><t-icon name="upload" /></template>
                  导入成绩
                </t-button>
              </t-upload>
              
              <!-- 导入目标成绩按钮 (仅在期末考试指标点显示) -->
              <t-upload
                v-if="indicator.name === '期末考试'"
                action=""
                :before-upload="(file) => importTargetScore(file, indicator.name)"
                accept=".xls,.xlsx"
                :showUploadProgress="false"
                theme="custom"
              >
                <t-button variant="outline" theme="warning" size="small" class="import-btn">
                  <template #icon><t-icon name="view-module" /></template>
                  导入目标成绩
                </t-button>
              </t-upload>
            </div>
          </div>

          <!-- 已导入的题目文件列表 -->
          <div
            v-if="indicator.importedProblemFiles && indicator.importedProblemFiles.length"
            class="imported-files-section"
          >
            <div class="files-header">
              <div class="files-title">
                <t-icon name="file-excel" /> 已导入的题目文件 ({{ indicator.importedProblemFiles.length }})
              </div>
              <div class="files-info">
                <t-tag theme="warning" size="small">
                  已用权重: {{ getIndicatorUsedWeight(indicator) }}%
                </t-tag>
                <t-tag theme="success" size="small">
                  剩余权重: {{ indicator.weight - getIndicatorUsedWeight(indicator) }}%
                </t-tag>
                <t-dropdown :options="[{ content: '按名称排序', value: 'name' }, { content: '按时间排序', value: 'time' }]" @click="sortProblemFiles(indicator, $event)">
                  <t-button variant="text" theme="primary" size="small">
                    <t-icon name="sort" />排序
                  </t-button>
                </t-dropdown>
              </div>
            </div>

            <div class="file-items-container">
              <div
                v-for="file in indicator.importedProblemFiles"
                :key="file.id"
                class="imported-file-item"
              >
                <div class="file-info">
                  <div class="file-header">
                    <span class="file-name">{{ file.fileName }}</span>
                    <div class="file-tags">
                      <t-tag theme="success" size="small">{{ file.problems?.length || 0 }}道题目</t-tag>
                      <t-tag v-if="file.scores && file.scores.length > 0" theme="primary" size="small">已导入{{ file.scores.length }}份成绩</t-tag>
                    </div>
                  </div>
                  <span class="file-date">导入时间: {{ file.importDate }}</span>
                  <!-- 题目列表预览 (前5个) -->
                  <div v-if="file.problems && file.problems.length > 0" class="problems-preview">
                    <div class="problems-header">
                      <span>题目预览:</span>
                      <span v-if="file.problems.length > 5" class="view-all" @click="handlePreviewProblemFile(file, indicator)">
                        查看全部 <t-icon name="chevron-right" />
                      </span>
                    </div>
                    <div class="problems-grid">
                      <div
                        v-for="(problem, index) in file.problems.slice(0, 5)"
                        :key="index"
                        class="problem-item"
                      >
                        {{problem.number}}. {{problem.title}}
                      </div>
                    </div>
                  </div>
                </div>
                
                <div class="file-actions">
                  <div class="weight-editor">
                    <t-button size="small" variant="text" @click="decreaseFileWeight(file)">
                      <t-icon name="remove" />
                    </t-button>
                    <span class="weight-value">{{ file.weight || 0 }}%</span>
                    <t-button size="small" variant="text" @click="increaseFileWeight(file, indicator)">
                      <t-icon name="add" />
                    </t-button>
                    <t-button theme="primary" size="small" @click="saveFileWeight(file, indicator)">
                      <t-icon name="check" />保存
                    </t-button>
                  </div>
                  
                  <div class="action-buttons">
                    <!-- 导入试题按钮 -->
                    <t-upload
                      action=""
                      :before-upload="(uploadFile) => handleImportProblems(uploadFile, indicator)"
                      accept=".xls,.xlsx"
                      :showUploadProgress="false"
                      theme="custom"
                    >
                      <t-button
                        size="small"
                        theme="warning"
                      >
                        <t-icon name="code" /> 导入试题
                      </t-button>
                    </t-upload>
                    <!-- 导入成绩按钮 -->
                    <t-upload
                      action=""
                      :before-upload="(uploadFile) => importScoreForFile(uploadFile, file, indicator)"
                      accept=".xls,.xlsx"
                      :showUploadProgress="false"
                      theme="custom"
                    >
                      <t-button
                        size="small"
                        theme="primary"
                      >
                        <t-icon name="upload" /> 导入成绩
                      </t-button>
                    </t-upload>
                    <t-button
                      size="small"
                      theme="default"
                      @click="handlePreviewProblemFile(file, indicator)"
                    >
                      <t-icon name="browse" /> 预览题目
                    </t-button>
                    <t-button
                      size="small"
                      theme="danger"
                      @click="handleDeleteProblemFile(file, indicator)"
                    >
                      <t-icon name="delete" /> 删除
                    </t-button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 成绩预览样式美化 -->
    <div v-if="selectedIndicator && selectedIndicator.isPreviewVisible" class="score-preview-card">
      <div class="preview-header">
        <div class="preview-title">
          <h4>
            {{ getIndicatorById(
              assessmentCategories.find(c => c.id === selectedIndicator.categoryId),
              selectedIndicator.id
            )?.name }} 成绩预览
          </h4>
          <div class="imported-files-info">
            <span v-if="selectedIndicator.importedFile" class="imported-file">
              导入文件: {{ selectedIndicator.importedFile }}
            </span>
            <span v-if="selectedIndicator.targetImportedFile" class="target-imported-file">
              目标成绩文件: {{ selectedIndicator.targetImportedFile }}
            </span>
          </div>
        </div>
        <t-button theme="default" variant="text" shape="square" @click="closePreview">
          <t-icon name="close" />
        </t-button>
      </div>
      <t-table
        :key="scoreColumns.length + (selectedIndicator && selectedIndicator.id ? selectedIndicator.id : '')"
        :data="selectedIndicator.scores || []"
        :columns="scoreColumns"
        row-key="studentId"
        bordered
        stripe
        :loading="loading"
        size="small"
        empty="暂无成绩数据"
      >
        <template #problem_default="{ row, col }">
          {{ getProblemScore(row, col) }}
        </template>
        
        <!-- 显示目标分数的列 -->
        <template #score="{ row }">
          <div class="score-comparison">
            <span :class="getScoreClass(row.score)">{{ row.score }}</span>
            <span v-if="selectedIndicator.showTargetScores && getTargetScore(row.studentId)" class="target-score">
              / <span class="target-value">{{ getTargetScore(row.studentId) }}</span>
            </span>
          </div>
        </template>
      </t-table>
    </div>

    <!-- 学生综合成绩表格 -->
    <div class="total-scores-container">
      <h3>学生综合成绩表</h3>
      <t-table
        :data="filteredStudents"
        :columns="columns"
        row-key="studentId"
        bordered
        stripe
        :loading="loading"
      >
        <template #studentName="{ row }">
          <div class="student-info">
            <span class="name">{{ row.name }}</span>
            <span class="student-id">{{ row.studentId }}</span>
          </div>
        </template>
        <template #totalScore="{ row }">
          <div :class="getScoreClass(row.totalScore)">
            {{ row.totalScore }}
          </div>
        </template>
      </t-table>
    </div>

    <!-- 权重设置对话框 -->
    <t-dialog v-model:visible="weightDialogVisible" header="评分权重设置" :width="600" :footer="false" class="weight-dialog">
      <div class="weight-setting-container">
        <div class="editor-header">
          <div class="editor-title">
            <t-icon name="chart-bubble" size="20px" style="color: #0052d9; margin-right: 8px;"/>
            <span>配置各考核方式权重，评分更加科学</span>
          </div>
          <t-tag theme="primary" variant="light">权重总和必须为100%</t-tag>
        </div>

        <!-- 主考核方式 -->
        <div class="main-assessment">
          <div class="main-assessment-item" v-for="(category, index) in tempAssessmentCategories" :key="index">
            <div class="category-header">
              <t-input class="category-name" v-model="category.name" placeholder="考核方式名称" />
              <div class="weight-editor">
                <t-input-number
                  v-model="category.weight"
                  theme="normal"
                  step="1"
                  size="small"
                  class="weight-input"
                  @change="validateMainWeights"
                >
                  <template #suffix>%</template>
                </t-input-number>
              </div>
            </div>
            <div class="indicators-container">
              <div class="indicator-item" v-for="indicator in category.indicators" :key="indicator.id">
                <t-icon name="discount" size="small" style="color: #0052d9;"/>
                <span class="indicator-name">{{ indicator.name }}</span>
                <t-tag theme="success" size="small">权重: {{ indicator.weight }}%</t-tag>
              </div>
            </div>
            <div class="validation-info" :class="{'valid': Math.abs(category.indicators.reduce((sum: number, i: Indicator) => sum + i.weight, 0) - 100) <= 0.1, 'invalid': Math.abs(category.indicators.reduce((sum: number, i: Indicator) => sum + i.weight, 0) - 100) > 0.1}">
              当前考核项权重合计：{{ category.indicators.reduce((sum: number, i: Indicator) => sum + i.weight, 0) }}%
              <span v-if="Math.abs(category.indicators.reduce((sum: number, i: Indicator) => sum + i.weight, 0) - 100) > 0.1" class="error-hint">
                （必须为100%）
              </span>
            </div>
          </div>
        </div>
        <div class="total-validation" :class="{'valid': Math.abs(totalMainWeight - 100) <= 0.1, 'invalid': Math.abs(totalMainWeight - 100) > 0.1}">
          <t-icon 
            :name="Math.abs(totalMainWeight - 100) <= 0.1 ? 'check-circle-filled' : 'error-circle-filled'" 
            :style="{ color: Math.abs(totalMainWeight - 100) <= 0.1 ? '#00a870' : '#e34d59' }"
          />
          <span>主考核总权重：{{ totalMainWeight }}%</span>
          <span v-if="Math.abs(totalMainWeight - 100) > 0.1" class="error-text">（权重总和必须为100%）</span>
        </div>
        <div class="dialog-footer">
          <t-button theme="default" @click="cancelWeights">取消</t-button>
          <t-button theme="primary" @click="saveWeights" :disabled="!isWeightValid">保存</t-button>
        </div>
      </div>
    </t-dialog>

    <!-- 考核方式管理对话框 -->
    <t-dialog v-model:visible="assessmentEditorVisible" header="考核项管理" :width="650" :footer="false" class="assessment-editor-dialog">
      <div class="assessment-editor-container">
        <div class="editor-header">
          <div class="editor-title">
            <t-icon name="setting" size="20px" style="color: #0052d9; margin-right: 8px;"/>
            <span>管理考核方式与考核项，权重设置合理将获得更准确的成绩评估</span>
          </div>
          <t-tag theme="primary" variant="light">权重总和必须为100%</t-tag>
        </div>

        <!-- 考核方式列表 -->
        <div class="assessment-categories">
          <div v-for="(category, categoryIndex) in editingCategories" :key="category.id" class="assessment-category">
            <div class="category-editor">
              <div class="category-editor-header">
                <div class="category-editor-name">
                  <span class="category-fixed-name">{{ category.name }}</span>
                  <t-tag theme="warning" size="small">权重: {{ category.weight }}%</t-tag>
                </div>
              </div>

              <!-- 指标点列表 -->
              <div class="indicators-editor">
                <div v-for="(indicator, indicatorIndex) in category.indicators" :key="indicator.id" class="indicator-editor">
                  <t-input v-model="indicator.name" placeholder="考核项名称" class="indicator-name-input" />
                  <div class="indicator-editor-actions">
                    <t-input-number 
                      v-model="indicator.weight" 
                      theme="normal" 
                      step="1" 
                      size="small" 
                      min="0" 
                      max="100"
                      class="weight-input"
                    >
                      <template #suffix>%</template>
                    </t-input-number>
                    <t-button theme="danger" variant="text" shape="circle" @click="removeIndicator(category, indicatorIndex)" class="delete-btn">
                      <t-icon name="delete" />
                    </t-button>
                  </div>
                </div>

                <!-- 添加指标点按钮 -->
                <div class="add-indicator-container">
                  <t-button theme="primary" variant="dashed" block @click="addIndicator(category)" class="add-indicator-btn">
                    <template #icon><t-icon name="add" /></template>
                    添加考核项
                  </t-button>
                </div>
              </div>

              <div class="category-validation-info">
                <div class="validation-result">
                  <span>考核项权重合计：</span>
                  <span :class="Math.abs(getIndicatorTotalWeight(category) - 100) <= 0.1 ? 'valid-weight' : 'invalid-weight'">
                    {{ getIndicatorTotalWeight(category) }}%
                  </span>
                  <span v-if="Math.abs(getIndicatorTotalWeight(category) - 100) > 0.1" class="error-hint">
                    （必须为100%）
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 编辑器底部信息 -->
        <div class="editor-footer">
          <div class="validation-summary">
            <t-icon 
              :name="isAssessmentEditValid ? 'check-circle-filled' : 'error-circle-filled'" 
              :style="{ color: isAssessmentEditValid ? '#00a870' : '#e34d59' }"
            />
            <span>
              {{ isAssessmentEditValid ? '权重设置合理' : '存在权重分配问题，请检查上方红色提示' }}
            </span>
          </div>

          <!-- 底部按钮 -->
          <div class="dialog-btn-group">
            <t-button theme="default" @click="cancelAssessmentEdit">取消</t-button>
            <t-button theme="primary" @click="saveAssessmentEdit" :disabled="!isAssessmentEditValid">保存</t-button>
          </div>
        </div>
      </div>
    </t-dialog>



    <!-- 题目模板使用指南对话框 -->
    <t-dialog
      v-if="templateDialogReady"
      v-model:visible="templateGuideVisible"
      header="题目导入模板使用指南"
      :width="600"
      :footer="true"
      @confirm="confirmExportTemplate"
      confirm-btn="确认下载"
      cancel-btn="取消"
      class="t-dialog-template"
    >
      <div class="template-guide-container">
        <h4>模板格式说明</h4>
        <p>题目导入模板包含以下列：</p>
        <ul class="guide-list">
          <li><strong>试题编号</strong>：题目的唯一编号，例如 1, 2, 3...</li>
          <li><strong>试题类型</strong>：如"选择题"、"填空题"、"证明题"等</li>
          <li><strong>题干</strong>：题目的具体内容</li>
          <li><strong>答案</strong>：题目的标准答案</li>
          <li><strong>课程目标1-4</strong>：该题对应的课程目标分值，请填写数字</li>
        </ul>

        <div class="warning-section">
          <p>
            <t-icon name="error-circle-filled" class="warning-icon" />
            重要提示:
          </p>
          <ul>
            <li>请勿修改Excel的表头结构和名称</li>
            <li>第一行的"试题编号"为合并单元格</li>
            <li>每个数字列表示该题的得分</li>
            <li>学号和姓名必须填写</li>
            <li>所有得分必须为数字</li>
          </ul>
        </div>
      </div>
    </t-dialog>

    <!-- 成绩模板使用指南对话框 -->
    <t-dialog
      v-if="scoreTemplateDialogReady"
      v-model:visible="scoreTemplateGuideVisible"
      header="成绩导入模板使用指南"
      :width="600"
      :footer="true"
      @confirm="confirmExportScoreTemplate"
      confirm-btn="确认下载"
      cancel-btn="取消"
      class="t-dialog-template"
    >
      <div class="template-guide-container">
        <h4>成绩导入模板格式说明</h4>
        <p>成绩模板包含以下列：</p>
        <ul class="guide-list">
          <li><strong>序号</strong>：行序号，用于标识每条记录</li>
          <li><strong>学号</strong>：学生的唯一标识</li>
          <li><strong>姓名</strong>：学生的姓名</li>
          <li><strong>试题编号</strong>：合并单元格表头，下方包含各题号列</li>
        </ul>

        <div class="template-example">
          <h5>模板表头格式</h5>
          <div class="example-table">
            <table>
              <tbody>
              <tr>
                <th>序号</th>
                <th>学号</th>
                <th>姓名</th>
                <th colspan="6">试题编号</th>
              </tr>
              <tr>
                <td></td>
                <td></td>
                <td></td>
                <td>1-1</td>
                <td>2-1</td>
                <td>2-2</td>
                <td>3-1</td>
                <td>3-2</td>
                <td>3-3</td>
              </tr>
              <tr>
                <td>1</td>
                <td>111</td>
                <td>张三</td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td>20</td>
                <td>0</td>
                <td>8</td>
              </tr>
              <tr>
                <td>2</td>
                <td>222</td>
                <td>李四</td>
                <td>13</td>
                <td>14</td>
                <td>5</td>
                <td>22</td>
                <td>0</td>
                <td>9</td>
              </tr>
              </tbody>
            </table>
          </div>
        </div>

        <div class="warning-section">
          <p>
            <t-icon name="error-circle-filled" class="warning-icon" />
            重要提示:
          </p>
          <ul>
            <li>请勿修改Excel的表头结构和名称</li>
            <li>第一行的"试题编号"为合并单元格</li>
            <li>第二行包含具体的题号（如"1-1"、"2-1"等）</li>
            <li>学号和姓名必须填写</li>
            <li>所有得分必须为数字</li>
          </ul>
        </div>
      </div>
    </t-dialog>
    
    <!-- 目标成绩模板使用指南对话框 -->
    <t-dialog
      v-if="targetScoreTemplateDialogReady"
      v-model:visible="targetScoreTemplateGuideVisible"
      header="目标成绩导入模板使用指南"
      :width="600"
      :footer="true"
      @confirm="confirmExportTargetScoreTemplate"
      confirm-btn="确认下载"
      cancel-btn="取消"
      class="t-dialog-template"
    >
      <div class="template-guide-container">
        <h4>目标成绩导入模板格式说明</h4>
        <p>目标成绩模板包含以下列：</p>
        <ul class="guide-list">
          <li><strong>序号</strong>：行序号，用于标识每条记录</li>
          <li><strong>学号</strong>：学生的唯一标识</li>
          <li><strong>姓名</strong>：学生的姓名</li>
          <li><strong>目标分数</strong>：学生的目标达成分数</li>
        </ul>

        <div class="template-example">
          <h5>模板表头格式</h5>
          <div class="example-table">
            <table>
              <tbody>
              <tr>
                <th>序号</th>
                <th>学号</th>
                <th>姓名</th>
                <th>目标分数</th>
              </tr>
              <tr>
                <td>1</td>
                <td>111</td>
                <td>张三</td>
                <td>85</td>
              </tr>
              <tr>
                <td>2</td>
                <td>222</td>
                <td>李四</td>
                <td>90</td>
              </tr>
              </tbody>
            </table>
          </div>
        </div>

        <div class="warning-section">
          <p>
            <t-icon name="error-circle-filled" class="warning-icon" />
            重要提示:
          </p>
          <ul>
            <li>请勿修改Excel的表头结构和名称</li>
            <li>学号和姓名必须填写</li>
            <li>目标分数必须为数字</li>
            <li>通常目标分数应在60-100之间</li>
          </ul>
        </div>
      </div>
    </t-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch, nextTick, reactive } from 'vue'
import {
  MessagePlugin,
  Icon as TIcon,
  Button as TButton,
  InputNumber as TInputNumber,
  Input as TInput,
  Select as TSelect,
  Option as TOption,
  Dialog as TDialog,
  Tag as TTag,
  Upload as TUpload,
  DialogPlugin,
  Dropdown as TDropdown
} from 'tdesign-vue-next'
// 使用全局引入的XLSX
import * as XLSX from 'xlsx'
// 导入指标点存储
import { useIndicatorStore } from '@/store/modules/indicator'
// 使用正确的相对路径，改用default import

import { exportProblemTemplate, importProblems } from '@/utils/problemFileHandler'
import { ImportedProblemFile, StudentScore } from '@/store/modules/indicator'
// 导入成绩模板导出和成绩导入函数
import { exportScoreTemplate, importScores } from '@/utils/scoreFileHandler'
import { useRouter, useRoute } from 'vue-router'

const router = useRouter();
const route = useRoute();

// 获取URL参数
const courseId = ref(route.params.courseId);
const selectedClass = ref(route.query.classId || 'all');

// 班级列表数据
const classList = ref([
  { label: '全部班级', value: 'all' },
  { label: 'R8软工1班241', value: '241' },
  { label: 'R8软工1班242', value: '242' },
  { label: 'R8软工1班243', value: '243' },
  { label: 'R8软工1班244', value: '244' },
]);

// 监听班级选择变化
watch(selectedClass, (newValue) => {
  // 更新URL参数，保留当前路径
  router.push({
    path: `/teachers/course/${courseId.value}/score`,
    query: { classId: newValue }
  });
  
  // 根据所选班级重新加载数据
  loadAssessmentData();
});

// 加载考核数据
const loadAssessmentData = async () => {
  try {
    // 这里根据courseId和班级ID加载对应数据
    console.log(`加载课程${courseId.value}班级${selectedClass.value}的考核数据`);
    // 实际应用中应该调用API获取数据
    // const data = await api.getAssessmentData(courseId.value, selectedClass.value);
    // 更新assessmentCategories
  } catch (error) {
    MessagePlugin.error('加载考核数据失败');
    console.error(error);
  }
};

// 初始化时加载数据
onMounted(() => {
  // 确保从URL查询参数同步班级选择
  const classIdFromQuery = route.query.classId;
  if (classIdFromQuery) {
    selectedClass.value = classIdFromQuery as string;
  }
  
  loadAssessmentData();
});

// 初始化存储
const indicatorStore = useIndicatorStore()

// 用于强制刷新树视图的计数器
const treeRefreshCounter = ref(0)

// 强制刷新树视图
const refreshTreeView = () => {
  treeRefreshCounter.value++
  console.log('树视图刷新计数器:', treeRefreshCounter.value)
}

// 权重设置对话框
const weightDialogVisible = ref(false)
const tempAssessmentCategories = ref([])

// 考核方式管理对话框
const assessmentEditorVisible = ref(false)
const editingCategories = ref([])

// 学生数据
const students = ref([])
const loading = ref(false)

// 成绩列
const scoreColumns = computed(() => {
  const cols = [
    { colKey: 'studentId', title: '学号', width: 120 },
    { colKey: 'name', title: '姓名', width: 120 },
  ];

  // 获取当前选中指标点
  if (selectedIndicator.value) {
    // 如果指标点有题目数据
    const indicator = getIndicatorById(
      assessmentCategories.value.find(c => c.id === selectedIndicator.value.categoryId),
      selectedIndicator.value.id
    );

    // 如果指标点有问题列表，添加问题列
    if (indicator && indicator.problems && indicator.problems.length > 0) {
      indicator.problems.forEach(problem => {
        cols.push({
          colKey: `problem_${problem.number || problem.title}`,
          title: problem.title || `题目${problem.number}`,
          width: 100
        });
      });
    }

    // 添加总分列
    cols.push({ colKey: 'total', title: '成绩得分', width: 100 });
  }

  return cols;
});


// 根据ID获取指标点
const getIndicatorById = (category: Category, indicatorId: string): Indicator | undefined => {
  return category.indicators.find(i => i.id === indicatorId)
}

// 获取指标点索引
const getIndicatorIndex = (category: Category, indicatorId: string): number => {
  return category.indicators.findIndex((i: Indicator) => i.id === indicatorId)
}

// 考核指标定义 - 改为从 store 中获取
const assessmentCategories = ref([])

// 权重验证
const totalMainWeight = computed(() =>
  tempAssessmentCategories.value.reduce((sum, c) => sum + c.weight, 0)
)

const isWeightValid = computed(() => {
  // 验证主考核权重
  if (Math.abs(totalMainWeight.value - 100) > 0.1) return false

  // 验证各考核方式内部权重
  return tempAssessmentCategories.value.every(category => {
    const total = category.indicators.reduce((sum: number, i: Indicator) => sum + i.weight, 0)
    // 每个分类内的指标点权重总和需要是100%
    return Math.abs(total - 100) < 0.1
  })
})

// 权重验证方法
const validateMainWeights = () => {
  if (totalMainWeight.value > 100) {
    MessagePlugin.warning('主考核权重总和不能超过100%')
    // 如果超过100%，自动调整为100%
    const overflow = totalMainWeight.value - 100
    const lastCategory = tempAssessmentCategories.value[tempAssessmentCategories.value.length - 1]
    if (lastCategory) {
      lastCategory.weight = Math.max(0, lastCategory.weight - overflow)
    }
  }
}

const validateIndicatorWeights = (categoryIndex: number) => {
  const category = tempAssessmentCategories.value[categoryIndex]
  const total = category.indicators.reduce((sum: number, i: Indicator) => sum + i.weight, 0)

  if (total > 100) {
    MessagePlugin.warning(`${category.name}内部权重总和不能超过100%`)
    // 如果超过100%，自动调整最后一个指标点的权重
    const overflow = total - 100
    const lastIndicator = category.indicators[category.indicators.length - 1]
    if (lastIndicator) {
      lastIndicator.weight = Math.max(0, lastIndicator.weight - overflow)
    }
  }
}

// 打开权重设置
const openWeightSetting = () => {
  // 创建深拷贝用于编辑
  tempAssessmentCategories.value = JSON.parse(JSON.stringify(assessmentCategories.value))

  // 确保所有权重都是数字类型
  tempAssessmentCategories.value.forEach(category => {
    if (typeof category.weight !== 'number') {
      category.weight = Number(category.weight) || 0;
    }

    category.indicators.forEach((indicator: Indicator) => {
      if (typeof indicator.weight !== 'number') {
        indicator.weight = Number(indicator.weight) || 0;
      }
    });
  });

  console.log('打开权重设置，当前权重数据:', tempAssessmentCategories.value);
  weightDialogVisible.value = true
}

// 取消权重设置
const cancelWeights = () => {
  weightDialogVisible.value = false
}

// 保存权重设置
const saveWeights = () => {
  // 验证主权重
  if (Math.abs(totalMainWeight.value - 100) > 0.1) {
    MessagePlugin.warning(`主考核权重总和必须为100%，当前为${totalMainWeight.value}%`)
    return
  }

  // 验证各分类内部指标点权重
  const invalidCategories = tempAssessmentCategories.value.filter(category => {
    const total = category.indicators.reduce((sum: number, i: Indicator) => sum + i.weight, 0)
    return Math.abs(total - 100) > 0.1
  })

  if (invalidCategories.length > 0) {
    const categoryNames = invalidCategories.map(c => c.name).join('、')
    MessagePlugin.warning(`以下分类的指标点权重总和不等于100%: ${categoryNames}`)
    return
  }

  // 确保临时权重数据中的指标点都有categoryId
  tempAssessmentCategories.value.forEach(category => {
    // 确保权重是数字
    category.weight = Number(category.weight);

    category.indicators.forEach((indicator: Indicator) => {
      indicator.categoryId = category.id;
      // 确保权重是数字
      indicator.weight = Number(indicator.weight);
      console.log(`设置指标点 ${indicator.name} 的categoryId为 ${category.id}，权重为 ${indicator.weight}%`)
    })
  })

  // 更新权重设置
  assessmentCategories.value = JSON.parse(JSON.stringify(tempAssessmentCategories.value))

  // 同步到 store
  indicatorStore.updateCategories(assessmentCategories.value)

  // 重新计算总分
  recalculateAllScores()

  weightDialogVisible.value = false
  MessagePlugin.success('权重设置已保存')

  // 再次确认所有指标点数据
  console.log('保存权重后的指标点数据:', JSON.stringify(assessmentCategories.value, null, 2))
  // 刷新树视图
  refreshTreeView()
}

// 选中的指标点
const selectedIndicator = ref(null)

// 处理节点点击
const handleNodeClick = (context: any) => {
  const node = context.node
  console.log('节点点击:', node, '权重:', node.data?.weight)

  // 如果是导入节点，显示其成绩数据
  if (node.data?.isImportNode && node.data.parentId) {
    // 找到父指标点
    const parentId = node.data.parentId
    const categoryId = node.data.categoryId

    // 从所有分类中查找父指标点
    for (const category of assessmentCategories.value) {
      if (category.id === categoryId) {
        const indicator = category.indicators.find((i: Indicator) => i.id === parentId)
        if (indicator) {
          selectedIndicator.value = {
            id: indicator.id,
            categoryId: category.id,
            scores: indicator.scores || [],
            importedFile: indicator.importedFile,
            isPreviewVisible: true
          }
          return
        }
      }
    }
    return
  }

  // 如果是指标点节点(无子节点或子节点只包含导入的成绩)
  if (!node.children || (node.children && node.data?.hasImportedScores)) {
    if (node.data?.id) {
      // 从节点数据中获取categoryId
      const categoryId = node.data.categoryId
      const indicatorId = node.data.id

      if (!categoryId) {
        console.error('节点缺少categoryId:', node.data)
        return
      }

      // 找到对应的分类
      const category = assessmentCategories.value.find(c => c.id === categoryId)
      if (!category) {
        console.error(`找不到ID为 ${categoryId} 的分类`)
        return
      }

      // 在分类中找到对应的指标点
      const indicator = category.indicators.find((i: Indicator) => i.id === indicatorId)
      if (!indicator) {
        console.error(`在分类 ${category.name} 中找不到ID为 ${indicatorId} 的指标点`)
        return
      }

      // 设置选中的指标点，但不自动显示预览
      selectedIndicator.value = {
        id: indicator.id,
        categoryId: category.id,
        scores: indicator.scores || [],
        importedFile: indicator.importedFile,
        isPreviewVisible: false
      }
    }
  }
}

// 表格列定义
const columns = computed(() => {
  const baseColumns = [
    { colKey: 'studentId', title: '学号', width: 120 },
    { colKey: 'name', title: '姓名', width: 100 },
    { colKey: 'totalScore', title: '成绩得分', width: 80 },
  ]

  // 动态添加考核指标列
  const dynamicColumns = assessmentCategories.value.flatMap(category =>
    category.indicators.map((indicator: Indicator) => ({
      colKey: indicator.id,
      title: `${category.name}-${indicator.name}`,
      width: 150
    }))
  )

  return [...baseColumns, ...dynamicColumns]
})

// 根据分数返回样式类
const getScoreClass = (score: number): string => {
  if (score >= 85) return 'score-excellent'
  if (score >= 60) return 'score-pass'
  return 'score-fail'
}

// 生成树状数据
const getTreeData = (category: Category) => {
  console.log(`正在为分类 ${category.name} 生成树，其指标点有:`, category.indicators)

  const treeData = [{
    label: category.name,
    children: category.indicators.map((indicator: Indicator) => {
      // 检查指标点数据完整性
      if (indicator.weight === undefined) {
        console.warn(`警告: 指标点 ${indicator.name} 缺少权重值，设置默认权重25`)
        indicator.weight = 25
      }

      console.log(`为指标点 [${indicator.name}] 创建树节点, ID: ${indicator.id}, 权重: ${indicator.weight}%, 分类: ${category.name}`)

      // 检查是否有导入的成绩
      const hasImportedScores = indicator.scores && indicator.scores.length > 0;
      const childNodes = [];

      // 如果有导入的成绩，添加子节点
      if (hasImportedScores && indicator.importedFile) {
        childNodes.push({
          label: `${indicator.importedFile} (${indicator.scores.length}条记录)`,
          data: {
            id: `${indicator.id}-import`,
            parentId: indicator.id,
            categoryId: category.id,
            isImportNode: true,
            scores: indicator.scores
          }
        });
      }

      // 确保权重值存在且为数字
      const nodeWeight = typeof indicator.weight === 'number' ? indicator.weight : 0;

      return {
        label: indicator.name,
        data: {
          id: indicator.id,
          weight: nodeWeight,
          preset: indicator.preset,
          importedFile: indicator.importedFile,
          categoryId: category.id,
          hasImportedScores
        },
        children: childNodes.length > 0 ? childNodes : null
      }
    })
  }]

  console.log(`树数据生成完成 - 分类: ${category.name}`, treeData)
  return treeData
}

// 显示图标
const displayIcon = () => {
  return () => h(TIcon, { name: 'folder' })
}

// 更新学生总成绩数据
const updateStudentsData = () => {
  // 收集所有学生ID
  const studentMap = new Map<string, StudentData>()

  // 从各指标点收集学生信息
  assessmentCategories.value.forEach(category => {
    category.indicators.forEach((indicator: Indicator) => {
      if (indicator.scores && indicator.scores.length > 0) {
        indicator.scores.forEach((score: any) => {
          if (!studentMap.has(score.studentId)) {
            studentMap.set(score.studentId, {
              studentId: score.studentId,
              name: score.name,
              totalScore: 0
            })
          }
        })
      }
    })
  })

  // 转换为数组
  const studentList = Array.from(studentMap.values())

  // 添加各指标分数
  assessmentCategories.value.forEach(category => {
    category.indicators.forEach((indicator: Indicator) => {
      if (indicator.scores && indicator.scores.length > 0) {
        indicator.scores.forEach((score: any) => {
          const student = studentList.find(s => s.studentId === score.studentId)
          if (student) {
            student[indicator.id] = score.score
          }
        })
      }
    })
  })

  // 更新学生数据
  students.value = studentList

  // 计算总分
  recalculateAllScores()
}

// 筛选后的学生列表
const filteredStudents = computed(() => {
  if (selectedClass.value === 'all') {
    return students.value
  }

  return students.value.filter(student =>
    student.studentId.startsWith(selectedClass.value)
  )
})

// 计算所有学生的总分
const recalculateAllScores = () => {
  students.value.forEach(student => {
    let total = 0;

    assessmentCategories.value.forEach(category => {
      let categoryTotal = 0;

      category.indicators.forEach((indicator: Indicator) => {
        const score = student[indicator.id] || 0;
        categoryTotal += score * (indicator.weight / 100);
      });

      // 每个大类别的得分
      total += categoryTotal * (category.weight / 100);
    });

    // 保留两位小数
    student.totalScore = Number(total.toFixed(2));
  });
}

// 导出模板
const exportTemplate = () => {
  scoreTemplateDialogReady.value = true;
  // 使用nextTick确保DOM已更新
  nextTick(() => {
    scoreTemplateGuideVisible.value = true;
  });
}

// 确认导出成绩模板
const confirmExportScoreTemplate = () => {
  if (exportScoreTemplate()) {
    MessagePlugin.success('成绩模板下载成功，包含序号、学号、姓名和试题编号列');
  } else {
    MessagePlugin.error('导出模板出错');
  }
  // 关闭对话框
  scoreTemplateGuideVisible.value = false;
}


// 直接指定指标点和分类的导入方法
const directImportScore = async (file: any, indicatorName: string): Promise<boolean> => {
  console.log('直接导入成绩 - 指标点名称:', indicatorName);

  if (!indicatorName) {
    MessagePlugin.error('导入失败：未指定指标点名称');
    return false;
  }

  // 根据名称查找指标点
  let foundIndicator = null;
  let foundCategory = null;

  // 从所有分类中寻找匹配名称的指标点
  for (const category of assessmentCategories.value) {
    for (const indicator of category.indicators) {
      if (indicator.name === indicatorName) {
        foundIndicator = indicator;
        foundCategory = category;
        break;
      }
    }
    if (foundIndicator) break;
  }

  if (!foundIndicator || !foundCategory) {
    MessagePlugin.error(`导入失败：找不到名称为"${indicatorName}"的指标点`);
    return false;
  }

  // 使用importScores代替原有的成绩导入逻辑
  const success = await importScores(
    file,
    foundIndicator as Indicator,
    {
      loading: (status: boolean) => { loading.value = status; },
    refresh: refreshTreeView,
      updateStore: (categories: Category[]) => {
        indicatorStore.updateCategories(categories);
      },
    updateStudentsData: updateStudentsData
    }
  );

  if (success) {
    updateStudentsData();
    selectedIndicator.value = {
      id: foundIndicator.id,
      categoryId: foundCategory.id,
      scores: foundIndicator.scores,
      importedFile: file.name || '',
      isPreviewVisible: true // 导入后自动显示预览
    };
    refreshTreeView();
    MessagePlugin.success(`成功导入成绩数据`);
    return true;
  }

  return false;
};

// 确保所有指标点都有categoryId
const initializeCategoryIds = () => {
  assessmentCategories.value.forEach(category => {
    category.indicators.forEach((indicator: Indicator) => {
      // 确保每个指标点都有categoryId字段
      if (!indicator.categoryId) {
        indicator.categoryId = category.id
      }
    })
  })
  console.log('指标点categoryId初始化完成', assessmentCategories.value)
  // 刷新树视图
  refreshTreeView()
}

// 初始化指标点数据
const initAssessmentCategories = async () => {
  // 初始化存储
  await indicatorStore.initCategories()

  // 尝试从 store 中获取数据
  if (indicatorStore.categories && indicatorStore.categories.length > 0) {
    assessmentCategories.value = JSON.parse(JSON.stringify(indicatorStore.categories))
  } else {
    // 如果 store 中没有数据，则使用默认数据
    assessmentCategories.value = [
      {
        id: 'final',
        name: '期末考核',
        type: 'final',
        weight: 60,
        indicators: [
          { id: 'final-exam', name: '期末考试', weight: 100, preset: true, scores: [], categoryId: 'final' }
        ]
      },
      {
        id: 'regular',
        name: '平时考核',
        type: 'regular',
        weight: 40,
        indicators: [
          { id: 'attendance', name: '签到', weight: 25, preset: true, scores: [], categoryId: 'regular' },
          { id: 'homework', name: '平时作业', weight: 25, preset: true, scores: [], categoryId: 'regular' },
          { id: 'quiz', name: '课堂测验', weight: 25, preset: true, scores: [], categoryId: 'regular' },
          { id: 'participation', name: '课堂参与', weight: 25, preset: true, scores: [], categoryId: 'regular' }
        ]
      }
    ]

    // 保存默认数据到 store
    indicatorStore.updateCategories(assessmentCategories.value)
  }

  // 确保所有指标点都有 categoryId
  initializeCategoryIds()

  // 确保所有指标点都有展开/折叠状态（默认折叠）
  assessmentCategories.value.forEach(category => {
    category.indicators.forEach((indicator: Indicator) => {
      indicator.expanded = true
    })
  })
}

// 监听 store 中数据的变化
watch(() => indicatorStore.categories, (newCategories) => {
  if (newCategories && newCategories.length > 0) {
    assessmentCategories.value = JSON.parse(JSON.stringify(newCategories))
    initializeCategoryIds()
    refreshTreeView()
    recalculateAllScores()
  }
}, { deep: true })

// 页面加载时获取示例数据
onMounted(() => {
  // 初始化指标点数据
  initAssessmentCategories()

  // 加载示例学生数据
  loading.value = true
  setTimeout(() => {
    students.value = Array.from({ length: 5 }, (_, i) => {
      const studentData: StudentData = {
        studentId: `202200${i + 1}`,
        name: `学生${i + 1}`,
        totalScore: 0
      }

      // 添加各指标得分
      assessmentCategories.value.forEach(category => {
        category.indicators.forEach((indicator: Indicator) => {
          // 随机生成60-100的分数
          studentData[indicator.id] = Math.floor(Math.random() * 40) + 60
        })
      })

      return studentData
    })

    // 计算总分
    recalculateAllScores()
    loading.value = false

    // 强制刷新树数据
    setTimeout(() => {
      console.log('刷新树数据...')
      // 这会强制Vue重新评估assessmentCategories，从而重新生成树数据
      assessmentCategories.value = JSON.parse(JSON.stringify(assessmentCategories.value))
      // 刷新树视图
      refreshTreeView()
    }, 500)
  }, 500)
})

// 关闭预览
const closePreview = () => {
  if (selectedIndicator.value) {
    selectedIndicator.value.isPreviewVisible = false;
  }
}

// 题目预览
const problemPreviewVisible = ref(false);
const selectedProblemFile = ref<ImportedProblemFile | null>(null);

// 处理题目文件预览
const handlePreviewFile = (file: ImportedProblemFile) => {
  selectedProblemFile.value = file;
  problemPreviewVisible.value = true;
};

/*// 处理题目文件删除
const handleDeleteProblemFile = () => {
  // 如果当前正在预览被删除的文件，关闭预览
  if (selectedProblemFile.value && problemPreviewVisible.value) {
    const fileStillExists = assessmentCategories.value.some(category =>
      category.indicators.some((indicator: Indicator) =>
        indicator.importedProblemFiles?.some((f: ImportedProblemFile) => f.id === selectedProblemFile.value?.id)
      )
    );

    if (!fileStillExists) {
      problemPreviewVisible.value = false;
    }
  }
};*/

// 导入题目
const handleImportProblems = async (file: any, indicator: Indicator) => {
  console.log('开始导入题目:', file.name);
  console.log('指标点信息:', JSON.stringify({
    id: indicator.id,
    name: indicator.name,
    categoryId: indicator.categoryId,
    hasFiles: indicator.importedProblemFiles?.length > 0
  }, null, 2));

  try {
    // 确保指标点有importedProblemFiles数组
    if (!indicator.importedProblemFiles) {
      console.log('创建importedProblemFiles数组');
      indicator.importedProblemFiles = [];
    }

    const result = await importProblems(file, indicator, {
      loading: (status: boolean) => loading.value = status,
      refresh: () => {
        console.log('刷新视图');
        // 确保导入后自动展开指标点
        indicator.expanded = true;

        // 初始化新导入文件的权重 - 平均分配剩余权重
        if (indicator.importedProblemFiles && indicator.importedProblemFiles.length > 0) {
          const usedWeight = indicator.importedProblemFiles
            .filter((f: ImportedProblemFile) => f.weight !== undefined && f.id !== "latest")
            .reduce((sum: number, f: ImportedProblemFile) => sum + (Number(f.weight) || 0), 0);

          const remainingWeight = indicator.weight - usedWeight;
          const latestFile = indicator.importedProblemFiles.find((f: ImportedProblemFile) => !f.weight || f.weight === 0);

          if (latestFile) {
            latestFile.weight = Math.max(0, remainingWeight);
            console.log(`初始化文件权重: ${latestFile.fileName} = ${latestFile.weight}%`);
          }
        }

        refreshTreeView();
        setTimeout(() => {
          console.log('导入后指标点状态:', indicator);
          console.log('导入的题目文件数量:', indicator.importedProblemFiles?.length || 0);

          // 详细检查导入的文件
          if (indicator.importedProblemFiles && indicator.importedProblemFiles.length > 0) {
            console.log('导入的文件列表:', JSON.stringify(indicator.importedProblemFiles.map((f: ImportedProblemFile) => ({
              id: f.id,
              fileName: f.fileName,
              weight: f.weight || 0,
              problemCount: f.problems?.length || 0
            })), null, 2));
          } else {
            console.warn('指标点下没有导入的题目文件!');
          }
        }, 500);
      },
      updateStore: (categories) => {
        console.log('更新存储:', categories.length, '个分类');
        // 确保更新后立即重新获取数据
        indicatorStore.updateCategories(categories);
        // 立即从存储重新获取
        assessmentCategories.value = JSON.parse(JSON.stringify(indicatorStore.categories));
      },
      showPreview: (importedFile) => {
        console.log('显示预览:', importedFile);
        selectedProblemFile.value = importedFile;
        problemPreviewVisible.value = true;
      }
    });

    if (result) {
      // 导入成功后，确保指标点被展开
      indicator.expanded = true;
      // 打印导入后的指标点状态
      console.log('导入成功后指标点状态:', {
        id: indicator.id,
        name: indicator.name,
        expanded: indicator.expanded,
        hasImportedFiles: indicator.importedProblemFiles && indicator.importedProblemFiles.length > 0
      });

      // 强制刷新页面
      setTimeout(() => {
        // 强制刷新assessmentCategories
        assessmentCategories.value = JSON.parse(JSON.stringify(indicatorStore.categories));
        // 显示成功提示
        MessagePlugin.success(`成功导入题目文件: ${file.name}`);
      }, 200);
    }

    console.log('导入结果:', result);
    return result;
  } catch (error) {
    console.error('导入题目出错:', error);
    MessagePlugin.error(`导入题目失败: ${error}`);
    return false;
  }
};

// 题目模板使用指南
const templateGuideVisible = ref(false);
const templateDialogReady = ref(false);

// 成绩模板使用指南
const scoreTemplateGuideVisible = ref(false);
const scoreTemplateDialogReady = ref(false);

// 导出题目模板
const handleExportProblemTemplate = () => {
  templateDialogReady.value = true;
  // 使用nextTick确保DOM已更新
  nextTick(() => {
    templateGuideVisible.value = true;
  });
};

// 确认导出模板
const confirmExportTemplate = () => {
  exportProblemTemplate();
  templateGuideVisible.value = false;
};

// 在<script>顶部添加接口定义
interface StudentData {
  studentId: string;
  name: string;
  totalScore: number;
  [key: string]: string | number; // 添加索引签名
}

interface Indicator {
  id: string;
  name: string;
  weight: number;
  categoryId?: string;
  scores?: StudentScore[];
  preset?: boolean;
  importedFile?: string;
  problems?: any[];
  importedProblemFiles?: ImportedProblemFile[];
  expanded?: boolean;
  [key: string]: any;
}

interface Category {
  id: string;
  name: string;
  type: string;
  weight: number;
  indicators: Indicator[];
}

interface Problem {
  title: string;
  score: number | string;
}

interface SelectedIndicator {
  id: string;
  categoryId: string;
  scores?: StudentScore[];
  importedFile?: string;
  isPreviewVisible?: boolean; // 添加预览可见性控制
}

// 可以添加Store类型定义
interface IndicatorStore {
  categories: Category[];
  loaded: boolean;
  getCategories: () => Category[];
  initCategories: () => void;
  updateCategories: (categories: Category[]) => void;
}
const getProblemScore = (row: any, col: any) => {
  const problemKey = col.colKey.replace('problem_', '');
  const problemScore = row.problems?.find((p: any) =>
    p.title === problemKey || p.title === `题目${problemKey}`
  );
  return problemScore ? problemScore.score : 0;
}

//展开/折叠指标点
const toggleIndicator = (indicator: Indicator) => {
  // 始终设置为展开状态
  indicator.expanded = true;
}
// 打开题目导入对话框相关变量
const currentImportTarget = ref<Indicator | null>(null);
const currentImportParent = ref<Indicator | null>(null);
const isExam = ref(false);
const questionImportDialogVisible = ref(false);

// 打开题目导入对话框
const openQuestionImportDialog = (indicator: Indicator) => {
  currentImportTarget.value = indicator;
  currentImportParent.value = indicator;
  isExam.value = indicator.name && (
    indicator.name.includes('考试') ||
    indicator.name.includes('测验') ||
    (indicator.parent && indicator.parent.name &&
      (indicator.parent.name.includes('考试') || indicator.parent.name.includes('测验')))
  );
  questionImportDialogVisible.value = true;
};

// 预览导入文件的成绩
const handlePreviewProblemFile = (file: ImportedProblemFile, indicator: Indicator) => {
  selectedProblemFile.value = file;
  problemPreviewVisible.value = true;
};

// 删除指标点成绩
const handleDeleteProblemFile = (file: ImportedProblemFile, indicator: Indicator) => {
  // 确认删除提示
  DialogPlugin.confirm({
    header: '确认删除',
    body: `确定要删除文件「${file.fileName}」吗？`,
    confirmBtn: {
      content: '确认',
      theme: 'primary',
    },
    cancelBtn: '取消',
    onConfirm: () => {
      // 删除该文件
      indicator.importedProblemFiles = indicator.importedProblemFiles.filter(
        (f: ImportedProblemFile) => f.id !== file.id
      );

      // 如果正在预览该文件，则关闭预览
      if (selectedProblemFile.value?.id === file.id) {
        problemPreviewVisible.value = false;
        selectedProblemFile.value = null;
      }

      // 更新Store
      indicatorStore.updateCategories(assessmentCategories.value);
      //更新学生成绩
      updateStudentsData();
      //刷新树视图
      refreshTreeView();

      MessagePlugin.success('题目文件已删除');
    }
  });
};

// 为指标点添加预览按钮
const previewIndicatorScores = (indicator: Indicator, categoryId: string) => {
  if (indicator.scores && indicator.scores.length > 0) {
    selectedIndicator.value = {
      id: indicator.id,
      categoryId: categoryId,
      scores: indicator.scores,
      importedFile: indicator.importedFile,
      isPreviewVisible: true // 点击预览按钮时显示
    };
  } else {
    MessagePlugin.info('该指标点暂无成绩数据');
  }
}

// 添加导入成绩的方法
const importScoreForFile = async (file: any, problemFile: ImportedProblemFile, indicator: Indicator) => {
  try {
    // 调用 importScores，传递正确的 indicator 参数
    const success = await importScores(
      file,
      indicator,
      {
        loading: (status: boolean) => {
        loading.value = status;
      },
      refresh: refreshTreeView,
        updateStore: (categories: Category[]) => {
        indicatorStore.updateCategories(categories);
      },
      updateStudentsData: () => {
        updateStudentsData();
      }
      }
    );

    if (success) {
      // 响应式更新 problemFile.scores
      const updatedFile = {
        ...problemFile,
        scores: indicator.scores || [] // 确保scores不是undefined
      };

      // 更新 Store 中的文件列表
      const updatedFiles = indicator.importedProblemFiles?.map((f: ImportedProblemFile) =>
        f.id === problemFile.id ? updatedFile : f
      ) || [];

      if (indicator.importedProblemFiles) {
      indicator.importedProblemFiles = updatedFiles;
      }

      MessagePlugin.success(`成功导入成绩数据`);
      return true;
    }
  } catch (error: any) {
    MessagePlugin.error(`导入失败: ${error.message}`);
  }
  return false;
};

// 题目文件排序
const sortProblemFiles = (indicator: Indicator, event:any) => {
  if (!indicator.importedProblemFiles || indicator.importedProblemFiles.length <= 1) {
    return;
  }
const sortType = typeof event ==='object'&&event.value?event.value:event;
  if (sortType === 'name') {
    indicator.importedProblemFiles.sort((a: ImportedProblemFile, b: ImportedProblemFile) =>
      a.fileName.localeCompare(b.fileName)
    );
  } else if (sortType === 'time') {
    indicator.importedProblemFiles.sort((a: ImportedProblemFile, b: ImportedProblemFile) =>
      new Date(b.importDate).getTime() - new Date(a.importDate).getTime()
    );
  }

  // 更新Store
  indicatorStore.updateCategories(assessmentCategories.value);
};

// 添加保存题目文件权重的函数
const saveFileWeight = (file: ImportedProblemFile, indicator: Indicator) => {
  // 确保权重是数字
  const currentWeight = Number(file.weight) || 0;

  // 计算当前指标点下所有题目文件的权重总和(不包括当前修改的文件)
  const otherFilesWeightSum = indicator.importedProblemFiles
    .filter((f: ImportedProblemFile) => f.id !== file.id)
    .reduce((sum: number, f: ImportedProblemFile) => sum + (Number(f.weight) || 0), 0);

  // 检查总权重是否超出指标点权重
  if (otherFilesWeightSum + currentWeight > indicator.weight) {
    const maxAllowed = indicator.weight - otherFilesWeightSum;
    MessagePlugin.warning(`所有题目文件的权重总和不能超过指标点权重${indicator.weight}%，当前已将权重调整为${maxAllowed}%`);
    file.weight = maxAllowed;
  } else {
    file.weight = currentWeight;
  }

  // 更新Store
  indicatorStore.updateCategories(assessmentCategories.value);

  // 重新计算总分
  recalculateAllScores();

  // 提示保存成功
  MessagePlugin.success(`题目文件权重已更新为${file.weight}%`);
};

// 计算指标点已使用的权重总和
const getIndicatorUsedWeight = (indicator: Indicator): number => {
  if (!indicator.importedProblemFiles || indicator.importedProblemFiles.length === 0) {
    return 0;
  }

  return indicator.importedProblemFiles.reduce(
    (sum: number, file: ImportedProblemFile) => sum + (Number(file.weight) || 0),
    0
  );
};

// 增减题目文件权重
const increaseFileWeight = (file: ImportedProblemFile, indicator: Indicator) => {
  const currentWeight = Number(file.weight) || 0;
  const otherFilesWeightSum = indicator.importedProblemFiles
    .filter((f: ImportedProblemFile) => f.id !== file.id)
    .reduce((sum: number, f: ImportedProblemFile) => sum + (Number(f.weight) || 0), 0);

  // 计算可用的最大权重增量
  const maxIncrease = Math.min(5, indicator.weight - otherFilesWeightSum - currentWeight);

  if (maxIncrease <= 0) {
    MessagePlugin.warning(`已达到指标点权重上限(${indicator.weight}%)，无法继续增加`);
    return;
  }

  file.weight = currentWeight + maxIncrease;
};

const decreaseFileWeight = (file: ImportedProblemFile) => {
  const currentWeight = Number(file.weight) || 0;
  if (currentWeight <= 0) {
    MessagePlugin.warning('权重已经是0，无法继续减少');
    return;
  }

  file.weight = Math.max(0, currentWeight - 5);
};

// 导入成绩的函数中，修改查找题号的逻辑
// 对于单行表头，我们直接查找形如"1-1"的题号列
const findProblemColumns = (headerRow: any[]): {indices: number[], problemNos: string[]} => {
  if (!headerRow || !Array.isArray(headerRow)) {
    return {indices: [], problemNos: []};
  }

  const indices: number[] = [];
  const problemNos: string[] = [];

  // 跳过前三列（序号、学号、姓名）
  for (let i = 3; i < headerRow.length; i++) {
    const cell = String(headerRow[i] || '').trim();
    if (/^\d+-\d+$/.test(cell)) {
      indices.push(i);
      problemNos.push(cell);
    }
  }

  return {indices, problemNos};
};

// 生成唯一ID
const generateUniqueId = () => {
  return 'id-' + Math.random().toString(36).substr(2, 9);
}

// 打开考核方式管理对话框
const openAssessmentEditor = () => {
  // 深拷贝当前分类数据
  editingCategories.value = JSON.parse(JSON.stringify(assessmentCategories.value));
  assessmentEditorVisible.value = true;
}

// 添加考核方式
const addCategory = () => {
  const newCategory = {
    id: generateUniqueId(),
    name: '新考核方式',
    type: 'custom',
    weight: 0,
    indicators: [
      {
        id: generateUniqueId(),
        name: '新指标点',
        weight: 100,
        preset: false,
        scores: [],
        categoryId: '' // 将在保存时更新
      }
    ]
  };

  editingCategories.value.push(newCategory);
}

// 删除考核方式
const removeCategory = (index: number) => {
  // 确认删除
  DialogPlugin.confirm({
    header: '确认删除',
    body: `确定要删除考核方式"${editingCategories.value[index].name}"吗？此操作将删除其下所有指标点及相关数据。`,
    confirmBtn: {
      content: '确认',
      theme: 'danger',
    },
    cancelBtn: '取消',
    onConfirm: () => {
      editingCategories.value.splice(index, 1);
    }
  });
}

// 添加指标点
const addIndicator = (category: Category) => {
  if (!category.indicators) {
    category.indicators = [];
  }

  category.indicators.push({
    id: generateUniqueId(),
    name: '新指标点',
    weight: 0, // 初始权重为0
    preset: false,
    scores: [],
    categoryId: category.id
  });

  // 重新计算指标点权重，保证总和为100%
  redistributeIndicatorWeights(category);
}

// 删除指标点
const removeIndicator = (category: Category, index: number) => {
  // 确认删除
  const confirmDialog = DialogPlugin.confirm({
    header: '确认删除',
    body: `确定要删除"${category.indicators[index].name}"考核项吗？`,
    confirmBtn: {
      content: '确认',
      theme: 'danger',
    },
    cancelBtn: '取消',
    onConfirm: () => {
      category.indicators.splice(index, 1);
      // 重新分配权重
      redistributeIndicatorWeights(category);
      // 关闭对话框
      confirmDialog.hide();
    }
  });
}

// 重新分配指标点权重
const redistributeIndicatorWeights = (category: Category) => {
  if (!category.indicators || category.indicators.length === 0) return;

  // 将指标点权重平均分配
  const equalWeight = 100 / category.indicators.length;
  category.indicators.forEach(indicator => {
    indicator.weight = Number(equalWeight.toFixed(1));
  });

  // 调整最后一个指标点的权重，确保总和正好为100%
  const totalWeight = category.indicators.reduce((sum, ind) => sum + ind.weight, 0);
  if (totalWeight !== 100) {
    const lastIndicator = category.indicators[category.indicators.length - 1];
    lastIndicator.weight += (100 - totalWeight);
  }
}

// 计算考核方式总权重
const getTotalCategoryWeight = () => {
  return editingCategories.value.reduce((sum, category) => sum + Number(category.weight), 0);
}

// 判断考核方式编辑是否有效
const isAssessmentEditValid = computed(() => {
  // 检查每个考核方式中的指标点权重总和是否为100%
  return editingCategories.value.every(category => {
    if (!category.indicators || category.indicators.length === 0) return false;

    const totalIndicatorWeight = category.indicators.reduce(
      (sum: number, ind: Indicator) => sum + Number(ind.weight),
      0
    );
    return Math.abs(totalIndicatorWeight - 100) <= 0.1;
  });
});

// 取消考核方式编辑
const cancelAssessmentEdit = () => {
  assessmentEditorVisible.value = false;
}

// 保存考核方式编辑
const saveAssessmentEdit = () => {
  // 验证各考核方式内部指标点权重
  const invalidCategories = editingCategories.value.filter(category => {
    if (!category.indicators || category.indicators.length === 0) return true;

    const totalWeight = category.indicators.reduce((sum: number, ind: Indicator) => sum + Number(ind.weight), 0);
    return Math.abs(totalWeight - 100) > 0.1;
  });

  if (invalidCategories.length > 0) {
    const categoryNames = invalidCategories.map(c => c.name).join('、');
    MessagePlugin.warning(`以下考核方式的考核项权重总和不等于100%: ${categoryNames}`);
    return;
  }

  // 更新categoryId
  editingCategories.value.forEach(category => {
    category.indicators.forEach(indicator => {
      indicator.categoryId = category.id;
    });
  });

  // 更新考核方式中的指标点（保留考核方式的名称和权重不变）
  const updatedCategories = assessmentCategories.value.map(originalCategory => {
    const editedCategory = editingCategories.value.find(c => c.id === originalCategory.id);
    if (editedCategory) {
      return {
        ...originalCategory,
        indicators: editedCategory.indicators
      };
    }
    return originalCategory;
  });

  // 更新考核方式
  assessmentCategories.value = updatedCategories;

  // 同步到store
  indicatorStore.updateCategories(assessmentCategories.value);

  // 重新计算总分
  recalculateAllScores();

  MessagePlugin.success('考核项设置已保存');
  assessmentEditorVisible.value = false;
}

// 计算指标点权重合计
const getIndicatorTotalWeight = (category: Category): number => {
  return category.indicators.reduce((sum: number, indicator: Indicator) => sum + Number(indicator.weight), 0);
}

// 处理导入目标成绩
const importTargetScore = async (file: any, indicatorName: string): Promise<boolean> => {
  if (!file) {
    MessagePlugin.error('请选择要导入的文件');
    return false;
  }

  // 根据名称查找指标点
  let foundIndicator = null;
  let foundCategory = null;

  // 从所有分类中寻找匹配名称的指标点
  for (const category of assessmentCategories.value) {
    for (const indicator of category.indicators) {
      if (indicator.name === indicatorName) {
        foundIndicator = indicator;
        foundCategory = category;
        break;
      }
    }
    if (foundIndicator) break;
  }

  if (!foundIndicator || !foundCategory) {
    MessagePlugin.error(`导入失败：找不到名称为"${indicatorName}"的指标点`);
    return false;
  }

  loading.value = true;
  try {
    // 模拟目标成绩导入过程
    await new Promise(resolve => setTimeout(resolve, 1500));
    
    // 这里应该有实际的Excel文件解析和数据导入逻辑
    // 目前使用假数据模拟
    foundIndicator.targetScores = students.value.map(student => ({
      studentId: student.studentId,
      name: student.name,
      score: Math.floor(Math.random() * 20) + 80, // 随机生成80-100的目标分数
      problems: [] // 可以添加具体题目的目标得分
    }));
    
    foundIndicator.hasTargetScores = true;
    foundIndicator.targetImportedFile = file.name;
    
    // 更新store
    indicatorStore.updateCategories(assessmentCategories.value);
    
    // 刷新视图
    refreshTreeView();
    
    MessagePlugin.success(`成功导入目标成绩数据`);
    
    // 显示导入的目标成绩
    selectedIndicator.value = {
      id: foundIndicator.id,
      categoryId: foundCategory.id,
      scores: foundIndicator.scores,
      targetScores: foundIndicator.targetScores,
      importedFile: foundIndicator.importedFile || '',
      targetImportedFile: file.name,
      isPreviewVisible: true,
      showTargetScores: true
    };
    
    loading.value = false;
    return true;
  } catch (error) {
    console.error('导入目标成绩失败:', error);
    MessagePlugin.error('导入目标成绩失败，请检查文件格式');
    loading.value = false;
    return false;
  }
};

// 获取学生的目标成绩
const getTargetScore = (studentId: string): number | null => {
  if (!selectedIndicator.value || !selectedIndicator.value.targetScores) {
    return null;
  }
  
  const targetScore = selectedIndicator.value.targetScores.find(
    (score: any) => score.studentId === studentId
  );
  
  return targetScore ? targetScore.score : null;
};

// 更新scoreColumns的定义，增加自定义渲染
const updateScoreColumns = () => {
  const basicColumns = [
    { colKey: 'studentId', title: '学号', width: 120 },
    { colKey: 'name', title: '姓名', width: 120 },
  ];

  // 如果是预览题目文件的成绩，添加题目列
  if (
    selectedIndicator.value &&
    selectedIndicator.value.scores &&
    selectedIndicator.value.scores.length > 0 &&
    selectedIndicator.value.scores[0].problems
  ) {
    // 添加题目列
    const problemColumns = selectedIndicator.value.scores[0].problems.map((problem: any, index: number) => ({
      colKey: `problem_${index}`,
      title: `题目${problem.number || (index + 1)}`,
      width: 100,
      cell: 'problem_default'
    }));

    // 添加总分列
    const scoreColumn = {
      colKey: 'score',
      title: `总分${selectedIndicator.value.showTargetScores ? '/目标分' : ''}`,
      width: 120,
      cell: 'score'
    };

    scoreColumns.value = [...basicColumns, ...problemColumns, scoreColumn];
  } else {
    // 只有总分
    const scoreColumn = {
      colKey: 'score',
      title: `总分${selectedIndicator.value && selectedIndicator.value.showTargetScores ? '/目标分' : ''}`,
      width: 120,
      cell: 'score'
    };
    
    scoreColumns.value = [...basicColumns, scoreColumn];
  }
};

// 监听selectedIndicator变化，更新列定义
watch(
  () => selectedIndicator.value,
  (newVal) => {
    if (newVal) {
      updateScoreColumns();
    }
  },
  { deep: true }
);

// 目标成绩模板使用指南
const targetScoreTemplateDialogReady = ref(false);
const targetScoreTemplateGuideVisible = ref(false);

// 导出目标成绩模板
const exportTargetScoreTemplate = () => {
  targetScoreTemplateDialogReady.value = true;
  // 使用nextTick确保DOM已更新
  nextTick(() => {
    targetScoreTemplateGuideVisible.value = true;
  });
};

// 确认导出目标成绩模板
const confirmExportTargetScoreTemplate = () => {
  try {
    // 创建新的工作簿
    const wb = XLSX.utils.book_new();
    
    // 创建工作表数据
    const wsData = [
      ['序号', '学号', '姓名', '目标分数'],
      [1, '2022001', '学生1', 85],
      [2, '2022002', '学生2', 90],
      [3, '2022003', '学生3', 80],
      [4, '2022004', '学生4', 85],
      [5, '2022005', '学生5', 95]
    ];
    
    // 创建工作表
    const ws = XLSX.utils.aoa_to_sheet(wsData);
    
    // 将工作表添加到工作簿
    XLSX.utils.book_append_sheet(wb, ws, '目标成绩模板');
    
    // 下载文件
    XLSX.writeFile(wb, '目标成绩导入模板.xlsx');
    
    MessagePlugin.success('目标成绩模板下载成功');
    targetScoreTemplateGuideVisible.value = false;
    return true;
  } catch (error) {
    console.error('导出目标成绩模板出错:', error);
    MessagePlugin.error('导出目标成绩模板出错');
    return false;
  }
}
</script>
<style scoped>
.grade-management-container {
  --header-padding: 24px;
  --card-spacing: 16px;
  --section-spacing: 24px;
  --item-spacing: 12px;
  --border-radius: 8px;
  --transition-duration: 0.2s;
  --primary-color: #0052d9;
  --success-color: #00a870;
  --warning-color: #ed7b2f;
  --danger-color: #e34d59;
  --hover-bg-color: #f5f9ff;

  padding: var(--header-padding);
  background-color: #f5f5f5;
  color: var(--td-text-color-primary);
  font-family: var(--td-font-family);
  line-height: 1.5;
}

.header-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--section-spacing);
  flex-wrap: wrap;
  gap: var(--item-spacing);
  background-color: white;
  border-radius: var(--border-radius);
  padding: var(--item-spacing);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  transition: all var(--transition-duration);

  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
}

.filter-area {
  display: flex;
  gap: var(--item-spacing);
  align-items: center;
}

.action-area {
  display: flex;
  flex-wrap: wrap;
  gap: var(--item-spacing);
}

.class-selector {
  min-width: 200px;
}

.category-container {
  margin-bottom: var(--section-spacing);
  background-color: white;
  border-radius: var(--border-radius);
  padding: var(--card-spacing);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  transition: all var(--transition-duration);

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
  }
}

.category-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--item-spacing);
  padding-bottom: var(--item-spacing);
  border-bottom: 1px solid #eee;
}

.category-title-section {
  display: flex;
  align-items: center;
  gap: var(--item-spacing);

  h3 {
    margin: 0;
    font-size: 18px;
    color: var(--td-text-color-primary);
    font-weight: 600;
  }
}

.indicator-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: var(--card-spacing);
}

.indicator-card {
  border: 1px solid #eee;
  border-radius: var(--border-radius);
  padding: var(--item-spacing);
  background-color: #fcfcfc;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  transition: all var(--transition-duration);

  &:hover {
    border-color: var(--primary-color);
    box-shadow: 0 4px 12px rgba(0, 82, 217, 0.1);
  }
}

.indicator-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--item-spacing);
}

.indicator-title {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.indicator-name {
  font-weight: 500;
  font-size: 16px;
  color: var(--td-text-color-primary);
}

.indicator-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.indicator-actions {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.imported-files-section {
  margin-top: var(--item-spacing);
  background-color: #f8f9fa;
  padding: var(--item-spacing);
  border-radius: 6px;
  border: 1px solid #e0e3e9;
}

.files-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--item-spacing);
}

.files-title {
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--primary-color);
  font-weight: 500;
}

.files-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.file-items-container {
  display: flex;
  flex-direction: column;
  gap: var(--item-spacing);
}

.imported-file-item {
  display: flex;
  flex-direction: column;
  padding: var(--item-spacing);
  background-color: white;
  border-radius: var(--border-radius);
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  transition: all var(--transition-duration);

  &:hover {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  }
}

.file-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: var(--item-spacing);
}

.file-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: var(--item-spacing);
}

.file-name {
  font-weight: 500;
  font-size: 16px;
  color: var(--td-text-color-primary);
}

.file-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.file-date {
  color: #888;
  font-size: 13px;
}

.problems-preview {
  margin-top: 8px;
}

.problems-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  color: #666;
}

.view-all {
  color: var(--primary-color);
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 4px;
  transition: color var(--transition-duration);

  &:hover {
    color: #3370ff;
    text-decoration: underline;
  }
}

.problems-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
  gap: 8px;
}

.problem-item {
  background-color: #f5f9ff;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 13px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  border: 1px solid rgba(0, 82, 217, 0.1);
  transition: all var(--transition-duration);

  &:hover {
    background-color: rgba(0, 82, 217, 0.08);
    border-color: rgba(0, 82, 217, 0.2);
  }
}

.file-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: var(--item-spacing);
  padding-top: var(--item-spacing);
  border-top: 1px solid #eee;
}

.weight-editor {
  display: flex;
  align-items: center;
  gap: 8px;
  background-color: #f8f9fa;
  padding: 4px 8px;
  border-radius: 4px;
  border: 1px solid #e0e0e0;
  transition: all var(--transition-duration);

  &:hover {
    border-color: var(--primary-color);
    background-color: #f0f7ff;
  }
}

.weight-value {
  min-width: 40px;
  text-align: center;
  font-weight: 500;
  color: var(--primary-color);
}

.action-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.score-preview-card {
  margin-top: var(--section-spacing);
  background-color: white;
  border-radius: var(--border-radius);
  padding: var(--card-spacing);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--card-spacing);
}

.preview-title {
  h4 {
    margin: 0;
    font-size: 18px;
    color: var(--td-text-color-primary);
    font-weight: 600;
  }
}

.imported-files-info {
  margin-top: 8px;
  font-size: 14px;
  color: #666;
}

.imported-file {
  display: block;
  margin-bottom: 4px;
}

.target-imported-file {
  display: block;
  color: var(--warning-color);
}

.score-comparison {
  display: flex;
  align-items: center;
  gap: 4px;
}

.target-score {
  color: #888;
}

.target-value {
  color: var(--warning-color);
  font-weight: 500;
}

.total-scores-container {
  margin-top: var(--section-spacing);
  background-color: white;
  border-radius: var(--border-radius);
  padding: var(--card-spacing);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);

  h3 {
    margin-bottom: var(--item-spacing);
    font-size: 18px;
    color: var(--td-text-color-primary);
    font-weight: 600;
  }
}

.student-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.name {
  font-weight: 500;
  color: var(--td-text-color-primary);
}

.student-id {
  font-size: 13px;
  color: #888;
}

.score-excellent {
  color: var(--success-color);
  font-weight: 500;
}

.score-pass {
  color: var(--primary-color);
  font-weight: 500;
}

.score-fail {
  color: var(--danger-color);
  font-weight: 500;
}

/* 对话框内容样式 */
.assessment-editor-container,
.weight-setting-container {
  padding: var(--card-spacing);
}

.editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--card-spacing);
}

.editor-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 15px;
  color: var(--td-text-color-primary);
}

.assessment-categories {
  margin-bottom: var(--card-spacing);
}

.assessment-category {
  margin-bottom: var(--item-spacing);
  background-color: #f9f9f9;
  border-radius: var(--border-radius);
  padding: var(--item-spacing);
  transition: all var(--transition-duration);

  &:hover {
    background-color: #f0f7ff;
  }
}

.category-editor {
  display: flex;
  flex-direction: column;
  gap: var(--item-spacing);
}

.category-editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.category-editor-name {
  display: flex;
  align-items: center;
  gap: var(--item-spacing);
}

.category-fixed-name {
  font-weight: 500;
  font-size: 16px;
  color: var(--td-text-color-primary);
}

.indicators-editor {
  display: flex;
  flex-direction: column;
  gap: var(--item-spacing);
  margin-top: var(--item-spacing);
  padding: var(--item-spacing);
  background-color: white;
  border-radius: var(--border-radius);
  border: 1px solid #e0e0e0;
}

.indicator-editor {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px;
  border-radius: 4px;
  transition: all var(--transition-duration);

  &:hover {
    background-color: var(--hover-bg-color);
  }
}

.indicator-name-input {
  flex: 1;
  margin-right: var(--item-spacing);
}

.indicator-editor-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.delete-btn {
  color: var(--danger-color);
  transition: all var(--transition-duration);

  &:hover {
    background-color: rgba(227, 77, 89, 0.1);
    transform: scale(1.1);
  }
}

.add-indicator-container {
  padding-top: var(--item-spacing);
}

.add-indicator-btn {
  width: 100%;
  border-style: dashed;
  transition: all var(--transition-duration);

  &:hover {
    background-color: rgba(0, 82, 217, 0.05);
  }
}

.category-validation-info {
  margin-top: var(--item-spacing);
  font-size: 14px;
  padding: 8px 12px;
  border-radius: 4px;
  background-color: #f8f9fa;
}

.validation-result {
  display: flex;
  align-items: center;
  gap: 8px;
}

.valid-weight {
  color: var(--success-color);
  font-weight: 500;
}

.invalid-weight {
  color: var(--danger-color);
  font-weight: 500;
}

.error-hint {
  font-size: 12px;
  color: var(--danger-color);
}

.editor-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: var(--card-spacing);
  padding-top: var(--item-spacing);
  border-top: 1px solid #eee;
}

.validation-summary {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
}

.dialog-btn-group {
  display: flex;
  gap: 8px;
}

.template-guide-container {
  padding: var(--card-spacing);

  h4 {
    margin-bottom: var(--item-spacing);
    font-size: 16px;
    color: var(--td-text-color-primary);
  }

  .guide-list {
    margin-bottom: var(--item-spacing);
    padding-left: var(--card-spacing);

    li {
      margin-bottom: 8px;
      position: relative;

      &::before {
        content: "•";
        position: absolute;
        left: -15px;
        color: var(--primary-color);
      }
    }
  }

  .warning-section {
    margin-top: var(--item-spacing);
    padding: var(--item-spacing);
    border-radius: var(--border-radius);
    background-color: #fff8f0;
    border: 1px solid #ffedd0;

    .warning-icon {
      color: var(--warning-color);
    }

    p {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 10px;
      font-weight: 500;
    }

    ul {
      margin-left: 20px;
    }
  }

  .template-example {
    margin: var(--card-spacing) 0;
    border: 1px solid #eee;
    border-radius: var(--border-radius);
    overflow: hidden;

    h5 {
      padding: 10px;
      margin: 0;
      background-color: #f5f9ff;
      border-bottom: 1px solid #eee;
    }

    .example-table {
      padding: var(--item-spacing);
      overflow-x: auto;

      table {
        width: 100%;
        border-collapse: collapse;

        th, td {
          padding: 8px;
          text-align: left;
          border: 1px solid #eee;
        }

        th {
          background-color: #f8f9fa;
          font-weight: normal;
        }

        tr:nth-child(even) {
          background-color: #f9f9f9;
        }
      }
    }
  }
}

/* 验证信息的样式 */
.validation-info {
  margin-top: var(--item-spacing);
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 14px;
  transition: all var(--transition-duration);
  
  &.valid {
    background-color: rgba(0, 168, 112, 0.1);
    border: 1px solid rgba(0, 168, 112, 0.2);
    color: var(--success-color);
  }
  
  &.invalid {
    background-color: rgba(227, 77, 89, 0.1);
    border: 1px solid rgba(227, 77, 89, 0.2);
    color: var(--danger-color);
  }
}

.total-validation {
  margin-top: var(--item-spacing);
  padding: 8px 12px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  transition: all var(--transition-duration);
  
  &.valid {
    background-color: rgba(0, 168, 112, 0.1);
    border: 1px solid rgba(0, 168, 112, 0.2);
    color: var(--success-color);
  }
  
  &.invalid {
    background-color: rgba(227, 77, 89, 0.1);
    border: 1px solid rgba(227, 77, 89, 0.2);
    color: var(--danger-color);
  }
  
  .error-text {
    margin-left: 8px;
    font-size: 12px;
  }
}

.dialog-footer {
  margin-top: var(--card-spacing);
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .grade-management-container {
    --header-padding: 16px;
    --card-spacing: 12px;
    --section-spacing: 20px;
  }

  .header-actions {
    flex-direction: column;
    align-items: stretch;

    .filter-area, 
    .action-area {
      width: 100%;
    }

    .action-area {
      margin-top: var(--item-spacing);
      justify-content: space-between;
    }
  }

  .category-container {
    padding: var(--item-spacing);
  }

  .indicator-list {
    grid-template-columns: 1fr;
  }

  .file-actions {
    flex-direction: column;
    align-items: stretch;

    .weight-editor,
    .action-buttons {
      width: 100%;
      justify-content: space-between;
    }

    .action-buttons {
      margin-top: var(--item-spacing);
    }
  }

  .assessment-editor-dialog,
  .weight-dialog {
    width: 90vw !important;
  }
}

@media (max-width: 480px) {
  .grade-management-container {
    --header-padding: 12px;
    --card-spacing: 10px;
    --section-spacing: 16px;
    --item-spacing: 8px;
  }

  .category-title-section h3 {
    font-size: 16px;
  }

  .indicator-name {
    font-size: 14px;
  }

  .preview-title h4 {
    font-size: 16px;
  }

  .editor-title {
    font-size: 14px;
  }

  .category-fixed-name {
    font-size: 14px;
  }

  .validation-result,
  .validation-summary {
    font-size: 12px;
  }
}
</style>

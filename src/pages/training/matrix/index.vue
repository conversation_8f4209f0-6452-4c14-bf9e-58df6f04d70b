<template>
  <div>
    <t-config-provider>
      <div class="dashboard" :class="[mode]">
        <!-- 控制栏 -->
        <div class="control-bar">
          <div class="left-controls">
            <t-button variant="outline" size="small" @click="exportData">
              <t-icon name="download" /> 导出
            </t-button>

            <!-- 批量操作工具 -->
            <t-dropdown :options="batchOptions" @click="handleBatchOperation">
              <t-button variant="outline" size="small">
                <t-icon name="tools" /> 批量操作
              </t-button>
            </t-dropdown>

            <t-button variant="outline" size="small" @click="showBatchFillDialog = true">
              <t-icon name="fill" /> 批量填充
            </t-button>
          </div>

          <div class="right-filters">
            <t-input
              v-model="searchText"
              size="small"
              placeholder="搜索课程..."
              clearable
              @change="handleSearch"
            >
              <template #prefix-icon>
                <t-icon name="search" />
              </template>
            </t-input>
          </div>
        </div>

        <!-- 数据表格 -->
        <div class="data-table">
          <table>
            <thead>
            <!-- 添加分组指标行 -->
            <tr>
              <th :rowspan="2" class="sticky-col">课程代码</th>
              <th :rowspan="2" class="sticky-col">课程名称</th>
              <th
                v-for="group in indicatorGroups"
                :key="group.name"
                :colspan="group.indicators.length"
                class="group-header"
              >
                {{ group.name }}
              </th>
            </tr>
            <!-- 原有指标行 -->
            <tr>
              <th
                v-for="indicator in indicators"
                :key="indicator.id"
              >
                {{ indicator.title }}
                <div class="indicator-sum">
                  {{ getColumnSum(indicator.id) }}
                </div>
              </th>
            </tr>
            </thead>

            <tbody>
            <tr v-for="(course, rowIndex) in filteredData" :key="course.courseId">
              <td class="sticky-col">{{ course.courseCode }}</td>
              <td class="sticky-col">
                {{ course.courseName }}
                <span class="row-count">({{ getRowCount(course.courseId) }})</span>
              </td>
              <td
                v-for="indicator in indicators"
                :key="indicator.id"
                :class="[
          'editable-cell',
          { 'editing': editingCell && editingCell.rowIndex === rowIndex && editingCell.indicator === indicator.id },
          getCellClass(matrix[String(course.courseId)][indicator.id].weight)
        ]"
              >
                <template v-if="editingCell && editingCell.rowIndex === rowIndex && editingCell.indicator === indicator.id">
                  <div @keydown.escape.prevent="handleEscapeKey">
                    <t-input-number
                      v-model="matrix[String(course.courseId)][indicator.id].weight"
                      :min="0"
                      :max="1"
                      :step="0.1"
                      :decimal-place="1"
                      theme="normal"
                      size="small"
                      :format="formatInput"
                      :parse="parseInput"
                      @enter="finishEditCell(course, indicator.id)"
                      @blur="finishEditCell(course, indicator.id)"
                    />
                  </div>
                </template>
                <template v-else>
                  <div class="cell-content" @click="startEditCell(rowIndex, indicator.id)">
            <span v-if="matrix[String(course.courseId)][indicator.id].weight !== 0">
              {{ formatValue(matrix[String(course.courseId)][indicator.id].weight) }}
              <span class="support-level" :class="'level-' + getLevel(matrix[String(course.courseId)][indicator.id].weight)">
                ({{ getLevel(matrix[String(course.courseId)][indicator.id].weight) }})
              </span>
            </span>
                    <span v-else class="empty-cell">
              点击编辑
            </span>
                  </div>
                </template>
              </td>
            </tr>
            </tbody>
          </table>
        </div>

        <!-- 批量填充对话框 -->
        <t-dialog
          v-model:visible="showBatchFillDialog"
          header="批量填充"
          width="480px"
          :footer="false"
        >
          <t-form :data="batchFillForm" @submit="handleBatchFill">
            <t-form-item label="选择指标">
              <t-select v-model="batchFillForm.indicator" placeholder="请选择要填充的指标">
                <t-option v-for="indicator in indicators" :key="indicator.id" :value="indicator.id" :label="indicator.title" />
              </t-select>
            </t-form-item>
            <t-form-item label="填充值">
              <t-input-number
                v-model="batchFillForm.value"
                :min="0"
                :max="1"
                :step="0.1"
                :decimal-place="1"
                placeholder="请输入填充值"
              />
            </t-form-item>
            <t-form-item label="应用范围">
              <t-radio-group v-model="batchFillForm.scope">
                <t-radio value="all">所有课程</t-radio>
                <t-radio value="filtered">当前筛选结果</t-radio>
                <t-radio value="empty">仅空值</t-radio>
              </t-radio-group>
            </t-form-item>
            <t-form-item>
              <t-space>
                <t-button theme="primary" type="submit">确认填充</t-button>
                <t-button theme="default" @click="showBatchFillDialog = false">取消</t-button>
              </t-space>
            </t-form-item>
          </t-form>
        </t-dialog>

      </div>
    </t-config-provider></div>
</template>

<script setup lang="ts">
import {ref, computed, onMounted, watch, nextTick} from 'vue';
import { useSettingStore } from '@/store';
import { MessagePlugin } from 'tdesign-vue-next';
import { useRoute } from 'vue-router';
import { getCourseList } from '@/api/training/course';
import { getPlan } from '@/api/training/plan';
import {
  getGraduationStandardDetail,
} from '@/api/base/standard';
import { getPoList } from '@/api/training/po';
import {
  getPoMatrixByPlanId,
  updatePoMatrix,
  addPoMatrix,
  deletePoMatrix,
  PoMatrix
} from '@/api/training/po-matrix';

const route = useRoute();
const id = ref(route.query.id);

interface Course {
  courseId: number;
  courseCode: string;
  courseName: string;
}

// 基础配置
const store = useSettingStore();
const mode = computed(() => store.displayMode);


const indicators = ref([]);
const indicatorGroups = ref([]);

// 状态管理
const searchText = ref('');
const sortConfig = ref({ key: 'courseId', asc: true });
const rawCourses = ref<Course[]>([]);

// 单元格编辑状态
const editingCell = ref<{ rowIndex: number; indicator: string } | null>(null);

// 批量操作相关
const showBatchFillDialog = ref(false);
const batchFillForm = ref({
  indicator: '',
  value: 0,
  scope: 'filtered'
});

const batchOptions = [
  { content: '清空所有', value: 'clear' },
  { content: '复制行', value: 'copy' },
  { content: '粘贴行', value: 'paste' },
  { content: '重置', value: 'reset' }
];

const matrix = ref<Record<string, Record<string, { weight: number; matrixId?: number }>>>({});

const plan = ref<any>(null);
const standard = ref<any>(null);
const po = ref<any>(null);
onMounted(async () => {
  if (!id.value) {
    // MessagePlugin.warning('未提供培养方案ID，无法加载支撑矩阵数据。');
    // return
    id.value = '9';
  }

  const planRes = await getPlan(Number(id.value));
  plan.value = planRes.data;
  const standardRes = await getGraduationStandardDetail(plan.value.standardId);
  standard.value = standardRes.data;

  const poRes = await getPoList(Number(id.value));
  po.value = poRes.data;
  const grouped = poRes.data.reduce((acc: any, item: any) => {
    const key = item.requirementId;
    if (!acc[key]) {
      acc[key] = [];
    }
    acc[key].push(item);
    return acc;
  }, {});

  indicatorGroups.value = standard.value.requirements.map((item: any) => {
    const key = item.id;
    let group = {
      name: item.standardName,
      indicators: [] as any[]
    }
    if (grouped[key]) {
      group.indicators = grouped[key].map((poItem: any) => {
        return {
          id: String(poItem.id),
          title: String(poItem.poTitle),
          requirementId: poItem.requirementId
        }
      })
      indicators.value.push(...group.indicators.map(ind => ({ id: String(ind.id), title: String(ind.title), requirementId: ind.requirementId })));
    }
    return group;
  });

  const res = await getCourseList({
    page: 1,
    pageSize: 1000,
    planId: id.value
  });
  rawCourses.value = res.data.records;

  // 初始化 matrix 数据结构
  const newMatrix: Record<string, Record<string, { weight: number; matrixId?: number }>> = {};
  if (rawCourses.value && indicators.value) {
    rawCourses.value.forEach(course => {
      newMatrix[String(course.courseId)] = {};
      indicators.value.forEach(indicator => {
        newMatrix[String(course.courseId)][indicator.id] = { weight: 0 };
      });
    });
  }
  matrix.value = newMatrix; // Assign initial structure

  const existingMatrixData = await getPoMatrixByPlanId(Number(plan.value.id));
  existingMatrixData.forEach((matrixItem: PoMatrix) => {
    const courseKey = String(matrixItem.courseId);
    const poKey = String(matrixItem.poId);
    if (matrix.value[courseKey] && matrix.value[courseKey][poKey]) {
      matrix.value[courseKey][poKey] = {
        weight: matrixItem.weight || 0,
        matrixId: matrixItem.id
      };
    } else {
      console.warn('Matrix cell not found for courseId:', courseKey, 'poId:', poKey, 'during population. Initializing.');
      if (!matrix.value[courseKey]) {
        matrix.value[courseKey] = {};
      }
      matrix.value[courseKey][poKey] = {
        weight: matrixItem.weight || 0,
        matrixId: matrixItem.id
      };
    }
  });

});


const filteredData = computed(() => {
  return rawCourses.value.filter(course => {
    const textMatch = course.courseCode.includes(searchText.value) ||
      course.courseName.includes(searchText.value);

    return textMatch;
  }).sort((a, b) => {
    const key = sortConfig.value.key as keyof Course;
    if (key === 'courseCode' || key === 'courseName') {
      return a[key].localeCompare(b[key]) * (sortConfig.value.asc ? 1 : -1);
    }
    return 0;
  });
});


const formatValue = (value: number) => {
  return value?.toFixed(1) || '';
};

const getLevel = (value: number): string => {
  if (value >= 0.7) return 'H';
  if (value >= 0.4) return 'M';
  return 'L';
};

const checkLevel = (value: number, level: string) => {
  const numValue = value || 0;
  switch(level) {
    case 'H': return numValue >= 0.7;
    case 'M': return numValue >= 0.4 && numValue < 0.7;
    case 'L': return numValue < 0.4;
    default: return true;
  }
};

const getCellClass = (value: number) => {
  const level = getLevel(value);
  return {
    'cell-H': level === 'H',
    'cell-M': level === 'M',
    'cell-L': level === 'L',
    'cell-zero': value === 0
  };
};

// 计算列总和
const getColumnSum = (indicatorId: string) => {
  let sum = 0;
  filteredData.value.forEach(course => {
    const weight = matrix.value[String(course.courseId)]?.[indicatorId]?.weight || 0;
    sum += weight;
  });
  return sum.toFixed(1);
};

// 计算行中大于0的权重数量
const getRowCount = (courseId: number) => {
  let count = 0;
  indicators.value.forEach(indicator => {
    const weight = matrix.value[String(courseId)]?.[indicator.id]?.weight || 0;
    if (weight > 0) count++;
  });
  return count;
};


const exportData = () => {
  // 导出逻辑实现...

};

const handleSearch = () => {
};

const startEditCell = (rowIndex: number, indicator: string) => {

  editingCell.value = { rowIndex, indicator };

  MessagePlugin.info('进入编辑模式');
  // 下一帧聚焦到输入框
  nextTick(() => {
    // 使用更精确的选择器，找到当前编辑单元格中的输入框
    const editingCellElement = document.querySelector('.editable-cell.editing .t-input-number input') as HTMLInputElement;
    if (editingCellElement) {
      editingCellElement.focus();
      editingCellElement.select();

    } else {

    }
  });
};

const finishEditCell = async (course: Course, indicatorIdToFind: string) => {
  if (editingCell.value === null) {
    return; // Already processed
  }

  const currentEditingRowIndex = editingCell.value.rowIndex;
  const currentEditingIndicatorId = editingCell.value.indicator;

  // Ensure we are finishing the correct cell that was being edited.
  if (course.courseId !== filteredData.value[currentEditingRowIndex]?.courseId || indicatorIdToFind !== currentEditingIndicatorId) {
    // Not the cell we started editing, or course data mismatch. This could happen with rapid blurs/enters.
    // console.warn("finishEditCell called on a different cell than expected or data mismatch");
    // editingCell.value = null; // Still clear the current edit regardless
    return;
  }

  editingCell.value = null; // Clear editing state immediately

  const indicator = indicators.value.find(ind => ind.id === indicatorIdToFind);
  if (!indicator || indicator.requirementId === undefined) {
    MessagePlugin.error('未找到指标或指标缺少关联的毕业要求细项ID');
    console.error('Indicator not found or missing requirementId for ID:', indicatorIdToFind);
    return;
  }

  const cellData = matrix.value[String(course.courseId)]?.[indicatorIdToFind];
  if (!cellData) {
    MessagePlugin.error('矩阵单元数据未找到，无法保存');
    console.error('Matrix cell data not found for courseId:', course.courseId, 'indicatorId:', indicatorIdToFind);
    return;
  }

  const payload: PoMatrix = {
    poId: Number(indicatorIdToFind),
    courseId: course.courseId,
    weight: cellData.weight, // This is the new weight from v-model
    planId: Number(plan.value.id),
    standardId: indicator.requirementId,
  };

  if (cellData.matrixId) {
    payload.id = cellData.matrixId;
  }

  try {
    const savedMatrixEntry = await addPoMatrix(payload);
    // Update matrixId in the local state if it was a new entry that now has an ID
    if (savedMatrixEntry && savedMatrixEntry.id && !cellData.matrixId) {
      matrix.value[String(course.courseId)][indicatorIdToFind].matrixId = savedMatrixEntry.id;
    }
    // The weight is already updated by v-model, ensure consistency if backend modifies it (though not expected here)
    matrix.value[String(course.courseId)][indicatorIdToFind].weight = savedMatrixEntry.weight !== undefined ? savedMatrixEntry.weight : cellData.weight;

    MessagePlugin.success('权重已更新');
  } catch (error) {
    MessagePlugin.error('更新权重失败');
    console.error('Error saving PoMatrix data:', error);
    // Potentially revert local optimistic update if needed, though v-model is source of truth for weight here
  }
};

const cancelEditCell = () => {

  editingCell.value = null;
  MessagePlugin.warning('操作已取消');
};

const handleEscapeKey = () => {

  cancelEditCell();
};

const handleBatchOperation = (data: any) => {

  // 实现批量操作逻辑
};

const handleBatchFill = (context: any) => {

};

const formatInput = (value: any): string => {
  if (value === null || value === undefined) return '';
  return value === 0 ? '' : String(value);
};

const parseInput = (text: any): number => {
  if (text === null || text === undefined || text.trim() === '') return 0;
  const num = Number(text);
  return isNaN(num) ? 0 : num;
};

</script>

<style lang="less" scoped>
.dashboard {
  padding: 6px; /* 原16px */
  background: #f8fafc;
  font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
}

.control-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 8px; /* 原12px */
  margin-bottom: 6px; /* 原16px */
  padding: 6px 8px; /* 原8px 12px */
  background: white;
  border-radius: 3px; /* 原4px */
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05); /* 原3px */
  border: 1px solid #e1e5eb;


  .left-controls {
    display: flex;
    gap: 6px; /* 原8px */
    align-items: center;
  }

  .right-filters {
    display: flex;
    gap: 6px; /* 原8px */
    align-items: center;

    > * {
      width: 200px; /* 原200px */
    }
  }
}

.data-table {
  background: white;
  border-radius: 3px; /* 原4px */
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05); /* 原3px */
  overflow-x: auto;
  font-size: 12px; /* 原13px */
  margin-bottom: 6px; /* 原16px */
  border: 1px solid #e1e5eb;

  .summary-col {
    background: #f1f5f9;
    font-weight: 600;
    color: #334155;
    min-width: 50px;
  }

  .indicator-sum {
    font-size: 0.7em;
    color: #64748b;
    margin-top: 1px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1px;
    font-weight: normal;
  }
  .row-count {
    font-size: 0.8em;
    color: #64748b;
    margin-left: 4px;
    font-weight: normal;
  }

  .indicator-sum {
    font-size: 0.7em;
    color: #64748b;
    margin-top: 1px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1px;
    font-weight: normal;
  }

  table {
    width: 100%;
    border-collapse: collapse;
    min-width: 500px; /* 原800px */

    th, td {
      padding: 6px 8px; /* 原8px 12px */
      border-bottom: 1px solid #e1e5eb;
      text-align: center;
      min-width: 50px; /* 原80px */
      height: 26px; /* 原36px */

      &.sticky-col {
        position: sticky;
        left: 0;
        background: white;
        z-index: 2;
        box-shadow: 1px 0 1px rgba(0, 0, 0, 0.05); /* 原2px */
        min-width: 50px; /* 原100px */
      }
    }

    th {
      background: #f1f5f9;
      font-weight: 600;
      color: #334155;
      font-size: 12px; /* 原13px */
      white-space: nowrap;
      border-top: 1px solid #e1e5eb;

      &:hover {
        background: #e9edf2;
      }

      .indicator-sum {
        font-size: 0.7em; /* 原0.8em */
        color: #64748b;
        margin-top: 1px; /* 原2px */
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 1px; /* 原2px */
      }
    }

    td {
      transition: background 0.2s;
      white-space: nowrap;
      color: #1e293b;

      &.cell-H {
        background: #e0f2fe;
        border-left: 2px solid #0ea5e9; /* 原3px */
      }
      &.cell-M {
        background: #fef9c3;
        border-left: 2px solid #facc15; /* 原3px */
      }
      &.cell-L {
        background: #dcfce7;
        border-left: 2px solid #4ade80; /* 原3px */
      }
      &.cell-zero {
        background: #f8fafc;
        color: #94a3b8;
        border-left: 2px solid #e2e8f0; /* 原3px */
      }

      &.editable-cell {
        cursor: pointer;
        position: relative;

        &:hover {
          background-color: rgba(56, 182, 255, 0.08);

          .edit-hint {
            opacity: 1;
          }
        }

        &.editing {
          background-color: rgba(56, 182, 255, 0.15);
          box-shadow: inset 0 0 0 1px #38b6ff;
        }

        .cell-content {
          position: relative;
          padding: 4px; /* 原6px */
          border-radius: 1px; /* 原2px */
          transition: all 0.2s ease;
          min-height: 20px; /* 原24px */
          display: flex;
          align-items: center;
          justify-content: center;
          font-weight: 500;
        }

        .edit-hint {
          position: absolute;
          top: 1px; /* 原2px */
          right: 1px; /* 原2px */
          opacity: 0;
          transition: opacity 0.2s ease;
          color: #38b6ff;
        }

        .empty-cell {
          color: #94a3b8;
          font-style: italic;
          font-size: 11px; /* 原12px */
          font-weight: normal;
        }
      }

      .support-level {
        font-size: 0.65em; /* 原0.75em */
        margin-left: 3px; /* 原4px */
        font-weight: 600;

        &.level-H {
          color: #0369a1;
        }
        &.level-M {
          color: #a16207;
        }
        &.level-L {
          color: #166534;
        }
      }
    }

    .group-header {
      background: #e2e8f0;
      font-weight: 600;
      color: #1e293b;
      border-top: 1px solid #cbd5e1;
    }
  }
}

.t-input-number {
  width: 70px; /* 原80px */

  :deep(.t-input__inner) {
    padding: 0 6px; /* 原8px */
    height: 24px; /* 原28px */
    line-height: 24px; /* 原28px */
    text-align: center;
    font-size: 12px; /* 原13px */
    border: 1px solid #cbd5e1;
    border-radius: 2px; /* 原3px */
  }

  :deep(.t-input-number__decrease),
  :deep(.t-input-number__increase) {
    width: 20px; /* 原24px */
    background: #f1f5f9;
    border: 1px solid #cbd5e1;
  }
}

.t-dialog {
  :deep(.t-dialog__header) {
    padding: 12px 16px; /* 原16px 20px */
    background: #f1f5f9;
    border-bottom: 1px solid #e1e5eb;
    font-weight: 600;
  }

  :deep(.t-dialog__body) {
    padding: 16px; /* 原20px */
  }
}

.t-form-item {
  margin-bottom: 12px; /* 原16px */
}

@media (max-width: 768px) {
  .dashboard {
    padding: 8px; /* 原12px */
  }
}
</style>

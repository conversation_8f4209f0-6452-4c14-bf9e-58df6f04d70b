<template>
  <t-config-provider>
    <div :class="[mode]" class="overview-container">
      <!-- 侧边导航栏 -->
      <t-menu theme="light" value="dashboard" class="side-menu">
        <t-menu-item value="dashboard">
          <template #icon>
            <t-icon name="dashboard" />
          </template>
          培养方案首页
        </t-menu-item>
        <t-menu-item value="introduction">
          <template #icon>
            <t-icon name="info-circle" />
          </template>
          培养方案介绍
        </t-menu-item>
        <t-menu-item value="objectives">
          <template #icon>
            <t-icon name="goal" />
          </template>
          培养目标管理
        </t-menu-item>
        <t-menu-item value="requirements">
          <template #icon>
            <t-icon name="file-certificate" />
          </template>
          毕业要求管理
        </t-menu-item>
        <t-menu-item value="courses">
          <template #icon>
            <t-icon name="book" />
          </template>
          课程体系管理
        </t-menu-item>
        <t-menu-item value="matrix">
          <template #icon>
            <t-icon name="table" />
          </template>
          课程-毕业要求矩阵
        </t-menu-item>
        <t-menu-item value="instructors">
          <template #icon>
            <t-icon name="user" />
          </template>
          课程负责人管理
        </t-menu-item>
        <t-menu-item value="achievement">
          <template #icon>
            <t-icon name="chart-bar" />
          </template>
          专业达成度管理
        </t-menu-item>
      </t-menu>

      <!-- 主要内容区域 -->
      <div class="main-content">
        <!-- 顶部概览卡片 -->
        <div class="overview-cards">
          <t-card v-for="card in courseIndicatorCards" :key="card.title" :title="card.title" class="overview-card">
            <div class="card-content">
              <span class="card-value">{{ card.value }}</span>
              <span v-if="card.change" class="card-change" :class="card.trend">
                <trend-arrow-up v-if="card.trend === 'up'" />
                <trend-arrow-down v-else />
                {{ card.change }}
              </span>
            </div>
            <div v-if="card.footer" class="card-footer">{{ card.footer }}</div>
          </t-card>
        </div>

        <!-- 第一行图表区域 -->
        <div class="chart-area">
          <t-card title="不同平台课程门数占比分析" class="chart-card">
            <pie-chart :data="coursePlatformDistribution" />
          </t-card>

          <t-card title="各类课程学分占比统计" class="chart-card">
            <pie-chart :data="courseCreditDistribution" />
          </t-card>
        </div>

        <!-- 第二行图表区域 -->
        <div class="chart-area">
          <t-card title="各类必修课程理教与课内实践学分分配" class="chart-card">
            <bar-chart :data="theoryPracticeCreditData" />
          </t-card>

          <t-card title="实践课程与集中实践课程" class="chart-card">
            <bar-chart :data="practiceCourseData" />
          </t-card>
        </div>

        <!-- 第三行图表区域 -->
        <div class="chart-area">
          <t-card title="各学期课程数量统计" subtitle="红色:考试, 黄色:考查" class="chart-card full-width">
            <bar-chart :data="semesterCourseCountData" />
          </t-card>
        </div>

        <!-- 第四行图表区域 -->
        <div class="chart-area">
          <t-card title="各学期不同类别课程统计" class="chart-card full-width">
            <stacked-bar-chart :data="semesterCourseTypeData" />
          </t-card>
        </div>
      </div>
    </div>
  </t-config-provider>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useSettingStore } from '@/store';
import PieChart from './PieChart.vue';
import BarChart from './BarChart.vue';
import StackedBarChart from './StackedBarChart.vue';

const store = useSettingStore();

const mode = computed(() => {
  return store.displayMode;
});

// 课程指标卡片数据
const courseIndicatorCards = [
  {
    title: '总课程数',
    value: '86门',
    footer: '不包含第二课堂与各类选修课'
  },
  {
    title: '必修课数',
    value: '32门',
    footer: '通识教育平台-必修门数'
  },
  {
    title: '专业课程数',
    value: '48门',
    footer: '学科基础+专业教育'
  },
  {
    title: '专业选修课',
    value: '24门',
    footer: '限选课数量'
  },
  {
    title: '集中实践课程',
    value: '18门',
    change: '2门',
    trend: 'up',
    footer: '较上年度增加2门'
  },
  {
    title: '核心课程总数',
    value: '28门'
  },
  {
    title: '开课学时数',
    value: '2560学时'
  },
  {
    title: '俄方授课学时',
    value: '480学时',
    footer: '俄方担任专业核心课程的学时数'
  }
];

// 不同平台课程门数占比分析
const coursePlatformDistribution = [
  { value: 32, name: '通识教育平台' },
  { value: 24, name: '学科基础平台' },
  { value: 24, name: '专业教育平台' },
  { value: 6, name: '集中实践环节' }
];

// 各类课程学分占比统计
const courseCreditDistribution = [
  { value: 48, name: '通识教育学分' },
  { value: 36, name: '学科基础学分' },
  { value: 42, name: '专业教育学分' },
  { value: 24, name: '实践教学学分' }
];

// 各类必修课程理教与课内实践学分分配
const theoryPracticeCreditData = {
  categories: ['思想政治', '外语', '数学', '物理', '计算机', '专业基础'],
  series: [
    {
      name: '理论教学',
      data: [12, 16, 10, 8, 6, 20]
    },
    {
      name: '课内实践',
      data: [2, 4, 2, 2, 4, 8]
    }
  ]
};

// 实践课程与集中实践课程
const practiceCourseData = {
  categories: ['实验课程', '课程设计', '实习实训', '毕业设计', '创新创业'],
  series: [
    {
      name: '实践课程',
      data: [12, 8, 6, 10, 4]
    },
    {
      name: '集中实践',
      data: [4, 6, 8, 12, 2]
    }
  ]
};

// 各学期课程数量统计 (红:考试, 黄:考查)
const semesterCourseCountData = {
  categories: ['第一学期', '第二学期', '第三学期', '第四学期', '第五学期', '第六学期', '第七学期', '第八学期'],
  series: [
    {
      name: '考试课程',
      data: [6, 8, 10, 12, 10, 8, 6, 2],
      color: '#f56c6c'
    },
    {
      name: '考查课程',
      data: [4, 6, 4, 6, 8, 10, 8, 4],
      color: '#e6a23c'
    }
  ]
};

// 各学期不同类别课程统计
const semesterCourseTypeData = {
  categories: ['第一学期', '第二学期', '第三学期', '第四学期', '第五学期', '第六学期', '第七学期', '第八学期'],
  series: [
    {
      name: '通识教育',
      data: [10, 8, 6, 4, 2, 2, 0, 0]
    },
    {
      name: '学科基础',
      data: [0, 4, 6, 8, 6, 4, 2, 0]
    },
    {
      name: '专业教育',
      data: [0, 2, 2, 6, 10, 12, 12, 6]
    }
  ]
};
</script>

<style lang="less" scoped>
.overview-container {
  display: flex;
  min-height: 100vh;
  background-color: var(--td-bg-color-container);

  &.dark {
    background-color: var(--td-bg-color-page);
  }
}

.side-menu {
  width: 220px;
  height: 100vh;
  position: sticky;
  top: 0;
  flex-shrink: 0;
  border-right: 1px solid var(--td-component-stroke);
}

.main-content {
  flex: 1;
  padding: 16px;
  overflow: auto;
}

.overview-cards {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
  margin-bottom: 16px;

  @media (max-width: 1200px) {
    grid-template-columns: repeat(2, 1fr);
  }

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
}

.overview-card {
  .card-content {
    display: flex;
    align-items: flex-end;
    margin-bottom: 8px;

    .card-value {
      font-size: 24px;
      font-weight: 500;
      margin-right: 12px;
    }

    .card-change {
      font-size: 14px;

      &.up {
        color: var(--td-error-color);
      }

      &.down {
        color: var(--td-success-color);
      }
    }
  }

  .card-footer {
    font-size: 12px;
    color: var(--td-text-color-secondary);
  }
}

.chart-area {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  margin-bottom: 16px;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
}

.chart-card {
  height: 350px;

  &.full-width {
    grid-column: 1 / -1;
  }

  :deep(.t-card__body) {
    height: calc(100% - 56px);
  }
}
</style>

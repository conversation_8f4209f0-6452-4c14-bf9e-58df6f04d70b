<template>
  <div class="course-question-bank">
    <t-card title="课程题库管理" subtitle="管理课程相关的试题和题库" header-bordered>
      <template #actions>
        <t-row justify="end">
          <t-button theme="primary" @click="addNewQuestion">
            <template #icon>
              <t-icon name="add" />
            </template>
            添加试题
          </t-button>
          <t-button theme="default" @click="importQuestions" style="margin-left: 8px;">
            <template #icon>
              <t-icon name="upload" />
            </template>
            批量导入
          </t-button>
        </t-row>
      </template>

      <!-- 筛选区域 -->
      <t-form ref="form" :data="formData" layout="inline" @reset="onReset" @submit="onSubmit">
        <t-form-item label="题型" name="questionType">
          <t-select v-model="formData.questionType" placeholder="请选择题型" clearable style="width: 180px">
            <t-option v-for="item in questionTypes" :key="item.value" :label="item.label" :value="item.value" />
          </t-select>
        </t-form-item>
        <t-form-item label="难度" name="difficulty">
          <t-select v-model="formData.difficulty" placeholder="请选择难度" clearable style="width: 180px">
            <t-option v-for="item in difficultyOptions" :key="item.value" :label="item.label" :value="item.value" />
          </t-select>
        </t-form-item>
        <t-form-item label="知识点" name="knowledgePoint">
          <t-select v-model="formData.knowledgePoint" placeholder="请选择知识点" clearable style="width: 200px">
            <t-option v-for="item in knowledgePoints" :key="item.value" :label="item.label" :value="item.value" />
          </t-select>
        </t-form-item>
        <t-form-item label="关键词" name="keyword">
          <t-input v-model="formData.keyword" placeholder="请输入试题关键词" style="width: 200px" />
        </t-form-item>
        <t-form-item>
          <t-button theme="primary" type="submit">查询</t-button>
          <t-button theme="default" type="reset" style="margin-left: 8px">重置</t-button>
        </t-form-item>
      </t-form>

      <!-- 表格区域 -->
      <t-table
        :data="questionList"
        :columns="columns"
        :hover="true"
        :loading="loading"
        :pagination="pagination"
        @page-change="onPageChange"
        style="margin-top: 20px"
      >
        <!-- 操作列的具名插槽 -->
        <template #operation="{ row }">
          <div class="operation-container">
            <t-button theme="primary" variant="text" @click="editQuestion(row)">
              编辑
            </t-button>
            <t-button theme="danger" variant="text" @click="deleteQuestion(row.id)">
              删除
            </t-button>
            <t-button theme="default" variant="text" @click="viewQuestion(row)">
              查看
            </t-button>
          </div>
        </template>
      </t-table>
    </t-card>

    <!-- 新增/编辑试题对话框 -->
    <t-dialog
      v-model:visible="dialogVisible"
      :header="dialogTitle"
      width="70%"
      :footer="false"
      @close="closeDialog"
    >
      <t-form ref="questionForm" :data="questionFormData" :rules="rules" label-width="100px">
        <t-form-item label="题型" name="type">
          <t-select v-model="questionFormData.type" placeholder="请选择题型">
            <t-option v-for="item in questionTypes" :key="item.value" :label="item.label" :value="item.value" />
          </t-select>
        </t-form-item>
        <t-form-item label="难度" name="difficulty">
          <t-select v-model="questionFormData.difficulty" placeholder="请选择难度">
            <t-option v-for="item in difficultyOptions" :key="item.value" :label="item.label" :value="item.value" />
          </t-select>
        </t-form-item>
        <t-form-item label="知识点" name="knowledgePoints">
          <t-select v-model="questionFormData.knowledgePoints" placeholder="请选择知识点" multiple>
            <t-option v-for="item in knowledgePoints" :key="item.value" :label="item.label" :value="item.value" />
          </t-select>
        </t-form-item>
        <t-form-item label="试题内容" name="content">
          <t-textarea v-model="questionFormData.content" placeholder="请输入试题内容" autosize />
        </t-form-item>
        
        <div v-if="['choice', 'multipleChoice'].includes(questionFormData.type)">
          <t-form-item 
            v-for="(option, index) in questionFormData.options" 
            :key="index" 
            :label="`选项${String.fromCharCode(65 + index)}`" 
            :name="`options.${index}.content`"
          >
            <t-input v-model="option.content" placeholder="请输入选项内容" />
            <t-checkbox v-model="option.isCorrect" style="margin-left: 10px">正确选项</t-checkbox>
          </t-form-item>
          <t-button theme="primary" variant="text" @click="addOption">
            <template #icon><t-icon name="add" /></template>
            添加选项
          </t-button>
        </div>
        
        <t-form-item v-if="questionFormData.type === 'tf'" label="正确答案" name="answer">
          <t-radio-group v-model="questionFormData.answer">
            <t-radio value="true">正确</t-radio>
            <t-radio value="false">错误</t-radio>
          </t-radio-group>
        </t-form-item>
        
        <t-form-item v-if="['shortAnswer', 'essay'].includes(questionFormData.type)" label="参考答案" name="answer">
          <t-textarea v-model="questionFormData.answer" placeholder="请输入参考答案" autosize />
        </t-form-item>
        
        <t-form-item label="分数" name="score">
          <t-input-number v-model="questionFormData.score" min="0" step="0.5" />
        </t-form-item>
        
        <t-form-item>
          <t-button theme="primary" @click="saveQuestion">保存</t-button>
          <t-button theme="default" @click="closeDialog" style="margin-left: 8px">取消</t-button>
        </t-form-item>
      </t-form>
    </t-dialog>

    <!-- 导入试题对话框 -->
    <t-dialog
      v-model:visible="importDialogVisible"
      header="批量导入试题"
      :footer="false"
    >
      <t-upload
        theme="file"
        :action="importUrl"
        :headers="uploadHeaders"
        accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
        :max-files="1"
        :on-success="onImportSuccess"
        :on-fail="onImportFail"
      >
        <t-button theme="primary">
          <template #icon><t-icon name="upload" /></template>
          点击上传
        </t-button>
        <template #tip>
          <p>支持xlsx、xls格式，请下载<a href="javascript:void(0)" @click="downloadTemplate">试题导入模板</a></p>
        </template>
      </t-upload>
    </t-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { MessagePlugin } from 'tdesign-vue-next';
import { useRoute } from 'vue-router';

// 路由参数
const route = useRoute();
const courseId = ref(Number(route.params.courseId) || 0);

// 表单数据
const formData = reactive({
  questionType: '',
  difficulty: '',
  knowledgePoint: '',
  keyword: '',
});

// 表格加载状态
const loading = ref(false);

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showJumper: true,
  showPageSize: true,
  onChange: (pageInfo: { current: number; pageSize: number }) => {
    pagination.current = pageInfo.current;
    pagination.pageSize = pageInfo.pageSize;
    fetchQuestionList();
  },
});

// 题型选项
const questionTypes = [
  { label: '单选题', value: 'choice' },
  { label: '多选题', value: 'multipleChoice' },
  { label: '判断题', value: 'tf' },
  { label: '简答题', value: 'shortAnswer' },
  { label: '论述题', value: 'essay' },
];

// 难度选项
const difficultyOptions = [
  { label: '简单', value: 'easy' },
  { label: '中等', value: 'medium' },
  { label: '困难', value: 'hard' },
];

// 知识点选项（实际应用中应该从后端获取）
const knowledgePoints = ref([
  { label: '软件工程基础', value: 'se-basics' },
  { label: '需求分析', value: 'requirements' },
  { label: '系统设计', value: 'design' },
  { label: '编码与测试', value: 'coding-testing' },
  { label: '项目管理', value: 'project-management' },
]);

// 表格列配置
const columns = [
  { colKey: 'id', title: 'ID', width: 80 },
  { colKey: 'type', title: '题型', width: 100 },
  { colKey: 'content', title: '题目内容', ellipsis: true },
  { colKey: 'difficulty', title: '难度', width: 80 },
  { colKey: 'score', title: '分数', width: 80 },
  {
    colKey: 'operation',
    title: '操作',
    width: 160,
    cell: 'operation'  // 使用具名插槽代替JSX
  },
];

// 问题列表
const questionList = ref<any[]>([
  {
    id: 1,
    type: '单选题',
    content: '软件生命周期包含哪些阶段？',
    difficulty: '中等',
    score: 5,
  },
  {
    id: 2,
    type: '多选题',
    content: '以下哪些是敏捷开发方法？',
    difficulty: '简单',
    score: 4,
  },
  {
    id: 3,
    type: '判断题',
    content: '瀑布模型适合需求频繁变化的项目。',
    difficulty: '简单',
    score: 2,
  },
  {
    id: 4,
    type: '简答题',
    content: '简述软件测试的主要方法。',
    difficulty: '困难',
    score: 10,
  },
]);

// 对话框相关
const dialogVisible = ref(false);
const dialogTitle = ref('新增试题');
const importDialogVisible = ref(false);
const importUrl = '/api/questions/import';

// 上传文件的请求头
const uploadHeaders = {
  Authorization: 'Bearer token',
};

// 表单校验规则
const rules = {
  type: [{ required: true, message: '请选择题型', type: 'error' }],
  difficulty: [{ required: true, message: '请选择难度', type: 'error' }],
  content: [{ required: true, message: '请输入试题内容', type: 'error' }],
  score: [{ required: true, message: '请输入分数', type: 'error' }],
};

// 问题表单数据
const questionFormData = reactive({
  id: null,
  type: '',
  difficulty: '',
  knowledgePoints: [],
  content: '',
  options: [
    { content: '', isCorrect: false },
    { content: '', isCorrect: false },
    { content: '', isCorrect: false },
    { content: '', isCorrect: false },
  ],
  answer: '',
  score: 5,
});

// 方法：添加选项
const addOption = () => {
  questionFormData.options.push({ content: '', isCorrect: false });
};

// 方法：获取题目列表
const fetchQuestionList = () => {
  loading.value = true;
  setTimeout(() => {
    // 模拟API调用
    pagination.total = 50;
    loading.value = false;
  }, 500);
};

// 方法：重置表单
const onReset = () => {
  Object.keys(formData).forEach(key => {
    formData[key as keyof typeof formData] = '';
  });
};

// 方法：提交表单
const onSubmit = () => {
  fetchQuestionList();
};

// 方法：分页变化
const onPageChange = (pageInfo: { current: number; pageSize: number }) => {
  pagination.current = pageInfo.current;
  pagination.pageSize = pageInfo.pageSize;
  fetchQuestionList();
};

// 方法：添加试题
const addNewQuestion = () => {
  dialogTitle.value = '新增试题';
  // 重置表单数据
  Object.keys(questionFormData).forEach(key => {
    if (key === 'options') {
      questionFormData.options = [
        { content: '', isCorrect: false },
        { content: '', isCorrect: false },
        { content: '', isCorrect: false },
        { content: '', isCorrect: false },
      ];
    } else if (key === 'score') {
      questionFormData.score = 5;
    } else {
      questionFormData[key as keyof typeof questionFormData] = '';
    }
  });
  dialogVisible.value = true;
};

// 方法：编辑试题
const editQuestion = (row: any) => {
  dialogTitle.value = '编辑试题';
  // 填充表单数据
  questionFormData.id = row.id;
  questionFormData.type = row.type === '单选题' ? 'choice' : 
                          row.type === '多选题' ? 'multipleChoice' : 
                          row.type === '判断题' ? 'tf' :
                          row.type === '简答题' ? 'shortAnswer' : 'essay';
  questionFormData.difficulty = row.difficulty === '简单' ? 'easy' : 
                                row.difficulty === '中等' ? 'medium' : 'hard';
  questionFormData.content = row.content;
  questionFormData.score = row.score;
  // 其他属性需要根据实际数据结构进行处理
  
  dialogVisible.value = true;
};

// 方法：查看试题
const viewQuestion = (row: any) => {
  // 实现查看试题的逻辑，可以使用对话框或新页面展示详情
  MessagePlugin.info(`查看试题：${row.id}`);
};

// 方法：删除试题
const deleteQuestion = (id: number) => {
  MessagePlugin.confirm({
    header: '确认删除',
    body: '确定要删除这道试题吗？此操作不可恢复！',
    confirmBtn: '确定',
    cancelBtn: '取消',
    onConfirm: () => {
      // 实现删除逻辑
      MessagePlugin.success('删除成功');
      // 重新加载数据
      fetchQuestionList();
    },
  });
};

// 方法：保存试题
const saveQuestion = () => {
  // 这里应该有表单验证和提交逻辑
  MessagePlugin.success(questionFormData.id ? '编辑成功' : '添加成功');
  dialogVisible.value = false;
  fetchQuestionList();
};

// 方法：关闭对话框
const closeDialog = () => {
  dialogVisible.value = false;
};

// 方法：批量导入试题
const importQuestions = () => {
  importDialogVisible.value = true;
};

// 方法：导入成功回调
const onImportSuccess = () => {
  MessagePlugin.success('导入成功');
  importDialogVisible.value = false;
  fetchQuestionList();
};

// 方法：导入失败回调
const onImportFail = () => {
  MessagePlugin.error('导入失败，请检查文件格式是否正确');
};

// 方法：下载模板
const downloadTemplate = () => {
  window.open('/api/questions/template/download');
};

// 生命周期钩子
onMounted(() => {
  fetchQuestionList();
});
</script>

<style lang="less" scoped>
.course-question-bank {
  padding-bottom: 20px;
  
  .operation-container {
    display: flex;
    justify-content: space-around;
  }
  
  :deep(.t-upload__dragger) {
    width: 100%;
    height: 150px;
  }
}
</style>

<template>
  <div class="teacher-course-overview">

    <!-- 课程头部信息 -->
    <div class="course-header">
      <div class="course-basic-info">
        <div class="course-title">
          <h1>{{ courseInfo.name }}</h1>
          <t-tag theme="primary" size="medium">{{ courseInfo.code }}</t-tag>
        </div>
        <div class="course-meta">
          <div class="meta-item">
            <span class="label">学分：</span>
            <span class="value">{{ courseInfo.credits }}</span>
          </div>
          <div class="meta-item">
            <span class="label">课程性质：</span>
            <span class="value">{{ getCourseNatureLabel(courseInfo.nature) }}</span>
          </div>
          <div class="meta-item">
            <span class="label">开课学期：</span>
            <span class="value">{{ courseInfo.semester }}</span>
          </div>
          <div class="meta-item">
            <span class="label">课程状态：</span>
            <t-tag :theme="courseStatusTheme" size="small">{{ courseInfo.status }}</t-tag>
          </div>
        </div>
      </div>


    </div>

    <!-- 页面内容 -->
    <div class="page-content">
      <!-- 统计卡片 -->
      <div class="stats-section">
        <div class="section-title">
          <h2>课程统计</h2>
        </div>
        <div class="stats-grid">
          <div class="stat-card" v-for="stat in statsData" :key="stat.key">
            <div class="stat-icon">
              <t-icon :name="stat.icon" size="24px" />
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ stat.value }}</div>
              <div class="stat-label">{{ stat.label }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 授课班级 -->
      <div class="classes-section">
        <div class="section-title">
          <h2>授课班级</h2>
        </div>
        <div class="classes-grid">
          <!-- 现有班级卡片 -->
          <ClassCard
            v-for="worklist in worklistData"
            :key="worklist.id"
            :worklist-data="worklist"
            @card-click="handleClassDetail"
            @manage-students="handleManageStudents"
            @import-students="handleImportStudents"
            @edit-class="handleEditClass"
            @delete-class="handleDeleteClass"
          />

          <!-- 新增班级卡片 -->
          <AddClassCard @add-class="handleAddClass" />
        </div>
      </div>

      <!-- 课程团队 -->
      <div class="team-section">
        <div class="section-title">
          <h2>课程团队</h2>
        </div>
        <div class="team-grid">
          <MemberCard
            v-for="member in teamMembersData"
            :key="member.id"
            :member-data="member"
            @view-profile="handleViewProfile"
            @contact="handleContactMember"
          />

          <!-- 空状态 -->
          <div v-if="teamMembersData.length === 0" class="empty-state">
            <t-icon name="user" size="48px" />
            <p>暂无团队成员</p>
            <t-button theme="primary" @click="handleManageTeam">
              <template #icon>
                <t-icon name="user-add" />
              </template>
              管理团队
            </t-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 新增班级对话框 -->
    <AddClassDialog
      v-model:visible="addClassDialogVisible"
      :course-id="courseId"
      @success="loadCourseData"
    />

    <!-- 学生导入对话框 -->
    <ImportDialog
      :visible="importDialogVisible"
      :config="importConfig"
      :callbacks="importCallbacks"
      @update:visible="handleImportDialogVisible"
    />

    <!-- 学生管理对话框 -->
    <StudentManagementDialog
      v-model:visible="studentManagementVisible"
      :class-id="currentClassId"
      :class-name="currentClassName"
      @success="loadCourseData"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import {
  Icon as TIcon,
  Tag as TTag,
  MessagePlugin
} from 'tdesign-vue-next'
import ClassCard from '@/components/ClassCard/index.vue'
import MemberCard from '@/components/MemberCard/index.vue'
import AddClassDialog from '@/components/AddClassDialog/index.vue'
import AddClassCard from '@/components/AddClassCard/index.vue'
import ImportDialog from '@/components/ImportDialog/index.vue'
import StudentManagementDialog from '@/components/StudentManagementDialog/index.vue'
import type { ImportConfig, ImportCallbacks } from '@/components/ImportDialog/types'
import type { WorklistItem, TeamMemberItem } from '@/api/base/classes'
import {
  getCourseTaskInfo,
  getCourseTaskStatistics,
  getCourseTaskClasses,
  getCourseTaskTeachers
} from '@/api/training/course'
import SemesterUtils from '@/utils/semesterUtils'
import { useDictByTypeTitle, useDict } from '@/hooks/useDict'
import { initializeGlobalDict } from '@/utils/dictUtil'

// 路由相关
const route = useRoute()
const router = useRouter()

// 课程ID和任务ID
const courseId = computed(() => route.params.courseId as string)
const taskId = computed(() => route.params.taskId as string)

// 验证必要参数
const validateRequiredParams = () => {
  console.log('courseId:', courseId.value)
  console.log('taskId:', taskId.value)
  if (!taskId.value || taskId.value === ':taskId') {
    MessagePlugin.warning('缺少必要的任务ID参数，请从工作台正确进入课程页面')
    setTimeout(() => {
      router.push({ name: 'DashboardTeacher' })
    }, 2000)
    return false
  }

  if (!courseId.value || taskId.value === ':courseId') {
    MessagePlugin.warning('缺少必要的课程ID参数，请从工作台正确进入课程页面')
    setTimeout(() => {
      router.push({ name: 'DashboardTeacher' })
    }, 2000)
    return false
  }

  return true
}

// 字典数据
const {
  dictOptions: courseNatureOptions,
  loading: courseNatureLoading,
  error: courseNatureError
} = useDictByTypeTitle('课程性质')

const {
  dictOptions: teacherRoleOptions,
  loading: teacherRoleLoading,
  error: teacherRoleError
} = useDictByTypeTitle('教师角色')

// 获取全局字典状态
const { globalState: dictGlobalState } = useDict()

// 字典转换工具函数
const createDictLabelGetter = (
  options: any,
  loading: any,
  error: any,
  dictName: string
) => {
  return (value: string | number) => {
    // 如果还在加载中，返回原值
    if (loading.value) {
      return `${value} (加载中...)`
    }
    // 如果有错误，返回原值
    if (error.value) {
      console.error(`${dictName}字典加载错误:`, error.value)
      return value
    }

    const option = options.value?.find((item: any) => item.value === value.toString())
    return option?.label || value
  }
}

// 创建字典标签获取器
const getCourseNatureLabel = createDictLabelGetter(
  courseNatureOptions,
  courseNatureLoading,
  courseNatureError,
  '课程性质'
)

const getTeacherRoleLabel = createDictLabelGetter(
  teacherRoleOptions,
  teacherRoleLoading,
  teacherRoleError,
  '教师角色'
)

// 课程基本信息
const courseInfo = ref({
  id: '',
  name: '',
  code: '',
  credits: 0,
  nature: '',
  semester: '',
  status: ''
})

// 课程状态主题
const courseStatusTheme = computed(() => {
  const statusMap: Record<string, 'default' | 'primary' | 'danger' | 'warning' | 'success'> = {
    '进行中': 'success',
    '已结课': 'default',
    '准备中': 'warning'
  }
  return statusMap[courseInfo.value.status] || 'default'
})

// 统计数据
const statsData = ref([
  {
    key: 'classes',
    label: '授课班级',
    value: '0',
    icon: 'layers'
  },
  {
    key: 'students',
    label: '学生总数',
    value: '0',
    icon: 'user'
  },
  {
    key: 'assessments',
    label: '考核项目',
    value: '0',
    icon: 'assignment'
  },
  {
    key: 'average',
    label: '平均成绩',
    value: '--',
    icon: 'chart-line'
  }
])

// 教学任务数据 (授课班级)
const worklistData = ref<WorklistItem[]>([])

// 团队成员数据
const teamMembersData = ref<TeamMemberItem[]>([])

// 对话框状态
const addClassDialogVisible = ref(false)
const importDialogVisible = ref(false)
const studentManagementVisible = ref(false)
const currentClassId = ref<string | number>('')
const currentClassName = ref('')

// 学生导入配置
const importConfig: ImportConfig = {
  title: '导入学生',
  tips: '请按照模板格式填写学生信息，支持批量导入学生到班级',
  templateFileName: '学生导入模板.xlsx',
  templateData: [
    ['学号', '姓名', '班级名称', '备注'],
    ['2021001', '张三', 'RB软工数231', ''],
    ['2021002', '李四', 'RB软工数231', ''],
    ['2021003', '王五', 'RB软工数232', '']
  ],
  acceptTypes: ['.xlsx', '.xls', '.csv'],
  maxFileSize: 10
}

// 学生导入回调函数
const importCallbacks: ImportCallbacks = {
  onImport: async (file: File) => {
    // 模拟导入API调用
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        // 模拟导入结果
        const random = Math.random()
        if (random > 0.3) {
          resolve({
            success: true,
            successMessage: `成功导入 ${Math.floor(random * 50 + 10)} 名学生`,
            successCount: Math.floor(random * 50 + 10),
            failCount: 0
          })
        } else {
          resolve({
            success: false,
            successCount: Math.floor(random * 20),
            failCount: Math.floor(random * 10 + 5),
            errorMessages: [
              '第3行：学号不能为空',
              '第5行：班级名称不存在',
              '第8行：学号重复'
            ]
          })
        }
      }, 2000)
    })
  },
  onSuccess: (result: any) => {
    console.log('导入成功:', result)
    loadCourseData()
  },
  onError: (error: Error) => {
    console.error('导入失败:', error)
  },
  onComplete: () => {
    console.log('导入完成')
  }
}

// 新增班级
const handleAddClass = () => {
  addClassDialogVisible.value = true
}

// 班级卡片点击
const handleClassDetail = (classId: string | number) => {
  MessagePlugin.info(`查看班级详情：${classId}`)
}

// 管理学生
const handleManageStudents = (classId: string | number) => {
  // 找到对应的班级信息
  const classInfo = worklistData.value.find(item => item.classId === classId)
  if (classInfo) {
    currentClassId.value = classId
    currentClassName.value = classInfo.className
    studentManagementVisible.value = true
  }
}

// 导入学生
const handleImportStudents = (classId: string | number) => {
  importDialogVisible.value = true
}

// 处理导入对话框显示状态
const handleImportDialogVisible = (visible: boolean) => {
  importDialogVisible.value = visible
}

// 编辑班级
const handleEditClass = (classId: string | number) => {
  MessagePlugin.info(`编辑班级：${classId}`)
}

// 删除班级
const handleDeleteClass = (classId: string | number) => {
  MessagePlugin.info(`删除班级：${classId}`)
}

// 查看教师档案
const handleViewProfile = (teacherId: string | number) => {
  MessagePlugin.info(`查看教师档案：${teacherId}`)
}

// 联系团队成员
const handleContactMember = (teacherId: string | number) => {
  MessagePlugin.info(`联系团队成员：${teacherId}`)
}

// 团队管理
const handleManageTeam = () => {
  MessagePlugin.info('跳转到团队管理页面')
}

// 数据处理工具函数
const processCourseInfo = (data: any) => {
  // 根据taskYear和taskTerm计算学期信息
  const semesterDisplay = SemesterUtils.formatSemesterDisplayName(data.taskYear, data.taskTerm)
  
  // 计算学期状态
  const currentDate = new Date()
  const currentYear = currentDate.getFullYear()
  const currentMonth = currentDate.getMonth() + 1
  const currentSemester = SemesterUtils.getSemesterByMonth(currentMonth)
  
  let status = '已结课'
  if (data.taskYear > currentYear) {
    status = '准备中'
  } else if (data.taskYear === currentYear) {
    const taskSemester = data.taskTerm === 1 ? SemesterUtils.SEMESTER_TYPE.SPRING : SemesterUtils.SEMESTER_TYPE.AUTUMN
    if (taskSemester === currentSemester) {
      status = '进行中'
    } else if (taskSemester > currentSemester) {
      status = '准备中'
    }
  }
  
  return {
    id: data.courseId?.toString() || '',
    name: data.courseName || '',
    code: data.courseCode || '',
    credits: data.courseCredit || 0,
    nature: data.courseNature || '',
    semester: semesterDisplay,
    status: status
  }
}

const processWorklistData = (data: any[], courseName: string) => {
  return data.map((item: any, index: number) => ({
    id: item.classId?.toString() || index.toString(),
    courseId: courseId.value,
    courseName,
    entranceYear: item.entranceYear || '',
    taskNumber: index + 1,
    classId: item.classId?.toString() || '',
    className: item.className || '',
    teacherId: item.teacherId?.toString() || '',
    teacherName: item.teacherName || '',
    teacherTitle: item.teacherTitle || '',
    teacherAcademyName: item.teacherAcademyName || '',
    teacherRole: getTeacherRoleLabel(item.teacherRole || ''),
    taskYear: 0,
    taskTerm: 0,
    studentCount: item.studentCount || 0,
    teachWeek: item.teachWeek || 0,
    weekHours: item.weekHours || 0,
    totalHours: item.totalHours || 0,
    courseLeaderId: '',
    courseLeaderName: '',
    scheduleInfo: [] as Array<{time: string; location: string}>
  }))
}

const processTeamMembersData = (data: any[]) => {
  return data.map((item: any) => ({
    id: item.teacherId?.toString() || '',
    teacherId: item.teacherId?.toString() || '',
    name: item.teacherName || '',
    title: item.teacherTitle || '',
    academyName: item.academyName || '',
    avatar: '',
    role: getTeacherRoleLabel(item.roleCode || item.roleName || ''),
    roleCode: item.roleCode || '',
    email: item.email || '',
    classCount: item.classCount || 0,
    studentCount: item.studentCount || 0
  }))
}

const updateStatsData = (totalClasses: number, totalStudents: number, assessmentCount: number) => {
  statsData.value = [
    {
      key: 'classes',
      label: '授课班级',
      value: totalClasses.toString(),
      icon: 'layers'
    },
    {
      key: 'students',
      label: '学生总数',
      value: totalStudents.toString(),
      icon: 'user'
    },
    {
      key: 'assessments',
      label: '考核项目',
      value: assessmentCount.toString(),
      icon: 'assignment'
    },
    {
      key: 'average',
      label: '平均成绩',
      value: '--',
      icon: 'chart-line'
    }
  ]
}

// 加载课程数据
const loadCourseData = async () => {
  try {
    // 再次验证参数（防止在异步操作过程中参数被修改）
    if (!validateRequiredParams()) {
      return
    }

    // 加载课程基本信息
    const { data } = await getCourseTaskInfo(courseId.value, taskId.value)
    courseInfo.value = processCourseInfo(data)

    // 加载班级信息
    const classesRes = await getCourseTaskClasses(courseId.value, taskId.value)
    if (classesRes.code === 200 && classesRes.data) {
      worklistData.value = processWorklistData(classesRes.data, courseInfo.value.name)

      // 计算统计数据
      const totalClasses = worklistData.value.length
      const totalStudents = worklistData.value.reduce((sum, item) => sum + (item.studentCount || 0), 0)

      // 加载课程统计信息
      const statisticsRes = await getCourseTaskStatistics(courseId.value, taskId.value)
      if (statisticsRes.code === 200 && statisticsRes.data) {
        const assessmentCount = statisticsRes.data.assessmentCount || 0
        updateStatsData(totalClasses, totalStudents, assessmentCount)
      }
    }

    // 加载教师团队信息
    const teachersRes = await getCourseTaskTeachers(courseId.value, taskId.value)
    if (teachersRes.code === 200 && teachersRes.data) {
      teamMembersData.value = processTeamMembersData(teachersRes.data)
    }

    MessagePlugin.success('课程数据加载成功')
  } catch (error) {
    console.error('加载课程数据失败:', error)
    MessagePlugin.error('加载课程数据失败')

    // 如果是参数相关错误，跳转到工作台
    if (error instanceof Error && error.message.includes('参数')) {
      router.push({ name: 'DashboardTeacher' })
    }
  }
}

// 字典初始化工具函数
const waitForDictInitialization = async (maxWaitTime = 10000) => {
  // 如果字典还没有初始化，先初始化
  if (!dictGlobalState.value.initialized && !dictGlobalState.value.initializing) {
    try {
      await initializeGlobalDict()
    } catch (error) {
      console.error('字典初始化失败:', error)
    }
  }

  // 等待字典数据加载完成
  const startTime = Date.now()
  while ((courseNatureLoading.value || teacherRoleLoading.value) && (Date.now() - startTime) < maxWaitTime) {
    await new Promise(resolve => setTimeout(resolve, 100))
  }
}

const logDictDebugInfo = () => {
  console.log('=== 字典数据调试信息 ===')
  console.log('课程性质字典选项:', courseNatureOptions.value)
  console.log('教师角色字典选项:', teacherRoleOptions.value)
  console.log('课程性质加载状态:', courseNatureLoading.value)
  console.log('教师角色加载状态:', teacherRoleLoading.value)
  console.log('课程性质错误:', courseNatureError.value)
  console.log('教师角色错误:', teacherRoleError.value)
  console.log('全局字典状态:', dictGlobalState.value)
  console.log('========================')
}

// 页面初始化
onMounted(async () => {
  console.log('课程ID:', courseId.value)
  console.log('任务ID:', taskId.value)

  // 验证必要参数
  if (!validateRequiredParams()) {
    return
  }

  // 等待字典数据初始化完成
  await waitForDictInitialization()

  // 记录调试信息
  logDictDebugInfo()

  // 加载课程数据
  loadCourseData()
})
</script>

<style lang="less" scoped>
.teacher-course-overview {
  padding: 24px;
  background: var(--td-bg-color-page);
  min-height: 100vh;

  .back-section {
    margin-bottom: 16px;
  }

  .course-header {
    background: var(--td-bg-color-container);
    border-radius: 6px;
    padding: 24px;
    margin-bottom: 24px;
    box-shadow: var(--td-shadow-1);

    .course-basic-info {
      margin-bottom: 24px;

      .course-title {
        display: flex;
        align-items: center;
        gap: 12px;
        margin-bottom: 16px;

        h1 {
          margin: 0;
          font-size: 28px;
          font-weight: 600;
          color: var(--td-text-color-primary);
        }
      }

      .course-meta {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 16px;

        .meta-item {
          display: flex;
          align-items: center;

          .label {
            color: var(--td-text-color-secondary);
            margin-right: 8px;
          }

          .value {
            color: var(--td-text-color-primary);
            font-weight: 500;
          }
        }
      }
    }

    .course-tabs {
      :deep(.t-tabs__nav-container) {
        border-bottom: 1px solid var(--td-border-level-1-color);
      }
    }
  }

  .page-content {
    .stats-section,
    .classes-section,
    .team-section {
      background: var(--td-bg-color-container);
      border-radius: 6px;
      padding: 24px;
      margin-bottom: 24px;
      box-shadow: var(--td-shadow-1);

      .section-title {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;

        h2 {
          margin: 0;
          font-size: 18px;
          font-weight: 600;
          color: var(--td-text-color-primary);
        }
      }
    }

    .stats-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 16px;

      .stat-card {
        display: flex;
        align-items: center;
        padding: 20px;
        background: var(--td-bg-color-page);
        border-radius: 6px;
        border: 1px solid var(--td-border-level-1-color);
        transition: all 0.3s ease;

        &:hover {
          box-shadow: var(--td-shadow-2);
          transform: translateY(-2px);
        }

        .stat-icon {
          width: 48px;
          height: 48px;
          background: var(--td-brand-color);
          border-radius: 6px;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 16px;

          :deep(.t-icon) {
            color: white;
          }
        }

        .stat-content {
          .stat-number {
            font-size: 24px;
            font-weight: 600;
            color: var(--td-text-color-primary);
            line-height: 1;
            margin-bottom: 4px;
          }

          .stat-label {
            font-size: 14px;
            color: var(--td-text-color-secondary);
          }
        }
      }
    }

    .classes-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
      gap: 20px;
    }

    .team-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
      gap: 20px;
    }

    .classes-table {
      :deep(.t-table) {
        border-radius: 6px;
      }
    }

    .team-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
      gap: 16px;

      .team-member {
        display: flex;
        align-items: center;
        padding: 16px;
        background: var(--td-bg-color-page);
        border-radius: 6px;
        border: 1px solid var(--td-border-level-1-color);
        transition: all 0.3s ease;

        &:hover {
          box-shadow: var(--td-shadow-2);
          transform: translateY(-2px);
        }

        .member-avatar {
          margin-right: 16px;
        }

        .member-info {
          .member-name {
            font-size: 16px;
            font-weight: 500;
            color: var(--td-text-color-primary);
            margin-bottom: 4px;
          }

          .member-role {
            font-size: 14px;
            color: var(--td-brand-color);
            margin-bottom: 4px;
          }

          .member-contact {
            font-size: 12px;
            color: var(--td-text-color-secondary);
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .teacher-course-overview {
    padding: 16px;

    .course-header {
      padding: 16px;

      .course-basic-info .course-meta {
        grid-template-columns: 1fr;
      }
    }

    .page-content {
      .stats-section,
      .classes-section,
      .team-section {
        padding: 16px;
      }

      .stats-grid {
        grid-template-columns: 1fr;
      }

      .team-grid {
        grid-template-columns: 1fr;
      }
    }
  }
}
</style>

<template>
  <div class="course-info-container">
    <!-- 头部信息 -->
    <!-- <div class="page-header">
      <div class="header-content">
        <div class="title-info">
          <h1 class="page-title">课程管理</h1>
          <p class="page-subtitle">管理课程的基本信息与教学资源</p>
        </div>
      </div>
    </div> -->
    
    <!-- 课程目标展示（全局展示） -->
    <div class="course-objectives-wrapper">
      <h3 class="section-title">
        <t-icon name="star" class="title-icon" />
        课程目标
        <t-tooltip content="课程目标数据加载状态">
          <t-tag theme="success" size="small" style="margin-left: 8px">
            {{ courseObjectives.length ? `已加载 ${courseObjectives.length} 个课程目标` : '暂无数据' }}
          </t-tag>
        </t-tooltip>
      </h3>
      <div v-if="!courseObjectives || courseObjectives.length === 0" class="empty-objectives">
        <t-empty description="暂无课程目标">
          <template #image>
            <t-icon name="info-circle" size="48px" />
          </template>
          <template #extra>
            <t-button theme="primary" size="small" @click="fetchCourseInfo">
              刷新数据
            </t-button>
          </template>
        </t-empty>
      </div>
      <CourseObjectives v-else :objectives="courseObjectives" @update="handleObjectivesUpdate" />
    </div>

    <!-- 主要内容区域 -->
    <div class="page-content">
      <t-tabs
        theme="card"
        v-model="activeTab"
        @change="handleTabChange"
      >
        <t-tab-panel value="basic" label="基本信息">
          <div v-if="activeTab === 'basic'" class="tab-content">
            <!-- 课程基本信息表单 -->
            <CourseBaseInfo
              :course-id="courseId"
              :initial-data="courseFormData"
              :textbook-data="textbookForComponent"
              @save="handleCourseInfoSave"
              @reset="handleCourseInfoReset"
            />
          </div>
        </t-tab-panel>

        <t-tab-panel value="syllabus" label="教学内容">
          <div v-if="activeTab === 'syllabus'" class="tab-content">
            <CourseTeachingOutline
              :course-id="courseId"
              :initial-data="syllabusFormData"
              :course-objectives="courseObjectives"
              @save="handleSyllabusSave"
              @reset="handleSyllabusReset"
            />
          </div>
        </t-tab-panel>
       

        <t-tab-panel value="resources" label="教学资源">
          <div v-if="activeTab === 'resources'" class="tab-content">
            <CourseResources 
              :course-id="courseId"
              :initial-resources="resourcesList"
              @upload="handleUploadResource"
              @preview="previewResource"
              @download="downloadResource"
              @delete="deleteResource"
            />
          </div>
        </t-tab-panel>
      </t-tabs>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, computed } from 'vue';
import { useRoute } from 'vue-router';
import { MessagePlugin } from 'tdesign-vue-next';
import CourseBaseInfo from './components/CourseBaseInfo.vue';
import CourseTeachingOutline from './components/CourseTeachingOutline.vue';
import CourseObjectives from './components/CourseObjectives.vue';
import CourseResources from './components/CourseResources.vue';
import { getCourseBaseInfo, getCourseTargetList, type CourseDetailInfo, type CourseObjectiveVO, type BookDetail } from '@/api/training/course';

const route = useRoute();
const courseId = ref(route.params.courseId as string);
// 课程信息相关状态
const course = ref({
  courseId: route.params.courseId ? Number(route.params.courseId) : null,
  courseName: route.params.courseName || '',
  courseCode: route.params.courseCode || '',
});
const loading = ref(false);
const activeTab = ref('basic');
const courseObjectives = ref<CourseObjectiveVO[]>([]);
const courseData = ref<CourseDetailInfo | null>(null);

// 资源管理相关状态
const resourcesLoading = ref(false);
const resourcesPagination = ref({
  current: 1,
  pageSize: 10,
  total: 0
});

// 表单数据
const courseFormData = ref({
  // 基本信息
  courseName: course.value.courseName,
  courseCode: course.value.courseCode,
  courseEnglishName: '',
  courseNature: '',
  courseType: '',
  credit: 0,
  hours: 0,
  hoursTheory: 0,
  hoursExperiment: 0,
  hoursOther: 0,
  hoursExtracurricular: 0,
  semester: '',
  
  // 课程分类
  courseType1: '', // 本专业课程类型
  courseType2: '', // 专业认证课程类型
  courseType3: '', // 国标课程类别
  
  // 其他信息
  courseCore: false,
  courseExam: false,
  prerequisite: '',
  department: '',
  teachingUnit: '',
  applicableMajor: '',
  teachingMethod: '',
  teachingPlatform: '',
  introducedCourse: '',
  courseDescription: '',

  // 教材信息
  courseBookVO: {
    mainBook: null as BookDetail | null,
    referenceBooks: [] as BookDetail[]
  }

});

// 教材数据
const textbookData = ref<{
  mainTextbook: BookDetail | null;
  referenceBooks: BookDetail[];
}>({
  mainTextbook: null,
  referenceBooks: []
});

// 教学大纲数据
const syllabusFormData = ref({
  teachingContents: []
});

// 获取课程基本信息
const fetchCourseInfo = async () => {
  if (!course.value.courseId) {
    MessagePlugin.warning('课程ID不能为空');
    return;
  }

  try {
    loading.value = true;
    const response = await getCourseBaseInfo(course.value.courseId);
    console.log('获取课程信息:', response);
    // 保存原始数据
    courseData.value = response.data;
    
    // 转换数据格式以适应表单显示
    transformCourseData(courseData.value);
    
    // 准备教材信息
    prepareTextbookData(courseData.value);
    
    // 如果API返回中没有课程目标数据，尝试单独获取
    if (!courseObjectives.value.length) {
      try {
        const courseId = Number(course.value.courseId);
        const objectivesResponse = await getCourseTargetList(courseId);
        if (objectivesResponse && Array.isArray(objectivesResponse)) {
          courseObjectives.value = objectivesResponse;
          console.log('单独获取课程目标成功:', courseObjectives.value);
        }
      } catch (objectiveError) {
        console.error('获取课程目标失败:', objectiveError);
      }
    }
    
    MessagePlugin.success('课程信息加载成功');
    console.log('课程信息加载后的数据:', {
      courseFormData: courseFormData.value,
      courseObjectives: courseObjectives.value,
      textbookData: textbookData.value
    });
  } catch (error) {
    console.error('获取课程信息失败:', error);
    MessagePlugin.error('获取课程信息失败，请重试');
  } finally {
    loading.value = false;
  }
};



// 转换课程数据格式
const transformCourseData = (data: CourseDetailInfo) => {
  // 转换课程基本信息
  courseFormData.value = {
    // 基本信息
    courseName: data.courseName || '',
    courseCode: data.courseCode || '',
    courseEnglishName: data.courseCode || '', // 英文名称可能需要后端补充
    courseNature: data.courseNature || '',
    courseType: data.courseType1 || '',
    credit: data.courseCredit || 0,
    hours: data.courseHoursTotal || 0,
    hoursTheory: data.courseHoursTheory || 0,
    hoursExperiment: data.courseHoursExperiment || 0,
    hoursOther: data.courseHoursOther || 0,
    hoursExtracurricular: data.courseHoursExtracurricular || 0,
    semester: data.courseSemester || '',
    
    // 课程分类
    courseType1: data.courseType1 || '',
    courseType2: data.courseType2 || '',
    courseType3: data.courseType3 || '',
    
    // 其他信息
    courseCore: data.courseCore || false,
    courseExam: data.courseExam || false,
    prerequisite: '', // 先修课程可能需要后端补充
    department: '', // 开课单位可能需要后端补充
    teachingUnit: '', // 教学单位可能需要后端补充
    applicableMajor: '', // 适用专业可能需要后端补充
    teachingMethod: '', // 授课形式可能需要后端补充
    teachingPlatform: '', // 教学平台可能需要后端补充
    introducedCourse: '', // 引入课程可能需要后端补充
    courseDescription: '', // 课程描述可能需要后端补充

    //教材信息
    courseBookVO: {
      mainBook: data.courseBookVO?.mainBook || null as BookDetail | null,
      referenceBooks: data.courseBookVO?.referenceBooks ||  [] as BookDetail[]
    }
 
  };

  // 设置课程目标数据
  if (data.courseObjectives && Array.isArray(data.courseObjectives)) {
    courseObjectives.value = [...data.courseObjectives];
    console.log('课程目标数据已设置:', courseObjectives.value);
  } else {
    console.warn('未找到有效的课程目标数据');
    // 设置默认课程目标示例数据
    courseObjectives.value = [
      {
        objectiveId: 1,
        number: 1,
        objectiveName: '目标一',
        expectedScore: 85,
        description: '培养学生的数学建模能力和解决实际问题的能力',
        po: { 
          id: 1, 
          poNumber: 1,
          poTitle: '工程知识',
          planId: 1,
          requirementId: 1,
          isRequirement: false,
          parentId: 0,
          standardId: 1,
          standardNumber: 1,
          status: 1,
          poDescription: '工程知识'
        }
      },
      {
        objectiveId: 2,
        number: 2,
        objectiveName: '目标二',
        expectedScore: 80,
        description: '使学生掌握微积分的基本概念、理论和方法，能够运用数学知识解决工程问题',
        po: {
          id: 2,
          poNumber: 2,
          poTitle: '问题分析',
          planId: 1,
          requirementId: 1,
          isRequirement: false,
          parentId: 0,
          standardId: 1,
          standardNumber: 1,
          status: 1,
          poDescription: '问题分析'
        }
      }
    ];
  }

  // 转换教材信息
  try {
    if (data.courseBookVO) {
      // 如果后端提供了教材信息，直接使用
      const { mainBook, referenceBooks } = data.courseBookVO;
      textbookData.value = {
        mainTextbook: mainBook || null,
        referenceBooks: referenceBooks || []
      };
    }
  } catch (error) {
    console.error('解析教材信息失败:', error);
    // 保持默认值
  }
};

// 准备教材信息
const prepareTextbookData = (data: CourseDetailInfo) => {
  try {
    console.log('准备教材信息:', data);
    // 从课程数据中解析教材信息
    if (data.courseBookVO) {
      // 如果后端已经提供了教材信息
      const { mainBook, referenceBooks } = data.courseBookVO;
      
      textbookData.value = {
        mainTextbook: mainBook || null,
        referenceBooks: referenceBooks || []
      };
    } else {
      // 示例：设置默认教材信息，实际项目中应从API获取
      const mainTextbook: BookDetail = {
        name: '高等数学(第7版)',
        author: '同济大学数学系',
        publisher: '高等教育出版社',
        publishDate: '2024-01-01',
        type: 'MAIN',
        isbn: '978-7-04-039877-7',
        version: '第7版',
        price: 89.00,
        applicableScope: '理工科专业'
      };
      
      const referenceBooks: BookDetail[] = [
        {
          name: '数学分析',
          author: '华东师范大学数学系',
          publisher: '高等教育出版社',
          publishDate: '2023-05-15',
          type: 'REFERENCE',
          isbn: '978-7-04-036221-1',
          version: '第4版',
          price: 76.50
        },
        {
          name: '高等数学习题集',
          author: '张三',
          publisher: '北京大学出版社',
          publishDate: '2022-09-01',
          type: 'REFERENCE',
          isbn: '978-7-30-145892-3',
          price: 45.00
        }
      ];
      
      textbookData.value = {
        mainTextbook: mainTextbook,
        referenceBooks: referenceBooks
      };
    }
  } catch (error) {
    console.error('准备教材信息失败:', error);
    textbookData.value = {
      mainTextbook: null,
      referenceBooks: []
    };
  }
};

// 类型转换函数：将 BookDetail 转换为 Textbook 格式
const convertBookDetailToTextbook = (book: BookDetail | null) => {
  if (!book) return null;
  return {
    title: book.name,
    author: book.author,
    publisher: book.publisher,
    publishDate: book.publishDate,
    isbn: book.isbn,
    version: book.version,
    price: book.price,
    description: book.description
  };
};

// 转换教材数据供组件使用
const textbookForComponent = computed(() => ({
  mainTextbook: convertBookDetailToTextbook(textbookData.value.mainTextbook),
  referenceBooks: textbookData.value.referenceBooks.map(convertBookDetailToTextbook)
}));

// 教材信息事件处理
const handleTextbookSave = async (data: { mainTextbook: BookDetail | null; referenceBooks: BookDetail[] }) => {
  console.log('保存教材信息:', data);
  
  try {
    // 更新本地教材数据
    textbookData.value = { ...data };
    
    // 如果需要更新到课程数据中
    if (courseData.value) {
      courseData.value.courseBookVO = {
        mainBook: data.mainTextbook,
        referenceBooks: data.referenceBooks
      };
    }
    
    // 保存完整的课程信息到后端
    await saveCourseToBackend(courseFormData.value, courseObjectives.value, textbookData.value);
    
  } catch (error) {
    console.error('保存教材信息失败:', error);
  }
};

const handleTextbookReset = () => {
  console.log('重置教材信息');
  if (courseData.value) {
    prepareTextbookData(courseData.value);
  }
};

// 课程基本信息事件处理
const handleCourseInfoSave = async (data: Record<string, any>) => {
  console.log('保存课程基本信息:', data);
  
  try {
    // 更新本地表单数据
    Object.assign(courseFormData.value, data);
    
    // 保存完整的课程信息到后端
    await saveCourseToBackend(courseFormData.value, courseObjectives.value, textbookData.value);
    
  } catch (error) {
    console.error('保存课程基本信息失败:', error);
  }
};

const handleCourseInfoReset = () => {
  console.log('重置课程基本信息');
  if (courseData.value) {
    transformCourseData(courseData.value);
  }
};

// 教学大纲事件处理
const handleSyllabusSave = async (data: Record<string, any>) => {
  console.log('保存教学大纲:', data);
  
  try {
    // 更新本地教学大纲数据
    Object.assign(syllabusFormData.value, data);
    
    // 如果包含课程目标更新，需要同步
    if (data.courseObjectives) {
      courseObjectives.value = data.courseObjectives;
    }
    
    // 保存完整的课程信息到后端
    await saveCourseToBackend(courseFormData.value, courseObjectives.value, textbookData.value);
    
  } catch (error) {
    console.error('保存教学大纲失败:', error);
  }
};

const handleSyllabusReset = () => {
  console.log('重置教学大纲');
  // 重置为初始数据
};

// 课程目标更新处理
const handleObjectivesUpdate = async (objectives: CourseObjectiveVO[]) => {
  console.log('更新课程目标:', objectives);
  
  try {
    courseObjectives.value = objectives;
    
    // 保存完整的课程信息到后端
    await saveCourseToBackend(courseFormData.value, courseObjectives.value, textbookData.value);
    
  } catch (error) {
    console.error('更新课程目标失败:', error);
  }
};

// Tab切换处理
const handleTabChange = (value: string | number) => {
  console.log('Tab切换到:', value);
  activeTab.value = value as string;
};

// 资源列表
const resourceColumns = [
  { colKey: 'name', title: '资源名称', width: 200 },
  { colKey: 'type', title: '资源类型', width: 100 },
  { colKey: 'size', title: '文件大小', width: 100 },
  { colKey: 'uploadTime', title: '上传时间', width: 150 },
  { colKey: 'operation', title: '操作', width: 150 }
];

const resourcesList = ref([
  { id: 1, name: '高等数学教学大纲.pdf', type: '教学大纲', size: '1.2MB', uploadTime: '2025-05-10' },
  { id: 2, name: '第一章课件.pptx', type: '课件', size: '5.8MB', uploadTime: '2025-05-15' },
  { id: 3, name: '习题集.pdf', type: '习题', size: '2.5MB', uploadTime: '2025-05-20' },
  { id: 4, name: '期末复习资料.docx', type: '复习资料', size: '3.1MB', uploadTime: '2025-06-10' }
]);



// 获取资源类型标签主题
const getResourceTypeTheme = (type: string): 'default' | 'success' | 'warning' | 'primary' | 'danger' => {
  const themeMap: Record<string, 'default' | 'success' | 'warning' | 'primary' | 'danger'> = {
    '教学大纲': 'primary',
    '课件': 'success',
    '习题': 'warning',
    '复习资料': 'default'
  };
  return themeMap[type] || 'default';
};

// 资源操作方法
const handleUploadResource = () => {
  console.log('上传资源');
  MessagePlugin.info('上传功能开发中...');
  // 这里可以打开文件选择对话框或上传组件
  // 实际实现中可以使用 t-upload 组件
};

const previewResource = (resource: any) => {
  console.log('预览资源', resource);
  MessagePlugin.info(`预览资源: ${resource.name}`);
  // 这里可以打开预览对话框
};

const downloadResource = (resource: any) => {
  console.log('下载资源', resource);
  MessagePlugin.success(`开始下载: ${resource.name}`);
  // 这里可以实现文件下载逻辑
};

const deleteResource = (resource: any) => {
  console.log('删除资源', resource);
  MessagePlugin.warning(`确认删除资源: ${resource.name}?`);
  // 这里可以实现删除确认和删除逻辑
};

// 数据验证函数
const validateCourseData = (courseInfo: any, objectives: CourseObjectiveVO[], textbooks: any) => {
  const errors: string[] = [];
  
  // 验证基本信息
  if (!courseInfo.courseName?.trim()) {
    errors.push('课程名称不能为空');
  }
  
  if (!courseInfo.courseCode?.trim()) {
    errors.push('课程代码不能为空');
  }
  
  if (courseInfo.credit <= 0) {
    errors.push('学分必须大于0');
  }
  
  if (courseInfo.hours <= 0) {
    errors.push('总学时必须大于0');
  }
  
  // 验证学时分配
  const totalSubHours = (courseInfo.hoursTheory || 0) + 
                       (courseInfo.hoursExperiment || 0) + 
                       (courseInfo.hoursOther || 0);
  
  if (totalSubHours > courseInfo.hours) {
    errors.push('理论、实验、其他学时之和不能超过总学时');
  }
  
  // 验证课程目标
  if (objectives.length === 0) {
    errors.push('至少需要设置一个课程目标');
  }
  
  // 验证教材信息
  if (!textbooks.mainTextbook) {
    errors.push('必须指定主教材');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
};

// 保存课程完整信息到后端
const saveCourseToBackend = async (courseInfo: any, objectives: CourseObjectiveVO[], textbooks: any) => {
  try {
    // 数据验证
    const validation = validateCourseData(courseInfo, objectives, textbooks);
    if (!validation.isValid) {
      MessagePlugin.error(`数据验证失败：${validation.errors.join(', ')}`);
      return;
    }
    
    // 构建完整的课程数据
    const coursePayload: Partial<CourseDetailInfo> = {
      courseId: course.value.courseId || 0,
      courseName: courseInfo.courseName,
      courseCode: courseInfo.courseCode,
      courseCredit: courseInfo.credit,
      courseCore: courseInfo.courseCore,
      courseExam: courseInfo.courseExam,
      courseHoursTotal: courseInfo.hours,
      courseHoursTheory: courseInfo.hoursTheory,
      courseHoursExperiment: courseInfo.hoursExperiment,
      courseHoursOther: courseInfo.hoursOther,
      courseHoursExtracurricular: courseInfo.hoursExtracurricular,
      courseSemester: courseInfo.semester,
      courseType1: courseInfo.courseType1,
      courseType2: courseInfo.courseType2,
      courseType3: courseInfo.courseType3,
      courseNature: courseInfo.courseNature,
      
      // 将课程目标转换为JSON字符串
      courseObjectives: courseInfo.courseObjectives,
      
      // 教材信息
      courseBookVO: {
        mainBook: textbooks.mainTextbook,
        referenceBooks: textbooks.referenceBooks
      }
    };
    
    console.log('准备保存的课程数据:', coursePayload);
    
    // 这里应该调用保存API
    // const result = await updateCourseInfo(coursePayload);
    
    MessagePlugin.success('课程信息保存成功');
    return coursePayload;
  } catch (error) {
    console.error('保存课程信息失败:', error);
    MessagePlugin.error('保存课程信息失败');
    throw error;
  }
};

// 监听路由参数变化
watch(() => route.params.id, (newId) => {
  if (newId) {
    courseId.value = newId as string;
    fetchCourseInfo();
  }
});

onMounted(() => {
  console.log('课程基本信息页面加载，课程ID:', courseId.value);
  fetchCourseInfo();

  // 初始化资源分页
  resourcesPagination.value.total = resourcesList.value.length;
});

// 监控课程目标数据的变化
watch(() => courseObjectives.value, (newVal) => {
  console.log('CourseInfo组件中的课程目标数据发生变化:', newVal);
}, { deep: true });
</script>

<style lang="less" scoped>
.course-info-container {
  padding: 20px;
  
  .page-header {
    margin-bottom: 24px;
    background: #fff;
    border-radius: 8px;
    padding: 24px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  }
  
  .course-objectives-wrapper {
    background: #fff;
    border-radius: 8px;
    padding: 12px 24px;
    margin-bottom: 16px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    
    .section-title {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 18px;
      font-weight: 600;
      margin-bottom: 8px;
      
      .title-icon {
        color: var(--td-brand-color);
      }
    }
    
    .empty-objectives {
      padding: 20px 0;
      text-align: center;
    }
    
    .header-content {
      .title-info {
        .page-title {
          font-size: 24px;
          font-weight: 600;
          margin: 0 0 8px 0;
        }
        
        .page-subtitle {
          font-size: 14px;
          color: var(--td-text-color-secondary);
          margin: 0;
        }
      }
    }
  }
  
  .page-content {
    :deep(.t-tabs__nav) {
      margin-bottom: 16px;
    }

    .tab-content {
      min-height: 400px;
      
      :deep(.course-base-info),
      :deep(.course-teaching-outline) {
        background: #fff;
        border-radius: 8px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
        padding: 0;
      }
    }

    .card-header {
      display: flex;
      align-items: center;
      gap: 8px;

      .title-icon {
        color: var(--td-brand-color);
      }
    }

    .upload-section {
      padding: 24px 0;
    }

    // 教学资源样式
    .resources-section {
      background: #fff;
      border-radius: 6px;

      .resources-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        padding: 24px 24px 16px;
        border-bottom: 1px solid var(--td-border-level-1-color);

        .header-info {
          .section-title {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 18px;
            font-weight: 600;
            margin: 0 0 8px 0;
            color: var(--td-text-color-primary);

            .title-icon {
              color: var(--td-brand-color);
              font-size: 20px;
            }
          }

          .section-desc {
            margin: 0;
            color: var(--td-text-color-secondary);
            font-size: 14px;
          }
        }

        .header-actions {
          flex-shrink: 0;
        }
      }

      .resources-content {
        padding: 24px;

        .file-size {
          color: var(--td-text-color-secondary);
          font-size: 12px;
        }

        .upload-time {
          color: var(--td-text-color-placeholder);
          font-size: 12px;
        }

        .empty-resources {
          padding: 60px 20px;
          text-align: center;
        }
      }
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    .page-header {
      padding: 16px;
    }
    
    .page-content {
      .resources-section {
        .resources-header {
          flex-direction: column;
          align-items: flex-start;
          gap: 16px;
        }
      }
    }
  }
}
</style>

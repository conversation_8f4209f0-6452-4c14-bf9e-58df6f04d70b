<template>
  <div class="teacher-course-info">
    <CourseBaseInfo
      :course-id="courseId"
      :initial-data="courseFormData"
      @save="handleCourseInfoSave"
      @reset="handleCourseInfoReset"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRoute } from 'vue-router'
import { MessagePlugin } from 'tdesign-vue-next'
import CourseBaseInfo from '@/pages/training/course/components/CourseBaseInfo.vue'

const route = useRoute()
const courseId = computed(() => route.params.courseId as string)

// 表单数据
const courseFormData = ref({
  courseName: 'Java程序设计',
  courseCode: '*********',
  courseEnglishName: 'Java Programming',
  courseNature: '必修',
  courseType: '专业基础课',
  credit: 4,
  hours: 64,
  semester: '3',
  prerequisite: '《程序设计基础》',
  department: '数学学院',
  teachingUnit: '数据科学与大数据技术教研室',
  applicableMajor: '软件工程专业',
  teachingMethod: '线上线下混合式',
  teachingPlatform: '超星学习中国大学MOOC',
  introducedCourse: 'Java程序设计+中国工学院',
  courseDescription: 'Java程序设计是软件工程专业的核心课程，主要介绍Java语言的基本语法、面向对象编程思想、常用类库的使用方法，通过本课程的学习，学生能够掌握Java语言的基本技能，具备开发简单Java应用程序的能力，为后续的软件开发课程奠定基础。',
  mainTextbook: {
    title: 'Java程序设计',
    author: '马世霞编著',
    publisher: '机械工业出版社',
    publishDate: '2022年6月'
  },
  referenceBooks: [
    {
      title: 'Java核心技术',
      author: 'Cay S. Horstmann',
      publisher: '机械工业出版社',
      publishDate: '2021年8月'
    },
    {
      title: 'Java编程思想',
      author: 'Bruce Eckel',
      publisher: '机械工业出版社',
      publishDate: '2020年12月'
    }
  ]
})

// 课程基本信息事件处理
const handleCourseInfoSave = (data: Record<string, any>) => {
  console.log('保存课程基本信息:', data)
  // 这里可以调用API保存数据
  Object.assign(courseFormData.value, data)
  MessagePlugin.success('课程信息保存成功')
}

const handleCourseInfoReset = () => {
  console.log('重置课程基本信息')
  // 重置为初始数据
  MessagePlugin.info('已重置课程信息')
}
</script>

<style lang="less" scoped>
.teacher-course-info {
  min-height: 100vh;
  background: var(--td-bg-color-page);
}
</style>
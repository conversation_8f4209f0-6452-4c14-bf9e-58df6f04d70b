<template>
  <div class="course-target-container">
    <!-- 头部信息 -->
    <div class="dashboard-header">
      <div class="header-content">
        <div class="title-info">
            <div class="title-container">
            <h1 class="dashboard-title">{{ courseDetailData?.courseName || '课程ID: ' + courseId }}</h1>
            <t-tag theme="primary" size="small" class="role-tag">
              课程目标管理
            </t-tag>
            </div>
          <!-- <p class="dashboard-subtitle">配置课程目标与毕业要求指标点的支撑关系</p> -->
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="dashboard-content">
      <!-- 加载状态 -->
      <div v-if="dataLoading" class="loading-container">
        <t-loading size="large" text="正在加载课程数据..." />
      </div>

      <!-- 错误状态 -->
      <div v-else-if="errorMessage" class="error-container">
        <div class="error-content">
          <t-icon name="error-circle" size="48px" class="error-icon" />
          <h3 class="error-title">数据加载失败</h3>
          <p class="error-subtitle">{{ errorMessage }}</p>
          <t-button theme="primary" @click="fetchCourseData">
            重新加载
          </t-button>
        </div>
      </div>

      <!-- 正常内容 -->
      <template v-else>
      <!-- 毕业要求指标点展示区域 -->
      <div class="graduation-indicators-section">
        <div class="section-header">
          <div class="header-left">
            <h2 class="section-title">
              <t-icon name="info-circle" class="title-icon" />
              毕业要求指标点
            </h2>
          </div>
          <div class="header-right">
            <div class="header-buttons">
              <!-- 设置毕业要求指标点按钮 -->
              <t-button
              v-if="showIndicatorSelectionButton"
                theme="primary"
                variant="base"
                size="medium"
                @click="showIndicatorSelectionDialog"
                :loading="matrixLoading"
                class="setup-indicators-btn"
              >
                <template #icon>
                  <t-icon name="setting" size="16px" />
                </template>
                设置毕业要求指标点
              </t-button>

            </div>
          </div>
        </div>
        
        <!-- 指标点内容区域 -->
        <div v-if="graduationIndicators.length > 0" class="indicators-grid">
          <div
            v-for="(indicator, index) in sortedGraduationIndicators"
            :key="indicator.id"
            class="indicator-card"
            :style="{ '--delay': `${index * 0.1}s` }"
          >
            <div class="indicator-card-inner">
              <!-- 指标点头部 -->
                <div class="indicator-header">
                <div class="indicator-left">
                  <div class="indicator-number">毕业要求-{{ indicator.standardNumber }}.{{  indicator.poNumber }}</div>
                  <div class="indicator-info">
                    <!-- <h3 class="indicator-title">{{ indicator.poTitle }}</h3> -->
                    
                  </div>
                </div>
                <!-- 课程目标关联状态标签（右上角） -->
                <div class="indicator-right">
                  <t-tag
                    v-if="getObjectiveCountByIndicator(indicator.poId) > 0"
                    theme="success"
                    variant="light"
                    size="small"
                    class="objective-count-tag"
                  >
                    已关联 {{ getObjectiveCountByIndicator(indicator.poId) }} 个课程目标
                  </t-tag>
                  <t-tag
                    v-else
                    theme="warning"
                    variant="light"
                    size="small"
                    class="objective-count-tag"
                  >
                    暂无关联的课程目标
                  </t-tag>
                </div>
              </div>

              <!-- 指标点描述 -->
              <div class="indicator-description">
                {{ indicator.poDescription }}
              </div>

            </div>
          </div>
        </div>

        <!-- 毕业要求指标点空状态 -->
        <div v-else class="indicators-empty-state">
          <div class="empty-state-content">
            <div class="empty-icon">
              <t-icon name="target" size="64px" />
            </div>
            <h3 class="empty-title">暂未设置毕业要求指标点</h3>
            <p class="empty-description">
              请先选择本课程需要支撑的毕业要求指标点，然后为每个指标点配置相应的课程目标
            </p>
          </div>
        </div>
      </div>

      <!-- 课程目标展示区域 -->
      <div class="course-objectives-section">
        <div class="section-header">
          <div class="header-left">
            <h2 class="section-title">
              <t-icon name="bulletpoint" class="title-icon" />
              课程目标
            </h2>
          </div>
 
        </div>

        <!-- 当没有毕业要求指标点时显示引导性空状态 -->
        <div v-if="graduationIndicators.length === 0" class="objectives-empty-guide">
          <div class="empty-guide-content">
            <div class="empty-guide-icon">
              <t-icon name="info-circle" size="64px" />
            </div>
            <h3 class="empty-guide-title">请先设置毕业要求指标点</h3>
            <p class="empty-guide-description">
              课程目标需要与毕业要求指标点建立支撑关系。<br>
              请先在上方设置本课程需要支撑的毕业要求指标点，然后为每个指标点配置相应的课程目标。
            </p>
            <div class="empty-guide-steps">
              <div class="guide-step">
                <div class="step-number">1</div>
                <div class="step-text">点击上方"设置毕业要求指标点"按钮</div>
              </div>
              <div class="guide-step">
                <div class="step-number">2</div>
                <div class="step-text">选择本课程需要支撑的指标点</div>
              </div>
              <div class="guide-step">
                <div class="step-number">3</div>
                <div class="step-text">为每个指标点添加课程目标</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 当已设置指标点后，按指标点分组显示课程目标 -->
        <div v-else class="objectives-by-indicator">
          <div
            v-for="indicator in sortedGraduationIndicators"
            :key="`objectives-${indicator.id}`"
            class="indicator-objectives-group"
          >
            <div class="group-header">
              <div class="group-indicator">
                <span class="group-number">毕业要求{{ indicator.standardNumber }}-{{  indicator.poNumber }}</span>
                <span class="group-title">{{ indicator.poTitle }}</span>
                <div style="flex: 1"></div> <!-- 添加空div作为弹性空间 -->
                <t-button
                  theme="primary"
                  variant="base"
                  size="small"
                  @click="showAddObjectiveDialog(indicator)"
                  class="add-objective-group-btn"
                >
                  <template #icon>
                    <t-icon name="add" size="14px" />
                  </template>
                  添加课程目标
                </t-button>
              </div>
            </div>
            
            <!-- 该指标点下的课程目标列表 -->
            <div class="objectives-list">
              <div
                v-for="(objective,index) in getObjectivesByIndicator(indicator.poId)"
                :key="objective.objectiveId"
                class="objective-card"
              >
                <div class="objective-card-inner">
                  <div class="objective-header">
                    <div class="objective-number">课程目标{{ objective.number }}</div>
                    <div class="objective-content">
                      <h4 class="objective-title">{{ objective.objectiveName }}</h4>
                      <p class="objective-description">{{ objective.description }}</p>
                    </div>
                    <div class="objective-weight">
                      <span class="weight-label">期望值</span>
                      <span class="weight-value">{{ objective.expectedScore }}%</span>
                    </div>
                    <div class="objective-actions">
                      <!-- <t-button
                        theme="warning"
                        variant="base"
                        size="small"
                        @click="showAssessmentMethodDialog(objective)"
                        class="assessment-btn"
                      >
                        <template #icon>
                          <t-icon name="setting" size="14px" />
                        </template>
                        设置考核方式
                      </t-button> -->
                      <t-button
                        theme="default"
                        variant="outline"
                        size="small"
                        @click="editObjective(objective)"
                        class="edit-btn"
                      >
                        <template #icon>
                          <t-icon name="edit" size="14px" />
                        </template>
                        编辑
                      </t-button>
                      <t-button
                        theme="danger"
                        variant="outline"
                        size="small"
                        @click="deleteObjective(objective)"
                        class="delete-btn"
                      >
                        <template #icon>
                          <t-icon name="delete" size="14px" />
                        </template>
                        删除
                      </t-button>
                    </div>
                  </div>
                  
                  <!-- 考核方式展示 -->
                  <div class="assessment-methods">
                    <div class="assessment-label">考核方式：</div>
                    <div class="assessment-tags">
                      <template v-if="objective.assessmentMethods && objective.assessmentMethods.length > 0">
                        <t-tag
                          v-for="method in objective.assessmentMethods"
                          :key="method.id"
                          theme="default"
                          size="small"
                          class="assessment-tag"
                        >
                          {{ method.name }}
                        </t-tag>
                      </template>
                      <template v-else>
                        <t-tag
                          theme="default"
                          variant="outline"
                          size="small"
                          class="assessment-empty-tag"
                        >
                          尚未配置考核方式
                        </t-tag>
                      </template>
                    </div>
                  </div>
                </div>
              </div>
              
              <!-- 空状态 -->
              <div v-if="getObjectivesByIndicator(indicator.poId).length === 0" class="empty-objectives">
                <div class="empty-content">
                  <t-icon name="file" size="48px" class="empty-icon" />
                  <h4 class="empty-title">暂无课程目标</h4>
                  <p class="empty-subtitle">点击上方"添加课程目标"按钮开始添加</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      </template>
    </div>

    <!-- 添加/编辑课程目标对话框 -->
    <AddCourseObjectiveDialog
      v-model:visible="showAddObjectiveDialogVisible"
      :selected-indicator="selectedIndicator"
      :existing-objectives="courseObjectives"
      :is-edit-mode="isEditMode"
      :editing-objective="editingObjective"
      @confirm="handleAddObjectiveConfirm"
      @cancel="handleAddObjectiveCancel"
      @reorderObjectives="handleObjectiveReorder"
    />

    <!-- 设置考核方式对话框 -->
    <t-dialog
      v-model:visible="showAssessmentDialogVisible"
      header="设置考核方式"
      width="500px"
      :confirm-btn="{ content: '保存', theme: 'primary' }"
      :cancel-btn="{ content: '取消', theme: 'default', variant: 'outline' }"
      @confirm="saveAssessmentMethods"
    >
      <div class="assessment-dialog-content">
        <div class="current-objective-info">
          <h4>{{ currentEditingObjective?.objectiveName }}</h4>
          <p>{{ currentEditingObjective?.description }}</p>
        </div>
        <t-form label-align="top">
          <t-form-item label="选择考核方式">
            <t-checkbox-group v-model="selectedAssessmentMethods">
              <t-checkbox
                v-for="method in availableAssessmentMethods"
                :key="method.id"
                :value="method.id"
              >
                {{ method.name }}
              </t-checkbox>
            </t-checkbox-group>
          </t-form-item>
        </t-form>
      </div>
    </t-dialog>

    <!-- 选择毕业要求指标点对话框 -->
    <IndicatorSelectionDialog
      v-model:visible="showIndicatorSelectionDialogVisible"
      :plan-id="getRealPlanId()"
      :selected-indicator-ids="graduationIndicators.map(indicator => indicator.poId.toString())"
      @confirm="handleIndicatorSelectionConfirm"
      @cancel="handleIndicatorSelectionCancel"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useRoute } from 'vue-router';
import { MessagePlugin } from 'tdesign-vue-next';
import { 
  getCourseTargetList,
  getCourseBaseInfo, 
  updateCourseTarget,  
  type CourseAssessmentDetailVO, 
  type CourseDetailInfo, 
  type CourseObjectiveVO,
  type AssessmentMethod,
} from '@/api/training/course';
import { getRequirementById, getPoList,type PoVO } from '@/api/training/po';
import { listPoMatrix ,getPoTreeFromMatrixByCourseId, type CoursePoVO} from '@/api/training/po-matrix';
import AddCourseObjectiveDialog from './components/AddCourseObjectiveDialog.vue';
import IndicatorSelectionDialog from './components/IndicatorSelectionDialog.vue';
import { getDictData,getDictDataByTypeTitle } from '@/utils/dictUtil';


// 路由参数
const route = useRoute();
const courseId = typeof route.params.courseId === 'string' ? Number(route.params.courseId) : Number(route.params.courseId[0]);

// 响应式数据
const dataLoading = ref(false);
const errorMessage = ref('');
const matrixLoading = ref(false);
const saveLoading = ref(false);

// 全局课程数据存储
const courseDetailData = ref<CourseDetailInfo | null>(null);

// 毕业要求指标点数据（课程对应的指标点数据，是支撑矩阵信息）
const graduationIndicators = ref<CoursePoVO[]>([]);

// 课程目标数据（课程对应的课程目标，是课程基本信息）
const courseObjectives = ref<CourseObjectiveVO[]>([]);

// 指标点到课程目标的映射关系,毕业设计指标->课程目标的映射关系
const indicatorToObjectivesMap =ref(new Map<string, CourseObjectiveVO[]>()) ;
// 匹配的毕业要求指标点Map（带对应的课程目标数组）
//const matchedIndicatorToObjectivesMap = new Map<string, CourseObjectiveVO[]>();
  
// 未匹配的课程目标列表
const unmatchedObjectives = ref<CourseObjectiveVO[]>([]);
// 可用的考核方式
const availableAssessmentMethods = ref<AssessmentMethod[]>([]);

// 对话框状态
const showAddObjectiveDialogVisible = ref(false);
const showAssessmentDialogVisible = ref(false);
const showIndicatorSelectionDialogVisible = ref(false);



// 数据加载状态标记
const hasTriedToLoadData = ref(false);
const dataLoadFailed = ref(false);

// 控制关联按钮显示的计算属性
const showAssociateButton = computed(() => {
  // 只有在以下情况才显示关联按钮：
  // 1. 已经尝试过加载数据
  // 2. 数据加载失败
  // 3. 或者存在指标点但数据不完整
  return hasTriedToLoadData.value &&
         (dataLoadFailed.value ||
          (graduationIndicators.value.length > 0 && courseObjectives.value.length === 0));
});

//控制设置毕业要求指标点按钮的显示
const showIndicatorSelectionButton = computed(() => {
  // 只有在以下情况才显示设置按钮：
  // graduationIndicators.length为0时
  return graduationIndicators.value.length === 0;
});


// 当前选中的指标点
const selectedIndicator = ref<CoursePoVO | null>(null);

// 当前编辑的课程目标
const currentEditingObjective = ref<CourseObjectiveVO | null>(null);
const selectedAssessmentMethods = ref<string[]>([]);

// 编辑模式状态
const isEditMode = ref(false);
const editingObjective = ref<CourseObjectiveVO | null>(null);

// 计算方法

const getSupportLevelTheme = (weight: number) => {
  if (weight >= 0.7) return 'success';
  if (weight >= 0.4) return 'warning';
  return 'default';
};




// 获取真实的planId
const getRealPlanId = (): number => {
  if (!courseDetailData.value) {
   // console.warn('课程详情数据未加载，使用默认planId');
    return 1; // fallback默认值
  }

  // 尝试从课程详情中获取planId
  const planId = courseDetailData.value.planId;

  if (planId && typeof planId === 'number' && planId > 0) {
    return planId;
  }

  // 如果没有有效的planId，使用默认值并记录警告
  console.warn('课程详情中未找到有效的planId，使用默认值1。课程详情:', courseDetailData.value);
  return 1;
};



// 获取指定指标点关联的课程目标数量
const getObjectiveCountByIndicator = (indicatorId: Number): number => {
  return courseObjectives.value.filter(objective => objective.po.id === indicatorId).length;
  //return indicatorToObjectivesMap.value.get(indicatorId.toString())?.length || 0;
};

// 获取指定指标点关联的课程目标列表（按编号排序）
const getObjectivesByIndicator = (indicatorId: Number): CourseObjectiveVO[] => {
  return courseObjectives.value
    .filter(objective => objective.po.id === indicatorId)
    .sort((a, b) => a.number - b.number); // 按编号升序排序
  //return indicatorToObjectivesMap.value.get(indicatorId.toString()) || [];
};

// 计算属性：按编号排序的毕业要求指标点
const sortedGraduationIndicators = computed(() => {
  return [...graduationIndicators.value].sort((a, b) => {
    // 首先按标准编号排序
    const aStandardNumber = a.standardNumber || 0;
    const bStandardNumber = b.standardNumber || 0;
    if (aStandardNumber !== bStandardNumber) {
      return aStandardNumber - bStandardNumber;
    }

    // 如果标准编号相同，按指标点编号排序
    const aPoNumber = a.poNumber || 0;
    const bPoNumber = b.poNumber || 0;
    return aPoNumber - bPoNumber;
  });
});


// 事件处理方法
const showAddObjectiveDialog = (indicator: CoursePoVO) => {
  // 重置为添加模式
  isEditMode.value = false;
  editingObjective.value = null;
  selectedIndicator.value = indicator;
  showAddObjectiveDialogVisible.value = true;
};

// 处理添加/编辑课程目标确认事件
const handleAddObjectiveConfirm = async (
  objective: CourseObjectiveVO, 
  positionInfo: { type: string, position: number }
) => {
  try {
    if (isEditMode.value) {
      // 编辑模式：首先移除原目标
      const index = courseObjectives.value.findIndex(obj => obj.objectiveId === objective.objectiveId);
      if (index > -1) {
        // 移除原目标
        courseObjectives.value.splice(index, 1);
        
        // 根据新的位置信息插入目标
        insertObjectiveAtPosition(objective, positionInfo);
        
        MessagePlugin.success(`已更新"${objective.objectiveName}"并调整位置`);
      }
    } else {
      // 添加模式：根据位置信息插入新课程目标
      insertObjectiveAtPosition(objective, positionInfo);
      MessagePlugin.success(`已添加"${objective.objectiveName}"并更新编号`);
    }

    showAddObjectiveDialogVisible.value = false;

    // 自动保存课程目标数据
    await saveCourseTargets();

  } catch (error) {
    const action = isEditMode.value ? '更新' : '添加';
    console.error(`${action}课程目标失败:`, error);
    MessagePlugin.error(`${action}课程目标失败`);
  }
};

// 根据位置信息插入课程目标
const insertObjectiveAtPosition = (
  objective: CourseObjectiveVO, 
  positionInfo: { type: string, position: number }
) => {
  const { type, position } = positionInfo;
  
  // 根据位置类型确定插入位置
  if (type === 'start') {
    // 插入到开头
    courseObjectives.value.unshift(objective);
  } else if (type === 'end') {
    // 插入到末尾
    courseObjectives.value.push(objective);
  } else if (type === 'after') {
    // 插入到指定位置之后
    courseObjectives.value.splice(position, 0, objective);
  }
  
  // 重新编号所有课程目标，确保编号连续
  reorderObjectiveNumbers();
};

// 处理添加/编辑课程目标取消事件
const handleAddObjectiveCancel = () => {
  showAddObjectiveDialogVisible.value = false;
  // 重置编辑状态
  isEditMode.value = false;
  editingObjective.value = null;
};

// 处理指标点选择确认事件
const handleIndicatorSelectionConfirm = async (indicators: CoursePoVO[]) => {
  try {
    // 更新组件状态
    graduationIndicators.value = indicators;
    showIndicatorSelectionDialogVisible.value = false;

    // 自动保存数据到后端
    await saveCourseTargets();

    MessagePlugin.success(`成功设置 ${indicators.length} 个毕业要求指标点并保存到后端`);
  } catch (error) {
    console.error('设置指标点失败:', error);
    MessagePlugin.error('设置指标点失败');
  }
};

// 处理指标点选择取消事件
const handleIndicatorSelectionCancel = () => {
  showIndicatorSelectionDialogVisible.value = false;
};

const editObjective = (objective: CourseObjectiveVO) => {
  // 设置编辑模式
  isEditMode.value = true;
  editingObjective.value = objective;

  // 找到对应的指标点
  const indicator = graduationIndicators.value.find(ind => ind.id === objective.po.id);
  selectedIndicator.value = indicator || null;

  // 打开对话框
  showAddObjectiveDialogVisible.value = true;
};

// 重新编号所有课程目标的辅助函数
const reorderObjectiveNumbers = () => {
  // 保持当前数组的顺序，只重新分配编号
  // 这样可以保持用户通过拖拽或移动按钮设置的顺序

  // 重新分配连续编号，从1开始
  let currentNumber = 1;
  courseObjectives.value.forEach((objective) => {
    objective.number = currentNumber++;
  });

  console.log('课程目标编号重排完成:',
    courseObjectives.value.map(obj => `${obj.number}(${obj.objectiveName})`).join(', ')
  );
};

const deleteObjective = async (objective: CourseObjectiveVO) => {
  try {
    // 使用objectiveId查找精确匹配的目标，而不是通过po.id（因为同一po.id可能有多个目标）
    const index = courseObjectives.value.findIndex(obj => obj.objectiveId === objective.objectiveId);
    if (index > -1) {
      // 删除课程目标
      courseObjectives.value.splice(index, 1);

      // 重新编号剩余的课程目标
      reorderObjectiveNumbers();

      // 自动保存更新后的课程目标数据
      await saveCourseTargets();

      MessagePlugin.success(`已删除"${objective.objectiveName}"并重新排序编号`);
    }
  } catch (error) {
    console.error('删除课程目标失败:', error);
    MessagePlugin.error('删除课程目标失败');
  }
};

const showAssessmentMethodDialog = (objective: CourseObjectiveVO) => {
  currentEditingObjective.value = objective;
  selectedAssessmentMethods.value = objective.assessmentMethods?.map(method => method.id) || [];
  showAssessmentDialogVisible.value = true;
};

const saveAssessmentMethods = async () => {
  if (currentEditingObjective.value) {
    try {
      const selectedMethods = availableAssessmentMethods.value.filter(
        method => selectedAssessmentMethods.value.includes(method.id)
      );

      // 更新内存中的考核方式数据
      currentEditingObjective.value.assessmentMethods = selectedMethods;

      // 关闭对话框
      showAssessmentDialogVisible.value = false;

      // 自动保存到后端
      await saveCourseTargets();

      MessagePlugin.success(`考核方式设置成功并已保存到后端`);
    } catch (error) {
      console.error('设置考核方式失败:', error);
      MessagePlugin.error('设置考核方式失败');
    }
  }
};


// 数据加载函数
const fetchCourseData = async () => {
  if (!courseId) {
    errorMessage.value = '课程ID不能为空';
    return;
  }
  try {
    dataLoading.value = true;
    errorMessage.value = '';
    hasTriedToLoadData.value = true;
    dataLoadFailed.value = false;

    // 1. 获取课程详情
    // 2. 保存完整的课程详情到全局变量
   // let matrixData: CoursePoVO[] = [];
    
    try {
      //courseDetailData.value = await getCourseDetailInfo(courseId);
      graduationIndicators.value =  await getPoTreeFromMatrixByCourseId(courseId);
      courseObjectives.value = await getCourseTargetList(courseId);
      console.log('granduationIndicators', graduationIndicators.value);
    } catch(error: any) {
      errorMessage.value = '加载数据失败，请稍后重试';
      dataLoadFailed.value = true;
    }
  
  
  // 创建毕业要求指标点ID的Set，用于快速查找
  const graduationIndicatorIds = new Set(graduationIndicators.value.map(indicator => 
  indicator.poId?.toString() || ''));
  
  // 遍历课程目标，进行匹配处理
  courseObjectives.value.forEach(objective => {
    const poId = objective.po?.id?.toString();
    
    // 检查课程目标是否有有效的毕业要求指标点ID
    if (!poId || poId === '' || poId === 'undefined' || poId === 'null') {
      // 课程目标没有对应的毕业要求指标点ID，标记为异常
      //objective.matchStatus = 'NO_PO_ID';
      //objective.matchMessage = '未匹配毕业要求异常';
      unmatchedObjectives.value.push(objective);
      return;
    }
    
    // 检查课程目标的毕业要求指标点ID是否在毕业要求指标点列表中
    if (graduationIndicatorIds.has(poId)) {
      // 匹配成功，添加到匹配的Map中
      if (!indicatorToObjectivesMap.value.has(poId)) {
        indicatorToObjectivesMap.value.set(poId, []);
      }
      indicatorToObjectivesMap.value.get(poId)?.push(objective);
      
      // 标记课程目标匹配状态
      //objective.matchStatus = 'MATCHED';
      //objective.matchMessage = '已匹配';
    } else {
      // 未匹配，毕业要求指标点ID不存在
      //objective.matchStatus = 'PO_NOT_FOUND';
      //objective.matchMessage = `毕业要求指标点ID "${poId}" 不存在`;
      unmatchedObjectives.value.push(objective);
    }
  });
  
  // 输出匹配结果用于调试
  console.log('匹配的毕业要求指标点Map:', indicatorToObjectivesMap.value);
  console.log('未匹配的课程目标列表:', unmatchedObjectives.value);
  
  // 如果存在未匹配的课程目标，显示警告
  if (unmatchedObjectives.value.length > 0) {
    console.warn(`发现 ${unmatchedObjectives.value.length} 个未匹配的课程目标:`, 
      unmatchedObjectives.value.map(obj => `${obj.objectiveName} (${obj.po?.id})`));
  }
  
  // 将匹配结果保存到组件状态
  //indicatorToObjectivesMap.clear();
  indicatorToObjectivesMap.value.forEach((objectives, indicatorId) => {
    indicatorToObjectivesMap.value.set(indicatorId, objectives);
  });
  } catch (error) {
    console.error('加载课程数据失败:', error);
    errorMessage.value = '加载课程数据失败，请稍后重试';
    dataLoadFailed.value = true;
    dataLoading.value = false;
  }finally {
    dataLoading.value = false;
  }
  console.log('指标点-课程目标映射关系:', indicatorToObjectivesMap.value);
  console.log('课程详情已保存到全局变量:', courseDetailData.value);
  console.log('课程目标数据加载完成:', courseObjectives.value);
  console.log('毕业要求指标点数据加载完成:', graduationIndicators.value);
};


// 显示指标点选择对话框
const showIndicatorSelectionDialog = async () => {
  showIndicatorSelectionDialogVisible.value = true;
};


// 关联毕业要求指标点功能（备用方案）
const associateGraduationIndicators = async (showUserMessages: boolean = true) => {
  if (!courseId) {
    if (showUserMessages) {
      MessagePlugin.warning('课程ID不能为空');
    }
    return;
  }

  try {
    matrixLoading.value = true;
    errorMessage.value = '';

    // 获取真实的planId
    const realPlanId = getRealPlanId();
   // console.log('开始关联毕业要求指标点，课程ID:', courseId, 'planId:', realPlanId);

    // 验证planId的有效性
    if (!realPlanId || realPlanId <= 0) {
      throw new Error(`无效的planId: ${realPlanId}`);
    }

    // 1. 调用listPoMatrix获取课程支撑矩阵数据
    const matrixData = await listPoMatrix({
      courseId: Number(courseId),
      planId: realPlanId
    });

    if (!matrixData || matrixData.length === 0) {
      if (showUserMessages) {
        MessagePlugin.info(`该课程暂无关联的毕业要求指标点，请先在支撑矩阵中配置`);
      }
      return;
    }

    // 2. 获取所有指标点详情
    const poListData = await getPoList(realPlanId);

    if (!poListData?.data || poListData.data.length === 0) {
      if (showUserMessages) {
        MessagePlugin.warning(`培养方案(ID: ${realPlanId})下未找到毕业要求指标点数据`);
      }
      return;
    }

    // 3. 根据矩阵数据筛选出该课程支撑的指标点
    const supportedPoIds = matrixData.map(matrix => matrix.poId);
    const supportedPoList = poListData.data.filter((po: any) =>
      supportedPoIds.includes(po.id)
    );

   // console.log('支撑的指标点:', supportedPoList);

    // 4. 转换为CoursePoVO格式
    const indicators: CoursePoVO[] = supportedPoList.map((po: any) => ({
      id: po.id,
      poId: po.id,
      poNumber: Number(po.poNumber) || 1,
      poTitle: po.poTitle || '未知指标点',
      poDescription: po.poDescription || '暂无描述',
      standardNumber: Number(po.standardNumber) || 1,
      courseId: courseId,
      weight: 1 // 默认权重
    }));

    // 5. 更新组件状态
    graduationIndicators.value = indicators;

    if (showUserMessages) {
      MessagePlugin.success(`成功关联 ${indicators.length} 个毕业要求指标点`);
    }
   // console.log('关联完成，指标点数据:', indicators);

  } catch (error) {
    console.error('关联毕业要求指标点失败:', error);

    if (showUserMessages) {
      // 提供更详细的错误信息
      const errorMsg = error instanceof Error ? error.message : '未知错误';
      errorMessage.value = `关联毕业要求指标点失败: ${errorMsg}`;
      MessagePlugin.error(`关联毕业要求指标点失败: ${errorMsg}`);
    }
  } finally {
    matrixLoading.value = false;
  }
};

// 数据验证函数
const validateCourseTargetData = (): boolean => {
  if (courseObjectives.value.length === 0) {
    MessagePlugin.warning('请先添加课程目标');
    return false;
  }

  // 检查每个课程目标的必要字段
  for (const objective of courseObjectives.value) {
    if (!objective.objectiveName || objective.objectiveName.trim() === '') {
      MessagePlugin.warning('课程目标标题不能为空');
      return false;
    }

    if (!objective.po || !objective.po.id || objective.po.id <= 0) {
      MessagePlugin.warning('课程目标必须关联毕业要求指标点');
      return false;
    }
  }

  return true;
};


// 保存课程目标数据
const saveCourseTargets = async () => {
  if (!courseId) {
    MessagePlugin.warning('课程ID不能为空');
    return;
  }

  try {
    saveLoading.value = true;
   // console.log('开始保存课程目标数据...');

    // 确保在保存前重新排序
    reorderObjectiveNumbers();
    
    // 1. 数据验证（仅在有课程目标时进行验证）
    if (courseObjectives.value.length > 0 && !validateCourseTargetData()) {
      return;
    }
    const objectiveList: CourseObjectiveVO[] = courseObjectives.value.map(objective => ({
      objectiveId: objective.objectiveId,
      objectiveName: objective.objectiveName,
      number: objective.number,
      description: objective.description,
      expectedScore: objective.expectedScore,
      assessmentMethods: objective.assessmentMethods?.map(method => ({
        id: method.id && !isNaN(parseInt(method.id)) ? method.id : (Date.now() + Math.floor(Math.random() * 1000)).toString(),
        name: method.name,
        weight: method.weight || 100,
        isCustom: method.isCustom || false
      })) || [],
      po: objective.po
    }));
    console.log('转换后的课程目标列表:', objectiveList);

    const coList: CourseAssessmentDetailVO = {
      courseId: Number(courseId),
      courseName: courseDetailData.value?.courseName || '',
      courseObjectiveList: objectiveList,
      assessmentMethods: [],
      assessmentWeight: [],
      assessmentProportions: [],
      finalExamWeights: []
    };
    console.log('准备保存的课程目标数据:', coList);

    // 3. 将毕业要求指标点转换为新格式
    // const graduationIndicatorsList: PoVO[] = graduationIndicators.value.map(indicator => ({
    //   id: indicator.id,
    //   poNumber: indicator.poNumber,
    //   poTitle: indicator.poTitle,
    //   poDescription: indicator.poDescription,
    //   planId: getRealPlanId(),
    //   requirementId: indicator.standardNumber.toString(),
    //   standardNumber: indicator.standardNumber,
    //   isRequirement: false,
    //   standardId: null as number | null,
    //   status: 0,
    //   parentId: null as number | null
    // }));

    // POST请求到updateTarget端点（查询参数+请求体）
     await updateCourseTarget(Number(courseId), coList);
    

  } catch (error) {
    console.error('保存课程目标失败:', error);
    const errorMsg = error instanceof Error ? error.message : '未知错误';
    MessagePlugin.error(`保存课程目标失败: ${errorMsg}`);
  } finally {
    saveLoading.value = false;
  }
};

// 处理拖拽排序
const handleObjectiveReorder = async (data: { sourceId: number, targetId: number, position: 'before' | 'after' | 'start' | 'end' }) => {
  try {
    const { sourceId, targetId, position } = data;
    
    // 找到源课程目标
    const sourceIndex = courseObjectives.value.findIndex(obj => obj.objectiveId === sourceId);
    if (sourceIndex === -1) return;
    
    // 从数组中移除源目标
    const sourceObjective = courseObjectives.value.splice(sourceIndex, 1)[0];
    
    if (targetId === -1) {
      // 特殊位置: start 或 end
      if (position === 'start') {
        // 放在列表最前面
        courseObjectives.value.unshift(sourceObjective);
      } else {
        // 放在列表最后面
        courseObjectives.value.push(sourceObjective);
      }
    } else {
      // 找到目标课程目标的索引
      const targetIndex = courseObjectives.value.findIndex(obj => obj.objectiveId === targetId);
      if (targetIndex === -1) {
        // 找不到目标，放回原位置
        courseObjectives.value.splice(sourceIndex, 0, sourceObjective);
        return;
      }
      
      // 根据位置插入
      if (position === 'before') {
        courseObjectives.value.splice(targetIndex, 0, sourceObjective);
      } else { // after
        courseObjectives.value.splice(targetIndex + 1, 0, sourceObjective);
      }
    }
    
    // 重新编号
    reorderObjectiveNumbers();
    
    // 自动保存
    await saveCourseTargets();
    
    MessagePlugin.success('课程目标排序已更新');
  } catch (error) {
    console.error('排序课程目标失败:', error);
    MessagePlugin.error('排序课程目标失败');
  }
};

// 生命周期
onMounted(() => {
  // 使用dictUtils获取字典数据
  getDictDataByTypeTitle('考核环节').then(data => {
    availableAssessmentMethods.value = data.map(item => ({
      id: item.id.toString(),
      name: item.label,
      weight: 100,
      isCustom: false
    }));
  });
  fetchCourseData();
});

</script>

<style lang="less" scoped>
.course-target-container {
  min-height: 100vh;
  height: 100%;
  background: var(--td-bg-color-page);
  position: relative;
  display: flex;
  flex-direction: column;

  .dashboard-header {
    background: var(--td-bg-color-container);
    border-bottom: 1px solid var(--td-border-level-1-color);
    padding: 16px 24px;
    position: relative;
    z-index: 2;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.03);

    .header-content {
      width: 100%;
      text-align: left;

      .title-info {
        .title-container {
          display: flex;
          align-items: center;
          gap: 12px;
          margin-bottom: 6px;
            .dashboard-title {
          .dashboard-title {
            font-size: 22px;
            font-weight: 600;
            color: var(--td-text-color-primary);
            display: flex;
            align-items: center;
            } margin: 0;
          }

          .role-tag {
            background: var(--td-bg-color-container-select);
            color: var(--td-brand-color);
            border: 1px solid var(--td-brand-color-3);
            font-weight: 500;
            font-size: 12px;
          }
        }

        .dashboard-subtitle {
          font-size: 14px;
          color: var(--td-text-color-secondary);
          margin: 0;
          font-weight: 400;
        }
      }
    }
  }

  .dashboard-content {
    max-width: 100%;
    width: 100%;
    margin: 0 auto;
    padding: 32px 24px;
    position: relative;
    z-index: 1;
    background: var(--td-bg-color-container);
    border-radius: 12px;
    box-shadow: var(--td-shadow-1);
    flex-grow: 1;
    min-height: calc(100vh - 180px); /* 减去header和其他元素的高度 */
  }

  // 加载和错误状态
  .loading-container,
  .error-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 400px;
    padding: 40px;
  }

  .error-content {
    text-align: center;

    .error-icon {
      color: var(--td-error-color);
      margin-bottom: 16px;
    }

    .error-title {
      font-size: 20px;
      font-weight: 600;
      color: var(--td-text-color-primary);
      margin: 0 0 8px 0;
    }

    .error-subtitle {
      font-size: 14px;
      color: var(--td-text-color-secondary);
      margin: 0 0 24px 0;
    }
  }

  // 毕业要求指标点空状态
  .indicators-empty-state {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 300px;
    padding: 40px 20px;

    .empty-state-content {
      text-align: center;
      max-width: 400px;

      .empty-icon {
        color: var(--td-text-color-placeholder);
        margin-bottom: 24px;
      }

      .empty-title {
        font-size: 20px;
        font-weight: 600;
        color: var(--td-text-color-primary);
        margin: 0 0 12px 0;
      }

      .empty-description {
        font-size: 14px;
        color: var(--td-text-color-secondary);
        line-height: 1.6;
        margin: 0 0 32px 0;
      }

      .empty-actions {
        .setup-indicators-btn {
          font-size: 16px;
          padding: 12px 24px;
          border-radius: 8px;
          font-weight: 500;
          transition: all 0.3s ease;

          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
          }
        }
      }
    }
  }

  // 毕业要求指标点区域
  .graduation-indicators-section {
    margin-bottom: 48px;

    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 24px;

      .header-left {
        .section-title {
          display: flex;
          align-items: center;
          gap: 12px;
          font-size: 22px;
          font-weight: 600;
          color: var(--td-text-color-primary);
          margin: 0;

          .title-icon {
            color: var(--td-brand-color);
          }
        }
      }

      .header-right {
        .header-buttons {
          display: flex;
          gap: 12px;
          align-items: center;

          .setup-indicators-btn,
          .associate-btn {
            font-size: 14px;
            padding: 8px 16px;
            border-radius: 8px;
            font-weight: 500;
            transition: all 0.3s ease;

            &:hover {
              transform: translateY(-1px);
              box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            }

            // 警告主题的特殊样式
            &.t-button--theme-warning {
              background: var(--td-warning-color);
              border-color: var(--td-warning-color);

              &:hover {
                background: var(--td-warning-color-hover);
                border-color: var(--td-warning-color-hover);
                box-shadow: 0 4px 12px var(--td-warning-color-2);
              }
            }
          }
        }
      }
    }

    .indicators-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
      gap: 24px;

      .indicator-card {
        opacity: 0;
        transform: translateY(30px);
        animation: slideInUp 0.6s ease-out var(--delay) both;

        .indicator-card-inner {
          background: var(--td-bg-color-container);
          border: 1px solid var(--td-border-level-1-color);
          border-radius: 16px;
          padding: 24px;
          position: relative;
          transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
          height: 100%;
          display: flex;
          flex-direction: column;
          box-shadow: var(--td-shadow-1);

          &:hover {
            background: var(--td-bg-color-container-hover);
            border-color: var(--td-brand-color-3);
            transform: translateY(-4px);
            box-shadow: var(--td-shadow-3);
          }

          .indicator-header {
            display: flex;
            align-items: flex-start;
            justify-content: space-between;
            margin-bottom: 16px;

            .indicator-left {
              display: flex;
              align-items: flex-start;
              gap: 16px;
              flex: 1;

              .indicator-number {
                background: var(--td-brand-color);
                color: white;
                padding: 8px 12px;
                border-radius: 8px;
                font-weight: 600;
                font-size: 14px;
                white-space: nowrap;
                flex-shrink: 0;
              }
            }

            .indicator-right {
              flex-shrink: 0;
              margin-left: 16px;

              .objective-count-tag {
                font-size: 12px;
                font-weight: 500;
              }
            }

            .indicator-info {
              flex: 1;
              min-width: 0;

              .indicator-title {
                font-size: 18px;
                font-weight: 600;
                color: var(--td-text-color-primary);
                margin: 0 0 8px 0;
                line-height: 1.3;
              }

              .support-level {
                .support-badge {
                  font-weight: 500;
                }
              }
            }

            .indicator-actions {
              flex-shrink: 0;

              .add-objective-btn {
                font-size: 14px;
                padding: 8px 16px;
                border-radius: 8px;
                font-weight: 500;
              }
            }
          }

          .indicator-description {
            color: var(--td-text-color-secondary);
            line-height: 1.6;
            font-size: 15px;
            margin-bottom: 16px;
          }

          .objective-status {
            margin-top: auto;

            .has-objectives {
              .objective-list {
                margin-top: 12px;
                display: flex;
                flex-wrap: wrap;
                gap: 8px;

                .objective-tag {
                  font-size: 12px;
                  font-weight: 600;
                  border-radius: 6px;
                  transition: all 0.2s ease;

                  &:hover {
                    transform: translateY(-1px);
                    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
                  }
                }
              }
            }

            .no-objectives {
              display: flex;
              flex-direction: column;
              gap: 8px;
              align-items: flex-start;

              .add-objective-link {
                font-size: 12px;
                padding: 4px 8px;
              }
            }
          }
        }
      }
    }
  }

  // 课程目标区域
  .course-objectives-section {
    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 24px;

      .header-left {
        .section-title {
          display: flex;
          align-items: center;
          gap: 12px;
          font-size: 22px;
          font-weight: 600;
          color: var(--td-text-color-primary);
          margin: 0;

          .title-icon {
            color: var(--td-brand-color);
          }
        }
      }

      .header-right {
        .save-btn {
          font-size: 14px;
          padding: 8px 16px;
          border-radius: 8px;
          font-weight: 500;
          transition: all 0.3s ease;

          &:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
          }
        }
      }
    }

    // 课程目标空状态引导页面
    .objectives-empty-guide {
      display: flex;
      justify-content: center;
      align-items: center;
      min-height: 400px;
      padding: 40px 20px;

      .empty-guide-content {
        text-align: center;
        max-width: 500px;

        .empty-guide-icon {
          color: var(--td-brand-color);
          margin-bottom: 24px;
        }

        .empty-guide-title {
          font-size: 20px;
          font-weight: 600;
          color: var(--td-text-color-primary);
          margin: 0 0 16px 0;
        }

        .empty-guide-description {
          font-size: 14px;
          color: var(--td-text-color-secondary);
          line-height: 1.6;
          margin: 0 0 32px 0;
        }

        .empty-guide-steps {
          display: flex;
          flex-direction: column;
          gap: 16px;
          text-align: left;

          .guide-step {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px 16px;
            background: var(--td-bg-color-container);
            border: 1px solid var(--td-border-level-1-color);
            border-radius: 8px;
            transition: all 0.3s ease;

            &:hover {
              border-color: var(--td-brand-color-3);
              background: var(--td-bg-color-container-hover);
            }

            .step-number {
              width: 24px;
              height: 24px;
              background: var(--td-brand-color);
              color: white;
              border-radius: 50%;
              display: flex;
              align-items: center;
              justify-content: center;
              font-size: 12px;
              font-weight: 600;
              flex-shrink: 0;
            }

            .step-text {
              font-size: 14px;
              color: var(--td-text-color-primary);
              font-weight: 500;
            }
          }
        }
      }
    }

    .objectives-by-indicator {
      display: flex;
      flex-direction: column;
      gap: 32px;

      .indicator-objectives-group {
        .group-header {
          margin-bottom: 16px;
          display: flex;
          align-items: center;
          justify-content: space-between;
          gap: 16px;

          .group-indicator {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px 20px;
            background: var(--td-bg-color-container);
            border-radius: 12px;
            border-left: 4px solid var(--td-brand-color);
            border: 1px solid var(--td-border-level-1-color);
            flex: 1;

            .group-number {
              background: var(--td-brand-color-1);
              color: var(--td-brand-color);
              padding: 4px 8px;
              border-radius: 6px;
              font-weight: 600;
              font-size: 13px;
            }

            .group-title {
              font-size: 16px;
              font-weight: 600;
              color: var(--td-text-color-primary);
            }
          }

          .group-actions {
            display: flex;
            align-items: center;

            .add-objective-group-btn {
              font-size: 13px;
              padding: 8px 16px;
              border-radius: 6px;
              font-weight: 500;
              box-shadow: var(--td-shadow-1);
              transition: all 0.2s ease;
              white-space: nowrap;

              &:hover {
                transform: translateY(-1px);
                box-shadow: var(--td-shadow-2);
              }
            }
          }
        }

        .objectives-list {
          display: flex;
          flex-direction: column;
          gap: 16px;

          .objective-card {
            .objective-card-inner {
              background: var(--td-bg-color-container);
              border: 1px solid var(--td-border-level-1-color);
              border-radius: 12px;
              padding: 20px;
              transition: all 0.3s ease;
              box-shadow: var(--td-shadow-1);
              &:hover {
                border-color: var(--td-brand-color-3);
                box-shadow: var(--td-shadow-2);
                background: var(--td-bg-color-container-hover);
              }

              .objective-header {
                display: flex;
                align-items: flex-start;
                gap: 16px;
                margin-bottom: 16px;

                .objective-number {
                  background: var(--td-brand-color-1);
                  color: var(--td-brand-color);
                  padding: 6px 10px;
                  border-radius: 6px;
                  font-weight: 600;
                  font-size: 13px;
                  white-space: nowrap;
                  flex-shrink: 0;
                }

                .objective-content {
                  flex: 1;
                  min-width: 0;

                  .objective-title {
                    font-size: 16px;
                    font-weight: 600;
                    color: var(--td-text-color-primary);
                    margin: 0 0 8px 0;
                    line-height: 1.3;
                  }

                  .objective-description {
                    color: var(--td-text-color-secondary);
                    line-height: 1.5;
                    margin: 0;
                    font-size: 14px;
                  }
                }

                .objective-weight {
                  display: flex;
                  flex-direction: column;
                  align-items: center;
                  gap: 4px;
                  padding: 8px 12px;
                  background: var(--td-brand-color-1);
                  border-radius: 8px;
                  flex-shrink: 0;

                  .weight-label {
                    font-size: 12px;
                    color: var(--td-text-color-secondary);
                  }

                  .weight-value {
                    font-size: 16px;
                    font-weight: 600;
                    color: var(--td-brand-color);
                  }
                }

                .objective-actions {
                  display: flex;
                  gap: 8px;
                  flex-shrink: 0;

                  .t-button {
                    font-size: 12px;
                    padding: 6px 12px;
                    border-radius: 6px;
                  }
                }
              }

              .assessment-methods {
                display: flex;
                align-items: center;
                gap: 12px;
                padding-top: 16px;
                border-top: 1px solid var(--td-border-level-1-color);

                .assessment-label {
                  font-size: 14px;
                  color: var(--td-text-color-secondary);
                  font-weight: 500;
                }

                .assessment-tags {
                  display: flex;
                  gap: 8px;
                  flex-wrap: wrap;

                  .assessment-tag {
                    background: var(--td-brand-color-1);
                    color: var(--td-brand-color);
                    border: 1px solid var(--td-brand-color-3);
                  }

                  .assessment-empty-tag {
                    color: var(--td-text-color-placeholder) !important;
                    font-style: italic;
                    background: var(--td-bg-color-container-select) !important;
                    border: 1px dashed var(--td-border-level-2-color) !important;

                    :deep(.t-tag__text) {
                      color: var(--td-text-color-placeholder);
                    }
                  }
                }
              }
            }
          }

          .empty-objectives {
            padding: 40px 20px;
            text-align: center;
            background: var(--td-bg-color-container);
            border-radius: 12px;
            border: 2px dashed var(--td-border-level-2-color);

            .empty-content {
              display: flex;
              flex-direction: column;
              align-items: center;
              gap: 12px;

              .empty-icon {
                color: var(--td-text-color-placeholder);
              }

              .empty-title {
                margin: 0;
                font-size: 16px;
                font-weight: 500;
                color: var(--td-text-color-secondary);
              }

              .empty-subtitle {
                margin: 0;
                font-size: 14px;
                color: var(--td-text-color-placeholder);
              }
            }
          }
        }
      }
    }
  }

  // 对话框样式
  .add-objective-dialog-content,
  .assessment-dialog-content {
    padding: 16px 0;
  }




  .current-objective-info {
    padding: 16px;
    background: var(--td-bg-color-container);
    border: 1px solid var(--td-border-level-1-color);
    border-radius: 8px;
    margin-bottom: 20px;

    h4 {
      margin: 0 0 8px 0;
      font-size: 16px;
      font-weight: 600;
      color: var(--td-text-color-primary);
    }

    p {
      margin: 0;
      color: var(--td-text-color-secondary);
      font-size: 14px;
      line-height: 1.5;
    }
  }

  // 动画
  @keyframes slideInUp {
    from {
      opacity: 0;
      transform: translateY(30px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    .dashboard-header {
      padding: 16px;
      box-shadow: 0 1px 2px rgba(0, 0, 0, 0.04);

      .header-content {
        .title-info {
          .title-container {
            flex-direction: column;
            align-items: flex-start;
            gap: 8px;

            .dashboard-title {
              font-size: 24px;
            }
          }

          .dashboard-subtitle {
            font-size: 14px;
          }
        }
      }
    }

    .dashboard-content {
      padding: 20px 16px;
    }

    .indicators-grid {
      grid-template-columns: 1fr;
      gap: 16px;

      .indicator-card {
        .indicator-card-inner {
          padding: 20px;

          .indicator-header {
            flex-direction: column;
            align-items: flex-start;
            gap: 12px;

            .indicator-actions {
              width: 100%;

              .add-objective-btn {
                width: 100%;
                justify-content: center;
              }
            }
          }
        }
      }
    }

    .objectives-by-indicator {
      .indicator-objectives-group {
        .group-header {
          flex-direction: column;
          align-items: stretch;
          gap: 12px;

          .group-indicator {
            padding: 12px 16px;
          }

          .group-actions {
            justify-content: center;

            .add-objective-group-btn {
              font-size: 12px;
              padding: 8px 12px;
              width: 100%;
              max-width: 200px;
            }
          }
        }
      }
    }

    .objective-card {
      .objective-card-inner {
        .objective-header {
          flex-direction: column;
          align-items: flex-start;
          gap: 12px;

          .objective-actions {
            width: 100%;
            justify-content: space-between;

            .t-button {
              flex: 1;
              font-size: 11px;
              padding: 8px 4px;
            }
          }
        }

        .assessment-methods {
          flex-direction: column;
          align-items: flex-start;
          gap: 8px;
        }
      }
    }
  }

  // 移动端响应式样式
  @media (max-width: 768px) {
    .dashboard-header {
      .header-content {
        .title-info {
          .title-container {
            flex-direction: column;
            align-items: flex-start;
            gap: 8px;

            .dashboard-title {
              font-size: 20px;
            }
          }
        }
      }
    }

    .graduation-indicators-section,
    .course-objectives-section {
      .section-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 16px;

        .header-right {
          width: 100%;

          .header-buttons {
            flex-direction: column;
            width: 100%;
            gap: 8px;

            .associate-btn,
            .setup-indicators-btn {
              width: 100%;
              justify-content: center;
            }
          }
        }
      }
    }

    .indicators-grid {
      grid-template-columns: 1fr;
    }

    .objectives-empty-guide {
      min-height: 300px;
      padding: 20px 16px;

      .empty-guide-content {
        .empty-guide-steps {
          .guide-step {
            padding: 10px 12px;

            .step-text {
              font-size: 13px;
            }
          }
        }
      }
    }


  }
}
</style>

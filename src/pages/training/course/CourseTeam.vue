<template>
  <div class="course-team-container">
    <!-- 头部信息 -->
    <div class="page-header">
      <div class="header-content">
        <div class="title-info">
          <h1 class="page-title">课程团队管理</h1>
          <p class="page-subtitle">管理课程教学团队成员与职责分配</p>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="page-content">
      <!-- 团队成员管理内容块 -->
      <div class="team-management-section">
        <div class="section-header">
          <div class="header-left">
            <h2 class="section-title">
              <t-icon name="usergroup" class="title-icon" />
              课程团队成员管理
            </h2>
          </div>
          <div class="header-actions">
            <t-button theme="primary" size="large" @click="handleAddMember">
              <template #icon><t-icon name="add" /></template>
              添加团队成员
            </t-button>
          </div>
        </div>

        <div class="content-block">
          <div class="team-management">
          <!-- 团队成员表格 -->
          <t-table
            :data="teamMembers"
            :columns="columns"
            :bordered="true"
            :hover="true"
            :loading="loading"
            row-key="id"
            :row-class-name="getRowClassName"
          >
            <template #avatar="{ row }">
              <t-avatar size="small" :image="row.avatar">{{ row.name.charAt(0) }}</t-avatar>
            </template>

            <!-- 工号列 -->
            <template #teacherNumber="{ row }">
              <span class="teacher-number">{{ row.teacherNumber }}</span>
            </template>

            <!-- 角色列 - 内联编辑 -->
            <template #role="{ row }">
              <div v-if="editingRoleId === row.id" class="role-edit-container">
                <t-select
                  v-model="row.role"
                  :options="memberRoleOptions"
                  placeholder="选择角色"
                  size="small"
                  :disabled="!canEditRole(row)"
                  @blur="handleRoleEditComplete(row)"
                  @change="handleRoleEditComplete(row)"
                  auto-focus
                />
              </div>
              <div v-else class="role-display-container" @click="handleRoleEditStart(row)">
                <t-tag :theme="getRoleTheme(row.role)">{{ row.role }}</t-tag>
                <t-icon name="edit" class="role-edit-icon" v-if="canEditRole(row)" />
              </div>
            </template>

            <!-- 操作列 -->
            <template #operation="{ row }">
              <t-button
                theme="danger"
                variant="text"
                size="small"
                :disabled="!canDeleteMember(row)"
                @click="handleDeleteMember(row)"
              >
                <template #icon><t-icon name="delete" /></template>
                删除
              </t-button>
            </template>
          </t-table>
          </div>
        </div>
      </div>
      
      <!-- 教学任务与职责分配内容块 -->
      <div class="responsibility-section">
        <div class="section-header">
          <div class="header-left">
            <h2 class="section-title">
              <t-icon name="swap" class="title-icon" />
              教学任务与职责分配
            </h2>
            <div class="section-subtitle">管理课程教学任务分工与截止日期</div>
          </div>
          <div class="header-actions">
            <t-space size="small">
              <template v-if="isEditingResponsibilities">
                <t-button theme="primary" size="small" @click="handleSaveResponsibilities">
                  <template #icon><t-icon name="check" /></template>
                  保存
                </t-button>
                <t-button theme="default" size="small" @click="handleCancelEditResponsibilities">
                  <template #icon><t-icon name="close" /></template>
                  取消
                </t-button>
              </template>
              <template v-else>
                <t-button theme="primary" variant="outline" size="small" @click="handleEditResponsibilities">
                  <template #icon><t-icon name="edit" /></template>
                  编辑
                </t-button>
              </template>
              <t-button theme="success" size="small" @click="handleAddTask">
                <template #icon><t-icon name="add" /></template>
                添加任务
              </t-button>
            </t-space>
          </div>
        </div>

        <div class="content-block">
          <div class="responsibility-management">
          <t-table
            :data="responsibilities"
            :columns="responsibilityColumns"
            :bordered="true"
            :hover="true"
            :loading="loading"
            row-key="id"
          >
            <!-- 教学任务列 -->
            <template #task="{ row }">
              <t-input
                v-if="isEditingResponsibilities"
                v-model="row.task"
                placeholder="请输入教学任务"
                size="small"
              />
              <span v-else>{{ row.task }}</span>
            </template>

            <!-- 任务描述列 -->
            <template #description="{ row }">
              <t-textarea
                v-if="isEditingResponsibilities"
                v-model="row.description"
                placeholder="请输入任务描述"
                :autosize="{ minRows: 2, maxRows: 4 }"
              />
              <span v-else>{{ row.description }}</span>
            </template>

            <!-- 责任人列 -->
            <template #assignTo="{ row }">
              <t-select
                v-model="row.assignedTo"
                :options="memberOptions"
                multiple
                placeholder="选择负责人员"
                :disabled="!isEditingResponsibilities"
              />
            </template>

            <!-- 任务时间列 -->
            <template #taskTime="{ row }">
              <div v-if="isEditingResponsibilities">
                <!-- 定时任务：日期范围选择器 -->
                <t-date-range-picker
                  v-if="row.taskType === '定时任务'"
                  :value="[row.startDate, row.endDate]"
                  placeholder="选择时间段"
                  size="small"
                  @change="(value) => handleDateRangeChange(row, value)"
                />
                <!-- 持续任务：单个日期选择器 -->
                <t-date-picker
                  v-else
                  v-model="row.startDate"
                  placeholder="选择开始时间"
                  size="small"
                  @change="updateTaskTime(row)"
                />
              </div>
              <span v-else>{{ row.taskTime }}</span>
            </template>

            <!-- 任务类型列 -->
            <template #taskType="{ row }">
              <t-select
                v-if="isEditingResponsibilities"
                v-model="row.taskType"
                :options="taskTypeOptions"
                placeholder="选择任务类型"
                size="small"
                @change="handleTaskTypeChange(row)"
              />
              <t-tag v-else :theme="getTaskTypeTheme(row.taskType)">{{ row.taskType }}</t-tag>
            </template>

            <!-- 操作列 -->
            <template #operation="{ rowIndex }">
              <t-space size="small" v-if="isEditingResponsibilities">
                <t-button theme="danger" variant="text" size="small" @click="handleDeleteTask(rowIndex)">
                  <template #icon><t-icon name="delete" /></template>
                  删除
                </t-button>
              </t-space>
            </template>
          </t-table>
          </div>
        </div>
      </div>
    </div>

    <!-- 添加团队成员对话框 -->
    <t-dialog
      v-model:visible="showAddMemberDialog"
      header="添加团队成员"
      width="800px"
      :confirm-btn="null"
      :cancel-btn="null"
      @close="handleCancelAddMembers"
    >
      <div class="add-member-dialog">
        <!-- 筛选区域 -->
        <div class="filter-section">
          <t-row :gutter="16">
            <t-col :span="6">
              <t-select
                v-model="searchFilters.department"
                :options="departmentOptions"
                placeholder="选择院系"
                clearable
                @change="handleDepartmentChange"
              />
            </t-col>
            <t-col :span="6">
              <t-input
                v-model="searchFilters.teacherName"
                placeholder="输入教师姓名搜索"
                clearable
                @input="handleSearch"
              >
                <template #prefix-icon>
                  <t-icon name="search" />
                </template>
              </t-input>
            </t-col>
            <t-col :span="12">
              <div class="selection-info">
                <span v-if="selectedTeachers.length > 0" class="selected-count">
                  已选择 {{ selectedTeachers.length }} 名教师
                </span>
                <t-button theme="default" variant="text" size="small" @click="resetAddMemberDialog">
                  重置
                </t-button>
              </div>
            </t-col>
          </t-row>
        </div>

        <!-- 教师列表表格 -->
        <div class="teacher-table-section">
          <t-table
            :data="teacherList"
            :columns="teacherColumns"
            :loading="teacherListLoading"
            :pagination="pagination"
            row-key="id"
            :selected-row-keys="selectedTeachers.map(t => t.id)"
            @select-change="handleSelectionChange"
            @page-change="handlePageChange"
            :bordered="true"
            :hover="true"
            size="medium"
          />
        </div>
      </div>

      <!-- 对话框底部按钮 -->
      <template #footer>
        <div class="dialog-footer">
          <t-space>
            <t-button theme="default" @click="handleCancelAddMembers">
              取消
            </t-button>
            <t-button
              theme="primary"
              :loading="addMemberLoading"
              @click="handleConfirmAddMembers"
            >
              确定添加
            </t-button>
          </t-space>
        </div>
      </template>
    </t-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useRoute } from 'vue-router';
import { MessagePlugin, DialogPlugin } from 'tdesign-vue-next';

const route = useRoute();
const courseId = ref(route.params.id);
const loading = ref(false);

// 编辑状态
const isEditingResponsibilities = ref(false);
const originalResponsibilities = ref<any[]>([]);

// 角色内联编辑状态
const editingRoleId = ref<number | null>(null);

// 从缓存中获取当前登录用户信息
const getCurrentUserFromCache = () => {
  try {
    // 优先从 sessionStorage 获取
    let userInfo = sessionStorage.getItem('currentUser');
    if (!userInfo) {
      // 备选从 localStorage 获取
      userInfo = localStorage.getItem('currentUser');
    }

    if (userInfo) {
      const user = JSON.parse(userInfo);
      return {
        id: user.teacher_id || user.id,
        name: user.teacher_name || user.name,
        teacherNumber: user.teacher_number || user.teacherNumber,
        avatar: user.image || user.avatar || '/api/placeholder/40/40',
        title: user.teacher_title || user.title,
        department: user.academy_name || user.department,
        phone: user.phone,
        gender: user.gender,
        joinTime: new Date().toLocaleDateString()
      };
    }
  } catch (error) {
    console.error('获取用户信息失败:', error);
  }

  // 如果缓存中没有用户信息，返回默认值
  return {
    id: 1001,
    name: '张教授',
    teacherNumber: 'T001001',
    avatar: '/api/placeholder/40/40',
    title: '教授',
    department: '计算机科学与技术学院',
    phone: '13800138000',
    gender: 1,
    joinTime: new Date().toLocaleDateString()
  };
};

// 当前登录用户信息
const currentUser = ref(getCurrentUserFromCache());

// 添加团队成员对话框状态
const showAddMemberDialog = ref(false);
const addMemberLoading = ref(false);

// 筛选条件
const searchFilters = ref({
  department: '',
  teacherName: ''
});

// 教师列表数据
const teacherList = ref<any[]>([]);
const selectedTeachers = ref<any[]>([]);
const teacherListLoading = ref(false);

// 分页配置
const pagination = ref({
  current: 1,
  pageSize: 12,
  total: 0
});

// 院系选项
const departmentOptions = ref([
  { label: '全部院系', value: '' },
  { label: '计算机科学与技术学院', value: 'computer' },
  { label: '软件学院', value: 'software' },
  { label: '信息工程学院', value: 'information' },
  { label: '数学与统计学院', value: 'math' },
  { label: '物理与电子学院', value: 'physics' },
  { label: '化学与材料学院', value: 'chemistry' },
  { label: '生命科学学院', value: 'biology' },
  { label: '外国语学院', value: 'foreign' },
  { label: '经济管理学院', value: 'economics' }
]);

// 表格列配置
const columns = [
  { colKey: 'avatar', title: '头像', width: 80 },
  { colKey: 'name', title: '姓名', width: 120 },
  { colKey: 'teacherNumber', title: '工号', width: 120 },
  { colKey: 'title', title: '职称', width: 100 },
  { colKey: 'department', title: '所属部门', width: 150 },
  { colKey: 'joinTime', title: '加入时间', width: 120 },
  { colKey: 'role', title: '角色', width: 120 },
  { colKey: 'operation', title: '操作', width: 100 }
];

// 职责表格列配置
const responsibilityColumns = computed(() => {
  const baseColumns = [
    { colKey: 'task', title: '教学任务', width: 150 },
    { colKey: 'description', title: '任务描述', width: 250 },
    { colKey: 'assignTo', title: '责任人', width: 200 },
    { colKey: 'taskTime', title: '任务时间', width: 180 },
    { colKey: 'taskType', title: '任务类型', width: 100 }
  ];

  // 只在编辑模式时显示操作列
  if (isEditingResponsibilities.value) {
    baseColumns.push({ colKey: 'operation', title: '操作', width: 100 });
  }

  return baseColumns;
});

// 团队成员数据
const teamMembers = ref([
  {
    id: 1001, // 当前登录用户ID
    name: '张教授',
    teacherNumber: 'T001001',
    avatar: '/api/placeholder/40/40',
    role: '课程负责人',
    title: '教授',
    department: '计算机科学与技术学院',
    joinTime: '2023-09-01',
    isCurrentUser: true
  },
  {
    id: 2,
    name: '李老师',
    teacherNumber: 'T001002',
    avatar: '/api/placeholder/40/40',
    role: '主讲教师',
    title: '副教授',
    department: '计算机科学与技术学院',
    joinTime: '2023-09-01',
    isCurrentUser: false
  },
  {
    id: 3,
    name: '王老师',
    teacherNumber: 'T001003',
    avatar: '/api/placeholder/40/40',
    role: '实验指导',
    title: '讲师',
    department: '计算机科学与技术学院',
    joinTime: '2024-03-01',
    isCurrentUser: false
  },
  {
    id: 4,
    name: '赵助教',
    teacherNumber: 'T001004',
    avatar: '/api/placeholder/40/40',
    role: '助教',
    title: '助教',
    department: '计算机科学与技术学院',
    joinTime: '2024-03-01',
    isCurrentUser: false
  }
]);

// 成员选项
const memberOptions = computed(() => {
  return teamMembers.value.map(member => ({
    label: `${member.name}(${member.role})`,
    value: member.id
  }));
});

// 任务类型选项
const taskTypeOptions = [
  { label: '定时任务', value: '定时任务' },
  { label: '持续任务', value: '持续任务' }
];

// 团队成员角色选项
const memberRoleOptions = [
  { label: '课程负责人', value: '课程负责人' },
  { label: '主讲教师', value: '主讲教师' },
  { label: '实验指导', value: '实验指导' },
  { label: '助教', value: '助教' }
];

// 教师表格列配置
const teacherColumns = [
  { colKey: 'row-select', type: 'multiple' as const, width: 50 },
  { colKey: 'teacherNo', title: '教师工号', width: 120 },
  { colKey: 'name', title: '教师姓名', width: 100 },
  { colKey: 'department', title: '所在院系', width: 150 },
  { colKey: 'title', title: '教师职称', width: 100 },
  { colKey: 'gender', title: '性别', width: 80 }
];

// 模拟教师数据
const mockTeachers = [
  { id: 1, teacherNo: 'T001', name: '张教授', department: '计算机科学与技术学院', title: '教授', gender: '男' },
  { id: 2, teacherNo: 'T002', name: '李副教授', department: '计算机科学与技术学院', title: '副教授', gender: '女' },
  { id: 3, teacherNo: 'T003', name: '王讲师', department: '软件学院', title: '讲师', gender: '男' },
  { id: 4, teacherNo: 'T004', name: '赵助教', department: '软件学院', title: '助教', gender: '女' },
  { id: 5, teacherNo: 'T005', name: '陈教授', department: '信息工程学院', title: '教授', gender: '男' },
  { id: 6, teacherNo: 'T006', name: '刘副教授', department: '信息工程学院', title: '副教授', gender: '女' },
  { id: 7, teacherNo: 'T007', name: '杨讲师', department: '数学与统计学院', title: '讲师', gender: '男' },
  { id: 8, teacherNo: 'T008', name: '周助教', department: '数学与统计学院', title: '助教', gender: '女' },
  { id: 9, teacherNo: 'T009', name: '吴教授', department: '物理与电子学院', title: '教授', gender: '男' },
  { id: 10, teacherNo: 'T010', name: '郑副教授', department: '物理与电子学院', title: '副教授', gender: '女' },
  { id: 11, teacherNo: 'T011', name: '孙讲师', department: '化学与材料学院', title: '讲师', gender: '男' },
  { id: 12, teacherNo: 'T012', name: '马助教', department: '化学与材料学院', title: '助教', gender: '女' },
  { id: 13, teacherNo: 'T013', name: '朱教授', department: '生命科学学院', title: '教授', gender: '男' },
  { id: 14, teacherNo: 'T014', name: '胡副教授', department: '生命科学学院', title: '副教授', gender: '女' },
  { id: 15, teacherNo: 'T015', name: '林讲师', department: '外国语学院', title: '讲师', gender: '男' },
  { id: 16, teacherNo: 'T016', name: '何助教', department: '外国语学院', title: '助教', gender: '女' },
  { id: 17, teacherNo: 'T017', name: '高教授', department: '经济管理学院', title: '教授', gender: '男' },
  { id: 18, teacherNo: 'T018', name: '梁副教授', department: '经济管理学院', title: '副教授', gender: '女' }
];

// 职责分配数据
const responsibilities = ref([
  {
    id: 1,
    task: '教学大纲制定',
    description: '根据培养方案制定教学大纲',
    assignedTo: [1],
    taskTime: '2025-06-01 至 2025-07-15',
    taskType: '定时任务',
    startDate: '2025-06-01',
    endDate: '2025-07-15'
  },
  {
    id: 2,
    task: '教案编写',
    description: '编写课程教案与讲义',
    assignedTo: [1, 2],
    taskTime: '2025-07-01 至 2025-07-30',
    taskType: '定时任务',
    startDate: '2025-07-01',
    endDate: '2025-07-30'
  },
  {
    id: 3,
    task: '实验指导书编写',
    description: '编写实验指导书与实验评分标准',
    assignedTo: [3],
    taskTime: '2025-07-15 至 2025-08-15',
    taskType: '定时任务',
    startDate: '2025-07-15',
    endDate: '2025-08-15'
  },
  {
    id: 4,
    task: '作业批改',
    description: '批改学生作业并进行反馈',
    assignedTo: [2, 4],
    taskTime: '2025-08-01 开始',
    taskType: '持续任务',
    startDate: '2025-08-01',
    endDate: null
  }
]);

// 团队成员管理函数
const handleAddMember = () => {
  showAddMemberDialog.value = true;
  resetAddMemberDialog();
  loadTeacherList();
};

// 权限检查函数
const canEditRole = (member: any) => {
  // 当前用户不能编辑自己的角色（如果自己是课程负责人）
  if (member.isCurrentUser && member.role === '课程负责人') {
    return false;
  }
  return true;
};

const canDeleteMember = (member: any) => {
  // 当前用户不能删除自己
  if (member.isCurrentUser) {
    return false;
  }
  return true;
};

// 角色内联编辑功能
const handleRoleEditStart = (member: any) => {
  // 检查是否可以编辑角色
  if (!canEditRole(member)) {
    if (member.isCurrentUser) {
      MessagePlugin.warning('您不能修改自己的课程负责人角色');
    }
    return;
  }
  editingRoleId.value = member.id;
};

const handleRoleEditComplete = (member: any) => {
  editingRoleId.value = null;
  MessagePlugin.success(`已更新${member.name}的角色为：${member.role}`);
};

// 表格行样式函数
const getRowClassName = ({ row }: { row: any }) => {
  return row.isCurrentUser ? 'current-user-row' : '';
};

// 重置添加成员对话框
const resetAddMemberDialog = () => {
  searchFilters.value = {
    department: '',
    teacherName: ''
  };
  selectedTeachers.value = [];
  pagination.value = {
    current: 1,
    pageSize: 12,
    total: 0
  };
};

// 加载教师列表
const loadTeacherList = () => {
  teacherListLoading.value = true;

  // 模拟API调用
  setTimeout(() => {
    let filteredTeachers = [...mockTeachers];

    // 院系筛选
    if (searchFilters.value.department) {
      filteredTeachers = filteredTeachers.filter(teacher =>
        teacher.department === searchFilters.value.department
      );
    }

    // 姓名搜索
    if (searchFilters.value.teacherName) {
      filteredTeachers = filteredTeachers.filter(teacher =>
        teacher.name.includes(searchFilters.value.teacherName)
      );
    }

    // 排除已存在的团队成员
    const existingMemberNames = teamMembers.value.map(member => member.name);
    filteredTeachers = filteredTeachers.filter(teacher =>
      !existingMemberNames.includes(teacher.name)
    );

    // 分页处理
    pagination.value.total = filteredTeachers.length;
    const start = (pagination.value.current - 1) * pagination.value.pageSize;
    const end = start + pagination.value.pageSize;
    teacherList.value = filteredTeachers.slice(start, end);

    teacherListLoading.value = false;
  }, 500);
};

// 搜索防抖
let searchTimeout: NodeJS.Timeout;
const handleSearch = () => {
  clearTimeout(searchTimeout);
  searchTimeout = setTimeout(() => {
    pagination.value.current = 1;
    loadTeacherList();
  }, 500);
};

// 院系筛选
const handleDepartmentChange = () => {
  pagination.value.current = 1;
  loadTeacherList();
};

// 分页变化
const handlePageChange = (pageInfo: any) => {
  pagination.value.current = pageInfo.current;
  pagination.value.pageSize = pageInfo.pageSize;
  loadTeacherList();
};

// 选择变化
const handleSelectionChange = (value: any[], { selectedRowData }: any) => {
  selectedTeachers.value = selectedRowData;
};

// 确认添加成员
const handleConfirmAddMembers = () => {
  if (selectedTeachers.value.length === 0) {
    MessagePlugin.warning('请选择要添加的教师');
    return;
  }

  addMemberLoading.value = true;

  // 模拟API调用
  setTimeout(() => {
    // 将选中的教师添加到团队成员列表
    selectedTeachers.value.forEach(teacher => {
      const newMember = {
        id: Date.now() + Math.random(),
        name: teacher.name,
        teacherNumber: teacher.teacherNumber || `T${Date.now()}`, // 工号
        avatar: '/api/placeholder/40/40',
        role: '助教', // 默认角色
        title: teacher.title,
        department: teacher.department,
        joinTime: new Date().toLocaleDateString(),
        isCurrentUser: false // 新添加的成员不是当前用户
      };
      teamMembers.value.push(newMember);
    });

    addMemberLoading.value = false;
    showAddMemberDialog.value = false;
    MessagePlugin.success(`成功添加 ${selectedTeachers.value.length} 名团队成员`);
  }, 1000);
};

// 取消添加
const handleCancelAddMembers = () => {
  showAddMemberDialog.value = false;
  selectedTeachers.value = [];
};



const handleDeleteMember = async (member: any) => {
  const confirmResult = await DialogPlugin.confirm({
    header: '确认删除',
    body: `确定要删除团队成员"${member.name}"吗？`,
    theme: 'warning'
  });

  if (confirmResult) {
    const index = teamMembers.value.findIndex(m => m.id === member.id);
    if (index > -1) {
      teamMembers.value.splice(index, 1);
      MessagePlugin.success('删除成功');
    }
  }
};

// 获取角色标签主题
const getRoleTheme = (role: string): 'primary' | 'success' | 'warning' | 'danger' | 'default' => {
  const themeMap: Record<string, 'primary' | 'success' | 'warning' | 'danger' | 'default'> = {
    '课程负责人': 'primary',
    '主讲教师': 'success',
    '实验指导': 'warning',
    '助教': 'default'
  };
  return themeMap[role] || 'default';
};

// 教学任务管理函数
const handleEditResponsibilities = () => {
  originalResponsibilities.value = JSON.parse(JSON.stringify(responsibilities.value));
  isEditingResponsibilities.value = true;
  MessagePlugin.info('进入编辑模式');
};

const handleSaveResponsibilities = () => {
  isEditingResponsibilities.value = false;
  originalResponsibilities.value = [];
  MessagePlugin.success('保存成功');
};

const handleCancelEditResponsibilities = () => {
  responsibilities.value = JSON.parse(JSON.stringify(originalResponsibilities.value));
  isEditingResponsibilities.value = false;
  originalResponsibilities.value = [];
  MessagePlugin.info('已取消编辑');
};

const handleAddTask = () => {
  const newTask = {
    id: Date.now(),
    task: '新教学任务',
    description: '请输入任务描述',
    assignedTo: [] as number[],
    taskTime: '',
    taskType: '定时任务',
    startDate: '',
    endDate: ''
  };
  responsibilities.value.push(newTask);

  if (!isEditingResponsibilities.value) {
    handleEditResponsibilities();
  }

  MessagePlugin.success('已添加新任务');
};

const handleDeleteTask = async (index: number) => {
  const confirmResult = await DialogPlugin.confirm({
    header: '确认删除',
    body: '确定要删除这个教学任务吗？',
    theme: 'warning'
  });

  if (confirmResult) {
    responsibilities.value.splice(index, 1);
    MessagePlugin.success('删除成功');
  }
};

// 获取任务类型标签主题
const getTaskTypeTheme = (taskType: string): 'primary' | 'success' | 'warning' | 'danger' | 'default' => {
  const themeMap: Record<string, 'primary' | 'success' | 'warning' | 'danger' | 'default'> = {
    '定时任务': 'primary',
    '持续任务': 'success'
  };
  return themeMap[taskType] || 'default';
};

// 处理日期范围变化
const handleDateRangeChange = (row: any, value: any) => {
  if (value && Array.isArray(value) && value.length === 2) {
    row.startDate = value[0];
    row.endDate = value[1];
    updateTaskTime(row);
  }
};

// 更新任务时间显示
const updateTaskTime = (row: any) => {
  if (row.taskType === '定时任务') {
    if (row.startDate && row.endDate) {
      row.taskTime = `${row.startDate} 至 ${row.endDate}`;
    } else {
      row.taskTime = '';
    }
  } else {
    if (row.startDate) {
      row.taskTime = `${row.startDate} 开始`;
      row.endDate = null;
    } else {
      row.taskTime = '';
    }
  }
};

// 处理任务类型变化
const handleTaskTypeChange = (row: any) => {
  if (row.taskType === '持续任务') {
    row.endDate = null;
  }
  updateTaskTime(row);
};

// 初始化当前用户为课程负责人
const initializeCurrentUser = () => {
  try {
    // 验证当前用户信息是否完整
    if (!currentUser.value.id || !currentUser.value.name || !currentUser.value.teacherNumber) {
      console.warn('当前用户信息不完整，使用默认设置');
      MessagePlugin.warning('用户信息不完整，请检查登录状态');
      return;
    }

    // 检查当前用户是否已在团队成员列表中
    const existingUser = teamMembers.value.find(member => member.id === currentUser.value.id);

    if (!existingUser) {
      // 如果当前用户不在列表中，添加为课程负责人
      const newMember = {
        id: currentUser.value.id,
        name: currentUser.value.name,
        teacherNumber: currentUser.value.teacherNumber,
        avatar: currentUser.value.avatar,
        role: '课程负责人',
        title: currentUser.value.title,
        department: currentUser.value.department,
        joinTime: currentUser.value.joinTime,
        isCurrentUser: true
      };
      teamMembers.value.unshift(newMember); // 添加到列表开头
      MessagePlugin.success('已自动设置您为课程负责人');
    } else {
      // 如果已存在，确保标记为当前用户
      existingUser.isCurrentUser = true;
      // 确保工号字段存在
      if (!existingUser.teacherNumber) {
        existingUser.teacherNumber = currentUser.value.teacherNumber;
      }
      // 如果不是课程负责人，自动设置为课程负责人
      if (existingUser.role !== '课程负责人') {
        existingUser.role = '课程负责人';
        MessagePlugin.info('已自动设置您为课程负责人');
      }
    }
  } catch (error) {
    console.error('初始化当前用户失败:', error);
    MessagePlugin.error('初始化用户信息失败，请刷新页面重试');
  }
};

onMounted(() => {
  console.log('课程团队管理页面加载，课程ID:', courseId.value);
  initializeCurrentUser();
});
</script>

<style lang="less" scoped>
.course-team-container {
  padding: 20px;
  
  .page-header {
    margin-bottom: 24px;
    
    .header-content {
      .title-info {
        .page-title {
          font-size: 24px;
          font-weight: 600;
          margin: 0 0 8px 0;
        }
        
        .page-subtitle {
          font-size: 14px;
          color: var(--td-text-color-secondary);
          margin: 0;
        }
      }
    }
  }
  
  .page-content {
    background: #ffffff;
    border-radius: 8px;
    padding: 24px;
    margin-top: 16px;

    // 团队成员管理区域
    .team-management-section {
      margin-bottom: 48px;

      .section-header {
        margin-bottom: 24px;
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        gap: 16px;

        .header-left {
          .section-title {
            display: flex;
            align-items: center;
            gap: 12px;
            font-size: 20px;
            font-weight: 600;
            color: var(--td-text-color-primary);
            margin: 0 0 8px 0;

            .title-icon {
              color: var(--td-brand-color);
              font-size: 20px;
            }
          }
        }

        .header-actions {
          display: flex;
          gap: 8px;
          align-items: center;
          flex-shrink: 0;
        }
      }

      .content-block {
        background: var(--td-bg-color-container);
        border: 1px solid var(--td-border-level-1-color);
        border-radius: 12px;
        padding: 24px;
        transition: all 0.3s ease;

        &:hover {
          border-color: var(--td-border-level-2-color);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
        }
      }
    }

    // 教学任务与职责分配区域
    .responsibility-section {
      .section-header {
        margin-bottom: 24px;
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        gap: 16px;

        .header-left {
          .section-title {
            display: flex;
            align-items: center;
            gap: 12px;
            font-size: 20px;
            font-weight: 600;
            color: var(--td-text-color-primary);
            margin: 0 0 8px 0;

            .title-icon {
              color: var(--td-brand-color);
              font-size: 20px;
            }
          }

          .section-subtitle {
            font-size: 14px;
            color: var(--td-text-color-secondary);
            margin: 0;
          }
        }

        .header-actions {
          display: flex;
          gap: 8px;
          align-items: center;
          flex-shrink: 0;
        }
      }

      .content-block {
        background: var(--td-bg-color-container);
        border: 1px solid var(--td-border-level-1-color);
        border-radius: 12px;
        padding: 24px;
        transition: all 0.3s ease;

        &:hover {
          border-color: var(--td-border-level-2-color);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
        }
      }
    }

  }
}

// 响应式设计
@media (max-width: 768px) {
  .course-team-container {
    .page-content {
      padding: 16px;
      margin-top: 12px;

      .team-management-section,
      .responsibility-section {
        .section-header {
          flex-direction: column;
          align-items: stretch;
          gap: 16px;

          .header-left {
            .section-title {
              justify-content: center;
              font-size: 18px;
            }
          }

          .header-actions {
            justify-content: center;
            flex-wrap: wrap;
          }
        }

        .content-block {
          padding: 16px;
        }
      }
    }
  }
}

// 中等屏幕优化
@media (max-width: 1024px) and (min-width: 769px) {
  .course-team-container {
    .page-content {
      .team-management-section,
      .responsibility-section {
        .section-header {
          .header-actions {
            .t-space {
              gap: 6px;
            }

            .t-button {
              font-size: 12px;
              padding: 4px 8px;
            }
          }
        }
      }
    }
  }

  // 添加团队成员对话框样式
  .add-member-dialog {
    .filter-section {
      margin-bottom: 20px;
      padding: 16px;
      background: var(--td-bg-color-container-hover);
      border-radius: var(--td-radius-default);

      .selection-info {
        display: flex;
        justify-content: space-between;
        align-items: center;
        height: 32px;

        .selected-count {
          color: var(--td-brand-color);
          font-weight: 500;
        }
      }
    }

    .teacher-table-section {
      .t-table {
        border-radius: var(--td-radius-default);
      }
    }
  }

  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    padding-top: 16px;
    border-top: 1px solid var(--td-border-level-1-color);
  }

  // 当前用户整行背景色标识
  :deep(.current-user-row) {
    background-color: rgba(var(--td-brand-color-rgb), 0.05) !important;

    &:hover {
      background-color: rgba(var(--td-brand-color-rgb), 0.08) !important;
    }

    td {
      border-color: rgba(var(--td-brand-color-rgb), 0.1);
    }
  }

  // 工号列样式
  .teacher-number {
    font-family: 'Courier New', monospace;
    font-weight: 500;
    color: var(--td-text-color-secondary);
  }

  // 角色内联编辑样式
  .role-edit-container {
    .t-select {
      width: 100%;
    }
  }

  .role-display-container {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    padding: 4px 8px;
    border-radius: 4px;
    transition: background-color 0.2s ease;

    &:hover {
      background-color: var(--td-bg-color-container-hover);

      .role-edit-icon {
        opacity: 1;
      }
    }

    .role-edit-icon {
      opacity: 0;
      font-size: 12px;
      color: var(--td-text-color-placeholder);
      transition: opacity 0.2s ease;
    }
  }
}
</style>

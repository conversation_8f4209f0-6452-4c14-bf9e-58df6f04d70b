<template>
  <div class="teacher-achievement-analysis">
    <!-- 页面标题 -->
    <t-card class="header-card">
      <template #header>
        <div class="card-header">
          <t-icon name="chart-bubble" class="title-icon" />
          <h3 class="t-card__title">课程目标达成度分析</h3>
          <div class="course-info">
            <span class="course-name">{{ courseName }}</span>
          </div>
        </div>
      </template>
    </t-card>

    <!-- 考核环节设置展示 -->
    <t-card class="section-card" :bordered="true">
      <template #header>
        <div class="card-header">
          <h3 class="t-card__title">考核环节与课程目标映射</h3>
          <div class="header-right">
            <t-button theme="primary" @click="handleAnalyzeAll">
              整体达成度分析
            </t-button>
          </div>
        </div>
      </template>
      
      <t-table
        :data="assessmentSections"
        :columns="mappingColumns"
        :bordered="true"
        :loading="loading"
        row-key="id"
        :row-class-name="() => 'mapping-table-row'"
      >
        <!-- 动态课程目标列 -->
        <template v-for="objective in courseObjectives" :key="objective.id" #[`objective_${objective.id}`]="{ row }">
          <div class="objective-mapping-cell" :class="getObjectiveClass(row, objective.id)">
            {{ getObjectiveScore(row, objective.id) }}
          </div>
        </template>
        
        <template #name="{ row }">
          <div class="section-name-cell">
            <span class="section-name">{{ row.name }}</span>
          </div>
        </template>
        
        <template #weight="{ row }">
          <div class="weight-cell">
            <t-tag theme="primary" variant="light">{{ row.weight }}%</t-tag>
          </div>
        </template>
        
        <template #totalScore="{ row }">
          <div class="total-score-cell">
            <t-tag theme="success" variant="light">{{ row.totalScore }}分</t-tag>
          </div>
        </template>
      </t-table>
    </t-card>

    <!-- 达成度指标卡 -->
    <t-card class="stats-card" title="课程目标达成度统计" :bordered="true">
      <div class="stats-container">
        <t-row :gutter="[16, 16]">
          <!-- 学生数统计卡 -->
          <t-col :xs="24" :sm="12" :md="6" :lg="4" :xl="3">
            <t-card class="stat-card" :bordered="true">
              <div class="stat-content">
                <div class="stat-title">学生总数</div>
                <div class="stat-value">{{ totalStudents }}</div>
              </div>
            </t-card>
          </t-col>
          
          <!-- 课程目标达成度统计卡 -->
          <t-col v-for="objective in courseObjectives" :key="objective.id" :xs="24" :sm="12" :md="6" :lg="4" :xl="3">
            <t-card class="stat-card" :bordered="true">
              <div class="stat-content">
                <div class="stat-title">{{ objective.name }}</div>
                <div class="stat-value">
                  <t-progress 
                    :percentage="objective.achievementRate" 
                    :color="getProgressColor(objective.achievementRate)"
                    :label="true" 
                    :stroke-width="8" 
                  />
                </div>
              </div>
            </t-card>
          </t-col>
          
          <!-- 整体达成度统计卡 -->
          <t-col :xs="24" :sm="12" :md="6" :lg="4" :xl="3">
            <t-card class="stat-card" :bordered="true">
              <div class="stat-content">
                <div class="stat-title">整体达成度</div>
                <div class="stat-value">
                  <t-progress 
                    :percentage="overallAchievementRate" 
                    :color="getProgressColor(overallAchievementRate)"
                    :label="true" 
                    :stroke-width="8" 
                  />
                </div>
              </div>
            </t-card>
          </t-col>
        </t-row>
      </div>
    </t-card>

    <!-- 学生成绩表格 -->
    <t-card class="grade-card" :bordered="true">
      <template #header>
        <div class="card-header">
          <span class="t-card__title">学生成绩与达成度</span>
          <div class="header-right">
            <t-input
              v-model="searchKeyword"
              placeholder="搜索学号或姓名"
              clearable
              style="width: 240px; margin-right: 16px;"
            >
              <template #prefix-icon>
                <t-icon name="search" />
              </template>
            </t-input>
            <t-button theme="default" @click="exportData">
              <template #icon><t-icon name="file-excel" /></template>
              导出数据
            </t-button>
          </div>
        </div>
      </template>
      
      <!-- 使用增强的表格结构 -->
      <div class="enhanced-table-container">
        <table class="enhanced-table">
          <colgroup>
            <col style="width: 120px" />
            <col style="width: 100px" />
            <template v-for="section in assessmentSections" :key="`col_${section.id}`">
              <col v-for="obj in section.objectives" :key="`col_${section.id}_${obj.id}`" style="width: 80px" />
            </template>
            <col style="width: 120px" />
          </colgroup>
          
          <thead>
            <tr>
              <th rowspan="2" class="base-header">学号</th>
              <th rowspan="2" class="base-header">姓名</th>
              <th v-for="section in assessmentSections" :key="`header_${section.id}`" :colspan="section.objectives.length" class="section-header">
                {{ section.name }} ({{ section.weight }}%)
              </th>
              <th rowspan="2" class="base-header">整体达成度</th>
            </tr>
            <tr>
              <template v-for="section in assessmentSections" :key="`subheader_${section.id}`">
                <th v-for="obj in section.objectives" :key="`subheader_${section.id}_${obj.id}`" class="objective-header">
                  {{ obj.name }}
                </th>
              </template>
            </tr>
          </thead>
          
          <tbody>
            <tr v-for="row in filteredStudentGrades" :key="row.studentId" class="student-row">
              <td class="student-id">{{ row.studentNumber }}</td>
              <td class="student-name">{{ row.studentName }}</td>
              
              <template v-for="section in assessmentSections" :key="`data_${section.id}`">
                <td v-for="obj in section.objectives" :key="`data_${section.id}_${obj.id}`" class="score-cell">
                  <div class="score-display">
                    <span class="score-value">{{ row[`${section.id}_${obj.id}`] || '-' }}</span>
                    <span class="score-total">/{{ obj.totalScore }}</span>
                  </div>
                </td>
              </template>
              
              <td class="achievement-cell">
                <t-button variant="text" theme="primary" @click="showAchievementDialog(row)" class="achievement-button">
                  <t-tag :theme="getAchievementTheme(row.achievementRate)" class="achievement-tag">
                    {{ row.achievementRate }}% {{ getAchievementLabel(row.achievementRate) }}
                  </t-tag>
                </t-button>
              </td>
            </tr>
          </tbody>
        </table>
        
        <!-- 分页控件 -->
        <div class="pagination-container">
          <t-pagination
            v-model:current="pagination.current"
            v-model:pageSize="pagination.pageSize"
            :total="pagination.total"
            :page-size-options="pagination.pageSizeOptions"
            :show-jumper="pagination.showJumper"
            :show-page-size="pagination.showPageSize"
            @change="onPageChange"
          />
        </div>
      </div>
    </t-card>

    <!-- 学生达成度分析弹窗 -->
    <t-dialog
      v-model:visible="achievementDialogVisible"
      :header="selectedStudent ? `${selectedStudent.studentName} 课程目标达成度分析` : '课程目标达成度分析'"
      width="900px"
      :footer="false"
    >
      <div v-if="selectedStudent" class="achievement-dialog-content">
        <div class="student-info">
          <div class="info-item"><span class="label">学号：</span>{{ selectedStudent.studentNumber }}</div>
          <div class="info-item"><span class="label">姓名：</span>{{ selectedStudent.studentName }}</div>
          <div class="info-item"><span class="label">班级：</span>{{ selectedStudent.className }}</div>
        </div>
        
        <!-- 整体达成度统计卡片 -->
        <div class="overall-achievement-card">
          <div class="overall-value">
            <span class="label">整体达成度：</span>
            <span class="value">{{ selectedStudent.achievementRate }}%</span>
            <t-tag :theme="getAchievementTheme(selectedStudent.achievementRate)" class="status-tag">
              {{ getAchievementLabel(selectedStudent.achievementRate) }}
            </t-tag>
          </div>
        </div>
        
        <div class="achievement-chart" ref="achievementChartRef"></div>
        
        <div class="achievement-details">
          <table class="student-achievement-table">
            <thead>
              <tr>
                <th>课程目标</th>
                <th>总得分/总分</th>
                <th>达成度</th>
                <th>达成状态</th>
                <th>分项得分</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="obj in selectedStudentObjectives" :key="obj.id">
                <td>{{ obj.name }}</td>
                <td>{{ getObjectiveTotalScore(selectedStudent, obj.id) }}</td>
                <td>
                  <t-progress 
                    :percentage="obj.achievementRate" 
                    :color="getProgressColor(obj.achievementRate)"
                    :label="true" 
                    :stroke-width="8" 
                  />
                </td>
                <td>
                  <t-tag :theme="getAchievementTheme(obj.achievementRate)">
                    {{ getAchievementLabel(obj.achievementRate) }}
                  </t-tag>
                </td>
                <td class="section-scores">
                  <div v-for="section in getObjectiveSections(obj.id)" :key="section.id" class="section-score-item">
                    <span class="section-name">{{ section.name }}:</span>
                    <span class="section-score">{{ selectedStudent[`${section.id}_${obj.id}`] || '-' }}/{{ getObjectiveSectionScore(section, obj.id) }}</span>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </t-dialog>

    <!-- 整体达成度分析弹窗 -->
    <t-dialog
      v-model:visible="overallAnalysisVisible"
      header="课程整体达成度分析"
      width="900px"
      :footer="false"
    >
      <div class="overall-analysis-content">
        <!-- 统计卡片 -->
        <div class="stats-cards">
          <t-row :gutter="[16, 16]">
            <t-col :span="4" v-for="(stat, index) in overallStats" :key="index">
              <t-card :bordered="true" class="stat-card-modern">
                <div class="stat-content-modern">
                  <div class="stat-title-modern">{{ stat.title }}</div>
                  <div class="stat-value-modern">{{ stat.value }}</div>
                  <div class="stat-icon-modern">
                    <t-icon :name="stat.icon" />
                  </div>
                </div>
              </t-card>
            </t-col>
          </t-row>
        </div>
        
        <div class="overall-chart" ref="overallChartRef"></div>
        
        <div class="analysis-table">
          <table class="student-achievement-table">
            <thead>
              <tr>
                <th>课程目标</th>
                <th>目标描述</th>
                <th>达成度</th>
                <th>达成状态</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="obj in courseObjectives" :key="obj.id">
                <td>{{ obj.name }}</td>
                <td>{{ obj.description }}</td>
                <td>
                  <t-progress 
                    :percentage="obj.achievementRate" 
                    :color="getProgressColor(obj.achievementRate)"
                    :label="true" 
                    :stroke-width="8" 
                  />
                </td>
                <td>
                  <t-tag :theme="getAchievementTheme(obj.achievementRate)">
                    {{ getAchievementLabel(obj.achievementRate) }}
                  </t-tag>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </t-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, nextTick } from 'vue'
import { useRoute } from 'vue-router'
import { MessagePlugin } from 'tdesign-vue-next'
import type { PrimaryTableCol, TableRowData, TdPaginationProps } from 'tdesign-vue-next'
// 使用常规导入而不是动态导入
import * as echarts from 'echarts/core'
import { BarChart, LineChart } from 'echarts/charts'
import {
  TitleComponent,
  TooltipComponent,
  GridComponent,
  LegendComponent,
  ToolboxComponent
} from 'echarts/components'
import { CanvasRenderer } from 'echarts/renderers'

// 注册必要的组件
echarts.use([
  TitleComponent,
  TooltipComponent,
  GridComponent,
  LegendComponent,
  ToolboxComponent,
  BarChart,
  LineChart,
  CanvasRenderer
])

// 路由参数
const route = useRoute()
const courseId = computed(() => route.params.courseId as string)
const courseName = ref('数据结构与算法')

// 页面状态
const loading = ref(false)
const searchKeyword = ref('')
const pagination = ref<TdPaginationProps>({
  current: 1,
  pageSize: 10,
  total: 0,
  showJumper: true,
  showPageSize: true,
  pageSizeOptions: [10, 20, 50]
})

// 弹窗状态
const achievementDialogVisible = ref(false)
const overallAnalysisVisible = ref(false)
const selectedStudent = ref<any>(null)
const achievementChartRef = ref<HTMLElement | null>(null)
const overallChartRef = ref<HTMLElement | null>(null)

// 课程目标数据
const courseObjectives = ref([
  { id: 'obj1', name: '课程目标1', description: '掌握基本理论与概念', achievementRate: 85 },
  { id: 'obj2', name: '课程目标2', description: '应用理论解决实际问题', achievementRate: 78 },
  { id: 'obj3', name: '课程目标3', description: '具备分析与设计能力', achievementRate: 92 },
  { id: 'obj4', name: '课程目标4', description: '培养创新思维与团队协作', achievementRate: 80 }
])

// 计算整体达成度
const overallAchievementRate = computed(() => {
  if (courseObjectives.value.length === 0) return 0
  const sum = courseObjectives.value.reduce((acc, obj) => acc + obj.achievementRate, 0)
  return Math.round(sum / courseObjectives.value.length)
})

// 考核环节数据
const assessmentSections = ref([
  {
    id: 'section1',
    name: '期末考试',
    weight: 40,
    totalScore: 100,
    description: '闭卷考试，考察基础理论和综合应用',
    objectives: [
      { id: 'obj1', name: '课程目标1', weight: 50, totalScore: 50 },
      { id: 'obj2', name: '课程目标2', weight: 30, totalScore: 30 },
      { id: 'obj3', name: '课程目标3', weight: 20, totalScore: 20 }
    ]
  },
  {
    id: 'section2',
    name: '期中考试',
    weight: 30,
    totalScore: 100,
    description: '开卷考试，注重理论应用',
    objectives: [
      { id: 'obj1', name: '课程目标1', weight: 40, totalScore: 40 },
      { id: 'obj2', name: '课程目标2', weight: 40, totalScore: 40 },
      { id: 'obj4', name: '课程目标4', weight: 20, totalScore: 20 }
    ]
  },
  {
    id: 'section3',
    name: '平时作业',
    weight: 20,
    totalScore: 100,
    description: '每周作业，巩固课堂知识',
    objectives: [
      { id: 'obj2', name: '课程目标2', weight: 50, totalScore: 50 },
      { id: 'obj3', name: '课程目标3', weight: 50, totalScore: 50 }
    ]
  },
  {
    id: 'section4',
    name: '课堂表现',
    weight: 10,
    totalScore: 100,
    description: '课堂互动与出勤',
    objectives: [
      { id: 'obj4', name: '课程目标4', weight: 100, totalScore: 100 }
    ]
  }
])

// 获取考核环节对应课程目标的分数
const getObjectiveScore = (section: any, objectiveId: string) => {
  const objective = section.objectives.find((obj: any) => obj.id === objectiveId)
  if (!objective) return '-'
  return `${objective.totalScore}分 (${objective.weight}%)`
}

// 学生成绩数据
const studentGrades = ref<any[]>([])
const totalStudents = ref(0)

// 生成考核环节与课程目标映射表格列
const mappingColumns = computed<PrimaryTableCol<TableRowData>[]>(() => {
  // 基础列
  const baseColumns: PrimaryTableCol<TableRowData>[] = [
    { colKey: 'name', title: '考核环节', width: 120 },
    { colKey: 'weight', title: '环节权重(%)', width: 100 }
  ]
  
  // 课程目标列
  const objectiveColumns: PrimaryTableCol<TableRowData>[] = courseObjectives.value.map(obj => ({
    colKey: `objective_${obj.id}`,
    title: obj.name,
    width: 120
  }))
  
  // 环节总分和说明列
  const additionalColumns: PrimaryTableCol<TableRowData>[] = [
    { colKey: 'totalScore', title: '环节总分', width: 100 },
    { colKey: 'description', title: '说明', minWidth: 200 }
  ]
  
  return [...baseColumns, ...objectiveColumns, ...additionalColumns]
})

// 动态生成学生成绩表格列
const gradeColumns = computed<PrimaryTableCol<TableRowData>[]>(() => {
  // 基础列
  const baseColumns: PrimaryTableCol<TableRowData>[] = [
    { colKey: 'studentNumber', title: '学号', width: 120, fixed: 'left' as 'left' },
    { colKey: 'studentName', title: '姓名', width: 100, fixed: 'left' as 'left' }
  ]
  
  // 考核环节列
  const sectionCols: PrimaryTableCol<TableRowData>[] = assessmentSections.value.map(section => ({
    colKey: `section_${section.id}`,
    title: section.name,
    width: section.objectives.length * 80,
    className: 'section-column'
  }))
  
  // 达成度列
  const achievementCol: PrimaryTableCol<TableRowData> = {
    colKey: 'achievement',
    title: '达成度',
    width: 100,
    className: 'achievement-column'
  }
  
  return [...baseColumns, ...sectionCols, achievementCol]
})

// 学生目标详情列
const objectiveDetailColumns: PrimaryTableCol<TableRowData>[] = [
  { colKey: 'name', title: '课程目标', width: 120 },
  { colKey: 'description', title: '目标描述', width: 240 },
  { colKey: 'achievementRate', title: '达成度(%)', width: 100 }
]

// 整体分析列
const overallAnalysisColumns: PrimaryTableCol<TableRowData>[] = [
  { colKey: 'name', title: '课程目标', width: 120 },
  { colKey: 'description', title: '目标描述', width: 240 },
  { colKey: 'achievementRate', title: '达成度', width: 200 }
]

// 整体统计数据
const overallStats = ref([
  { title: '平均达成度', value: '83.75%', icon: 'chart-bubble' },
  { title: '最高达成度', value: '92%', icon: 'arrow-up' },
  { title: '最低达成度', value: '78%', icon: 'arrow-down' }
])

// 选中学生的课程目标详情
const selectedStudentObjectives = computed(() => {
  if (!selectedStudent.value) return []
  
  return courseObjectives.value.map(obj => ({
    ...obj,
    achievementRate: selectedStudent.value[`achievement_${obj.id}`] || 0
  }))
})

// 获取课程目标总得分/总分
const getObjectiveTotalScore = (student: any, objectiveId: string) => {
  let totalScore = 0
  let maxScore = 0
  
  assessmentSections.value.forEach(section => {
    const objective = section.objectives.find(obj => obj.id === objectiveId)
    if (objective) {
      const score = student[`${section.id}_${objectiveId}`] || 0
      totalScore += score
      maxScore += objective.totalScore
    }
  })
  
  return `${totalScore}/${maxScore}`
}

// 获取考核环节中特定课程目标的总分
const getObjectiveSectionScore = (section: any, objectiveId: string) => {
  const objective = section.objectives.find((obj: {id: string}) => obj.id === objectiveId)
  return objective ? objective.totalScore : 0
}

// 获取课程目标关联的考核环节
const getObjectiveSections = (objectiveId: string) => {
  return assessmentSections.value.filter(section => 
    section.objectives.some((obj: {id: string}) => obj.id === objectiveId)
  )
}

// 过滤后的学生成绩数据
const filteredStudentGrades = computed(() => {
  if (!searchKeyword.value) return studentGrades.value
  
  const keyword = searchKeyword.value.toLowerCase()
  return studentGrades.value.filter((student: any) => 
    student.studentNumber.toLowerCase().includes(keyword) || 
    student.studentName.toLowerCase().includes(keyword)
  )
})

// 获取进度条颜色
const getProgressColor = (value: number) => {
  if (value >= 90) return '#0abb87'
  if (value >= 80) return '#5867dd'
  if (value >= 70) return '#ffb822'
  if (value >= 60) return '#fd7e14'
  return '#f5222d'
}

// 获取达成度标签
const getAchievementLabel = (value: number) => {
  if (value >= 90) return '优秀达成'
  if (value >= 80) return '良好达成'
  if (value >= 70) return '达成'
  if (value >= 60) return '基本达成'
  return '未达成'
}

// 获取达成度主题样式
const getAchievementTheme = (value: number) => {
  if (value >= 90) return 'success'
  if (value >= 80) return 'primary'
  if (value >= 70) return 'warning'
  if (value >= 60) return 'default'
  return 'danger'
}

// 获取课程目标单元格样式
const getObjectiveClass = (section: any, objectiveId: string) => {
  const objective = section.objectives.find((obj: any) => obj.id === objectiveId)
  if (!objective) return 'no-objective'
  
  // 根据权重返回不同的样式类
  if (objective.weight >= 50) return 'high-weight'
  if (objective.weight >= 30) return 'medium-weight'
  return 'low-weight'
}

// 显示学生达成度弹窗
const showAchievementDialog = (student: any) => {
  selectedStudent.value = student
  achievementDialogVisible.value = true
  
  // 等待DOM更新后渲染图表
  nextTick(() => {
    renderStudentAchievementChart()
  })
}

// 显示整体达成度分析
const handleAnalyzeAll = () => {
  overallAnalysisVisible.value = true
  
  // 等待DOM更新后渲染图表
  nextTick(() => {
    renderOverallAnalysisChart()
  })
}

// 分页变化
const onPageChange = (pageInfo: any) => {
  pagination.value.current = pageInfo.current
  pagination.value.pageSize = pageInfo.pageSize
  // 这里可以调用API获取对应页的数据
}

// 导出数据
const exportData = () => {
  MessagePlugin.success('成绩数据导出成功')
}

// 渲染学生达成度图表
const renderStudentAchievementChart = () => {
  if (!achievementChartRef.value || !selectedStudent.value) return
  
  const chartInstance = echarts.init(achievementChartRef.value)
  
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: [
      {
        type: 'category',
        data: courseObjectives.value.map(obj => obj.name),
        axisTick: {
          alignWithLabel: true
        }
      }
    ],
    yAxis: [
      {
        type: 'value',
        name: '达成度(%)',
        max: 100
      }
    ],
    series: [
      {
        name: '达成度',
        type: 'bar',
        barWidth: '60%',
        data: courseObjectives.value.map(obj => ({
          value: selectedStudent.value[`achievement_${obj.id}`] || 0,
          itemStyle: {
            color: getProgressColor(selectedStudent.value[`achievement_${obj.id}`] || 0)
          }
        }))
      }
    ]
  }
  
  chartInstance.setOption(option)
}

// 渲染整体达成度分析图表
const renderOverallAnalysisChart = () => {
  if (!overallChartRef.value) return
  
  const chartInstance = echarts.init(overallChartRef.value)
  
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    legend: {
      data: ['达成度', '预期目标']
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: [
      {
        type: 'category',
        data: courseObjectives.value.map(obj => obj.name),
        axisTick: {
          alignWithLabel: true
        }
      }
    ],
    yAxis: [
      {
        type: 'value',
        name: '达成度(%)',
        max: 100
      }
    ],
    series: [
      {
        name: '达成度',
        type: 'bar',
        barWidth: '40%',
        data: courseObjectives.value.map(obj => ({
          value: obj.achievementRate,
          itemStyle: {
            color: getProgressColor(obj.achievementRate)
          }
        }))
      },
      {
        name: '预期目标',
        type: 'line',
        symbol: 'circle',
        symbolSize: 8,
        itemStyle: {
          color: '#5e72e4'
        },
        data: courseObjectives.value.map(() => 80)
      }
    ]
  }
  
  chartInstance.setOption(option)
}

// 生成模拟学生数据
const generateMockStudentData = () => {
  const students = []
  totalStudents.value = 45
  
  for (let i = 1; i <= totalStudents.value; i++) {
    const studentId = `ST${String(i).padStart(4, '0')}`
    const student: any = {
      studentId,
      studentNumber: studentId,
      studentName: `学生${i}`,
      className: i <= 25 ? '软工231班' : '软工232班',
      achievementRate: Math.floor(60 + Math.random() * 35)
    }
    
    // 为每个考核环节的每个目标生成成绩
    assessmentSections.value.forEach(section => {
      section.objectives.forEach(obj => {
        const scoreKey = `${section.id}_${obj.id}`
        const maxScore = obj.totalScore
        student[scoreKey] = Math.floor(Math.random() * (maxScore * 0.4) + maxScore * 0.6)
      })
    })
    
    // 为每个课程目标生成达成度
    courseObjectives.value.forEach(obj => {
      student[`achievement_${obj.id}`] = Math.floor(60 + Math.random() * 35)
    })
    
    students.push(student)
  }
  
  studentGrades.value = students
  pagination.value.total = students.length
}

// 页面加载时获取数据
onMounted(() => {
  loading.value = true
  
  // 模拟API请求延迟
  setTimeout(() => {
    generateMockStudentData()
    loading.value = false
  }, 800)
})
</script>

<style lang="less" scoped>
.teacher-achievement-analysis {
  padding: 24px;
  
  .card-header {
    display: flex;
    align-items: center;
    flex-direction: row;
    width: 100%;
    .title-icon {
      margin-right: 8px;
      font-size: 20px;
    }
    
    .course-info {
      margin-left: 16px;
      
      .course-name {
        font-size: 16px;
        font-weight: 500;
        color: var(--td-brand-color);
        background-color: var(--td-brand-color-light);
        padding: 2px 8px;
        border-radius: 4px;
      }
    }
    
    .header-right {
      margin-left: auto;
      display: flex;
      align-items: center;
    }
  }
  
  .section-card,
  .stats-card,
  .grade-card {
    margin-bottom: 24px;
  }
  
  .stats-container {
    margin-bottom: 16px;
    
    .stat-card {
      height: 100%;
      
      .stat-content {
        text-align: center;
        padding: 8px;
        
        .stat-title {
          font-size: 14px;
          color: var(--td-text-color-secondary);
          margin-bottom: 4px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        
        .stat-value {
          font-size: 20px;
          font-weight: 600;
          color: var(--td-text-color-primary);
        }
      }
      
      &:hover {
        box-shadow: var(--td-shadow-1);
        transform: translateY(-2px);
        transition: all 0.3s ease;
      }
    }
  }
  
  // 考核环节与课程目标映射表格样式
  :deep(.mapping-table-row) {
    &:hover {
      background-color: rgba(0, 0, 0, 0.02);
    }
  }
  
  .section-name-cell {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    
    .section-name {
      font-weight: 500;
      color: var(--td-text-color-primary);
    }
  }
  
  .weight-cell,
  .total-score-cell {
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .objective-mapping-cell {
    text-align: center;
    padding: 4px;
    border-radius: 4px;
    
    &.high-weight {
      background-color: rgba(0, 168, 112, 0.1);
      color: #006a47;
      font-weight: 500;
    }
    
    &.medium-weight {
      background-color: rgba(16, 93, 219, 0.1);
      color: #0052d9;
    }
    
    &.low-weight {
      background-color: rgba(227, 77, 89, 0.05);
      color: #e34d59;
    }
    
    &.no-objective {
      color: var(--td-text-color-placeholder);
    }
  }
  
  // 学生成绩表格样式 - 增强版
  .enhanced-table-container {
    width: 100%;
    overflow-x: auto;
    
    .enhanced-table {
      width: 100%;
      border-collapse: collapse;
      border-spacing: 0;
      font-size: 14px;
      
      th, td {
        padding: 12px 8px;
        border: 1px solid var(--td-component-stroke);
        text-align: center;
      }
      
      thead {
        background-color: var(--td-bg-color-component);
        
        .base-header {
          background-color: var(--td-bg-color-container);
          font-weight: 600;
        }
        
        .section-header {
          background-color: rgba(0, 82, 217, 0.05);
          font-weight: 600;
          color: var(--td-brand-color);
        }
        
        .objective-header {
          background-color: rgba(0, 82, 217, 0.02);
          font-size: 13px;
        }
      }
      
      tbody {
        .student-row {
          &:hover {
            background-color: var(--td-table-row-hover-bg-color);
          }
        }
        
        .student-id, .student-name {
          font-weight: 500;
        }
        
        .score-cell {
          .score-display {
            background-color: var(--td-bg-color-container-hover);
            border-radius: 4px;
            padding: 2px 6px;
            display: inline-block;
            
            .score-value {
              font-weight: 600;
            }
            
            .score-total {
              color: var(--td-text-color-secondary);
              font-size: 12px;
            }
          }
        }
        
        .achievement-cell {
          .achievement-button {
            padding: 0;
            
            .achievement-tag {
              min-width: 90px;
            }
          }
        }
      }
    }
    
    .pagination-container {
      margin-top: 16px;
      display: flex;
      justify-content: flex-end;
    }
  }
  
  .section-objectives {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    
    .objective-score {
      background-color: var(--td-bg-color-container-hover);
      border-radius: 4px;
      padding: 2px 6px;
      font-size: 12px;
      
      .score-value {
        font-weight: 600;
      }
      
      .score-total {
        color: var(--td-text-color-secondary);
      }
    }
  }
  
  .objectives-list {
    display: flex;
    flex-wrap: wrap;
  }
  
  .overall-achievement-card {
    margin-bottom: 16px;
    background-color: var(--td-bg-color-container-hover);
    border-radius: 8px;
    padding: 16px;
    
    .overall-value {
      font-size: 16px;
      font-weight: 600;
      color: var(--td-text-color-primary);
      display: flex;
      align-items: center;
      
      .label {
        margin-right: 8px;
      }
      
      .value {
        margin-right: 8px;
        font-size: 24px;
        color: var(--td-brand-color);
      }
      
      .status-tag {
        margin-left: 8px;
      }
    }
  }
}

.achievement-dialog-content {
  .student-info {
    display: flex;
    margin-bottom: 16px;
    
    .info-item {
      margin-right: 24px;
      
      .label {
        font-weight: 600;
        margin-right: 4px;
      }
    }
  }
  
  .achievement-chart {
    height: 300px;
    margin-bottom: 16px;
  }
  
  .student-achievement-table {
    width: 100%;
    border-collapse: collapse;
    border-spacing: 0;
    
    th, td {
      padding: 12px;
      border: 1px solid var(--td-component-stroke);
      text-align: center;
    }
    
    th {
      background-color: var(--td-bg-color-component);
      font-weight: 600;
    }
    
    td {
      vertical-align: middle;
    }
    
    .section-scores {
      text-align: left;
      
      .section-score-item {
        margin-bottom: 4px;
        
        .section-name {
          font-weight: 500;
          margin-right: 8px;
        }
        
        .section-score {
          color: var(--td-text-color-primary);
        }
      }
    }
  }
}

.overall-analysis-content {
  .stats-cards {
    margin-bottom: 24px;
    
    .stat-card-modern {
      height: 100%;
      
      .stat-content-modern {
        position: relative;
        padding: 16px;
        
        .stat-title-modern {
          font-size: 14px;
          color: var(--td-text-color-secondary);
          margin-bottom: 8px;
        }
        
        .stat-value-modern {
          font-size: 28px;
          font-weight: 700;
          color: var(--td-text-color-primary);
        }
        
        .stat-icon-modern {
          position: absolute;
          top: 16px;
          right: 16px;
          font-size: 32px;
          opacity: 0.15;
          color: var(--td-brand-color);
        }
      }
      
      &:hover {
        box-shadow: var(--td-shadow-1);
        transform: translateY(-2px);
        transition: all 0.3s ease;
      }
    }
  }
  
  .overall-chart {
    height: 300px;
    margin-bottom: 24px;
  }
  
  .student-achievement-table {
    width: 100%;
    border-collapse: collapse;
    border-spacing: 0;
    
    th, td {
      padding: 12px;
      border: 1px solid var(--td-component-stroke);
      text-align: center;
    }
    
    th {
      background-color: var(--td-bg-color-component);
      font-weight: 600;
    }
    
    td {
      vertical-align: middle;
    }
  }
}
</style> 
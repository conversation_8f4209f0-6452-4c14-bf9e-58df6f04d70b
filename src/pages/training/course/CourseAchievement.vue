<template>
  <div class="container">
    <a-card class="general-card" title="课程达成度分析">
      <a-row :gutter="16">
        <a-col :span="24">
          <a-card title="达成度分布概览" :bordered="false">
            <template #extra>
              <a-select 
                v-model="selectedTerm" 
                style="width: 180px"
                placeholder="请选择学期" 
                @change="handleTermChange"
              >
                <a-option 
                  v-for="term in termOptions" 
                  :key="term.value" 
                  :value="term.value"
                >
                  {{ term.label }}
                </a-option>
              </a-select>
            </template>
            <a-row :gutter="16">
              <a-col :span="8">
                <a-statistic 
                  title="整体达成度" 
                  :value="overallAchievement" 
                  :precision="2" 
                  style="text-align: center"
                  :value-style="{ color: '#3f8600' }"
                >
                  <template #suffix>
                    <span>/ 100</span>
                  </template>
                </a-statistic>
                <div id="overall-achievement-chart" style="height: 240px"></div>
              </a-col>
              <a-col :span="16">
                <div id="achievement-chart" style="height: 300px"></div>
              </a-col>
            </a-row>
          </a-card>
        </a-col>
      </a-row>
      
      <a-divider />
      
      <a-row :gutter="16" style="margin-top: 20px">
        <a-col :span="24">
          <a-card title="课程目标达成度详情" :bordered="false">
            <a-table
              :columns="targetColumns"
              :data="targetData"
              :pagination="false"
              :loading="loading"
            >
              <template #target="{ record }">
                <div>{{ record.targetName }}</div>
                <div style="color: #999; font-size: 12px;">{{ record.targetDescription }}</div>
              </template>
              <template #achievement="{ record }">
                <a-progress
                  :percent="record.achievementRate"
                  :stroke-color="{
                    '0%': '#108ee9',
                    '100%': '#87d068',
                  }"
                />
                <span>{{ record.achievementRate }}%</span>
              </template>
              <template #actions="{ record }">
                <a-button type="text" @click="showDetailModal(record)">
                  查看详情
                </a-button>
                <a-button type="text" @click="showAnalysisReport(record)">
                  分析报告
                </a-button>
              </template>
            </a-table>
          </a-card>
        </a-col>
      </a-row>
      
      <a-row :gutter="16" style="margin-top: 20px">
        <a-col :span="24">
          <a-card title="学生达成度分布" :bordered="false">
            <template #extra>
              <div>
                <a-radio-group v-model="distributionType" @change="handleDistributionTypeChange">
                  <a-radio value="gradeDistribution">按分数段</a-radio>
                  <a-radio value="targetDistribution">按目标达成</a-radio>
                </a-radio-group>
              </div>
            </template>
            <div id="student-achievement-distribution" style="height: 360px"></div>
          </a-card>
        </a-col>
      </a-row>
      
      <a-row :gutter="16" style="margin-top: 20px">
        <a-col :span="24">
          <a-card title="历年达成度变化趋势" :bordered="false">
            <div id="trend-chart" style="height: 360px"></div>
          </a-card>
        </a-col>
      </a-row>
    </a-card>
    
    <!-- 详情弹窗 -->
    <a-modal
      v-model:visible="detailModalVisible"
      title="课程目标达成度详情"
      width="800px"
      @cancel="closeDetailModal"
    >
      <template v-if="selectedTarget">
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="课程目标" :span="2">{{ selectedTarget.targetName }}</a-descriptions-item>
          <a-descriptions-item label="目标描述" :span="2">{{ selectedTarget.targetDescription }}</a-descriptions-item>
          <a-descriptions-item label="达成度">{{ selectedTarget.achievementRate }}%</a-descriptions-item>
          <a-descriptions-item label="关联毕业要求" :span="1">{{ selectedTarget.graduationRequirement }}</a-descriptions-item>
        </a-descriptions>
        
        <a-divider />
        
        <a-table
          :columns="assessmentColumns"
          :data="assessmentData"
          :pagination="false"
          size="small"
        ></a-table>
      </template>
    </a-modal>
    
    <!-- 分析报告弹窗 -->
    <a-modal
      v-model:visible="analysisModalVisible"
      title="达成度分析报告"
      width="900px"
      @cancel="closeAnalysisModal"
    >
      <template v-if="selectedTarget">
        <a-card title="分析概要" :bordered="false">
          <a-alert :type="getAlertType(selectedTarget.achievementRate)" show-icon style="margin-bottom: 16px;">
            <template #message>
              <span>
                目标"{{ selectedTarget.targetName }}"达成度为{{ selectedTarget.achievementRate }}%，
                {{ getAchievementComment(selectedTarget.achievementRate) }}
              </span>
            </template>
          </a-alert>
          
          <p>{{ generateAnalysisText(selectedTarget) }}</p>
        </a-card>
        
        <a-card title="改进建议" :bordered="false" style="margin-top: 16px">
          <a-timeline>
            <a-timeline-item v-for="(item, index) in improvementSuggestions" :key="index">
              {{ item }}
            </a-timeline-item>
          </a-timeline>
        </a-card>
      </template>
      
      <template #footer>
        <a-button key="close" @click="closeAnalysisModal">关闭</a-button>
        <a-button key="export" type="primary" @click="exportAnalysisReport">
          <template #icon>
            <icon-download />
          </template>
          导出报告
        </a-button>
      </template>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
  import { ref, reactive, onMounted } from 'vue';
  import { useRoute } from 'vue-router';
  import * as echarts from 'echarts';

  const route = useRoute();
  const courseId = ref(route.query.courseId as string);
  const loading = ref(false);
  const detailModalVisible = ref(false);
  const analysisModalVisible = ref(false);
  const selectedTarget = ref(null);
  
  // 学期选择
  const selectedTerm = ref('2024-2025-2');
  const termOptions = [
    { label: '2024-2025学年第二学期', value: '2024-2025-2' },
    { label: '2024-2025学年第一学期', value: '2024-2025-1' },
    { label: '2023-2024学年第二学期', value: '2023-2024-2' },
    { label: '2023-2024学年第一学期', value: '2023-2024-1' },
  ];
  
  // 达成度类型选择
  const distributionType = ref('gradeDistribution');
  
  // 整体达成度
  const overallAchievement = ref(82.6);
  
  // 表格列定义
  const targetColumns = [
    {
      title: '序号',
      dataIndex: 'id',
      width: 80,
    },
    {
      title: '课程目标',
      dataIndex: 'target',
      slotName: 'target',
    },
    {
      title: '达成度',
      dataIndex: 'achievement',
      slotName: 'achievement',
      width: 200,
    },
    {
      title: '操作',
      slotName: 'actions',
      width: 160,
      align: 'center',
    }
  ];
  
  // 模拟数据 - 课程目标达成度
  const targetData = reactive([
    {
      id: 1,
      targetName: '课程目标1',
      targetDescription: '掌握计算机网络基本原理和概念体系',
      achievementRate: 85,
      graduationRequirement: '工程知识',
    },
    {
      id: 2,
      targetName: '课程目标2',
      targetDescription: '能够运用数学、自然科学、工程基础和专业知识解决计算机网络工程问题',
      achievementRate: 76,
      graduationRequirement: '问题分析',
    },
    {
      id: 3,
      targetName: '课程目标3',
      targetDescription: '具备网络规划、设计和优化能力',
      achievementRate: 91,
      graduationRequirement: '设计/开发解决方案',
    },
    {
      id: 4,
      targetName: '课程目标4',
      targetDescription: '能够基于科学原理并采用科学方法对复杂网络工程问题进行研究',
      achievementRate: 78,
      graduationRequirement: '研究',
    },
  ]);
  
  // 详情弹窗数据
  const assessmentColumns = [
    { title: '评价方式', dataIndex: 'method' },
    { title: '权重', dataIndex: 'weight' },
    { title: '平均分', dataIndex: 'averageScore' },
    { title: '达成度', dataIndex: 'achievementRate' },
    { title: '贡献度', dataIndex: 'contribution' },
  ];
  
  const assessmentData = reactive([
    { method: '期末考试', weight: '40%', averageScore: '82', achievementRate: '82%', contribution: '32.8%' },
    { method: '实验报告', weight: '30%', averageScore: '89', achievementRate: '89%', contribution: '26.7%' },
    { method: '平时作业', weight: '20%', averageScore: '85', achievementRate: '85%', contribution: '17%' },
    { method: '课堂表现', weight: '10%', averageScore: '90', achievementRate: '90%', contribution: '9%' },
  ]);
  
  // 改进建议
  const improvementSuggestions = reactive([
    '加强对学生的理论教学，增加课程案例讲解',
    '优化实验教学内容，提高学生动手能力',
    '调整课程考核方式，增加过程性考核比重',
    '改进教学方法，提高学生学习积极性'
  ]);
  
  // 方法定义
  const handleTermChange = (value) => {
    console.log('选择学期:', value);
    // TODO: 根据选择的学期加载相应的数据
    loadData(value);
  };
  
  const handleDistributionTypeChange = () => {
    renderStudentDistribution();
  };
  
  const showDetailModal = (record) => {
    selectedTarget.value = record;
    detailModalVisible.value = true;
  };
  
  const closeDetailModal = () => {
    detailModalVisible.value = false;
    selectedTarget.value = null;
  };
  
  const showAnalysisReport = (record) => {
    selectedTarget.value = record;
    analysisModalVisible.value = true;
  };
  
  const closeAnalysisModal = () => {
    analysisModalVisible.value = false;
    selectedTarget.value = null;
  };
  
  const getAlertType = (rate) => {
    if (rate >= 85) return 'success';
    if (rate >= 70) return 'info';
    if (rate >= 60) return 'warning';
    return 'error';
  };
  
  const getAchievementComment = (rate) => {
    if (rate >= 85) return '达成情况优秀';
    if (rate >= 70) return '达成情况良好';
    if (rate >= 60) return '达成情况一般，需要改进';
    return '达成情况不佳，需要重点改进';
  };
  
  const generateAnalysisText = (target) => {
    return `通过对课程目标"${target.targetName}"的达成度分析，本学期学生在${target.targetDescription}方面的掌握程度为${target.achievementRate}%。
    相较于上学期的${(target.achievementRate - 5).toFixed(1)}%，有所提升，但仍有改进空间。
    学生在实验环节表现较好，但在理论知识的掌握和应用上有待加强。建议在教学中增加案例分析和实践应用环节，提高学生的理解和运用能力。`;
  };
  
  const exportAnalysisReport = () => {
    // TODO: 实现导出报告功能
    console.log('导出分析报告');
    // Message.success('分析报告导出成功');
  };
  
  const loadData = (term) => {
    loading.value = true;
    // 模拟加载数据
    setTimeout(() => {
      // 更新数据...
      loading.value = false;
      renderCharts();
    }, 500);
  };
  
  // 图表渲染
  const renderCharts = () => {
    renderOverallAchievementChart();
    renderAchievementChart();
    renderStudentDistribution();
    renderTrendChart();
  };
  
  const renderOverallAchievementChart = () => {
    const chartDom = document.getElementById('overall-achievement-chart');
    if (!chartDom) return;
    
    const myChart = echarts.init(chartDom);
    const option = {
      series: [
        {
          type: 'gauge',
          startAngle: 90,
          endAngle: -270,
          pointer: {
            show: false
          },
          progress: {
            show: true,
            overlap: false,
            roundCap: true,
            clip: false,
            itemStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  { offset: 0, color: '#67e0e3' },
                  { offset: 1, color: '#37a2da' }
                ]
              }
            }
          },
          axisLine: {
            lineStyle: {
              width: 20
            }
          },
          splitLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          axisLabel: {
            show: false
          },
          data: [
            {
              value: overallAchievement.value,
              name: '达成度',
              title: {
                offsetCenter: ['0%', '-20%']
              },
              detail: {
                valueAnimation: true,
                offsetCenter: ['0%', '0%']
              }
            }
          ],
          title: {
            fontSize: 14
          },
          detail: {
            width: 50,
            height: 14,
            fontSize: 18,
            color: 'inherit',
            formatter: '{value}%'
          }
        }
      ]
    };
    myChart.setOption(option);
  };
  
  const renderAchievementChart = () => {
    const chartDom = document.getElementById('achievement-chart');
    if (!chartDom) return;
    
    const myChart = echarts.init(chartDom);
    const option = {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        }
      },
      legend: {
        data: ['实际达成度', '期望达成度']
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: targetData.map(item => `目标${item.id}`)
      },
      yAxis: {
        type: 'value',
        max: 100
      },
      series: [
        {
          name: '实际达成度',
          type: 'bar',
          data: targetData.map(item => item.achievementRate),
          itemStyle: {
            color: '#5470c6'
          }
        },
        {
          name: '期望达成度',
          type: 'bar',
          data: [80, 80, 80, 80],
          itemStyle: {
            color: '#91cc75'
          }
        }
      ]
    };
    myChart.setOption(option);
  };
  
  const renderStudentDistribution = () => {
    const chartDom = document.getElementById('student-achievement-distribution');
    if (!chartDom) return;
    
    const myChart = echarts.init(chartDom);
    let option = {};
    
    if (distributionType.value === 'gradeDistribution') {
      option = {
        title: {
          text: '学生成绩分布',
          left: 'center'
        },
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b} : {c} ({d}%)'
        },
        legend: {
          orient: 'vertical',
          left: 'left',
          data: ['优秀(90-100)', '良好(80-89)', '中等(70-79)', '及格(60-69)', '不及格(<60)']
        },
        series: [
          {
            name: '成绩分布',
            type: 'pie',
            radius: '55%',
            center: ['50%', '60%'],
            data: [
              { value: 25, name: '优秀(90-100)' },
              { value: 35, name: '良好(80-89)' },
              { value: 20, name: '中等(70-79)' },
              { value: 15, name: '及格(60-69)' },
              { value: 5, name: '不及格(<60)' }
            ],
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          }
        ]
      };
    } else {
      option = {
        title: {
          text: '课程目标达成度分布',
          subtext: '按目标分布',
          left: 'center'
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        xAxis: {
          type: 'category',
          data: targetData.map(item => `目标${item.id}`)
        },
        yAxis: {
          type: 'value',
          name: '人数'
        },
        legend: {
          data: ['优秀(≥85%)', '良好(70-84%)', '中等(60-69%)', '不及格(<60%)'],
          top: 30
        },
        series: [
          {
            name: '优秀(≥85%)',
            type: 'bar',
            stack: '总量',
            emphasis: {
              focus: 'series'
            },
            data: [15, 12, 18, 10]
          },
          {
            name: '良好(70-84%)',
            type: 'bar',
            stack: '总量',
            emphasis: {
              focus: 'series'
            },
            data: [12, 15, 8, 16]
          },
          {
            name: '中等(60-69%)',
            type: 'bar',
            stack: '总量',
            emphasis: {
              focus: 'series'
            },
            data: [8, 10, 5, 7]
          },
          {
            name: '不及格(<60%)',
            type: 'bar',
            stack: '总量',
            emphasis: {
              focus: 'series'
            },
            data: [2, 3, 1, 4]
          }
        ]
      };
    }
    
    myChart.setOption(option);
  };
  
  const renderTrendChart = () => {
    const chartDom = document.getElementById('trend-chart');
    if (!chartDom) return;
    
    const myChart = echarts.init(chartDom);
    const option = {
      title: {
        text: '近四学期课程目标达成度趋势',
        left: 'center'
      },
      tooltip: {
        trigger: 'axis'
      },
      legend: {
        data: targetData.map(item => `目标${item.id}`),
        top: 30
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: ['2023-2024(1)', '2023-2024(2)', '2024-2025(1)', '2024-2025(2)']
      },
      yAxis: {
        type: 'value',
        min: 60,
        max: 100
      },
      series: targetData.map((item, index) => ({
        name: `目标${item.id}`,
        type: 'line',
        data: [
          70 + Math.floor(Math.random() * 10),
          75 + Math.floor(Math.random() * 10),
          80 + Math.floor(Math.random() * 5),
          item.achievementRate
        ]
      }))
    };
    myChart.setOption(option);
  };
  
  // 生命周期钩子
  onMounted(() => {
    loadData(selectedTerm.value);
  });
</script>

<style scoped>
.container {
  padding: 20px;
}

.general-card {
  margin-bottom: 20px;
}

.achievement-stats {
  display: flex;
  justify-content: space-around;
  margin-bottom: 20px;
}

.achievement-item {
  text-align: center;
  padding: 10px;
}

.achievement-value {
  font-size: 24px;
  font-weight: bold;
  color: #2f88ff;
}

.achievement-label {
  font-size: 14px;
  color: #666;
}
</style>

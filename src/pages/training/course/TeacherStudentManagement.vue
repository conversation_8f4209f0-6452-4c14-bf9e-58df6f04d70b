<template>
  <div class="teacher-student-management">
    <!-- 页面内容 -->
    <div class="page-content">
      <h2>学生管理</h2>
      <p>学生管理功能开发中...</p>
      <p>当前课程ID: {{ courseId }}</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRoute } from 'vue-router'

const route = useRoute()
const courseId = computed(() => route.params.courseId as string)
</script>

<style lang="less" scoped>
.teacher-student-management {
  padding: 24px;

  .page-content {
    background: var(--td-bg-color-container);
    border-radius: 6px;
    padding: 24px;
    box-shadow: var(--td-shadow-1);
  }
}
</style> 
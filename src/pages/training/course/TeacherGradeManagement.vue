<template>
  <div class="teacher-grade-management">
    <!-- 页面内容 -->
    <div class="page-content">
      <div class="page-header">
        <h2 class="page-title">成绩管理</h2>
        <div class="header-actions">
          <t-button theme="primary" @click="handleExportGrades">
            <template #icon>
              <t-icon name="download" />
            </template>
            导出成绩
          </t-button>
        </div>
      </div>

      <!-- 考核环节概览 -->
      <div class="assessment-sections-overview">
        <div class="section-title">
          <h3>考核环节概览</h3>
        </div>
        <t-loading :loading="loading">
          <div v-if="assessmentSections.length === 0" class="empty-state">
            <t-icon name="inbox" size="48px" />
            <p>暂无考核环节配置</p>
          </div>
          
          <div v-else class="section-cards">
            <div
              v-for="section in assessmentSections"
              :key="section.id"
              class="section-card"
              :class="{ active: selectedSectionId === section.id }"
            >
              <div class="card-header">
                <div class="section-info">
                  <h4 class="section-name">{{ section.name }}</h4>
                </div>
                <div class="section-weight">
                  <span class="weight-value">{{ section.weight }}%</span>
                </div>
              </div>
              
              <div class="card-content">
                <div class="section-description">
                  {{ section.description || '暂无描述' }}
                </div>
                
                <!-- 课程目标占比 -->
                <div class="objective-distribution">
                  <h5>课程目标占比</h5>
                  <div class="objective-tags">
                    <t-tag 
                      v-for="objective in section.objectiveDistribution" 
                      :key="objective.objectiveId"
                      :theme="selectedSectionId === section.id ? 'primary' : 'default'"
                      :variant="selectedSectionId === section.id ? 'light' : 'outline'"
                      size="small"
                      class="objective-tag"
                    >
                      {{ objective.name }}
                      <span class="percentage-separator">·</span>
                      <span class="percentage">{{ objective.percentage }}%</span>
                    </t-tag>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </t-loading>
      </div>

      <!-- 数据统计卡片 -->
      <div class="grade-statistics">
        <div class="section-title">
          <h3>成绩统计</h3>
        </div>
        <!-- 第一行统计卡片 -->
        <t-row :gutter="[16, 16]" class="stats-row">
          <t-col :xs="6" :sm="4" :md="3" :lg="2" :xl="2">
            <div class="stat-card">
              <div class="stat-icon">
                <t-icon name="user-circle" />
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ gradeStats.totalStudents || 0 }}</div>
                <div class="stat-label">学生总数</div>
              </div>
            </div>
          </t-col>
          <t-col :xs="6" :sm="4" :md="3" :lg="2" :xl="2">
            <div class="stat-card">
              <div class="stat-icon">
                <t-icon name="chart-bubble" />
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ gradeStats.averageScore || 0 }}</div>
                <div class="stat-label">平均分</div>
              </div>
            </div>
          </t-col>
          <t-col :xs="6" :sm="4" :md="3" :lg="2" :xl="2">            <div class="stat-card">
              <div class="stat-icon">
                <t-icon name="arrow-up" />
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ gradeStats.highestScore || 0 }}</div>
                <div class="stat-label">最高分</div>
              </div>
            </div>
          </t-col>
          <t-col :xs="6" :sm="4" :md="3" :lg="2" :xl="2">            <div class="stat-card">
              <div class="stat-icon">
                <t-icon name="arrow-down" />
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ gradeStats.lowestScore || 0 }}</div>
                <div class="stat-label">最低分</div>
              </div>
            </div>
          </t-col>
          <t-col :xs="6" :sm="4" :md="3" :lg="2" :xl="2">            <div class="stat-card">
              <div class="stat-icon">
                <t-icon name="minus" />
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ gradeStats.medianScore || 0 }}</div>
                <div class="stat-label">中位数</div>
              </div>
            </div>
          </t-col>
      
          <t-col :xs="6" :sm="4" :md="3" :lg="2" :xl="2">            <div class="stat-card">
              <div class="stat-icon excellent-icon">
                <t-icon name="star-filled" />
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ gradeStats.excellentCount || 0 }}</div>
                <div class="stat-label">优秀人数</div>
              </div>
            </div>
          </t-col>
          <t-col :xs="6" :sm="4" :md="3" :lg="2" :xl="2">            <div class="stat-card">
              <div class="stat-icon good-icon">
                <t-icon name="check-circle-filled" />
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ gradeStats.goodCount || 0 }}</div>
                <div class="stat-label">良好人数</div>
              </div>
            </div>
          </t-col>
          <t-col :xs="6" :sm="4" :md="3" :lg="2" :xl="2">            <div class="stat-card">
              <div class="stat-icon average-icon">
                <t-icon name="minus-circle-filled" />
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ gradeStats.averageCount || 0 }}</div>
                <div class="stat-label">中等人数</div>
              </div>
            </div>
          </t-col>
          <t-col :xs="6" :sm="4" :md="3" :lg="2" :xl="2">            <div class="stat-card">
              <div class="stat-icon pass-icon">
                <t-icon name="check" />
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ gradeStats.passCount || 0 }}</div>
                <div class="stat-label">及格人数</div>
              </div>
            </div>
          </t-col>
          <t-col :xs="6" :sm="4" :md="3" :lg="2" :xl="2">            <div class="stat-card">
              <div class="stat-icon fail-icon">
                <t-icon name="close-circle-filled" />
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ gradeStats.failCount || 0 }}</div>
                <div class="stat-label">不及格人数</div>
              </div>
            </div>
          </t-col>
        </t-row>
      </div>

      <!-- 学生成绩表 -->
      <div class="student-grades-table">
        <div class="table-header">
          <div class="table-title">
            <h3>学生成绩表</h3>
          </div>
          <div class="table-actions">
            <t-select
              v-model="selectedClass"
              placeholder="选择班级"
              clearable
              style="width: 200px; margin-right: 16px;"
            >
              <t-option v-for="cls in classList" :key="cls.classId" :value="cls.classId" :label="cls.className" />
            </t-select>
            <t-input
              v-model="searchKeyword"
              placeholder="搜索学号/姓名"
              clearable
              style="width: 200px;"
            >
              <template #prefix-icon>
                <t-icon name="search" />
              </template>
            </t-input>
          </div>
        </div>

        <t-table
          :data="filteredStudentGrades"
          :columns="gradeColumns"
          :loading="loading"
          :pagination="pagination"
          stripe
          hover
          row-key="studentId"
          @page-change="onPageChange"
        >
          <template #scoreLevel="{ row }">
            <t-tag :theme="getScoreLevelTheme(row.scoreLevel)" size="small">
              {{ row.scoreLevel }}
            </t-tag>
          </template>
        </t-table>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, reactive } from 'vue'
import { useRoute } from 'vue-router'
import { MessagePlugin } from 'tdesign-vue-next'
import type { PrimaryTableCol, TableRowData } from 'tdesign-vue-next'

// 定义学生成绩数据类型
interface StudentGrade {
  studentId: string
  studentNumber: string
  studentName: string
  classId: string
  className: string
  totalScore: number
  scoreLevel: string
  [key: string]: any // 用于存储动态的考核环节成绩
}

// 定义考核环节类型
interface AssessmentSection {
  id: string
  name: string
  weight: number
  description: string
  objectiveDistribution: {
    objectiveId: string
    name: string
    percentage: number
  }[]
}

// 定义班级类型
interface ClassInfo {
  classId: string
  className: string
}

// 获取路由参数
const route = useRoute()
const courseId = computed(() => route.params.courseId as string)

// 页面状态
const loading = ref(false)
const searchKeyword = ref('')
const selectedClass = ref('')
const selectedSectionId = ref('')

// 考核环节数据
const assessmentSections = ref<AssessmentSection[]>([])

// 班级列表
const classList = ref<ClassInfo[]>([])

// 成绩统计
const gradeStats = reactive({
  totalStudents: 0,
  averageScore: 0,
  highestScore: 0,
  lowestScore: 0,
  medianScore: 0,
  excellentCount: 0,
  goodCount: 0,
  averageCount: 0,
  passCount: 0,
  failCount: 0
})

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showJumper: true,
  showPageSize: true,
  pageSizeOptions: [10, 20, 50, 100]
})

// 学生成绩数据
const studentGrades = ref<StudentGrade[]>([])

// 表格列配置
const gradeColumns = computed<PrimaryTableCol<TableRowData>[]>(() => {
  const baseColumns: PrimaryTableCol<TableRowData>[] = [
    { colKey: 'studentNumber', title: '学号', width: 120 },
    { colKey: 'studentName', title: '姓名', width: 100 },
    { colKey: 'className', title: '班级', width: 180 }
  ]
  
  // 动态生成考核环节列
  const sectionColumns: PrimaryTableCol<TableRowData>[] = assessmentSections.value.map(section => ({
    colKey: `section_${section.id}`,
    title: `${section.name}(${section.weight}%)`,
    width: 120,
    align: 'center' as 'center'
  }))
  
  // 添加综合得分和成绩等级列
  const finalColumns: PrimaryTableCol<TableRowData>[] = [
    { colKey: 'totalScore', title: '综合得分', width: 100, align: 'center' as 'center' },
    { colKey: 'scoreLevel', title: '成绩等级', width: 100, align: 'center' as 'center' }
  ]
  
  return [...baseColumns, ...sectionColumns, ...finalColumns]
})

// 过滤后的学生成绩数据
const filteredStudentGrades = computed(() => {
  let result = [...studentGrades.value]
  
  // 按班级筛选
  if (selectedClass.value) {
    result = result.filter(student => student.classId === selectedClass.value)
  }
  
  // 按关键词搜索
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    result = result.filter(student => 
      student.studentNumber.toLowerCase().includes(keyword) ||
      student.studentName.toLowerCase().includes(keyword)
    )
  }
  
  return result
})

// 生命周期钩子
onMounted(() => {
  loadData()
})

// 加载数据
const loadData = async () => {
  loading.value = true
  try {
    // 加载考核环节数据
    await loadAssessmentSections()
    
    // 加载班级列表
    await loadClassList()
    
    // 加载学生成绩数据
    await loadStudentGrades()
    
    // 计算成绩统计数据
    calculateGradeStats()
  } catch (error) {
    console.error('加载数据失败:', error)
    MessagePlugin.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

// 加载考核环节数据
const loadAssessmentSections = async () => {
  // 模拟API调用
  await new Promise(resolve => setTimeout(resolve, 500))
  
  // 模拟数据
  assessmentSections.value = [
    {
      id: '1',
      name: '平时作业',
      weight: 30,
      description: '课后练习作业，包含编程题和理论题',
      objectiveDistribution: [
        { objectiveId: 'obj1', name: '课程目标1', percentage: 40 },
        { objectiveId: 'obj2', name: '课程目标2', percentage: 60 }
      ]
    },
    {
      id: '2',
      name: '阶段测验',
      weight: 20,
      description: '期中阶段性测验，检验学习效果',
      objectiveDistribution: [
        { objectiveId: 'obj1', name: '课程目标1', percentage: 50 },
        { objectiveId: 'obj2', name: '课程目标2', percentage: 50 }
      ]
    },
    {
      id: '3',
      name: '期末考试',
      weight: 50,
      description: '期末综合考试',
      objectiveDistribution: [
        { objectiveId: 'obj1', name: '课程目标1', percentage: 30 },
        { objectiveId: 'obj2', name: '课程目标2', percentage: 70 }
      ]
    }
  ]
  
  // 默认选择第一个考核环节
  if (assessmentSections.value.length > 0) {
    selectedSectionId.value = assessmentSections.value[0].id
  }
}

// 加载班级列表
const loadClassList = async () => {
  // 模拟API调用
  await new Promise(resolve => setTimeout(resolve, 300))
  
  // 模拟数据
  classList.value = [
    { classId: 'class_001', className: '计算机科学与技术2022级1班' },
    { classId: 'class_002', className: '计算机科学与技术2022级2班' },
    { classId: 'class_003', className: '软件工程2022级1班' },
    { classId: 'class_004', className: '软件工程2022级2班' }
  ]
}

// 加载学生成绩数据
const loadStudentGrades = async () => {
  // 模拟API调用
  await new Promise(resolve => setTimeout(resolve, 700))
  
  // 模拟数据
  const mockGrades: StudentGrade[] = []
  
  // 生成随机学生数据
  for (let i = 1; i <= 100; i++) {
    const classIndex = Math.floor(i / 25) // 每25个学生一个班级
    const classId = `class_00${classIndex + 1}`
    const className = classList.value[classIndex]?.className || '未知班级'
    
    // 生成各考核环节的成绩
    const section1Score = Math.floor(Math.random() * 30) + 70 // 70-100分
    const section2Score = Math.floor(Math.random() * 40) + 60 // 60-100分
    const section3Score = Math.floor(Math.random() * 50) + 50 // 50-100分
    
    // 计算综合得分
    const totalScore = parseFloat((
      section1Score * (assessmentSections.value[0]?.weight || 30) / 100 +
      section2Score * (assessmentSections.value[1]?.weight || 20) / 100 +
      section3Score * (assessmentSections.value[2]?.weight || 50) / 100
    ).toFixed(1))
    
    // 确定成绩等级
    let scoreLevel = ''
    if (totalScore >= 90) {
      scoreLevel = '优秀'
    } else if (totalScore >= 80) {
      scoreLevel = '良好'
    } else if (totalScore >= 70) {
      scoreLevel = '中等'
    } else if (totalScore >= 60) {
      scoreLevel = '及格'
    } else {
      scoreLevel = '不及格'
    }
    
    // 创建学生成绩对象，包含各考核环节的成绩
    const studentGrade: StudentGrade = {
      studentId: `student_${i.toString().padStart(3, '0')}`,
      studentNumber: `2022${Math.floor(i / 25) + 1}${i.toString().padStart(3, '0')}`,
      studentName: `学生${i}`,
      classId,
      className,
      totalScore,
      scoreLevel
    }
    
    // 添加各考核环节的成绩
    studentGrade[`section_1`] = section1Score
    studentGrade[`section_2`] = section2Score
    studentGrade[`section_3`] = section3Score
    
    mockGrades.push(studentGrade)
  }
  
  studentGrades.value = mockGrades
  pagination.total = mockGrades.length
}

// 计算成绩统计数据
const calculateGradeStats = () => {
  const grades = studentGrades.value
  
  // 学生总数
  gradeStats.totalStudents = grades.length
  
  // 计算平均分
  const totalScores = grades.map(student => student.totalScore)
  gradeStats.averageScore = totalScores.length > 0 
    ? parseFloat((totalScores.reduce((sum, score) => sum + score, 0) / totalScores.length).toFixed(1))
    : 0
  
  // 最高分、最低分
  gradeStats.highestScore = parseFloat(Math.max(...totalScores).toFixed(1))
  gradeStats.lowestScore = parseFloat(Math.min(...totalScores).toFixed(1))
  
  // 中位数
  const sortedScores = [...totalScores].sort((a, b) => a - b)
  const mid = Math.floor(sortedScores.length / 2)
  gradeStats.medianScore = sortedScores.length % 2 === 0
    ? parseFloat(((sortedScores[mid - 1] + sortedScores[mid]) / 2).toFixed(1))
    : parseFloat(sortedScores[mid].toFixed(1))
  
  // 各等级人数
  gradeStats.excellentCount = grades.filter(student => student.scoreLevel === '优秀').length
  gradeStats.goodCount = grades.filter(student => student.scoreLevel === '良好').length
  gradeStats.averageCount = grades.filter(student => student.scoreLevel === '中等').length
  gradeStats.passCount = grades.filter(student => student.scoreLevel === '及格').length
  gradeStats.failCount = grades.filter(student => student.scoreLevel === '不及格').length
}

// 分页变化处理
const onPageChange = (pageInfo: { current: number; pageSize: number }) => {
  pagination.current = pageInfo.current
  pagination.pageSize = pageInfo.pageSize
}

// 导出成绩
const handleExportGrades = () => {
  MessagePlugin.success('成绩导出功能开发中...')
}

// 获取成绩等级对应的主题色
const getScoreLevelTheme = (level: string): 'success' | 'primary' | 'warning' | 'default' | 'danger' => {
  const themes: Record<string, 'success' | 'primary' | 'warning' | 'default' | 'danger'> = {
    '优秀': 'success',
    '良好': 'primary',
    '中等': 'warning',
    '及格': 'default',
    '不及格': 'danger'
  }
  return themes[level] || 'default'
}
</script>

<style lang="less" scoped>
.teacher-grade-management {
  padding: 24px;

  .page-content {
    background: var(--td-bg-color-container);
    border-radius: 6px;
    padding: 24px;
    box-shadow: var(--td-shadow-1);
  }

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;

    .page-title {
      margin: 0;
      font-size: 20px;
      font-weight: 500;
    }
  }

  .section-title {
    margin-bottom: 16px;
    
    h3 {
      font-size: 16px;
      font-weight: 500;
      margin: 0;
      color: var(--td-text-color-primary);
    }
  }

  // 考核环节概览样式
  .assessment-sections-overview {
    margin-bottom: 24px;

    .section-cards {
      display: flex;
      flex-wrap: wrap;
      gap: 16px;
    }

    .section-card {
      flex: 1;
      min-width: 300px;
      max-width: calc(33.33% - 16px);
      background: var(--td-bg-color-container);
      border: 1px solid var(--td-component-stroke);
      border-radius: 6px;
      padding: 16px;
      transition: all 0.3s ease;

      &:hover {
        box-shadow: var(--td-shadow-2);
      }

      &.active {
        border-color: var(--td-brand-color);
        background-color: var(--td-brand-color-light);
      }

      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12px;

        .section-name {
          margin: 0;
          font-size: 16px;
          font-weight: 500;
        }

        .section-weight {
          padding: 2px 8px;
          background-color: var(--td-brand-color-light);
          border-radius: 12px;
          font-size: 14px;
          font-weight: 500;
          color: var(--td-brand-color);
        }
      }

      .section-description {
        margin-bottom: 12px;
        font-size: 14px;
        color: var(--td-text-color-secondary);
      }

      .objective-distribution {
        h5 {
          font-size: 14px;
          font-weight: 500;
          margin: 0 0 8px;
        }

        .objective-tags {
          display: flex;
          flex-wrap: wrap;
          gap: 8px;
        }

        .objective-tag {
          .percentage-separator {
            margin: 0 4px;
            opacity: 0.6;
          }
        }
      }
    }
  }

  // 统计卡片样式
  .grade-statistics {
    margin-bottom: 24px;

    .stats-row {
      margin-bottom: 16px;
      
      &:last-child {
        margin-bottom: 0;
      }
    }

    .stat-card {
      display: flex;
      align-items: center;
      padding: 16px;
      background: var(--td-bg-color-container);
      border: 1px solid var(--td-component-stroke);
      border-radius: 6px;
      height: 100%;
      transition: all 0.3s ease;

      &:hover {
        box-shadow: var(--td-shadow-2);
        transform: translateY(-2px);
      }

      .stat-icon {
        width: 40px;
        height: 40px;
        background: var(--td-brand-color);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 12px;

        :deep(.t-icon) {
          color: white;
          font-size: 20px;
        }
      }

      .excellent-icon {
        background: #0ABF5B;
      }

      .good-icon {
        background: #2172D6;
      }

      .average-icon {
        background: #ED7B2F;
      }

      .pass-icon {
        background: #888;
      }

      .fail-icon {
        background: #E34D59;
      }

      .stat-content {
        .stat-number {
          font-size: 20px;
          font-weight: 600;
          color: var(--td-text-color-primary);
          line-height: 1.2;
        }

        .stat-label {
          font-size: 14px;
          color: var(--td-text-color-secondary);
          margin-top: 4px;
        }
      }
    }
  }

  // 学生成绩表样式
  .student-grades-table {
    .table-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;

      .table-title {
        h3 {
          font-size: 16px;
          font-weight: 500;
          margin: 0;
        }
      }

      .table-actions {
        display: flex;
        align-items: center;
      }
    }
  }

  // 空状态样式
  .empty-state {
    text-align: center;
    padding: 60px 20px;
    color: var(--td-text-color-secondary);

    .t-icon {
      color: var(--td-text-color-placeholder);
      margin-bottom: 16px;
    }

    p {
      margin: 0;
      font-size: 14px;
    }
  }

  @media (max-width: 768px) {
    padding: 16px;

    .page-content {
      padding: 16px;
    }

    .section-card {
      min-width: 100% !important;
      max-width: 100% !important;
    }
  }
}
</style> 
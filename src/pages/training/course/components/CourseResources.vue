<template>
  <div class="resources-section">
    <!-- 资源管理头部 -->
    <div class="resources-header">
      <div class="header-info">
        <h3 class="section-title">
          <t-icon name="folder-open" class="title-icon" />
          教学资源管理
        </h3>
        <p class="section-desc">管理课程相关的教学资源文件</p>
      </div>
      <div class="header-actions">
        <t-button theme="primary" @click="handleUploadResource">
          <template #icon>
            <t-icon name="add" />
          </template>
          上传资源
        </t-button>
      </div>
    </div>

    <!-- 资源列表 -->
    <div class="resources-content">
      <t-table
        :data="resourcesList"
        :columns="resourceColumns"
        :bordered="true"
        :hover="true"
        :loading="loading"
        row-key="id"
        :pagination="resourcesPagination"
      >
        <template #type="{ row }">
          <t-tag :theme="getResourceTypeTheme(row.type)">{{ row.type }}</t-tag>
        </template>
        <template #size="{ row }">
          <span class="file-size">{{ row.size }}</span>
        </template>
        <template #uploadTime="{ row }">
          <span class="upload-time">{{ row.uploadTime }}</span>
        </template>
        <template #operation="{ row }">
          <t-space size="small">
            <t-button theme="primary" variant="text" size="small" @click="previewResource(row)">
              <template #icon>
                <t-icon name="browse" />
              </template>
              预览
            </t-button>
            <t-button theme="success" variant="text" size="small" @click="downloadResource(row)">
              <template #icon>
                <t-icon name="download" />
              </template>
              下载
            </t-button>
            <t-button theme="danger" variant="text" size="small" @click="deleteResource(row)">
              <template #icon>
                <t-icon name="delete" />
              </template>
              删除
            </t-button>
          </t-space>
        </template>
      </t-table>

      <!-- 空状态 -->
      <div v-if="resourcesList.length === 0" class="empty-resources">
        <t-empty description="暂无教学资源">
          <template #action>
            <t-button theme="primary" @click="handleUploadResource">
              <template #icon>
                <t-icon name="add" />
              </template>
              上传第一个资源
            </t-button>
          </template>
        </t-empty>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, defineProps, defineEmits } from 'vue';
import { MessagePlugin } from 'tdesign-vue-next';

// Props
interface Props {
  courseId?: string | number;
  initialResources?: any[];
}

const props = withDefaults(defineProps<Props>(), {
  initialResources: () => []
});

// Emits
const emit = defineEmits<{
  (e: 'upload'): void;
  (e: 'preview', resource: any): void;
  (e: 'download', resource: any): void;
  (e: 'delete', resource: any): void;
}>();

// 资源管理相关状态
const loading = ref(false);
const resourcesPagination = ref({
  current: 1,
  pageSize: 10,
  total: 0
});

// 表格列配置
const resourceColumns = [
  { colKey: 'name', title: '资源名称', width: 200 },
  { colKey: 'type', title: '资源类型', width: 100 },
  { colKey: 'size', title: '文件大小', width: 100 },
  { colKey: 'uploadTime', title: '上传时间', width: 150 },
  { colKey: 'operation', title: '操作', width: 150 }
];

// 资源列表数据
const resourcesList = ref(props.initialResources.length > 0 ? props.initialResources : [
  { id: 1, name: '高等数学教学大纲.pdf', type: '教学大纲', size: '1.2MB', uploadTime: '2025-05-10' },
  { id: 2, name: '第一章课件.pptx', type: '课件', size: '5.8MB', uploadTime: '2025-05-15' },
  { id: 3, name: '习题集.pdf', type: '习题', size: '2.5MB', uploadTime: '2025-05-20' },
  { id: 4, name: '期末复习资料.docx', type: '复习资料', size: '3.1MB', uploadTime: '2025-06-10' }
]);

// 获取资源类型标签主题
const getResourceTypeTheme = (type: string): 'default' | 'success' | 'warning' | 'primary' | 'danger' => {
  const themeMap: Record<string, 'default' | 'success' | 'warning' | 'primary' | 'danger'> = {
    '教学大纲': 'primary',
    '课件': 'success',
    '习题': 'warning',
    '复习资料': 'default'
  };
  return themeMap[type] || 'default';
};

// 资源操作方法
const handleUploadResource = () => {
  console.log('上传资源');
  MessagePlugin.info('上传功能开发中...');
  emit('upload');
};

const previewResource = (resource: any) => {
  console.log('预览资源', resource);
  MessagePlugin.info(`预览资源: ${resource.name}`);
  emit('preview', resource);
};

const downloadResource = (resource: any) => {
  console.log('下载资源', resource);
  MessagePlugin.success(`开始下载: ${resource.name}`);
  emit('download', resource);
};

const deleteResource = (resource: any) => {
  console.log('删除资源', resource);
  MessagePlugin.warning(`确认删除资源: ${resource.name}?`);
  emit('delete', resource);
};
</script>

<style lang="less" scoped>
.resources-section {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);

  .resources-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 24px 24px 16px;
    border-bottom: 1px solid var(--td-border-level-1-color);

    .header-info {
      .section-title {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 18px;
        font-weight: 600;
        margin: 0 0 8px 0;
        color: var(--td-text-color-primary);

        .title-icon {
          color: var(--td-brand-color);
          font-size: 20px;
        }
      }

      .section-desc {
        margin: 0;
        color: var(--td-text-color-secondary);
        font-size: 14px;
      }
    }

    .header-actions {
      flex-shrink: 0;
    }
  }

  .resources-content {
    padding: 24px;

    .file-size {
      color: var(--td-text-color-secondary);
      font-size: 12px;
    }

    .upload-time {
      color: var(--td-text-color-placeholder);
      font-size: 12px;
    }

    .empty-resources {
      padding: 60px 20px;
      text-align: center;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .resources-section {
    .resources-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;
    }
  }
}
</style>

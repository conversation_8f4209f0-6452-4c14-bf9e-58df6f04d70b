<template>
  <div class="ideological-education-card">
    <t-card>
      <template #header>
        <div class="card-header">
          <div class="header-title">
            <t-icon name="heart" class="header-icon" />
            课程思政教学设计
          </div>
          <div class="header-actions">
            <div class="header-info">说明：课程思政教学设计包括教学目标、授课章节、思政融入点等内容</div>
            <t-button v-if="isEditing" theme="primary" size="small" @click="addIdeologicalItem">
              <template #icon><t-icon name="add" /></template>
              添加思政设计
            </t-button>
          </div>
        </div>
      </template>

      <!-- 空状态 -->
      <div v-if="ideologicalData.length === 0" class="empty-state">
        <div class="empty-icon">
          <t-icon name="heart" size="32px" />
        </div>
        <div class="empty-text">暂无课程思政教学设计</div>
        <t-button v-if="isEditing" theme="primary" variant="outline" @click="addIdeologicalItem">
          <template #icon><t-icon name="add" /></template>
          添加第一个思政设计
        </t-button>
      </div>

      <!-- 课程思政表格 -->
      <t-table
        v-else
        :data="ideologicalData"
        :columns="columns"
        :bordered="true"
        :hover="true"
        :stripe="true"
        row-key="id"
        size="medium"
      >
        <!-- 序号列 -->
        <template #index="{ rowIndex }">
          {{ rowIndex + 1 }}
        </template>

        <!-- 课程思政教学目标列 -->
        <template #target="{ row }">
          <div v-if="!isEditing" class="target-display">{{ row?.target || '-' }}</div>
          <t-textarea
            v-else-if="row"
            v-model="row.target"
            placeholder="请输入课程思政教学目标"
            :autosize="{ minRows: 2, maxRows: 4 }"
            @blur="emitUpdate"
          />
          <div v-else>-</div>
        </template>

        <!-- 授课章节列 -->
        <template #chapters="{ row }">
          <div v-if="!isEditing">
            <t-tag v-for="chapter in (row?.chapters || [])" :key="chapter" size="small" class="chapter-tag">
              {{ getChapterName(chapter) }}
            </t-tag>
          </div>
          <t-select
            v-else-if="row"
            v-model="row.chapters"
            placeholder="请选择授课章节"
            multiple
            :options="chapterOptions"
            @change="emitUpdate"
          />
          <div v-else>-</div>
        </template>

        <!-- 课程思政融入点列 -->
        <template #integrationPoint="{ row }">
          <div v-if="!isEditing" class="integration-display">{{ row?.integrationPoint || '-' }}</div>
          <t-textarea
            v-else-if="row"
            v-model="row.integrationPoint"
            placeholder="请输入课程思政融入点"
            :autosize="{ minRows: 3, maxRows: 6 }"
            @blur="emitUpdate"
          />
          <div v-else>-</div>
        </template>

        <!-- 操作列 -->
        <template #operation="{ rowIndex }">
          <t-space v-if="isEditing">
            <t-button
              theme="danger"
              variant="text"
              size="small"
              @click="removeIdeologicalItem(rowIndex)"
            >
              <template #icon><t-icon name="delete" /></template>
              删除
            </t-button>
          </t-space>
        </template>
      </t-table>
    </t-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue'
import { MessagePlugin } from 'tdesign-vue-next'

// Props
interface Props {
  data: any[]
  teachingContents: any[]
  isEditing: boolean
}

const props = withDefaults(defineProps<Props>(), {
  data: () => [],
  teachingContents: () => [],
  isEditing: false
})

// Emits
const emit = defineEmits<{
  update: [data: any[]]
}>()

// 课程思政数据
const ideologicalData = ref<any[]>([])

// 表格列配置
const columns = [
  { colKey: 'index', title: '序号', width: 80 },
  { colKey: 'target', title: '课程思政教学目标', width: 200 },
  { colKey: 'chapters', title: '授课章节', width: 150 },
  { colKey: 'integrationPoint', title: '课程思政融入点', width: 300 },
  { colKey: 'operation', title: '操作', width: 100, fixed: 'right' as const }
]

// 章节选项（基于教学内容）
const chapterOptions = computed(() => {
  return props.teachingContents.map((content, index) => ({
    label: content.title || `第${index + 1}章`,
    value: content.id || `chapter_${index + 1}`
  }))
})

// 获取章节名称
const getChapterName = (chapterId: string) => {
  const chapter = props.teachingContents.find(content => content.id === chapterId)
  return chapter?.title || chapterId
}

// 监听props变化
watch(() => props.data, (newData) => {
  if (Array.isArray(newData)) {
    ideologicalData.value = newData.map(item => ({
      id: item?.id || `ideological_${Date.now()}_${Math.random()}`,
      target: item?.target || '',
      chapters: Array.isArray(item?.chapters) ? item.chapters : [],
      integrationPoint: item?.integrationPoint || ''
    }))
  } else {
    ideologicalData.value = []
  }
}, { immediate: true, deep: true })

// 手动触发更新的方法
const emitUpdate = () => {
  nextTick(() => {
    emit('update', ideologicalData.value)
  })
}

// 添加思政设计
const addIdeologicalItem = () => {
  const newItem = {
    id: `ideological_${Date.now()}_${Math.random()}`,
    target: '',
    chapters: [] as string[],
    integrationPoint: ''
  }
  ideologicalData.value.push(newItem)
  emitUpdate()
}

// 删除思政设计
const removeIdeologicalItem = (index: number) => {
  ideologicalData.value.splice(index, 1)
  emitUpdate()
  MessagePlugin.success('已删除课程思政教学设计')
}
</script>

<style scoped lang="less">
.ideological-education-card {
  margin-bottom: 24px;

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;

    .header-title {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 16px;
      font-weight: 600;
      color: var(--td-text-color-primary);

      .header-icon {
        color: var(--td-error-color);
        font-size: 18px;
      }
    }
    
    .header-actions {
      display: flex;
      gap: 8px;
      align-items: center;
      justify-content: flex-end;
    }
    
    .header-info {
      font-style: italic;
      color: var(--td-text-color-placeholder);
      font-size: 14px;
      margin-right: 8px;
    }
  }

  .empty-state {
    text-align: center;
    padding: 40px 20px;

    .empty-icon {
      margin-bottom: 16px;
      color: var(--td-text-color-placeholder);
    }

    .empty-text {
      margin-bottom: 20px;
      color: var(--td-text-color-secondary);
    }
  }

  .target-display,
  .integration-display {
    line-height: 1.5;
    white-space: pre-line;
  }

  .chapter-tag {
    margin-right: 4px;
    margin-bottom: 4px;
  }
}

@media (max-width: 768px) {
  .ideological-education-card {
    .card-header {
      flex-direction: column;
      gap: 12px;
      align-items: stretch;
      padding: 8px 0;

      .header-title {
        justify-content: center;
      }

      .header-actions {
        justify-content: center;
      }
    }
  }
}
</style>

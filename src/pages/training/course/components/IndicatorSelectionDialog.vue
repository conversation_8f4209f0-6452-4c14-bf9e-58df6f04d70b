<template>
  <t-dialog
    v-model:visible="dialogVisible"
    header="选择毕业要求指标点"
    width="1000px"
    top="10vh"
    :footer="false"
    @close="handleCancel"
  >
    <div class="indicator-selection-dialog-content">
      <!-- 顶部操作区域 -->
      <div class="dialog-header-actions">
        <div class="dialog-description">
          <p>请选择本课程需要支撑的毕业要求指标点：</p>
        </div>
        <div class="dialog-action-buttons">
          <t-button
            theme="primary"
            variant="base"
            size="medium"
            @click="handleConfirm"
            :disabled="selectedIndicatorIds.length === 0"
            class="confirm-btn"
          >
            <template #icon>
              <t-icon name="check" size="16px" />
            </template>
            确认选择 ({{ selectedIndicatorIds.length }})
          </t-button>
          <t-button
            theme="default"
            variant="outline"
            size="medium"
            @click="handleCancel"
            class="cancel-btn"
          >
            取消
          </t-button>
        </div>
      </div>

      <!-- 已选择指标点展示区域 -->
      <div class="selected-indicators-section">
        <div class="selected-indicators-header">
          <h4 class="selected-title">
            <t-icon name="check-circle" class="selected-icon" />
            已选择的指标点
            <span class="selected-count">({{ selectedIndicatorIds.length }})</span>
          </h4>
          <t-button
            v-if="selectedIndicatorIds.length > 0"
            theme="default"
            variant="text"
            size="small"
            @click="clearAllSelections"
            class="clear-all-btn"
          >
            <template #icon>
              <t-icon name="close" size="14px" />
            </template>
            清空全部
          </t-button>
        </div>

        <!-- 已选择指标点卡片列表 -->
        <div v-if="selectedIndicatorIds.length > 0" class="selected-indicators-list">
          <div
            v-for="indicator in selectedIndicatorDetails"
            :key="indicator.id"
            class="selected-indicator-card"
          >
            <div class="indicator-card-content">
              <div class="indicator-card-header">
                <span class="indicator-card-number">GR-{{ indicator.poNumber }}</span>
                <span class="indicator-card-title">{{ indicator.title }}</span>
              </div>
              <div class="indicator-card-description">
                {{ indicator.description }}
              </div>
            </div>
            <t-button
              theme="default"
              variant="text"
              size="small"
              @click="removeSelection(indicator.id)"
              class="remove-btn"
            >
              <template #icon>
                <t-icon name="close" size="16px" />
              </template>
            </t-button>
          </div>
        </div>

        <!-- 空状态提示 -->
        <div v-else class="selected-indicators-empty">
          <t-icon name="info-circle" size="24px" class="empty-hint-icon" />
          <span class="empty-hint-text">暂未选择任何指标点，请在下方列表中选择</span>
        </div>
      </div>

      <!-- 加载状态 -->
      <div v-if="indicatorListLoading" class="dialog-loading">
        <t-loading size="large" text="正在加载指标点列表..." />
      </div>

      <!-- 层级化指标点列表 -->
      <div v-else-if="indicatorsWithUpdatedStatus.length > 0" class="indicator-selection-list">
        <t-checkbox-group v-model="selectedIndicatorIds" class="indicator-checkbox-group">
          <!-- 遍历父级指标 -->
          <div
            v-for="parentIndicator in indicatorsWithUpdatedStatus"
            :key="parentIndicator.id"
            class="parent-indicator-group"
          >
            <!-- 父级指标标题 -->
            <div class="parent-indicator-header">
              <div class="parent-indicator-content">
                <div class="parent-indicator-info">
                  <span class="parent-indicator-number">GR-{{ parentIndicator.poNumber }}</span>
                  <span class="parent-indicator-title">{{ parentIndicator.title }}</span>
                </div>
                <div class="parent-indicator-description">
                  {{ parentIndicator.description }}
                </div>
              </div>
            </div>

            <!-- 子级指标列表 -->
            <div v-if="parentIndicator.children && parentIndicator.children.length > 0" class="child-indicators">
              <div
                v-for="childIndicator in parentIndicator.children"
                :key="childIndicator.id"
                class="child-indicator-item"
                :class="{ 'already-selected': childIndicator.isAlreadySelected }"
              >
                <t-checkbox
                  :value="childIndicator.id"
                  :disabled="childIndicator.isAlreadySelected"
                  class="child-indicator-checkbox"
                >
                  <div class="child-indicator-content">
                    <div class="child-indicator-header">
                      <span class="child-indicator-number">GR-{{ childIndicator.poNumber }}</span>
                      <span class="child-indicator-title">{{ childIndicator.title }}</span>
                      <t-tag
                        v-if="childIndicator.isAlreadySelected"
                        theme="success"
                        size="small"
                        class="selected-tag"
                      >
                        已选择
                      </t-tag>
                    </div>
                    <div class="child-indicator-description">
                      {{ childIndicator.description }}
                    </div>
                  </div>
                </t-checkbox>
              </div>
            </div>

            <!-- 如果父级指标没有子指标的提示 -->
            <div v-else class="no-child-indicators">
              <span class="no-child-text">该指标下暂无子级指标点</span>
            </div>
          </div>
        </t-checkbox-group>
      </div>

      <!-- 空状态 -->
      <div v-else class="dialog-empty-state">
        <t-icon name="info-circle" size="48px" class="empty-icon" />
        <h4>暂无可选择的指标点</h4>
        <p>当前培养方案下没有可用的毕业要求指标点</p>
      </div>
    </div>
  </t-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { MessagePlugin } from 'tdesign-vue-next';
import { getPoTree, type PoVO } from '@/api/training/po';

// PoTreeVO 数据结构定义
interface PoTreeVO {
  id: number;
  poNumber: number;
  poTitle: string;
  poDescription: string;
  isRequirement: boolean;
  children: PoTreeVO[];
  modifyTime: string;
}

// 层级化指标点数据结构
interface HierarchicalIndicator {
  id: string;
  requirementId: string;
  poNumber: string | number; // 可以是数字或字符串(组合编号形式)
  title: string;
  description: string;
  isParent: boolean;
  children?: HierarchicalIndicator[];
  // 是否已被选择（用于过滤显示）
  isAlreadySelected?: boolean;
  // 原始数据
  originalData: any;
}

// 毕业要求指标点接口
interface GraduationIndicator {
  id: string;
  number: string;
  title: string;
  description: string;
  supportLevel: string;
}

// Props 定义
interface Props {
  visible: boolean;
  planId?: number;
  selectedIndicatorIds?: string[];
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  planId: 1,
  selectedIndicatorIds: () => []
});

// Emits 定义
interface Emits {
  (e: 'update:visible', value: boolean): void;
  (e: 'confirm', indicators: GraduationIndicator[]): void;
  (e: 'cancel'): void;
}

const emit = defineEmits<Emits>();

// 响应式数据
const dialogVisible = computed({
  get: () => props.visible,
  set: (value: boolean) => emit('update:visible', value)
});

const availableIndicators = ref<HierarchicalIndicator[]>([]);
const selectedIndicatorIds = ref<string[]>([]);
const indicatorListLoading = ref(false);

// 计算属性：动态更新选择状态的指标点列表
const indicatorsWithUpdatedStatus = computed(() => {
  return availableIndicators.value.map(parent => ({
    ...parent,
    children: parent.children?.map(child => ({
      ...child,
      isAlreadySelected: selectedIndicatorIds.value.includes(child.id.toString())
    }))
  }));
});

// 计算属性：获取已选择指标点的详细信息
const selectedIndicatorDetails = computed(() => {
  if (selectedIndicatorIds.value.length === 0) {
    return [];
  }

  // 从层级化数据中收集所有子级指标
  const allChildIndicators: HierarchicalIndicator[] = [];
  availableIndicators.value.forEach(parent => {
    if (parent.children) {
      allChildIndicators.push(...parent.children);
    }
  });

  // 根据选择的ID筛选指标点详情
  return selectedIndicatorIds.value
    .map(id => allChildIndicators.find(indicator => indicator.id.toString() === id))
    .filter(indicator => indicator !== undefined) as HierarchicalIndicator[];
});

// 辅助函数：获取poNumber的排序值
const getSortableValue = (poNumber: string | number): number => {
  if (poNumber === undefined || poNumber === null) return Infinity; // 无效值放到最后
  
  // 如果已经是数字，直接返回
  if (typeof poNumber === 'number') return poNumber;
  
  // 处理组合编号形式 "父编号-子编号"
  if (typeof poNumber === 'string' && poNumber.includes('-')) {
    const parts = poNumber.split('-');
    // 直接将父编号和子编号转为数字进行排序
    const parentNum = parseInt(parts[0]) || 0;
    const childNum = parseInt(parts[1]) || 0;
    
    // 主要按父编号排序，父编号相同时按子编号排序
    return parentNum * 1000 + childNum; // 通过乘以1000给父编号提供足够权重
  }
  
  // 尝试直接将poNumber转为数字
  const numValue = parseInt(String(poNumber));
  return isNaN(numValue) ? Infinity : numValue;
};



// 将 PoTreeVO 数据转换为组件所需的层级化数据结构
const transformPoTreeToHierarchical = (poTreeData: PoTreeVO[]): HierarchicalIndicator[] => {
  const hierarchicalData: HierarchicalIndicator[] = [];

  poTreeData.forEach(item => {
    if (item.isRequirement) {
      // 这是一个父级毕业要求
      const parentIndicator: HierarchicalIndicator = {
        id: item.id.toString(),
        requirementId: item.id.toString(),
        poNumber: item.poNumber,
        title: item.poTitle,
        description: item.poDescription,
        isParent: true,
        children: [],
        originalData: item
      };

      // 处理子级指标点
      if (item.children && item.children.length > 0) {
        item.children.forEach(child => {
          if (!child.isRequirement) {
            // 这是一个子级指标点
            const childIndicator: HierarchicalIndicator = {
              id: child.id.toString(),
              requirementId: item.id.toString(),
              poNumber: child.poNumber,
              title: child.poTitle,
              description: child.poDescription,
              isParent: false,
              isAlreadySelected: props.selectedIndicatorIds?.includes(child.id.toString()) || false,
              originalData: child
            };
            parentIndicator.children!.push(childIndicator);
          }
        });
      }

      hierarchicalData.push(parentIndicator);
    }
  });

  return hierarchicalData;
};

// 加载指标点数据
const loadIndicatorData = async () => {
  if (!props.planId || props.planId <= 0) {
    console.warn(`无效的planId: ${props.planId}`);
    MessagePlugin.warning('培养方案ID无效');
    return;
  }

  try {
    indicatorListLoading.value = true;
    console.log('使用planId获取指标点树形数据:', props.planId);

    // 使用 getPoTree 获取树形结构的毕业要求数据
    const poTreeResponse = await getPoTree(props.planId);
    console.log('获取到的指标点树形数据:', poTreeResponse);

    // 确保响应数据有效
    if (poTreeResponse?.code === 200 && poTreeResponse?.data && Array.isArray(poTreeResponse.data)) {
      const poTreeData: PoTreeVO[] = poTreeResponse.data;

      // 将 PoTreeVO 数据转换为组件所需的层级化数据结构
      const hierarchicalData = transformPoTreeToHierarchical(poTreeData);

      // 对父级指标按poNumber排序
      hierarchicalData.sort((a, b) => {
        const aNum = getSortableValue(a.poNumber);
        const bNum = getSortableValue(b.poNumber);
        return aNum - bNum;
      });

      // 对每个父级指标内的子指标按poNumber排序
      hierarchicalData.forEach(parent => {
        if (parent.children && parent.children.length > 0) {
          parent.children.sort((a, b) => {
            const aNum = getSortableValue(a.poNumber);
            const bNum = getSortableValue(b.poNumber);
            return aNum - bNum;
          });
        }
      });

      availableIndicators.value = hierarchicalData;

      // 统计总的子指标数量（排除已选择的）
      const totalChildCount = hierarchicalData.reduce((count, parent) =>
        count + (parent.children?.filter(child => !child.isAlreadySelected).length || 0), 0
      );
      const selectedChildCount = hierarchicalData.reduce((count, parent) =>
        count + (parent.children?.filter(child => child.isAlreadySelected).length || 0), 0
      );

      console.log(`成功加载 ${hierarchicalData.length} 个父级指标，${totalChildCount} 个可选子级指标，${selectedChildCount} 个已选择指标`);

      if (totalChildCount === 0) {
        MessagePlugin.info('所有指标点都已被选择');
      }
    } else {
      availableIndicators.value = [];
      const errorMsg = poTreeResponse?.message || '数据格式不正确';
      MessagePlugin.warning(`培养方案(ID: ${props.planId})下未找到可用的毕业要求指标点: ${errorMsg}`);
    }
  } catch (error) {
    console.error('获取指标点列表失败:', error);

    // 提供更详细的错误信息
    const errorMsg = error instanceof Error ? error.message : '未知错误';
    MessagePlugin.error(`获取指标点列表失败: ${errorMsg}`);

    // 清空数据
    availableIndicators.value = [];
  } finally {
    indicatorListLoading.value = false;
  }
};

// 监听对话框显示状态，自动加载数据
watch(() => props.visible, (newVisible) => {
  if (newVisible) {
    // 重置选择状态
    selectedIndicatorIds.value = [...(props.selectedIndicatorIds || [])];
    // 加载指标点数据
    loadIndicatorData();
  }
}, { immediate: false });

// 监听传入的选中状态变化
watch(() => props.selectedIndicatorIds, (newIds) => {
  if (newIds) {
    selectedIndicatorIds.value = [...newIds];
  }
}, { immediate: true });

// 事件处理方法
const handleConfirm = () => {
  if (selectedIndicatorIds.value.length === 0) {
    MessagePlugin.warning('请至少选择一个毕业要求指标点');
    return;
  }

  try {
    // 从层级化数据中收集所有子级指标
    const allChildIndicators: HierarchicalIndicator[] = [];
    availableIndicators.value.forEach(parent => {
      if (parent.children) {
        allChildIndicators.push(...parent.children);
      }
    });

    // 根据选择的ID筛选子级指标点（只有子级指标可以被选择）
    const selectedIndicators = allChildIndicators.filter(indicator =>
      selectedIndicatorIds.value.includes(indicator.id.toString())
    );

    //console.log('选中的子级指标:', selectedIndicators);
    
    // 确保所有选中的ID都有对应的指标点对象
    const missingIds = selectedIndicatorIds.value.filter(id => 
      !selectedIndicators.some(indicator => indicator.id.toString() === id)
    );
    
    if (missingIds.length > 0) {
      console.warn(`有${missingIds.length}个选中的指标点在当前列表中不存在:`, missingIds);
      
      // 可以选择添加占位符或提醒用户
      MessagePlugin.warning(`有${missingIds.length}个选中的指标点在当前列表中不可用，请重新选择`);
      return;
    }

    // 转换为GraduationIndicator格式
    const indicators: GraduationIndicator[] = selectedIndicators.map(indicator => ({
      id: indicator.id.toString(),
      number: String(indicator.poNumber), // 确保是字符串类型，已经是组合编号格式 "父编号-子编号"
      title: indicator.title || '未知指标点',
      description: indicator.description || '暂无描述',
      supportLevel: '中'
    }));

    // 发送确认事件
    emit('confirm', indicators);
    
    //console.log('设置的指标点:', indicators);
  } catch (error) {
    console.error('设置指标点失败:', error);
    MessagePlugin.error('设置指标点失败');
  }
};

const handleCancel = () => {
  selectedIndicatorIds.value = [];
  emit('cancel');
};

// 移除单个选择
const removeSelection = (indicatorId: string) => {
  const index = selectedIndicatorIds.value.indexOf(indicatorId);
  if (index > -1) {
    selectedIndicatorIds.value.splice(index, 1);
  }
};

// 清空所有选择
const clearAllSelections = () => {
  selectedIndicatorIds.value = [];
};
</script>

<style lang="less" scoped>
.indicator-selection-dialog-content {
  padding: 16px 0;

  // 顶部操作区域
  .dialog-header-actions {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 1px solid var(--td-border-level-1-color);

    .dialog-description {
      flex: 1;
      margin-right: 20px;

      p {
        margin: 0;
        color: var(--td-text-color-secondary);
        font-size: 14px;
        line-height: 1.5;
      }
    }

    .dialog-action-buttons {
      display: flex;
      gap: 12px;
      align-items: center;
      flex-shrink: 0;

      .confirm-btn {
        font-weight: 600;
        min-width: 140px;
        border-radius: 8px;
        transition: all 0.3s ease;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

        &:hover:not(:disabled) {
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        &:disabled {
          opacity: 0.6;
          cursor: not-allowed;
          transform: none;
          box-shadow: none;
        }
      }

      .cancel-btn {
        min-width: 80px;
        border-radius: 8px;
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-1px);
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
      }
    }
  }

  // 已选择指标点展示区域
  .selected-indicators-section {
    margin-bottom: 24px;
    padding: 16px;
    background: var(--td-bg-color-container);
    border: 1px solid var(--td-border-level-1-color);
    border-radius: 8px;

    .selected-indicators-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;

      .selected-title {
        display: flex;
        align-items: center;
        gap: 8px;
        margin: 0;
        font-size: 16px;
        font-weight: 600;
        color: var(--td-text-color-primary);

        .selected-icon {
          color: var(--td-success-color);
        }

        .selected-count {
          color: var(--td-brand-color);
          font-weight: 700;
        }
      }

      .clear-all-btn {
        color: var(--td-error-color);

        &:hover {
          color: var(--td-error-color-hover);
          background: var(--td-error-color-1);
        }
      }
    }

    .selected-indicators-list {
      display: flex;
      flex-wrap: wrap;
      gap: 12px;
      max-height: 200px;
      overflow-y: auto;

      .selected-indicator-card {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 8px 12px;
        background: var(--td-brand-color-1);
        border: 1px solid var(--td-brand-color-3);
        border-radius: 6px;
        transition: all 0.3s ease;
        min-width: 0;
        flex: 0 0 auto;

        &:hover {
          background: var(--td-brand-color-2);
          border-color: var(--td-brand-color-4);
          transform: translateY(-1px);
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .indicator-card-content {
          flex: 1;
          min-width: 0;

          .indicator-card-header {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 4px;

            .indicator-card-number {
              background: var(--td-brand-color);
              color: white;
              padding: 2px 6px;
              border-radius: 3px;
              font-size: 11px;
              font-weight: 600;
              flex-shrink: 0;
            }

            .indicator-card-title {
              font-size: 13px;
              font-weight: 500;
              color: var(--td-text-color-primary);
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }
          }

          .indicator-card-description {
            font-size: 11px;
            color: var(--td-text-color-secondary);
            line-height: 1.4;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            max-width: 200px;
          }
        }

        .remove-btn {
          flex-shrink: 0;
          color: var(--td-text-color-secondary);
          padding: 2px;

          &:hover {
            color: var(--td-error-color);
            background: var(--td-error-color-1);
          }
        }
      }
    }

    .selected-indicators-empty {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
      padding: 20px;
      text-align: center;

      .empty-hint-icon {
        color: var(--td-text-color-placeholder);
      }

      .empty-hint-text {
        color: var(--td-text-color-placeholder);
        font-size: 14px;
        font-style: italic;
      }
    }
  }

  .dialog-loading {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 200px;
  }

  .indicator-selection-list {
    max-height: 500px;
    overflow-y: auto;

    .indicator-checkbox-group {
      display: flex;
      flex-direction: column;
      gap: 20px;

      // 父级指标组样式
      .parent-indicator-group {
        border: 1px solid var(--td-border-level-1-color);
        border-radius: 12px;
        overflow: hidden;
        background: var(--td-bg-color-container);

        // 父级指标头部
        .parent-indicator-header {
          background: var(--td-bg-color-container-select);
          padding: 16px 20px;
          border-bottom: 1px solid var(--td-border-level-1-color);

          .parent-indicator-content {
            .parent-indicator-info {
              display: flex;
              align-items: center;
              gap: 12px;
              margin-bottom: 8px;

              .parent-indicator-number {
                background: var(--td-brand-color);
                color: white;
                padding: 6px 12px;
                border-radius: 6px;
                font-size: 14px;
                font-weight: 700;
                flex-shrink: 0;
              }

              .parent-indicator-title {
                font-size: 18px;
                font-weight: 700;
                color: var(--td-text-color-primary);
                flex: 1;
              }
            }

            .parent-indicator-description {
              color: var(--td-text-color-secondary);
              font-size: 14px;
              line-height: 1.6;
              margin-left: 44px;
            }
          }
        }

        // 子级指标容器
        .child-indicators {
          padding: 12px 0;

          .child-indicator-item {
            padding: 12px 20px;
            margin-left: 20px;
            border-left: 3px solid var(--td-border-level-2-color);
            transition: all 0.3s ease;

            &:hover {
              background: var(--td-bg-color-container-hover);
              border-left-color: var(--td-brand-color-3);
            }

            // 已选择状态样式
            &.already-selected {
              background: var(--td-bg-color-container-select);
              border-left-color: var(--td-success-color);
              opacity: 0.7;

              &:hover {
                background: var(--td-bg-color-container-select);
                border-left-color: var(--td-success-color);
              }

              .child-indicator-content {
                .child-indicator-header {
                  .child-indicator-title {
                    color: var(--td-text-color-secondary);
                  }
                }

                .child-indicator-description {
                  color: var(--td-text-color-placeholder);
                }
              }
            }

            .child-indicator-checkbox {
              width: 100%;

              .child-indicator-content {
                margin-left: 8px;
                flex: 1;

                .child-indicator-header {
                  display: flex;
                  align-items: center;
                  gap: 12px;
                  margin-bottom: 6px;

                  .child-indicator-number {
                    background: var(--td-brand-color-1);
                    color: var(--td-brand-color);
                    padding: 3px 8px;
                    border-radius: 4px;
                    font-size: 12px;
                    font-weight: 600;
                    flex-shrink: 0;
                  }

                  .child-indicator-title {
                    font-size: 15px;
                    font-weight: 500;
                    color: var(--td-text-color-primary);
                    flex: 1;
                  }

                  .selected-tag {
                    margin-left: auto;
                    flex-shrink: 0;
                  }
                }

                .child-indicator-description {
                  color: var(--td-text-color-secondary);
                  font-size: 13px;
                  line-height: 1.5;
                }
              }
            }
          }
        }

        // 无子指标提示
        .no-child-indicators {
          padding: 20px;
          text-align: center;

          .no-child-text {
            color: var(--td-text-color-placeholder);
            font-size: 14px;
            font-style: italic;
          }
        }
      }
    }
  }

  .dialog-empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 200px;
    padding: 40px 20px;
    text-align: center;

    .empty-icon {
      color: var(--td-text-color-placeholder);
      margin-bottom: 16px;
    }

    h4 {
      font-size: 18px;
      font-weight: 600;
      color: var(--td-text-color-primary);
      margin: 0 0 8px 0;
    }

    p {
      font-size: 14px;
      color: var(--td-text-color-secondary);
      margin: 0;
      line-height: 1.5;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .indicator-selection-dialog-content {
    // 顶部操作区域移动端样式
    .dialog-header-actions {
      flex-direction: column;
      align-items: stretch;
      gap: 16px;

      .dialog-description {
        margin-right: 0;
        text-align: center;
      }

      .dialog-action-buttons {
        justify-content: center;
        gap: 8px;

        .confirm-btn,
        .cancel-btn {
          flex: 1;
          min-width: auto;
        }
      }
    }

    // 已选择指标点区域移动端样式
    .selected-indicators-section {
      padding: 12px;
      margin-bottom: 20px;

      .selected-indicators-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;

        .selected-title {
          font-size: 15px;
        }

        .clear-all-btn {
          align-self: flex-end;
        }
      }

      .selected-indicators-list {
        max-height: 150px;
        gap: 8px;

        .selected-indicator-card {
          padding: 6px 10px;
          min-width: 100%;
          flex: 1 1 100%;

          .indicator-card-content {
            .indicator-card-header {
              flex-direction: column;
              align-items: flex-start;
              gap: 4px;

              .indicator-card-title {
                font-size: 12px;
                white-space: normal;
                line-height: 1.3;
              }
            }

            .indicator-card-description {
              font-size: 10px;
              white-space: normal;
              line-height: 1.3;
              max-width: none;
              overflow: hidden;
              display: -webkit-box;
              -webkit-line-clamp: 2;
              line-clamp: 2;
              -webkit-box-orient: vertical;
            }
          }
        }
      }

      .selected-indicators-empty {
        padding: 16px;

        .empty-hint-text {
          font-size: 13px;
        }
      }
    }

    .indicator-selection-list {
      max-height: 300px;

      .indicator-checkbox-group {
        gap: 16px;

        .parent-indicator-group {
          .parent-indicator-header {
            padding: 12px 16px;

            .parent-indicator-content {
              .parent-indicator-info {
                flex-direction: column;
                align-items: flex-start;
                gap: 8px;

                .parent-indicator-title {
                  font-size: 16px;
                }
              }

              .parent-indicator-description {
                margin-left: 0;
                font-size: 13px;
              }
            }
          }

          .child-indicators {
            .child-indicator-item {
              padding: 10px 16px;
              margin-left: 12px;

              .child-indicator-checkbox {
                .child-indicator-content {
                  .child-indicator-header {
                    flex-direction: column;
                    align-items: flex-start;
                    gap: 6px;

                    .child-indicator-title {
                      font-size: 14px;
                    }
                  }

                  .child-indicator-description {
                    font-size: 12px;
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}
</style>

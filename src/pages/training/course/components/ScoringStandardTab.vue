<template>
  <div>
    <t-card>
      <template #title>
        <div class="flex items-center w-full">
          <t-icon name="control-platform" class="mr-2 text-blue-500" />
          <span class="flex-1">课程评分标准配置</span>
        </div>
      </template>
      
      <div class="score-rule-content">
        <t-form :data="scoreRuleForm" label-width="120px">
          <t-form-item label="平时成绩占比">
            <t-input-number v-model="scoreRuleForm.homeworkPercentage" :min="0" :max="100" :step="5" suffix="%" style="width: 150px;"></t-input-number>
          </t-form-item>
          
          <t-form-item label="期末考试占比">
            <t-input-number v-model="scoreRuleForm.examPercentage" :min="0" :max="100" :step="5" suffix="%" style="width: 150px;"></t-input-number>
          </t-form-item>
          
          <t-divider>平时成绩组成</t-divider>
          
          <t-table
            :data="scoreRuleForm.homeworkItems"
            :columns="homeworkColumns"
            :bordered="true"
            :hover="true"
            row-key="id"
          >
            <template #percentage="{ row }">
              <t-input-number v-model="row.percentage" :min="0" :max="100" :step="5" suffix="%" size="small" style="width: 120px;"></t-input-number>
            </template>
            <template #operation="{ row }">
              <t-space size="small">
                <t-button theme="danger" variant="text" size="small" @click="handleDeleteHomeworkItem(row.id)">
                  <template #icon>
                    <t-icon name="delete" />
                  </template>
                  删除
                </t-button>
              </t-space>
            </template>
          </t-table>
          
          <div class="mt-4 mb-6">
            <t-button theme="primary" variant="outline" @click="handleAddHomeworkItem">
              <template #icon>
                <t-icon name="add" />
              </template>
              添加评分项
            </t-button>
          </div>
          
          <t-divider>成绩等级划分</t-divider>
          
          <t-row :gutter="[16, 16]">
            <t-col :span="6">
              <t-form-item label="优秀(≥)">
                <t-input-number v-model="scoreRuleForm.excellentThreshold" :min="0" :max="100" style="width: 100px;"></t-input-number>
              </t-form-item>
            </t-col>
            <t-col :span="6">
              <t-form-item label="良好(≥)">
                <t-input-number v-model="scoreRuleForm.goodThreshold" :min="0" :max="100" style="width: 100px;"></t-input-number>
              </t-form-item>
            </t-col>
            <t-col :span="6">
              <t-form-item label="中等(≥)">
                <t-input-number v-model="scoreRuleForm.mediumThreshold" :min="0" :max="100" style="width: 100px;"></t-input-number>
              </t-form-item>
            </t-col>
            <t-col :span="6">
              <t-form-item label="及格(≥)">
                <t-input-number v-model="scoreRuleForm.passThreshold" :min="0" :max="100" style="width: 100px;"></t-input-number>
              </t-form-item>
            </t-col>
          </t-row>
          
          <t-form-item>
            <t-space size="small">
              <t-button theme="primary" type="submit" @click="handleSaveConfig">保存配置</t-button>
              <t-button theme="default" variant="base" type="reset" @click="handleResetConfig">重置</t-button>
            </t-space>
          </t-form-item>
        </t-form>
      </div>
    </t-card>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { MessagePlugin } from 'tdesign-vue-next';
import {
  mockScoreRuleForm,
  mockHomeworkColumns,
  type HomeworkItem
} from '../../../../../mock/assessment';

// 平时成绩组成表格列配置
const homeworkColumns = mockHomeworkColumns;

// 评分规则表单数据
const scoreRuleForm = ref({
  ...mockScoreRuleForm,
  homeworkItems: [...mockScoreRuleForm.homeworkItems] // 深拷贝数组
});

// 删除作业项
const handleDeleteHomeworkItem = (id: number) => {
  const index = scoreRuleForm.value.homeworkItems.findIndex(item => item.id === id);
  if (index > -1) {
    scoreRuleForm.value.homeworkItems.splice(index, 1);
    MessagePlugin.success('评分项已删除');
  }
};

// 添加作业项
const handleAddHomeworkItem = () => {
  const newId = Math.max(...scoreRuleForm.value.homeworkItems.map(item => item.id)) + 1;
  const newItem: HomeworkItem = {
    id: newId,
    name: '新评分项',
    description: '请填写评分项描述',
    percentage: 0
  };
  scoreRuleForm.value.homeworkItems.push(newItem);
  MessagePlugin.success('评分项已添加');
};

// 保存配置
const handleSaveConfig = () => {
  // 验证权重总和是否为100%
  const totalPercentage = scoreRuleForm.value.homeworkItems.reduce((sum, item) => sum + item.percentage, 0);
  if (totalPercentage !== 100) {
    MessagePlugin.warning(`平时成绩组成权重总和应为100%，当前为${totalPercentage}%`);
    return;
  }
  
  // 验证平时成绩和期末考试占比总和是否为100%
  const totalExamPercentage = scoreRuleForm.value.homeworkPercentage + scoreRuleForm.value.examPercentage;
  if (totalExamPercentage !== 100) {
    MessagePlugin.warning(`平时成绩和期末考试占比总和应为100%，当前为${totalExamPercentage}%`);
    return;
  }
  
  MessagePlugin.success('评分标准配置已保存');
};

// 重置配置
const handleResetConfig = () => {
  scoreRuleForm.value = {
    ...mockScoreRuleForm,
    homeworkItems: [...mockScoreRuleForm.homeworkItems]
  };
  MessagePlugin.success('配置已重置');
};
</script>

<style lang="less" scoped>
// 表格单元格支持换行
:deep(.t-table) {
  td {
    white-space: normal;
  }
}
</style>

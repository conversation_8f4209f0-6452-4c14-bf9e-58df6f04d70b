<template>
  <div class="assessment-plan-container">
    <!-- 顶部统计卡片 -->
    <div class="stats-cards">
      <div class="stats-card">
        <div class="stats-content">
          <div class="stats-title">考核环节数量</div>
          <div class="stats-value">{{ assessmentMethodData.length }}</div>
        </div>
      </div>
      <div class="stats-card">
        <div class="stats-content">
          <div class="stats-title">毕业要求数量</div>
          <div class="stats-value">{{ courseObjectives.length }}</div>
        </div>
      </div>
      <div class="stats-card">
        <div class="stats-content">
          <div class="stats-title">课程目标数量</div>
          <div class="stats-value">{{ courseObjectives.length }}</div>
        </div>
      </div>
      <div class="stats-card success">
        <div class="stats-content">
          <div class="stats-title">毕业要求支撑权重</div>
          <div class="stats-value">100%</div>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <t-loading :loading="loading">
        <!-- 课程目标配置区域 -->
        <div class="assessment-config-section">
          <div class="section-header">
            <h3 class="section-title">
              <span>课程目标信息</span>
              <span class="course-name" v-if="courseInfo">- {{ courseInfo.courseName }}</span>
              <span class="course-code" v-if="courseInfo">({{ courseInfo.courseCode }})</span>
            </h3>
            <div class="section-actions">
              <t-button theme="warning" size="small" @click="showStandardsDialog = true">
                <t-icon name="view-module" />
                查看评价标准
              </t-button>
            </div>
          </div>

          <!-- 课程目标方法选择 -->
          <div class="target-method-selection">

            <!-- 课程目标列表 -->
            <div v-if="courseObjectiveList.length > 0" class="target-cards">
              <div
                v-for="(objective, index) in courseObjectiveList"
                :key="objective.objectiveId"
                class="target-card"
              >
                <div class="target-header">
                  <span class="target-number">{{ index + 1 }}</span>
                  <span class="target-title">{{ objective.objectiveName || `课程目标${index + 1}` }}</span>
                </div>
                <div class="target-content">
                  <div class="target-description">{{ objective.description || '课程目标描述' }}</div>
                  <div class="target-indicator">
                    <t-button
                      theme="primary"
                      size="small"
                      variant="outline"
                      @click="showIndicatorDialog(objective)"
                    >
                      毕业要求{{ objective.po.title || `${index + 1}.1` }}
                    </t-button>
                  </div>
                </div>
              </div>
            </div>

            <!-- 课程目标空状态 -->
            <div v-else class="target-empty-state">
              <div class="empty-content">
                <div class="empty-icon">
                  <t-icon name="view-list" size="56px" style="color: var(--td-text-color-placeholder);" />
                </div>
                <div class="empty-text">
                  <h4 class="empty-title">暂无课程目标数据</h4>
                  <p class="empty-description">课程目标数据加载失败或尚未配置</p>
                  <p class="empty-tip">请检查课程设置或尝试重新加载数据</p>
                </div>
                <div class="empty-actions">
                  <t-button theme="primary" size="medium" @click="getCourseTargetList(courseId)">
                    <t-icon name="refresh" />
                    重新加载课程目标
                  </t-button>
                </div>
              </div>
            </div>
          </div>

          <!-- 提示信息 -->
          <div class="config-tip">
            <t-icon name="info-circle" />
            <span>提示：请先选择考核方式和教学目标，然后配置对应的权重分配，确保各项权重总和为100%</span>
          </div>
        </div>

        <!-- 考核方式选择区域 -->
        <div class="assessment-table-section method-selection-section">
          <div class="table-header">
            <h3 class="table-title">考核方式配置</h3>
            <div class="table-actions">
              <t-button theme="primary" size="small" @click="showAddMethodDialog = true">
                <t-icon name="add"></t-icon>
                添加考核方式
              </t-button>
            </div>
          </div>

          <div class="method-selection-content">
            <!-- 已选考核方式展示 -->
            <div v-if="selectedMethods.length > 0" class="method-selection">
              <div class="method-label">已选考核方式</div>
              <div class="method-tags">
                <t-tag
                  v-for="method in selectedMethods"
                  :key="method.id"
                  :theme="method.isCustom ? 'warning' : 'primary'"
                  size="small"
                  closable
                  @close="removeMethod(method.id)"
                  class="method-tag"
                  style="margin-right: 8px; margin-bottom: 8px;"
                >
                  {{ method.name }}
                  <span v-if="method.isCustom" class="custom-badge">(自定义)</span>
                </t-tag>
              </div>
            </div>

            <!-- 空状态展示 -->
            <div v-else class="method-empty-state">
              <div class="empty-content">
              <div class="empty-icon">
                <t-icon name="plus-circle" size="56px" style="color: var(--td-text-color-placeholder);" />
              </div>
              <div class="empty-text">
                <h4 class="empty-title">暂无考核方式</h4>
                <p class="empty-description">尚未配置任何考核方式</p>
                <p class="empty-tip">请点击"添加考核方式"按钮开始配置课程的考核方式</p>
              </div>
              <div class="empty-actions">
                <t-button theme="primary" size="medium" @click="showAddMethodDialog = true">
                <t-icon name="add" />
                添加考核方式
                </t-button>
              </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 表1：课程考核各环节占比 -->
        <div class="assessment-table-section">
          <div class="table-header">
            <h3 class="table-title">表1 课程考核各环节占比</h3>
            <div class="table-actions">
              <t-button theme="primary" size="small">
                <t-icon name="download" />
                导出配置表
              </t-button>
              <t-button
                v-if="!isEditMode"
                theme="warning"
                size="small"
                @click="enterEditMode"
              >
                <t-icon name="edit" />
                编辑配置
              </t-button>
              <t-space v-else size="8">
                <t-button
                  theme="success"
                  size="small"
                  @click="saveAllChanges"
                  :loading="loading"
                >
                  <t-icon name="check" />
                  保存
                </t-button>
                <t-button
                  theme="default"
                  size="small"
                  @click="cancelEdit"
                >
                  <t-icon name="close" />
                  取消
                </t-button>
              </t-space>
            </div>
          </div>

          <div class="assessment-table">
            <table class="config-table">
              <thead>
                <tr>
                  <th rowspan="2">序号</th>
                  <th rowspan="2">考核环节</th>
                  <th :colspan="courseObjectives.length">课程目标达成度考核占比(%)</th>
                  <th rowspan="2">合计</th>
                  <th rowspan="2">评价说明</th>
                  <th rowspan="2">课程目标</th>
                </tr>
                <tr>
                  <th v-for="(objective, index) in courseObjectives" :key="objective.id" class="objective-header">
                    CO{{ index + 1 }}
                  </th>
                </tr>
              </thead>
              <tbody>
                <!-- 空状态显示 -->
                <tr v-if="(isEditMode ? editingData : assessmentTableData).length === 0">
                  <td :colspan="courseObjectives.length + 6" class="empty-state">
                    <div class="empty-content">
                      <t-icon name="inbox" size="48px" style="color: var(--td-text-color-placeholder);" />
                      <p>暂无考核方式数据</p>
                      <p class="empty-tip">请先配置考核方式，然后设置各课程目标的占比</p>
                    </div>
                  </td>
                </tr>

                <!-- 数据行 -->
                <tr
                  v-for="(item, index) in (isEditMode ? editingData : assessmentTableData)"
                  :key="index"
                  :class="{ 'edit-mode-row': isEditMode }"
                >
                  <td>{{ index + 1 }}</td>
                  <td>{{ item.methodName }}</td>
                  <td
                    v-for="(objective, objIndex) in courseObjectives"
                    :key="objective.id"
                    class="objective-cell"
                  >
                    <t-input
                      v-if="isEditMode"
                      v-model="item[`co${objIndex + 1}`]"
                      type="number"
                      size="small"
                      :min="0"
                      :max="100"
                      placeholder="0"
                      class="objective-input"
                    />
                    <span v-else>{{ getObjectiveValue(item, objIndex) }}</span>
                  </td>
                  <td class="total-cell">
                    <t-tag
                      :theme="getTotalTagTheme(item)"
                      size="small"
                    >
                      {{ calculateRowTotal(item) }}%
                    </t-tag>
                  </td>
                  <td class="criteria-cell">
                    <t-textarea
                      v-if="isEditMode"
                      v-model="item.criteria"
                      size="small"
                      :autosize="{ minRows: 1, maxRows: 3 }"
                      placeholder="请输入评价说明"
                      class="criteria-input"
                    />
                    <span v-else>{{ item.criteria }}</span>
                  </td>
                  <td class="objectives-cell">
                    <t-space size="8">
                      <t-tag
                        v-for="(objective, objIndex) in getActiveObjectives(item)"
                        :key="objIndex"
                        :theme="getObjectiveTagTheme(objIndex)"
                        size="small"
                        class="objective-tag"
                      >
                        CO{{ objIndex + 1 }}
                      </t-tag>
                    </t-space>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        <!-- 表2：课程目标考核权重分配表 -->
        <div class="weight-distribution-section">
          <div class="table-header">
            <h3 class="table-title">表2 课程目标考核权重分配表</h3>
            <!-- <div class="table-actions">
              <t-button theme="primary" size="small">
                <t-icon name="download" />
                导出分配表
              </t-button>
            </div> -->
          </div>

          <div class="weight-distribution-table">
            <table class="distribution-table">
              <thead>
                <tr>
                  <th rowspan="2">序号</th>
                  <th rowspan="2">考核环节</th>
                  <th :colspan="courseObjectives.length">课程目标权重分配(%)</th>
                  <th rowspan="2">期末综合成绩计算权重(%)</th>
                </tr>
                <tr>
                  <th v-for="(objective, index) in courseObjectives" :key="objective.id" class="objective-weight-header">
                    CO{{ index + 1 }}<br><span class="sub-title">权重要求</span>
                  </th>
                </tr>
              </thead>
              <tbody>
                <!-- 空状态显示 -->
                <tr v-if="(isEditMode ? editingTable2Data : weightDistributionData).length === 0">
                  <td :colspan="courseObjectives.length + 3" class="empty-state">
                    <div class="empty-content">
                      <t-icon name="inbox" size="48px" style="color: var(--td-text-color-placeholder);" />
                      <p>暂无权重分配数据</p>
                      <p class="empty-tip">请先配置考核方式，然后设置各课程目标的权重分配</p>
                    </div>
                  </td>
                </tr>

                <!-- 数据行 -->
                <tr
                  v-for="(item, index) in (isEditMode ? editingTable2Data : weightDistributionData)"
                  :key="index"
                  :class="{ 'edit-mode-row': isEditMode }"
                >
                  <td>{{ index + 1 }}</td>
                  <td>{{ item.methodName }}</td>
                  <td
                    v-for="(objective, objIndex) in courseObjectives"
                    :key="objective.id"
                    class="weight-cell"
                  >
                    <div v-if="isEditMode" class="weight-edit-container">
                      <t-input
                        v-model="item[`co${objIndex + 1}Weight`]"
                        type="number"
                        size="small"
                        :min="0"
                        :max="100"
                        placeholder="0"
                        class="weight-input"
                      />
                    </div>
                    <div v-else class="weight-display-container">
                      <div class="weight-value">{{ getTable2ObjectiveWeight(item, objIndex) }}%</div>
                      <t-tag
                        :theme="getSupportLevelTheme(getTable2ObjectiveWeight(item, objIndex))"
                        size="small"
                        class="support-tag"
                      >
                        {{ getSupportLevelText(getTable2ObjectiveWeight(item, objIndex)) }}
                      </t-tag>
                    </div>
                  </td>
                  <td class="final-weight-cell">
                    <div v-if="isEditMode" class="weight-edit-container">
                      <t-input
                        v-model="item.examWeight"
                        type="number"
                        size="small"
                        :min="0"
                        :max="100"
                        placeholder="0"
                        class="weight-input"
                      />
                    </div>
                    <div v-else class="weight-display-container">
                      <div class="weight-value">{{ item.examWeight || 0 }}%</div>
                      <t-tag
                        :theme="getSupportLevelTheme(item.examWeight || 0)"
                        size="small"
                        class="support-tag"
                      >
                        {{ getSupportLevelText(item.examWeight || 0) }}
                      </t-tag>
                    </div>
                  </td>
                </tr>
              </tbody>
              <tfoot v-if="(isEditMode ? editingTable2Data : weightDistributionData).length > 0">
                <tr class="total-row">
                  <td colspan="2">合计</td>
                  <td v-for="(objective, objIndex) in courseObjectives" :key="objective.id" class="total-cell">
                    <t-tag
                      :theme="getTable2ColumnTotalTheme(objIndex)"
                      size="small"
                    >
                      {{ calculateTable2ColumnTotal(objIndex) }}%
                    </t-tag>
                  </td>
                  <td class="total-cell">
                    <t-tag
                      :theme="getTable2FinalWeightTotalTheme()"
                      size="small"
                    >
                      {{ calculateTable2FinalWeightTotal() }}%
                    </t-tag>
                  </td>
                </tr>
                <tr class="expected-achievement-row">
                  <td colspan="2">达成度期望值</td>
                  <td v-for="(objective, objIndex) in courseObjectives" :key="objective.id" class="expected-cell">
                    <div v-if="isEditMode" class="expected-edit-container">
                      <t-input
                        v-model="expectedAchievements[`co${objIndex + 1}`]"
                        type="number"
                        size="small"
                        :min="0"
                        :max="100"
                        placeholder="0"
                        class="expected-input"
                      />
                      <span class="percent-symbol">%</span>
                    </div>
                    <div v-else class="expected-display-container">
                      <span class="expected-value">{{ expectedAchievements[`co${objIndex + 1}`] || 0 }}%</span>
                    </div>
                  </td>
                  <td class="expected-cell">
                    <!-- <div v-if="isEditMode" class="expected-edit-container">
                      <t-input
                        v-model="expectedAchievements.finalWeight"
                        type="number"
                        size="small"
                        :min="0"
                        :max="100"
                        placeholder="0"
                        class="expected-input"
                      />
                      <span class="percent-symbol">%</span>
                    </div>v-else  -->
                    <div class="expected-display-container">
                      <span class="expected-value">60</span>
                    </div>
                  </td>
                </tr>
              </tfoot>
            </table>
          </div>

          <!-- 权重校验提示 -->
          <div class="weight-validation">
            <t-icon name="info-circle" />
            <span>权重校验：各考核方式对应的课程目标权重总和应为100%</span>
          </div>
          
          <!-- 验证错误列表 -->
          <ul v-if="validationErrors.length > 0" class="validation-errors">
            <li v-for="error in validationErrors" :key="error">{{ error }}</li>
          </ul>
        </div>

       
        
      </t-loading>
    </div>

    <!-- 评价标准详细查看对话框 -->
    <EvaluationStandardsDialog
      v-model:visible="showStandardsDialog"
      :evaluation-standards="evaluationStandards"
      :grade-distribution="gradeDistribution"
      :total-students="totalStudents"
      @export="handleExportStandards"
      @edit="handleEditStandards"
    />

    <!-- 指标点详情对话框 -->
    <t-dialog
      v-model:visible="showIndicatorDetailDialog"
      :header="currentIndicator?.title || '指标点详情'"
      :width="800"
      :footer="false"
      placement="center"
      class="indicator-detail-dialog"
    >
      <div v-if="currentIndicator" class="indicator-content">
        <!-- 基本信息 -->
        <div class="indicator-basic">
          <div class="info-row">
            <span class="info-label">指标点编号：</span>
            <t-tag theme="primary" size="small">{{ currentIndicator.id }}</t-tag>
          </div>
          <div class="info-row">
            <span class="info-label">关联课程目标：</span>
            <t-tag theme="success" size="small">{{ currentIndicator.relatedObjective }}</t-tag>
          </div>
          <div class="info-row">
            <span class="info-label">权重占比：</span>
            <t-tag theme="warning" size="small">{{ currentIndicator.weight }}</t-tag>
          </div>
        </div>

        <!-- 详细描述 -->
        <div class="indicator-description">
          <h4>指标点描述</h4>
          <p>{{ currentIndicator.description }}</p>
        </div>

        <!-- 毕业要求 -->
        <div class="indicator-requirements">
          <h4>毕业要求</h4>
          <ul>
            <li v-for="(req, index) in currentIndicator.requirements" :key="index">
              {{ req }}
            </li>
          </ul>
        </div>

        <!-- 考核方式 -->
        <div class="indicator-assessment">
          <h4>相关考核方式</h4>
          <t-space size="8">
            <t-tag
              v-for="(method, index) in currentIndicator.assessmentMethods"
              :key="index"
              theme="default"
              size="small"
            >
              {{ method }}
            </t-tag>
          </t-space>
        </div>
      </div>
    </t-dialog>

    <!-- 添加考核方式对话框 -->
    <t-dialog
      v-model:visible="showAddMethodDialog"
      header="添加考核方式"
      :width="500"
      :confirm-btn="{ content: '确定', theme: 'primary' }"
      :cancel-btn="{ content: '取消', theme: 'default', variant: 'outline' }"
      @confirm="addSelectedMethods"
      @close="resetMethodSelection"
    >
      <div class="assessment-method-selector">
        <h4 class="selector-title">选择或创建考核方式</h4>
        <p class="selector-description">选择预设考核方式或输入自定义方式（按Enter创建）</p>
        
        <div class="selector-content">
          <t-select
            ref="selectRef"
            v-model="selectedMethodsToAdd"
            v-model:input-value="inputValue"
            placeholder="选择或创建考核方式"
            filterable
            multiple
            allow-input
            allow-create
            clearable
            :create-option="createOption"
            @create="onCreateNewMethod"
          >
            <t-option
              v-for="method in availablePresetMethods"
              :key="method.id"
              :value="method.id"
              :label="method.name"
            />
          </t-select>
        </div>
        
        <div class="selected-methods-preview" v-if="selectedMethodsToAdd.length > 0">
          <h5>将要添加的考核方式：</h5>
          <t-tag
            v-for="methodId in selectedMethodsToAdd"
            :key="methodId"
            :theme="isCustomMethod(methodId) ? 'warning' : 'primary'"
            size="small"
            class="preview-tag"
          >
            {{ getMethodName(methodId) }}
            <span v-if="isCustomMethod(methodId)" class="custom-badge">(自定义)</span>
          </t-tag>
        </div>
      </div>
    </t-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, watch } from 'vue';
import { useRoute } from 'vue-router';
import { MessagePlugin } from 'tdesign-vue-next';
import {
  getCourseAssessmentDetail,
  getCourseTargetList,
  type AssessmentMethod,
  type CourseAssessmentDetailVO,
  type CourseAssessmentConfig,
  saveCourseAssessmentConfig,
  type CourseDetailInfo,
  type CourseObjectiveVO
} from '@/api/training/course';

import EvaluationStandardsDialog from './components/EvaluationStandardsDialog.vue';
import {getDictDataByTypeTitle} from '@/utils/dictUtil';

// 路由参数
const route = useRoute();
const courseId = Number(route.params.courseId || route.query.courseId || 0);


// 响应式数据
const loading = ref(false);
const tableLoading = ref(false);
const courseInfo = ref<CourseDetailInfo | null>(null);
const showAddMethodDialog = ref(false);
const showStandardsDialog = ref(false); // 评价标准对话框显示状态
const showIndicatorDetailDialog = ref(false); // 指标点详情对话框显示状态
const currentIndicator = ref<any>(null); // 当前查看的指标点信息
const isEditMode = ref(false); // 统一编辑模式状态（控制两个表格）
const editingData = ref<any[]>([]); // 表1编辑中的数据副本
const editingTable2Data = ref<any[]>([]); // 表2编辑中的数据副本
// 达成度期望值数据（用于编辑）
const expectedAchievements = ref<any>({});

// 基于assessmentProportionData的达成度期望值计算属性（用于显示）
const displayExpectedAchievements = computed(() => {
  // const data: any = {};
  // // 为每个考核方法设置期望值
  // courseObjectiveList.value.forEach((co,index) => {
  //   data[`co${index+1}`] = expectedAchievements.value[`co${index+1}`] || 60; // 默认值80
  // });
  return expectedAchievements.value;
});
const validationErrors = ref<string[]>([]);

// 课程考核计划数据 - 使用新的数据结构
const assessmentDetailData = ref<CourseAssessmentDetailVO | null>(null);
const courseObjectiveList = ref<CourseObjectiveVO[]>([]);
const assessmentMethodData = ref<ExtendedAssessmentMethod[]>([]); // 考核方式数据
const assessmentWeightData = ref<ExtendedCourseAssessmentConfig[]>([]); //  课程目标权重数据 (表1)
const assessmentProportionData = ref<ExtendedCourseAssessmentConfig[]>([]); //占比据 (表2)
const assessmentWeightExamData = ref<ExtendedCourseAssessmentConfig[]>([]); //期末考核(表2)
const selectedMethodsToAdd = ref<string[]>([]); // 对话框中选择的考核方式ID列表
const createdMethodIds = ref<Map<string, string>>(new Map()); // 保存创建的方法ID映射
const selectRef = ref(); // Select 组件引用
const inputValue = ref(''); // 用于控制输入框的值
// 权重配置表格数据
const weightTableData = ref<any[]>([]);
const courseObjectives = ref<any[]>([]);
// 扩展考核方式接口
interface ExtendedAssessmentMethod extends AssessmentMethod {
  typeName?: string;
  typeId?: string;
}

// 预设考核方式数据
const presetMethods  = ref<ExtendedAssessmentMethod[]>([
]);

const assessmentMethods = ref<ExtendedAssessmentMethod[]>([]);//...presetMethods

// 选中的考核方式
const selectedMethods = ref<ExtendedAssessmentMethod[]>([
]);

// 可选的预设考核方式（已过滤掉已选的）
const availablePresetMethods = computed(() => {
  const selectedIds = selectedMethods.value.map(m => m.id);
  return presetMethods.value.filter(m => !selectedIds.includes(m.id));
});


// 基于API数据的考核表格数据计算属性（表1计算）
const assessmentTableData = computed(() => {
  // 如果没有考核方式数据，返回空数组
  if (!assessmentMethodData.value || assessmentMethodData.value.length === 0) {
    return [];
  }
  // 遍历占比分配数据，生成表格行
  return assessmentMethodData.value.map((method:ExtendedAssessmentMethod) => {
    const proportion = assessmentProportionData.value.find((item: ExtendedCourseAssessmentConfig) => item.methodId === method.id);
   // console.log(`考核方式 ${method.name} 的占比数据:`, proportion);
    
    // 处理proportion为undefined的情况
    if (!proportion) {
      const row: any = {
        methodId: method.id,
        methodName: method.name,
        totalWeight: 0,
        criteria: '待配置',
        objectives: '' // 将根据占比数据动态计算
      };
      
      // 为每个课程目标添加默认占比数据
      courseObjectives.value.forEach((_, index) => {
        const coKey = `co${index + 1}`;
        row[coKey] = 0;
      });
      
      return row;
    }else{
        const row: any = {
              methodId: method.id,
              methodName: method.name,
              totalWeight: 0,
              criteria: proportion.description || '待配置',
              objectives: '' // 将根据占比数据动态计算
            };

            // 为每个课程目标添加占比数据
            courseObjectives.value.forEach((objective, index) => {
              const coKey = `co${index + 1}`;
              const objectiveKey = objective.objectiveId || objective.title;
              // 从weights中获取占比数据
              const weightValue = proportion.weightMap?.[objectiveKey] || 0;
             // console.log(`课程目标 ${objectiveKey} 的占比:`, weightValue);
              row[coKey] = typeof weightValue === 'number' ? weightValue : 0;
            });
            // 计算关联的课程目标
            const relatedObjectives: string[] = [];
            courseObjectives.value.forEach((objective, index) => {
              const coKey = `co${index + 1}`;
              if (row[coKey] > 0) {
                relatedObjectives.push(`课程目标${index + 1}`);
              }
            });
            row.objectives = relatedObjectives.join(',');

            return row;
            
    } 
  });
});



// 基于API数据的权重分配数据计算属性（表2计算）
const weightDistributionData = computed(() => {
  // 如果没有课程目标权重数据，返回空数组
  if (!assessmentMethodData.value || assessmentMethodData.value.length === 0) {
    return [];
  }
 
  return assessmentMethodData.value.map((method:ExtendedAssessmentMethod) => {
    const weight = assessmentWeightData.value.find((item: ExtendedCourseAssessmentConfig) => item.methodId === method.id);
    if(!weight){
      const row: any = {
        methodId: method.id,
        methodName: method.name,
        examWeight: 0, // 默认期末综合成绩权重
        criteria: '待配置',
        objectives: '', // 将根据占比数据动态计算
        finalWeight: 0 // 默认期末综合成绩权重
      };

      // 为每个课程目标添加默认权重数据
      courseObjectives.value.forEach((objective, index) => {
        const coKey =  `co${index + 1}Weight`;//objective.objectiveId;
        row[coKey] = 0; // 默认权重为0
      });

      return row;

    }else{
        const row: any = {
        methodId: method.id,
        methodName: method.name,
        criteria: weight.description || '待配置',
        objectives: '', // 将根据占比数据动态计算,
        examWeight: weight?.examWeight || 0 ,// 默认期末综合成绩权重
        finalWeight: weight?.examWeight || 0 // 达成度期望值
      };

      // 为每个课程目标添加权重数据
      courseObjectives.value.forEach((objective, index) => {
        const coKey = `co${index + 1}Weight`;
        const objectiveKey = objective.objectiveId;

        // 从assessmentWeightData中查找对应课程目标的权重
        const objectiveWeightData = weight?.weightMap[objectiveKey];
        // 如果没有找到对应的权重数据，默认为0
        row[coKey] = objectiveWeightData || 0;
      });

      return row;
    }  
  });
});

// // 表格列配置
// const weightTableColumns = computed(() => {
//   const baseColumns = [
//     {
//       colKey: 'methodName',
//       title: '考核方式',
//       width: 120,
//       fixed: 'left'
//     }
//   ];
  
//   // 动态添加课程目标列
//   const objectiveColumns = courseObjectives.value.map((obj, index) => ({
//     colKey: `objective_${obj.id}`,
//     title: `目标${index + 1}`,
//     width: 100,
//     cell: 'weight'
//   }));
  
//   const endColumns = [
//     {
//       colKey: 'totalWeight',
//       title: '权重合计',
//       width: 100,
//       cell: 'total'
//     }
//   ];
  
//   return [...baseColumns, ...objectiveColumns, ...endColumns];
// });

// 方法
const loadCourseInfo = async () => {
  try {
    loading.value = true;

    // 1. 加载课程考核计划详情（独立处理错误）
    try {
      await loadAssessmentDetail();
    } catch (assessmentError: any) {
      console.error('加载考核计划详情失败:', assessmentError);
      MessagePlugin.warning('考核计划详情加载失败，但课程目标和基本功能仍可使用');
      // 考核详情加载失败时，确保有基本的数据结构
      assessmentMethodData.value = [];
      assessmentWeightData.value = [];
      assessmentProportionData.value = [];
      assessmentWeightExamData.value = [];
      selectedMethods.value = [];
    }

  } catch (error: any) {
    console.error('加载课程信息失败:', error);
    MessagePlugin.error(error.message || '获取课程信息失败');

    // 即使所有API都失败，也要确保页面有基本的数据结构
    initializeEmptyData();
  } finally {
    loading.value = false;
  }
};

interface ExtendedCourseAssessmentConfig  extends CourseAssessmentConfig {
  weightMap: Record<string, number>;
}

// 解析新的数据结构（CourseAssessmentDetailVO）
const parseNewDataStructure = (data: CourseAssessmentDetailVO) => {
  try {

    //0、开始解析课程目标数据
    //console.log('开始解析课程目标数据...');
    // 初始化课程目标列表
    if (data.courseObjectiveList && Array.isArray(data.courseObjectiveList)) {
      courseObjectiveList.value = data.courseObjectiveList.map((objective, index) => {
        return {
          id: index + 1, // 前端显示用的序号
          title: objective.objectiveName, // 使用objectiveName字段
          description: objective.description || objective.objectiveName || `课程目标${index + 1}`,
          objectiveId: objective.objectiveId || index + 1, // 使用objectiveId字段
          number: index + 1, // 添加必需的number属性
          objectiveName: objective.objectiveName || `课程目标${index + 1}`,
          expectedScore: objective.expectedScore || 60,
          po: objective.po || {
            id: '',
            poNumber: '',
            title: '',
            description: '',
            supportLevel: ''
          }
        };
      });
      courseObjectives.value = courseObjectiveList.value; // 更新courseObjectives
    } else {
      courseObjectiveList.value = [];
      console.warn('课程目标数据格式不正确或为空');
    }
   // console.log('课程目标数据解析完成:', courseObjectiveList.value);
    // 1. 解析考核方式列表
    if (data.assessmentMethods && Array.isArray(data.assessmentMethods)) {
      assessmentMethodData.value = data.assessmentMethods.map(method => {
        // 查询预设考核方式数据，根据id确认name
        const presetMethod = presetMethods.value.find(m => m.id === method.id);
        const custom = !presetMethod; // 如果没有找到预设方式，则为自定义考核方式
        return {
          id: method.id,
          name: presetMethod ? presetMethod.name : method.name || `自定义考核方式${method.name}`,
          weight: Number(method.weight) || 0,
          isCustom: custom, // 是否为自定义考核方式
        };
      });
    } else {
      assessmentMethodData.value = [];
      console.warn('考核方式数据格式不正确或为空');
    }
    
    // 2. 解析权重配置数据（表2数据）- 从assessmentWeight字段转换为CourseAssessmentConfig格式
    if (data.assessmentWeight && Array.isArray(data.assessmentWeight)) {
      assessmentWeightData.value = data.assessmentWeight.map((weightItem: CourseAssessmentConfig): ExtendedCourseAssessmentConfig => {
      //  console.log('解析权重数据项:', weightItem);
        
        // 转换weights字段，保持后端字段名不变
        let weightsMap: Record<string, number> = {};
        weightItem.objectiveList.forEach((item: any) => {
            if (item.objectiveId && item.weight !== undefined) {
            
              weightsMap[item.objectiveId] = Number(item.weight) || 0;

            }
          });

        return {
          methodId: weightItem.methodId || '',
          methodName: weightItem.methodName || '',
          examWeight: Number(weightItem.examWeight) || 0,
          description: weightItem.description || '',
          isFinalExam: weightItem.isFinalExam || false,
          objectiveList: weightItem.objectiveList || [],
          // 添加weightMap字段，保持后端字段名不变
          weightMap: weightsMap,
        };
      });
    } else {
      assessmentWeightData.value = [];
      console.warn('课程目标权重配置数据格式不正确或为空');
    }
    
    // 3. 解析课程目标权重数据（表1数据）- 从assessmentProportions字段转换为CourseAssessmentConfig格式
    if (data.assessmentProportions && Array.isArray(data.assessmentProportions)) {
      assessmentProportionData.value = data.assessmentProportions.map((weightItem: CourseAssessmentConfig): ExtendedCourseAssessmentConfig => {
      //    console.log('解析占比数据项:', weightItem);
        // 转换weights字段，保持后端字段名不变
            let weightsMap: Record<string, number> = {};
            weightItem.objectiveList.forEach((item: any) => {
                if (item.objectiveId && item.weight !== undefined) {
                  weightsMap[item.objectiveId] = Number(item.weight) || 0;
                }
              });

            return {
              methodId: weightItem.methodId || '',
              methodName: weightItem.methodName || '',
              examWeight: Number(weightItem.examWeight) || 0,
              description: weightItem.description || '',
              isFinalExam: weightItem.isFinalExam || false,
              objectiveList: weightItem.objectiveList || [],
              // 添加weightMap字段，保持后端字段名不变
              weightMap: weightsMap,
            };
       
      });
    } else {
      assessmentProportionData.value = [];
      console.warn('课程目标考核占比数据格式不正确或为空');
    }

    // 更新相关数据
    updateSelectedMethodsFromData();
    //updateWeightTableFromData();

    //console.log('=== 数据解析完成 ===');
    //console.log('- assessmentMethodData:', assessmentMethodData.value);
    //console.log('- assessmentProportionData(表1):', assessmentProportionData.value);
    //console.log('- assessmentWeightData(表2):', assessmentWeightData.value);
    

  } catch (error) {
    console.error('解析新数据结构失败:', error);
    throw error;
  }
};

// 初始化空数据结构
const initializeEmptyData = () => {
  courseObjectives.value = [];
  courseObjectiveList.value = [];
  assessmentMethodData.value = [];
  assessmentWeightData.value = [];
  assessmentProportionData.value = [];
  selectedMethods.value = [];
  //weightTableData.value = [];
};



// 加载课程考核计划详情
const loadAssessmentDetail = async () => {
  try {
    assessmentDetailData.value = await getCourseAssessmentDetail(courseId);
    //解析从后端请求的数据,并初始化权重表格
    parseNewDataStructure(assessmentDetailData.value);
    
    // 初始化期望值数据
    initExpectedAchievements();


  } catch (error: any) {
    console.warn('获取课程考核计划详情失败，使用默认数据:', error.message);
  }
};

// 从后端数据更新选中的考核方式
const updateSelectedMethodsFromData = () => {
  // 清空现有的selectedMethods，避免重复
  selectedMethods.value = [];

  selectedMethods.value = assessmentMethodData.value.map((method, index) => ({
    id: method.id,
    name: method.name,
    weight: method.weight,
    typeName: method.name,
    isCustom: false, // 从后端加载的数据默认为非自定义
    typeId: method.id,
    //description: method.description || ''
  }));
};

const validateWeights = () => {
  validationErrors.value = [];
  
  // 检查每行权重总和
  weightTableData.value.forEach(row => {
    let rowTotal = 0;
    courseObjectives.value.forEach(obj => {
      rowTotal += row[`objective_${obj.id}`] || 0;
    });
    
    if (Math.abs(rowTotal - 100) > 0.1) {
      validationErrors.value.push(`${row.methodName} 的权重总和应为100%，当前为${rowTotal.toFixed(1)}%`);
    }
  });
  
  // 检查每列权重总和
  courseObjectives.value.forEach((obj, index) => {
    let colTotal = 0;
    weightTableData.value.forEach(row => {
      colTotal += row[`objective_${obj.id}`] || 0;
    });
    
    if (Math.abs(colTotal - 100) > 0.1) {
      validationErrors.value.push(`目标${index + 1} 的权重总和应为100%，当前为${colTotal.toFixed(1)}%`);
    }
  });
};

const getTotalValue = (colKey: string) => {
  if (colKey === 'totalWeight') {
    return '100.0';
  }
  
  let total = 0;
  weightTableData.value.forEach(row => {
    total += row[colKey] || 0;
  });
  
  return total.toFixed(1);
};


// 添加选择的考核方式
const addSelectedMethods = async () => {
  try {
    if (selectedMethodsToAdd.value.length === 0) {
      MessagePlugin.warning('请至少选择或创建一种考核方式');
      return;
    }

    const newMethods: ExtendedAssessmentMethod[] = [];

    // 添加每个选择的方法
    selectedMethodsToAdd.value.forEach(methodId => {
      // 判断是否为自定义创建的方法
      if (methodId.startsWith('custom_')) {
        const methodName = createdMethodIds.value.get(methodId) || '';
        const newMethod: ExtendedAssessmentMethod = {
          id: methodId,
          name: methodName,
          weight: 100, // 默认权重100
         // description: '',
          isCustom: true
        };

        // 添加到数组
        newMethods.push(newMethod);
        
        const extendedMethod: ExtendedAssessmentMethod = {
          id: Date.now().toString(), // 动态生成一个唯一类型ID
          name: methodName, // 直接使用用户输入的名称
          isCustom: true // 标记为自定义
        };

        // 添加到全局方法列表和已选列表
        assessmentMethods.value.push(extendedMethod);
        selectedMethods.value.push(extendedMethod);
      } else {
        // 从预设方法中查找
        const presetMethod = presetMethods.value.find(m => m.id === methodId);
        if (presetMethod && !selectedMethods.value.some(m => m.id === methodId)) {
          const newMethod: ExtendedAssessmentMethod = {
            id: presetMethod.id,
            name: presetMethod.name,
            weight: presetMethod.weight,
         //   description: presetMethod.description,
            isCustom: false
          };
          
          newMethods.push(newMethod);
          selectedMethods.value.push(presetMethod);
        }
      }
    });

    // 将新方法添加到考核方式数组
    assessmentMethodData.value.push(...newMethods);

    // 重置对话框和更新表格
    //initWeightTableData();
    showAddMethodDialog.value = false;
    selectedMethodsToAdd.value = [];
    createdMethodIds.value.clear();
    inputValue.value = '';

    MessagePlugin.success(`考核方式添加成功，已添加 ${newMethods.length} 个考核方式`);

    // 自动保存到后端（使用统一保存函数）
    try {
      await saveAssessmentConfiguration('methods');
    } catch (saveError) {
      console.warn('自动保存失败，用户可稍后手动保存:', saveError);
      MessagePlugin.warning('考核方式添加成功，但自动保存失败，请稍后手动保存');
    }

  } catch (error) {
    console.error('添加考核方式失败:', error);
    MessagePlugin.error('添加失败，请重试');
  }
};

// 重置方法选择
const resetMethodSelection = () => {
  selectedMethodsToAdd.value = [];
  createdMethodIds.value.clear();
  inputValue.value = '';
};



// 移除考核方式
const removeMethod = async (methodId: string) => {
  const index = selectedMethods.value.findIndex(m => m.id === methodId);
  if (index > -1) {
    const removedMethod = selectedMethods.value[index];
    const methodName = removedMethod.name;

    try {
      // 从selectedMethods中移除
      selectedMethods.value.splice(index, 1);

      // 从assessmentMethodData数组中移除
      const methodIndex = assessmentMethodData.value.findIndex(m => m.name === methodName);
      if (methodIndex > -1) {
        assessmentMethodData.value.splice(methodIndex, 1);
      }

      // 从assessmentWeightData数组中移除相关数据
      const weightIndex = assessmentWeightData.value.findIndex(item => item.methodName === methodName);
      if (weightIndex > -1) {
        assessmentWeightData.value.splice(weightIndex, 1);
      }

      // 从assessmentProportionData数组中移除相关数据（查找对应的课程目标权重记录）
      const propoertionIndex = assessmentProportionData.value.findIndex(item => item.methodName === methodName);
      if (propoertionIndex > -1) {
        assessmentProportionData.value.splice(propoertionIndex, 1);
      }
      // 更新权重\占比，表1，表2表格数据
      weightTableData.value = assessmentWeightData.value;
      assessmentProportionData.value = assessmentProportionData.value;

      

      // 调用统一的保存函数同步到后端
      const saveSuccess = await saveAssessmentConfiguration('methods');

      if (saveSuccess) {
        MessagePlugin.success(`已移除考核方式并保存配置: ${methodName}`);
      } else {
        // 保存失败但前端数据已移除，提示用户
        MessagePlugin.warning(`考核方式"${methodName}"已从界面移除，但保存到后端失败，请稍后手动保存配置`);
      }

    } catch (error) {
      console.error('移除考核方式时保存配置失败:', error);
      // 即使保存失败，前端数据已经移除，只提示保存失败
      MessagePlugin.warning(`考核方式"${methodName}"已从界面移除，但保存到后端失败: ${error.message || '未知错误'}`);
    }
  }
};



// 获取方法名称
const getMethodName = (id: string): string => {
  // 如果是自定义创建的方法，从ID映射中获取名称
  if (id.startsWith('custom_')) {
    const customName = createdMethodIds.value.get(id);
    if (customName) {
      return customName;
    }
    // 如果映射中没有找到，尝试从已选方法中查找
    const selectedMethod = selectedMethods.value.find(m => m.id === id);
    if (selectedMethod) {
      return selectedMethod.name;
    }
    // 最后返回ID本身（不应该发生）
    return id;
  }
  // 否则从预设方法中查找
  const method = presetMethods.value.find(m => m.id === id);
  return method ? method.name : id;
};

// 创建新的考核方式时触发
const onCreateNewMethod = (value: any) => {
  // 处理可能的对象或简单值
  let customId: string;
  let stringValue: string;

  if (typeof value === 'object' && value !== null && 'value' in value && 'label' in value) {
    // TDesign的createOption返回的对象结构
    customId = value.value;
    stringValue = value.label;
    // createOption已经保存了映射，这里不需要重复保存
  } else {
    // 直接输入的情况（备用处理）
    stringValue = String(value).trim();
    if (!stringValue) return;
    customId = `custom_${Date.now()}`;
    // 保存ID和名称映射
    createdMethodIds.value.set(customId, stringValue);
  }

  // 确保customId已经在selectedMethodsToAdd中
  if (!selectedMethodsToAdd.value.includes(customId)) {
    selectedMethodsToAdd.value.push(stringValue);
  }

  // 清空输入框的值
  inputValue.value = '';
};

// 自定义选项创建函数
const createOption = (value: string): { value: string; label: string } => {
  const customId = `custom_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  // 预先保存ID和名称映射
  createdMethodIds.value.set(customId, value);
  // 返回正确的格式，防止显示前缀
  return {
    value: customId, // 作为内部值使用
    label: value // 显示给用户的文本
  };
};

// 判断是否为自定义方法
const isCustomMethod = (id: string): boolean => {
  return id && (id.startsWith('custom_') || id.startsWith('private_'));
};

// 获取课程目标对应的评价值
const getObjectiveValue = (item: any, objIndex: number) => {
  // 根据课程目标索引获取对应的评价值
  const objectiveKey = `co${objIndex + 1}`;
  return item[objectiveKey] || 0;
};

// 计算行总计
const calculateRowTotal = (item: any) => {
  let total = 0;
  for (let i = 0; i < courseObjectives.value.length; i++) {
    total += getObjectiveValue(item, i);
  }
  return total;
};

// 获取总计标签主题
const getTotalTagTheme = (item: any) => {
  const total = calculateRowTotal(item);
  return total === 100 ? 'success' : 'danger';
};

// 获取活跃的课程目标（评价值不为0的）
const getActiveObjectives = (item: any) => {
  const activeObjectives = [];
  for (let i = 0; i < courseObjectives.value.length; i++) {
    if (getObjectiveValue(item, i) > 0) {
      activeObjectives.push({ index: i, value: getObjectiveValue(item, i) });
    }
  }
  return activeObjectives;
};

// 获取课程目标标签主题
const getObjectiveTagTheme = (objIndex: number) => {
  const themes = ['primary', 'success', 'warning', 'danger', 'default'] as const;
  return themes[objIndex % themes.length];
};

// 显示指标点详情对话框
const showIndicatorDialog = (objective: any) => {
  currentIndicator.value = {
    id: objective.graduateTargetId || `${objective.id}.1`,
    title: `毕业要求指标点${objective.graduateTargetId || `${objective.id}.1`}`,
    description: objective.description || '课程目标描述',
    requirements: [
      '具备扎实的专业基础知识',
      '能够运用专业知识解决实际问题',
      '具有良好的沟通协调能力',
      '具备持续学习和创新能力'
    ],
    relatedObjective: objective.title || `课程目标${objective.id}`,
    weight: '20%',
    assessmentMethods: ['期末考试', '平时作业', '实验报告']
  };
  showIndicatorDetailDialog.value = true;
};

// 进入编辑模式（同时控制两个表格）
const enterEditMode = () => {
  isEditMode.value = true;
  // 创建表1数据副本用于编辑
  editingData.value = assessmentTableData.value;
  // 创建表2数据副本用于编辑
  editingTable2Data.value = weightDistributionData.value;

  MessagePlugin.info('已进入编辑模式，可同时编辑两个表格');
};

// 统一保存所有更改（表1和表2）
const saveAllChanges = async () => {
  // 验证表1数据
  const table1Errors: string[] = [];
  editingData.value.forEach((item, index) => {
    const total = calculateRowTotal(item);
    if (total !== 100) {
      table1Errors.push(`表1第${index + 1}行课程目标占比总和为${total}%，应为100%`);
    }
  });

  // 验证表2数据
  const table2Errors: string[] = [];

  // 验证课程目标列
  for (let i = 0; i < courseObjectives.value.length; i++) {
    const total = calculateTable2ColumnTotal(i);
    if (total !== 100) {
      table2Errors.push(`表2 CO${i + 1}列权重总和为${total}%，应为100%`);
    }
  }

  // 验证期末综合成绩权重列
  const finalWeightTotal = calculateTable2FinalWeightTotal();
  if (finalWeightTotal !== 100) {
    table2Errors.push(`表2期末综合成绩权重总和为${finalWeightTotal}%，应为100%`);
  }

  // 如果有验证错误，显示所有错误
  const allErrors = [...table1Errors, ...table2Errors];
  if (allErrors.length > 0) {
    MessagePlugin.error(`保存失败：\n${allErrors.join('\n')}`);
    return;
  }

  try {
    // 更新表1数据
    updateApiDataFromEditingData();
    // 更新表2数据
    updateApiDataFromTable2EditingData();

    // 调用统一的保存函数
    const success = await saveAssessmentConfiguration('all');

    if (success) {
      isEditMode.value = false;
      MessagePlugin.success('所有配置已保存成功');
    }
  } catch (error) {
    console.error('保存失败:', error);
    MessagePlugin.error('保存失败，请重试');
  }
};

// 验证行总计
const validateRowTotal = (item: any, index: number) => {
  const total = calculateRowTotal(item);
  if (total !== 100) {
    MessagePlugin.warning(`第${index + 1}行课程目标占比总和为${total}%，应为100%`);
  }
};

// 保存编辑更改
const saveEditTable1 = async () => {
  // 验证所有行的总和
  const errors: string[] = [];
  editingData.value.forEach((item, index) => {
    const total = calculateRowTotal(item);
    if (total !== 100) {
      errors.push(`第${index + 1}行课程目标占比总和为${total}%，应为100%`);
    }
  });

  if (errors.length > 0) {
    MessagePlugin.error(`保存失败：\n${errors.join('\n')}`);
    return;
  }

  try {
    // 将编辑后的数据转换回API数据格式
    updateApiDataFromEditingData();

    // 调用统一的保存函数
    const success = await saveAssessmentConfiguration('table1');

    if (success) {
      isEditMode.value = false;
    }
  } catch (error) {
    console.error('表1保存失败:', error);
    MessagePlugin.error('保存失败，请重试');
  }
};
// 将编辑后的数据editingData转换回API数据assessmentProportionData格式（表1）
const updateApiDataFromEditingData = () => {
  // 更新assessmentProportionData数组中的占比数据
  editingData.value.forEach(editedRow => {
    const methodId = editedRow.methodId;
    // 找到对应的assessmentProportionData记录
    const proportionIndex = assessmentProportionData.value.findIndex(item => item.methodId === methodId);
   
    if (proportionIndex > -1) {
      // 更新现有记录
      const proportion = assessmentProportionData.value[proportionIndex];
      if (!proportion.weightMap) {
        proportion.weightMap = {};
      }
      
      courseObjectives.value.forEach((objective, index) => {
        const coKey = `co${index + 1}`;
        const objectiveKey = objective.objectiveId || objective.title;
        proportion.weightMap![objectiveKey] = Number(editedRow[coKey] || 0);
        objective.weight = Number(editedRow[coKey] || 0);
      });
      
      // 更新评价说明
      proportion.description = editedRow.criteria || '';
    } else {
      // 创建新记录
      const newProportion: ExtendedCourseAssessmentConfig = {
        methodId: editedRow.methodId, // Add missing methodId property
        methodName: editedRow.methodName,
        examWeight: editedRow.totalWeight || 100,
        description: editedRow.criteria || '',
        isFinalExam: false,
        objectiveList: [],
        weightMap: {}
      };
      
      courseObjectives.value.forEach((objective, index) => {
        const coKey = `co${index + 1}`;
        const objectiveKey = objective.objectiveId || objective.title;
        newProportion.objectiveList!.push({
          objectiveId: objective.objectiveId,
          weight: Number(editedRow[coKey] || 0),
          objectiveName: objective.title,
          expectedScore:  expectedAchievements.value[`co${index + 1}`] || 60, // 默认期望值为80
          description: editedRow.criteria || ''
        });
        newProportion.weightMap![objectiveKey] = Number(editedRow[coKey] || 0);
      });
      
      assessmentProportionData.value.push(newProportion);
    } 
  });
 // console.log('更新后的占比数据:', assessmentProportionData.value);
};


// 取消编辑（同时清理两个表格的编辑数据）
const cancelEdit = () => {
  isEditMode.value = false;
  editingData.value = [];
  editingTable2Data.value = [];
  MessagePlugin.info('已取消编辑');
};

// 表2相关函数
// 获取表2课程目标权重值
const getTable2ObjectiveWeight = (item: any, objIndex: number) => {
  const weightKey = `co${objIndex + 1}Weight`;
  return item[weightKey] || 0;
};

// 获取支撑度主题
const getSupportLevelTheme = (weight: number) => {
  if (weight >= 70) return 'success';
  if (weight >= 30) return 'warning';
  return 'danger';
};

// 获取支撑度文本
const getSupportLevelText = (weight: number) => {
  if (weight >= 70) return '高';
  if (weight >= 30) return '中';
  return '低';
};

// 计算表2行总计
const calculateTable2RowTotal = (item: any) => {
  let total = 0;
  for (let i = 0; i < courseObjectives.value.length; i++) {
    total += getTable2ObjectiveWeight(item, i);
  }
  total += (item.finalWeight || 0);
  return total;
};

// 获取表2行总计标签主题
const getTable2RowTotalTheme = (item: any) => {
  const total = calculateTable2RowTotal(item);
  return total === 100 ? 'success' : 'danger';
};

// 计算表2列总计
const calculateTable2ColumnTotal = (objIndex: number) => {
  const data = isEditMode.value ? editingTable2Data.value : weightDistributionData.value;
  let total = 0;
  data.forEach(item => {
    total += getTable2ObjectiveWeight(item, objIndex);
  });
  return total;
};

// 获取表2列总计标签主题
const getTable2ColumnTotalTheme = (objIndex: number) => {
  const total = calculateTable2ColumnTotal(objIndex);
  return total === 100 ? 'success' : 'danger';
};

// 计算期末综合成绩权重总计
const calculateTable2FinalWeightTotal = () => {
  const data = isEditMode.value ? editingTable2Data.value : weightDistributionData.value;
  let total = 0;
  data.forEach(item => {
    total += (item.examWeight || 0);
  });
  return total;//courseObjectiveList!.value.length * 100 || 0; // 计算平均值
};

// 获取期末综合成绩权重总计标签主题
const getTable2FinalWeightTotalTheme = () => {
  const total = calculateTable2FinalWeightTotal();
  return total === 100 ? 'success' : 'danger';
};

// 表2编辑模式函数
// 进入表2编辑模式
// 已废弃：表2编辑模式已合并到统一编辑控制中
// const enterTable2EditMode = () => {
//   // 功能已合并到 enterEditMode 函数中
// };


// 保存表2编辑更改
const saveTable2Changes = async () => {
  // 验证所有列的总和
  const errors: string[] = [];

  // 验证课程目标列
  for (let i = 0; i < courseObjectives.value.length; i++) {
    const total = calculateTable2ColumnTotal(i);
    if (total !== 100) {
      errors.push(`CO${i + 1}列权重总和为${total}%，应为100%`);
    }
  }

  // 验证期末综合成绩权重列
  const finalWeightTotal = calculateTable2FinalWeightTotal();
  if (finalWeightTotal !== 100) {
    errors.push(`期末综合成绩权重总和为${finalWeightTotal}%，应为100%`);
  }

  if (errors.length > 0) {
    MessagePlugin.error(`保存失败：\n${errors.join('\n')}`);
    return;
  }

  try {
    // 将编辑后的表2数据转换回API数据格式
    updateApiDataFromTable2EditingData();


    // 调用统一的保存函数
    const success = await saveAssessmentConfiguration('table2');

    if (success) {
      // 表2编辑状态已合并到统一编辑控制中
      // isTable2EditMode.value = false;
    }
  } catch (error) {
    console.error('表2保存失败:', error);
    MessagePlugin.error('保存失败，请重试');
  }
};
// 将编辑后的表1数据editingTable2Data转换回API数据格式（表2）
const updateApiDataFromTable2EditingData = () => {
  // 更新assessmentWeightData数组中的权重数据
  console.log('更新表2中的数据-editingTable2Data:', editingTable2Data.value);
  editingTable2Data.value.forEach(editedRow => {
    const methodId = editedRow.methodId;
    // 找到对应的assessmentWeightData记录
    const weightIndex = assessmentWeightData.value.findIndex(item => item.methodId === methodId);
    if (weightIndex > -1) {
      // 更新现有记录
      const weightData = assessmentWeightData.value[weightIndex];
      if (!weightData.weightMap) {
        weightData.weightMap = {};
      }
      
      courseObjectives.value.forEach((objective, index) => {
        const coKey = `co${index + 1}Weight`;
        const objectiveKey = objective.objectiveId;
        weightData.weightMap[objectiveKey] = Number(editedRow[coKey] || 0);
        objective.expectedScore = expectedAchievements.value[`co${index + 1}`] || 60; // 默认期望值为80
   //     expectedAchievements.value[`co${index + 1}`] = editedRow.finalWeight || 60; // 默认期望值为80
        objective.weight = Number(editedRow[coKey] || 0);
      });
      
      // 更新评价说明
      weightData.description = editedRow.criteria || '';
      weightData.examWeight = editedRow.examWeight || 0;
     // expectedAchievements.value[`co${index + 1}`] = editedRow.finalWeight || 0;
    } else {
      // 创建新记录
      const newRecord: ExtendedCourseAssessmentConfig = {
        methodId: editedRow.methodId, // Add missing methodId property
        methodName: editedRow.methodName,
        examWeight: editedRow.examWeight || 100,
        description: editedRow.criteria || '',
        isFinalExam: false,
        objectiveList: [],
        weightMap: {}
      };
      
      courseObjectives.value.forEach((objective, index) => {
        const coKey = `co${index + 1}Weight`;
        const objectiveKey = objective.objectiveId || objective.title;
        newRecord.weightMap![objectiveKey] = Number(editedRow[coKey] || 0);
        newRecord.objectiveList!.push({
          objectiveId: objective.objectiveId,
          objectiveName: objective.title,
          weight: Number(editedRow[coKey] || 0),
          expectedScore: expectedAchievements.value[`co${index + 1}`] || 60 // 默认期望值为80
          
        });
       
        expectedAchievements.value[`co${index + 1}`] = objective.expectedScore || 60; // 默认期望值为80
      });
      
      assessmentWeightData.value.push(newRecord);
    }
    
  });
 // console.log('更新后的权重数据:', assessmentWeightData.value);
 // console.log('更新后的期望值数据:', expectedAchievements.value);
  
};


// 已废弃：表2取消编辑功能已合并到统一编辑控制中
// const cancelTable2Edit = () => {
//   // 功能已合并到 cancelEdit 函数中
// };


// 数据转换函数：将前端数据结构转换为CourseAssessmentDetailVO格式
const convertToAssessmentDataDTO = () => {
//  console.log('=== 开始转换数据为CourseAssessmentDetailVO格式 ===');
//  console.log('转换前的数据:', {
//    assessmentMethodData: assessmentMethodData.value,
//    assessmentWeightData: assessmentWeightData.value,
//    assessmentProportionData: assessmentProportionData.value,
//    assessmentWeightExamData: assessmentWeightExamData.value,
//  });

  try {
    // 构建CourseAssessmentDetailVO结构
    const assessmentDetailVO: CourseAssessmentDetailVO = {
      courseId: courseId,
      courseName: courseInfo.value?.courseName || '',
      courseObjectiveList: courseObjectiveList.value,
      
      assessmentMethods: assessmentMethodData.value.map(method => ({
        id: method.id,
        name: method.name,
        weight: method.weight,
        isCustom: method.isCustom || false,
        typeId: method.typeId || '',
        typeName: method.typeName || ''
      })
      ),
      assessmentWeight: assessmentWeightData.value.map(weight => ({
        methodId: weight.methodId || '',
        methodName: weight.methodName || '',
        examWeight: weight.examWeight || 0,
        description: weight.description || '',
        isFinalExam: weight.isFinalExam || false,
        objectiveList: Array.isArray(weight.objectiveList) ? weight.objectiveList.map(obj => ({
          objectiveId: obj.objectiveId || '',
          objectiveName: obj.objectiveName || '',
          weight: (weight.weightMap && weight.weightMap[obj.objectiveId]) || 0,
          expectedScore: Number(obj.expectedScore) || 0,
          description: typeof obj.description === 'string' ? obj.description : ''
        })) : []

      })),
      assessmentProportions: assessmentProportionData.value.map(weight => ({
        methodId: weight.methodId || '',
        methodName: weight.methodName || '',
        examWeight: weight.examWeight || 0,
        description: weight.description || '',
        isFinalExam: weight.isFinalExam || false,
        objectiveList: Array.isArray(weight.objectiveList) ? weight.objectiveList.map(obj => ({
          objectiveId: obj.objectiveId || '',
          objectiveName: obj.objectiveName || '',
          weight: (weight.weightMap && weight.weightMap[obj.objectiveId]) || 0,
          expectedScore: Number(obj.expectedScore) || 0,
          description: typeof obj.description === 'string' ? obj.description : ''
        })) : []
      })),
      finalExamWeights: assessmentWeightExamData.value.map(weight => ({
        methodId: weight.methodId || '',
        methodName: weight.methodName || '',
        examWeight: weight.examWeight || 0,
        description: weight.description || '',
        isFinalExam: true,
        objectiveList: []
      }))
    };

//    console.log('转换后的CourseAssessmentDetailVO:', assessmentDetailVO);
    return assessmentDetailVO;
  } catch (error) {
    console.error('数据转换失败:', error);
    throw new Error('数据转换失败: ' + error.message);
  }
};

// 统一的保存函数
const saveAssessmentConfiguration = async (saveType: 'table1' | 'table2' | 'methods' | 'all' = 'all') => {
  try {
    loading.value = true;

    // 转换数据格式
    const assessmentDataDTO = convertToAssessmentDataDTO();
    console.log('=== 准备保存的CourseAssessmentDetailVO数据 ===');
    console.log(assessmentDataDTO);

    // 调用统一的保存API
    await saveCourseAssessmentConfig(courseId, assessmentDataDTO);


    // 根据保存类型显示不同的成功消息
    const successMessages = {
      table1: '表1配置保存成功',
      table2: '表2配置保存成功',
      methods: '考核方式配置保存成功',
      all: '所有配置保存成功'
    };

    MessagePlugin.success(successMessages[saveType]);
    return true;

  } catch (error: any) {
    console.error('保存配置失败:', error);
    MessagePlugin.error(error.message || '保存配置失败，请重试');
    return false;
  } finally {
    loading.value = false;
  }
};


// 初始化达成度期望值数据
const initExpectedAchievements = () => {
 // console.log('=== 初始化达成度期望值数据 ===');
  // 加载期望值数据到编辑状态
 courseObjectiveList.value.forEach((objective, index) => {
    const coKey = `co${index + 1}`; 
    expectedAchievements.value[coKey] = objective?.expectedScore || 60; // 默认80分
  });
 // console.log('期望值数据初始化完成:', expectedAchievements.value);
};

// 生命周期
onMounted(async () => {
  // getDictDataByTypeTitle('考核环节')的结果数据转化到presetMethods
  const dictData = await getDictDataByTypeTitle('考核环节');
  presetMethods.value = dictData.map(item => ({
    id: String(item.value),
    name: item.label,
    weight: 100, // 默认权重100
    isCustom: false
  }));
  loadCourseInfo();
  //initExpectedAchievements();
});


// 表3：综合成绩评价标准数据
const evaluationStandards = ref([
  {
    gradeName: '优秀',
    scoreRange: '90-100',
    description: '全面掌握课程内容，能够熟练运用所学知识',
    criteria: '课程目标达成度≥90%，各项考核表现优异',
    achievementLevel: '优秀',
    achievementPercentage: 95,
    gradeClass: 'excellent'
  },
  {
    gradeName: '良好',
    scoreRange: '80-89',
    description: '较好掌握课程内容，能够运用所学知识',
    criteria: '课程目标达成度80%-89%，各项考核表现良好',
    achievementLevel: '良好',
    achievementPercentage: 85,
    gradeClass: 'good'
  },
  {
    gradeName: '中等',
    scoreRange: '70-79',
    description: '基本掌握课程内容，能够理解主要概念',
    criteria: '课程目标达成度70%-79%，各项考核表现中等',
    achievementLevel: '中等',
    achievementPercentage: 75,
    gradeClass: 'medium'
  },
  {
    gradeName: '及格',
    scoreRange: '60-69',
    description: '达到课程基本要求，掌握基础知识',
    criteria: '课程目标达成度60%-69%，各项考核达到及格要求',
    achievementLevel: '及格',
    achievementPercentage: 65,
    gradeClass: 'pass'
  },
  {
    gradeName: '不及格',
    scoreRange: '0-59',
    description: '未达到课程基本要求，需要重新学习',
    criteria: '课程目标达成度<60%，各项考核未达到要求',
    achievementLevel: '不及格',
    achievementPercentage: 40,
    gradeClass: 'fail'
  }
]);

// 成绩分布统计数据
const totalStudents = ref(120);
const gradeDistribution = ref([
  {
    gradeName: '优秀',
    count: 18,
    percentage: 15.0,
    gradeClass: 'excellent'
  },
  {
    gradeName: '良好',
    count: 36,
    percentage: 30.0,
    gradeClass: 'good'
  },
  {
    gradeName: '中等',
    count: 42,
    percentage: 35.0,
    gradeClass: 'medium'
  },
  {
    gradeName: '及格',
    count: 20,
    percentage: 16.7,
    gradeClass: 'pass'
  },
  {
    gradeName: '不及格',
    count: 4,
    percentage: 3.3,
    gradeClass: 'fail'
  }
]);
const handleEditStandards = () => {
  try {
    // TODO: 实现编辑功能
    MessagePlugin.info('进入评价标准编辑模式');
  } catch (error) {
    MessagePlugin.error('编辑功能暂不可用');
  }
};

// 处理导出评价标准
const handleExportStandards = () => {
  try {
    // TODO: 实现导出功能
    MessagePlugin.info('开始导出评价标准');
    console.log('导出评价标准数据:', {
      evaluationStandards: evaluationStandards.value,
      gradeDistribution: gradeDistribution.value,
      totalStudents: totalStudents.value,
      courseInfo: courseInfo.value
    });
  } catch (error) {
    console.error('导出评价标准失败:', error);
    MessagePlugin.error('导出功能暂不可用');
  }
};

</script>

<style lang="less" scoped>
.assessment-plan-container {
  min-height: 100vh;
  background: #f5f7fa;
  padding: 16px;

  // 顶部统计卡片
  .stats-cards {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 16px;
    margin-bottom: 24px;

    .stats-card {
      background: white;
      border-radius: 8px;
      padding: 20px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      border-left: 4px solid #e0e0e0;

      &.success {
        border-left-color: #52c41a;
      }

      .stats-content {
        .stats-title {
          font-size: 14px;
          color: #666;
          margin-bottom: 8px;
        }

        .stats-value {
          font-size: 24px;
          font-weight: 600;
          color: #333;
        }
      }
    }
  }

  // 主要内容区域
  .main-content {
    display: flex;
    flex-direction: column;
    gap: 28px;

    // 考核方式配置区域
    .assessment-config-section {
      background: white;
      border-radius: 8px;
      padding: 24px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

      .section-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        padding-bottom: 16px;
        border-bottom: 1px solid #f0f0f0;

        .section-title {
          font-size: 18px;
          font-weight: 600;
          color: #333;
          margin: 0;
        }

        .section-actions {
          display: flex;
          gap: var(--td-comp-margin-s);
          align-items: center;

          .t-button {
            display: flex;
            align-items: center;
            justify-content: center;

            .t-icon {
              margin-right: var(--td-comp-margin-xs);
            }
          }
        }
      }

      .method-selection {
        margin-bottom: 20px;

        .method-label {
          font-size: 14px;
          color: #666;
          margin-bottom: 8px;
        }

        .method-tags {
          display: flex;
          flex-wrap: wrap;
          gap: 8px;

          .method-tag {
            cursor: pointer;
            
            .custom-badge {
              font-size: 12px;
              opacity: 0.8;
              margin-left: 4px;
            }
          }
        }
      }

      .objective-selection {
        margin-bottom: 20px;

        .objective-label {
          font-size: 14px;
          color: #666;
          margin-bottom: 8px;
        }

        .objective-info {
          font-size: 14px;
          color: #999;
        }
      }

      .target-method-selection {
        margin-bottom: 20px;

        .target-cards {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
          gap: 16px;

          .target-card {
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 16px;
            background: #fafafa;

            .target-header {
              display: flex;
              align-items: center;
              gap: 8px;
              margin-bottom: 12px;

              .target-number {
                width: 24px;
                height: 24px;
                border-radius: 50%;
                background: #1890ff;
                color: white;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 12px;
                font-weight: 600;
              }

              .target-title {
                font-weight: 600;
                color: #333;
                flex: 1;
              }
            }

            .target-content {
              display: flex;
              justify-content: space-between;
              align-items: center;

              .target-description {
                font-size: 14px;
                color: #666;
                flex: 1;
              }
            }
          }
        }
      }

      .config-tip {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 12px 16px;
        background: #f6ffed;
        border: 1px solid #b7eb8f;
        border-radius: 6px;
        font-size: 14px;
        color: #52c41a;
      }
    }

    // 考核表格区域
    .assessment-table-section,
    .weight-distribution-section,
    .comprehensive-evaluation-section {
      background: var(--td-bg-color-container);
      border-radius: var(--td-radius-default);
      padding: var(--td-comp-paddingTB-xl) var(--td-comp-paddingLR-xl);
      box-shadow: var(--td-shadow-1);
      border: 1px solid var(--td-border-level-1-color);

      .table-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        padding-bottom: 16px;
        border-bottom: 1px solid #f0f0f0;

        .table-title {
          font-size: 18px;
          font-weight: 600;
          color: #333;
          margin: 0;
        }

        .table-status {
          padding: 4px 12px;
          border-radius: 12px;
          font-size: 12px;
          background: #e6f7ff;
          color: #1890ff;
          border: 1px solid #91d5ff;
        }

        .table-actions {
          display: flex;
          gap: 8px;
        }
      }

      .config-table,
      .distribution-table,
      .standards-table {
        width: 100%;
        border-collapse: collapse;
        font-size: var(--td-font-size-body-medium);
        color: var(--td-text-color-primary);

        th,
        td {
          border: 1px solid var(--td-border-level-1-color);
          padding: var(--td-comp-paddingTB-m) var(--td-comp-paddingLR-s);
          text-align: center;
          vertical-align: middle;
        }

        th {
          background: var(--td-bg-color-container-hover);
          font-weight: var(--td-font-weight-semi-bold);
          color: var(--td-text-color-primary);

          .sub-title {
            font-size: 12px;
            color: #999;
            font-weight: normal;
          }
        }

        td {
          background: white;

          &.weight-cell {
            .weight-display {
              display: flex;
              align-items: center;
              gap: 8px;

              .weight-value {
                font-weight: 600;
                color: #333;
              }

              .weight-bar {
                flex: 1;
                height: 6px;
                background: #f0f0f0;
                border-radius: 3px;
                overflow: hidden;

                .weight-progress {
                  height: 100%;
                  background: linear-gradient(90deg, #52c41a 0%, #73d13d 100%);
                  transition: width 0.3s ease;
                }
              }
            }

            .weight-value {
              font-weight: 600;
              color: #333;
            }

            .weight-indicator {
              margin-top: 4px;
              font-size: 12px;

              &.high-weight {
                color: #f5222d;
              }

              &.medium-weight {
                color: #fa8c16;
              }

              &.low-weight {
                color: #52c41a;
              }
            }
          }
        }

        .total-row {
          background: #f0f9ff;
          font-weight: 600;

          .total-cell {
            color: #1890ff;
            font-weight: 700;
          }
        }
      }

      // 权重统计卡片
      .weight-summary-cards {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 16px;
        margin: 24px 0;

        .summary-card {
          background: #fafafa;
          border: 1px solid #e0e0e0;
          border-radius: 8px;
          padding: 16px;
          text-align: center;

          .summary-title {
            font-size: 12px;
            color: #666;
            margin-bottom: 8px;
          }

          .summary-value {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 4px;

            &.status-normal {
              color: #52c41a;
            }

            &.status-warning {
              color: #fa8c16;
            }

            &.status-error {
              color: #f5222d;
            }
          }

          .summary-status {
            font-size: 12px;

            &.status-normal {
              color: #52c41a;
            }

            &.status-warning {
              color: #fa8c16;
            }

            &.status-error {
              color: #f5222d;
            }
          }
        }
      }

      // 权重校验提示
      .weight-validation {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 12px 16px;
        background: #f6ffed;
        border: 1px solid #b7eb8f;
        border-radius: 6px;
        font-size: 14px;
        color: #52c41a;
        margin-top: 16px;
      }

      // 课程目标卡片样式优化
      .target-card {
        .target-indicator {
          margin-top: var(--td-comp-margin-s);
          display: flex;
          justify-content: flex-end;
        }
      }

      // 表格单元格样式优化
      .total-cell {
        text-align: center;
      }

      .objectives-cell {
        .t-space {
          justify-content: center;
        }
      }

      // 课程目标列间距优化
      .objective-header {
        border-left: 2px solid var(--td-border-level-2-color);
        border-right: 2px solid var(--td-border-level-2-color);
        background: linear-gradient(135deg, var(--td-bg-color-container-hover) 0%, var(--td-bg-color-container) 100%);
        font-weight: var(--td-font-weight-bold);
        color: var(--td-brand-color);
      }

      .objective-cell {
        border-left: 1px solid var(--td-border-level-2-color);
        border-right: 1px solid var(--td-border-level-2-color);
        padding: var(--td-comp-paddingTB-m) var(--td-comp-paddingLR-m);
        position: relative;

        &:first-of-type {
          border-left: 2px solid var(--td-border-level-1-color);
        }

        &:last-of-type {
          border-right: 2px solid var(--td-border-level-1-color);
        }
      }

      // 编辑模式样式
      .edit-mode-row {
        background: rgba(var(--td-warning-color-rgb), 0.05);
        border: 1px solid var(--td-warning-color-3);

        &:hover {
          background: rgba(var(--td-warning-color-rgb), 0.08);
        }
      }

      .objective-input {
        width: 100%;

        :deep(.t-input__inner) {
          text-align: center;
          font-weight: var(--td-font-weight-semi-bold);
        }
      }

      .criteria-cell {
        max-width: 200px;
      }

      .criteria-input {
        width: 100%;

        :deep(.t-textarea__inner) {
          font-size: var(--td-font-size-body-small);
          line-height: 1.4;
        }
      }

      // 表2专用样式
      .objective-weight-header {
        border-left: 2px solid var(--td-border-level-2-color);
        border-right: 2px solid var(--td-border-level-2-color);
        background: linear-gradient(135deg, var(--td-success-color-1) 0%, var(--td-success-color-2) 100%);
        font-weight: var(--td-font-weight-bold);
        color: var(--td-success-color-8);

        .sub-title {
          font-size: var(--td-font-size-body-small);
          font-weight: var(--td-font-weight-normal);
          color: var(--td-success-color-6);
        }
      }

      .weight-cell {
        border-left: 1px solid var(--td-border-level-2-color);
        border-right: 1px solid var(--td-border-level-2-color);
        padding: var(--td-comp-paddingTB-m) var(--td-comp-paddingLR-m);

        .weight-display-container {
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: var(--td-comp-margin-xs);

          .weight-value {
            font-weight: var(--td-font-weight-semi-bold);
            color: var(--td-text-color-primary);
            font-size: var(--td-font-size-body-medium);
          }

          .support-tag {
            font-size: var(--td-font-size-body-small);
          }
        }

        .weight-edit-container {
          display: flex;
          justify-content: center;

          .weight-input {
            width: 80px;

            :deep(.t-input__inner) {
              text-align: center;
              font-weight: var(--td-font-weight-semi-bold);
            }
          }
        }
      }

      .final-weight-cell {
        background: rgba(var(--td-warning-color-rgb), 0.05);
        border-left: 2px solid var(--td-warning-color-3);
        border-right: 2px solid var(--td-warning-color-3);

        .weight-display-container {
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: var(--td-comp-margin-xs);

          .weight-value {
            font-weight: var(--td-font-weight-bold);
            color: var(--td-warning-color-8);
            font-size: var(--td-font-size-body-medium);
          }
        }
      }

      .total-row {
        background: var(--td-bg-color-container-hover);
        font-weight: var(--td-font-weight-semi-bold);

        td {
          border-top: 2px solid var(--td-border-level-1-color);
          padding: var(--td-comp-paddingTB-l) var(--td-comp-paddingLR-m);
        }
      }

      // 评价说明列样式
      .evaluation-description-cell {
        max-width: 300px;
        min-width: 200px;

        .evaluation-description-input {
          width: 100%;

          :deep(.t-textarea__inner) {
            font-size: var(--td-font-size-body-small);
            line-height: 1.4;
          }
        }

        .evaluation-description-text {
          color: var(--td-text-color-secondary);
          font-size: var(--td-font-size-body-small);
          line-height: 1.5;
          word-break: break-word;
        }
      }

      // 达成度期望值行样式
      .expected-achievement-row {
        background: rgba(var(--td-brand-color-rgb), 0.05);
        font-weight: var(--td-font-weight-semi-bold);

        td {
          border-top: 1px solid var(--td-border-level-2-color);
          padding: var(--td-comp-paddingTB-m) var(--td-comp-paddingLR-m);
        }
      }

      .expected-cell {
        .expected-edit-container {
          display: flex;
          align-items: center;
          justify-content: center;
          gap: var(--td-comp-margin-xs);

          .expected-input {
            width: 70px;

            :deep(.t-input__inner) {
              text-align: center;
              font-weight: var(--td-font-weight-semi-bold);
            }
          }

          .percent-symbol {
            color: var(--td-text-color-secondary);
            font-size: var(--td-font-size-body-small);
          }
        }

        .expected-display-container {
          display: flex;
          justify-content: center;

          .expected-value {
            font-weight: var(--td-font-weight-semi-bold);
            color: var(--td-brand-color);
            font-size: var(--td-font-size-body-medium);
          }
        }
      }

      // 表1课程目标标签优化
      .objectives-cell {
        .t-space {
          justify-content: center;
        }

        .objective-tag {
          margin: 2px;
          transition: all 0.3s ease;

          &:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
          }
        }
      }

      // 表3专用样式
      .standards-table {
        .grade-cell {
          .grade-info {
            display: flex;
            align-items: center;
            gap: 8px;

            .grade-name {
              font-weight: 600;
              color: #333;
            }

            .grade-indicator {
              width: 12px;
              height: 12px;
              border-radius: 50%;

              &.excellent {
                background: #52c41a;
              }

              &.good {
                background: #1890ff;
              }

              &.medium {
                background: #fa8c16;
              }

              &.pass {
                background: #faad14;
              }

              &.fail {
                background: #f5222d;
              }
            }
          }
        }

        .score-range {
          font-weight: 600;
          color: #1890ff;
        }

        .description {
          color: #666;
          line-height: 1.5;
        }

        .criteria {
          color: #333;
          line-height: 1.5;
        }

        .achievement-level {
          .achievement-display {
            display: flex;
            align-items: center;
            gap: 8px;

            .achievement-value {
              font-weight: 600;
              color: #333;
            }

            .achievement-bar {
              flex: 1;
              height: 6px;
              background: #f0f0f0;
              border-radius: 3px;
              overflow: hidden;

              .achievement-progress {
                height: 100%;
                background: linear-gradient(90deg, #52c41a 0%, #73d13d 100%);
                transition: width 0.3s ease;
              }
            }
          }
        }

        // 不同等级的行样式
        tr {
          &.excellent {
            background: rgba(82, 196, 26, 0.05);
          }

          &.good {
            background: rgba(24, 144, 255, 0.05);
          }

          &.medium {
            background: rgba(250, 140, 22, 0.05);
          }

          &.pass {
            background: rgba(250, 173, 20, 0.05);
          }

          &.fail {
            background: rgba(245, 34, 45, 0.05);
          }
        }
      }

      // 评价标准说明
      .evaluation-notes {
        margin-top: 24px;
        padding: 16px;
        background: #f8f9fa;
        border-radius: 8px;
        border-left: 4px solid #1890ff;

        .notes-header {
          display: flex;
          align-items: center;
          gap: 8px;
          font-weight: 600;
          color: #333;
          margin-bottom: 12px;
        }

        .notes-content {
          ul {
            margin: 0;
            padding-left: 20px;
            color: #666;

            li {
              margin-bottom: 8px;
              line-height: 1.5;

              &:last-child {
                margin-bottom: 0;
              }
            }
          }
        }
      }

      // 成绩分布统计
      .grade-distribution {
        margin-top: 32px;

        .distribution-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 16px;

          h4 {
            margin: 0;
            font-size: 16px;
            font-weight: 600;
            color: #333;
          }

          .distribution-summary {
            font-size: 14px;
            color: #666;
          }
        }

        .distribution-cards {
          display: grid;
          grid-template-columns: repeat(5, 1fr);
          gap: 16px;

          .distribution-card {
            background: #fafafa;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 16px;
            text-align: center;
            transition: all 0.3s ease;

            &:hover {
              box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
              transform: translateY(-2px);
            }

            &.excellent {
              border-left: 4px solid #52c41a;
            }

            &.good {
              border-left: 4px solid #1890ff;
            }

            &.medium {
              border-left: 4px solid #fa8c16;
            }

            &.pass {
              border-left: 4px solid #faad14;
            }

            &.fail {
              border-left: 4px solid #f5222d;
            }

            .card-header {
              display: flex;
              justify-content: space-between;
              align-items: center;
              margin-bottom: 12px;

              .grade-name {
                font-weight: 600;
                color: #333;
              }

              .grade-count {
                font-size: 18px;
                font-weight: 700;
                color: #1890ff;
              }
            }

            .card-content {
              .percentage-display {
                margin-bottom: 8px;

                .percentage-value {
                  font-size: 14px;
                  font-weight: 600;
                  color: #666;
                }
              }

              .progress-bar {
                height: 6px;
                background: #f0f0f0;
                border-radius: 3px;
                overflow: hidden;

                .progress-fill {
                  height: 100%;
                  background: linear-gradient(90deg, #1890ff 0%, #40a9ff 100%);
                  transition: width 0.3s ease;
                }
              }
            }
          }
        }
      }
    }
  }

  // 考核方式选择器样式
  .assessment-method-selector {
    padding: 16px 0;

    .selector-title {
      font-size: 16px;
      font-weight: 600;
      margin-bottom: 8px;
      color: #333;
    }

    .selector-description {
      color: #666;
      margin-bottom: 16px;
      font-size: 14px;
    }

    .selector-content {
      margin-bottom: 16px;
    }

    .selected-methods-preview {
      background: #f5f7fa;
      padding: 12px;
      border-radius: 6px;
      margin-top: 16px;

      h5 {
        font-size: 14px;
        margin-top: 0;
        margin-bottom: 8px;
        color: #666;
      }

      .preview-tag {
        margin-right: 8px;
        margin-bottom: 8px;
        
        .custom-badge {
          font-size: 12px;
          opacity: 0.8;
          margin-left: 4px;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .assessment-plan-container {
    padding: 8px;

    .stats-cards {
      grid-template-columns: repeat(2, 1fr);
      gap: 12px;
    }

    .main-content {
      gap: 20px;

      .assessment-config-section,
      .assessment-table-section,
      .weight-distribution-section,
      .comprehensive-evaluation-section {
        padding: 16px;
      }

      .target-cards {
        grid-template-columns: 1fr;
      }

      .weight-summary-cards {
        grid-template-columns: repeat(2, 1fr);
      }

      .config-table,
      .distribution-table,
      .standards-table {
        font-size: 12px;

        th,
        td {
          padding: 8px 4px;
        }
      }

      .distribution-cards {
        grid-template-columns: repeat(2, 1fr);
      }

      .evaluation-notes {
        padding: 12px;
      }

      // 移动端编辑模式优化
      .objective-cell {
        padding: var(--td-comp-paddingTB-s) var(--td-comp-paddingLR-xs);
      }

      .objective-input {
        :deep(.t-input__inner) {
          font-size: 12px;
          padding: 4px 6px;
        }
      }

      .criteria-input {
        :deep(.t-textarea__inner) {
          font-size: 11px;
          padding: 4px 6px;
        }
      }

      // 表2移动端优化
      .weight-cell {
        padding: var(--td-comp-paddingTB-s) var(--td-comp-paddingLR-xs);

        .weight-input {
          width: 60px;

          :deep(.t-input__inner) {
            font-size: 12px;
            padding: 4px 6px;
          }
        }

        .weight-value {
          font-size: var(--td-font-size-body-small);
        }

        .support-tag {
          font-size: 10px;
          padding: 2px 4px;
        }
      }

      .final-weight-cell {
        padding: var(--td-comp-paddingTB-s) var(--td-comp-paddingLR-xs);
      }

      // 评价说明列移动端优化
      .evaluation-description-cell {
        max-width: 200px;
        min-width: 150px;

        .evaluation-description-input {
          :deep(.t-textarea__inner) {
            font-size: 11px;
            padding: 4px 6px;
          }
        }

        .evaluation-description-text {
          font-size: 11px;
        }
      }

      // 达成度期望值移动端优化
      .expected-cell {
        .expected-edit-container {
          .expected-input {
            width: 50px;

            :deep(.t-input__inner) {
              font-size: 12px;
              padding: 4px 6px;
            }
          }

          .percent-symbol {
            font-size: 10px;
          }
        }

        .expected-display-container {
          .expected-value {
            font-size: var(--td-font-size-body-small);
          }
        }
      }

      // 表1课程目标标签移动端优化
      .objectives-cell {
        .objective-tag {
          font-size: 10px;
          padding: 2px 4px;
          margin: 1px;
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .assessment-plan-container {
    .stats-cards {
      grid-template-columns: 1fr;
    }

    .weight-summary-cards {
      grid-template-columns: 1fr;
    }

    .distribution-cards {
      grid-template-columns: 1fr;
    }
  }
}

// 指标点详情对话框样式
.indicator-detail-dialog {
  :deep(.t-dialog__body) {
    padding: var(--td-comp-paddingTB-xl) var(--td-comp-paddingLR-xl);
  }
}

.indicator-content {
  .indicator-basic {
    margin-bottom: var(--td-comp-margin-xl);
    padding: var(--td-comp-paddingTB-l) var(--td-comp-paddingLR-l);
    background: var(--td-bg-color-container-hover);
    border-radius: var(--td-radius-default);

    .info-row {
      display: flex;
      align-items: center;
      margin-bottom: var(--td-comp-margin-m);

      &:last-child {
        margin-bottom: 0;
      }

      .info-label {
        font-weight: var(--td-font-weight-semi-bold);
        color: var(--td-text-color-primary);
        margin-right: var(--td-comp-margin-m);
        min-width: 120px;
      }
    }
  }

  .indicator-description,
  .indicator-requirements,
  .indicator-assessment {
    margin-bottom: var(--td-comp-margin-xl);

    h4 {
      margin: 0 0 var(--td-comp-margin-m) 0;
      font-size: var(--td-font-size-title-medium);
      font-weight: var(--td-font-weight-semi-bold);
      color: var(--td-text-color-primary);
      border-left: 4px solid var(--td-brand-color);
      padding-left: var(--td-comp-paddingLR-m);
    }

    p {
      margin: 0;
      line-height: 1.6;
      color: var(--td-text-color-secondary);
    }

    ul {
      margin: 0;
      padding-left: var(--td-comp-paddingLR-xl);
      color: var(--td-text-color-secondary);

      li {
        margin-bottom: var(--td-comp-margin-s);
        line-height: 1.6;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }

  .indicator-assessment {
    margin-bottom: 0;
  }

  // 空状态样式
  .empty-state {
    text-align: center;
    padding: 60px 20px;

    .empty-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 16px;

      p {
        margin: 0;
        color: var(--td-text-color-secondary);

        &.empty-tip {
          font-size: 12px;
          color: var(--td-text-color-placeholder);
        }
      }
    }
  }

  // 课程目标空状态样式
  .target-empty-state {
    text-align: center;
    padding: var(--td-comp-paddingTB-xxl) var(--td-comp-paddingLR-xl);
    background: var(--td-bg-color-container);
    border: 1px dashed var(--td-border-level-2-color);
    border-radius: var(--td-radius-default);
    margin: var(--td-comp-margin-xl) 0;

    .empty-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      max-width: 400px;
      margin: 0 auto;

      .empty-icon {
        margin-bottom: var(--td-comp-margin-xl);
        opacity: 0.6;
      }

      .empty-text {
        margin-bottom: var(--td-comp-margin-xxl);

        .empty-title {
          margin: 0 0 var(--td-comp-margin-m) 0;
          font-size: 16px;
          font-weight: 500;
          color: var(--td-text-color-primary);
          line-height: 1.5;
        }

        .empty-description {
          margin: 0 0 var(--td-comp-margin-s) 0;
          font-size: 14px;
          color: var(--td-text-color-secondary);
          line-height: 1.5;
        }

        .empty-tip {
          margin: 0;
          font-size: 12px;
          color: var(--td-text-color-placeholder);
          line-height: 1.4;
        }
      }

      .empty-actions {
        display: flex;
        gap: var(--td-comp-margin-m);
        justify-content: center;
      }
    }
  }

  // 考核方式选择区域样式（使用统一的assessment-table-section样式）
  .method-selection-section {
    .method-selection-content {
      .method-selection {
        .method-label {
          font-size: 14px;
          font-weight: 500;
          color: var(--td-text-color-primary);
          margin-bottom: var(--td-comp-margin-m);
        }

        .method-tags {
          display: flex;
          flex-wrap: wrap;
          gap: var(--td-comp-margin-m) var(--td-comp-margin-s);
          min-height: 40px;
          align-items: flex-start;
          padding: var(--td-comp-paddingTB-s) 0;

          .method-tag {
            margin: 0; // 确保标签本身没有额外的margin

            .custom-badge {
              margin-left: 4px;
              font-size: 12px;
              opacity: 0.8;
            }
          }
        }
      }

      // 考核方式空状态样式（与课程目标空状态保持一致）
      .method-empty-state {
        text-align: center;
        padding: var(--td-comp-paddingTB-xxl) var(--td-comp-paddingLR-xl);
        background: var(--td-bg-color-container);
        border: 1px dashed var(--td-border-level-2-color);
        border-radius: var(--td-radius-default);
        margin: var(--td-comp-margin-xl) 0;

        .empty-content {
          display: flex;
          flex-direction: column;
          align-items: center;
          max-width: 400px;
          margin: 0 auto;

          .empty-icon {
            margin-bottom: var(--td-comp-margin-xl);
            opacity: 0.6;
          }

          .empty-text {
            margin-bottom: var(--td-comp-margin-xxl);

            .empty-title {
              margin: 0 0 var(--td-comp-margin-m) 0;
              font-size: 16px;
              font-weight: 500;
              color: var(--td-text-color-primary);
              line-height: 1.5;
            }

            .empty-description {
              margin: 0 0 var(--td-comp-margin-s) 0;
              font-size: 14px;
              color: var(--td-text-color-secondary);
              line-height: 1.5;
            }

            .empty-tip {
              margin: 0;
              font-size: 12px;
              color: var(--td-text-color-placeholder);
              line-height: 1.4;
            }
          }

          .empty-actions {
            display: flex;
            gap: var(--td-comp-margin-m);
            justify-content: center;
          }
        }
      }
    }
  }
}
</style>

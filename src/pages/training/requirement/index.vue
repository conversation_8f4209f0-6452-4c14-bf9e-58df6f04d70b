<template>
  <div class="graduation-requirements-container">
    <!-- 顶部标题区域 -->
    <div class="header-section">
      <div class="header-content">
        <div class="title-wrapper">
          <h1 class="page-title">
            {{ pageTitle }}
          </h1>
        </div>
        <p class="page-subtitle">专业人才培养方案核心内容</p>
      </div>
    </div>

    <!-- 统计信息卡片 -->
    <div class="stats-cards">
      <t-card class="stat-card" hover-shadow>
        <div class="stat-content">
          <div class="stat-icon total-icon">
            <file-icon />
          </div>
          <div class="stat-info">
            <div class="stat-value">{{ requirements.length }}</div>
            <div class="stat-label">总毕业要求</div>
          </div>
        </div>
      </t-card>

      <t-card class="stat-card" hover-shadow>
        <div class="stat-content">
          <div class="stat-icon sub-icon">
            <list-icon />
          </div>
          <div class="stat-info">
            <div class="stat-value">{{ totalSubRequirements }}</div>
            <div class="stat-label">子要求总数</div>
          </div>
        </div>
      </t-card>

      <t-card class="stat-card" hover-shadow>
        <div class="stat-content">
          <div class="stat-icon recent-icon">
            <time-icon />
          </div>
          <div class="stat-info">
            <div class="stat-value">{{ lastUpdated }}</div>
            <div class="stat-label">最后更新</div>
          </div>
        </div>
      </t-card>
    </div>

    <!-- Plan 和 Standard 信息展示 -->
    <t-card title="培养计划与标准信息" style="margin-bottom: 24px;" bordered>
      <t-row :gutter="[16, 24]">
        <t-col :span="12">
          <div style="font-size: 16px; font-weight: 600; margin-bottom: 12px;">培养计划信息</div>
          <t-descriptions v-if="plan" :column="1" size="small" bordered>
            <t-descriptions-item label="计划名称">{{ plan.planName }}</t-descriptions-item>
            <t-descriptions-item label="计划版本">{{ plan.planVersion }}</t-descriptions-item>
            <t-descriptions-item label="创建时间">{{ plan.createTime }}</t-descriptions-item>
          </t-descriptions>
          <t-empty v-else description="暂无培养计划信息" />
        </t-col>
        <t-col :span="12">
          <div style="font-size: 16px; font-weight: 600; margin-bottom: 12px;">关联标准信息</div>
          <t-descriptions v-if="standard" :column="1" size="small" bordered>
            <t-descriptions-item label="标准名称">{{ standard.standardName }}</t-descriptions-item>
            <t-descriptions-item label="标准版本">{{ standard.standardVersion }}</t-descriptions-item>
            <t-descriptions-item label="学科类型">{{ enumData?.map?.disciplineType?.[standard.disciplineType] || standard.disciplineType }}</t-descriptions-item>
            <t-descriptions-item label="发布日期">{{ standard.releaseDate }}</t-descriptions-item>
          </t-descriptions>
          <t-empty v-else description="暂无关联标准信息" />
        </t-col>
      </t-row>
    </t-card>

    <!-- 操作工具栏 -->
    <div class="toolbar">
      <t-input
        v-model="searchText"
        placeholder="搜索毕业要求..."
        clearable
        class="search-input"
      >
        <template #prefix-icon>
          <search-icon />
        </template>
      </t-input>
    </div>

    <div v-if="standard && standard.requirements && standard.requirements.length" style="margin-top: 24px;">
      <t-space direction="vertical" size="large" style="width: 100%;">
        <t-card
          v-for="(req, idx) in standard.requirements"
          :key="req.id"
          :header="`${idx + 1}. ${req.standardName}`"
          style="width: 100%; margin-bottom: 16px;"
          bordered
        >
          <t-descriptions :column="3" size="small" bordered>
            <t-descriptions-item label="要求描述">
              {{ req.standardDescription }}
            </t-descriptions-item>
          </t-descriptions>
          
          <div style="margin-top: 16px;">
            <div style="font-weight: 500; margin-bottom: 8px; margin-top: 8px;">本专业要求描述</div>
            <t-textarea
                v-model="poDescriptions[req.id]"
                :value="mapReq[req.id]?.poDescription || ''"
                placeholder="填写本专业对该毕业要求的具体描述"
                @blur="() => {
                  const currentReqData = mapReq[req.id] || { requirementId: req.id, isRequirement: true, planId: plan.id, poTitle: idx }
                  onUpdateProfessionalDescription({
                    ...currentReqData,
                    poDescription: poDescriptions[req.id] || ''
                  })
                }"
                :autosize="{ minRows: 2, maxRows: 5 }"
                style="width: 100%;"
              />
            <div style="font-weight: 500; margin-bottom: 8px; margin-top: 8px;">分解指标点</div>
            <t-list v-if="poMap[req.id] && poMap[req.id].length" split size="small">
              <t-list-item
              v-for="po in poMap[req.id].filter(po => !po.isRequirement)"
              :key="po.id"
            >
              <t-list-item-meta
                :title="`${po.poTitle || po.name}`"
                :description="po.poDescription || po.description"
              />
              <template #action>
                <t-space size="small">
                  <t-button size="small" variant="text" theme="primary" @click="onEditPo(req, po)">
                    <template #icon><edit-icon /></template>
                    编辑
                  </t-button>
                  <t-button size="small" variant="text" theme="danger" @click="onDeletePo(req, po)">
                    <template #icon><delete-icon /></template>
                    删除
                  </t-button>
                </t-space>
              </template>
            </t-list-item>
            </t-list>
            <t-empty v-else description="暂无分解指标点" />
            <div style="margin-top: 8px;">
              <t-button size="small" variant="dashed" @click="onAddPo(req)">
                <template #icon><t-icon name="add" /></template>
                添加指标点
              </t-button>
            </div>
          </div>
        </t-card>
      </t-space>
    </div>

    <!-- 指标点编辑弹窗 -->
    <t-dialog
      v-model:visible="poDialogVisible"
      :header="poDialogTitle"
      :footer="false"
      width="480px"
    >
      <t-alert v-if="currentEditingRequirement" theme="info" style="margin-bottom: 16px;">
        当前毕业要求：{{ currentEditingRequirement.standardName }}
      </t-alert>
      <t-form :data="poForm" :rules="poFormRules" @submit="handlePoFormSubmit">
        <t-form-item label="名称" name="poTitle">
          <t-input v-model="poForm.poTitle" placeholder="请输入指标点名称" />
        </t-form-item>
        <t-form-item label="描述" name="poDescription">
          <t-textarea v-model="poForm.poDescription" placeholder="请输入描述" :autosize="{ minRows: 2, maxRows: 5 }" />
        </t-form-item>
        <t-form-item>
          <t-space>
            <t-button theme="primary" type="submit">确认</t-button>
            <t-button theme="default" variant="base" @click="poDialogVisible = false">取消</t-button>
          </t-space>
        </t-form-item>
      </t-form>
    </t-dialog>
  </div>
</template>

<script setup lang="ts">
import {ref, computed, onMounted } from 'vue'
import { MessagePlugin } from 'tdesign-vue-next'
import { useRoute } from 'vue-router'
import { getPlan } from '@/api/training/plan'
import {
  DownloadIcon,
  UploadIcon,
  EditIcon,
  DeleteIcon,
  AddIcon,
  ChevronDownIcon,
  CloseIcon,
  FileIcon,
  ListIcon,
  TimeIcon,
  SearchIcon
} from 'tdesign-icons-vue-next'
import {
  getGraduationStandardList,
  getGraduationStandardDetail
} from '@/api/base/standard';
import { getPoList, addPo, updatePo, deletePo } from '@/api/training/po'
import { getEnum } from '@/api/system/enum'

const route = useRoute()
const id = ref(Number(route.query.id))
const plan = ref<any>(null)

const standard = ref<any>(null)

const poDescriptions = ref<Record<string, string>>({})

const poMap = ref<Record<string, any[]>>({})
const mapReq = ref<Record<string, any>>({})



// 弹窗相关
const poDialogVisible = ref(false)
const poDialogTitle = ref('')

const emptyPoForm = { planId: id.value, poTitle: '', poDescription: '', isRequirement: false,requirementId: null, id: null } as any

const poForm = ref<any>(emptyPoForm)
const poFormRules = {
  poTitle: [{ required: true, message: '请输入名称', trigger: 'blur' as const }],
  poDescription: [{ required: true, message: '请输入描述', trigger: 'blur' as const }]
}

const enumData = ref<any>(null)

const currentEditingRequirement = ref<any>(null)

onMounted(() => {
  if (!id.value) {
    MessagePlugin.warning('未提供培养方案ID，无法加载毕业要求数据。');
    return
  }
  getPlan(id.value).then(res => {
    plan.value = res.data
    const standardId = plan.value.standardId
    getGraduationStandardDetail(standardId).then(res => {
      standard.value = res.data
      requirements.value = res.data.requirements
      loadAllPo()
    })
  })

  getEnum().then(res => {
    enumData.value = res.data
  })

})

// 页面标题
const pageTitle = ref('毕业要求')
const updateTitle = (e: Event) => {
  pageTitle.value = (e.target as HTMLElement).innerText
}

// 搜索功能
const searchText = ref('')

// 统计信息
const lastUpdated = ref('今天')
const totalSubRequirements = computed(() => {
  return requirements.value.reduce((total, req) => {
    if (req.subRequirements) {
      return total + req.subRequirements.length
    }
    return total
  }, 0)
})

// 毕业要求数据
interface SubRequirement {
  id: string
  content: string
}

interface Requirement {
  id: string
  title: string
  description: string
  subRequirements: SubRequirement[]
  collapsed: boolean
}

const requirements = ref<Requirement[]>([])

const loadAllPo = async () => {
  if (!plan.value?.id) return
  const res = await getPoList(plan.value.id)
  // 假设返回数组，每个po有requirementId字段
  const map: Record<string, any[]> = {}
  const reqMap: Record<string, any[]> = {}
  res.data.forEach((po: any) => {
    if (!map[po.requirementId]) map[po.requirementId] = []
    //if (!reqMap[po.requirementId])  reqMap[po.requirementId] = []
    if(po.isRequirement) { 
      reqMap[po.requirementId]=po
      poDescriptions.value[po.requirementId] = po.poDescription || ''
    }else{
      map[po.requirementId].push(po)
    }
    
  })
  poMap.value = map
  mapReq.value = reqMap
  console.log('加载以及指标点',  mapReq.value)
}


// 添加指标点
const onAddPo = (req: any) => {
  poDialogTitle.value = '添加指标点'
  poForm.value = { ...emptyPoForm, requirementId: req.id }
  currentEditingRequirement.value = req
  poDialogVisible.value = true
}
// 编辑指标点
const onEditPo = (req: any, po: any) => {
  poDialogTitle.value = '编辑指标点'
  poForm.value = { ...po, requirementId: req.id, id: po.id }
  currentEditingRequirement.value = req
  poDialogVisible.value = true
}
// 删除指标点
const onDeletePo = async (req: any, po: any) => {
  await deletePo(po.id)
  MessagePlugin.success('删除成功')
  loadAllPo()
}
// 更新本专业要求描述
const onUpdateProfessionalDescription = async (params: any) => { 
  try {
    if (params.id) {
      console.log('更新专业要求描述', params)
      await updatePo(params)
      MessagePlugin.success('编辑成功')
    } else {
      console.log('添加专业要求描述', params)
      const poId = await addPo(params)
      // 如果是添加新指标点，更新mapReq
      if (!mapReq.value[params.requirementId]) {
        mapReq.value[params.requirementId] = { ...params, id: poId }
      } else {
        mapReq.value[params.requirementId].poDescription = params.poDescription
      }
      poDescriptions.value[params.requirementId] = params.poDescription || ''
      if (params.isRequirement) {
        mapReq.value[params.requirementId].poTitle = params.poTitle
      }

      MessagePlugin.success('添加成功')
    }
    MessagePlugin.success('本专业要求描述已更新')
    loadAllPo() // 重新加载数据以更新显示
  } catch (e) {
    MessagePlugin.error('更新失败')
  }
}

// 提交表单
const handlePoFormSubmit = async (context: any) => {
  if (context.validateResult === true) {
    if (poForm.value.id) {
      await updatePo(poForm.value)
      MessagePlugin.success('编辑成功')
    } else {
      await addPo(poForm.value)
      MessagePlugin.success('添加成功')
    }
    poDialogVisible.value = false
    loadAllPo()
  }
}
</script>

<style lang="less" scoped>
.graduation-requirements-container {
  --header-padding: 24px;
  --card-spacing: 16px;
  --section-spacing: 24px;
  --item-spacing: 12px;
  --border-radius: 8px;
  --transition-duration: 0.2s;

  padding: var(--header-padding);
  max-width: 1200px;
  margin: 0 auto;
  color: var(--td-text-color-primary);
  font-family: var(--td-font-family);
  line-height: 1.5;
}

/* 头部区域优化 */
.header-section {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  gap: var(--card-spacing);
  margin-bottom: var(--section-spacing);
  padding-bottom: var(--card-spacing);
  border-bottom: 1px solid var(--td-component-stroke);

  .header-content {
    flex: 1;
    min-width: 300px;

    .title-wrapper {
      display: flex;
      flex-wrap: wrap;
      align-items: center;
      gap: var(--item-spacing);
      margin-bottom: var(--item-spacing);

      .page-title {
        margin: 0;
        font-size: 28px;
        font-weight: 600;
        line-height: 1.3;
        outline: none;
        min-width: 200px;
        flex:1;

        &[contenteditable="true"] {
          padding: 8px 12px;
          border-radius: var(--border-radius);
          background: var(--td-bg-color-component);
          outline: 1px dashed rgba(var(--td-brand-color-rgb), 0.3);
          transition: all var(--transition-duration);

          &:focus {
            outline: 2px solid rgba(var(--td-brand-color-rgb), 0.5);
            box-shadow: 0 0 0 2px rgba(var(--td-brand-color-rgb), 0.1);
          }
        }
      }

      .grade-select {
        width: 200px;
        flex-shrink: 0;
      }
    }

    .page-subtitle {
      margin: 0;
      font-size: 14px;
      color: var(--td-text-color-secondary);
    }
  }

  .header-actions {
    .edit-btn {
      min-width: 120px;
    }
  }
}

/* 统计卡片优化 */
.stats-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--card-spacing);
  margin-bottom: var(--section-spacing);

  .stat-card {
    border-radius: var(--border-radius);
    transition: transform var(--transition-duration), box-shadow var(--transition-duration);
    will-change: transform;

    &:hover {
      transform: translateY(-4px);
      box-shadow: var(--td-shadow-3);
    }

    .stat-content {
      display: flex;
      align-items: center;
      padding: 16px;

      .stat-icon {
        width: 48px;
        height: 48px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 16px;
        font-size: 24px;
        flex-shrink: 0;
        transition: all var(--transition-duration);

        &.total-icon {
          background: rgba(var(--td-brand-color-rgb), 0.1);
          color: var(--td-brand-color);
        }

        &.sub-icon {
          background: rgba(var(--td-success-color-rgb), 0.1);
          color: var(--td-success-color);
        }

        &.recent-icon {
          background: rgba(var(--td-purple-color-rgb), 0.1);
          color: var(--td-purple-color);
        }
      }

      .stat-info {
        .stat-value {
          font-size: 24px;
          font-weight: 600;
          line-height: 1.2;
          margin-bottom: 4px;
        }

        .stat-label {
          font-size: 14px;
          color: var(--td-text-color-secondary);
        }
      }
    }
  }
}

/* 工具栏优化 */
.toolbar {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: center;
  gap: var(--card-spacing);
  margin-bottom: var(--section-spacing);

  .t-space {
    display: flex;
    flex-wrap: wrap;
    gap: var(--item-spacing);
  }

  .search-input {
    width: 280px;
    min-width: 100%;

    @media (min-width: 480px) {
      min-width: auto;
    }
  }
}

/* 空状态优化 */
.empty-state {
  margin: 40px 0;
  text-align: center;

  .t-result {
    max-width: 500px;
    margin: 0 auto;
  }
}

/* 导入对话框优化 */
.import-dialog-content {
  display: flex;
  flex-direction: column;
  gap: var(--card-spacing);

  .t-alert {
    margin-bottom: 8px;
  }

  .import-textarea {
    font-family: var(--td-font-family-mono);
    font-size: 14px;
    line-height: 1.5;
  }
}

/* 响应式设计优化 */
@media (max-width: 768px) {
  .header-section {
    flex-direction: column;

    .header-content {
      width: 100%;

      .title-wrapper {
        flex-direction: column;
        gap: var(--item-spacing);

        .page-title,
        .grade-select {
          width: 100%;
        }
      }
    }

    .header-actions {
      width: 100%;

      .edit-btn {
        width: 100%;
      }
    }
  }

  .toolbar {
    flex-direction: column;
    align-items: stretch;

    .t-space {
      justify-content: space-between;
    }

    .search-input {
      width: 100%;
    }
  }
}

@media (max-width: 480px) {
  .graduation-requirements-container {
    padding: 16px;
  }

  .stats-cards {
    grid-template-columns: 1fr;
  }

  .requirement-header {
    padding: 12px 16px !important;

    .requirement-meta {
      gap: 12px !important;

      .requirement-number {
        width: 28px !important;
        height: 28px !important;
        font-size: 14px;
      }

      .requirement-title {
        font-size: 16px !important;
      }
    }
  }

  .requirement-content {
    padding: 0 16px 16px 16px !important;
  }
}

.requirement-card-list {
  margin-top: 24px;
}
</style>

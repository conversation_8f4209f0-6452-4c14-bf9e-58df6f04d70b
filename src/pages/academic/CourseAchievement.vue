<template>
  <div class="course-achievement">
    <t-card title="课程目标达成度分析" class="mb-4">
      <template #actions>
        <t-button @click="exportChart">导出图表</t-button>
      </template>
    </t-card>

    <t-loading :loading="loading">
      <!-- 课程目标卡片列表 -->
      <div v-if="courseTargets.length > 0" class="mb-4">
        <div class="target-card-container">
          <div
            v-for="target in courseTargets"
            :key="target.objectiveId"
            class="target-card"
            :class="{ active: activeTarget?.objectiveId === target.objectiveId }"
            @click="selectTarget(target)"
          >
            <div class="target-card-content">
              <h3>课程目标 {{ target.number }}</h3>
              <p class="target-name">{{ target.objectiveName }}</p>
              <p class="target-po">毕业要求: {{ target.po?.title || '无' }}</p>
              <p class="student-count">学生数: {{ getStudentCount(target.number) }}人</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 散点图展示区域 -->
      <t-card v-if="activeTarget && studentScores.length > 0">
        <div class="chart-container">
          <div ref="chartContainer" class="chart" id="achievement-chart" style="width: 100%; height: 500px;"></div>
          <div class="chart-info">
            <p><strong>课程目标名称:</strong> {{ activeTarget.objectiveName }}</p>
            <p><strong>对应毕业要求:</strong> {{ activeTarget.po?.title || '无' }}</p>
            <p><strong>学生总数:</strong> {{ getStudentCount(activeTarget.number) }}人</p>
          </div>
        </div>
      </t-card>

      <t-card v-else-if="courseId && !loading">
        <t-empty description="暂无数据" />
      </t-card>
      
      <t-card v-else-if="!courseId && !loading">
        <t-empty description="未获取到课程信息" />
      </t-card>
    </t-loading>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, nextTick, watch } from 'vue';
import { useRoute } from 'vue-router';
import { MessagePlugin } from 'tdesign-vue-next';
import * as echarts from 'echarts';

// API imports
import { getCourseTargetList } from '@/api/training/course';
import { getTargetScoresByCourseId } from '@/api/assessment/assessmentScore';
import type { CourseObjectiveVO } from '@/api/training/course';

// Types
interface CourseTargetScore {
  courseTargetNo: number; // 课程目标编号
  objectiveId: string; // 课程目标ID
  poId: number; // 对应专业的毕业要求ID
  courseTargetName: string; // 课程目标名称
  studentScore: number; // 学生得分
  targetTotalScore: number; // 课程目标总分
  achievement: number; // 达成度（学生得分/总分）
  weight: number; // 权重
  weightedAchievement: number; // 加权达成度
}

interface StudentCourseTargetScoreVO {
  studentId: number;
  studentName: string;
  studentNumber: string;
  targetScores: CourseTargetScore[];
}

// Reactive data
const route = useRoute();
const courseId = computed(() => Number(route.params.courseId) || 0);
const loading = ref<boolean>(false);
const courseTargets = ref<CourseObjectiveVO[]>([]);
const studentScores = ref<StudentCourseTargetScoreVO[]>([]);
const activeTarget = ref<CourseObjectiveVO | null>(null);
const chartContainer = ref<HTMLDivElement | null>(null);

// Lifecycle
onMounted(() => {
  // 页面加载时自动根据路由参数加载数据
  if (courseId.value) {
    loadData();
  }
});

// Methods
const loadData = async () => {
  if (!courseId.value) {
    return;
  }

  loading.value = true;
  try {
    // 获取课程目标列表
    const targets = await getCourseTargetList(courseId.value);
    courseTargets.value = targets;
    
    // 获取学生成绩数据
    const scoresResponse = await getTargetScoresByCourseId(courseId.value);
    // 确保数据存在且为数组
    if (scoresResponse && scoresResponse.data && Array.isArray(scoresResponse.data)) {
      studentScores.value = scoresResponse.data;
    } else {
      studentScores.value = [];
    }
    
    // 默认选中第一个课程目标
    if (targets.length > 0) {
      activeTarget.value = targets[0];
      await nextTick();
      renderChart();
    }
    
    MessagePlugin.success('数据加载成功');
  } catch (err) {
    console.error('数据加载失败:', err);
    MessagePlugin.error('数据加载失败');
  } finally {
    loading.value = false;
  }
};

const getStudentCount = (targetNumber: number) => {
  // 确保studentScores是数组后再进行过滤
  if (!Array.isArray(studentScores.value)) {
    return 0;
  }
  
  return studentScores.value.filter(student => {
    // 确保student存在且targetScores是数组
    if (!student || !Array.isArray(student.targetScores)) {
      return false;
    }
    
    // 检查是否有匹配的课程目标编号
    return student.targetScores.some(score => {
      return score && score.courseTargetNo === targetNumber;
    });
  }).length;
};

const selectTarget = (target: CourseObjectiveVO) => {
  activeTarget.value = target;
  nextTick(() => {
    renderChart();
  });
};

const renderChart = () => {
  if (!activeTarget.value) return;
  
  const chartDom = document.getElementById('achievement-chart');
  if (chartDom) {
    const chart = echarts.init(chartDom);
    
    // 准备散点图数据
    const scatterData: [number, number, string][] = [];
    
    // 确保studentScores是数组
    if (Array.isArray(studentScores.value)) {
      studentScores.value.forEach((student, index) => {
        // 确保targetScores存在且为数组
        if (student && Array.isArray(student.targetScores)) {
          const targetScore = student.targetScores.find(
            score => score && score.courseTargetNo === activeTarget.value!.number
          );
          
          // 确保targetScore和achievement都存在
          if (targetScore && 
              targetScore.achievement !== null && 
              targetScore.achievement !== undefined &&
              !isNaN(targetScore.achievement)) {
            scatterData.push([
              index + 1, // 学生序号
              parseFloat(targetScore.achievement.toFixed(3)), // 达成度
              student.studentName || `学生${index + 1}` // 学生姓名，如果不存在则使用默认名称
            ]);
          }
        }
      });
    }
    
    // 图表配置
    const option = {
      title: {
        text: `课程目标${activeTarget.value.number}达成度散点图`,
        left: 'center'
      },
      tooltip: {
        trigger: 'item',
        formatter: (params: any) => {
          return `${params.data[2]}<br/>学生序号: ${params.data[0]}<br/>达成度: ${params.data[1]}`;
        }
      },
      xAxis: {
        name: '学生序号',
        type: 'value',
        scale: true
      },
      yAxis: {
        name: '达成度',
        type: 'value',
        scale: true
      },
      series: [{
        type: 'scatter',
        name: `课程目标${activeTarget.value.number}`,
        data: scatterData,
        symbolSize: 10,
        emphasis: {
          focus: 'series',
          label: {
            show: true,
            formatter: (params: any) => {
              return params.data[2]; // 显示学生姓名
            },
            position: 'top'
          }
        }
      }],
      grid: {
        left: '10%',
        right: '10%',
        top: '20%',
        bottom: '15%'
      }
    };
    
    chart.setOption(option);
    
    // 监听窗口大小变化，自适应图表大小
    const handleResize = () => {
      chart.resize();
    };
    
    window.addEventListener('resize', handleResize);
  }
};

const exportChart = () => {
  MessagePlugin.warning('导出功能暂未实现');
};
</script>

<style scoped>
.course-achievement {
  padding: 16px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.target-card-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 16px;
  margin-bottom: 16px;
}

.target-card {
  background: #ffffff;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.target-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-color: #0052d9;
}

.target-card.active {
  border-color: #0052d9;
  box-shadow: 0 4px 12px rgba(0, 82, 217, 0.2);
}

.target-card h3 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 16px;
}

.target-card .target-name {
  margin: 0 0 8px 0;
  color: #666;
  font-size: 14px;
  min-height: 40px;
}

.target-card .target-po,
.target-card .student-count {
  margin: 0;
  color: #999;
  font-size: 12px;
}

.chart-container {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.chart {
  width: 100%;
  height: 500px;
  margin-bottom: 20px;
}

.chart-info {
  width: 100%;
  padding: 16px;
  background-color: #f0f0f0;
  border-radius: 4px;
}

.mb-4 {
  margin-bottom: 16px;
}

.ml-4 {
  margin-left: 16px;
}

.w-40 {
  width: 10rem;
}

.flex {
  display: flex;
}

.items-center {
  align-items: center;
}
</style>
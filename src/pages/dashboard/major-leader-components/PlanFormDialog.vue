<template>
  <t-dialog
    :visible="visible"
    @update:visible="$emit('update:visible', $event)"
    :header="dialogTitle"
    :width="900"
    :footer="false"
    @close="handleClose"
    class="plan-dialog"
    :close-on-esc-keydown="false"
  >
    <div class="p-1">
      <!-- 查看模式提示 -->
      <div
        v-if="mode === 'view'"
        class="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg"
      >
        <div class="flex items-center">
          <t-icon name="info-circle" class="text-blue-500 mr-2" />
          <span class="text-blue-700 text-sm"
            >当前处于查看模式，如需编辑请点击下方的"编辑培养方案"按钮</span
          >
        </div>
      </div>

      <t-form
        ref="formRef"
        :data="formData"
        :rules="mode === 'view' ? {} : (validationRules as any)"
        label-width="100px"
        @submit="handleSubmit"
        :class="['plan-form', { 'readonly-form': mode === 'view' }]"
      >
        <div class="h-full">
          <!-- 基本信息组 -->
          <div class="mb-6">
            <h4
              class="text-sm font-semibold text-gray-800 mb-4 pb-2 border-b border-gray-200"
            >
              基本信息
            </h4>
            <div class="space-y-4">
              <t-form-item label="计划名称" name="planName">
                <t-input
                  v-model="planName"
                  placeholder="请输入培养计划名称"
                  clearable
                  :disabled="mode === 'view'"
                />
              </t-form-item>

              <t-form-item label="计划版本" name="planVersion">
                <t-input-number
                  v-model="planVersion"
                  placeholder="请输入版本号"
                  :min="1"
                  :max="9999"
                  style="width: 100%"
                  :disabled="mode === 'view'"
                />
              </t-form-item>

              <t-form-item label="适用年级" name="applicableGrade">
                <t-input
                  :value="planVersion + '级'"
                  placeholder="自动根据版本号生成"
                  disabled
                />
              </t-form-item>
            </div>
          </div>

          <!-- 配置信息组 -->
          <div class="mb-0">
            <h4
              class="text-sm font-semibold text-gray-800 mb-4 pb-2 border-b border-gray-200"
            >
              配置信息
            </h4>
            <div class="space-y-4">
              <t-form-item label="毕业要求标准" name="standardId">
                <t-select
                  v-model="standardId"
                  :placeholder="
                    props.standardList.length === 0
                      ? '该学科类型暂无可用标准'
                      : '请选择毕业要求标准'
                  "
                  clearable
                  :options="
                    props.standardList.map((item) => ({
                      label: `${item.standardName} (${item.standardVersion})`,
                      value: item.id,
                    }))
                  "
                  :disabled="mode === 'view'"
                  @change="handleStandardChange"
                />
                <!-- 变更标准提示 -->
              </t-form-item>      
                <div v-if="mode !== 'view'" class="mt-2">
                  <t-alert theme="warning" class="text-sm">
                    <template #icon>
                      <t-icon name="error-triangle" />
                    </template>
                    <span class="font-medium">注意：</span>变更毕业要求标准将删除已有的毕业要求数据，此操作不可撤销！
                  </t-alert>
                </div>

              <t-form-item label="是否启用" name="status">
                <t-switch
                  v-model="status"
                  :customValue="[1, 0]"
                  :label="['启用', '禁用']"
                  :disabled="mode === 'view'"
                />
              </t-form-item>
            </div>
          </div>
        </div>

        <!-- 标准预览 -->
        <StandardPreview
          :standard-detail="standardDetail"
          :enum-data="enumData"
          :loading="standardDetailLoading"
        />

        <!-- 操作按钮 -->
        <div class="mt-6 flex justify-end">
          <t-space size="medium">
            <t-button
              v-if="mode !== 'view'"
              theme="primary"
              type="submit"
              :loading="submitLoading"
            >
              {{ mode === "add" ? "确认添加" : "确认修改" }}
            </t-button>
            <t-button
              v-if="mode === 'view'"
              theme="primary"
              @click="handleSwitchToEdit"
            >
              <span class="flex items-center">
                <t-icon name="edit" class="mr-1" />
                编辑培养方案
              </span>
            </t-button>
            <t-button theme="default" variant="base" @click="handleClose">
              {{ mode === "view" ? "关闭" : "取消" }}
            </t-button>
          </t-space>
        </div>
      </t-form>
    </div>
  </t-dialog>
</template>

<script setup lang="ts">
import { getGraduationStandardDetail } from "@/api/system/graduation-standard";
import { computed, ref, watch } from "vue";
import StandardPreview from "./StandardPreview.vue";
import {
  EnumData,
  Major,
  PlanFormData,
  Standard,
  StandardDetail,
} from "./types";

// Props
const props = defineProps<{
  visible: boolean;
  mode: "add" | "edit" | "view";
  formData: PlanFormData;
  enumData: EnumData | null;
  submitLoading: boolean;
  major: Major | null;
  standardList: Standard[];
}>();

// Emits
const emit = defineEmits<{
  "update:visible": [visible: boolean];
  "update:formData": [formData: PlanFormData];
  submit: [formData: PlanFormData];
  switchToEdit: [];
  close: [];
}>();

// Refs
const formRef = ref(null);
const standardDetail = ref<StandardDetail | null>(null);
const standardDetailLoading = ref(false);

// Computed
const dialogTitle = computed(() => {
  switch (props.mode) {
    case "add":
      return "新增培养方案";
    case "edit":
      return "编辑培养方案";
    case "view":
      return "查看培养方案";
    default:
      return "培养方案";
  }
});

// 表单字段的计算属性，处理双向绑定
const planName = computed({
  get: () => props.formData.planName,
  set: (value: string) => {
    emit("update:formData", { ...props.formData, planName: value });
  },
});

const planVersion = computed({
  get: () => props.formData.planVersion,
  set: (value: number) => {
    emit("update:formData", { ...props.formData, planVersion: value });
  },
});

const standardId = computed({
  get: () => props.formData.standardId,
  set: (value: number | null) => {
    emit("update:formData", { ...props.formData, standardId: value });
  },
});

const status = computed({
  get: () => props.formData.status,
  set: (value: number) => {
    emit("update:formData", { ...props.formData, status: value });
  },
});

const validationRules = {
  planName: [
    { required: true, message: "请输入培养计划名称", trigger: "blur" as const },
    {
      max: 100,
      message: "计划名称不能超过100个字符",
      trigger: "blur" as const,
    },
  ],
  planVersion: [
    { required: true, message: "请输入计划版本", trigger: "blur" as const },
  ],
  standardId: [
    {
      required: true,
      message: "请选择毕业要求标准",
      trigger: "change" as const,
    },
  ],
};

const loadStandardDetail = async (standardId: number) => {
  standardDetailLoading.value = true;
  try {
    const res = await getGraduationStandardDetail(String(standardId));
    standardDetail.value = res.data;
  } finally {
    standardDetailLoading.value = false;
  }
};

const handleStandardChange = (newStandardId: any) => {
  if (newStandardId) {
    loadStandardDetail(Number(newStandardId));
  } else {
    standardDetail.value = null;
  }
};

const handleSubmit = (context: any) => {
  if (props.mode === "view") {
    return;
  }

  if (context.validateResult !== true) {
    return;
  }

  emit("submit", props.formData);
};

const handleSwitchToEdit = () => {
  emit("switchToEdit");
};

const handleClose = () => {
  emit("close");
};

// Watch for visibility changes
watch(
  () => props.visible,
  (visible) => {
    if (visible) {
      // 当对话框打开时，如果有学科类型和标准ID，加载相应数据
      if (props.formData.standardId) {
        loadStandardDetail(props.formData.standardId);
      }
    } else {
      // 当对话框关闭时，重置状态
      standardDetail.value = null;
      if (formRef.value) {
        formRef.value.reset();
      }
    }
  },
);
</script>

<style scoped>
.plan-dialog :deep(.t-dialog__body) {
  padding: 0;
}

.plan-form {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
  min-height: 500px;
}

.readonly-form :deep(.t-input),
.readonly-form :deep(.t-select),
.readonly-form :deep(.t-input-number),
.readonly-form :deep(.t-switch) {
  pointer-events: none;
}
</style>

<template>
  <div class="border rounded-lg p-4 hover:shadow-md transition-shadow">
    <div class="flex justify-between items-start mb-4">
      <div>
        <h4 class="font-medium text-gray-900">{{ plan.planName }}</h4>
        <p class="text-sm text-gray-500 mt-1">版本 {{ plan.planVersion }}</p>
      </div>
      <t-tag :theme="plan.status === 1 ? 'success' : 'warning'" size="small">
        {{ plan.status === 1 ? "已启用" : "未启用" }}
      </t-tag>
    </div>
    <div class="space-y-2 text-sm mb-4">
      <div class="flex justify-between">
        <span class="text-gray-500">适用年级：</span>
        <span class="text-gray-900">{{ plan.planVersion }}级</span>
      </div>
      <div class="flex justify-between">
        <span class="text-gray-500">毕业要求标准：</span>
        <span class="text-gray-900">{{
          getStandardName(plan.standardId)
        }}</span>
      </div>
    </div>
    <div class="flex justify-end space-x-2">
      <t-button variant="text" size="small" @click="handleConfig">
        <span class="flex items-center">
          <t-icon name="setting-1" class="mr-1" /> 配置
        </span>
      </t-button>
      <t-button variant="text" size="small" @click="handleView">
        <span class="flex items-center">
          <t-icon name="browse" class="mr-1" /> 查看
        </span>
      </t-button>
      <t-button variant="text" size="small" @click="handleEdit">
        <span class="flex items-center">
          <t-icon name="edit" class="mr-1" /> 编辑
        </span>
      </t-button>
      <t-button variant="text" theme="success" size="small" @click="handleCopy">
        <span class="flex items-center">
          <t-icon name="copy" class="mr-1" /> 复制
        </span>
      </t-button>
      <t-button
        variant="text"
        theme="danger"
        size="small"
        @click="handleDelete"
      >
        <span class="flex items-center">
          <t-icon name="delete" class="mr-1" /> 删除
        </span>
      </t-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Plan, Standard } from "./types";

// Props
const props = defineProps<{
  plan: Plan;
  standardList: Standard[];
}>();

// Emits
const emit = defineEmits<{
  view: [planId: number];
  edit: [plan: Plan];
  copy: [plan: Plan];
  delete: [planId: number];
  config: [plan: Plan];
}>();

// Methods
const getStandardName = (standardId: number) => {
  if (!standardId) return "未设置";
  const standard = props.standardList.find((item) => item.id == standardId);
  return standard
    ? `${standard.standardName} (${standard.standardVersion})`
    : "未设置";
};

const handleConfig = () => {
  emit("config", props.plan);
};

const handleView = () => {
  emit("view", props.plan.id);
};

const handleEdit = () => {
  emit("edit", props.plan);
};

const handleCopy = () => {
  emit("copy", props.plan);
};

const handleDelete = () => {
  emit("delete", props.plan.id);
};
</script>

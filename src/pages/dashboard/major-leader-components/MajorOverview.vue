<template>
  <div>
    <!-- 专业信息头部 -->
    <div class="bg-white rounded-lg shadow mb-6">
      <div class="p-4">
        <div class="flex justify-between items-center">
          <div class="space-y-1">
            <div class="flex items-center space-x-2">
              <h2 class="text-xl font-medium">{{ major?.name }}</h2>
              <span class="text-gray-500">({{ major?.code }})</span>
            </div>
            <p class="text-gray-500 text-sm">{{ major?.academyName }}</p>
          </div>
          <t-button theme="primary" @click="handleReselectMajor" variant="base">
            <span class="flex items-center">
              <t-icon name="refresh" class="mr-1" />
              切换专业
            </span>
          </t-button>
        </div>
        <div class="mt-2">
          <t-tag theme="success" size="small">工控教育认证已通过</t-tag>
        </div>
      </div>
    </div>

    <!-- 数据概览 -->
    <div class="grid grid-cols-4 gap-4 mb-6">
      <div class="bg-white p-6 rounded-lg shadow">
        <div class="flex flex-col items-center">
          <t-icon name="user-circle" class="text-blue-500 text-2xl mb-2" />
          <div class="text-2xl font-semibold text-gray-900">
            {{ major?.studentTotalCount }}
          </div>
          <div class="text-sm text-gray-500 mt-1">累计培养人数</div>
        </div>
      </div>
      <div class="bg-white p-6 rounded-lg shadow">
        <div class="flex flex-col items-center">
          <t-icon name="usergroup" class="text-blue-500 text-2xl mb-2" />
          <div class="text-2xl font-semibold text-gray-900">
            {{ major?.studentInSchoolCount }}
          </div>
          <div class="text-sm text-gray-500 mt-1">在校学生数</div>
        </div>
      </div>
      <div class="bg-white p-6 rounded-lg shadow">
        <div class="flex flex-col items-center">
          <t-icon name="book" class="text-blue-500 text-2xl mb-2" />
          <div class="text-2xl font-semibold text-gray-900">
            {{ major?.courseTotalCount }}
          </div>
          <div class="text-sm text-gray-500 mt-1">专业课程总数</div>
        </div>
      </div>
      <div class="bg-white p-6 rounded-lg shadow">
        <div class="flex flex-col items-center">
          <t-icon name="file" class="text-blue-500 text-2xl mb-2" />
          <div class="text-2xl font-semibold text-gray-900">
            {{ major?.planCount }}
          </div>
          <div class="text-sm text-gray-500 mt-1">培养方案数量</div>
        </div>
      </div>
    </div>

    <!-- 专业概述 -->
    <div class="bg-white rounded-lg shadow mb-6">
      <div class="p-6">
        <h3 class="text-lg font-medium mb-4">专业概述</h3>
        <div class="flex flex-row gap-8">
          <!-- 左侧：专业概述文本 -->
          <div class="flex-5">
            <p class="text-gray-600 leading-relaxed">
              {{ major?.professionalOverview }}
            </p>
          </div>
          <!-- 右侧：专业信息表格 -->
          <div class="flex-2">
            <div class="grid grid-cols-1 gap-2 text-sm">
              <div class="flex justify-between py-2 border-b">
                <span class="text-gray-500">专业代码：</span>
                <span class="text-gray-900">{{ major?.code }}</span>
              </div>
              <div class="flex justify-between py-2 border-b">
                <span class="text-gray-500">学科类型：</span>
                <span class="text-gray-900">{{
                  enumData?.map?.disciplineType[major?.discipline]
                }}</span>
              </div>
              <div class="flex justify-between py-2 border-b">
                <span class="text-gray-500">所属学院：</span>
                <span class="text-gray-900">{{ major?.academyName }}</span>
              </div>
              <div class="flex justify-between py-2 border-b">
                <span class="text-gray-500">专业负责人：</span>
                <span class="text-gray-900">{{ major?.directorName }}</span>
              </div>
              <div class="flex justify-between py-2 border-b">
                <span class="text-gray-500">最近更新：</span>
                <span class="text-gray-900">{{
                  major?.modifyTime.split(" ")[0]
                }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { EnumData, Major } from "./types";

// Props
defineProps<{
  major: Major | null;
  enumData: EnumData | null;
}>();

// Emits
const emit = defineEmits<{
  reselectMajor: [];
}>();

const handleReselectMajor = () => {
  emit("reselectMajor");
};
</script>

<template>
  <div class="preview-area h-full border-l border-gray-200 pl-6">
    <div class="mb-4">
      <h4 class="text-sm font-semibold text-gray-800 mb-2">标准预览</h4>
      <t-divider class="!m-0" />
    </div>
    <div class="h-[calc(100%-60px)] overflow-y-auto">
      <t-loading :loading="loading">
        <div v-if="standardDetail" class="standard-preview">
          <!-- 标准基本信息 -->
          <div class="mb-5">
            <t-descriptions bordered size="small" :column="1">
              <t-descriptions-item label="标准名称">
                <span class="font-medium">{{
                  standardDetail.standardName
                }}</span>
              </t-descriptions-item>
              <t-descriptions-item label="版本">
                {{ standardDetail.standardVersion }}
              </t-descriptions-item>
              <t-descriptions-item label="学科类型">
                {{
                  enumData?.map?.disciplineType?.[standardDetail.disciplineType]
                }}
              </t-descriptions-item>
              <t-descriptions-item label="发布日期">
                {{ formatDate(standardDetail.releaseDate) }}
              </t-descriptions-item>
              <t-descriptions-item label="描述">
                <div
                  class="leading-relaxed text-gray-500 whitespace-pre-wrap break-words"
                >
                  {{ standardDetail.standardDescription }}
                </div>
              </t-descriptions-item>
            </t-descriptions>
          </div>

          <!-- 毕业要求列表 -->
          <div
            v-if="standardDetail.requirements?.length"
            class="requirements-section"
          >
            <div class="mb-3">
              <h5
                class="text-xs font-medium text-gray-700 flex items-center gap-2"
              >
                毕业要求项
                <t-tag size="small" theme="primary"
                  >{{ standardDetail.requirements.length }}项</t-tag
                >
              </h5>
            </div>
            <div class="rounded-md overflow-hidden">
              <t-table
                :data="standardDetail.requirements"
                :columns="[
                  { colKey: 'standardName', title: '标题', width: 120 },
                  { colKey: 'standardDescription', title: '描述' },
                ]"
                size="small"
                bordered
                row-key="id"
                :max-height="200"
              />
            </div>
          </div>
        </div>
        <div
          v-else
          class="flex items-center justify-center h-[200px] bg-gray-50 rounded-lg border border-dashed border-gray-300"
        >
          <t-empty description="请选择毕业要求标准以查看详情" />
        </div>
      </t-loading>
    </div>
  </div>
</template>

<script setup lang="ts">
import dayjs from "dayjs";
import { EnumData, StandardDetail } from "./types";

// Props
defineProps<{
  standardDetail: StandardDetail | null;
  enumData: EnumData | null;
  loading: boolean;
}>();

// Methods
const formatDate = (date: string | number | Date | null | undefined) => {
  if (!date) return "";
  return dayjs(date).format("YYYY-MM-DD");
};
</script>

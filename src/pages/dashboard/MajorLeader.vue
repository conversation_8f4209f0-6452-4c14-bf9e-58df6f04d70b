<template>
  <div class="min-h-screen bg-gray-50">
    <div class="p-6 w-full mx-auto">
      <!-- 页面加载骨架屏 -->
      <div v-if="pageLoading" class="loading-container">
        <!-- 专业信息概览骨架屏 -->
        <t-card :bordered="false" class="mb-6">
          <template #header>
            <t-skeleton theme="text" :row-col="[{ width: '200px' }]" />
          </template>
          <div class="overview-skeleton">
            <t-row :gutter="24">
              <t-col :span="8">
                <t-skeleton theme="text" :row-col="[{ width: '100%' }, { width: '80%' }]" />
              </t-col>
              <t-col :span="8">
                <t-skeleton theme="text" :row-col="[{ width: '100%' }, { width: '60%' }]" />
              </t-col>
              <t-col :span="8">
                <t-skeleton theme="text" :row-col="[{ width: '100%' }, { width: '70%' }]" />
              </t-col>
            </t-row>
            <div class="mt-4">
              <t-skeleton theme="text" :row-col="[{ width: '100%' }, { width: '90%' }, { width: '80%' }]" />
            </div>
          </div>
        </t-card>

        <!-- 培养方案管理骨架屏 -->
        <t-card :bordered="false">
          <template #header>
            <div class="flex justify-between items-center">
              <t-skeleton theme="text" :row-col="[{ width: '150px' }]" />
              <t-skeleton theme="text" :row-col="[{ width: '100px' }]" />
            </div>
          </template>
          <div class="plan-grid-skeleton">
            <t-row :gutter="24">
              <t-col :span="8" v-for="i in 3" :key="i">
                <div class="plan-card-skeleton">
                  <t-skeleton theme="text" :row-col="[{ width: '100%' }, { width: '70%' }]" />
                  <div class="mt-4">
                    <t-skeleton theme="text" :row-col="[{ width: '50%' }, { width: '60%' }]" />
                  </div>
                                     <div class="mt-4 flex justify-end gap-2">
                     <t-skeleton theme="text" :row-col="[{ width: '60px' }]" />
                     <t-skeleton theme="text" :row-col="[{ width: '60px' }]" />
                     <t-skeleton theme="text" :row-col="[{ width: '60px' }]" />
                   </div>
                </div>
              </t-col>
            </t-row>
          </div>
        </t-card>
      </div>

      <!-- 实际内容 -->
      <div v-else>
        <!-- 专业信息概览 -->
        <MajorOverview
          :major="currentMajor"
          :enum-data="enumData"
          @reselect-major="handleReselectMajor"
        />

        <!-- 培养方案管理 -->
        <PlanGrid
          :plan-list="planList"
          :standard-list="standardList"
          @add-plan="handleAddPlanClick"
          @view-plan="showDetails"
          @edit-plan="handleEditPlan"
          @copy-plan="handleCopyPlan"
          @delete-plan="handleDeletePlan"
          @config-plan="handleConfigPlan"
        />
      </div>
    </div>

    <!-- 专业选择弹窗 -->
    <MajorSelector
      :majors="myMajors"
      v-model:visible="showMajorSelector"
      :current-major-id="currentMajor?.id"
      @select="handleMajorSelect"
    />

    <!-- 培养方案表单弹窗 -->
    <PlanFormDialog
      :major="currentMajor"
      :standard-list="standardList"
      v-model:visible="planDialog.visible"
      :form-data="planFormData"
      @update:form-data="planFormData = $event"
      :mode="planDialog.mode"
      :enum-data="enumData"
      :submit-loading="planDialog.loading"
      @submit="handlePlanSubmit"
      @switch-to-edit="handleSwitchToEdit"
      @close="handleDialogClose"
    />
  </div>
</template>

<script setup lang="ts">
import {
  addPlan,
  deleteByplanId,
  getPlan,
  getPlanList,
  updatePlan,
} from "@/api/major/plan";
import { getEnum } from "@/api/system/enum";
import {
  getGraduationStandardDetail,
  getGraduationStandardList,
} from "@/api/system/graduation-standard";
import { DialogPlugin, MessagePlugin } from "tdesign-vue-next";
import { onMounted, ref } from "vue";
import { useRoute, useRouter } from "vue-router";

import { getMyMajors } from "@/api/base/major";
import MajorSelector from "@/pages/components/MajorSelector.vue";
import MajorOverview from "./major-leader-components/MajorOverview.vue";
import PlanFormDialog from "./major-leader-components/PlanFormDialog.vue";
import PlanGrid from "./major-leader-components/PlanGrid.vue";
import {
  EnumData,
  Major,
  Plan,
  PlanFormData,
  Standard,
  StandardDetail,
} from "./major-leader-components/types";

// 路由相关
const route = useRoute();
const router = useRouter();

// 页面加载状态
const pageLoading = ref(true);

// 专业选择弹窗状态
const showMajorSelector = ref(false);

// Major selection
const currentMajor = ref<Major | null>(null);

// 专业选择处理
const handleMajorSelect = async (major: Major) => {
  showMajorSelector.value = false;
  await changeMajor(major);
};

const changeMajor = async (major: Major) => {
  pageLoading.value = true;
  try {
    currentMajor.value = major;
    await router.replace({
      query: {
        ...route.query,
        majorId: major.id,
      },
    });
    await getPlanListData(major.id);
    getStandardList(major.discipline);
  } finally {
    pageLoading.value = false;
  }
};

// 重新选择专业
const handleReselectMajor = () => {
  showMajorSelector.value = true;
};

// 枚举数据
const enumData = ref<EnumData | null>(null);

const fetchEnumData = async () => {
  const res = await getEnum();
  enumData.value = res.data;
};

const emptyPlanData: PlanFormData = {
  planName: "",
  planVersion: new Date().getFullYear(),
  standardId: null,
  status: 1,
};

const planDialog = ref<{
  visible: boolean;
  loading: boolean;
  mode: "add" | "edit" | "view";
}>({
  visible: false,
  loading: false,
  mode: "add",
});

const planFormData = ref<PlanFormData>({
  ...emptyPlanData,
});

const planFormRef = ref(null);

/**
 * 统一的表单初始化函数
 * @param mode 表单模式：add(新增)/edit(编辑)/view(查看)
 * @param planData 培养方案数据(编辑和查看模式必传，新增复制模式可选)
 */
const initPlanForm = async (mode: "add" | "edit" | "view", planData?: Plan) => {
  // Step 1: 显示对话框并设置加载状态
  planDialog.value = { visible: true, loading: true, mode };

  try {
    // Step 2: 准备表单数据
    const formData = prepareFormData(mode, planData);

    // Step 4: 设置表单数据到响应式对象
    planFormData.value = formData;

    // Step 6: 加载标准详情(如果已选择标准)
    if (formData.standardId) {
      loadStandardDetail(formData.standardId);
    }
  } finally {
    planDialog.value.loading = false;
  }
};

/**
 * 根据模式和原始数据准备表单数据
 */
const prepareFormData = (mode: "add" | "edit" | "view", planData?: Plan) => {
  switch (mode) {
    case "add":
      if (planData) {
        // 复制模式：基于现有数据创建副本
        return {
          ...planData,
          id: undefined,
          planName: `${planData.planName} (副本)`,
          planVersion: planData.planVersion + 1,
        };
      } else {
        // 新增模式：使用空白数据
        return { ...emptyPlanData };
      }

    case "edit":
    case "view":
      // 编辑/查看模式：使用现有数据并确保状态为数字类型
      return {
        ...planData,
        status: Number(planData.status),
      };

    default:
      throw new Error(`未知的表单模式: ${mode}`);
  }
};

// 加载标准详情
const loadStandardDetail = async (standardId: number) => {
  standardDetailLoading.value = true;
  try {
    const res = await getGraduationStandardDetail(String(standardId));
    standardDetail.value = res.data;
  } finally {
    standardDetailLoading.value = false;
  }
};

// 简化后的操作函数
const showDetails = async (id: number) => {
  const res = await getPlan(id);
  await initPlanForm("view", res.data);
};

const handleCopyPlan = (plan: Plan) => {
  initPlanForm("add", plan);
};

const handleAddPlanClick = () => {
  initPlanForm("add");
};

const handleEditPlan = (plan: Plan) => {
  initPlanForm("edit", plan);
};

// 表单提交
const handlePlanSubmit = async (formData: PlanFormData) => {
  if (planDialog.value.mode === "view") {
    return;
  }

  planDialog.value.loading = true;
  try {
    const isAdd = planDialog.value.mode === "add";
    isAdd ? await addPlan(formData) : await updatePlan(formData.id, formData);

    MessagePlugin.success(isAdd ? "培养计划添加成功" : "培养计划更新成功");
    handleDialogClose();
    await getPlanListData(currentMajor.value?.id);
  } finally {
    planDialog.value.loading = false;
  }
};

// 删除培养方案
const handleDeletePlan = (id: number) => {
  const confirmDialog = DialogPlugin.confirm({
    header: "确认删除",
    body: "确定要删除这个培养方案吗？删除后将无法恢复！",
    theme: "warning",
    confirmBtn: "确认删除",
    cancelBtn: "取消",
    onConfirm: async () => {
      try {
        // 用户点击确认后执行删除操作
        await deleteByplanId(id, {});
        MessagePlugin.success("培养计划删除成功");
        await getPlanListData(currentMajor.value?.id);
        confirmDialog.destroy(); // 关闭对话框
      } catch (error) {
        console.error("删除培养方案失败:", error);
        MessagePlugin.error("删除失败，请重试");
        confirmDialog.destroy(); // 确保对话框关闭
      }
    },
    onCancel: () => {
      // 用户点击取消时的处理
      console.log("用户取消删除操作");
      confirmDialog.destroy(); // 关闭对话框
    }
  });
};

// 关闭对话框并重置状态
const handleDialogClose = () => {
  planDialog.value.visible = false;
  planFormData.value = { ...emptyPlanData };
  standardDetail.value = null;
  if (planFormRef.value) {
    planFormRef.value.reset();
  }
};

// 从查看模式切换到编辑模式
const handleSwitchToEdit = () => {
  planDialog.value.mode = "edit";
};

const handleConfigPlan = (plan: Plan) => {
  router.push({
    path: `/manage/major/${currentMajor.value?.id}/plan/${plan.id}/main`,
    query: {
      majorId: currentMajor.value?.id,
      planId: plan.id,
    }
  });
};

const standardDetail = ref<StandardDetail | null>(null);
const standardDetailLoading = ref(false);

// 培养方案列表管理
const planList = ref<Plan[]>([]);
const getPlanListData = async (majorId: number) => {
  const res = await getPlanList({
    current: 1,
    size: 1000,
    majorId: majorId,
  });
  planList.value = res.data.records;
};

const standardList = ref<Standard[]>([]);
// Methods
const getStandardList = async (disciplineType: string) => {
  const params = {
    disciplineType: disciplineType,
    current: 1,
    size: 100,
    status: 0,
  };

  const res = await getGraduationStandardList(params);
  standardList.value = res.data.records;
};

// 专业管理
const myMajors = ref<Major[]>([]);
const initializePage = async () => {
  pageLoading.value = true;
  try {
    const [enumRes, majorsRes] = await Promise.all([
      fetchEnumData(),
      getMyMajors(),
    ]);

    myMajors.value = majorsRes.data;

    // 处理专业选择逻辑
    const majorId = route.query.majorId as string;
    if (majorId) {
      const major = myMajors.value.find((item) => item.id == Number(majorId));
      if (major) {
        await changeMajor(major);
      } else {
        pageLoading.value = false;
      }
    } else if (myMajors.value.length === 1) {
      await changeMajor(myMajors.value[0]);
      MessagePlugin.success("当前仅有一个专业，已自动切换到该专业");
    } else {
      pageLoading.value = false;
      showMajorSelector.value = true;
    }
  } catch (error) {
    pageLoading.value = false;
    console.error('页面初始化失败:', error);
    MessagePlugin.error('页面初始化失败');
  }
};

onMounted(initializePage);
</script>

<style scoped>
.loading-container {
  animation: fadeIn 0.3s ease-in-out;
}

.overview-skeleton {
  padding: 20px 0;
}

.plan-grid-skeleton {
  padding: 20px 0;
}

.plan-card-skeleton {
  border: 1px solid #e6e6e6;
  border-radius: 8px;
  padding: 20px;
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  margin-bottom: 20px;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.mt-4 {
  margin-top: 16px;
}

.mb-6 {
  margin-bottom: 24px;
}

.flex {
  display: flex;
}

.justify-between {
  justify-content: space-between;
}

.justify-end {
  justify-content: flex-end;
}

.items-center {
  align-items: center;
}

.gap-2 {
  gap: 8px;
}
</style>

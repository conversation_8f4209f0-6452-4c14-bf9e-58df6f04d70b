<template>
  <div class="study-style-dashboard">
    <!-- 头部信息 -->
    <div class="dashboard-header">
      <div class="header-content">
        <div class="title-info">
          <div class="title-container">
            <h1 class="dashboard-title">学风建设工作台</h1>
            <t-tag theme="primary" size="small" class="role-tag">
              学风建设
            </t-tag>
          </div>
          <p class="dashboard-subtitle">全校学风建设数据概览与管理</p>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="dashboard-content">
      <!-- 全校总体统计模块 -->
      <div class="statistics-section">
        <div class="section-header">
          <div class="header-left">
            <h2 class="section-title">
              <t-icon name="chart-pie" class="title-icon" />
              全校统计总览
            </h2>
          </div>
        </div>

        <div class="stats-grid">
          <div class="stat-card" v-for="(stat, index) in statsData" :key="stat.key" :style="{ '--delay': `${index * 0.1}s` }">
            <div class="stat-card-inner">
              <div class="stat-icon-wrapper" :class="stat.iconClass">
                <t-icon :name="stat.icon" size="28px" />
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ stat.value }}</div>
                <div class="stat-label">{{ stat.label }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 院系学风管理模块 -->
      <div class="departments-section">
        <div class="section-header">
          <div class="header-left">
            <h2 class="section-title">
              <t-icon name="building" class="title-icon" />
              院系学风管理
            </h2>
          </div>
          <div class="header-right">
            <t-input
              v-model="searchKeyword"
              placeholder="搜索院系..."
              style="width: 250px;"
              prefix-icon="search"
              clearable
            />
          </div>
        </div>

        <div class="departments-grid">
          <div
            class="department-card"
            v-for="(department, index) in filteredDepartments"
            :key="department.id"
            :style="{ '--delay': `${(index + 4) * 0.1}s` }"
          >
            <div class="department-card-inner">
              <!-- 院系头部 -->
              <div class="department-header">
                <h3 class="department-name">{{ department.name }}</h3>
                <div class="department-code">{{ department.code }}</div>
              </div>

              <!-- 院系统计信息 -->
              <div class="department-stats">
                <div class="stats-row">
                  <div class="stat-item">
                    <div class="stat-icon major-icon">
                      <t-icon name="file-text" size="16px" />
                    </div>
                    <div class="stat-info">
                      <div class="stat-label-with-icon">
                        <t-icon name="folder" size="12px" class="inline-icon" />
                        专业数量
                      </div>
                      <div class="stat-value">{{ department.majorCount }}</div>
                    </div>
                  </div>
                  <div class="stat-item">
                    <div class="stat-icon student-icon">
                      <t-icon name="user" size="16px" />
                    </div>
                    <div class="stat-info">
                      <div class="stat-label-with-icon">
                        <t-icon name="usergroup" size="12px" class="inline-icon" />
                        学生人数
                      </div>
                      <div class="stat-value">{{ department.studentCount }}</div>
                    </div>
                  </div>
                </div>

                <div class="stats-row">
                  <div class="stat-item">
                    <div class="stat-icon teacher-icon">
                      <t-icon name="user-talk" size="16px" />
                    </div>
                    <div class="stat-info">
                      <div class="stat-label-with-icon">
                        <t-icon name="education" size="12px" class="inline-icon" />
                        教师人数
                      </div>
                      <div class="stat-value">{{ department.teacherCount }}</div>
                    </div>
                  </div>
                  <div class="stat-item">
                    <div class="stat-icon score-icon">
                      <t-icon name="star" size="16px" />
                    </div>
                    <div class="stat-info">
                      <div class="stat-label-with-icon">
                        <t-icon name="chart-line" size="12px" class="inline-icon" />
                        学风评分
                      </div>
                      <div class="stat-value" :class="getScoreClass(department.studyStyleScore)">
                        {{ department.studyStyleScore }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 操作按钮 -->
              <div class="department-actions">
                <t-button
                  theme="primary"
                  variant="base"
                  size="small"
                  @click="viewDepartmentStudyStyle(department)"
                >
                  <template #icon>
                    <t-icon name="view-module" size="14px" />
                  </template>
                  查看院系学风
                </t-button>
                <t-button
                  theme="success"
                  variant="base"
                  size="small"
                  @click="viewMajorStudyStyle(department)"
                >
                  <template #icon>
                    <t-icon name="layers" size="14px" />
                  </template>
                  查看专业学风
                </t-button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 空状态提示 -->
      <div v-if="filteredDepartments.length === 0 && searchKeyword" class="empty-state">
        <div class="empty-content">
          <t-icon name="search" size="48px" class="empty-icon" />
          <h3 class="empty-title">未找到相关院系</h3>
          <p class="empty-description">请尝试使用其他关键词搜索</p>
          <t-button theme="primary" variant="outline" @click="clearSearch">
            清空搜索
          </t-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { MessagePlugin } from 'tdesign-vue-next';

// 数据接口定义
interface Department {
  id: number;
  name: string;
  code: string;
  majorCount: number;
  studentCount: number;
  teacherCount: number;
  studyStyleScore: number;
}

interface StudyStyleStatistics {
  totalDepartments: number;
  totalMajors: number;
  totalStudents: number;
  totalTeachers: number;
}

// 响应式数据
const loading = ref(false);
const searchKeyword = ref('');

// 初始化 router
const router = useRouter();

// 模拟统计数据
const statistics = ref<StudyStyleStatistics>({
  totalDepartments: 12,
  totalMajors: 58,
  totalStudents: 25680,
  totalTeachers: 1245
});

// 模拟院系数据
const departments = ref<Department[]>([
  {
    id: 1,
    name: '计算机科学与技术学院',
    code: 'CS',
    majorCount: 6,
    studentCount: 2850,
    teacherCount: 125,
    studyStyleScore: 8.8
  },
  {
    id: 2,
    name: '电子与信息工程学院',
    code: 'EIE',
    majorCount: 5,
    studentCount: 2120,
    teacherCount: 98,
    studyStyleScore: 8.5
  },
  {
    id: 3,
    name: '机械工程学院',
    code: 'ME',
    majorCount: 4,
    studentCount: 1890,
    teacherCount: 87,
    studyStyleScore: 8.2
  },
  {
    id: 4,
    name: '材料科学与工程学院',
    code: 'MSE',
    majorCount: 3,
    studentCount: 1560,
    teacherCount: 76,
    studyStyleScore: 7.9
  },
  {
    id: 5,
    name: '经济管理学院',
    code: 'EM',
    majorCount: 7,
    studentCount: 3240,
    teacherCount: 142,
    studyStyleScore: 8.6
  },
  {
    id: 6,
    name: '外国语学院',
    code: 'FL',
    majorCount: 4,
    studentCount: 1820,
    teacherCount: 89,
    studyStyleScore: 8.3
  },
  {
    id: 7,
    name: '化学与化工学院',
    code: 'CCE',
    majorCount: 3,
    studentCount: 1450,
    teacherCount: 68,
    studyStyleScore: 8.0
  },
  {
    id: 8,
    name: '土木工程学院',
    code: 'CE',
    majorCount: 5,
    studentCount: 2180,
    teacherCount: 95,
    studyStyleScore: 8.1
  },
  {
    id: 9,
    name: '数学与统计学院',
    code: 'MS',
    majorCount: 3,
    studentCount: 1320,
    teacherCount: 72,
    studyStyleScore: 8.7
  },
  {
    id: 10,
    name: '物理与光电工程学院',
    code: 'POE',
    majorCount: 4,
    studentCount: 1680,
    teacherCount: 83,
    studyStyleScore: 8.4
  },
  {
    id: 11,
    name: '艺术设计学院',
    code: 'AD',
    majorCount: 6,
    studentCount: 2890,
    teacherCount: 118,
    studyStyleScore: 7.8
  },
  {
    id: 12,
    name: '生命科学学院',
    code: 'LS',
    majorCount: 3,
    studentCount: 1380,
    teacherCount: 65,
    studyStyleScore: 8.3
  }
]);

// 计算属性
const statsData = computed(() => [
  {
    key: 'departments',
    label: '院系数量',
    value: statistics.value.totalDepartments,
    icon: 'building',
    iconClass: 'icon-blue'
  },
  {
    key: 'majors',
    label: '专业数量',
    value: statistics.value.totalMajors,
    icon: 'file-text',
    iconClass: 'icon-green'
  },
  {
    key: 'students',
    label: '学生总数',
    value: statistics.value.totalStudents.toLocaleString(),
    icon: 'usergroup',
    iconClass: 'icon-orange'
  },
  {
    key: 'teachers',
    label: '教师总数',
    value: statistics.value.totalTeachers.toLocaleString(),
    icon: 'user-talk',
    iconClass: 'icon-purple'
  }
]);

const filteredDepartments = computed(() => {
  if (!searchKeyword.value) {
    return departments.value;
  }

  const keyword = searchKeyword.value.toLowerCase();
  return departments.value.filter(dept =>
    dept.name.toLowerCase().includes(keyword) ||
    dept.code.toLowerCase().includes(keyword)
  );
});

// 方法
const getScoreClass = (score: number) => {
  if (score >= 8.5) return 'score-excellent';
  if (score >= 8.0) return 'score-good';
  if (score >= 7.5) return 'score-average';
  return 'score-poor';
};

const viewDepartmentStudyStyle = (department: Department) => {
  // 跳转到院系学风详情页面，传递院系 id 和 dataType 参数
  router.push({
    name: "StudyStyle_Home",
    params: {
      departmentId: department.id,
      dataType: 'college'
    }
  });
};

const viewMajorStudyStyle = (department: Department) => {
  MessagePlugin.info(`查看 ${department.name} 专业学风详情`);
  // TODO: 跳转到专业学风详情页面
  console.log('查看专业学风:', department);
};

const clearSearch = () => {
  searchKeyword.value = '';
};

const loadData = async () => {
  try {
    loading.value = true;
    // TODO: 调用实际的API获取数据
    await new Promise(resolve => setTimeout(resolve, 500)); // 模拟加载延迟

    // 这里应该调用真实的API
    // const response = await getStudyStyleStatistics();
    // statistics.value = response.statistics;
    // departments.value = response.departments;

  } catch (error) {
    console.error('加载数据失败:', error);
    MessagePlugin.error('加载数据失败，请稍后重试');
  } finally {
    loading.value = false;
  }
};

// 生命周期
onMounted(() => {
  loadData();
});
</script>

<style lang="less" scoped>
.study-style-dashboard {
  min-height: 100%;
  background: linear-gradient(135deg, var(--td-brand-color) 0%, var(--td-brand-color-8) 100%);
  position: relative;
  overflow: hidden;

  .dashboard-header {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    padding: 24px;
    position: relative;
    z-index: 2;

    .header-content {
      margin: 0 auto;
      width: 100%;

      .title-info {
        .title-container {
          position: relative;
          display: inline-block;

          .dashboard-title {
            font-size: 32px;
            font-weight: 700;
            color: var(--td-text-color-anti);
            margin: 0;
            text-shadow: 0 2px 10px var(--td-shadow-1);
            padding-right: 60px;
          }

          .role-tag {
            position: absolute;
            top: -4px;
            right: 0;
            backdrop-filter: blur(10px);
            font-size: 10px;

            :deep(.t-tag__text) {
              color: var(--td-brand-color);
              font-weight: 600;
              font-size: 10px;
            }
          }
        }

        .dashboard-subtitle {
          font-size: 16px;
          color: rgba(255, 255, 255, 0.85);
          margin: 0;
          line-height: 1.5;
        }
      }
    }
  }

  .dashboard-content {
    margin: 0 auto;
    width: 100%;
    padding: 32px 24px;

    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-end;
      margin-bottom: 32px;

      .header-left {
        flex: 1;

        .section-title {
          font-size: 24px;
          font-weight: 700;
          color: var(--td-text-color-anti);
          margin-bottom: 8px;
          display: flex;
          align-items: center;
          gap: 12px;

          .title-icon {
            color: var(--td-text-color-anti);
            filter: drop-shadow(0 2px 4px var(--td-shadow-1));
          }
        }
      }

      .header-right {
        flex-shrink: 0;

        :deep(.t-input) {
          background: rgba(255, 255, 255, 0.1);
          border: 1px solid rgba(255, 255, 255, 0.3);

          .t-input__inner {
            color: var(--td-text-color-anti);

            &::placeholder {
              color: var(--td-text-color-placeholder);
            }
          }

          .t-input__prefix,
          .t-input__suffix {
            color: var(--td-text-color-secondary);
          }

          &:hover {
            border-color: rgba(255, 255, 255, 0.5);
          }
        }
      }
    }

    // 总体统计样式
    .statistics-section {
      margin-bottom: 48px;

      .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
        gap: 20px;

        .stat-card {
          opacity: 0;
          transform: translateY(30px);
          animation: slideInUp 0.6s ease-out var(--delay) both;

          .stat-card-inner {
            background: white;
            border: 1px solid rgba(0, 0, 0, 0.1);
            border-radius: 16px;
            padding: 20px;
            display: flex;
            align-items: center;
            gap: 16px;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;

            &:hover {
              transform: translateY(-4px);
              box-shadow: 0 12px 32px var(--td-shadow-3);
              background: white;
              border-color: rgba(0, 0, 0, 0.2);
            }

            .stat-icon-wrapper {
              width: 48px;
              height: 48px;
              border-radius: 12px;
              display: flex;
              align-items: center;
              justify-content: center;
              flex-shrink: 0;

              &.icon-blue {
                background: var(--td-brand-color);
              }

              &.icon-green {
                background: var(--td-success-color);
              }

              &.icon-orange {
                background: var(--td-warning-color);
              }

              &.icon-purple {
                background: var(--td-brand-color-6);
              }

              :deep(.t-icon) {
                color: var(--td-text-color-anti);
                filter: drop-shadow(0 2px 4px var(--td-shadow-1));
              }
            }

            .stat-content {
              flex: 1;

              .stat-number {
                font-size: 24px;
                font-weight: 700;
                color: var(--td-text-color-primary);
                margin-bottom: 4px;
                line-height: 1;
              }

              .stat-label {
                font-size: 14px;
                color: var(--td-text-color-secondary);
                margin: 0;
                line-height: 1.2;
              }
            }
          }
        }
      }
    }

    // 院系管理样式
    .departments-section {
      .departments-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
        gap: 24px;

        .department-card {
          opacity: 0;
          transform: translateY(30px);
          animation: slideInUp 0.6s ease-out var(--delay) both;

          .department-card-inner {
            background: white;
            border: 1px solid rgba(0, 0, 0, 0.1);
            border-radius: 16px;
            padding: 24px;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            height: 100%;
            display: flex;
            flex-direction: column;

            &:hover {
              transform: translateY(-4px);
              box-shadow: 0 12px 32px var(--td-shadow-3);
              border-color: var(--td-brand-color-3);
            }

            .department-header {
              margin-bottom: 20px;

              .department-name {
                font-size: 18px;
                font-weight: 700;
                color: var(--td-text-color-primary);
                margin: 0 0 8px 0;
                line-height: 1.3;
              }

              .department-code {
                font-size: 12px;
                color: var(--td-text-color-placeholder);
                background: var(--td-bg-color-container-select);
                padding: 4px 8px;
                border-radius: 4px;
                display: inline-block;
                font-weight: 500;
              }
            }

            .department-stats {
              flex: 1;
              margin-bottom: 20px;

              .stats-row {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 16px;
                margin-bottom: 16px;

                &:last-child {
                  margin-bottom: 0;
                }

                .stat-item {
                  display: flex;
                  align-items: flex-start;
                  gap: 8px;

                  .stat-icon {
                    width: 32px;
                    height: 32px;
                    border-radius: 8px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    flex-shrink: 0;

                    &.major-icon {
                      background: var(--td-brand-color-1);
                      color: var(--td-brand-color);
                    }

                    &.student-icon {
                      background: var(--td-success-color-1);
                      color: var(--td-success-color);
                    }

                    &.teacher-icon {
                      background: var(--td-warning-color-1);
                      color: var(--td-warning-color);
                    }

                    &.score-icon {
                      background: var(--td-brand-color-6-1);
                      color: var(--td-brand-color-6);
                    }
                  }

                  .stat-info {
                    flex: 1;

                    .stat-label-with-icon {
                      display: flex;
                      align-items: center;
                      gap: 4px;
                      font-size: 12px;
                      color: var(--td-text-color-secondary);
                      margin-bottom: 4px;

                      .inline-icon {
                        opacity: 0.6;
                      }
                    }

                    .stat-value {
                      font-size: 16px;
                      font-weight: 600;
                      color: var(--td-text-color-primary);

                      &.score-excellent {
                        color: var(--td-success-color);
                      }

                      &.score-good {
                        color: var(--td-brand-color);
                      }

                      &.score-average {
                        color: var(--td-warning-color);
                      }

                      &.score-poor {
                        color: var(--td-error-color);
                      }
                    }
                  }
                }
              }
            }

            .department-actions {
              display: flex;
              gap: 12px;

              :deep(.t-button) {
                flex: 1;

                &.t-button--theme-primary {
                  background: var(--td-brand-color);
                  border-color: var(--td-brand-color);

                  &:hover {
                    background: var(--td-brand-color-7);
                    border-color: var(--td-brand-color-7);
                  }
                }

                &.t-button--theme-success {
                  background: var(--td-success-color);
                  border-color: var(--td-success-color);

                  &:hover {
                    background: var(--td-success-color-7);
                    border-color: var(--td-success-color-7);
                  }
                }
              }
            }
          }
        }
      }
    }

    // 空状态样式
    .empty-state {
      text-align: center;
      padding: 80px 20px;

      .empty-content {
        .empty-icon {
          color: rgba(255, 255, 255, 0.5);
          margin-bottom: 16px;
        }

        .empty-title {
          font-size: 20px;
          font-weight: 600;
          color: var(--td-text-color-anti);
          margin: 0 0 8px 0;
        }

        .empty-description {
          font-size: 14px;
          color: rgba(255, 255, 255, 0.7);
          margin: 0 0 24px 0;
        }

        :deep(.t-button) {
          background: rgba(255, 255, 255, 0.1);
          border-color: rgba(255, 255, 255, 0.3);
          color: var(--td-text-color-anti);

          &:hover {
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.5);
          }
        }
      }
    }
  }
}

// 动画
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .study-style-dashboard {
    .dashboard-content {
      padding: 24px 16px;

      .section-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 16px;

        .header-right {
          width: 100%;

          :deep(.t-input) {
            width: 100% !important;
          }
        }
      }

      .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 16px;
      }

      .departments-grid {
        grid-template-columns: 1fr;
        gap: 16px;
      }
    }
  }
}
</style>

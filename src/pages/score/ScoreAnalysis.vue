<template>
  <div class="score-analysis-container">
    <!-- 头部信息 -->
    <div class="page-header">
      <div class="header-content">
        <div class="title-info">
          <div class="title-row">
            <t-button theme="default" variant="text" @click="handleGoBack" class="back-button">
              <template #icon>
                <t-icon name="chevron-left" />
              </template>
              返回成绩管理
            </t-button>
            <h1 class="page-title">成绩数据分析</h1>
          </div>
          <p class="page-subtitle">课程成绩数据统计与可视化分析</p>
          <div class="page-info">
            <t-tag theme="primary" variant="light">课程ID: {{ courseId }}</t-tag>
            <t-tag theme="success" variant="light">学期: {{ semesterLabel }}</t-tag>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="page-content">
      <!-- 统计概览卡片 -->
      <div class="overview-section">
        <t-row :gutter="[16, 16]">
          <t-col :span="3">
            <div class="overview-card">
              <div class="card-header">
                <t-icon name="user-circle" class="card-icon total-students" />
                <span class="card-title">总学生数</span>
              </div>
              <div class="card-value">{{ overviewData.totalStudents }}</div>
              <div class="card-desc">参与考试学生总数</div>
            </div>
          </t-col>
          <t-col :span="3">
            <div class="overview-card">
              <div class="card-header">
                <t-icon name="check-circle" class="card-icon pass-students" />
                <span class="card-title">通过人数</span>
              </div>
              <div class="card-value">{{ overviewData.passStudents }}</div>
              <div class="card-desc">成绩合格学生数量</div>
            </div>
          </t-col>
          <t-col :span="3">
            <div class="overview-card">
              <div class="card-header">
                <t-icon name="percentage" class="card-icon pass-rate" />
                <span class="card-title">通过率</span>
              </div>
              <div class="card-value">{{ overviewData.passRate }}%</div>
              <div class="card-desc">总体成绩通过率</div>
            </div>
          </t-col>
          <t-col :span="3">
            <div class="overview-card">
              <div class="card-header">
                <t-icon name="chart-bubble" class="card-icon avg-score" />
                <span class="card-title">平均分</span>
              </div>
              <div class="card-value">{{ overviewData.avgScore }}</div>
              <div class="card-desc">学生成绩平均值</div>
            </div>
          </t-col>
        </t-row>
      </div>

      <!-- 图表分析区域 -->
      <t-row :gutter="[16, 16]">
        <t-col :span="12">
          <t-card>
            <template #title>
              <div class="card-header">
                <t-icon name="chart-line" class="title-icon" />
                <span>成绩分布趋势</span>
              </div>
            </template>
            
            <div ref="scoreTrendChartRef" class="chart-container"></div>
          </t-card>
        </t-col>
        
        <t-col :span="12">
          <t-card>
            <template #title>
              <div class="card-header">
                <t-icon name="chart-bar" class="title-icon" />
                <span>成绩分布统计</span>
              </div>
            </template>
            
            <div ref="scoreDistributionChartRef" class="chart-container"></div>
          </t-card>
        </t-col>
      </t-row>

      <t-row :gutter="[16, 16]" style="margin-top: 16px;">
        <t-col :span="8">
          <t-card>
            <template #title>
              <div class="card-header">
                <t-icon name="chart-pie" class="title-icon" />
                <span>成绩等级分布</span>
              </div>
            </template>
            
            <div ref="gradeDistributionChartRef" class="chart-container"></div>
          </t-card>
        </t-col>
        
        <t-col :span="16">
          <t-card>
            <template #title>
              <div class="card-header">
                <t-icon name="view-module" class="title-icon" />
                <span>班级成绩对比</span>
              </div>
            </template>
            
            <div ref="classComparisonChartRef" class="chart-container"></div>
          </t-card>
        </t-col>
      </t-row>

      <!-- 详细数据表格 -->
      <t-card style="margin-top: 16px;">
        <template #title>
          <div class="card-header">
            <t-icon name="table" class="title-icon" />
            <span>班级统计详情</span>
          </div>
        </template>
        
        <t-table
          :data="classStatistics"
          :columns="statisticsColumns"
          :bordered="true"
          :hover="true"
          row-key="className"
        >
          <template #passRate="{ row }">
            <div class="progress-cell">
              <t-progress 
                :percentage="row.passRate" 
                :theme="(row.passRate >= 80 ? 'success' : row.passRate >= 60 ? 'warning' : 'danger') as any"
                size="small"
              />
              <span class="progress-text">{{ row.passRate }}%</span>
            </div>
          </template>
          <template #avgScore="{ row }">
            <span :class="getScoreClass(row.avgScore)">{{ row.avgScore }}</span>
          </template>
        </t-table>
      </t-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, nextTick } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import * as echarts from 'echarts';

const route = useRoute();
const router = useRouter();

// 从路由参数获取数据
const courseId = ref(Number(route.query.courseId) || Number(route.params.courseId) || 1);
const semester = ref(String(route.query.semester) || '2024-spring');

// 学期标签映射
const semesterMap: Record<string, string> = {
  '2025-spring': '2025春季学期',
  '2024-autumn': '2024秋季学期', 
  '2024-spring': '2024春季学期',
  '2023-autumn': '2023秋季学期'
};

const semesterLabel = computed(() => semesterMap[semester.value] || semester.value);

// 图表引用
const scoreTrendChartRef = ref<HTMLElement>();
const scoreDistributionChartRef = ref<HTMLElement>();
const gradeDistributionChartRef = ref<HTMLElement>();
const classComparisonChartRef = ref<HTMLElement>();

// 概览数据
const overviewData = ref({
  totalStudents: 156,
  passStudents: 134,
  passRate: 85.9,
  avgScore: 78.5
});

// 班级统计数据
const classStatistics = ref([
  {
    className: '计算机科学与技术2021-1班',
    totalStudents: 42,
    passStudents: 38,
    passRate: 90.5,
    avgScore: 81.2,
    maxScore: 95,
    minScore: 52
  },
  {
    className: '计算机科学与技术2021-2班', 
    totalStudents: 39,
    passStudents: 32,
    passRate: 82.1,
    avgScore: 76.8,
    maxScore: 92,
    minScore: 45
  },
  {
    className: '软件工程2021-1班',
    totalStudents: 38,
    passStudents: 33,
    passRate: 86.8,
    avgScore: 79.3,
    maxScore: 98,
    minScore: 58
  },
  {
    className: '软件工程2021-2班',
    totalStudents: 37,
    passStudents: 31,
    passRate: 83.8,
    avgScore: 77.1,
    maxScore: 89,
    minScore: 43
  }
]);

// 表格列配置
const statisticsColumns = [
  { colKey: 'className', title: '班级名称', width: 300 },
  { colKey: 'totalStudents', title: '总人数', width: 100, align: 'center' as const },
  { colKey: 'passStudents', title: '通过人数', width: 100, align: 'center' as const },
  { colKey: 'passRate', title: '通过率', width: 200, align: 'center' as const },
  { colKey: 'avgScore', title: '平均分', width: 100, align: 'center' as const },
  { colKey: 'maxScore', title: '最高分', width: 100, align: 'center' as const },
  { colKey: 'minScore', title: '最低分', width: 100, align: 'center' as const }
];

// 获取成绩样式类
const getScoreClass = (score: number) => {
  if (score >= 90) return 'score-excellent';
  if (score >= 80) return 'score-good';
  if (score >= 70) return 'score-medium';
  if (score >= 60) return 'score-pass';
  return 'score-fail';
};

// 返回上一页
const handleGoBack = () => {
  router.back();
};

// 初始化成绩分布趋势图
const initScoreTrendChart = () => {
  if (!scoreTrendChartRef.value) return;
  
  const chart = echarts.init(scoreTrendChartRef.value);
  const option = {
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['平时成绩', '期末成绩', '总成绩']
    },
    xAxis: {
      type: 'category',
      data: ['第1周', '第2周', '第3周', '第4周', '第5周', '第6周', '第7周', '第8周']
    },
    yAxis: {
      type: 'value',
      min: 0,
      max: 100
    },
    series: [
      {
        name: '平时成绩',
        type: 'line',
        smooth: true,
        data: [75, 78, 76, 80, 82, 79, 84, 86]
      },
      {
        name: '期末成绩',
        type: 'line',
        smooth: true,
        data: [72, 74, 73, 77, 79, 76, 81, 83]
      },
      {
        name: '总成绩',
        type: 'line',
        smooth: true,
        data: [74, 76, 75, 79, 81, 78, 83, 85]
      }
    ]
  };
  chart.setOption(option);
};

// 初始化成绩分布统计图
const initScoreDistributionChart = () => {
  if (!scoreDistributionChartRef.value) return;
  
  const chart = echarts.init(scoreDistributionChartRef.value);
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    xAxis: {
      type: 'category',
      data: ['0-59', '60-69', '70-79', '80-89', '90-100']
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '学生人数',
        type: 'bar',
        data: [22, 35, 48, 38, 13],
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#83bff6' },
            { offset: 0.5, color: '#188df0' },
            { offset: 1, color: '#188df0' }
          ])
        }
      }
    ]
  };
  chart.setOption(option);
};

// 初始化成绩等级分布饼图
const initGradeDistributionChart = () => {
  if (!gradeDistributionChartRef.value) return;
  
  const chart = echarts.init(gradeDistributionChartRef.value);
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 10,
      data: ['优秀', '良好', '中等', '及格', '不及格']
    },
    series: [
      {
        name: '成绩等级',
        type: 'pie',
        radius: '50%',
        data: [
          { value: 13, name: '优秀', itemStyle: { color: '#ed7b2f' } },
          { value: 38, name: '良好', itemStyle: { color: '#00a870' } },
          { value: 48, name: '中等', itemStyle: { color: '#0052d9' } },
          { value: 35, name: '及格', itemStyle: { color: '#888' } },
          { value: 22, name: '不及格', itemStyle: { color: '#e34d59' } }
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  };
  chart.setOption(option);
};

// 初始化班级成绩对比图
const initClassComparisonChart = () => {
  if (!classComparisonChartRef.value) return;
  
  const chart = echarts.init(classComparisonChartRef.value);
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    legend: {
      data: ['平均分', '通过率']
    },
    xAxis: [
      {
        type: 'category',
        data: ['计科2021-1班', '计科2021-2班', '软工2021-1班', '软工2021-2班'],
        axisPointer: {
          type: 'shadow'
        }
      }
    ],
    yAxis: [
      {
        type: 'value',
        name: '平均分',
        min: 0,
        max: 100,
        axisLabel: {
          formatter: '{value}'
        }
      },
      {
        type: 'value',
        name: '通过率',
        min: 0,
        max: 100,
        axisLabel: {
          formatter: '{value}%'
        }
      }
    ],
    series: [
      {
        name: '平均分',
        type: 'bar',
        data: [81.2, 76.8, 79.3, 77.1],
        itemStyle: {
          color: '#188df0'
        }
      },
      {
        name: '通过率',
        type: 'line',
        yAxisIndex: 1,
        data: [90.5, 82.1, 86.8, 83.8],
        itemStyle: {
          color: '#00a870'
        }
      }
    ]
  };
  chart.setOption(option);
};

// 初始化所有图表
const initAllCharts = async () => {
  await nextTick();
  initScoreTrendChart();
  initScoreDistributionChart();
  initGradeDistributionChart();
  initClassComparisonChart();
};

onMounted(() => {
  console.log('数据分析页面加载，课程ID:', courseId.value, '学期:', semester.value);
  initAllCharts();
});
</script>

<style lang="less" scoped>
.score-analysis-container {
  padding: 20px;
  
  .page-header {
    background: white;
    border-radius: 12px;
    padding: 24px;
    margin-bottom: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    border: 1px solid rgba(0, 0, 0, 0.06);
    
    .header-content {
      .title-info {
        .title-row {
          display: flex;
          align-items: center;
          gap: 16px;
          margin-bottom: 8px;
          
          .back-button {
            font-size: 14px;
            color: var(--td-text-color-secondary);
            
            &:hover {
              color: var(--td-brand-color);
            }
          }
          
          .page-title {
            font-size: 24px;
            font-weight: 600;
            margin: 0;
            color: var(--td-text-color-primary);
          }
        }
        
        .page-subtitle {
          font-size: 14px;
          color: var(--td-text-color-secondary);
          margin: 0 0 12px 0;
        }
        
        .page-info {
          display: flex;
          gap: 8px;
        }
      }
    }
  }
  
  .page-content {
    .overview-section {
      margin-bottom: 24px;
      
      .overview-card {
        background: white;
        border: 1px solid rgba(0, 0, 0, 0.08);
        border-radius: 12px;
        padding: 20px;
        transition: all 0.3s ease;
        height: 100%;
        
        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
        }
        
        .card-header {
          display: flex;
          align-items: center;
          gap: 8px;
          margin-bottom: 12px;
          
          .card-icon {
            font-size: 20px;
            
            &.total-students {
              color: var(--td-brand-color);
            }
            
            &.pass-students {
              color: var(--td-success-color);
            }
            
            &.pass-rate {
              color: var(--td-warning-color);
            }
            
            &.avg-score {
              color: #722ed1;
            }
          }
          
          .card-title {
            font-size: 14px;
            color: var(--td-text-color-secondary);
            font-weight: 500;
          }
        }
        
        .card-value {
          font-size: 24px;
          font-weight: 700;
          color: var(--td-text-color-primary);
          margin-bottom: 4px;
        }
        
        .card-desc {
          font-size: 12px;
          color: var(--td-text-color-secondary);
        }
      }
    }
    
    .card-header {
      display: flex;
      align-items: center;
      gap: 8px;
      
      .title-icon {
        color: var(--td-brand-color);
      }
    }
    
    .chart-container {
      height: 300px;
      width: 100%;
    }
    
    .progress-cell {
      display: flex;
      align-items: center;
      gap: 8px;
      
      .progress-text {
        font-size: 12px;
        font-weight: 600;
        min-width: 35px;
      }
    }
    
    // 成绩样式
    .score-excellent {
      color: #ed7b2f;
      font-weight: 600;
    }
    
    .score-good {
      color: #00a870;
      font-weight: 600;
    }
    
    .score-medium {
      color: #0052d9;
    }
    
    .score-pass {
      color: #888;
    }
    
    .score-fail {
      color: #e34d59;
      font-weight: 600;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .score-analysis-container {
    padding: 16px;
    
    .page-header {
      .header-content {
        .title-info {
          .title-row {
            flex-direction: column;
            align-items: flex-start;
            gap: 8px;
            
            .page-title {
              font-size: 20px;
            }
          }
          
          .page-info {
            flex-wrap: wrap;
          }
        }
      }
    }
    
    .page-content {
      .chart-container {
        height: 250px;
      }
    }
  }
}
</style>

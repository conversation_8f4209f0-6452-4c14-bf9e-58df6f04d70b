<template>
  <div class="profile-demo">
    <!-- 角色切换器 -->
    <div class="role-switcher">
      <t-card title="个人中心演示" :bordered="false">
        <t-space>
          <t-button 
            :theme="currentRole === 'teacher' ? 'primary' : 'default'"
            @click="switchRole('teacher')"
          >
            <template #icon><t-icon name="user" /></template>
            教师个人中心
          </t-button>
          <t-button 
            :theme="currentRole === 'student' ? 'primary' : 'default'"
            @click="switchRole('student')"
          >
            <template #icon><t-icon name="user-circle" /></template>
            学生个人中心
          </t-button>
        </t-space>
        <div style="margin-top: 16px;">
          <t-alert theme="info" :message="`当前显示：${currentRole === 'teacher' ? '教师' : '学生'}个人中心界面`" />
        </div>
      </t-card>
    </div>

    <!-- 内容展示区域 -->
    <div class="content-area">
      <teacher-profile v-if="currentRole === 'teacher'" />
      <student-profile v-if="currentRole === 'student'" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import TeacherProfile from './TeacherProfile.vue';
import StudentProfile from './StudentProfile.vue';

// 当前角色
const currentRole = ref<'teacher' | 'student'>('teacher');

// 切换角色
const switchRole = (role: 'teacher' | 'student') => {
  currentRole.value = role;
  console.log('切换到角色:', role);
};
</script>

<style lang="less" scoped>
.profile-demo {
  .role-switcher {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
    max-width: 400px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
  }

  .content-area {
    padding-top: 120px; // 为顶部控制器留出空间
  }
}
</style> 
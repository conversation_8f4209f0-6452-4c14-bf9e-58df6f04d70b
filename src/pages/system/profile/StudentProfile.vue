<template>
  <div class="student-profile">
    <t-row :gutter="[24, 24]">
      <!-- 左侧主要内容 -->
      <t-col :flex="3">
        <!-- 问候语和基本信息 -->
        <div class="profile-greeting">
          <div class="greeting-text">
            您好，{{ studentInfo.name || '同学' }}
            <span class="regular">欢迎使用学生学习平台</span>
          </div>
          <t-avatar size="60px" :image="studentInfo.image">
            {{ studentInfo.name ? studentInfo.name.charAt(0) : 'S' }}
          </t-avatar>
        </div>

        <!-- 个人信息详情 -->
        <t-card class="info-card" title="个人信息" :bordered="false">
          <template #actions>
            <t-button theme="primary" variant="text" @click="editProfile">
              <template #icon><t-icon name="edit" /></template>
              编辑
            </t-button>
          </template>
          <t-descriptions :column="3" item-layout="vertical">
            <t-descriptions-item label="姓名">
              {{ studentInfo.name || '-' }}
            </t-descriptions-item>
            <t-descriptions-item label="学号">
              {{ studentInfo.studentId || '-' }}
            </t-descriptions-item>
            <t-descriptions-item label="性别">
              {{ studentInfo.gender || '-' }}
            </t-descriptions-item>
            <t-descriptions-item label="学院">
              {{ studentInfo.college || '-' }}
            </t-descriptions-item>
            <t-descriptions-item label="专业">
              {{ studentInfo.major || '-' }}
            </t-descriptions-item>
            <t-descriptions-item label="班级">
              {{ studentInfo.className || '-' }}
            </t-descriptions-item>
            <t-descriptions-item label="入学年份">
              {{ studentInfo.enrollmentYear || '-' }}
            </t-descriptions-item>
            <t-descriptions-item label="学籍状态">
              <t-tag 
                :theme="getStatusTheme(studentInfo.status) as any" 
                size="small"
              >
                {{ getStatusText(studentInfo.status) }}
              </t-tag>
            </t-descriptions-item>
            <t-descriptions-item label="联系电话">
              {{ studentInfo.phone || '-' }}
            </t-descriptions-item>
          </t-descriptions>
        </t-card>

        <!-- 学习统计 -->
        <t-card class="stats-card" title="学习统计" :bordered="false">
          <t-row :gutter="[16, 16]">
            <t-col :span="3">
              <div class="stat-item">
                <div class="stat-number">{{ studentInfo.totalCredits || 0 }}</div>
                <div class="stat-label">总学分</div>
              </div>
            </t-col>
            <t-col :span="3">
              <div class="stat-item">
                <div class="stat-number">{{ studentInfo.completedCredits || 0 }}</div>
                <div class="stat-label">已修学分</div>
              </div>
            </t-col>
            <t-col :span="3">
              <div class="stat-item">
                <div class="stat-number">{{ studentInfo.gpa || '0.00' }}</div>
                <div class="stat-label">平均绩点</div>
              </div>
            </t-col>
            <t-col :span="3">
              <div class="stat-item">
                <div class="stat-number">{{ studentInfo.rank || '-' }}</div>
                <div class="stat-label">专业排名</div>
              </div>
            </t-col>
          </t-row>
        </t-card>

        <!-- 本学期课程 -->
        <t-card class="courses-card" title="本学期课程" :bordered="false">
          <t-table
            v-if="currentCourses.length > 0"
            :data="currentCourses"
            :columns="courseColumns"
            :pagination="{ pageSize: 10, current: 1, total: currentCourses.length }"
            size="small"
          />
          <t-empty v-else description="本学期暂无课程" />
        </t-card>

        <!-- 学习进度 -->
        <t-card class="progress-card" title="毕业进度" :bordered="false">
          <div class="progress-container">
            <div class="progress-item">
              <div class="progress-label">
                学分完成度
                <span class="progress-value">{{ completionPercentage }}%</span>
              </div>
              <t-progress 
                :percentage="completionPercentage" 
                :theme="getProgressTheme(completionPercentage) as any"
                size="large"
              />
            </div>
            <div class="progress-requirements">
              <t-row :gutter="16">
                <t-col :span="6">
                  <div class="requirement-item">
                    <div class="requirement-title">必修课学分</div>
                    <div class="requirement-progress">
                      {{ studentInfo.requiredCredits || 0 }} / {{ studentInfo.totalRequiredCredits || 0 }}
                    </div>
                  </div>
                </t-col>
                <t-col :span="6">
                  <div class="requirement-item">
                    <div class="requirement-title">选修课学分</div>
                    <div class="requirement-progress">
                      {{ studentInfo.electiveCredits || 0 }} / {{ studentInfo.totalElectiveCredits || 0 }}
                    </div>
                  </div>
                </t-col>
              </t-row>
            </div>
          </div>
        </t-card>
      </t-col>

      <!-- 右侧快捷操作 -->
      <t-col :flex="1">
        <!-- 头像和基本信息 -->
        <t-card class="avatar-card" :bordered="false">
          <div class="avatar-container">
            <t-avatar size="100px" :image="studentInfo.image">
              {{ studentInfo.name ? studentInfo.name.charAt(0) : 'S' }}
            </t-avatar>
            <div class="name">{{ studentInfo.name || '学生' }}</div>
            <div class="position">{{ studentInfo.major || '专业' }}</div>
            <div class="college">{{ studentInfo.college || '' }}</div>
            <div class="class">{{ studentInfo.className || '' }}</div>
          </div>
        </t-card>

        <!-- 快捷操作 -->
        <t-card title="快捷操作" class="quick-actions" :bordered="false">
          <t-list :split="false">
            <t-list-item>
              <t-button theme="primary" variant="text" block @click="goToStudentHome">
                <template #icon><t-icon name="home" /></template>
                学生首页
              </t-button>
            </t-list-item>
            <t-list-item>
              <t-button theme="primary" variant="text" block @click="goToGrades">
                <template #icon><t-icon name="chart" /></template>
                成绩查询
              </t-button>
            </t-list-item>
            <t-list-item>
              <t-button theme="primary" variant="text" block @click="goToHomework">
                <template #icon><t-icon name="file" /></template>
                作业管理
              </t-button>
            </t-list-item>
            <t-list-item>
              <t-button theme="primary" variant="text" block @click="goToExam">
                <template #icon><t-icon name="edit" /></template>
                考试中心
              </t-button>
            </t-list-item>
          </t-list>
        </t-card>

        <!-- 最近动态 -->
        <t-card title="最近动态" class="recent-activity" :bordered="false">
          <t-timeline>
            <t-timeline-item>
              <div class="activity-title">提交了数据结构作业</div>
              <div class="activity-time">1小时前</div>
            </t-timeline-item>
            <t-timeline-item>
              <div class="activity-title">完成了在线测验</div>
              <div class="activity-time">1天前</div>
            </t-timeline-item>
            <t-timeline-item>
              <div class="activity-title">查看了期中考试成绩</div>
              <div class="activity-time">3天前</div>
            </t-timeline-item>
          </t-timeline>
        </t-card>

        <!-- 成绩概览 -->
        <t-card title="成绩概览" class="grade-overview" :bordered="false">
          <div class="grade-summary">
            <div class="grade-item excellent">
              <div class="grade-count">{{ gradeStats.excellent || 0 }}</div>
              <div class="grade-label">优秀(90+)</div>
            </div>
            <div class="grade-item good">
              <div class="grade-count">{{ gradeStats.good || 0 }}</div>
              <div class="grade-label">良好(80-89)</div>
            </div>
            <div class="grade-item average">
              <div class="grade-count">{{ gradeStats.average || 0 }}</div>
              <div class="grade-label">中等(70-79)</div>
            </div>
            <div class="grade-item poor">
              <div class="grade-count">{{ gradeStats.poor || 0 }}</div>
              <div class="grade-label">及格(60-69)</div>
            </div>
          </div>
        </t-card>
      </t-col>
    </t-row>

    <!-- 编辑个人信息对话框 -->
    <t-dialog
      v-model:visible="editDialogVisible"
      title="编辑个人信息"
      width="500px"
      @confirm="saveProfile"
      @cancel="cancelEdit"
    >
      <t-form ref="editFormRef" :data="editForm" :rules="editRules" layout="vertical">
        <t-form-item label="联系电话" name="phone">
          <t-input v-model="editForm.phone" placeholder="请输入联系电话" />
        </t-form-item>
        <t-form-item label="邮箱地址" name="email">
          <t-input v-model="editForm.email" placeholder="请输入邮箱地址" />
        </t-form-item>
      </t-form>
    </t-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { MessagePlugin } from 'tdesign-vue-next';
import { useUserStore } from '@/store';

const router = useRouter();
const userStore = useUserStore();

// 学生信息
const studentInfo = ref({
  id: 0,
  name: '',
  studentId: '',
  gender: '',
  college: '',
  major: '',
  className: '',
  enrollmentYear: '',
  status: 0, // 0: 在读, 1: 休学, 2: 退学, 3: 毕业
  phone: '',
  email: '',
  image: '',
  totalCredits: 0,
  completedCredits: 0,
  gpa: '0.00',
  rank: '',
  requiredCredits: 0,
  totalRequiredCredits: 0,
  electiveCredits: 0,
  totalElectiveCredits: 0
});

// 本学期课程
const currentCourses = ref([
  {
    courseName: '数据结构与算法',
    courseCode: 'CS2001',
    credits: 4,
    teacher: '张教授',
    classroom: '教学楼A101',
    time: '周一 8:00-10:00'
  },
  {
    courseName: '数据库系统原理',
    courseCode: 'CS2002',
    credits: 3,
    teacher: '李教授',
    classroom: '教学楼B201',
    time: '周三 14:00-16:00'
  },
  {
    courseName: '计算机网络',
    courseCode: 'CS2003',
    credits: 3,
    teacher: '王教授',
    classroom: '教学楼C301',
    time: '周二 10:00-12:00'
  },
  {
    courseName: '软件工程',
    courseCode: 'CS2004',
    credits: 3,
    teacher: '陈教授',
    classroom: '实验楼D201',
    time: '周四 14:00-16:00'
  },
  {
    courseName: '操作系统',
    courseCode: 'CS2005',
    credits: 4,
    teacher: '刘教授',
    classroom: '教学楼A203',
    time: '周五 8:00-10:00'
  }
]);

// 成绩统计
const gradeStats = ref({
  excellent: 12,
  good: 18,
  average: 8,
  poor: 3
});

// 课程表格列配置
const courseColumns = [
  { colKey: 'courseName', title: '课程名称', width: 150 },
  { colKey: 'courseCode', title: '课程代码', width: 100 },
  { colKey: 'credits', title: '学分', width: 60 },
  { colKey: 'teacher', title: '授课教师', width: 100 },
  { colKey: 'time', title: '上课时间', width: 120 }
];

// 编辑对话框
const editDialogVisible = ref(false);
const editFormRef = ref();
const editForm = reactive({
  phone: '',
  email: ''
});

const editRules = {
  phone: [
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' as const }
  ],
  email: [
    { pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/, message: '请输入正确的邮箱格式', trigger: 'blur' as const }
  ]
};

// 计算属性
const completionPercentage = computed(() => {
  if (studentInfo.value.totalCredits === 0) return 0;
  return Math.round((studentInfo.value.completedCredits / studentInfo.value.totalCredits) * 100);
});

// 获取学籍状态文本
const getStatusText = (status: number) => {
  const statusMap: Record<number, string> = {
    0: '在读',
    1: '休学',
    2: '退学',
    3: '毕业'
  };
  return statusMap[status] || '未知';
};

// 获取学籍状态主题
const getStatusTheme = (status: number) => {
  const themeMap: Record<number, string> = {
    0: 'success',
    1: 'warning',
    2: 'danger',
    3: 'primary'
  };
  return themeMap[status] || 'default';
};

// 获取进度条主题
const getProgressTheme = (percentage: number) => {
  if (percentage >= 90) return 'success';
  if (percentage >= 70) return 'warning';
  return 'danger';
};

// 加载学生信息 - 使用更丰富的模拟数据
const loadStudentInfo = async () => {
  try {
    // 模拟API调用延迟
    await new Promise(resolve => setTimeout(resolve, 500));
    
    const mockData = {
      id: 1,
      name: '王小明',
      studentId: '2021001001',
      gender: '男',
      college: '计算机科学与技术学院',
      major: '计算机科学与技术',
      className: '计科2101班',
      enrollmentYear: '2021',
      status: 0,
      phone: '13912345678',
      email: '<EMAIL>',
      image: '',
      totalCredits: 160,
      completedCredits: 112,
      gpa: '3.72',
      rank: '15/120',
      requiredCredits: 95,
      totalRequiredCredits: 120,
      electiveCredits: 17,
      totalElectiveCredits: 40
    };
    
    studentInfo.value = { ...mockData };
    console.log('学生信息加载成功:', studentInfo.value);
  } catch (error) {
    console.error('加载学生信息失败:', error);
    MessagePlugin.error('加载学生信息失败');
  }
};

// 编辑个人信息
const editProfile = () => {
  editForm.phone = studentInfo.value.phone;
  editForm.email = studentInfo.value.email;
  editDialogVisible.value = true;
};

// 保存个人信息
const saveProfile = async () => {
  try {
    const valid = await editFormRef.value?.validate();
    if (!valid) return;

    // 这里应该调用API保存数据
    studentInfo.value.phone = editForm.phone;
    studentInfo.value.email = editForm.email;

    editDialogVisible.value = false;
    MessagePlugin.success('个人信息更新成功');
  } catch (error) {
    console.error('保存个人信息失败:', error);
    MessagePlugin.error('保存个人信息失败');
  }
};

// 取消编辑
const cancelEdit = () => {
  editDialogVisible.value = false;
};

// 快捷操作导航
const goToStudentHome = () => {
  router.push('/studentsHome/list');
};

const goToGrades = () => {
  router.push('/studentGrades');
};

const goToHomework = () => {
  router.push('/studentHomework');
};

const goToExam = () => {
  router.push('/studentsPaper');
};

onMounted(() => {
  loadStudentInfo();
});
</script>

<style lang="less" scoped>
.student-profile {
  padding: 16px;
  background-color: var(--td-bg-color-container);

  .profile-greeting {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 24px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 8px;
    margin-bottom: 24px;
    color: white;

    .greeting-text {
      font-size: 24px;
      font-weight: 600;

      .regular {
        font-size: 16px;
        font-weight: 400;
        margin-left: 8px;
        opacity: 0.9;
      }
    }
  }

  .info-card {
    margin-bottom: 24px;

    :deep(.t-descriptions) {
      margin-top: 16px;
    }
  }

  .stats-card {
    margin-bottom: 24px;

    .stat-item {
      text-align: center;
      padding: 16px;
      border-radius: 8px;
      background: var(--td-bg-color-container-hover);

      .stat-number {
        font-size: 28px;
        font-weight: 600;
        color: var(--td-brand-color);
        margin-bottom: 8px;
      }

      .stat-label {
        font-size: 14px;
        color: var(--td-text-color-secondary);
      }
    }
  }

  .courses-card {
    margin-bottom: 24px;
  }

  .progress-card {
    .progress-container {
      .progress-item {
        margin-bottom: 24px;

        .progress-label {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 8px;
          font-weight: 500;

          .progress-value {
            color: var(--td-brand-color);
            font-weight: 600;
          }
        }
      }

      .progress-requirements {
        .requirement-item {
          text-align: center;
          padding: 16px;
          border-radius: 8px;
          background: var(--td-bg-color-container-hover);

          .requirement-title {
            font-size: 14px;
            color: var(--td-text-color-secondary);
            margin-bottom: 8px;
          }

          .requirement-progress {
            font-size: 18px;
            font-weight: 600;
            color: var(--td-text-color-primary);
          }
        }
      }
    }
  }

  .avatar-card {
    margin-bottom: 24px;

    .avatar-container {
      text-align: center;
      padding: 16px;

      .name {
        font-size: 18px;
        font-weight: 600;
        margin: 16px 0 8px;
        color: var(--td-text-color-primary);
      }

      .position {
        font-size: 14px;
        color: var(--td-brand-color);
        margin-bottom: 4px;
      }

      .college {
        font-size: 12px;
        color: var(--td-text-color-secondary);
        margin-bottom: 4px;
      }

      .class {
        font-size: 12px;
        color: var(--td-text-color-placeholder);
      }
    }
  }

  .quick-actions {
    margin-bottom: 24px;

    :deep(.t-list-item) {
      padding: 8px 0;
    }
  }

  .recent-activity {
    margin-bottom: 24px;

    .activity-title {
      font-size: 14px;
      color: var(--td-text-color-primary);
      margin-bottom: 4px;
    }

    .activity-time {
      font-size: 12px;
      color: var(--td-text-color-placeholder);
    }
  }

  .grade-overview {
    .grade-summary {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 12px;

      .grade-item {
        text-align: center;
        padding: 12px;
        border-radius: 6px;

        .grade-count {
          font-size: 20px;
          font-weight: 600;
          margin-bottom: 4px;
        }

        .grade-label {
          font-size: 12px;
        }

        &.excellent {
          background-color: #e6f7ff;
          border: 1px solid #1890ff;
          
          .grade-count {
            color: #1890ff;
          }
          
          .grade-label {
            color: #1890ff;
          }
        }

        &.good {
          background-color: #f6ffed;
          border: 1px solid #52c41a;
          
          .grade-count {
            color: #52c41a;
          }
          
          .grade-label {
            color: #52c41a;
          }
        }

        &.average {
          background-color: #fff7e6;
          border: 1px solid #fa8c16;
          
          .grade-count {
            color: #fa8c16;
          }
          
          .grade-label {
            color: #fa8c16;
          }
        }

        &.poor {
          background-color: #fff2f0;
          border: 1px solid #ff4d4f;
          
          .grade-count {
            color: #ff4d4f;
          }
          
          .grade-label {
            color: #ff4d4f;
          }
        }
      }
    }
  }
}
</style> 
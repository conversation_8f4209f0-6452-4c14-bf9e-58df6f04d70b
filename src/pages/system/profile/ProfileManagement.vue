<template>
  <div class="profile-container">
    <!-- 根据用户角色显示不同的个人中心界面 -->
    <teacher-profile v-if="isTeacher" />
    <student-profile v-else-if="isStudent" />
    
    <!-- 其他角色显示通用个人中心 -->
    <div v-else class="general-profile">
      <t-card title="个人中心" :bordered="false">
        <div class="user-info">
          <t-avatar size="80px">
            {{ userName ? userName.charAt(0) : 'U' }}
          </t-avatar>
          <div class="user-details">
            <h3>{{ userName || '用户' }}</h3>
            <p>角色：{{ userRoleName }}</p>
          </div>
        </div>
        
        <t-divider />
        
        <div class="role-notice">
          <t-alert theme="info" :message="`当前角色为${userRoleName}，暂无专用个人中心界面。`" />
          
          <div class="quick-actions" style="margin-top: 24px;">
            <t-space>
              <t-button theme="primary" @click="goToHome">
                <template #icon><t-icon name="home" /></template>
                返回首页
              </t-button>
              <t-button theme="default" @click="goToSystemHome" v-if="isAdmin">
                <template #icon><t-icon name="setting" /></template>
                系统管理
              </t-button>
            </t-space>
          </div>
        </div>
      </t-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { useUserStore } from '@/store';
import TeacherProfile from './TeacherProfile.vue';
import StudentProfile from './StudentProfile.vue';

const router = useRouter();
const userStore = useUserStore();

// 获取用户信息
const userInfo = computed(() => userStore.userInfo);
const userRole = computed(() => userInfo.value?.type || -1);
const userName = computed(() => userInfo.value?.username || '');

// 角色判断
const isTeacher = computed(() => userRole.value === 3);
const isStudent = computed(() => userRole.value === 4);
const isAdmin = computed(() => userRole.value === 0 || userRole.value === 5);

// 获取角色名称
const userRoleName = computed(() => {
  const roleMap: Record<number, string> = {
    0: '学校管理员',
    1: '专业负责人',
    2: '课题负责人',
    3: '教师',
    4: '学生',
    5: '学院管理员',
    '-1': '游客'
  };
  return roleMap[userRole.value] || '未知角色';
});

// 导航方法
const goToHome = () => {
  // 根据角色跳转到对应的首页
  switch (userRole.value) {
    case 0: // 管理员
    case 5: // 学院管理员
      router.push('/system-home');
      break;
    case 1: // 专业负责人
      router.push('/directers/goal');
      break;
    case 2: // 课题负责人
      router.push('/courses/home');
      break;
    case 3: // 教师
      router.push('/teachers/main');
      break;
    case 4: // 学生
      router.push('/studentsHome/list');
      break;
    default:
      router.push('/');
  }
};

const goToSystemHome = () => {
  router.push('/system-home');
};

onMounted(() => {
  console.log('当前用户角色:', userRole.value, userRoleName.value);
});
</script>

<style lang="less" scoped>
.profile-container {
  min-height: 100vh;
  background-color: var(--td-bg-color-page);
}

.general-profile {
  padding: 24px;
  max-width: 800px;
  margin: 0 auto;

  .user-info {
    display: flex;
    align-items: center;
    gap: 24px;
    padding: 24px 0;

    .user-details {
      h3 {
        margin: 0 0 8px 0;
        font-size: 24px;
        color: var(--td-text-color-primary);
      }

      p {
        margin: 0;
        color: var(--td-text-color-secondary);
        font-size: 16px;
      }
    }
  }

  .role-notice {
    text-align: center;
    padding: 24px 0;
  }
}
</style> 
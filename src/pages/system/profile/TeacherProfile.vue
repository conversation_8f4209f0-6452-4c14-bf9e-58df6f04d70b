<template>
  <div class="teacher-profile">
    <t-row :gutter="[24, 24]">
      <!-- 左侧主要内容 -->
      <t-col :flex="3">
        <!-- 问候语和基本信息 -->
        <div class="profile-greeting">
          <div class="greeting-text">
            您好，{{ teacherInfo.name || '教师' }}老师
            <span class="regular">欢迎使用教师工作台</span>
          </div>
          <t-avatar size="60px" :image="teacherInfo.image">
            {{ teacherInfo.name ? teacherInfo.name.charAt(0) : 'T' }}
          </t-avatar>
        </div>

        <!-- 个人信息详情 -->
        <t-card class="info-card" title="个人信息" :bordered="false">
          <template #actions>
            <t-button theme="primary" variant="text" @click="editProfile">
              <template #icon><t-icon name="edit" /></template>
              编辑
            </t-button>
          </template>
          <t-descriptions :column="3" item-layout="vertical">
            <t-descriptions-item label="姓名">
              {{ teacherInfo.name || '-' }}
            </t-descriptions-item>
            <t-descriptions-item label="工号">
              {{ teacherInfo.number || '-' }}
            </t-descriptions-item>
            <t-descriptions-item label="性别">
              {{ teacherInfo.gender || '-' }}
            </t-descriptions-item>
            <t-descriptions-item label="职称">
              {{ teacherInfo.title || '-' }}
            </t-descriptions-item>
            <t-descriptions-item label="学院">
              {{ teacherInfo.college || '-' }}
            </t-descriptions-item>
            <t-descriptions-item label="联系电话">
              {{ teacherInfo.phone || '-' }}
            </t-descriptions-item>
            <t-descriptions-item label="邮箱">
              {{ teacherInfo.email || '-' }}
            </t-descriptions-item>
          </t-descriptions>
        </t-card>

        <!-- 教学统计 -->
        <t-card class="stats-card" title="教学统计" :bordered="false">
          <t-row :gutter="[16, 16]">
            <t-col :span="4">
              <div class="stat-item">
                <div class="stat-number">{{ teacherInfo.courseCount || 0 }}</div>
                <div class="stat-label">教授课程</div>
              </div>
            </t-col>
            <t-col :span="4">
              <div class="stat-item">
                <div class="stat-number">{{ teacherInfo.studentCount || 0 }}</div>
                <div class="stat-label">学生总数</div>
              </div>
            </t-col>
            <t-col :span="4">
              <div class="stat-item">
                <div class="stat-number">{{ currentSemesterCourses }}</div>
                <div class="stat-label">本学期课程</div>
              </div>
            </t-col>
          </t-row>
        </t-card>

        <!-- 教授课程列表 -->
        <t-card class="courses-card" title="教授课程" :bordered="false">
          <t-list v-if="teacherInfo.courseList && teacherInfo.courseList.length > 0" :split="true">
            <t-list-item v-for="(course, index) in teacherInfo.courseList" :key="index">
              <t-list-item-meta>
                <template #title>
                  <span class="course-name">{{ course }}</span>
                </template>
                <template #description>
                  <t-tag theme="primary" size="small" variant="light">
                    {{ getCurrentSemester() }}
                  </t-tag>
                </template>
              </t-list-item-meta>
            </t-list-item>
          </t-list>
          <t-empty v-else description="暂无教授课程" />
        </t-card>
      </t-col>

      <!-- 右侧快捷操作 -->
      <t-col :flex="1">
        <!-- 头像和基本信息 -->
        <t-card class="avatar-card" :bordered="false">
          <div class="avatar-container">
            <t-avatar size="100px" :image="teacherInfo.image">
              {{ teacherInfo.name ? teacherInfo.name.charAt(0) : 'T' }}
            </t-avatar>
            <div class="name">{{ teacherInfo.name || '教师' }}</div>
            <div class="position">{{ teacherInfo.title || '教师' }}</div>
            <div class="college">{{ teacherInfo.college || '' }}</div>
          </div>
        </t-card>

        <!-- 快捷操作 -->
        <t-card title="快捷操作" class="quick-actions" :bordered="false">
          <t-list :split="false">
            <t-list-item>
              <t-button theme="primary" variant="text" block @click="goToTeacherMain">
                <template #icon><t-icon name="home" /></template>
                教师工作台
              </t-button>
            </t-list-item>
            <t-list-item>
              <t-button theme="primary" variant="text" block @click="goToDocumentSubmit">
                <template #icon><t-icon name="upload" /></template>
                文件提交
              </t-button>
            </t-list-item>
            <t-list-item>
              <t-button theme="primary" variant="text" block @click="goToGradeManagement">
                <template #icon><t-icon name="chart" /></template>
                成绩管理
              </t-button>
            </t-list-item>
          </t-list>
        </t-card>

        <!-- 最近活动 -->
        <t-card title="最近活动" class="recent-activity" :bordered="false">
          <t-timeline>
            <t-timeline-item>
              <div class="activity-title">提交了课程教学大纲</div>
              <div class="activity-time">2小时前</div>
            </t-timeline-item>
            <t-timeline-item>
              <div class="activity-title">批改了期中考试成绩</div>
              <div class="activity-time">1天前</div>
            </t-timeline-item>
            <t-timeline-item>
              <div class="activity-title">上传了课程课件</div>
              <div class="activity-time">3天前</div>
            </t-timeline-item>
          </t-timeline>
        </t-card>
      </t-col>
    </t-row>

    <!-- 编辑个人信息对话框 -->
    <t-dialog
      v-model:visible="editDialogVisible"
      title="编辑个人信息"
      width="600px"
      @confirm="saveProfile"
      @cancel="cancelEdit"
    >
      <t-form ref="editFormRef" :data="editForm" :rules="editRules" layout="vertical">
        <t-row :gutter="16">
          <t-col :span="6">
            <t-form-item label="姓名" name="name">
              <t-input v-model="editForm.name" placeholder="请输入姓名" />
            </t-form-item>
          </t-col>
          <t-col :span="6">
            <t-form-item label="联系电话" name="phone">
              <t-input v-model="editForm.phone" placeholder="请输入联系电话" />
            </t-form-item>
          </t-col>
        </t-row>
        <t-form-item label="邮箱" name="email">
          <t-input v-model="editForm.email" placeholder="请输入邮箱地址" />
        </t-form-item>
      </t-form>
    </t-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { MessagePlugin } from 'tdesign-vue-next';
import { useUserStore } from '@/store';

const router = useRouter();
const userStore = useUserStore();

// 教师信息
const teacherInfo = ref({
  id: 0,
  name: '',
  number: '',
  gender: '',
  title: '',
  college: '',
  courseCount: 0,
  studentCount: 0,
  email: '',
  phone: '',
  image: '',
  courseList: [] as string[]
});

// 编辑对话框
const editDialogVisible = ref(false);
const editFormRef = ref();
const editForm = reactive({
  name: '',
  phone: '',
  email: ''
});

const editRules = {
  name: [{ required: true, message: '请输入姓名', trigger: 'blur' as const }],
  phone: [
    { required: true, message: '请输入联系电话', trigger: 'blur' as const },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' as const }
  ],
  email: [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' as const },
    { pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/, message: '请输入正确的邮箱格式', trigger: 'blur' as const }
  ]
};

// 计算属性
const currentSemesterCourses = computed(() => {
  // 这里可以根据实际业务逻辑计算本学期课程数量
  return Math.min(teacherInfo.value.courseCount, 8);
});

// 获取当前学期
const getCurrentSemester = () => {
  const now = new Date();
  const year = now.getFullYear();
  const month = now.getMonth() + 1;
  
  if (month >= 9 || month <= 1) {
    return `${year}-${year + 1}秋季学期`;
  } else if (month >= 2 && month <= 7) {
    return `${year - 1}-${year}春季学期`;
  } else {
    return `${year}夏季学期`;
  }
};

// 加载教师信息 - 使用模拟数据
const loadTeacherInfo = async () => {
  try {
    // 模拟API调用延迟
    await new Promise(resolve => setTimeout(resolve, 500));
    
    const mockData = {
      id: 1,
      name: '张三教授',
      number: 'T202301001',
      gender: '男',
      title: '教授',
      college: '计算机科学与技术学院',
      courseCount: 8,
      studentCount: 280,
      email: '<EMAIL>',
      phone: '13812345678',
      image: '',
      courseList: [
        '数据结构与算法',
        '计算机网络原理',
        '数据库系统原理',
        '软件工程',
        '操作系统',
        '编译原理',
        '计算机组成原理',
        '人工智能基础'
      ]
    };
    
    teacherInfo.value = { ...mockData };
    console.log('教师信息加载成功:', teacherInfo.value);
  } catch (error) {
    console.error('加载教师信息失败:', error);
    MessagePlugin.error('加载教师信息失败');
  }
};

// 编辑个人信息
const editProfile = () => {
  editForm.name = teacherInfo.value.name;
  editForm.phone = teacherInfo.value.phone;
  editForm.email = teacherInfo.value.email;
  editDialogVisible.value = true;
};

// 保存个人信息
const saveProfile = async () => {
  try {
    const valid = await editFormRef.value?.validate();
    if (!valid) return;

    // 模拟API保存
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    teacherInfo.value.name = editForm.name;
    teacherInfo.value.phone = editForm.phone;
    teacherInfo.value.email = editForm.email;

    editDialogVisible.value = false;
    MessagePlugin.success('个人信息更新成功');
  } catch (error) {
    console.error('保存个人信息失败:', error);
    MessagePlugin.error('保存个人信息失败');
  }
};

// 取消编辑
const cancelEdit = () => {
  editDialogVisible.value = false;
};

// 快捷操作导航
const goToTeacherMain = () => {
  router.push('/teachers/main');
};

const goToDocumentSubmit = () => {
  router.push('/teachers/document-submit');
};

const goToGradeManagement = () => {
  router.push('/teachersGrade');
};

onMounted(() => {
  loadTeacherInfo();
});
</script>

<style lang="less" scoped>
.teacher-profile {
  padding: 16px;
  background-color: var(--td-bg-color-container);

  .profile-greeting {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 24px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 8px;
    margin-bottom: 24px;
    color: white;

    .greeting-text {
      font-size: 24px;
      font-weight: 600;

      .regular {
        font-size: 16px;
        font-weight: 400;
        margin-left: 8px;
        opacity: 0.9;
      }
    }
  }

  .info-card {
    margin-bottom: 24px;

    :deep(.t-descriptions) {
      margin-top: 16px;
    }
  }

  .stats-card {
    margin-bottom: 24px;

    .stat-item {
      text-align: center;
      padding: 16px;
      border-radius: 8px;
      background: var(--td-bg-color-container-hover);

      .stat-number {
        font-size: 32px;
        font-weight: 600;
        color: var(--td-brand-color);
        margin-bottom: 8px;
      }

      .stat-label {
        font-size: 14px;
        color: var(--td-text-color-secondary);
      }
    }
  }

  .courses-card {
    .course-name {
      font-weight: 500;
      color: var(--td-text-color-primary);
    }
  }

  .avatar-card {
    margin-bottom: 24px;

    .avatar-container {
      text-align: center;
      padding: 16px;

      .name {
        font-size: 18px;
        font-weight: 600;
        margin: 16px 0 8px;
        color: var(--td-text-color-primary);
      }

      .position {
        font-size: 14px;
        color: var(--td-brand-color);
        margin-bottom: 4px;
      }

      .college {
        font-size: 12px;
        color: var(--td-text-color-secondary);
      }
    }
  }

  .quick-actions {
    margin-bottom: 24px;

    :deep(.t-list-item) {
      padding: 8px 0;
    }
  }

  .recent-activity {
    .activity-title {
      font-size: 14px;
      color: var(--td-text-color-primary);
      margin-bottom: 4px;
    }

    .activity-time {
      font-size: 12px;
      color: var(--td-text-color-placeholder);
    }
  }
}
</style> 
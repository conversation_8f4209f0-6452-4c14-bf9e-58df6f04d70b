<template>
  <div class="dict-container">
    <t-row :gutter="[16, 16]" class="dict-row">
      <!-- 左侧字典类型列表 -->
      <t-col :xs="24" :xl="6" class="dict-col">
        <t-card :bordered="false" class="dict-card">
          <template #header>
            <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 0;">
              <t-button theme="primary" @click="handleAddType">
                <template #icon><t-icon name="add" /></template>
                新增类型
              </t-button>
              <t-input
                v-model="typeFilter.input"
                placeholder="字典名称/备注"
                clearable
                style="width: 160px"
              />
              <t-select
                v-model="typeFilter.status"
                :options="statusOptions"
                placeholder="全部"
                clearable
                style="width: 90px"
              />
              <t-button theme="primary" @click="onTypeFilter">查询</t-button>
              <t-button variant="outline" @click="onTypeFilterReset">重置</t-button>
            </div>
          </template>

          <t-table
            :data="typeTableData"
            :columns="typeColumns"
            :loading="typeLoading"
            :pagination="typePagination"
            @page-change="onTypePageChange"
            @row-click="handleTypeRowClick"
            hover
            row-key="id"
            class="modern-table"
            style="flex: 1 1 0%; min-height: 0;"
            :show-header="true"
          >
            <template #status="{ row }">
              <t-tag :theme="row.status === 0 ? 'success' : 'danger'">
                {{ row.status === 0 ? '正常' : '停用' }}
              </t-tag>
            </template>
            <template #op="{ row }">
              <t-space>
                <t-button theme="primary" variant="text" @click.stop="handleEditType(row)">
                  编辑
                </t-button>
                <t-button theme="danger" variant="text" @click.stop="handleDeleteType(row)">
                  删除
                </t-button>
              </t-space>
            </template>
          </t-table>
        </t-card>
      </t-col>

      <!-- 右侧字典数据列表 -->
      <t-col :xs="24" :xl="6" class="dict-col">
        <t-card :title="selectedType ? `字典数据 - ${selectedType.title}` : '字典数据'" :bordered="false" class="dict-card">
          <template #header>
            <t-space>
              <t-button theme="primary" @click="handleAddData" :disabled="!selectedType">
                <template #icon><t-icon name="add" /></template>
                新增数据
              </t-button>
            </t-space>
          </template>
          <t-table
            :data="dataTableData"
            :columns="dataColumns"
            :loading="dataLoading"
            :pagination="dataPagination"
            @page-change="onDataPageChange"
            hover
            row-key="id"
            class="modern-table"
            style="flex: 1 1 0%; min-height: 0;"
            :show-header="true"
          >
            <template #defaul="{ row }">
              <t-tag :theme="row.defaul === 1 ? 'primary' : 'default'">
                {{ row.defaul === 1 ? '是' : '否' }}
              </t-tag>
            </template>
            <template #status="{ row }">
              <t-tag :theme="row.status === 0 ? 'success' : 'danger'">
                {{ row.status === 0 ? '正常' : '停用' }}
              </t-tag>
            </template>
            <template #op="{ row }">
              <t-space>
                <t-button theme="primary" variant="text" @click="handleEditData(row)">
                  编辑
                </t-button>
                <t-button theme="danger" variant="text" @click="handleDeleteData(row)">
                  删除
                </t-button>
              </t-space>
            </template>
          </t-table>
        </t-card>
      </t-col>
    </t-row>

    <!-- 字典类型对话框 -->
    <t-dialog
      v-model:visible="typeDialogVisible"
      :header="typeDialogType === 'add' ? '新增字典类型' : '编辑字典类型'"
      :on-confirm="handleSaveType"
    >
      <t-form
        ref="typeForm"
        :data="typeFormData"
        :rules="typeRules"
        :label-width="100"
      >
        <t-form-item label="字典名称" name="title">
          <t-input v-model="typeFormData.title" placeholder="请输入字典名称" />
        </t-form-item>
        <t-form-item label="状态" name="status">
          <t-radio-group v-model="typeFormData.status">
            <t-radio :value="0">正常</t-radio>
            <t-radio :value="9">停用</t-radio>
          </t-radio-group>
        </t-form-item>
        <t-form-item label="备注" name="remark">
          <t-textarea v-model="typeFormData.remark" placeholder="请输入备注" />
        </t-form-item>
      </t-form>
    </t-dialog>

    <!-- 字典数据对话框 -->
    <t-dialog
      v-model:visible="dataDialogVisible"
      :header="dataDialogType === 'add' ? '新增字典数据' : '编辑字典数据'"
      :on-confirm="handleSaveData"
    >
      <t-form
        ref="dataForm"
        :data="dataFormData"
        :rules="dataRules"
        :label-width="100"
      >
        <t-form-item label="字典标签" name="label">
          <t-input v-model="dataFormData.label" placeholder="请输入字典标签" />
        </t-form-item>
        <t-form-item label="字典键值" name="value">
          <t-input v-model="dataFormData.value" placeholder="请输入字典键值" />
        </t-form-item>
        <t-form-item label="排序" name="sort">
          <t-input-number v-model="dataFormData.sort" :min="0" />
        </t-form-item>
        <t-form-item label="CSS样式" name="cssClass">
          <t-input v-model="dataFormData.cssClass" placeholder="请输入CSS样式类名" />
        </t-form-item>
        <t-form-item label="列表样式" name="listClass">
          <t-input v-model="dataFormData.listClass" placeholder="请输入列表样式类名" />
        </t-form-item>
        <t-form-item label="是否默认" name="defaul">
          <t-radio-group v-model="dataFormData.defaul">
            <t-radio :value="1">是</t-radio>
            <t-radio :value="0">否</t-radio>
          </t-radio-group>
        </t-form-item>
        <t-form-item label="状态" name="status">
          <t-radio-group v-model="dataFormData.status">
            <t-radio :value="0">正常</t-radio>
            <t-radio :value="9">停用</t-radio>
          </t-radio-group>
        </t-form-item>
        <t-form-item label="备注" name="remark">
          <t-textarea v-model="dataFormData.remark" placeholder="请输入备注" />
        </t-form-item>
      </t-form>
    </t-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue';
import { MessagePlugin } from 'tdesign-vue-next';
import type { PageInfo, TableRowData, FormRules } from 'tdesign-vue-next';
import { getDictTypeListPage, addDictType, updateDictType, deleteDictType } from '@/api/system/dict';
import { getDictDataList, addDictData, updateDictData, deleteDictData } from '@/api/system/dict';

// 字典类型相关
const typeLoading = ref(false);
const typeTableData = ref([]);
const typeDialogVisible = ref(false);
const typeDialogType = ref<'add' | 'edit'>('add');
const typeFormData = ref({
  id: undefined,
  title: '',
  status: 0,
  remark: ''
});

const typeColumns = [
  { colKey: 'id', title: '字典ID', width: 100 },
  { colKey: 'title', title: '字典名称', width: 200 },
  { colKey: 'status', title: '状态', width: 100 },
  { colKey: 'remark', title: '备注' },
  { colKey: 'op', title: '操作', width: 160 }
];

const typePagination = ref({
  current: 1,
  pageSize: 10,
  total: 0
});

const typeRules: FormRules = {
  title: [{ required: true, message: '请输入字典名称', type: 'error' }],
  status: [{ required: true, message: '请选择状态', type: 'error' }]
};

// 字典数据相关
const dataLoading = ref(false);
const dataTableData = ref([]);
const dataDialogVisible = ref(false);
const dataDialogType = ref<'add' | 'edit'>('add');
const selectedType = ref<any>(null);
const dataFormData = ref({
  id: undefined,
  typeId: '',
  label: '',
  value: '',
  sort: 0,
  cssClass: '',
  listClass: '',
  defaul: 0,
  status: 0,
  remark: ''
});

const dataColumns = [
  { colKey: 'id', title: '字典编码', width: 100 },
  { colKey: 'label', title: '字典标签', width: 150 },
  { colKey: 'value', title: '字典键值', width: 150 },
  { colKey: 'sort', title: '排序', width: 80 },
  { colKey: 'cssClass', title: 'CSS样式', width: 120 },
  { colKey: 'listClass', title: '列表样式', width: 120 },
  { colKey: 'defaul', title: '是否默认', width: 100 },
  { colKey: 'status', title: '状态', width: 80 },
  { colKey: 'remark', title: '备注', width: 150 },
  { colKey: 'op', title: '操作', width: 160 }
];

const dataPagination = ref({
  current: 1,
  pageSize: 10,
  total: 0
});

const dataRules: FormRules = {
  label: [{ required: true, message: '请输入字典标签', type: 'error' }],
  value: [{ required: true, message: '请输入字典键值', type: 'error' }],
  status: [{ required: true, message: '请选择状态', type: 'error' }]
};

// 筛选相关
const typeFilter = ref<{
  input: string;
  status: '' | 0 | 9;
}>({
  input: '',
  status: ''
});

const statusOptions = [
  { label: '全部', value: '' },
  { label: '正常', value: 0 },
  { label: '停用', value: 9 }
];

const onTypeFilter = () => {
  typePagination.value.current = 1;
  fetchDictTypeList();
};

const onTypeFilterReset = () => {
  typeFilter.value = { input: '', status: '' };
  typePagination.value.current = 1;
  fetchDictTypeList();
};

// 获取字典类型列表
const fetchDictTypeList = async () => {
  typeLoading.value = true;
  try {
    const params = {
      current: typePagination.value.current,
      size: typePagination.value.pageSize,
      title: typeFilter.value.input,
      remark: typeFilter.value.input,
      status: typeFilter.value.status
    };
    const res = await getDictTypeListPage(params);
    typeTableData.value = res.data.records;
    typePagination.value.total = res.data.total;
  } catch (error) {
    console.error('获取字典类型列表失败:', error);
  } finally {
    typeLoading.value = false;
  }
};

// 获取字典数据列表
const fetchDictDataList = async () => {
  if (!selectedType.value) return;
  console.log(selectedType.value);
  dataLoading.value = true;
  try {
    const res = await getDictDataList(selectedType.value.id, {
      current: dataPagination.value.current,
      size: dataPagination.value.pageSize
    });
    // 处理分页响应数据
    if (res.data.records) {
      dataTableData.value = res.data.records;
      dataPagination.value.total = res.data.total;
    } else {
      // 兼容非分页响应
      dataTableData.value = res.data;
    }
  } catch (error) {
    console.error('获取字典数据列表失败:', error);
  } finally {
    dataLoading.value = false;
  }
};

// 字典类型行点击
const handleTypeRowClick = (row: any) => {
  selectedType.value = row.row;
  dataPagination.value.current = 1;
  fetchDictDataList();
};

// 新增字典类型
const handleAddType = () => {
  typeDialogType.value = 'add';
  typeFormData.value = {
    id: undefined,
    title: '',
    status: 0,
    remark: ''
  };
  typeDialogVisible.value = true;
};

// 编辑字典类型
const handleEditType = (row: any) => {
  typeDialogType.value = 'edit';
  typeFormData.value = { ...row };
  typeDialogVisible.value = true;
};

// 删除字典类型
const handleDeleteType = async (row: any) => {
  try {
    await deleteDictType(row.id);
    MessagePlugin.success('删除成功');
    if (selectedType.value?.id === row.id) {
      selectedType.value = null;
      dataTableData.value = [];
    }
    fetchDictTypeList();
  } catch (error) {
    console.error('删除字典类型失败:', error);
  }
};

// 保存字典类型
const handleSaveType = async () => {
  try {
    if (typeDialogType.value === 'add') {
      await addDictType(typeFormData.value);
      MessagePlugin.success('添加成功');
    } else {
      await updateDictType(typeFormData.value);
      MessagePlugin.success('更新成功');
    }
    typeDialogVisible.value = false;
    fetchDictTypeList();
  } catch (error) {
    console.error('保存字典类型失败:', error);
  }
};

// 新增字典数据
const handleAddData = () => {
  dataDialogType.value = 'add';
  dataFormData.value = {
    id: undefined,
    typeId: selectedType.value.id,
    label: '',
    value: '',
    sort: 0,
    cssClass: '',
    listClass: '',
    defaul: 0,
    status: 0,
    remark: ''
  };
  dataDialogVisible.value = true;
};

// 编辑字典数据
const handleEditData = (row: any) => {
  dataDialogType.value = 'edit';
  dataFormData.value = { ...row };
  dataDialogVisible.value = true;
};

// 删除字典数据
const handleDeleteData = async (row: any) => {
  try {
    await deleteDictData(row.id);
    MessagePlugin.success('删除成功');
    fetchDictDataList();
  } catch (error) {
    console.error('删除字典数据失败:', error);
  }
};

// 保存字典数据
const handleSaveData = async () => {
  try {
    if (dataDialogType.value === 'add') {
      await addDictData(dataFormData.value);
      MessagePlugin.success('添加成功');
    } else {
      await updateDictData(dataFormData.value);
      MessagePlugin.success('更新成功');
    }
    dataDialogVisible.value = false;
    fetchDictDataList();
  } catch (error) {
    console.error('保存字典数据失败:', error);
  }
};

// 分页变化
const onTypePageChange = (pageInfo: PageInfo) => {
  typePagination.value.current = pageInfo.current;
  fetchDictTypeList();
};

const onDataPageChange = (pageInfo: PageInfo) => {
  dataPagination.value.current = pageInfo.current;
  fetchDictDataList();
};

onMounted(() => {
  fetchDictTypeList();
});
</script>

<style lang="less" scoped>
.dict-container {
  height: calc(100vh - 172px);
  min-height: 500px;
  padding: 0;
  box-sizing: border-box;
  overflow: visible;

  .dict-row {
    height: 100%;
  }
  .dict-col {
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  .dict-card {
    height: 100%;
    display: flex;
    flex-direction: column;
    box-shadow: 0 6px 24px 0 rgba(0,0,0,0.06);
    border-radius: 16px;
    background: #fff;
    overflow: hidden;
    border: none;
    padding: 0;
    transition: box-shadow 0.2s;
  }
  :deep(.modern-table) {
    flex: 1 1 0%;
    height: 0;
    min-height: 70vh;
    border: none;
    border-radius: 12px;
    background: #fff;
    font-size: 15px;
    color: #222;
    .t-table__header {
      background: linear-gradient(90deg, #f0f3fa 0%, #f7f8fa 100%);
      font-weight: 600;
      color: #222;
      font-size: 15px;
      letter-spacing: 0.5px;
      position: sticky;
      top: 0;
      z-index: 2;
    }
    .t-table__content {
      overflow-y: auto !important;
    }
    .t-table__row {
      transition: background 0.2s;
      font-size: 15px;
      height: 48px;
    }
    .t-table__row:hover {
      background: #e6f7ff;
    }
    .t-table__cell {
      border-bottom: 1px solid #f0f0f0;
      padding: 12px 10px;
    }
    .t-table__row:last-child .t-table__cell {
      border-bottom: none;
    }
    .t-table__empty {
      color: #999;
      font-size: 15px;
      background: #fafbfc;
      border-radius: 0 0 12px 12px;
      padding: 40px 0;
    }
    .t-table__pagination, .t-pagination {
      margin-bottom: 0 !important;
      padding-bottom: 0 !important;
    }
  }
  // 仅美化筛选区（卡片header内flex区域）的表单和按钮，弹窗内表单控件保持默认
  :deep(.dict-card > div[style*='display: flex']) {
    font-size: 15px;
    .t-input, .t-select {
      border-radius: 8px;
      background: #f7f8fa;
      border: 1px solid #e5e6eb;
      font-size: 15px;
      transition: border 0.2s;
    }
    .t-input:focus, .t-select:focus {
      border: 1.5px solid #1890ff;
      background: #fff;
    }
    .t-button {
      border-radius: 8px;
      font-size: 15px;
      padding: 0 18px;
      height: 36px;
      box-shadow: none;
      transition: background 0.2s, color 0.2s;
    }
    .t-button[theme='primary'] {
      background: linear-gradient(90deg, #1890ff 0%, #40a9ff 100%);
      color: #fff;
      border: none;
    }
    .t-button[variant='outline'] {
      background: #fff;
      color: #1890ff;
      border: 1px solid #1890ff;
    }
    .t-button:active {
      filter: brightness(0.95);
    }
  }
}
</style>

<template>
  <div class="role-container">
    <t-card>
      <t-space direction="vertical" size="large" style="width: 100%">
        <!-- 搜索表单 -->
        <t-form :data="searchForm" layout="inline">
          <t-form-item label="角色名称" name="roleName">
            <t-input v-model="searchForm.title" placeholder="请输入角色名称" />
          </t-form-item>
          <t-form-item label="角色标识" name="role">
            <t-input v-model="searchForm.code" placeholder="请输入角色标识" />
          </t-form-item>
          <t-form-item label="状态" name="status">
            <t-select v-model="searchForm.status" placeholder="请选择状态">
              <t-option value="" label="全部" />
              <t-option :value="0" label="启用" />
              <t-option :value="1" label="禁用" />
            </t-select>
          </t-form-item>
          <t-form-item>
            <t-space>
              <t-button theme="primary" @click="handleSearch">
                <template #icon><SearchIcon /></template>
                搜索
              </t-button>
              <t-button theme="default" @click="handleReset">
                <template #icon><RefreshIcon /></template>
                重置
              </t-button>
            </t-space>
          </t-form-item>
        </t-form>

        <!-- 操作按钮 -->
        <t-space>
          <t-button theme="primary" @click="handleAdd">
            <template #icon><PlusIcon /></template>
            新增角色
          </t-button>
        </t-space>

        <!-- 角色列表 -->
        <t-table
          :data="roleList"
          :columns="columns"
          :loading="loading"
          row-key="roleId"
          :pagination="pagination"
          @page-change="onPageChange"
        >
          <template #status="{ row }">
            <t-tag :theme="row.status === 0 ? 'success' : 'danger'">
              {{ row.status === 0 ? '启用' : '禁用' }}
            </t-tag>
          </template>
          <template #action="{ row }">
            <t-space>
              <t-button theme="primary" variant="text" @click="handleEdit(row)">
                <template #icon><EditIcon /></template>
                编辑
              </t-button>
              <t-button theme="primary" variant="text" @click="handleAssignPermissions(row)">
                <template #icon><LockOnIcon /></template>
                分配权限
              </t-button>
              <t-button
                :theme="row.status === 0 ? 'danger' : 'success'"
                variant="text"
                @click="handleStatusChange(row)"
              >
                <template #icon>
                  <component :is="row.status === 0 ? LockOnIcon : CheckIcon" />
                </template>
                {{ row.status === 0 ? '禁用' : '启用' }}
              </t-button>
              <t-button theme="danger" variant="text" @click="handleDelete(row)">
                <template #icon><DeleteIcon /></template>
                删除
              </t-button>
            </t-space>
          </template>
        </t-table>
      </t-space>
    </t-card>

    <!-- 角色表单对话框 -->
    <t-dialog
      v-model:visible="modalVisible"
      :header="modalTitle"
      :width="680"
      :footer="false"
    >
      <template #body>
        <t-form
          ref="formRef"
          :data="formState"
          :rules="rules"
          @submit="handleSave"
        >
          <t-form-item label="角色名称" name="title">
            <t-input v-model="formState.name" placeholder="请输入角色名称" />
          </t-form-item>
          <t-form-item label="角色标识" name="code">
            <t-input v-model="formState.code" placeholder="请输入角色标识" />
          </t-form-item>
          <t-form-item label="排序" name="sort">
            <t-input-number v-model="formState.sort" placeholder="请输入排序号" />
          </t-form-item>
          <t-form-item label="备注" name="remark">
            <t-textarea v-model="formState.remark" placeholder="请输入备注信息" />
          </t-form-item>
          <t-form-item label="状态" name="status">
            <t-radio-group v-model="formState.status">
              <t-radio :value="0">启用</t-radio>
              <t-radio :value="1">禁用</t-radio>
            </t-radio-group>
          </t-form-item>
          <t-form-item>
            <t-space>
              <t-button theme="primary" type="submit">确定</t-button>
              <t-button theme="default" @click="modalVisible = false">取消</t-button>
            </t-space>
          </t-form-item>
        </t-form>
      </template>
    </t-dialog>

    <!-- 权限分配对话框 -->
    <t-dialog
      v-model:visible="permissionModalVisible"
      header="分配权限"
      :width="800"
      :footer="false"
    >
      <template #body>
        <t-form
          ref="permissionFormRef"
          :data="permissionFormState"
          @submit="handleSavePermissions"
          class="permission-form"
        >
          <t-form-item label="角色名称">
            <span>{{ currentRole?.name }}</span>
          </t-form-item>
          <t-form-item label="权限菜单" name="ids" class="permission-tree-item">
            <t-tree
              v-model="permissionFormState.ids"
              :data="menuTree"
              :keys="{
                value: 'id',
                label: 'title',
                children: 'children'
              }"
              checkable
              hover
              expand-all
              class="permission-tree"
            >
              <template #label="{ node }">
                <template v-if="node.data.type === 2">
                  <div class="permission-tree-item-button" style="color:red;">
                    {{ node.data.title }}
                  </div>
                </template>
                <template v-else>
                  {{ node.data.title }}
                </template>
              </template>
            </t-tree>
          </t-form-item>
          <t-form-item>
            <t-space>
              <t-button theme="primary" type="submit">确定</t-button>
              <t-button theme="default" @click="permissionModalVisible = false">取消</t-button>
            </t-space>
          </t-form-item>
        </t-form>
      </template>
    </t-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, getCurrentInstance } from 'vue';
import {
  PlusIcon,
  EditIcon,
  LockOnIcon,
  DeleteIcon,
  CheckIcon,
  SearchIcon,
  RefreshIcon,
} from 'tdesign-icons-vue-next';
import type { FormInstanceFunctions, FormRule, SubmitContext, PageInfo } from 'tdesign-vue-next';
import { getRoleList, addRole, updateRole, deleteRole, updateRoleStatus, updateRoleMenu, getRolePermissions, updateRolePermissions } from '@/api/system/role';
import {getMenuTree, getRoleMenuIds} from "@/api/system/menu";

interface Role {
  id: number;
  name: string;
  code: string;
  sort: number;
  remark: string;
  status: number;
  createTime: string;
  modifyTime: string;
  creator: number;
  modifier: number;
}

interface Menu {
  id: number;
  title: string;
  children?: Menu[];
}

interface SearchForm {
  title: string;
  code: string;
  status: number | '';
}

const columns = [
  { colKey: 'name', title: '角色名称', width: 150 },
  { colKey: 'code', title: '角色标识', width: 150 },
  { colKey: 'sort', title: '排序', width: 80 },
  { colKey: 'status', title: '状态', width: 100 },
  { colKey: 'createTime', title: '创建时间', width: 180 },
  { colKey: 'action', title: '操作', width: 280 },
];

const roleList = ref<Role[]>([]);
const menuTree = ref<Menu[]>([]);
const loading = ref(false);
const modalVisible = ref(false);
const modalTitle = ref('新增角色');
const permissionModalVisible = ref(false);
const currentRole = ref<Role | null>(null);

const searchForm = reactive<SearchForm>({
  title: '',
  code: '',
  status: '',
});

const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
});

const formRef = ref<FormInstanceFunctions>();
const formState = reactive({
  id: null as number | null,
  name: '',
  code: '',
  sort: 0,
  remark: '',
  status: 0,
});

const rules: Record<string, FormRule[]> = {
  roleName: [
    { required: true, message: '请输入角色名称', type: 'error' as const },
  ],
  role: [
    { required: true, message: '请输入角色标识', type: 'error' as const },
  ],
  sort: [
    { required: true, message: '请输入排序号', type: 'error' as const },
  ],
};

// 权限分配表单
const permissionFormRef = ref<FormInstanceFunctions>();
const permissionFormState = reactive({
  rid: null as number | null,
  ids: [] as number[],
});

const { proxy } = getCurrentInstance();

// 获取角色列表
const fetchRoleList = async () => {
  loading.value = true;
  try {
    const res = await getRoleList({
      title: searchForm.title || undefined,
      code: searchForm.code || undefined,
      status: searchForm.status === '' ? undefined : searchForm.status,
      current: pagination.current,
      size: pagination.pageSize,
    });
    roleList.value = res.data.records || [];
    pagination.total = res.data.total || 0;
  } catch (error) {
    console.error('获取角色列表失败:', error);
    proxy.$baseMessage('获取角色列表失败', 'error');
  } finally {
    loading.value = false;
  }
};

// 获取菜单树
const fetchMenuTree = async () => {
  try {
    const res = await getMenuTree();
    menuTree.value = res.data;
  } catch (error) {
    console.error('获取菜单树失败:', error);
    proxy.$baseMessage('获取菜单树失败', 'error');
  }
};

// 获取角色权限
const fetchRolePermissions = async (roleId: number) => {
  try {
    const res = await getRoleMenuIds(roleId);
    permissionFormState.ids = res.data || [];
  } catch (error) {
    console.error('获取角色权限失败:', error);
    proxy.$baseMessage('获取角色权限失败', 'error');
  }
};

// 搜索
const handleSearch = () => {
  pagination.current = 1;
  fetchRoleList();
};

// 重置
const handleReset = () => {
  searchForm.title = '';
  searchForm.code = '';
  searchForm.status = '';
  pagination.current = 1;
  fetchRoleList();
};

// 分页变化
const onPageChange = (pageInfo: PageInfo) => {
  pagination.current = pageInfo.current;
  fetchRoleList();
};

// 新增角色
const handleAdd = () => {
  modalTitle.value = '新增角色';
  modalVisible.value = true;
  formState.id = null;
  formState.name = '';
  formState.code = '';
  formState.sort = 0;
  formState.remark = '';
  formState.status = 0;
};

// 编辑角色
const handleEdit = (record: Role) => {
  modalTitle.value = '编辑角色';
  modalVisible.value = true;
  formState.id = record.id;
  formState.name = record.name;
  formState.code = record.code;
  formState.sort = record.sort;
  formState.remark = record.remark;
  formState.status = record.status;
};

// 删除角色
const handleDelete = async (record: Role) => {
  const confirmed = await proxy.$baseConfirm('确认删除该角色？', '提示');
  if (confirmed) {
    await deleteRole(record.id);
    proxy.$baseMessage('删除成功', 'success');
    await fetchRoleList();
  }
};

// 打开权限分配对话框
const handleAssignPermissions = async (record: Role) => {
  currentRole.value = record;
  permissionFormState.rid = record.id;
  permissionModalVisible.value = true;
  await Promise.all([
    fetchMenuTree(),
    fetchRolePermissions(record.id)
  ]);
};

// 提交权限分配
const handleSavePermissions = async ({ validateResult }: SubmitContext) => {
  if (validateResult === true) {
    try {
      await updateRoleMenu({
        rid: currentRole.value.id,
        ids: permissionFormState.ids
      });
      proxy.$baseMessage('权限分配成功', 'success');
      permissionModalVisible.value = false;
    } catch (error) {
      console.error('权限分配失败:', error);
    }
  }
};

// 提交表单
const handleSave = async ({ validateResult }: SubmitContext) => {
  if (validateResult === true) {
    try {
      if (formState.id) {
        await updateRole(formState);
      } else {
        await addRole(formState);
      }
      proxy.$baseMessage('保存成功', 'success');
      modalVisible.value = false;
      fetchRoleList();
    } catch (error) {
      console.error('保存失败:', error);
      proxy.$baseMessage('保存失败', 'error');
    }
  }
};

// 切换角色状态
const handleStatusChange = async (record: Role) => {
  try {
    await updateRoleStatus({
      id: record.id,
      status: record.status === 0 ? 1 : 0
    });
    proxy.$baseMessage('状态切换成功', 'success');
    fetchRoleList();
  } catch (error) {
    console.error('状态切换失败:', error);
    proxy.$baseMessage('状态切换失败', 'error');
  }
};

onMounted(() => {
  fetchRoleList();
  fetchMenuTree();
});
</script>

<style scoped>
.role-container {
  padding: 16px;
}

.permission-form {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.permission-tree-item {
  flex: 1;
  margin-bottom: 0;
}

.permission-tree {
  height: 500px;
  overflow-y: auto;
  border: 1px solid var(--td-component-border);
  border-radius: var(--td-radius-medium);
  padding: 8px;
  width: 100%;
  :deep(){
    .t-tree__item:has(.permission-tree-item-button){
      display: inline-block;
    }
  }
}

.tree-node {
  padding: 4px 0;
}

.button-checkbox {
  margin: 4px;
  background-color: var(--td-brand-color-1);
  padding: 2px 8px;
  border-radius: var(--td-radius-small);
}

.button-checkbox :deep(.t-checkbox__label) {
  color: var(--td-brand-color);
  font-size: 12px;
}
</style>

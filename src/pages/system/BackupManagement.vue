<template>
  <div class="backup-management-container">
    <t-card title="数据备份管理">
      <template #actions>
        <t-space>
          <t-button theme="primary" @click="handleManualBackup">
            <template #icon><t-icon name="download" /></template>
            手动备份
          </t-button>
          <t-button theme="default" @click="showAutoBackupConfig = true">
            <template #icon><t-icon name="setting" /></template>
            自动备份设置
          </t-button>
        </t-space>
      </template>

      <!-- 统计信息卡片 -->
      <t-row :gutter="16" class="statistics-cards">
        <t-col :span="6">
          <t-card hover-shadow>
            <template #title>
              <t-space>
                <t-icon name="database" />
                <span>备份次数</span>
              </t-space>
            </template>
            <div class="statistics-value">{{ statistics.totalBackups }}</div>
          </t-card>
        </t-col>
        <t-col :span="6">
          <t-card hover-shadow>
            <template #title>
              <t-space>
                <t-icon name="file" />
                <span>总备份大小</span>
              </t-space>
            </template>
            <div class="statistics-value">{{ statistics.totalSize }}</div>
          </t-card>
        </t-col>
        <t-col :span="6">
          <t-card hover-shadow>
            <template #title>
              <t-space>
                <t-icon name="time" />
                <span>最近备份时间</span>
              </t-space>
            </template>
            <div class="statistics-value">{{ statistics.lastBackupTime }}</div>
          </t-card>
        </t-col>
        <t-col :span="6">
          <t-card hover-shadow>
            <template #title>
              <t-space>
                <t-icon name="user" />
                <span>最近备份人</span>
              </t-space>
            </template>
            <div class="statistics-value">{{ statistics.lastBackupUser }}</div>
          </t-card>
        </t-col>
      </t-row>

      <t-loading :loading="loading">
        <t-table
          :data="data"
          :columns="columns"
          row-key="backupId"
          hover
          stripe
          :pagination="pagination"
        >
          <template #serial-number="{ rowIndex }">
            {{ (pagination.current - 1) * pagination.pageSize + rowIndex + 1 }}
          </template>
          <template #backupType="{ row }">
            <t-tag theme="primary" v-if="row.backupType === 1">手动备份</t-tag>
            <t-tag theme="success" v-else-if="row.backupType === 2">自动备份</t-tag>
            <t-tag v-else>其他</t-tag>
          </template>
          <template #status="{ row }">
            <t-tag :theme="row.status === 1 ? 'success' : 'danger'">
              {{ row.status === 1 ? '成功' : '失败' }}
            </t-tag>
          </template>
          <template #operation="slotProps">
            <t-space>
              <t-tooltip content="预览">
                <t-button size="small" shape="circle" variant="text" theme="primary" @click="handlePreview(slotProps.row)">
                  <template #icon><t-icon name="view" /></template>
                </t-button>
              </t-tooltip>
              <t-tooltip content="恢复">
                <t-button size="small" shape="circle" variant="text" theme="warning" @click="handleRestore(slotProps.row)">
                  <template #icon><t-icon name="refresh" /></template>
                </t-button>
              </t-tooltip>
              <t-tooltip content="下载">
                <t-button size="small" shape="circle" variant="text" theme="primary" @click="handleDownload(slotProps.row)">
                  <template #icon><t-icon name="download" /></template>
                </t-button>
              </t-tooltip>
              <t-tooltip content="删除">
                <t-button size="small" shape="circle" variant="text" theme="danger" @click="handleDelete(slotProps.row)">
                  <template #icon><t-icon name="delete" /></template>
                </t-button>
              </t-tooltip>
            </t-space>
          </template>
        </t-table>
      </t-loading>
    </t-card>

    <!-- 自动备份设置对话框 -->
    <t-dialog
      v-model:visible="showAutoBackupConfig"
      header="自动备份设置"
      :width="500"
      :footer="false"
      @close="showAutoBackupConfig = false"
    >
      <t-form :data="autoBackupConfig" label-width="120px">
        <t-form-item label="启用自动备份">
          <t-switch v-model="autoBackupConfig.enabled" />
        </t-form-item>
        <t-form-item label="备份频率">
          <t-radio-group v-model="autoBackupConfig.frequency">
            <t-radio value="daily">每天</t-radio>
            <t-radio value="weekly">每周</t-radio>
            <t-radio value="monthly">每月</t-radio>
          </t-radio-group>
        </t-form-item>
        <t-form-item label="备份时间">
          <t-time-picker v-model="autoBackupConfig.time" />
        </t-form-item>
        <t-form-item label="保留天数">
          <t-input-number v-model="autoBackupConfig.retentionDays" :min="1" :max="365" />
        </t-form-item>
      </t-form>
      <template #footer>
        <t-space>
          <t-button theme="default" @click="showAutoBackupConfig = false">取消</t-button>
          <t-button theme="primary" @click="handleSaveAutoBackupConfig">保存</t-button>
        </t-space>
      </template>
    </t-dialog>

    <!-- 备份文件预览对话框 -->
    <t-dialog
      v-model:visible="previewVisible"
      header="备份文件预览"
      :width="800"
      :footer="false"
      @close="previewVisible = false"
    >
      <template v-if="previewData">
        <t-descriptions :data="[
          { label: '备份文件名', value: previewData.backupName },
          { label: '文件大小', value: previewData.backupSize },
          { label: '备份时间', value: previewData.createTime },
          { label: '备份人', value: previewData.createBy },
          { label: '备份类型', value: previewData.backupType === 1 ? '手动备份' : '自动备份' },
          { label: '状态', value: previewData.status === 1 ? '成功' : '失败' },
          { label: '备注', value: previewData.remark },
        ]" />

        <div class="preview-content">
          <t-alert theme="info" message="预览内容仅显示部分数据，完整数据请下载后查看" />
          <t-textarea
            v-model="previewContent"
            :rows="10"
            readonly
            placeholder="预览内容加载中..."
          />
        </div>
      </template>
    </t-dialog>
    
    <!-- 手动备份对话框 -->
    <t-dialog
      v-model:visible="manualBackupVisible"
      header="手动备份"
      :width="500"
      :footer="false"
      @close="manualBackupVisible = false"
    >
      <t-form :data="manualBackupForm" label-width="120px">
        <t-form-item label="备份描述">
          <t-input v-model="manualBackupForm.description" placeholder="请输入备份描述"/>
        </t-form-item>
        <t-form-item label="备注">
          <t-textarea v-model="manualBackupForm.remark" placeholder="请输入备注信息"/>
        </t-form-item>
      </t-form>
      <template #footer>
        <t-space>
          <t-button theme="default" @click="manualBackupVisible = false">取消</t-button>
          <t-button theme="primary" @click="confirmManualBackup" :loading="backupLoading">确认备份</t-button>
        </t-space>
      </template>
    </t-dialog>
  </div>
</template>
<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { MessagePlugin, DialogPlugin } from 'tdesign-vue-next';
import { Icon } from 'tdesign-icons-vue-next';
import { 
  getBackupList, 
  createBackup, 
  downloadBackup, 
  restoreBackup, 
  deleteBackup,
  getBackupConfig,
  updateBackupConfig,
  BackupItem 
} from '@/api/system/backup';

// 手动备份表单
const manualBackupVisible = ref(false);
const backupLoading = ref(false);
const manualBackupForm = reactive({
  description: '',
  remark: ''
});

// 自动备份设置
interface AutoBackupConfig {
  enabled: boolean;
  frequency: 'daily' | 'weekly' | 'monthly';
  time: string;
  retentionDays: number;
}

// 表格数据
const data = ref<BackupItem[]>([]);
const loading = ref(false);

// 自动备份配置
const autoBackupConfig = reactive<AutoBackupConfig>({
  enabled: true,
  frequency: 'daily',
  time: '00:00',
  retentionDays: 30,
});

// 预览对话框
const previewVisible = ref(false);
const previewData = ref<BackupItem | null>(null);
const previewContent = ref('-- 预览内容示例 --\nSELECT * FROM users;\nINSERT INTO logs (action) VALUES ("backup");');

// 自动备份设置对话框
const showAutoBackupConfig = ref(false);

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  pageSizeOptions: [10, 20, 50],
  showJumper: true,
  showPageSize: true,
  onChange: (pageInfo: any) => {
    pagination.current = pageInfo.current;
    pagination.pageSize = pageInfo.pageSize;
    fetchBackupList();
  },
});

// 获取备份列表
const fetchBackupList = async () => {
  loading.value = true;
  try {
    const response = await getBackupList({
      current: pagination.current,
      pageSize: pagination.pageSize
    });
    
    if (response.code === 200) {
      data.value = response.data.list;
      pagination.total = response.data.total;
      updateStatistics();
    } else {
      MessagePlugin.error('获取备份列表失败: ' + response.msg);
    }
  } catch (error) {
    console.error('获取备份列表失败:', error);
    MessagePlugin.error('获取备份列表失败');
  } finally {
    loading.value = false;
  }
};

// 表格列配置
const columns = [
  { colKey: 'serial-number', title: '序号', width: 80 },
  { colKey: 'backupName', title: '备份文件名' },
  { colKey: 'backupSize', title: '文件大小' },
  { colKey: 'backupType', title: '备份类型' },
  { colKey: 'createTime', title: '备份时间' },
  { colKey: 'createBy', title: '备份人' },
  { colKey: 'status', title: '状态' },
  { colKey: 'description', title: '描述' },
  { colKey: 'operation', title: '操作', width: 200, align: 'center' as 'center' },
];

// 统计信息
const statistics = reactive({
  totalBackups: 0,
  totalSize: '0 MB',
  lastBackupTime: '',
  lastBackupUser: '',
});

// 更新统计信息
const updateStatistics = () => {
  statistics.totalBackups = pagination.total;
  
  // 计算总大小
  let totalSize = 0;
  data.value.forEach(item => {
    // 假设格式是 "123.45 MB" 或 "1.23 GB"
    const sizeStr = item.backupSize;
    const sizeNum = parseFloat(sizeStr);
    const sizeUnit = sizeStr.split(' ')[1];
    
    if (sizeUnit === 'MB') {
      totalSize += sizeNum;
    } else if (sizeUnit === 'GB') {
      totalSize += sizeNum * 1024;
    }
  });
  
  if (totalSize > 1024) {
    statistics.totalSize = (totalSize / 1024).toFixed(2) + ' GB';
  } else {
    statistics.totalSize = totalSize.toFixed(2) + ' MB';
  }
  
  if (data.value.length > 0) {
    statistics.lastBackupTime = data.value[0].createTime;
    statistics.lastBackupUser = data.value[0].createBy;
  }
};

// 下载备份文件
const handleDownload = async (row: BackupItem) => {
  try {
    const response = await downloadBackup(row.backupId);
    
    // 创建blob链接并下载
    const blob = new Blob([response.data]);
    const link = document.createElement('a');
    link.href = window.URL.createObjectURL(blob);
    link.download = row.backupName;
    link.click();
    window.URL.revokeObjectURL(link.href);
    
    MessagePlugin.success('下载成功');
  } catch (error) {
    console.error('下载失败:', error);
    MessagePlugin.error('下载失败');
  }
};

// 打开手动备份对话框
const handleManualBackup = () => {
  manualBackupVisible.value = true;
  manualBackupForm.description = '';
  manualBackupForm.remark = '';
};

// 确认手动备份
const confirmManualBackup = async () => {
  backupLoading.value = true;
  try {
    const response = await createBackup({
      description: manualBackupForm.description,
      remark: manualBackupForm.remark
    });
    
    if (response.code === 200) {
      MessagePlugin.success('备份创建成功');
      manualBackupVisible.value = false;
      fetchBackupList();
    } else {
      MessagePlugin.error('备份创建失败: ' + response.msg);
    }
  } catch (error) {
    console.error('备份创建失败:', error);
    MessagePlugin.error('备份创建失败');
  } finally {
    backupLoading.value = false;
  }
};

// 删除备份文件
const handleDelete = (row: BackupItem) => {
  DialogPlugin.confirm({
    header: '删除确认',
    body: `确定要删除备份文件 ${row.backupName} 吗？`,
    confirmBtn: '确认删除',
    cancelBtn: '取消',
    onConfirm: async () => {
      try {
        const response = await deleteBackup(row.backupId);
        
        if (response.code === 200) {
          MessagePlugin.success('删除成功');
          fetchBackupList();
        } else {
          MessagePlugin.error('删除失败: ' + response.msg);
        }
      } catch (error) {
        console.error('删除失败:', error);
        MessagePlugin.error('删除失败');
      }
    }
  });
};

// 恢复备份文件
const handleRestore = (row: BackupItem) => {
  DialogPlugin.confirm({
    header: '恢复确认',
    body: '恢复备份将覆盖当前数据，确定要继续吗？',
    confirmBtn: '确认恢复',
    cancelBtn: '取消',
    onConfirm: async () => {
      try {
        const response = await restoreBackup(row.backupId, {
          override: true
        });
        
        if (response.code === 200) {
          MessagePlugin.success('数据恢复成功');
        } else {
          MessagePlugin.error('数据恢复失败: ' + response.msg);
        }
      } catch (error) {
        console.error('数据恢复失败:', error);
        MessagePlugin.error('数据恢复失败');
      }
    }
  });
};

// 预览备份文件
const handlePreview = (row: BackupItem) => {
  previewData.value = row;
  previewVisible.value = true;
};

// 获取自动备份配置
const fetchBackupConfig = async () => {
  try {
    const response = await getBackupConfig();
    
    if (response.code === 200) {
      autoBackupConfig.enabled = response.data.enabled;
      autoBackupConfig.frequency = response.data.frequency;
      autoBackupConfig.time = response.data.time;
      autoBackupConfig.retentionDays = response.data.retentionDays;
    }
  } catch (error) {
    console.error('获取备份配置失败:', error);
  }
};

// 保存自动备份设置
const handleSaveAutoBackupConfig = async () => {
  try {
    const response = await updateBackupConfig({
      enabled: autoBackupConfig.enabled,
      frequency: autoBackupConfig.frequency,
      time: autoBackupConfig.time,
      retentionDays: autoBackupConfig.retentionDays
    });
    
    if (response.code === 200) {
      MessagePlugin.success('自动备份设置已保存');
      showAutoBackupConfig.value = false;
    } else {
      MessagePlugin.error('保存设置失败: ' + response.msg);
    }
  } catch (error) {
    console.error('保存设置失败:', error);
    MessagePlugin.error('保存设置失败');
  }
};

// 初始化
onMounted(() => {
  fetchBackupList();
  fetchBackupConfig();
});
</script>

<style scoped lang="less">
.backup-management-container {
  padding: 20px;
  background-color: var(--td-bg-color-container);
  border-radius: var(--td-radius-medium);
}

.statistics-cards {
  margin-bottom: 24px;

  :deep(.t-card) {
    height: 100%;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-4px);
    }

    .t-card__title {
      color: var(--td-text-color-secondary);
    }
  }
}

.statistics-value {
  font-size: 24px;
  font-weight: bold;
  color: var(--td-text-color-primary);
  margin-top: 8px;
}

:deep(.t-button) {
  min-width: unset;
  margin: 0;
  padding: 4px;
  transition: all 0.2s ease;

  &:hover {
    background-color: rgba(0, 0, 0, 0.05);
    transform: translateY(-2px);
  }
}

.preview-content {
  margin-top: 16px;
}
</style>

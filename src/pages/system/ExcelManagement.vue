<template>
  <div class="course-scheduler">
    <t-card :bordered="false" class="scheduler-card">
      <template #header>
        <div class="header-container">
          <h1 class="title">{{ plan?.planName }} - 课程管理</h1>
          <div class="action-bar">
            <t-space size="medium">
              <t-button @click="openDialog('add')" theme="primary" :disabled="!plan?.id">
                <template #icon><t-icon name="add" /></template>新增课程
              </t-button>
              <t-button @click="exportTable" variant="outline" :disabled="!plan?.id">
                <template #icon><t-icon name="download" /></template>导出Excel
              </t-button>
              <t-upload
                accept=".xlsx,.xls"
                :before-upload="beforeImport"
                :showUploadProgress="false"
                :disabled="!plan?.id"
              >
                <t-button variant="outline" :disabled="!plan?.id">
                  <template #icon><t-icon name="upload" /></template>导入Excel
                </t-button>
              </t-upload>
              <t-space class="search-group">
                <t-input
                  v-model="searchValue.courseName"
                  placeholder="按课程名称搜索"
                  clearable
                  class="search-input-item"
                  @enter="fetchCourses"
                />
                <t-input
                  v-model="searchValue.courseCode"
                  placeholder="按课程编码搜索"
                  clearable
                  class="search-input-item"
                  @enter="fetchCourses"
                />
                <t-button @click="fetchCourses" variant="outline">搜索</t-button>
              </t-space>
            </t-space>
          </div>
        </div>
      </template>

      <t-table
        :data="courses"
        :columns="columns"
        row-key="courseId"
        :hover="true"
        :loading="loading"
        :pagination="pagination"
        @page-change="handlePageChange"
        class="course-table"
        size="small"
        :header-affixed-top="true"
        :show-pagination="true"
      >
        <template #courseCore="{ row }">
          <t-tag :theme="row.courseCore ? 'success' : 'default'" shape="round" size="small">
            {{ row.courseCore ? '是' : '否' }}
          </t-tag>
        </template>
        <template #courseExam="{ row }">
          <t-tag :theme="row.courseExam ? 'warning' : 'default'" shape="round" size="small">
            {{ row.courseExam ? '是' : '否' }}
          </t-tag>
        </template>
        <template #courseType1Display="{ row }">
          {{ enumData?.map?.courseCategory?.[row.courseType1] }}
        </template>
         <template #courseNatureDisplay="{ row }">
          {{ enumData?.map?.courseNature?.[row.courseNature] }}
        </template>
        <template #courseSemester="{ row }">
          {{ enumData?.map?.semesterPeriod?.[String(row.courseSemester)] || row.courseSemester }}
        </template>
        <template #courseLeaderDisplay="{ row }">
          {{ teacherMap[row.courseLeader] || '未设置' }}
        </template>
        <template #operation="{ row }">
          <t-space :size="4">
            <t-button variant="text" @click="openDialog('edit', row)" theme="primary" size="small">
              <template #icon><t-icon name="edit" /></template>编辑
            </t-button>
            <t-popconfirm content="确认删除该课程吗？此操作不可逆！" @confirm="confirmDeleteCourse(row)">
              <t-button variant="text" theme="danger" size="small">
                <template #icon><t-icon name="delete" /></template>删除
              </t-button>
            </t-popconfirm>
          </t-space>
        </template>
      </t-table>
    </t-card>

    <t-dialog
      v-model:visible="showDialog"
      :header="dialogTitle"
      width="calc(100vw - 500px)"
      top="5vh"
      :on-confirm="handleSubmit"
      destroy-on-close
      :confirm-btn="{ content: '提交', loading: submitLoading }"
      :cancel-btn="{ content: '取消' }"
      :closeOnEscKeydown="false"
    >
      <div style="width: 98%; max-height: 75vh; overflow-y: auto;">
        <t-form ref="formRef" :data="currentCourse" :rules="rules" label-align="top">
          <t-divider align="left">基本信息</t-divider>
          <t-row :gutter="[16, 16]">
            <t-col :span="4">
              <t-form-item label="课程编码" name="courseCode">
                <t-input v-model="currentCourse.courseCode" placeholder="请输入课程编码" />
              </t-form-item>
            </t-col>
            <t-col :span="4">
              <t-form-item label="课程名称" name="courseName">
                <t-input v-model="currentCourse.courseName" placeholder="请输入课程名称" />
              </t-form-item>
            </t-col>
            <t-col :span="4">
              <t-form-item label="学分" name="courseCredit">
                <t-input-number v-model="currentCourse.courseCredit" :min="0" :step="0.5" placeholder="请输入学分" style="width:100%"/>
              </t-form-item>
            </t-col>
              <t-col :span="4">
              <t-form-item label="上课学期" name="courseSemester">
                <t-select
                  v-model="currentCourse.courseSemester"
                  :options="enumData?.list?.semesterPeriod"
                  placeholder="请选择上课学期"
                  clearable
                  style="width:100%"
                />
              </t-form-item>
            </t-col>
            <t-col :span="4">
              <t-form-item label="课程类别" name="courseType1">
                <t-select
                  v-model="currentCourse.courseType1"
                  :options="enumData?.list?.courseCategory"
                  placeholder="请选择课程类别"
                  clearable
                  style="width:100%"
                />
              </t-form-item>
            </t-col>
            <t-col :span="4">
              <t-form-item label="课程性质" name="courseNature">
                  <t-select
                  v-model="currentCourse.courseNature"
                  :options="enumData?.list?.courseNature"
                  placeholder="请选择课程性质"
                  clearable
                  style="width:100%"
                />
              </t-form-item>
            </t-col>

            <t-col :span="4">
                <t-form-item label="是否核心" name="courseCore">
                  <t-switch v-model="currentCourse.courseCore" :label="['是', '否']" />
                </t-form-item>
            </t-col>
            <t-col :span="4">
                <t-form-item label="是否考试" name="courseExam">
                  <t-switch v-model="currentCourse.courseExam" :label="['是', '否']" />
                </t-form-item>
            </t-col>
              <t-col :span="4">
              <t-form-item label="课程版本" name="courseVersion">
                <t-input-number v-model="currentCourse.courseVersion" placeholder="年份" :min="2000" style="width:100%"/>
              </t-form-item>
            </t-col>
            <t-col :span="4">
              <t-form-item label="关联专业ID" name="majorId">
                <t-input-number v-model="currentCourse.majorId" placeholder="请输入关联专业ID" :min="0" style="width:100%"/>
              </t-form-item>
            </t-col>
            <t-col :span="4">
              <t-form-item label="课程负责人" name="courseLeader">
                <t-select
                  v-model="currentCourse.courseLeader"
                  :options="teacherList"
                  placeholder="请选择课程负责人"
                  clearable
                  style="width:100%"
                />
              </t-form-item>
            </t-col>
          </t-row>

          <t-divider align="left">学时信息</t-divider>
          <t-row :gutter="[16,16]">
            <t-col :span="4">
              <t-form-item label="总学时" name="courseHoursTotal">
                <t-input-number v-model="currentCourse.courseHoursTotal" :min="0" placeholder="总学时" style="width:100%"/>
              </t-form-item>
            </t-col>
            <t-col :span="4">
              <t-form-item label="理教学时" name="courseHoursTheory">
                <t-input-number v-model="currentCourse.courseHoursTheory" :min="0" placeholder="理教学时" style="width:100%"/>
              </t-form-item>
            </t-col>
            <t-col :span="4">
              <t-form-item label="实验学时" name="courseHoursExperiment">
                <t-input-number v-model="currentCourse.courseHoursExperiment" :min="0" placeholder="实验学时" style="width:100%"/>
              </t-form-item>
            </t-col>
            <t-col :span="4">
              <t-form-item label="课外学时" name="courseHoursExtracurricular">
                <t-input-number v-model="currentCourse.courseHoursExtracurricular" :min="0" placeholder="课外学时" style="width:100%"/>
              </t-form-item>
            </t-col>
            <t-col :span="4">
              <t-form-item label="其他学时" name="courseHoursOther">
                <t-input-number v-model="currentCourse.courseHoursOther" :min="0" placeholder="其他学时" style="width:100%"/>
              </t-form-item>
            </t-col>
          </t-row>
          <t-row :gutter="[16,16]">
            <t-col :span="4">
              <t-form-item label="专业认证课程类型" name="courseType2">
                <t-input-number v-model="currentCourse.courseType2" placeholder="专业认证课程类型" :min="0" style="width:100%"/>
              </t-form-item>
            </t-col>
            <t-col :span="4">
              <t-form-item label="国标课程类别" name="courseType3">
                <t-input-number v-model="currentCourse.courseType3" placeholder="国标课程类别" :min="0" style="width:100%"/>
              </t-form-item>
            </t-col>
          </t-row>
        </t-form>
      </div>
    </t-dialog>
  </div>
</template>

<script lang="tsx" setup>
import { ref, reactive, computed, onMounted, watch, h } from 'vue';
import {
  MessagePlugin,
  type PageInfo,
  type FormInstanceFunctions,
  type FormRule,
  type UploadProps,
  type DialogInstance,
  Tag as TTag,
} from 'tdesign-vue-next';
import * as XLSX from 'xlsx';
import { useRoute } from 'vue-router';
import { getPlan } from '@/api/training/plan';
import { getCourseList, addCourse, updateCourse, deleteCourse } from '@/api/training/course';
import { getEnum } from '@/api/system/enum';


const route = useRoute();
const planIdFromRoute = ref<number | null>(null);
const plan = ref<any>(null);

const courses = ref<any[]>([]);
const loading = ref(false);
const submitLoading = ref(false);
const showDialog = ref(false);

const emptyCourse = {
  courseCode: '',
  courseName: '',
  courseCredit: 0,
  courseCore: false,
  courseExam: false,
  courseHoursTotal: 0,
  courseHoursTheory: 0,
  courseHoursExperiment: 0,
  courseHoursOther: 0,
  courseHoursExtracurricular: 0,
  courseSemester: null as number | null,
  courseType1: null as number | null,
  courseType2: null as number | null,
  courseType3: null as number | null,
  courseNature: null as number | null,
  courseVersion: new Date().getFullYear(),
  planId: null as number | null,
  majorId: null as number | null,
  courseLeader: null as number | null,
  status: 0,
};
const currentCourse = ref({ ...emptyCourse });
const dialogType = ref<'add' | 'edit'>('add');
const formRef = ref<FormInstanceFunctions | null>(null);
const searchValue = reactive({ courseName: '', courseCode: '' });

const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  pageSizeOptions: [10, 20, 50, 100],
});

const columns: any[] = [
  { colKey: 'courseId', title: 'ID', width: 70, align: 'center' as const },
  { colKey: 'courseCode', title: '课程编码', width: 120, ellipsis: true },
  { colKey: 'courseName', title: '课程名称', width: 200, ellipsis: true },
  { colKey: 'courseCredit', title: '学分', width: 80, align: 'center' as const },
  { colKey: 'courseCore', title: '核心', width: 80, align: 'center' as const, cell: 'courseCore' },
  { colKey: 'courseExam', title: '考试', width: 80, align: 'center' as const, cell: 'courseExam' },
  { colKey: 'courseHoursTotal', title: '总学时', width: 90, align: 'center' as const },
  { colKey: 'courseSemester', title: '学期', width: 90, align: 'center' as const, cell: 'courseSemester' },
  { colKey: 'courseType1', title: '课程类别', width: 150, cell: 'courseType1Display', ellipsis: true },
  { colKey: 'courseNature', title: '课程性质', width: 120, cell: 'courseNatureDisplay', ellipsis: true },
  { colKey: 'courseLeader', title: '课程负责人', width: 120, cell: 'courseLeaderDisplay', ellipsis: true },
  {
    colKey: 'status',
    title: '状态',
    width: 80,
    align: 'center' as const,
    cell: (cellH: typeof h, { row }: { row: any }) => {
      const statusMap: Record<string | number, { text: string; theme: 'success' | 'danger' | 'default' | 'warning' | 'primary' }> = {
        '0': { text: '正常', theme: 'success' },
        '1': { text: '停用', theme: 'warning' }, // Assuming '1' means '停用' from other enum contexts
        '-1': { text: '已删除', theme: 'danger' },
      };
      // Use enumData for status if available and matches expected structure
      const statusText = enumData.value?.map?.courseStatus?.[String(row.status)] || statusMap[String(row.status)]?.text || '未知';
      const theme = row.status === 0 ? 'success' : (row.status === -1 ? 'danger' : 'default');
      return cellH(TTag, { theme: theme, size: 'small', shape: 'round' }, () => statusText);
    },
  },
  { colKey: 'createTime', title: '创建时间', width: 160 },
  { colKey: 'operation', title: '操作', width: 150, fixed: 'right' as const, align: 'center' as const }
];

const rules: Record<string, FormRule[]> = {
  courseCode: [{ required: true, message: '课程编码必填', trigger: 'blur' }],
  courseName: [{ required: true, message: '课程名称必填', trigger: 'blur' }],
  courseCredit: [{ required: true, message: '学分必填且为数字', trigger: 'blur' }],
  courseSemester: [{ required: true, message: '上课学期必选', trigger: 'change' }],
  courseType1: [{ required: true, message: '课程类别必选', trigger: 'change' }],
  courseNature: [{ required: true, message: '课程性质必选', trigger: 'change' }],
  courseHoursTotal: [{ required: true, message: '总学时必填', trigger: 'blur' }],
  planId: [{ required: true, message: '培养方案ID丢失', trigger: 'blur'}],
  courseLeader: [{ required: true, message: '课程负责人必选', trigger: 'change' }],
};

const dialogTitle = computed(() => (dialogType.value === 'add' ? '新增课程' : '编辑课程'));

const enumData = ref<any>(null);
const teacherList = ref<any[]>([]);
const teacherMap = ref<any>({});

onMounted(async () => {
  const idFromQuery = route.query.id;
  if (!idFromQuery) {
    MessagePlugin.warning('未提供培养方案ID，无法加载课程数据。');
    return;
  }

  planIdFromRoute.value = Number(idFromQuery);
  const planRes = await getPlan(planIdFromRoute.value);
  plan.value = planRes.data;
  fetchCourses();

  // const teacherRes = await getTeacherByAcademyId(plan.value?.academyId);
  const teacherRes = {
    data: [
      {
        value: 1,
        label: '张三'
      }
    ]
  };
  teacherList.value = teacherRes.data;
  teacherMap.value = teacherRes.data.reduce((acc: any, curr: any) => {
    acc[curr.value] = curr.label;
    return acc;
  }, {});

  const res = await getEnum();
  enumData.value = res.data;

});


const fetchCourses = async () => {
  if (!planIdFromRoute.value) {
    courses.value = [];
    pagination.total = 0;
    return;
  }
  loading.value = true;
  try {
    const params = {
      current: pagination.current,
      size: pagination.pageSize,
      planId: planIdFromRoute.value,
      courseName: searchValue.courseName || undefined,
      courseCode: searchValue.courseCode || undefined,
    };
    const response = await getCourseList(params);
    courses.value = response.data.records || [];
    pagination.total = response.data.total || 0;
  } catch (error: any) {
    console.error('获取课程列表失败:', error);
    MessagePlugin.error(String(error?.message || '获取课程列表失败'));
    courses.value = [];
    pagination.total = 0;
  } finally {
    loading.value = false;
  }
};

const openDialog = (type: 'add' | 'edit', row?: any) => {
  dialogType.value = type;
  if (type === 'add') {
    currentCourse.value = {
      ...emptyCourse,
      planId: planIdFromRoute.value,
      majorId: plan.value?.majorId || null,
      courseVersion: plan.value?.planVersion || new Date().getFullYear(),
    };
  } else {
    currentCourse.value = { ...row, planId: planIdFromRoute.value };
    console.log(currentCourse.value);
  }
  showDialog.value = true;
};

const handleSubmit = async (context?: { e?: MouseEvent | KeyboardEvent} | DialogInstance) => {
  if (context && typeof context === 'object' && 'trigger' in context && context.trigger !== 'confirm') {
    return;
  }

  const form = formRef.value;
  if (!form) return;

  const valid = await form.validate();
  if (valid === true) {
    submitLoading.value = true;
    try {
      const dataToSubmit = { ...currentCourse.value };
      if (!dataToSubmit.planId && planIdFromRoute.value) {
        dataToSubmit.planId = planIdFromRoute.value;
      }

      if (!dataToSubmit.planId) {
          MessagePlugin.error('培养方案ID缺失，无法提交！');
          submitLoading.value = false;
          return;
      }
      // Ensure numeric types from select are numbers if DTO expects numbers
      dataToSubmit.courseSemester = dataToSubmit.courseSemester ? Number(dataToSubmit.courseSemester) : null;
      dataToSubmit.courseType1 = dataToSubmit.courseType1 ? Number(dataToSubmit.courseType1) : null;
      dataToSubmit.courseNature = dataToSubmit.courseNature ? Number(dataToSubmit.courseNature) : null;
      dataToSubmit.courseLeader = dataToSubmit.courseLeader ? Number(dataToSubmit.courseLeader) : null;

      if (dialogType.value === 'add') {
        await addCourse(dataToSubmit);
        MessagePlugin.success('新增课程成功');
      } else {
        await updateCourse(dataToSubmit);
        MessagePlugin.success('更新课程成功');
      }
      showDialog.value = false;
      fetchCourses();
    } catch (error: any) {
      MessagePlugin.error(String(error?.message || '操作失败'));
    } finally {
      submitLoading.value = false;
    }
  } else {
     MessagePlugin.warning('请检查表单必填项或错误信息');
  }
};

const confirmDeleteCourse = async (row: any) => {
  if (!row.courseId) {
    MessagePlugin.error('课程ID无效');
    return;
  }
  loading.value = true;
  try {
    await deleteCourse(String(row.courseId));
    MessagePlugin.success('删除课程成功');
    fetchCourses();
  } catch (error: any) {
    MessagePlugin.error(String(error?.message || '删除失败'));
  } finally {
    loading.value = false;
  }
};

const exportTable = () => {
  if (courses.value.length === 0) {
    MessagePlugin.info('没有数据可导出');
    return;
  }
  try {
    const dataToExport = courses.value.map((c: any) => ({
      '课程编码': c.courseCode,
      '课程名称': c.courseName,
      '学分': c.courseCredit,
      '核心课程': c.courseCore ? '是' : '否',
      '考试课程': c.courseExam ? '是' : '否',
      '总学时': c.courseHoursTotal,
      '理教学时': c.courseHoursTheory,
      '实验学时': c.courseHoursExperiment,
      '课外学时': c.courseHoursExtracurricular,
      '其他学时': c.courseHoursOther,
      '上课学期': enumData.value?.map?.semesterPeriod?.[String(c.courseSemester)] || c.courseSemester,
      '课程类别': enumData.value?.map?.courseCategory?.[String(c.courseType1)] || c.courseType1,
      '课程性质': enumData.value?.map?.courseNature?.[String(c.courseNature)] || c.courseNature,
      '课程负责人': teacherMap.value[c.courseLeader] || '未设置',
      '课程版本': c.courseVersion,
      '所属培养方案ID': c.planId,
      '所属专业ID': c.majorId,
      '状态': enumData.value?.map?.courseStatus?.[String(c.status)] || (c.status === 0 ? '正常' : (c.status === -1 ? '已删除' : '未知')),
    }));
    const worksheet = XLSX.utils.json_to_sheet(dataToExport);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, `课程表-${plan.value?.planName || planIdFromRoute.value}`);
    XLSX.writeFile(workbook, `课程表_${plan.value?.planName || planIdFromRoute.value}_${new Date().toISOString().slice(0,10)}.xlsx`);
    MessagePlugin.success('导出成功');
  } catch (error: any) {
    console.error('导出失败:', error);
    MessagePlugin.error(String(error?.message || '导出失败'));
  }
};

const beforeImport: UploadProps['beforeUpload'] = (file) => {
  const reader = new FileReader();
  reader.onload = async (e) => {
    try {
      const fileData = new Uint8Array(e.target?.result as ArrayBuffer);
      const workbook = XLSX.read(fileData, { type: 'array' });
      const worksheet = workbook.Sheets[workbook.SheetNames[0]];
      const json: any[] = XLSX.utils.sheet_to_json(worksheet);

      if (!planIdFromRoute.value) {
        MessagePlugin.error('培养方案ID未知，无法导入课程。');
        return;
      }

      const importedCourses = json.map((item: any) => {
        const semesterValueFromLabel = enumData.value?.map?.semesterPeriod?.[item['上课学期']];
        const type1ValueFromLabel = enumData.value?.map?.courseCategory?.[item['课程类别']];
        const natureValueFromLabel = enumData.value?.map?.courseNature?.[item['课程性质']];

        // Find teacher ID by name
        const teacherName = item['课程负责人'];
        const teacher = teacherList.value.find(t => t.teacherName === teacherName);
        const teacherId = teacher ? teacher.teacherId : null;

        return {
          courseCode: String(item['课程编码'] || ''),
          courseName: String(item['课程名称'] || ''),
          courseCredit: Number(item['学分'] || 0),
          courseCore: String(item['核心课程']).toLowerCase() === '是',
          courseExam: String(item['考试课程']).toLowerCase() === '是',
          courseHoursTotal: Number(item['总学时'] || 0),
          courseHoursTheory: Number(item['理教学时'] || 0),
          courseHoursExperiment: Number(item['实验学时'] || 0),
          courseHoursExtracurricular: Number(item['课外学时'] || 0),
          courseHoursOther: Number(item['其他学时'] || 0),
          courseSemester: semesterValueFromLabel ? Number(semesterValueFromLabel) : (Number(item['上课学期']) || null),
          courseType1: type1ValueFromLabel ? Number(type1ValueFromLabel) : (Number(item['课程类别']) || null),
          courseNature: natureValueFromLabel ? Number(natureValueFromLabel) : (Number(item['课程性质']) || null),
          courseType2: Number(item['专业认证课程类型']) || null,
          courseType3: Number(item['国标课程类别']) || null,
          courseTarget: String(item['课程目标(JSON)'] || ''),
          assessmentMethod: String(item['考核方式(JSON)'] || ''),
          assessmentWeight: String(item['考核权重(JSON)'] || ''),
          assessmentProportion: String(item['考核占比(JSON)'] || ''),
          courseVersion: Number(item['课程版本']) || plan.value?.planVersion || new Date().getFullYear(),
          planId: planIdFromRoute.value,
          majorId: Number(item['所属专业ID']) || null,
          courseLeader: teacherId,
          status: 0,
        };
      });

      if (importedCourses.length === 0) {
        MessagePlugin.info('没有可导入的有效课程数据。');
        return;
      }

      loading.value = true;
      let successCount = 0;
      let errorCount = 0;
      const errorMessages: string[] = [];

      for (const course of importedCourses) {
        if (!course.courseCode || !course.courseName) {
            errorCount++;
            errorMessages.push(`导入错误: 课程编码或名称为空. 数据: ${JSON.stringify(course)}`);
            continue;
        }
        const courseToSubmit = {
          ...course,
          courseSemester: course.courseSemester ? Number(course.courseSemester) : null,
          courseType1: course.courseType1 ? Number(course.courseType1) : null,
          courseNature: course.courseNature ? Number(course.courseNature) : null,
          courseLeader: course.courseLeader ? Number(course.courseLeader) : null,
        };

        try {
          await addCourse(courseToSubmit as any);
          successCount++;
        } catch (errImport: any) {
          errorMessages.push(`课程 ${course.courseCode || '未知编码'} 导入失败: ${String(errImport?.message || '未知错误')}`);
          errorCount++;
        }
      }
      loading.value = false;

      if (errorCount > 0) {
        MessagePlugin.warning(`成功导入 ${successCount} 条课程，${errorCount} 条导入失败。详情请见控制台。`);
        console.warn("导入失败详情:", errorMessages);
      } else {
        MessagePlugin.success(`成功导入 ${successCount} 条课程数据`);
      }
      fetchCourses();
    } catch (error: any) {
      console.error('文件解析或导入过程出错:', error);
      MessagePlugin.error(String(error?.message || '文件解析失败或导入过程中发生错误。请检查文件格式或控制台输出。'));
      loading.value = false;
    }
  };
  reader.readAsArrayBuffer(file.raw as File);
  return false;
};

const handlePageChange = (pageInfo: PageInfo) => {
  pagination.current = pageInfo.current;
  pagination.pageSize = pageInfo.pageSize;
  fetchCourses();
};

</script>

<style scoped lang="less">
.course-scheduler {
  padding: 16px;
  height: calc(100vh - 32px);
  display: flex;
  flex-direction: column;

  .scheduler-card {
    flex: 1;
    min-height: 0;
    display: flex;
    flex-direction: column;

    :deep(.t-card__header){
      padding-top: 8px;
      padding-bottom: 12px;
    }
    :deep(.t-card__body) {
      flex: 1;
      display: flex;
      flex-direction: column;
      overflow: hidden;
      padding-top: 0px;
    }
  }
}

.header-container {
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 16px;

  .title {
    margin: 0;
    font-size: 20px;
    font-weight: 600;
    color: var(--td-text-color-primary);
  }
}

.action-bar {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
  margin-top: 12px;

  .search-group {
    display: flex;
    gap: 8px;
  }
  .search-input-item {
    width: 160px;
  }
}



@media (max-width: 768px) {
  .header-container {
    flex-direction: column;
    align-items: flex-start;
  }
  .action-bar {
    width: 100%;
    flex-direction: column;
    align-items: stretch;
    .search-group {
      flex-direction: column;
      width: 100%;
      .search-input-item {
        width: 100%;
      }
      .t-button {
        width: 100%;
      }
    }
     .t-button {
      width: 100%;
      margin-bottom: 8px;
      &:last-child {
        margin-bottom: 0;
      }
    }
    :deep(.t-upload) {
      width: 100%;
      .t-button {
        width: 100%;
      }
    }
  }
   .t-dialog {
    width: 95vw !important;
    max-width: 95vw !important;
    top: 2.5vh !important;
    :deep(.t-dialog__body > div) { // Target the inner div for scrolling
      max-height: 85vh !important;
      overflow-y: auto !important;
    }
  }
}
</style>

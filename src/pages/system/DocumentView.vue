<template>
  <div class="document-view-container">
    <t-card title="教学组织文件查看">
      <!-- 搜索区域 -->
      <t-form ref="form" :data="formData" :colon="true" labelWidth="80px" layout="inline" @reset="onReset" @submit="onSubmit">
        <t-form-item label="文件名称" name="title">
          <t-input v-model="formData.title" placeholder="请输入文件名称" clearable />
        </t-form-item>
        <t-form-item label="文件类别" name="category">
          <t-select v-model="formData.category" placeholder="请选择文件类别" clearable>
            <t-option v-for="item in categoryOptions" :key="item.id" :value="item.name" :label="item.name" />
          </t-select>
        </t-form-item>
        <t-form-item label="文件类型" name="fileType">
          <t-select v-model="formData.fileType" placeholder="请选择文件类型" clearable>
            <t-option value="pdf" label="PDF" />
            <t-option value="doc" label="DOC" />
            <t-option value="docx" label="DOCX" />
            <t-option value="xls" label="XLS" />
            <t-option value="xlsx" label="XLSX" />
            <t-option value="ppt" label="PPT" />
            <t-option value="pptx" label="PPTX" />
          </t-select>
        </t-form-item>
        <t-form-item>
          <t-space>
            <t-button theme="primary" type="submit">查询</t-button>
            <t-button theme="default" variant="base" type="reset">重置</t-button>
          </t-space>
        </t-form-item>
      </t-form>

      <!-- 文件表格 -->
      <t-table
        :data="documentList"
        :columns="columns"
        :row-key="rowKey"
        :loading="loading"
        :pagination="pagination"
        stripe
        hover
        @page-change="onPageChange"
      >
        <template #fileType="{ row }">
          <t-tag v-if="row.fileType === 'pdf'" theme="success">PDF</t-tag>
          <t-tag v-else-if="['doc', 'docx'].includes(row.fileType)" theme="primary">Word</t-tag>
          <t-tag v-else-if="['xls', 'xlsx'].includes(row.fileType)" theme="warning">Excel</t-tag>
          <t-tag v-else-if="['ppt', 'pptx'].includes(row.fileType)" theme="danger">PPT</t-tag>
          <t-tag v-else theme="default">{{ row.fileType }}</t-tag>
        </template>
        <template #fileSize="{ row }">
          <span>{{ formatFileSize(row.fileSize) }}</span>
        </template>
        <template #createTime="{ row }">
          {{ formatDate(row.createTime) }}
        </template>
        <template #operation="{ row }">
          <t-space>
            <t-button size="small" variant="outline" theme="primary" @click="handleView(row)">
              <template #icon><t-icon name="browse" /></template>
              预览
            </t-button>
            <t-button size="small" variant="outline" theme="primary" @click="handleDownload(row)">
              <template #icon><t-icon name="download" /></template>
              下载
            </t-button>
          </t-space>
        </template>
      </t-table>

      <!-- 文件预览弹窗 -->
      <t-dialog
        v-model:visible="previewDialogVisible"
        header="文件预览"
        width="80%"
        height="80%"
        :footer="false"
        destroy-on-close
      >
        <div class="preview-container">
          <template v-if="selectedFile">
            <div class="preview-header">
              <h3>{{ selectedFile.title }}</h3>
              <div class="preview-info">
                <span>更新时间: {{ formatDate(selectedFile.updateTime) }}</span>
                <span>文件大小: {{ formatFileSize(selectedFile.fileSize) }}</span>
              </div>
              <div class="preview-description">{{ selectedFile.description }}</div>
            </div>
            
            <!-- PDF 预览 -->
            <div v-if="selectedFile.fileType === 'pdf'" class="pdf-preview">
              <iframe :src="previewUrl" width="100%" height="600" frameborder="0"></iframe>
            </div>
            
            <!-- 其他文件类型预览 -->
            <div v-else class="other-preview">
              <!-- <t-result
                status="info"
                title="暂不支持该类型文件预览"
                description="请下载查看或转换为 PDF 格式后上传"
              >
                <template #actions>
                  <t-button theme="primary" @click="handleDownload(selectedFile)">下载文件</t-button>
                </template>
              </t-result> -->
            </div>
          </template>
        </div>
      </t-dialog>
    </t-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { MessagePlugin } from 'tdesign-vue-next';
import dayjs from 'dayjs';
import { getDocumentList, getDocumentCategories, type DocumentItem, type DocumentCategory, type DocumentParams } from '@/api/system/document';

// 表格列定义
const columns = [
  { colKey: 'title', title: '文件名称', width: 200 },
  { colKey: 'category', title: '文件类别', width: 120 },
  { colKey: 'fileType', title: '文件类型', width: 100 },
  { colKey: 'fileSize', title: '文件大小', width: 100 },
  { colKey: 'createTime', title: '创建时间', width: 160 },
  { colKey: 'operation', title: '操作', width: 140, fixed: 'right' as const },
];

// 数据定义
const documentList = ref<DocumentItem[]>([]);
const categoryOptions = ref<DocumentCategory[]>([]);
const loading = ref(false);
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showJumper: true,
  showPageSize: true,
  pageSizeOptions: [5, 10, 15, 20],
});

// 搜索表单
const formData = reactive<Partial<DocumentParams>>({
  title: '',
  category: '',
  fileType: '',
});

// 表格行键
const rowKey = (row: DocumentItem) => row.id;

// 文件预览相关
const previewDialogVisible = ref(false);
const selectedFile = ref<DocumentItem | null>(null);
const previewUrl = ref('');

// 获取文件列表
const fetchDocumentList = async () => {
  loading.value = true;
  try {
    const params: DocumentParams = {
      page: pagination.current,
      pageSize: pagination.pageSize,
      ...formData,
    };
    // 删除status参数，显示所有文件而不仅是已发布的
    delete params.status;
    
    const { list, total } = await getDocumentList(params);
    
    // 直接显示所有文件，不再过滤
    documentList.value = list;
    pagination.total = total;
  } catch (error) {
    console.error('获取文件列表失败:', error);
    MessagePlugin.error('获取文件列表失败');
  } finally {
    loading.value = false;
  }
};

// 获取文件类别
const fetchDocumentCategories = async () => {
  try {
    categoryOptions.value = await getDocumentCategories();
  } catch (error) {
    console.error('获取文件类别失败:', error);
    MessagePlugin.error('获取文件类别失败');
  }
};

// 初始化
onMounted(async () => {
  await fetchDocumentCategories();
  await fetchDocumentList();
});

// 分页变化
const onPageChange = (pageInfo: { current: number; pageSize: number }) => {
  pagination.current = pageInfo.current;
  pagination.pageSize = pageInfo.pageSize;
  fetchDocumentList();
};

// 搜索表单提交
const onSubmit = () => {
  pagination.current = 1;
  fetchDocumentList();
};

// 搜索表单重置
const onReset = () => {
  Object.assign(formData, {
    title: '',
    category: '',
    fileType: '',
  });
  pagination.current = 1;
  fetchDocumentList();
};

// 格式化日期
const formatDate = (dateStr: string) => {
  return dayjs(dateStr).format('YYYY-MM-DD HH:mm');
};

// 格式化文件大小
const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// 预览文件
const handleView = (row: DocumentItem) => {
  selectedFile.value = row;
  // 模拟预览URL，实际应该是后端提供预览链接
  previewUrl.value = row.viewUrl;
  previewDialogVisible.value = true;
};

// 下载文件
const handleDownload = (row: DocumentItem) => {
  // 模拟下载，实际需要调用接口
  window.open(row.downloadUrl, '_blank');
};
</script>

<style scoped lang="less">
.document-view-container {
  padding: 20px;
  
  .t-form {
    margin-bottom: 20px;
  }
  
  .preview-container {
    padding: 16px;
    
    .preview-header {
      margin-bottom: 24px;
      
      h3 {
        font-size: 20px;
        margin-bottom: 8px;
      }
      
      .preview-info {
        display: flex;
        flex-wrap: wrap;
        margin-bottom: 8px;
        gap: 16px;
        font-size: 14px;
        color: rgba(0, 0, 0, 0.6);
      }
      
      .preview-description {
        font-size: 14px;
        line-height: 1.6;
        margin-top: 16px;
        padding: 12px;
        background: #f5f5f5;
        border-radius: 4px;
      }
    }
    
    .pdf-preview {
      height: 600px;
      border: 1px solid #e5e5e5;
      border-radius: 4px;
      overflow: hidden;
    }
    
    .other-preview {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 400px;
    }
  }
}
</style> 
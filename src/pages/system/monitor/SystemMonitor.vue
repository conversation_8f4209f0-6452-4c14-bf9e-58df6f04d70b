<template>
  <div class="system-monitor-container">
        <!-- 实时监控区域 -->
    <div class="monitor-realtime">
      <t-row :gutter="[16, 16]">
        <t-col :span="24">
          <t-card title="实时监控" :bordered="true">
            <div class="monitor-status">
              <div class="status-item">
                <div class="status-title">CPU使用率</div>
                <div class="status-value" :class="cpuData.usage > 80 ? 'danger' : cpuData.usage > 60 ? 'warning' : 'success'">
                  {{ cpuData.usage.toFixed(2) }}%
                </div>
                <t-progress :percentage="cpuData.usage" :color="cpuData.usage > 80 ? '#E54D42' : cpuData.usage > 60 ? '#F9A826' : '#0ECD9D'" />
              </div>
              <div class="status-item">
                <div class="status-title">内存使用率</div>
                <div class="status-value" :class="memoryData.usage > 80 ? 'danger' : memoryData.usage > 60 ? 'warning' : 'success'">
                  {{ memoryData.usage.toFixed(2) }}%
                </div>
                <t-progress :percentage="memoryData.usage" :color="memoryData.usage > 80 ? '#E54D42' : memoryData.usage > 60 ? '#F9A826' : '#0ECD9D'" />
              </div>
              <div class="status-item">
                <div class="status-title">JVM使用率</div>
                <div class="status-value" :class="jvmData.usage > 80 ? 'danger' : jvmData.usage > 60 ? 'warning' : 'success'">
                  {{ jvmData.usage.toFixed(2) }}%
                </div>
                <t-progress :percentage="jvmData.usage" :color="jvmData.usage > 80 ? '#E54D42' : jvmData.usage > 60 ? '#F9A826' : '#0ECD9D'" />
              </div>
              <div class="status-item">
                <div class="status-title">并发量</div>
                <div class="status-value" :class="systemLoadData.concurrentRequests / systemLoadData.maxConcurrentRequests * 100 > 80 ? 'danger' : systemLoadData.concurrentRequests / systemLoadData.maxConcurrentRequests * 100 > 60 ? 'warning' : 'success'">
                  {{ systemLoadData.concurrentRequests }}/{{ systemLoadData.maxConcurrentRequests }}
                </div>
                <t-progress
                  :percentage="systemLoadData.concurrentRequests / systemLoadData.maxConcurrentRequests * 100"
                  :color="systemLoadData.concurrentRequests / systemLoadData.maxConcurrentRequests * 100 > 80 ? '#E54D42' : systemLoadData.concurrentRequests / systemLoadData.maxConcurrentRequests * 100 > 60 ? '#F9A826' : '#0ECD9D'"
                />
              </div>
              <div class="status-item">
                <div class="status-title">每秒请求数</div>
                <div class="status-value">{{ systemLoadData.qps }}</div>
                <t-progress :percentage="systemLoadData.qps / 250 * 100" :color="'#0681d0'" />
              </div>
            </div>
          </t-card>
        </t-col>
      </t-row>
    </div>
    <!-- 顶部卡片区域 -->
    <div class="monitor-top-cards">
      <t-row :gutter="[16, 16]">
        <!-- 服务器信息卡片 -->
        <t-col :xs="24" :sm="24" :md="4" :lg="4" :xl="4">
          <t-card :bordered="true" class="info-card server-card" hover-shadow>
            <template #title>
              <div class="card-title">
                <t-icon name="server" size="24px" />
                <span>服务器信息</span>
              </div>
            </template>
            <template #actions>
              <t-button theme="primary" size="small" shape="circle" variant="text">
                <template #icon><t-icon name="refresh" /></template>
              </t-button>
            </template>
            <div class="info-list">
              <div class="info-item">
                <span class="info-label"><t-icon name="internet" /> 服务器名称</span>
                <span class="info-value highlight">{{ systemLoadData.serverName }}</span>
              </div>
              <div class="info-item">
                <span class="info-label"><t-icon name="link" /> 服务器IP</span>
                <span class="info-value">{{ systemLoadData.serverIp }}</span>
              </div>
              <div class="info-item">
                <span class="info-label"><t-icon name="setting" /> 操作系统</span>
                <span class="info-value">{{ systemLoadData.osName }}</span>
              </div>
              <div class="info-item">
                <span class="info-label"><t-icon name="layers" /> 系统架构</span>
                <span class="info-value">{{ systemLoadData.osArch }}</span>
              </div>
              <div class="info-item">
                <span class="info-label"><t-icon name="discount" /> CPU核心数</span>
                <span class="info-value highlight">{{ systemLoadData.cpuCount }} 核</span>
              </div>
              <div class="info-item">
                <span class="info-label"><t-icon name="time" /> 运行时间</span>
                <span class="info-value highlight">{{ systemLoadData.uptime }}</span>
              </div>
            </div>
          </t-card>
        </t-col>

        <!-- JVM信息卡片 -->
        <t-col :xs="24" :sm="24" :md="4" :lg="4" :xl="4">
          <t-card :bordered="true" class="info-card jvm-card" hover-shadow>
            <template #title>
              <div class="card-title">
                <t-icon name="swap" size="24px" />
                <span>JVM信息</span>
              </div>
            </template>
            <template #actions>
              <t-button theme="primary" size="small" shape="circle" variant="text">
                <template #icon><t-icon name="refresh" /></template>
              </t-button>
            </template>
            <div class="info-list">
              <div class="info-item">
                <span class="info-label"><t-icon name="flag" /> JDK版本</span>
                <span class="info-value">{{ jvmData.version }}</span>
              </div>
              <div class="info-item">
                <span class="info-label"><t-icon name="folder" /> JDK路径</span>
                <span class="info-value ellipsis" :title="jvmData.home">{{ jvmData.home }}</span>
              </div>
              <div class="info-item">
                <span class="info-label"><t-icon name="play-circle-stroke" /> 启动时间</span>
                <span class="info-value">{{ jvmData.startTime }}</span>
              </div>
              <div class="info-item">
                <span class="info-label"><t-icon name="time" /> 运行时长</span>
                <span class="info-value highlight">{{ jvmData.runTime }}</span>
              </div>
              <div class="info-item">
                <span class="info-label"><t-icon name="loop" /> GC次数</span>
                <span class="info-value">{{ jvmData.gcCount }} 次</span>
              </div>
              <div class="info-item">
                <span class="info-label"><t-icon name="timer" /> GC耗时</span>
                <span class="info-value">{{ jvmData.gcTime }} ms</span>
              </div>
            </div>
          </t-card>
        </t-col>

        <!-- 请求统计卡片 -->
        <t-col :xs="24" :sm="24" :md="4" :lg="4" :xl="4">
          <t-card :bordered="true" class="info-card request-card" hover-shadow>
            <template #title>
              <div class="card-title">
                <t-icon name="chart-bubble" size="24px" />
                <span>请求统计</span>
              </div>
            </template>
            <template #actions>
              <t-button theme="primary" size="small" shape="circle" variant="text">
                <template #icon><t-icon name="refresh" /></template>
              </t-button>
            </template>
            <div class="info-list">
              <div class="info-item">
                <span class="info-label"><t-icon name="root-list" /> 今日请求数</span>
                <span class="info-value highlight">{{ requestData.today.toLocaleString() }}</span>
              </div>
              <div class="info-item">
                <span class="info-label"><t-icon name="chart-bubble" /> 昨日请求数</span>
                <span class="info-value">{{ requestData.yesterday.toLocaleString() }}</span>
              </div>
              <div class="info-item">
                <span class="info-label"><t-icon name="view-module" /> 本周请求数</span>
                <span class="info-value">{{ requestData.week.toLocaleString() }}</span>
              </div>
              <div class="info-item">
                <span class="info-label"><t-icon name="chart" /> 本月请求数</span>
                <span class="info-value">{{ requestData.month.toLocaleString() }}</span>
              </div>
              <div class="info-item">
                <span class="info-label"><t-icon name="user-circle" /> 当前并发数</span>
                <span class="info-value highlight">{{ systemLoadData.concurrentRequests }}</span>
              </div>
              <div class="info-item">
                <span class="info-label"><t-icon name="arrow-right" /> 平均响应时间</span>
                <span class="info-value">{{ systemLoadData.responseTime }} ms</span>
              </div>
            </div>
          </t-card>
        </t-col>
      </t-row>
    </div>



    <!-- 图表区域 -->
    <div class="monitor-charts">
      <t-row :gutter="[16, 16]">
        <!-- CPU使用率图表 -->
        <t-col :xs="24" :sm="24" :md="3" :lg="3" :xl="3">
          <t-card :bordered="true">
            <div ref="cpuChartRef" class="chart-container"></div>
          </t-card>
        </t-col>

        <!-- 内存使用情况图表 -->
        <t-col :xs="24" :sm="24" :md="3" :lg="3" :xl="3">
          <t-card :bordered="true">
            <div ref="memoryChartRef" class="chart-container"></div>
          </t-card>
        </t-col>

        <!-- JVM使用情况图表 -->
        <t-col :xs="24" :sm="24" :md="3" :lg="3" :xl="3">
          <t-card :bordered="true">
            <div ref="jvmChartRef" class="chart-container"></div>
          </t-card>
        </t-col>

        <!-- 请求统计图表 -->
        <t-col :xs="24" :sm="3" :md="3" :lg="3" :xl="3">
          <t-card :bordered="true">
            <div ref="requestStatRef" class="chart-container"></div>
          </t-card>
        </t-col>
      </t-row>
    </div>

    <!-- 磁盘使用情况 -->
    <div class="monitor-disk">
      <t-row :gutter="[16, 16]">
        <t-col :span="24">
          <t-card title="磁盘使用情况" :bordered="true" class="disk-card" hover-shadow>
            <template #title>
              <div class="card-title">
                <t-icon name="root-list" size="24px" />
                <span>磁盘使用情况</span>
              </div>
            </template>
            <t-table
              :data="diskData"
              stripe
              hover
              row-key="name"
              :columns="[
                { colKey: 'name', title: '磁盘名称', width: '15%' },
                { colKey: 'type', title: '挂载点', width: '15%' },
                { colKey: 'total', title: '总容量(GB)', width: '15%' },
                { colKey: 'used', title: '已用容量(GB)', width: '15%' },
                { colKey: 'available', title: '可用容量(GB)', width: '15%' },
                { colKey: 'usage', title: '使用率', width: '25%' }
              ]"
            >
              <template #usage="{ row }">
                <t-progress
                  :percentage="row.usage"
                  :color="row.usage > 80 ? '#E54D42' : row.usage > 60 ? '#F9A826' : '#0ECD9D'"
                  :label="true"
                />
              </template>
            </t-table>
          </t-card>
        </t-col>
      </t-row>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref, reactive, onMounted, computed, nextTick, onBeforeUnmount } from 'vue';
import { MessagePlugin } from 'tdesign-vue-next';
import * as echarts from 'echarts';
import {
  getRealtimeMonitorData,
  getServerInfo,
  getJvmInfo,
  getApiStats,
  getCpuUsageSeries,
  getMemoryUsageSeries
} from '@/api/system/monitor';

// CPU使用率图表
const cpuChartRef = ref<HTMLDivElement | null>(null);
let cpuChart: echarts.ECharts | null = null;
const cpuData = ref({
  total: 8, // 总核心数
  used: 3.5, // 已使用
  usage: 43.75, // 使用率
  loadAvg: [2.05, 1.78, 1.56], // 1分钟、5分钟、15分钟负载
  coreUsage: [38, 42, 56, 33, 29, 51, 42, 49] // 每个核心使用率
});

// 内存使用情况
const memoryChartRef = ref<HTMLDivElement | null>(null);
let memoryChart: echarts.ECharts | null = null;
const memoryData = ref({
  total: 16, // 总内存 GB
  used: 10.2, // 已使用 GB
  usage: 63.75, // 使用率
  free: 5.8, // 空闲内存 GB
  swap: {
    total: 8, // 交换区总大小 GB
    used: 1.3, // 已使用 GB
    usage: 16.25 // 使用率
  }
});

// JVM内存使用情况
const jvmChartRef = ref<HTMLDivElement | null>(null);
let jvmChart: echarts.ECharts | null = null;
const jvmData = ref({
  max: 4, // 最大内存 GB
  total: 2, // 已分配内存 GB
  used: 1.4, // 已使用 GB
  usage: 70, // 使用率
  free: 0.6, // 空闲 GB
  version: '1.8.0_292', // JDK版本
  home: '/usr/lib/jvm/java-8-openjdk-amd64', // JDK路径
  startTime: '2023-03-24 08:00:00', // 启动时间
  runTime: '10天12小时34分钟', // 运行时间
  heapUsage: 68, // 堆内存使用率
  nonHeapUsage: 74, // 非堆内存使用率
  gcCount: 158, // GC次数
  gcTime: 3542 // GC耗时(ms)
});

// 磁盘使用情况
const diskData = ref([
  { name: '/', total: 120, used: 76.2, usage: 63.5, available: 43.8, type: '/' },
  { name: '/boot', total: 1, used: 0.25, usage: 25, available: 0.75, type: '/boot' },
  { name: '/data', total: 500, used: 385, usage: 77, available: 115, type: '/data' },
  { name: '/var', total: 100, used: 42, usage: 42, available: 58, type: '/var' }
]);

// 系统负载数据
const systemLoadData = ref({
  serverName: 'application-server-01', // 服务器名
  serverIp: '*************', // 服务器IP
  osName: 'CentOS Linux release 7.9', // 操作系统名
  osArch: 'x86_64', // 系统架构
  cpuCount: 8, // CPU核心数
  userDir: '/home/<USER>', // 用户目录
  activeUsers: 128, // 活跃用户数
  concurrentRequests: 45, // 当前并发请求数
  maxConcurrentRequests: 200, // 最大并发数
  qps: 138, // 每秒查询数
  responseTime: 42, // 平均响应时间(ms)
  uptime: '45天12小时34分钟' // 系统运行时间
});

// 请求统计数据
const requestStatRef = ref<HTMLDivElement | null>(null);
let requestStatChart: echarts.ECharts | null = null;
const requestData = ref({
  today: 15428, // 今日请求数
  yesterday: 14832, // 昨日请求数
  week: 98754, // 本周请求数
  month: 352187, // 本月请求数
  byHour: [320, 332, 301, 334, 390, 330, 320, 452, 542, 523, 640, 724, 656, 635, 465, 444, 543, 532, 421, 395, 367, 345, 312, 298], // 24小时请求分布
  byStatus: [
    { name: '成功', value: 12540, percentage: 81.28 },
    { name: '失败', value: 2458, percentage: 15.93 },
    { name: '超时', value: 430, percentage: 2.79 }
  ]
});

// 获取实时监控数据
const fetchRealtimeData = async () => {
  try {
    const response = await getRealtimeMonitorData();
    if (response.code === 200) {
      const data = response.data;
      
      // 更新CPU数据
      if (data.cpu) {
        cpuData.value.total = data.cpu.cpuNum || cpuData.value.total;
        cpuData.value.usage = data.cpu.used || cpuData.value.usage;
        cpuData.value.loadAvg = data.cpu.loadAverage || cpuData.value.loadAvg;
        
        // 如果有核心使用率数据，更新它
        if (data.cpu.coreUsage && Array.isArray(data.cpu.coreUsage)) {
          cpuData.value.coreUsage = data.cpu.coreUsage;
        }
      }
      
      // 更新内存数据
      if (data.mem) {
        memoryData.value.total = data.mem.total / 1024 || memoryData.value.total; // 转换为GB
        memoryData.value.used = data.mem.used / 1024 || memoryData.value.used;
        memoryData.value.free = data.mem.free / 1024 || memoryData.value.free;
        memoryData.value.usage = data.mem.usage || memoryData.value.usage;
        
        if (data.mem.swap) {
          memoryData.value.swap.total = data.mem.swap.total / 1024 || memoryData.value.swap.total;
          memoryData.value.swap.used = data.mem.swap.used / 1024 || memoryData.value.swap.used;
          memoryData.value.swap.usage = data.mem.swap.usage || memoryData.value.swap.usage;
        }
      }
      
      // 更新系统负载数据
      if (data.sys) {
        systemLoadData.value.serverName = data.sys.computerName || systemLoadData.value.serverName;
        systemLoadData.value.serverIp = data.sys.computerIp || systemLoadData.value.serverIp;
        systemLoadData.value.osName = data.sys.osName || systemLoadData.value.osName;
        systemLoadData.value.osArch = data.sys.osArch || systemLoadData.value.osArch;
        systemLoadData.value.userDir = data.sys.userDir || systemLoadData.value.userDir;
      }
      
      // 更新请求量数据
      if (data.request) {
        systemLoadData.value.qps = data.request.qps || systemLoadData.value.qps;
        systemLoadData.value.concurrentRequests = data.request.concurrentRequests || systemLoadData.value.concurrentRequests;
        systemLoadData.value.responseTime = data.request.avgResponseTime || systemLoadData.value.responseTime;
      }
      
      // 更新图表
      updateCharts();
    }
  } catch (error) {
    console.error('获取实时监控数据失败:', error);
  }
};

// 获取服务器信息
const fetchServerInfo = async () => {
  try {
    const response = await getServerInfo();
    if (response.code === 200) {
      const data = response.data;
      
      // 更新CPU数据
      if (data.cpu) {
        cpuData.value.total = data.cpu.cpuNum || cpuData.value.total;
        cpuData.value.usage = data.cpu.used || cpuData.value.usage;
      }
      
      // 更新内存数据
      if (data.mem) {
        memoryData.value.total = data.mem.total / 1024 || memoryData.value.total; // 转换为GB
        memoryData.value.used = data.mem.used / 1024 || memoryData.value.used;
        memoryData.value.free = data.mem.free / 1024 || memoryData.value.free;
        memoryData.value.usage = data.mem.usage || memoryData.value.usage;
      }
      
      // 更新系统信息
      if (data.sys) {
        systemLoadData.value.serverName = data.sys.computerName || systemLoadData.value.serverName;
        systemLoadData.value.serverIp = data.sys.computerIp || systemLoadData.value.serverIp;
        systemLoadData.value.osName = data.sys.osName || systemLoadData.value.osName;
        systemLoadData.value.osArch = data.sys.osArch || systemLoadData.value.osArch;
        systemLoadData.value.userDir = data.sys.userDir || systemLoadData.value.userDir;
      }
      
      // 更新磁盘数据
      if (data.sysFiles && Array.isArray(data.sysFiles)) {
        diskData.value = data.sysFiles.map(disk => ({
          name: disk.dirName,
          type: disk.typeName,
          total: disk.total / 1024, // 转换为GB
          used: disk.used / 1024,
          available: disk.free / 1024,
          usage: disk.usage
        }));
      }
    }
  } catch (error) {
    console.error('获取服务器信息失败:', error);
  }
};

// 获取JVM信息
const fetchJvmInfo = async () => {
  try {
    const response = await getJvmInfo();
    if (response.code === 200) {
      const data = response.data;
      
      jvmData.value.max = data.max / 1024 || jvmData.value.max; // 转换为GB
      jvmData.value.total = data.total / 1024 || jvmData.value.total;
      jvmData.value.used = data.used / 1024 || jvmData.value.used;
      jvmData.value.free = data.free / 1024 || jvmData.value.free;
      jvmData.value.usage = data.usage || jvmData.value.usage;
      jvmData.value.version = data.version || jvmData.value.version;
      jvmData.value.home = data.home || jvmData.value.home;
      jvmData.value.startTime = data.startTime || jvmData.value.startTime;
      jvmData.value.runTime = data.runTime || jvmData.value.runTime;
      
      if (data.memoryUsage) {
        jvmData.value.heapUsage = data.memoryUsage.heap?.usage || jvmData.value.heapUsage;
        jvmData.value.nonHeapUsage = data.memoryUsage.nonHeap?.usage || jvmData.value.nonHeapUsage;
      }
      
      if (data.gc) {
        jvmData.value.gcCount = (data.gc.minor?.count || 0) + (data.gc.major?.count || 0);
        jvmData.value.gcTime = (data.gc.minor?.time || 0) + (data.gc.major?.time || 0);
      }
    }
  } catch (error) {
    console.error('获取JVM信息失败:', error);
  }
};

// 获取API调用统计
const fetchApiStats = async () => {
  try {
    const response = await getApiStats();
    if (response.code === 200) {
      const data = response.data;
      
      requestData.value.today = data.today || requestData.value.today;
      requestData.value.yesterday = data.yesterday || requestData.value.yesterday;
      requestData.value.week = data.week || requestData.value.week;
      requestData.value.month = data.month || requestData.value.month;
      
      if (data.byHour && Array.isArray(data.byHour)) {
        requestData.value.byHour = data.byHour;
        
        // 更新请求统计图表
        if (requestStatChart) {
          requestStatChart.setOption({
            series: [{
              data: requestData.value.byHour
            }]
          });
        }
      }
      
      if (data.byStatus && Array.isArray(data.byStatus)) {
        requestData.value.byStatus = data.byStatus;
      }
    }
  } catch (error) {
    console.error('获取API调用统计失败:', error);
  }
};

// 获取CPU使用率时间序列
const fetchCpuUsageSeries = async () => {
  try {
    const response = await getCpuUsageSeries();
    if (response.code === 200 && Array.isArray(response.data)) {
      const cpuSeries = response.data;
      
      // 更新CPU使用率时间序列图表数据
      if (cpuChart && cpuSeries.length > 0) {
        // 提取时间和值
        const times = cpuSeries.map(item => item.time);
        const values = cpuSeries.map(item => item.value);
        
        // 更新图表
        cpuChart.setOption({
          xAxis: {
            data: times
          },
          series: [{
            data: values
          }]
        });
      }
    }
  } catch (error) {
    console.error('获取CPU使用率时间序列失败:', error);
  }
};

// 获取内存使用率时间序列
const fetchMemoryUsageSeries = async () => {
  try {
    const response = await getMemoryUsageSeries();
    if (response.code === 200 && Array.isArray(response.data)) {
      const memorySeries = response.data;
      
      // 更新内存使用率时间序列图表数据
      if (memoryChart && memorySeries.length > 0) {
        // 提取时间和值
        const times = memorySeries.map(item => item.time);
        const values = memorySeries.map(item => item.value);
        
        // 更新图表
        memoryChart.setOption({
          xAxis: {
            data: times
          },
          series: [{
            data: values
          }]
        });
      }
    }
  } catch (error) {
    console.error('获取内存使用率时间序列失败:', error);
  }
};

// 初始化CPU图表
const initCpuChart = () => {
  if (!cpuChartRef.value) return;

  cpuChart = echarts.init(cpuChartRef.value);
  const option = {
    title: {
      text: 'CPU使用率',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis',
      formatter: '{b}: {c}%'
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: Array.from({length: cpuData.value.coreUsage.length}, (_, i) => `核心${i+1}`)
    },
    yAxis: {
      type: 'value',
      min: 0,
      max: 100,
      axisLabel: {
        formatter: '{value}%'
      }
    },
    series: [
      {
        name: 'CPU使用率',
        type: 'bar',
        data: cpuData.value.coreUsage,
        itemStyle: {
          color: function(params: any) {
            const value = params.value;
            // 根据使用率设置不同颜色
            if (value < 50) {
              return '#0ECD9D'; // 绿色
            } else if (value < 80) {
              return '#F9A826'; // 黄色
            } else {
              return '#E54D42'; // 红色
            }
          }
        }
      }
    ]
  };

  cpuChart.setOption(option);
  window.addEventListener('resize', () => cpuChart?.resize());
};

// 初始化内存图表
const initMemoryChart = () => {
  if (!memoryChartRef.value) return;

  memoryChart = echarts.init(memoryChartRef.value);
  const option = {
    title: {
      text: '内存使用情况',
      left: 'center'
    },
    tooltip: {
      trigger: 'item',
      formatter: '{b}: {c}GB ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 'left',
      data: ['已用内存', '空闲内存']
    },
    series: [
      {
        name: '内存使用',
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '18',
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: [
          { value: memoryData.value.used, name: '已用内存', itemStyle: { color: '#0681d0' } },
          { value: memoryData.value.free, name: '空闲内存', itemStyle: { color: '#8fd0ec' } }
        ]
      }
    ]
  };

  memoryChart.setOption(option);
  window.addEventListener('resize', () => memoryChart?.resize());
};

// 初始化JVM图表
const initJvmChart = () => {
  if (!jvmChartRef.value) return;

  jvmChart = echarts.init(jvmChartRef.value);
  const option = {
    title: {
      text: 'JVM内存使用情况',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'value',
      max: jvmData.value.max,
      axisLabel: {
        formatter: '{value} GB'
      }
    },
    yAxis: {
      type: 'category',
      data: ['堆内存', '非堆内存'],
      axisLine: {
        show: true
      }
    },
    series: [
      {
        name: '已用',
        type: 'bar',
        stack: 'total',
        label: {
          show: true,
          formatter: '{c} GB'
        },
        emphasis: {
          focus: 'series'
        },
        data: [jvmData.value.used * 0.75, jvmData.value.used * 0.25],
        itemStyle: {
          color: '#0681d0'
        }
      },
      {
        name: '空闲',
        type: 'bar',
        stack: 'total',
        label: {
          show: true,
          formatter: '{c} GB',
          position: 'right'
        },
        emphasis: {
          focus: 'series'
        },
        data: [jvmData.value.free * 0.8, jvmData.value.free * 0.2],
        itemStyle: {
          color: '#8fd0ec'
        }
      }
    ]
  };

  jvmChart.setOption(option);
  window.addEventListener('resize', () => jvmChart?.resize());
};

// 初始化请求统计图表
const initRequestStatChart = () => {
  if (!requestStatRef.value) return;

  requestStatChart = echarts.init(requestStatRef.value);
  const option = {
    title: {
      text: '24小时请求量',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        label: {
          backgroundColor: '#6a7985'
        }
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: Array.from({length: 24}, (_, i) => `${i}时`)
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '请求数',
        type: 'line',
        emphasis: {
          focus: 'series'
        },
        data: requestData.value.byHour,
        smooth: true,
        itemStyle: {
          color: '#0681d0'
        },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            {
              offset: 0,
              color: 'rgba(6, 129, 208, 0.6)'
            },
            {
              offset: 1,
              color: 'rgba(6, 129, 208, 0.1)'
            }
          ])
        }
      }
    ]
  };

  requestStatChart.setOption(option);
  window.addEventListener('resize', () => requestStatChart?.resize());
};

// 更新图表
const updateCharts = () => {
  if (cpuChart) {
    cpuChart.setOption({
      series: [{
        data: cpuData.value.coreUsage
      }]
    });
  }

  if (memoryChart) {
    memoryChart.setOption({
      series: [{
        data: [
          { value: memoryData.value.used, name: '已用内存' },
          { value: memoryData.value.free, name: '空闲内存' }
        ]
      }]
    });
  }

  if (jvmChart) {
    jvmChart.setOption({
      series: [
        {
          data: [jvmData.value.used * 0.75, jvmData.value.used * 0.25]
        },
        {
          data: [jvmData.value.free * 0.8, jvmData.value.free * 0.2]
        }
      ]
    });
  }
};

// 实时数据刷新定时器
let timer: number | null = null;
const startRealtimeDataRefresh = () => {
  // 先立即获取一次数据
  fetchRealtimeData();
  
  // 然后设置定时器，每3秒刷新一次
  timer = window.setInterval(() => {
    fetchRealtimeData();
  }, 3000);
};

// 初始化
onMounted(() => {
  nextTick(async () => {
    // 初始化图表
    initCpuChart();
    initMemoryChart();
    initJvmChart();
    initRequestStatChart();
    
    // 获取初始数据
    await fetchServerInfo();
    await fetchJvmInfo();
    await fetchApiStats();
    await fetchCpuUsageSeries();
    await fetchMemoryUsageSeries();
    
    // 开始实时数据刷新
    startRealtimeDataRefresh();
  });
});

// 组件卸载前清除定时器
onBeforeUnmount(() => {
  if (timer) {
    clearInterval(timer);
    timer = null;
  }

  // 销毁图表
  cpuChart?.dispose();
  memoryChart?.dispose();
  jvmChart?.dispose();
  requestStatChart?.dispose();

  window.removeEventListener('resize', () => cpuChart?.resize());
  window.removeEventListener('resize', () => memoryChart?.resize());
  window.removeEventListener('resize', () => jvmChart?.resize());
  window.removeEventListener('resize', () => requestStatChart?.resize());
});
</script>

<style scoped lang="less">
.system-monitor-container {
  padding: 20px;
  background-color: var(--td-bg-color-container);
  border-radius: var(--td-radius-medium);
  .monitor-top-cards,

  .monitor-charts,
  .monitor-disk {
    margin-bottom: 16px;
  }
  .monitor-realtime{
    margin-bottom: 16px;

  }
  // 卡片通用样式
  .info-card {
    transition: all 0.3s ease;
    height: 100%;

    &:hover {
      transform: translateY(-5px);
    }

    .card-title {
      display: flex;
      align-items: center;
      gap: 8px;
      font-weight: bold;
      font-size: 16px;

      .t-icon {
        margin-top: -2px;
      }
    }
  }

  // 服务器卡片样式
  .server-card {
    border-top: 3px solid #0052d9;

    .card-title {
      color: #0052d9;
    }
  }

  // JVM卡片样式
  .jvm-card {
    border-top: 3px solid #0cbb52;

    .card-title {
      color: #0cbb52;
    }
  }

  // 请求统计卡片样式
  .request-card {
    border-top: 3px solid #ff9d00;

    .card-title {
      color: #ff9d00;
    }
  }

  .info-list {
    .info-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 8px 0;
      border-bottom: 1px dashed var(--td-border-level-1-color);

      &:last-child {
        border-bottom: none;
      }

      .info-label {
        color: var(--td-text-color-secondary);
        min-width: 120px;
        display: flex;
        align-items: center;
        gap: 5px;

        .t-icon {
          font-size: 16px;
        }
      }

      .info-value {
        color: var(--td-text-color-primary);
        font-weight: 500;

        &.highlight {
          color: #0052d9;
          font-weight: bold;
        }

        &.ellipsis {
          max-width: 180px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
    }
  }

  .monitor-status {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;

    .status-item {
      flex: 1;
      min-width: 248px;

      .status-title {
        margin-bottom: 8px;
        color: var(--td-text-color-secondary);
      }

      .status-value {
        font-size: 24px;
        font-weight: bold;
        margin-bottom: 8px;

        &.success {
          color: #0ECD9D;
        }

        &.warning {
          color: #F9A826;
        }

        &.danger {
          color: #E54D42;
        }
      }
    }
  }

  .chart-container {
    width: 100%;
    height: 300px;
  }
}
</style>

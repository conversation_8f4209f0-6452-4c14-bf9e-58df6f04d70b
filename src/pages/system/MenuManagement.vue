<template>
  <div class="menu-container">
    <!-- 操作按钮 -->
    <div class="operation-container">
      <t-space>
        <t-button theme="primary" @click="handleAdd">
          <template #icon><plus-icon /></template>
          新增
        </t-button>
        <t-button theme="danger" @click="handleBatchDelete" :disabled="!selectedRowKeys.length">
          <template #icon><delete-icon /></template>
          批量删除
        </t-button>
        <t-button @click="handleRefresh">
          <template #icon><refresh-icon /></template>
          刷新
        </t-button>
      </t-space>
    </div>

    <!-- 表格 -->
    <t-enhanced-table
      :data="menuList"
      :columns="columns"
      :treeExpandAndFoldIcon="treeExpandAndFoldIcon"
      :tree="{
        indent: 0,
        childrenKey: 'children',
        treeNodeColumnIndex: 0,
        checkStrictly: true
      }"
      row-key="id"
      bordered
      stripe
      hover
      :loading="loading"
      :expanded-keys="expandedKeys"
      :selected-row-keys="selectedRowKeys"
      :select-on-row-click="false"
      reserve-selected-row-on-paginate
      select-all-on-current-page
      show-sorted-column-background
      @expand-change="(keys) => expandedKeys = keys"
      @select-change="handleSelectChange"
      @select-all-change="handleSelectAll"
      @indeterminate-change="handleIndeterminateChange"
    >
      <template #title="{ row }">
        <div class="menu-title" :style="{ paddingLeft: getIndent(row) }" >
          <t-icon v-if="row.meta?.icon" :name="row.meta?.icon" class="menu-icon" />
          <span>{{ row.meta.title }}</span>
        </div>
      </template>
      <template #type="{ row }">
        <t-tag :theme="getTypeTag(row.type)">
          {{ getTypeText(row.type) }}
        </t-tag>
      </template>
      <template #hidden="{ row }">
        <t-switch
          v-model="row.meta.hidden"
          :custom-value="[true, false]"
          :label="['是', '否']"
          :default-value="false"
          @change="(val) => handleHiddenChange(row, val)"
        />
      </template>
      <template #expanded="{ row }">
        <t-switch
          v-model="row.meta.expanded"
          :custom-value="[true, false]"
          :label="['是', '否']"
          :default-value="false"
          @change="(val) => handleExpandedChange(row, val)"
        />
      </template>
      <template #keepAlive="{ row }">
        <t-switch
          v-model="row.meta.keepAlive"
          :custom-value="[true, false]"
          :label="['是', '否']"
          :default-value="false"
          @change="(val) => handleKeepAliveChange(row, val)"
        />
      </template>
      <template #action="{ row }">
        <t-space :size="0">
          <t-button theme="success" variant="text" @click="handleAddChild(row)">
            添加
          </t-button>
          <t-button theme="primary" variant="text" @click="handleView(row)">
            详情
          </t-button>
          <t-button theme="primary" variant="text" @click="handleEdit(row)">
            编辑
          </t-button>
          <t-button theme="danger" variant="text" @click="handleDelete(row)">
            删除
          </t-button>
        </t-space>
      </template>
    </t-enhanced-table>

    <!-- 菜单表单对话框 -->
    <t-dialog
      v-model:visible="dialogVisible"
      :header="dialogTitle"
      :on-confirm="handleDialogConfirm"
      :on-close="handleDialogClose"
      width="680px"
      top="5vh"
      :close-on-overlay-click="false"
    >
      <t-form
        ref="formRef"
        :data="formData"
        :rules="rules"
        :label-width="100"
      >
        <t-form-item label="上级菜单" name="pid">
          <t-tree-select
            v-model="formData.pid"
            :data="menuTreeSelectData"
            :keys="{ value: 'id', label: 'title', children: 'children' }"
            :placeholder="formData.type === 2 ? '请选择最底层菜单' : '请选择上级菜单'"
            clearable
          >
          </t-tree-select>
        </t-form-item>
        <t-form-item label="菜单类型" name="type">
          <t-radio-group v-model="formData.type">
            <t-radio :value="0">目录</t-radio>
            <t-radio :value="1">菜单</t-radio>
            <t-radio :value="2">按钮</t-radio>
          </t-radio-group>
        </t-form-item>
        <t-form-item :label="formData.type === 2 ? '按钮名称' : '菜单名称'" name="title">
          <t-input v-model="formData.title" :placeholder="formData.type === 2 ? '请输入按钮名称' : '请输入菜单名称'" />
        </t-form-item>
        <template v-if="formData.type !== 2">
          <t-form-item label="路由名称" name="name">
            <t-input v-model="formData.name" placeholder="请输入路由名称" />
          </t-form-item>
          <t-form-item label="路由地址" name="path">
            <t-input v-model="formData.path" placeholder="请输入路由地址" />
          </t-form-item>
          <t-form-item name="component">
            <template #label>
              <t-tooltip placement="top-left" :offset="[0, 4]">
                <span>组件路径</span>
                <template #content>
                  <div class="tooltip-content" v-html="getComponentPathTooltip(formData.type)"></div>
                </template>
              </t-tooltip>
            </template>
            <!-- 目录类型：布局组件选择 -->
            <div v-if="formData.type === 0">
              <t-select
                v-model="formData.component"
                :options="layoutOptions"
                placeholder="请选择布局组件"
                clearable
              />
              <!-- 布局描述信息 -->
              <div v-if="selectedLayoutDescription" class="layout-description">
                <div class="layout-description-text">
                  {{ selectedLayoutDescription }}
                </div>
              </div>
            </div>
            <!-- 菜单类型：组件路径输入 -->
            <t-input
              v-else-if="formData.type === 1"
              v-model="formData.component"
              placeholder="请输入组件路径，如：pages/example/index"
            />
            <!-- 按钮类型：不需要组件路径 -->
            <t-input
              v-else
              v-model="formData.component"
              placeholder="按钮类型无需组件路径"
              disabled
            />

          </t-form-item>
          <t-form-item label="菜单图标" name="icon">
            <t-input-adornment prepend="图标">
              <t-input
                v-model="formData.icon"
                placeholder="请选择图标"
                readonly
                @click="showIconSelect = true"
              >
                <template #suffix>
                  <t-icon :name="formData.icon" v-if="formData.icon" />
                </template>
              </t-input>
            </t-input-adornment>
          </t-form-item>
        </template>
        <t-form-item label="权限标识" name="code">
          <t-input v-model="formData.code" placeholder="请输入权限标识" />
        </t-form-item>
        <t-form-item label="排序" name="sort">
          <t-input-number v-model="formData.sort" :min="0" :max="999" />
        </t-form-item>
        <t-form-item label="是否隐藏" name="hidden">
          <t-switch
            v-model="formData.hidden"
            :custom-value="[true, false]"
            :label="['是', '否']"
            :default-value="false"
          />
        </t-form-item>
        <template v-if="formData.type === 0">
          <t-form-item label="重定向" name="redirect">
            <t-input v-model="formData.redirect" placeholder="请输入重定向地址" />
          </t-form-item>
          <t-form-item label="单层菜单" name="single">
            <t-switch
              v-model="formData.single"
              :custom-value="[true, false]"
              :label="['是', '否']"
              :default-value="false"
            />
          </t-form-item>
        </template>
      </t-form>
    </t-dialog>

    <!-- 详情对话框 -->
    <t-dialog
      v-model:visible="detailDialogVisible"
      header="菜单详情"
      :on-close="handleDetailClose"
      width="680px"
    >
      <t-descriptions :data="detailData" :column="2" />
    </t-dialog>

    <!-- 删除确认对话框 -->
    <t-dialog
      v-model:visible="deleteDialogVisible"
      header="删除确认"
      :on-confirm="handleDeleteConfirm"
      :on-close="handleDeleteClose"
      :on-cancel="handleDeleteClose"
    >
      <p>{{ batchDeleteIds.length > 0 ? `确定要删除选中的${batchDeleteIds.length}个菜单吗？` : '确定要删除该菜单吗？' }}删除后不可恢复。</p>
    </t-dialog>

    <!-- 图标选择器弹窗 -->
    <t-dialog
      v-model:visible="showIconSelect"
      header="选择图标"
      :width="680"
      :footer="false"
    >
      <div class="icon-select-container">
        <t-input
          v-model="iconSearchValue"
          placeholder="搜索图标"
          clearable
          style="margin-bottom: 16px;"
        >
          <template #prefix-icon>
            <SearchIcon />
          </template>
        </t-input>
        <div class="icon-grid">
          <div
            v-for="item in filteredIcons"
            :key="item.stem"
            class="icon-item"
            :class="{ active: formData.icon === item.stem }"
            @click="handleIconSelect(item.stem)"
          >
            <t-icon :name="item.stem" />
            <span class="icon-name">{{ item.stem }}</span>
          </div>
        </div>
      </div>
    </t-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, watch, h, computed, getCurrentInstance } from 'vue';
import { FormInstanceFunctions } from 'tdesign-vue-next';
import { PlusIcon, SearchIcon, RefreshIcon, ChevronRightCircleIcon, ChevronDownCircleIcon, DeleteIcon } from 'tdesign-icons-vue-next';
import { getMenuList, getMenuTree, addMenu, updateMenu, deleteMenu, updateMenuStatus } from '@/api/system/menu';
import { manifest } from 'tdesign-icons-vue-next';
const iconsList = ref(manifest);

// 布局组件选项配置
const layoutOptions = [
  {
    label: 'Layout',
    value: 'Layout',
    description: '标准布局：包含顶部导航和侧边栏，适用于大部分管理页面。侧边栏显示完整的系统菜单树，支持菜单展开折叠。'
  },
  {
    label: 'HeaderOnly',
    value: 'HeaderOnly',
    description: '简洁布局：仅包含顶部导航，适用于工作台、展示页面等需要全屏显示内容的场景。'
  },
  {
    label: 'DynamicSidebar',
    value: 'DynamicSidebar',
    description: '动态侧边栏布局：包含顶部导航和动态侧边栏，侧边栏根据当前模块显示过滤后的子菜单。适用于模块化功能页面，如课程详情、项目管理等。支持返回上级页面功能。'
  }
];

interface MenuItem {
  id: number;
  pid?: number;
  path?: string;
  name?: string;
  component?: string;
  redirect?: string;
  type: number;
  title?: string;
  sort?: number;
  icon?: string;
  children?: MenuItem[];
  meta?: {
    title: string;
    icon?: string;
    code?: string;
    expanded?: boolean;
    orderNo: number;
    hidden: boolean;
    hiddenBreadcrumb?: boolean;
    single?: boolean;
    frameSrc?: string;
    frameBlank?: boolean;
    keepAlive?: boolean;
    noClosable?: boolean;
    noColumn?: boolean;
    badge?: string;
    target?: string;
    activeMenu?: string;
  };
}

interface SearchForm {
  title?: string;
  code?: string;
  type?: number;
  hidden?: boolean;
}

const columns = [
  {
    title: '',
    colKey: 'expand',
    width: 50,
    align: 'center' as 'center',
  },
  {
    colKey: 'row-select',
    type: 'multiple' as 'multiple',
    width: 46,
  },
  {
    title: '菜单名称',
    colKey: 'title',
    width: 200,
    tree: true
  },
  {
    title: '路由名称',
    colKey: 'name',
    width: 150,
  },
  {
    title: '路由地址',
    colKey: 'path',
    width: 150,
  },
  {
    title: '权限标识',
    colKey: 'meta.code',
    width: 150,
  },
  {
    title: '组件路径',
    colKey: 'component',
    width: 150,
  },
  {
    title: '菜单类型',
    colKey: 'type',
    width: 100,
    align: 'center' as 'center',
  },
  {
    title: '排序',
    colKey: 'meta.orderNo',
    width: 64,
    align: 'center' as 'center',
  },
  {
    title: '隐藏',
    colKey: 'hidden',
    width: 64,
  },
  {
    title: '操作',
    colKey: 'action',
    width: 280,
    fixed: 'right' as 'right',
    align: 'center' as 'center',
  }
];

// 获取菜单类型标签
const getTypeTag = (type: number) => {
  switch (type) {
    case 0:
      return 'primary';
    case 1:
      return 'success';
    case 2:
      return 'warning';
    default:
      return 'default';
  }
};

// 获取菜单类型文本
const getTypeText = (type: number) => {
  switch (type) {
    case 0:
      return '目录';
    case 1:
      return '菜单';
    case 2:
      return '按钮';
    default:
      return '未知';
  }
};

const menuList = ref<MenuItem[]>([]);
const searchForm = ref<SearchForm>({
  title: '',
  code: '',
  type: undefined,
  hidden: undefined
});

const loading = ref(false);
const expandedKeys = ref<(string | number)[]>([]);

// 选中的行
const selectedRows = ref<MenuItem[]>([]);
const selectedRowKeys = ref<Array<string | number>>([]);

// 选择变化
const handleSelectChange = (selectedKeys: Array<string | number>, options: { selectedRowData: Array<any>; type: 'uncheck' | 'check'; currentRowKey?: string; currentRowData?: any }) => {
  console.log('选择变化:', selectedKeys, options);
  selectedRowKeys.value = selectedKeys;
  selectedRows.value = options.selectedRowData as MenuItem[];
};

// 全选变化
const handleSelectAll = (checked: boolean) => {
  console.log('全选变化:', checked);
};

// 处理半选状态
const handleIndeterminateChange = (indeterminate: boolean, { rowKeys }: { rowKeys: Array<string | number> }) => {
  console.log('半选状态变化:', indeterminate, rowKeys);
};

const { proxy } = getCurrentInstance();

// 批量删除
const handleBatchDelete = () => {
  if (!selectedRowKeys.value.length) {
    proxy.$baseMessage('请选择要删除的菜单', 'warning');
    return;
  }

  // 设置批量删除ID数组并显示确认对话框
  batchDeleteIds.value = selectedRowKeys.value.map(id => Number(id));
  currentDeleteId.value = null; // 清空单个删除ID
  deleteDialogVisible.value = true;
};

// 刷新菜单
const handleRefresh = async () => {
  // 保存当前表格展开状态
  saveExpandedState();

  // 刷新数据
  await fetchMenuListData();

  // 恢复表格展开状态
  restoreExpandedState();

  proxy.$baseMessage('刷新成功', 'success');
};

// 获取菜单列表
const fetchMenuListData = async () => {
  try {
    loading.value = true;
    const res = await getMenuList({});
    console.log('菜单列表数据:', res.data);
    // 确保 meta 字段中的布尔值类型正确
    const processMenuData = (menu: MenuItem): MenuItem => {
      if (menu.meta) {
        menu.meta.hidden = typeof menu.meta.hidden === 'boolean' ? menu.meta.hidden : Boolean(menu.meta.hidden);
      }
      return {
        ...menu,
        children: menu.children ? menu.children.map(processMenuData) : undefined
      };
    };
    menuList.value = res.data.map(processMenuData);
    console.log('处理后的菜单列表:', menuList.value);

    // 如果savedExpandedKeys为空且当前没有展开的节点，设置默认展开根节点
    if (savedExpandedKeys.value.length === 0 && expandedKeys.value.length === 0) {
      // 默认展开顶层节点
      const topLevelIds = menuList.value
        .filter(item => item.pid === 0 || !item.pid)
        .map(item => item.id);
      expandedKeys.value = topLevelIds;
      console.log('设置默认展开的顶层节点:', expandedKeys.value);
    }
  } catch (error) {
    console.error('获取菜单列表失败:', error);
  } finally {
    loading.value = false;
  }
};

// 搜索
const handleSearch = () => {
  fetchMenuListData();
};

// 重置
const handleReset = () => {
  searchForm.value = {
    title: '',
    code: '',
    type: undefined,
    hidden: undefined
  };
  fetchMenuListData();
};

// 对话框相关
const dialogVisible = ref(false);
const dialogTitle = ref('新增菜单');
const formRef = ref<FormInstanceFunctions>();

// 修改表单数据接口，确保与 MenuDTO 结构一致
interface FormData {
  id?: number;
  pid?: number;
  sort?: number;
  code?: string;
  type: number;
  hidden: boolean;
  levelHidden: boolean;
  title: string;
  icon?: string;
  cache: boolean;
  badge?: string;
  target?: string;
  activeMenu?: string;
  name: string;
  path: string;
  component: string;
  redirect?: string;
  single: boolean;
  expanded: boolean;
  hiddenBreadcrumb: boolean;
  keepAlive: boolean;
  noClosable: boolean;
  noColumn: boolean;
}

// 修改表单数据初始化
const formData = reactive<FormData>({
  title: '',
  name: '',
  code: '',
  path: '',
  component: '',
  redirect: '',
  type: 0,
  sort: 0,
  hidden: false,
  levelHidden: false,
  icon: '',
  cache: false,
  badge: '',
  target: '',
  activeMenu: '',
  single: false,
  expanded: false,
  hiddenBreadcrumb: false,
  keepAlive: false,
  noClosable: false,
  noColumn: false
});

// 删除对话框
const deleteDialogVisible = ref(false);
const currentDeleteId = ref<number | null>(null);
const batchDeleteIds = ref<number[]>([]);

// 添加图标搜索相关状态
const iconSearchValue = ref('');
const filteredIcons = computed(() => {
  return !iconSearchValue.value
    ? iconsList.value
    : iconsList.value.filter(item =>
        item.stem.toLowerCase().includes(iconSearchValue.value.toLowerCase())
      );
});

// 计算选中布局的描述信息
const selectedLayoutDescription = computed(() => {
  if (formData.type === 0 && formData.component) {
    const selectedLayout = layoutOptions.find(option => option.value === formData.component);
    return selectedLayout ? selectedLayout.description : '';
  }
  return '';
});

// 表单校验规则
const rules = {
  title: [{ required: true, message: '请输入菜单名称', type: 'error' as const }],
  name: [{ required: true, message: '请输入路由名称', type: 'error' as const }],
  path: [{ required: true, message: '请输入路由地址', type: 'error' as const }],
  component: [{ required: true, message: '请输入组件路径', type: 'error' as const }],
  type: [{ required: true, message: '请选择菜单类型', type: 'error' as const }],
  sort: [{ required: true, message: '请输入排序号', type: 'error' as const }],
};

// 监听菜单类型变化
watch(() => formData.type, (newType) => {
  if (newType === 2) {
    // 按钮类型时，清空路由相关字段
    formData.name = '';
    formData.path = '';
    formData.component = ''; // 确保按钮类型时组件路径为空
    formData.redirect = '';
    formData.icon = '';
    // 设置开关字段为 false
    formData.expanded = false;
    formData.hiddenBreadcrumb = false;
    formData.single = false;
    formData.keepAlive = false;
    formData.noClosable = false;
    formData.noColumn = false;
    // 设置新字段
    formData.cache = false;
    formData.levelHidden = false;
  } else if (newType === 0) {
    // 目录类型时，如果组件路径为空则设置默认布局组件
    if (!formData.component || formData.component === '') {
      formData.component = 'Layout'; // 默认使用标准布局
    }
    // 如果已有值，保持用户的选择
  } else if (newType === 1) {
    // 菜单类型时，如果当前是布局组件，则清空让用户输入
    if (formData.component === 'Layout' || formData.component === 'HeaderOnly' || formData.component === 'DynamicSidebar') {
      formData.component = '';
    }
  }
});

// 获取父菜单路径
const getParentPath = (pid: number): string => {
  const findPath = (menuList: MenuItem[], targetId: number): string => {
    for (const menu of menuList) {
      if (menu.id === targetId) {
        return menu.path;
      }
      if (menu.children && menu.children.length > 0) {
        const childPath = findPath(menu.children, targetId);
        if (childPath) {
          return childPath;
        }
      }
    }
    return '';
  };
  return findPath(menuList.value, pid);
};

// 格式化组件路径
const formatComponentPath = (path: string, pid: number): string => {
  if (pid === 0) return path; // 一级菜单不需要处理
  if (!path) return '';
  // 确保路径以/开头，以.vue结尾
  let formattedPath = path;
  if (!formattedPath.startsWith('/')) {
    formattedPath = '/' + formattedPath;
  }
  if (!formattedPath.endsWith('.vue')) {
    formattedPath = formattedPath + '.vue';
  }
  return formattedPath;
};

// 监听路由地址变化
watch(() => formData.path, (newPath) => {
  if (formData.type !== 2 && formData.pid !== 0) {
    const parentPath = getParentPath(formData.pid);
    if (parentPath && !newPath.startsWith(parentPath)) {
      formData.path = parentPath + '/' + newPath;
    }
  }
});

// 监听组件路径变化
watch(() => formData.component, (newComponent) => {
  if (formData.type === 1 && formData.pid !== 0) {
    formData.component = formatComponentPath(newComponent, formData.pid);
  }
});

// 监听上级菜单变化
watch(() => formData.pid, (newPid) => {
  if (formData.type === 0) {
    // 目录类型时，如果没有设置组件或组件为空，则设置默认布局
    if (!formData.component || formData.component === '') {
      formData.component = 'Layout';
    }
    // 如果已有值，保持用户的选择
  } else if (formData.type === 1 && newPid !== 0) {
    // 当上级菜单变化时，重新格式化组件路径
    formData.component = formatComponentPath(formData.component, newPid);
  }

  // 如果当前是按钮类型，且选择了非零的父ID，切换回菜单类型
  if (formData.type === 2 && newPid !== 0) {
    formData.type = 1;
  }
});

// 获取菜单树形选项
const menuTreeData = ref<MenuItem[]>([]);

// 添加一个计算属性来处理树形选择器的数据
const menuTreeSelectData = computed(() => {
  // 为树形选择器调整数据结构，创建扁平化的树结构
  const filterButtons = (menus: MenuItem[]): any[] => {
    return menus
      .filter(menu => menu.type !== 2) // 过滤掉按钮类型
      .map(menu => ({
        id: menu.id,
        pid: menu.pid,
        title: menu.meta?.title || menu.title || '',
        children: menu.children ? filterButtons(menu.children) : undefined
      }));
  };

  if (!menuTreeData.value.length) return [];

  const rootMenu = menuTreeData.value[0];
  return [{
    id: rootMenu.id,
    pid: rootMenu.pid,
    title: '根目录', // 明确设置根目录的标题
    children: rootMenu.children ? filterButtons(rootMenu.children) : undefined
  }];
});

const fetchMenuTreeData = async () => {
  try {
    const res = await getMenuTree();
    console.log('菜单树形数据:', res.data);
    if (res.code === 200) {
      // 创建根目录，确保所有字段都初始化
      menuTreeData.value = [{
        id: 0,
        pid: -1,
        type: 0,
        title: '根目录', // 确保根目录有标题
        icon: 'app-icon', // 添加一个图标
        sort: 0,
        children: res.data
      }];
      console.log('处理后的菜单树形数据:', menuTreeData.value);
      // 默认展开根节点
      expandedKeys.value = [0];
    }
  } catch (error) {
    console.error('获取菜单树形选项失败:', error);
  }
};

// 新增菜单
const handleAdd = async () => {
  // 先获取最新的菜单树数据
  try {
    loading.value = true;
    await fetchMenuTreeData();
  } catch (error) {
    console.error('获取菜单树数据失败:', error);
    proxy.$baseMessage('获取菜单树数据失败', 'error');
  } finally {
    loading.value = false;
  }

  // 设置表单默认值
  dialogTitle.value = '新增菜单';
  formData.id = undefined;
  formData.pid = 0;
  formData.title = '';
  formData.name = '';
  formData.code = '';
  formData.type = 0;
  formData.path = '';
  formData.component = 'Layout'; // 默认设置为Layout，因为新增时默认是一级目录
  formData.redirect = '';
  formData.icon = 'menu-unfold'; // 设置默认图标
  formData.sort = 0;
  formData.hidden = false;
  formData.levelHidden = false;
  formData.cache = false;
  formData.badge = '';
  formData.target = '';
  formData.activeMenu = '';

  // 显示对话框
  dialogVisible.value = true;
};

// 新增子菜单
const handleAddChild = async (parentMenu: MenuItem) => {
  // 先获取最新的菜单树数据
  try {
    loading.value = true;
    await fetchMenuTreeData();
  } catch (error) {
    console.error('获取菜单树数据失败:', error);
    proxy.$baseMessage('获取菜单树数据失败', 'error');
  } finally {
    loading.value = false;
  }

  // 设置表单默认值
  dialogTitle.value = `新增"${parentMenu.meta.title}"的子菜单`;
  formData.id = undefined;
  formData.pid = parentMenu.id; // 设置父ID为当前菜单ID
  formData.title = '';
  formData.name = '';
  formData.code = '';

  // 根据父菜单类型设置合适的子菜单类型
  if (parentMenu.type === 0) { // 如果父菜单是目录，子菜单默认为菜单
    formData.type = 1;
  } else if (parentMenu.type === 1) { // 如果父菜单是菜单，子菜单默认为按钮
    formData.type = 2;
  } else {
    formData.type = 2; // 其他情况默认为按钮
  }

  formData.path = '';
  formData.component = '';
  formData.redirect = '';
  formData.icon = 'menu-unfold'; // 设置默认图标
  formData.sort = 0;
  formData.hidden = false;
  formData.levelHidden = false;
  formData.cache = false;
  formData.badge = '';
  formData.target = '';
  formData.activeMenu = '';

  // 显示对话框
  dialogVisible.value = true;
};

// 编辑菜单
const handleEdit = async (record: MenuItem) => {
  // 先获取最新的菜单树数据
  try {
    loading.value = true;
    await fetchMenuTreeData();
  } catch (error) {
    console.error('获取菜单树数据失败:', error);
    proxy.$baseMessage('获取菜单树数据失败', 'error');
  } finally {
    loading.value = false;
  }

  // 设置表单值
  dialogTitle.value = '编辑菜单';
  Object.assign(formData, {
    id: record.id,
    pid: record.pid === 0 || !record.pid ? 0 : record.pid,
    title: record.meta.title,
    name: record.name,
    code: record.meta.code || '',
    type: record.type,
    path: record.path,
    component: record.component,
    redirect: record.redirect || '',
    icon: record.meta.icon || '',
    sort: record.meta.orderNo || 0,
    hidden: record.meta.hidden,
    levelHidden: record.meta.hiddenBreadcrumb || false,
    cache: record.meta.keepAlive || false,
    badge: record.meta.badge || '',
    target: record.meta.target || '',
    activeMenu: record.meta.activeMenu || ''
  });

  // 显示对话框
  dialogVisible.value = true;
};

// 删除菜单
const handleDelete = (row: MenuItem) => {
  currentDeleteId.value = row.id;
  batchDeleteIds.value = []; // 清空批量删除ID数组
  deleteDialogVisible.value = true;
};

// 处理删除确认
const handleDeleteConfirm = async () => {
  try {
    let ids: number[] = [];

    // 判断是单个删除还是批量删除
    if (currentDeleteId.value) {
      // 单个删除
      ids = [currentDeleteId.value];
    } else if (batchDeleteIds.value.length > 0) {
      // 批量删除
      ids = batchDeleteIds.value;
    } else {
      return; // 没有要删除的ID，直接返回
    }

    // 保存当前表格展开状态
    saveExpandedState();

    // 执行删除操作
    await deleteMenu(ids);
    proxy.$baseMessage('删除成功', 'success');

    // 清理状态并关闭对话框
    deleteDialogVisible.value = false;
    currentDeleteId.value = null;
    batchDeleteIds.value = [];

    // 刷新数据
    await fetchMenuListData();

    // 恢复表格展开状态
    restoreExpandedState();
  } catch (error) {
    console.error('删除菜单失败:', error);
  }
};

// 关闭删除对话框
const handleDeleteClose = () => {
  deleteDialogVisible.value = false;
  currentDeleteId.value = null;
  batchDeleteIds.value = [];
};

// 确认表单
const handleDialogConfirm = async () => {
  try {
    const valid = await formRef.value?.validate();
    if (valid) {
      const data = {
        id: formData.id,
        pid: formData.pid,
        title: formData.title,
        name: formData.name,
        code: formData.code,
        type: formData.type,
        path: formData.path,
        component: formData.type === 2 ? '' : formData.component, // 按钮类型时组件路径为空
        redirect: formData.redirect,
        icon: formData.icon,
        sort: formData.sort,
        hidden: formData.hidden,
        levelHidden: formData.levelHidden,
        cache: formData.cache,
        badge: formData.badge,
        target: formData.target,
        activeMenu: formData.activeMenu
      };

      // 确保一级目录菜单有组件路径，如果为空则设置默认值
      if (data.type === 0 && (data.pid === 0 || data.pid === null) && (!data.component || data.component === '')) {
        data.component = 'Layout';
        console.log('一级目录菜单组件路径为空，设置默认值为Layout');
      }

      // 处理路由地址，移除父级路径
      if (data.type !== 2 && data.pid !== 0) {
        const parentPath = getParentPath(data.pid);
        if (parentPath && data.path.startsWith(parentPath)) {
          data.path = data.path.slice(parentPath.length + 1);
        }
      }

      console.log('提交到后端的数据:', data);

      // 保存当前表格展开状态
      saveExpandedState();

      if (formData.id) {
        await updateMenu(data);
        proxy.$baseMessage('更新成功', 'success');
      } else {
        await addMenu(data);
        proxy.$baseMessage('添加成功', 'success');
      }

      dialogVisible.value = false;

      // 刷新数据
      await fetchMenuListData();

      // 恢复表格展开状态
      restoreExpandedState();
    }
  } catch (error) {
    console.error('保存菜单失败:', error);
  }
};

// 关闭表单对话框
const handleDialogClose = () => {
  dialogVisible.value = false;
  formRef.value?.reset();
  Object.assign(formData, {
    id: undefined,
    pid: 0,
    title: '',
    name: '',
    code: '',
    type: 0,
    path: '',
    component: '',
    redirect: '',
    icon: 'menu-unfold',
    sort: 0,
    hidden: false,
    levelHidden: false,
    cache: false,
    badge: '',
    target: '',
    activeMenu: '',
    single: false,
    expanded: false,
    hiddenBreadcrumb: false,
    keepAlive: false,
    noClosable: false,
    noColumn: false
  });
};

// 处理隐藏状态变更
const handleHiddenChange = async (row: MenuItem, val: unknown) => {
  const originalValue = row.meta.hidden; // 保存原始值

  try {
    // 强制转换为布尔值
    const hidden = val === true;

    // 先更新UI状态，提供即时反馈
    row.meta.hidden = hidden;

    // 调用后端API更新状态
    await updateMenuStatus(row.id, hidden ? 1 : 0);

    // 显示成功消息
    proxy.$baseMessage(`菜单${hidden ? '隐藏' : '显示'}成功`, 'success');

    console.log(`菜单 "${row.meta.title}" 状态已更新为: ${hidden ? '隐藏' : '显示'}`);
  } catch (error) {
    console.error('更新菜单状态失败:', error);

    // 恢复原来的状态
    row.meta.hidden = originalValue;

    // 显示错误消息
    const errorMessage = error?.response?.data?.message || error?.message || '更新菜单状态失败';
    proxy.$baseMessage(errorMessage, 'error');
  }
};

// 处理展开状态变更
const handleExpandedChange = async (row: MenuItem, val: unknown) => {
  const originalValue = row.meta.expanded; // 保存原始值

  try {
    const expanded = val === true;
    row.meta.expanded = expanded;

    // 这里可以调用后端API更新展开状态，如果需要持久化的话
    // await updateMenuExpanded(row.id, expanded);

    proxy.$baseMessage(`菜单${expanded ? '展开' : '折叠'}状态已更新`, 'success');
    console.log(`菜单 "${row.meta.title}" 展开状态已更新为: ${expanded ? '展开' : '折叠'}`);
  } catch (error) {
    console.error('更新菜单展开状态失败:', error);
    row.meta.expanded = originalValue;
    proxy.$baseMessage('更新菜单展开状态失败', 'error');
  }
};

// 处理缓存状态变更
const handleKeepAliveChange = async (row: MenuItem, val: unknown) => {
  const originalValue = row.meta.keepAlive; // 保存原始值

  try {
    const keepAlive = val === true;
    row.meta.keepAlive = keepAlive;

    // 这里可以调用后端API更新缓存状态，如果需要持久化的话
    // await updateMenuKeepAlive(row.id, keepAlive);

    proxy.$baseMessage(`菜单${keepAlive ? '开启' : '关闭'}缓存成功`, 'success');
    console.log(`菜单 "${row.meta.title}" 缓存状态已更新为: ${keepAlive ? '开启' : '关闭'}`);
  } catch (error) {
    console.error('更新菜单缓存状态失败:', error);
    row.meta.keepAlive = originalValue;
    proxy.$baseMessage('更新菜单缓存状态失败', 'error');
  }
};

// 处理单层菜单状态变更
const handleSingleChange = async (row: MenuItem, val: unknown) => {
  const originalValue = row.meta.single; // 保存原始值

  try {
    const single = val === true;
    row.meta.single = single;

    // 这里可以调用后端API更新单层菜单状态，如果需要持久化的话
    // await updateMenuSingle(row.id, single);

    proxy.$baseMessage(`菜单${single ? '设为' : '取消'}单层显示成功`, 'success');
    console.log(`菜单 "${row.meta.title}" 单层显示状态已更新为: ${single ? '是' : '否'}`);
  } catch (error) {
    console.error('更新菜单单层显示状态失败:', error);
    row.meta.single = originalValue;
    proxy.$baseMessage('更新菜单单层显示状态失败', 'error');
  }
};

// 详情对话框
const detailDialogVisible = ref(false);
const detailData = ref([]);

// 查看详情
const handleView = (row: MenuItem) => {
  detailData.value = [
    { label: '菜单名称', content: row.meta.title },
    { label: '路由名称', content: row.name },
    { label: '权限标识', content: row.meta.code || '-' },
    { label: '路由地址', content: row.path },
    { label: '组件路径', content: row.component },
    { label: '菜单类型', content: getTypeText(row.type) },
    { label: '排序号', content: row.meta.orderNo || 0 },
    { label: '是否隐藏', content: row.meta.hidden ? '是' : '否' },
    { label: '是否展开', content: row.meta.expanded ? '是' : '否' },
    { label: '隐藏面包屑', content: row.meta.hiddenBreadcrumb ? '是' : '否' },
    { label: '单层菜单', content: row.meta.single ? '是' : '否' },
    { label: 'Keep Alive', content: row.meta.keepAlive ? '是' : '否' },
    { label: '不可关闭', content: row.meta.noClosable ? '是' : '否' },
    { label: '不显示列', content: row.meta.noColumn ? '是' : '否' },
  ];
  detailDialogVisible.value = true;
};

// 关闭详情对话框
const handleDetailClose = () => {
  detailDialogVisible.value = false;
};

// 处理展开/折叠点击
const handleExpandClick = (row: MenuItem) => {
  const index = expandedKeys.value.indexOf(row.id);
  if (index > -1) {
    expandedKeys.value.splice(index, 1);
  } else {
    expandedKeys.value.push(row.id);
  }
};

// 计算菜单层级
const getMenuLevel = (menuList: MenuItem[], id: number, level = 0): number => {
  for (const menu of menuList) {
    if (menu.id === id) {
      return level;
    }
    if (menu.children && menu.children.length > 0) {
      const childLevel = getMenuLevel(menu.children, id, level + 1);
      if (childLevel !== -1) {
        return childLevel;
      }
    }
  }
  return -1;
};

// 计算缩进值
const getIndent = (row: MenuItem) => {
  // 由于表格的tree配置中indent已设置为24，此处只需为非树形表格提供辅助缩进
  // 让我们为标题显示添加一些缩进以保持美观
  const level = getMenuLevel(menuList.value, row.id);
  if (level > 0) {
    return `${level * 24}px`;
  }
  return '0';
};

// 修正图标渲染函数
const renderIcon = (iconName: string) => {
  if (!iconName) return null;
  return h('t-icon', { name: iconName, size: '1.2em' });
};

// 显示图标选择器
const showIconSelect = ref(false);

// 监听图标选择器弹窗的显示
watch(showIconSelect, (newVal) => {
  if (newVal) {
    const icons = filteredIcons.value;
    console.log('图标选择器已打开，当前图标库总数:', icons.length);
    // 输出示例图标对象信息，便于了解结构
    if (icons.length > 0) {
      console.log('示例图标:', icons[0]);
      console.log('图标名称 (stem):', icons[0].stem);
      console.log('图标组件名 (icon):', icons[0].icon);
    }
  }
});

// 处理图标选择
const handleIconSelect = (iconName: string) => {
  formData.icon = iconName;
  console.log('选择的图标:', iconName);
  showIconSelect.value = false;
};

// 用于保存表格展开状态的变量
const savedExpandedKeys = ref<(string | number)[]>([]);

// 保存当前表格展开状态
const saveExpandedState = () => {
  savedExpandedKeys.value = [...expandedKeys.value];
  console.log('保存表格展开状态:', savedExpandedKeys.value);
};

// 恢复表格展开状态
const restoreExpandedState = () => {
  expandedKeys.value = [...savedExpandedKeys.value];
  console.log('恢复表格展开状态:', expandedKeys.value);
};

// 获取组件路径的提示信息
const getComponentPathTooltip = (type: number) => {
  switch (type) {
    case 0:
      return `布局组件选择：<br/>• Layout: 标准布局，包含顶部导航和侧边栏，适用于大部分管理页面。侧边栏显示完整的系统菜单树。<br/>• HeaderOnly: 简洁布局，仅包含顶部导航，适用于工作台、展示页面等需要全屏显示的场景。<br/>• DynamicSidebar: 动态侧边栏布局，侧边栏根据当前模块显示过滤后的子菜单，适用于模块化功能页面如课程详情等。`;
    case 1:
      return '请输入组件路径，例如：pages/example/index，对应 src/pages/example/index.vue 文件。<br/>组件路径相对于 src 目录，无需包含 .vue 扩展名。';
    case 2:
      return '按钮类型菜单不需要组件路径，主要用于权限控制。<br/>通过权限标识控制按钮或功能的显示与隐藏。';
    default:
      return '组件路径配置说明';
  }
};

// 树形表格展开/折叠图标渲染函数
const treeExpandAndFoldIcon = (h: any, { type }: { type: string }) => {
  return h(type === 'expand' ? ChevronRightCircleIcon : ChevronDownCircleIcon);
};

onMounted(() => {
  fetchMenuListData();
  fetchMenuTreeData();
});
</script>

<style scoped>
.menu-container {
  padding: 16px;
  padding-bottom: 72px; /* 为底部工具栏留出空间 */
}

.operation-container {
  margin-bottom: 16px;
}

.menu-title {
  display: flex;
  align-items: center;
  gap: 8px;
}

.menu-icon {
  font-size: 16px;
  vertical-align: middle;
}

.icon-select-container {
  max-height: 500px;
  overflow-y: auto;
}

.icon-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 12px;
  padding: 8px;
}

.icon-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
  border: 1px solid transparent;
}

.icon-item :deep(.t-icon) {
  font-size: 24px;
  margin-bottom: 8px;
}

.icon-item:hover {
  background-color: var(--td-brand-color-1);
  border-color: var(--td-brand-color);
}

.icon-item.active {
  background-color: var(--td-brand-color-1);
  border-color: var(--td-brand-color);
}

.icon-name {
  font-size: 12px;
  color: var(--td-text-color-secondary);
  text-align: center;
  word-break: break-all;
}

.batch-toolbar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 12px 24px;
  background-color: var(--td-bg-color-container);
  border-top: 1px solid var(--td-component-border);
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.05);
  z-index: 100;
}

.selected-count {
  color: var(--td-text-color-secondary);
  font-size: 14px;
}

.t-space :deep(.t-icon) {
  font-size: 16px;
  vertical-align: middle;
}

.layout-description {
  margin-top: 8px;
}

.layout-description-text {
  font-size: 12px;
  color: var(--td-text-color-secondary);
  background-color: var(--td-bg-color-container-hover);
  padding: 8px 12px;
  border-radius: 4px;
  border-left: 3px solid var(--td-brand-color);
  line-height: 1.5;
  font-weight: normal;
}

.tooltip-content {
  max-width: 300px;
  line-height: 1.4;
  white-space: normal;
}

.tooltip-content br {
  margin: 2px 0;
}
</style>

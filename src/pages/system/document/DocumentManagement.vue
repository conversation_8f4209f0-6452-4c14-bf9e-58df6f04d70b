<template>
  <div class="document-management-container">
    <t-card title="教学组织文件管理">
      <template #actions>
        <t-space>
          <t-button theme="primary" icon="upload" @click="handleOpenUploadDialog">上传文件</t-button>
          <t-button theme="default" icon="refresh" @click="refreshData">刷新</t-button>
        </t-space>
      </template>

      <!-- 搜索区域 -->
      <t-form ref="form" :data="formData" :colon="true" labelWidth="80px" layout="inline" @reset="onReset" @submit="onSubmit">
        <t-form-item label="文件名称" name="title">
          <t-input v-model="formData.title" placeholder="请输入文件名称" clearable />
        </t-form-item>
        <t-form-item label="文件类别" name="category">
          <t-select v-model="formData.category" placeholder="请选择文件类别" clearable>
            <t-option v-for="item in categoryOptions" :key="item.id" :value="item.name" :label="item.name" />
          </t-select>
        </t-form-item>
        <t-form-item label="文件状态" name="status">
          <t-select v-model="formData.status" placeholder="请选择状态" clearable>
            <t-option value="已发布" label="已发布" />
            <t-option value="草稿" label="草稿" />
          </t-select>
        </t-form-item>
        <t-form-item label="文件类型" name="fileType">
          <t-select v-model="formData.fileType" placeholder="请选择文件类型" clearable>
            <t-option value="pdf" label="PDF" />
            <t-option value="doc" label="DOC" />
            <t-option value="docx" label="DOCX" />
            <t-option value="xls" label="XLS" />
            <t-option value="xlsx" label="XLSX" />
            <t-option value="ppt" label="PPT" />
            <t-option value="pptx" label="PPTX" />
          </t-select>
        </t-form-item>
        <t-form-item>
          <t-space>
            <t-button theme="primary" type="submit">查询</t-button>
            <t-button theme="default" variant="base" type="reset">重置</t-button>
          </t-space>
        </t-form-item>
      </t-form>

      <!-- 文件表格 -->
      <t-table
        :data="documentList"
        :columns="columns"
        :row-key="rowKey"
        :loading="loading"
        :pagination="pagination"
        stripe
        hover
        @page-change="onPageChange"
      >
        <template #fileType="{ row }">
          <t-tag v-if="row.fileType === 'pdf'" theme="success">PDF</t-tag>
          <t-tag v-else-if="['doc', 'docx'].includes(row.fileType)" theme="primary">Word</t-tag>
          <t-tag v-else-if="['xls', 'xlsx'].includes(row.fileType)" theme="warning">Excel</t-tag>
          <t-tag v-else-if="['ppt', 'pptx'].includes(row.fileType)" theme="danger">PPT</t-tag>
          <t-tag v-else theme="default">{{ row.fileType }}</t-tag>
        </template>
        <template #fileSize="{ row }">
          <span>{{ formatFileSize(row.fileSize) }}</span>
        </template>
        <template #permission="{ row }">
          <t-tooltip :content="row.permission.join(', ')">
            <t-space>
              <t-tag v-if="row.permission.includes('admin')" theme="primary" variant="light">管理员</t-tag>
              <t-tag v-if="row.permission.includes('teacher')" theme="success" variant="light">教师</t-tag>
              <t-tag v-if="row.permission.includes('student')" theme="warning" variant="light">学生</t-tag>
              <t-tag v-if="row.permission.includes('all')" theme="default" variant="light">所有人</t-tag>
            </t-space>
          </t-tooltip>
        </template>
        <template #status="{ row }">
          <t-tag :theme="row.status === '已发布' ? 'success' : 'warning'">{{ row.status }}</t-tag>
        </template>
        <template #createTime="{ row }">
          {{ formatDate(row.createTime) }}
        </template>
        <template #operation="{ row }">
          <t-space>
            <t-button v-if="row.status === '草稿'" size="small" variant="outline" theme="primary" @click="handlePublish(row)">
              <template #icon><t-icon name="check-circle" /></template>
              发布
            </t-button>
            <t-button size="small" variant="outline" theme="primary" @click="handleView(row)">
              <template #icon><t-icon name="browse" /></template>
              预览
            </t-button>
            <t-button size="small" variant="outline" theme="primary" @click="handleDownload(row)">
              <template #icon><t-icon name="download" /></template>
              下载
            </t-button>
            <t-button size="small" variant="outline" theme="primary" @click="handleEdit(row)">
              <template #icon><t-icon name="edit" /></template>
              编辑
            </t-button>
            <t-button size="small" variant="outline" theme="danger" @click="handleDelete(row)">
              <template #icon><t-icon name="delete" /></template>
              删除
            </t-button>
          </t-space>
        </template>
      </t-table>

      <!-- 上传文件弹窗 -->
      <t-dialog
        v-model:visible="uploadDialogVisible"
        header="上传文件"
        width="700px"
        :footer="false"
        destroy-on-close
      >
        <t-form :data="uploadForm" :rules="uploadRules" label-width="80px">
          <t-form-item label="文件名称" name="title">
            <t-input v-model="uploadForm.title" placeholder="请输入文件名称" />
          </t-form-item>
          <t-form-item label="文件类别" name="category">
            <t-select v-model="uploadForm.category" placeholder="请选择文件类别">
              <t-option v-for="item in categoryOptions" :key="item.id" :value="item.name" :label="item.name" />
            </t-select>
          </t-form-item>
          <t-form-item label="文件状态" name="status">
            <t-radio-group v-model="uploadForm.status">
              <t-radio value="已发布">发布</t-radio>
              <t-radio value="草稿">保存为草稿</t-radio>
            </t-radio-group>
          </t-form-item>
          <t-form-item label="访问权限" name="permission">
            <t-checkbox-group v-model="uploadForm.permission">
              <t-checkbox value="admin">管理员</t-checkbox>
              <t-checkbox value="teacher">教师</t-checkbox>
              <t-checkbox value="student">学生</t-checkbox>
              <t-checkbox value="all">所有人</t-checkbox>
            </t-checkbox-group>
          </t-form-item>
          <t-form-item label="文件描述" name="description">
            <t-textarea v-model="uploadForm.description" placeholder="请输入文件描述" />
          </t-form-item>
          <t-form-item label="上传文件" name="file">
            <t-upload
              v-model="uploadForm.file"
              :action="uploadAction"
              theme="file"
              accept="application/pdf,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document,application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-powerpoint,application/vnd.openxmlformats-officedocument.presentationml.presentation"
              :request-method="customUpload"
              :before-upload="beforeUpload"
              :max-size="20 * 1024 * 1024"
              :format-response="formatUploadResponse"
              @success="onUploadSuccess"
              @fail="onUploadFail"
            />
          </t-form-item>
          <t-form-item>
            <t-space>
              <t-button theme="primary" @click="handleSubmitUpload">提交</t-button>
              <t-button theme="default" variant="base" @click="uploadDialogVisible = false">取消</t-button>
            </t-space>
          </t-form-item>
        </t-form>
      </t-dialog>

      <!-- 编辑文件弹窗 -->
      <t-dialog
        v-model:visible="editDialogVisible"
        header="编辑文件信息"
        width="700px"
        :footer="false"
        destroy-on-close
      >
        <t-form :data="editForm" :rules="uploadRules" label-width="80px">
          <t-form-item label="文件名称" name="title">
            <t-input v-model="editForm.title" placeholder="请输入文件名称" />
          </t-form-item>
          <t-form-item label="文件类别" name="category">
            <t-select v-model="editForm.category" placeholder="请选择文件类别">
              <t-option v-for="item in categoryOptions" :key="item.id" :value="item.name" :label="item.name" />
            </t-select>
          </t-form-item>
          <t-form-item label="文件状态" name="status">
            <t-radio-group v-model="editForm.status">
              <t-radio value="已发布">发布</t-radio>
              <t-radio value="草稿">保存为草稿</t-radio>
            </t-radio-group>
          </t-form-item>
          <t-form-item label="访问权限" name="permission">
            <t-checkbox-group v-model="editForm.permission">
              <t-checkbox value="admin">管理员</t-checkbox>
              <t-checkbox value="teacher">教师</t-checkbox>
              <t-checkbox value="student">学生</t-checkbox>
              <t-checkbox value="all">所有人</t-checkbox>
            </t-checkbox-group>
          </t-form-item>
          <t-form-item label="文件描述" name="description">
            <t-textarea v-model="editForm.description" placeholder="请输入文件描述" />
          </t-form-item>
          <t-form-item>
            <t-space>
              <t-button theme="primary" @click="handleSubmitEdit">提交</t-button>
              <t-button theme="default" variant="base" @click="editDialogVisible = false">取消</t-button>
            </t-space>
          </t-form-item>
        </t-form>
      </t-dialog>

      <!-- 文件预览弹窗 -->
      <t-dialog
        v-model:visible="previewDialogVisible"
        header="文件预览"
        width="80%"
        height="80%"
        :footer="false"
        destroy-on-close
      >
        <div class="preview-container">
          <template v-if="selectedFile">
            <div class="preview-header">
              <h3>{{ selectedFile.title }}</h3>
              <div class="preview-info">
                <span>上传者: {{ selectedFile.uploadedBy }}</span>
                <span>更新时间: {{ formatDate(selectedFile.updateTime) }}</span>
                <span>文件大小: {{ formatFileSize(selectedFile.fileSize) }}</span>
              </div>
              <div class="preview-description">{{ selectedFile.description }}</div>
            </div>
            
            <!-- PDF 预览 -->
            <div v-if="selectedFile.fileType === 'pdf'" class="pdf-preview">
              <iframe :src="previewUrl" width="100%" height="600" frameborder="0"></iframe>
            </div>
            
            <!-- 其他文件类型预览 -->
            <div v-else class="other-preview">
              <!-- <t-result
                status="info"
                title="暂不支持该类型文件预览"
                description="请下载查看或转换为 PDF 格式后上传"
              >
                <template #actions>
                  <t-button theme="primary" @click="handleDownload(selectedFile)">下载文件</t-button>
                </template>
              </t-result> -->
            </div>
          </template>
        </div>
      </t-dialog>
    </t-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { MessagePlugin } from 'tdesign-vue-next';
import dayjs from 'dayjs';
import {
  getDocumentList,
  getDocumentCategories,
  uploadDocument,
  updateDocument,
  deleteDocument,
  type DocumentItem,
  type DocumentCategory,
  type DocumentParams
} from '@/api/system/document';

// 表格列定义
const columns = [
  { colKey: 'title', title: '文件名称', width: 120 },
  { colKey: 'category', title: '文件类别', width: 120 },
  { colKey: 'fileType', title: '文件类型', width: 100 },
  { colKey: 'fileSize', title: '文件大小', width: 100 },
  { colKey: 'createTime', title: '创建时间', width: 160 },
  { colKey: 'permission', title: '访问权限', width: 160 },
  { colKey: 'status', title: '状态', width: 100 },
  { colKey: 'uploadedBy', title: '上传者', width: 120 },
  { colKey: 'operation', title: '操作', width: 240, fixed: 'right' },
];

// 数据定义
const documentList = ref<DocumentItem[]>([]);
const categoryOptions = ref<DocumentCategory[]>([]);
const loading = ref(false);
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showJumper: true,
  showPageSize: true,
  pageSizeOptions: [5, 10, 15, 20],
});

// 搜索表单
const formData = reactive<Partial<DocumentParams>>({
  title: '',
  category: '',
  fileType: '',
  status: '',
});

// 表格行键
const rowKey = (row: DocumentItem) => row.id;

// 上传文件相关
const uploadDialogVisible = ref(false);
const uploadAction = '/api/document/upload';
const uploadForm = reactive({
  title: '',
  category: '',
  description: '',
  status: '已发布',
  permission: ['admin'],
  file: [] as any[],
});

// 上传文件规则
const uploadRules = {
  title: [{ required: true, message: '请输入文件名称', type: 'error' }],
  category: [{ required: true, message: '请选择文件类别', type: 'error' }],
  permission: [{ required: true, message: '请选择文件访问权限', type: 'error' }],
  file: [{ required: true, message: '请上传文件', type: 'error' }],
};

// 编辑文件相关
const editDialogVisible = ref(false);
const editForm = reactive<Partial<DocumentItem> & { id: string }>({
  id: '',
  title: '',
  category: '',
  description: '',
  status: '',
  permission: [],
});

// 文件预览相关
const previewDialogVisible = ref(false);
const selectedFile = ref<DocumentItem | null>(null);
const previewUrl = ref('');

// 获取文件列表
const fetchDocumentList = async () => {
  loading.value = true;
  try {
    const params: DocumentParams = {
      page: pagination.current,
      pageSize: pagination.pageSize,
      ...formData,
    };
    const { list, total } = await getDocumentList(params);
    documentList.value = list;
    pagination.total = total;
  } catch (error) {
    console.error('获取文件列表失败:', error);
    MessagePlugin.error('获取文件列表失败');
  } finally {
    loading.value = false;
  }
};

// 获取文件类别
const fetchDocumentCategories = async () => {
  try {
    categoryOptions.value = await getDocumentCategories();
  } catch (error) {
    console.error('获取文件类别失败:', error);
    MessagePlugin.error('获取文件类别失败');
  }
};

// 初始化
onMounted(async () => {
  await fetchDocumentCategories();
  await fetchDocumentList();
});

// 分页变化
const onPageChange = (pageInfo: { current: number; pageSize: number }) => {
  pagination.current = pageInfo.current;
  pagination.pageSize = pageInfo.pageSize;
  fetchDocumentList();
};

// 搜索表单提交
const onSubmit = () => {
  pagination.current = 1;
  fetchDocumentList();
};

// 搜索表单重置
const onReset = () => {
  pagination.current = 1;
  fetchDocumentList();
};

// 刷新数据
const refreshData = () => {
  fetchDocumentList();
};

// 格式化日期
const formatDate = (dateStr: string) => {
  return dayjs(dateStr).format('YYYY-MM-DD HH:mm');
};

// 格式化文件大小
const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// 打开上传对话框
const handleOpenUploadDialog = () => {
  // 重置表单
  Object.assign(uploadForm, {
    title: '',
    category: '',
    description: '',
    status: '已发布',
    permission: ['admin'],
    file: [],
  });
  uploadDialogVisible.value = true;
};

// 自定义上传方法
const customUpload = (options: any) => {
  return uploadDocument(options);
};

// 上传前检查
const beforeUpload = (file: File) => {
  // 检查文件大小（20MB 以内）
  if (file.size > 20 * 1024 * 1024) {
    MessagePlugin.error('文件大小不能超过20MB');
    return false;
  }
  
  // 检查文件类型
  const allowedTypes = [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'application/vnd.ms-powerpoint',
    'application/vnd.openxmlformats-officedocument.presentationml.presentation'
  ];
  
  if (!allowedTypes.includes(file.type)) {
    MessagePlugin.error('只支持上传PDF、Word、Excel、PPT文件');
    return false;
  }
  
  return true;
};

// 格式化上传响应
const formatUploadResponse = (res: any) => {
  return {
    ...res,
    url: '/api/document/download/' + res.fileId,
  };
};

// 上传成功回调
const onUploadSuccess = (result: any) => {
  MessagePlugin.success('文件上传成功');
};

// 上传失败回调
const onUploadFail = (error: any) => {
  MessagePlugin.error('文件上传失败');
  console.error('上传失败:', error);
};

// 提交上传表单
const handleSubmitUpload = async () => {
  // 检查必填字段
  if (!uploadForm.title || !uploadForm.category || !uploadForm.permission.length || !uploadForm.file.length) {
    MessagePlugin.error('请填写必填项');
    return;
  }
  
  // 这里应该调用接口提交数据，这里仅模拟
  try {
    // 模拟提交成功
    MessagePlugin.success('文件上传成功');
    uploadDialogVisible.value = false;
    
    // 刷新列表
    await fetchDocumentList();
  } catch (error) {
    console.error('提交上传表单失败:', error);
    MessagePlugin.error('提交失败');
  }
};

// 预览文件
const handleView = (row: DocumentItem) => {
  selectedFile.value = row;
  // 模拟预览URL，实际应该是后端提供预览链接
  previewUrl.value = row.viewUrl;
  previewDialogVisible.value = true;
};

// 下载文件
const handleDownload = (row: DocumentItem) => {
  // 模拟下载，实际需要调用接口
  window.open(row.downloadUrl, '_blank');
};

// 编辑文件
const handleEdit = (row: DocumentItem) => {
  editForm.id = row.id;
  editForm.title = row.title;
  editForm.category = row.category;
  editForm.description = row.description;
  editForm.status = row.status;
  editForm.permission = [...row.permission];
  
  editDialogVisible.value = true;
};

// 提交编辑
const handleSubmitEdit = async () => {
  // 检查必填字段
  if (!editForm.title || !editForm.category || !editForm.permission?.length) {
    MessagePlugin.error('请填写必填项');
    return;
  }
  
  try {
    // 调用更新接口
    await updateDocument(editForm);
    MessagePlugin.success('更新成功');
    editDialogVisible.value = false;
    
    // 刷新列表
    await fetchDocumentList();
  } catch (error) {
    console.error('更新文件信息失败:', error);
    MessagePlugin.error('更新失败');
  }
};

// 删除文件
const handleDelete = (row: DocumentItem) => {
  // 使用普通的对话框而不是MessagePlugin.confirm
  if (window.confirm(`确定要删除文件 "${row.title}" 吗？此操作不可恢复。`)) {
    deleteDocument(row.id).then(() => {
      MessagePlugin.success('删除成功');
      fetchDocumentList();
    }).catch(error => {
      console.error('删除文件失败:', error);
      MessagePlugin.error('删除失败');
    });
  }
};

// 发布文件
const handlePublish = async (row: DocumentItem) => {
  try {
    await updateDocument({
      id: row.id,
      status: '已发布'
    });
    MessagePlugin.success('发布成功');
    await fetchDocumentList();
  } catch (error) {
    console.error('发布文件失败:', error);
    MessagePlugin.error('发布失败');
  }
};
</script>

<style scoped lang="less">
.document-management-container {
  padding: 20px;
  
  .t-form {
    margin-bottom: 20px;
  }
  
  .preview-container {
    padding: 16px;
    
    .preview-header {
      margin-bottom: 24px;
      
      h3 {
        font-size: 20px;
        margin-bottom: 8px;
      }
      
      .preview-info {
        display: flex;
        flex-wrap: wrap;
        margin-bottom: 8px;
        gap: 16px;
        font-size: 14px;
        color: rgba(0, 0, 0, 0.6);
      }
      
      .preview-description {
        font-size: 14px;
        line-height: 1.6;
        margin-top: 16px;
        padding: 12px;
        background: #f5f5f5;
        border-radius: 4px;
      }
    }
    
    .pdf-preview {
      height: 600px;
      border: 1px solid #e5e5e5;
      border-radius: 4px;
      overflow: hidden;
    }
    
    .other-preview {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 400px;
    }
  }
}
</style> 
<template>
  <div class="graduation-requirements-container">
    <!-- 顶部标题区域 -->
    <div class="header-section">
      <div class="header-content">
        <div class="title-wrapper">
          <h1 class="page-title" :contenteditable="isEditing" @blur="updateTitle">
            {{ pageTitle }}
          </h1>
          <t-select
            v-model="selectedGrade"
            :options="gradeOptions"
            class="grade-select"
            placeholder="请选择年级"
            clearable
          />
        </div>
      </div>

      <div class="header-actions">
        <t-button theme="primary" @click="toggleEditMode" class="edit-btn">
          <template #icon><edit-icon /></template>
          {{ isEditing ? '保存更改' : '编辑内容' }}
        </t-button>
      </div>
    </div>

    <!-- 统计信息卡片 -->
    <div class="stats-cards">
      <t-card class="stat-card" hover-shadow>
        <div class="stat-content">
          <div class="stat-icon total-icon">
            <file-icon />
          </div>
          <div class="stat-info">
            <div class="stat-value">{{ requirements.length }}</div>
            <div class="stat-label">总毕业要求</div>
          </div>
        </div>
      </t-card>

      <t-card class="stat-card" hover-shadow>
        <div class="stat-content">
          <div class="stat-icon sub-icon">
            <list-icon />
          </div>
          <div class="stat-info">
            <div class="stat-value">{{ totalSubRequirements }}</div>
            <div class="stat-label">子要求总数</div>
          </div>
        </div>
      </t-card>

      <t-card class="stat-card" hover-shadow>
        <div class="stat-content">
          <div class="stat-icon recent-icon">
            <time-icon />
          </div>
          <div class="stat-info">
            <div class="stat-value">{{ lastUpdated }}</div>
            <div class="stat-label">最后更新</div>
          </div>
        </div>
      </t-card>
    </div>

    <!-- 操作工具栏 -->
    <div class="toolbar">
      <t-space>
        <t-button variant="outline" @click="exportData">
          <template #icon><download-icon /></template>
          导出数据
        </t-button>
        <t-button variant="outline" @click="importData">
          <template #icon><upload-icon /></template>
          导入数据
        </t-button>
        <t-button v-if="isEditing" @click="addNewRequirement">
          <template #icon><add-icon /></template>
          添加毕业要求
        </t-button>
      </t-space>

    </div>

    <!-- 毕业要求列表 -->
    <div class="requirements-list">
      <div
        v-for="(req, index) in filteredRequirements"
        :key="req.id"
        class="requirement-item"
        :class="{ 'editing-item': isEditing }"
      >
        <!-- 要求头部 -->
        <div class="requirement-header" @click="toggleCollapse(index)">
          <div class="requirement-meta">
            <div class="requirement-number">{{ index + 1 }}</div>
            <div class="requirement-title" :contenteditable="isEditing" @blur="(e) => updateRequirementTitle(index, (e.target as HTMLElement).innerText)">
              {{ req.title }}
            </div>
          </div>

          <div class="requirement-actions">
            <t-button
              v-if="isEditing"
              shape="circle"
              variant="text"
              @click.stop="deleteRequirement(index)"
              class="action-btn delete-btn"
            >
              <template #icon><delete-icon /></template>
            </t-button>

            <t-button
              shape="circle"
              variant="text"
              class="action-btn toggle-btn"
              :class="{ rotated: req.collapsed }"
            >
              <template #icon><chevron-down-icon /></template>
            </t-button>
          </div>
        </div>

        <!-- 要求内容 -->
        <!-- <t-collapse-transition>
          <div v-show="!req.collapsed" class="requirement-content">
            <div class="description-section">
              <div class="section-label">要求描述</div>
              <div
                class="description-content"
                :contenteditable="isEditing"
                @blur="(e) => updateRequirementDescription(index, (e.target as HTMLElement).innerText)"
              >
                {{ req.description }}
              </div>
            </div>

            <div class="sub-requirements-section">
              <div class="section-label">二级指标点</div>

              <div class="sub-requirements-list">
                <div
                  v-for="(subReq, subIndex) in req.subRequirements"
                  :key="subReq.id"
                  class="sub-requirement-item"
                >
                  <div class="sub-requirement-meta">
                    <div class="sub-requirement-number">{{ index + 1 }}.{{ subIndex + 1 }}</div>
                    <div
                      class="sub-requirement-content"
                      :contenteditable="isEditing"
                      @blur="(e) => updateSubRequirement(index, subIndex, (e.target as HTMLElement).innerText)"
                    >
                      {{ subReq.content }}
                    </div>
                  </div>

                  <t-button
                    v-if="isEditing"
                    shape="circle"
                    variant="text"
                    @click.stop="deleteSubRequirement(index, subIndex)"
                    class="action-btn delete-sub-btn"
                  >
                    <template #icon><close-icon /></template>
                  </t-button>
                </div>
              </div>

              <t-button
                v-if="isEditing"
                variant="dashed"
                block
                @click="addSubRequirement(index)"
                class="add-sub-btn"
              >
                <template #icon><add-icon /></template>
                添加指标点
              </t-button>
            </div>
          </div>
        </t-collapse-transition> -->
      </div>
    </div>

    <!-- 空状态 -->
    <div v-if="filteredRequirements.length === 0" class="empty-state">
      <!-- <t-result
        title="暂无毕业要求"
        sub-title="添加或导入的毕业要求"
      >
        <template #icon>
          <file-icon />
        </template>
        <template #extra>
          <t-button v-if="isEditing" @click="addNewRequirement">
            <template #icon><add-icon /></template>
            添加毕业要求
          </t-button>
        </template>
      </t-result> -->
    </div>

    <!-- 导入对话框 -->
    <t-dialog
      v-model:visible="showImportDialog"
      header="导入毕业要求数据"
      :on-confirm="confirmImport"
      width="600px"
    >
      <div class="import-dialog-content">
        <t-alert theme="info" message="请确保导入的数据格式正确，建议先导出当前数据作为参考" />
        <t-textarea
          v-model="importDataText"
          placeholder="请粘贴JSON格式的数据..."
          :autosize="{ minRows: 8, maxRows: 12 }"
          class="import-textarea"
        />
      </div>
    </t-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { MessagePlugin, DialogPlugin } from 'tdesign-vue-next'
import {
  DownloadIcon,
  UploadIcon,
  EditIcon,
  DeleteIcon,
  AddIcon,
  ChevronDownIcon,
  CloseIcon,
  FileIcon,
  ListIcon,
  TimeIcon
} from 'tdesign-icons-vue-next'

// 页面标题
const pageTitle = ref('毕业要求')
const updateTitle = (e: Event) => {
  pageTitle.value = (e.target as HTMLElement).innerText
}

const selectedGrade = ref('freshman')
const gradeOptions = [
  { label: '21届', value: 'freshman' },
  { label: '22届', value: 'sophomore' },
  { label: '23届', value: 'junior' },
  { label: '24届', value: 'senior' }
]

// 编辑状态
const isEditing = ref(false)
const toggleEditMode = () => {
  isEditing.value = !isEditing.value
  if (!isEditing.value) {
    MessagePlugin.success('修改已保存')
  }
}

// 查看功能
const searchText = ref('')
const filteredRequirements = computed(() => {
  if (!searchText.value) return requirements.value
  const search = searchText.value.toLowerCase()
  return requirements.value.filter(req => {
    return (
      req.title.toLowerCase().includes(search) ||
      req.description.toLowerCase().includes(search) ||
      req.subRequirements.some(sub => sub.content.toLowerCase().includes(search))
    )
  })
})

// 统计信息
const lastUpdated = ref('今天')
const totalSubRequirements = computed(() => {
  return requirements.value.reduce((total, req) => total + req.subRequirements.length, 0)
})

// 毕业要求数据，模拟数据
interface SubRequirement {
  id: string
  content: string
}

interface Requirement {
  id: string
  title: string
  description: string
  subRequirements: SubRequirement[]
  collapsed: boolean
}

const requirements = ref<Requirement[]>([
  {
    id: generateId(),
    title: '思想品德',
    description:
      '具有坚定正确的政治方向，热爱祖国，热爱人民，拥护中国共产党的领导；具有正确的世界观、人生观、价值观，具有良好的思想品德、健全的人格、健康的体魄，践行社会主义核心价值观。',
    subRequirements: [
      {
        id: generateId(),
        content: '了解中国国情，拥护中国共产党的领导，具有爱国主义精神和社会责任感'
      },
      {
        id: generateId(),
        content: '具有正确的世界观、人生观、价值观，具备良好的道德品质和职业操守'
      },
      {
        id: generateId(),
        content: '具备健全的人格和健康的体魄，能够适应社会发展需求'
      }
    ],
    collapsed: false
  },
  {
    id: generateId(),
    title: '工程知识',
    description: '能够将数学、自然科学、工程基础和专业知识用于解决复杂工程问题。',
    subRequirements: [
      {
        id: generateId(),
        content: '掌握数学、自然科学和工程科学的基本原理和方法'
      },
      {
        id: generateId(),
        content: '掌握专业核心理论和知识体系，了解前沿发展趋势'
      },
      {
        id: generateId(),
        content: '能够运用专业知识分析和解决复杂工程问题'
      },
      {
        id: generateId(),
        content: '具备跨学科知识整合能力，能够应对综合性工程挑战'
      }
    ],
    collapsed: false
  },
  {
    id: generateId(),
    title: '问题分析',
    description: '能够应用数学、自然科学和工程科学的基本原理，识别、表达、并通过文献研究分析复杂工程问题，以获得有效结论。',
    subRequirements: [
      {
        id: generateId(),
        content: '能够识别和判断复杂工程问题的关键环节和因素'
      },
      {
        id: generateId(),
        content: '能够运用基本原理对工程问题进行建模和分析'
      },
      {
        id: generateId(),
        content: '能够通过文献研究寻求解决方案，并验证其有效性'
      }
    ],
    collapsed: true
  }
])

// 生成唯一ID,模拟
function generateId(): string {
  return Math.random().toString(36).substring(2, 9)
}

// 展开/折叠
const toggleCollapse = (index: number) => {
  requirements.value[index].collapsed = !requirements.value[index].collapsed
}

// 添加主要求
const addNewRequirement = () => {
  requirements.value.push({
    id: generateId(),
    title: '新毕业要求',
    description: '请在此输入毕业要求的详细描述...',
    subRequirements: [],
    collapsed: false
  })
  MessagePlugin.success('已添加新毕业要求')

  // 滚动到新添加的元素
  setTimeout(() => {
    const container = document.querySelector('.requirements-list')
    if (container) {
      container.scrollTop = container.scrollHeight
    }
  }, 100)
}

// 添加子要求
const addSubRequirement = (reqIndex: number) => {
  requirements.value[reqIndex].subRequirements.push({
    id: generateId(),
    content: '请在此输入指标点的具体内容...'
  })
  MessagePlugin.success('已添加指标点')
}

// 删除主要求
const deleteRequirement = (index: number) => {
  const confirmDlg = DialogPlugin.confirm({
    body: '确定要删除这个毕业要求吗？删除后将无法恢复',
    confirmBtn: '确定删除',
    cancelBtn: '取消',
    onConfirm: (e) => {
      // 直接操作数据并销毁对话框
      requirements.value.splice(index, 1);
      MessagePlugin.success('已删除毕业要求');
      confirmDlg.destroy(); // 销毁对话框
    },
    onClose: ({ trigger, e }) => {
      confirmDlg.destroy(); // 确保对话框被销毁
    }
  });
};


// 删除子要求
const deleteSubRequirement = (reqIndex: number, subIndex: number) => {
  const confirmDlg = DialogPlugin.confirm({
    body: '确定要删除这个指标点吗？删除后将无法恢复',
    confirmBtn: '确定删除',
    cancelBtn: '取消',
    onConfirm: (e) => {
      // 直接操作数据并销毁对话框
      requirements.value[reqIndex].subRequirements.splice(subIndex, 1);
      MessagePlugin.success('已删除指标点');
      confirmDlg.destroy(); // 销毁对话框
    },
    onClose: ({ trigger, e }) => {
      confirmDlg.destroy(); // 确保对话框被销毁
    }
  });
};

// 更新要求标题
const updateRequirementTitle = (index: number, title: string) => {
  requirements.value[index].title = title
}

// 更新要求描述
const updateRequirementDescription = (index: number, description: string) => {
  requirements.value[index].description = description
}

// 更新子要求内容
const updateSubRequirement = (
  reqIndex: number,
  subIndex: number,
  content: string
) => {
  requirements.value[reqIndex].subRequirements[subIndex].content = content
}

// 导入/导出相关
const showImportDialog = ref(false)
const importDataText = ref('')

const exportData = () => {}

const importData = () => {
  showImportDialog.value = true
}

const confirmImport = () => {}

// 初始化
onMounted(() => {
  // 可以在这里添加初始化逻辑，如从服务器加载数据
})
</script>

<style lang="less" scoped>
.graduation-requirements-container {
  --header-padding: 24px;
  --card-spacing: 16px;
  --section-spacing: 24px;
  --item-spacing: 12px;
  --border-radius: 8px;
  --transition-duration: 0.2s;

  padding: var(--header-padding);
  max-width: 1200px;
  margin: 0 auto;
  color: var(--td-text-color-primary);
  font-family: var(--td-font-family);
  line-height: 1.5;
}

/* 头部区域优化 */
.header-section {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  gap: var(--card-spacing);
  margin-bottom: var(--section-spacing);
  padding-bottom: var(--card-spacing);
  border-bottom: 1px solid var(--td-component-stroke);

  .header-content {
    flex: 1;
    min-width: 300px;

    .title-wrapper {
      display: flex;
      flex-wrap: wrap;
      align-items: center;
      gap: var(--item-spacing);
      margin-bottom: var(--item-spacing);

      .page-title {
        margin: 0;
        font-size: 28px;
        font-weight: 600;
        line-height: 1.3;
        outline: none;
        min-width: 200px;
        flex:1;

        &[contenteditable="true"] {
          padding: 8px 12px;
          border-radius: var(--border-radius);
          background: var(--td-bg-color-component);
          outline: 1px dashed rgba(var(--td-brand-color-rgb), 0.3);
          transition: all var(--transition-duration);

          &:focus {
            outline: 2px solid rgba(var(--td-brand-color-rgb), 0.5);
            box-shadow: 0 0 0 2px rgba(var(--td-brand-color-rgb), 0.1);
          }
        }
      }

      .grade-select {
        width: 200px;
        flex-shrink: 0;
      }
    }

    .page-subtitle {
      margin: 0;
      font-size: 14px;
      color: var(--td-text-color-secondary);
    }
  }

  .header-actions {
    .edit-btn {
      min-width: 120px;
    }
  }
}

/* 统计卡片优化 */
.stats-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--card-spacing);
  margin-bottom: var(--section-spacing);

  .stat-card {
    border-radius: var(--border-radius);
    transition: transform var(--transition-duration), box-shadow var(--transition-duration);
    will-change: transform;

    &:hover {
      transform: translateY(-4px);
      box-shadow: var(--td-shadow-3);
    }

    .stat-content {
      display: flex;
      align-items: center;
      padding: 16px;

      .stat-icon {
        width: 48px;
        height: 48px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 16px;
        font-size: 24px;
        flex-shrink: 0;
        transition: all var(--transition-duration);

        &.total-icon {
          background: rgba(var(--td-brand-color-rgb), 0.1);
          color: var(--td-brand-color);
        }

        &.sub-icon {
          background: rgba(var(--td-success-color-rgb), 0.1);
          color: var(--td-success-color);
        }

        &.recent-icon {
          background: rgba(var(--td-purple-color-rgb), 0.1);
          color: var(--td-purple-color);
        }
      }

      .stat-info {
        .stat-value {
          font-size: 24px;
          font-weight: 600;
          line-height: 1.2;
          margin-bottom: 4px;
        }

        .stat-label {
          font-size: 14px;
          color: var(--td-text-color-secondary);
        }
      }
    }
  }
}

/* 工具栏优化 */
.toolbar {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: center;
  gap: var(--card-spacing);
  margin-bottom: var(--section-spacing);

  .t-space {
    display: flex;
    flex-wrap: wrap;
    gap: var(--item-spacing);
  }

  .search-input {
    width: 280px;
    min-width: 100%;

    @media (min-width: 480px) {
      min-width: auto;
    }
  }
}

/* 毕业要求列表优化 */
.requirements-list {
  display: flex;
  flex-direction: column;
  gap: var(--card-spacing);

  .requirement-item {
    background: var(--td-bg-color-container);
    border-radius: var(--border-radius);
    box-shadow: var(--td-shadow-1);
    transition: all var(--transition-duration);
    overflow: hidden;
    will-change: transform, box-shadow;

    &:hover {
      box-shadow: var(--td-shadow-2);
    }

    &.editing-item {
      border-left: 4px solid var(--td-brand-color);
    }

    .requirement-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 24px;
      cursor: pointer;
      user-select: none;
      background: var(--td-bg-color-container);
      transition: background-color var(--transition-duration);

      &:hover {
        background-color: var(--td-bg-color-container-hover);
      }

      .requirement-meta {
        display: flex;
        align-items: center;
        gap: var(--card-spacing);
        min-width: 0;

        .requirement-number {
          width: 32px;
          height: 32px;
          background: var(--td-brand-color);
          color: white;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          font-weight: 600;
          flex-shrink: 0;
        }

        .requirement-title {
          font-size: 18px;
          font-weight: 600;
          flex: 1;
          min-width: 0;
          overflow: hidden;
          text-overflow: ellipsis;
          outline: none;

          &[contenteditable="true"] {
            padding: 8px 12px;
            border-radius: var(--border-radius);
            background: var(--td-bg-color-component);
            outline: 1px dashed rgba(var(--td-brand-color-rgb), 0.3);
            transition: all var(--transition-duration);

            &:focus {
              outline: 2px solid rgba(var(--td-brand-color-rgb), 0.5);
              box-shadow: 0 0 0 2px rgba(var(--td-brand-color-rgb), 0.1);
            }
          }
        }
      }

      .requirement-actions {
        display: flex;
        gap: 8px;
        flex-shrink: 0;

        .action-btn {
          transition: all var(--transition-duration);
          will-change: transform;

          &:hover {
            transform: scale(1.1);
          }

          &.delete-btn {
            color: var(--td-error-color);
          }

          &.toggle-btn {
            transition: transform var(--transition-duration);

            &.rotated {
              transform: rotate(-180deg);
            }
          }
        }
      }
    }

    .requirement-content {
      padding: 0 24px 24px 72px;
      border-top: 1px solid var(--td-component-stroke);

      .description-section {
        margin-bottom: var(--section-spacing);

        .section-label {
          font-size: 14px;
          color: var(--td-text-color-secondary);
          margin-bottom: 8px;
          font-weight: 500;
        }

        .description-content {
          line-height: 1.8;
          color: var(--td-text-color-primary);
          outline: none;

          &[contenteditable="true"] {
            padding: 12px 16px;
            border-radius: var(--border-radius);
            background: var(--td-bg-color-component);
            outline: 1px dashed rgba(var(--td-brand-color-rgb), 0.3);
            transition: all var(--transition-duration);

            &:focus {
              outline: 2px solid rgba(var(--td-brand-color-rgb), 0.5);
              box-shadow: 0 0 0 2px rgba(var(--td-brand-color-rgb), 0.1);
            }
          }
        }
      }

      .sub-requirements-section {
        .section-label {
          font-size: 14px;
          color: var(--td-text-color-secondary);
          margin-bottom: 8px;
          font-weight: 500;
        }

        .sub-requirements-list {
          display: flex;
          flex-direction: column;
          gap: var(--item-spacing);
          margin-bottom: 16px;
        }

        .sub-requirement-item {
          display: flex;
          justify-content: space-between;
          align-items: flex-start;
          padding: 12px 16px;
          background: var(--td-bg-color-secondarycontainer);
          border-radius: var(--border-radius);
          transition: all var(--transition-duration);

          &:hover {
            background: var(--td-bg-color-container-hover);
          }

          .sub-requirement-meta {
            display: flex;
            align-items: flex-start;
            gap: var(--item-spacing);
            min-width: 0;

            .sub-requirement-number {
              font-weight: 600;
              color: var(--td-brand-color);
              flex-shrink: 0;
              margin-top: 2px;
            }

            .sub-requirement-content {
              flex: 1;
              line-height: 1.6;
              min-width: 0;
              outline: none;

              &[contenteditable="true"] {
                padding: 8px 12px;
                border-radius: var(--border-radius);
                background: var(--td-bg-color-component);
                outline: 1px dashed rgba(var(--td-brand-color-rgb), 0.3);
                transition: all var(--transition-duration);

                &:focus {
                  outline: 2px solid rgba(var(--td-brand-color-rgb), 0.5);
                  box-shadow: 0 0 0 2px rgba(var(--td-brand-color-rgb), 0.1);
                }
              }
            }
          }

          .delete-sub-btn {
            color: var(--td-error-color);
            flex-shrink: 0;
          }
        }

        .add-sub-btn {
          margin-top: 8px;
        }
      }
    }
  }
}

/* 空状态优化 */
.empty-state {
  margin: 40px 0;
  text-align: center;

  .t-result {
    max-width: 500px;
    margin: 0 auto;
  }
}

/* 导入对话框优化 */
.import-dialog-content {
  display: flex;
  flex-direction: column;
  gap: var(--card-spacing);

  .t-alert {
    margin-bottom: 8px;
  }

  .import-textarea {
    font-family: var(--td-font-family-mono);
    font-size: 14px;
    line-height: 1.5;
  }
}

/* 响应式设计优化 */
@media (max-width: 768px) {
  .header-section {
    flex-direction: column;

    .header-content {
      width: 100%;

      .title-wrapper {
        flex-direction: column;
        gap: var(--item-spacing);

        .page-title,
        .grade-select {
          width: 100%;
        }
      }
    }

    .header-actions {
      width: 100%;

      .edit-btn {
        width: 100%;
      }
    }
  }

  .toolbar {
    flex-direction: column;
    align-items: stretch;

    .t-space {
      justify-content: space-between;
    }

    .search-input {
      width: 100%;
    }
  }

  .requirement-item {
    .requirement-content {
      padding-left: 24px;
      padding-right: 24px;
    }
  }
}

@media (max-width: 480px) {
  .graduation-requirements-container {
    padding: 16px;
  }

  .stats-cards {
    grid-template-columns: 1fr;
  }

  .requirement-header {
    padding: 12px 16px !important;

    .requirement-meta {
      gap: 12px !important;

      .requirement-number {
        width: 28px !important;
        height: 28px !important;
        font-size: 14px;
      }

      .requirement-title {
        font-size: 16px !important;
      }
    }
  }

  .requirement-content {
    padding: 0 16px 16px 16px !important;
  }
}
</style>

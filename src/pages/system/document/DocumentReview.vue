<template>
  <div class="document-review-container">
    <t-card title="文件审核管理" bordered>
      <t-tabs value="pending" @change="handleTabChange">
        <t-tab-panel value="pending" label="待审核">
          <t-space direction="vertical" size="large" style="width: 100%">
            <t-table
              :data="pendingDocuments"
              :columns="pendingColumns"
              row-key="id"
              hover
              stripe
              :loading="tableLoading"
              :pagination="pagination"
              @row-click="handleRowClick"
            />
          </t-space>
        </t-tab-panel>
        <t-tab-panel value="processed" label="已审核">
          <t-space direction="vertical" size="large" style="width: 100%">
            <t-table
              :data="processedDocuments"
              :columns="processedColumns"
              row-key="id"
              hover
              stripe
              :loading="tableLoading"
              :pagination="pagination"
              @row-click="handleRowClick"
            />
          </t-space>
        </t-tab-panel>
      </t-tabs>
    </t-card>
    
    <!-- 文件详情对话框 -->
    <t-dialog
      v-model:visible="detailVisible"
      header="文件详情"
      :footer="currentStatus === 'pending' ? true : false"
      width="700px"
      :confirm-btn="{ content: '批准', theme: 'primary' }"
      :cancel-btn="{ content: '驳回', theme: 'danger' }"
      @confirm="approveDocument"
      @cancel="showRejectDialog"
    >
      <div class="document-detail">
        <t-loading :loading="detailLoading">
          <t-descriptions
            :data="documentDetailItems"
            :column="1"
            label-align="right"
            title="基本信息"
            bordered
          />
          
          <div class="document-preview mt-4">
            <div class="preview-header">
              <h3>文件预览</h3>
              <t-space>
                <t-button theme="primary" variant="text" @click="previewDocument">
                  <template #icon><t-icon name="view-module" /></template>
                  预览
                </t-button>
                <t-button theme="default" variant="text" @click="downloadDocument">
                  <template #icon><t-icon name="download" /></template>
                  下载
                </t-button>
              </t-space>
            </div>
            
            <div class="preview-content">
              <div class="preview-placeholder" v-if="!previewUrl">
                <t-icon name="file-excel" size="48px" />
                <p>点击"预览"按钮查看文件内容</p>
              </div>
              <iframe v-else :src="previewUrl" class="preview-frame"></iframe>
            </div>
          </div>
          
          <div class="review-comments mt-4" v-if="currentStatus === 'pending'">
            <h3>审核意见</h3>
            <t-textarea
              v-model="reviewComments"
              placeholder="请输入审核意见或建议..."
              :autosize="{ minRows: 3, maxRows: 5 }"
            />
          </div>
          
          <div class="review-history mt-4" v-if="reviewHistory.length > 0">
            <h3>审核记录</h3>
            <t-timeline>
              <t-timeline-item
                v-for="(item, index) in reviewHistory"
                :key="index"
                :label="item.time"
              >
                <template #dot>
                  <t-icon
                    :name="item.status === 'approved' ? 'check-circle-filled' : 'close-circle-filled'"
                    :style="{
                      color: item.status === 'approved' ? '#00a870' : '#e34d59'
                    }"
                  />
                </template>
                <div class="timeline-content">
                  <p class="reviewer">{{ item.reviewer }} ({{ item.role }})</p>
                  <p class="action">
                    {{ item.status === 'approved' ? '批准通过' : '驳回' }}
                  </p>
                  <p class="comments" v-if="item.comments">{{ item.comments }}</p>
                </div>
              </t-timeline-item>
            </t-timeline>
          </div>
        </t-loading>
      </div>
    </t-dialog>
    
    <!-- 驳回原因对话框 -->
    <t-dialog
      v-model:visible="rejectDialogVisible"
      header="驳回原因"
      width="500px"
      :confirm-btn="{ content: '确认驳回', theme: 'danger' }"
      :cancel-btn="{ content: '取消' }"
      @confirm="rejectDocument"
    >
      <t-form>
        <t-form-item label="驳回原因">
          <t-textarea
            v-model="rejectReason"
            placeholder="请输入驳回原因..."
            :autosize="{ minRows: 3, maxRows: 5 }"
          />
        </t-form-item>
      </t-form>
    </t-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue';
import {
  Card as TCard,
  Table as TTable,
  Space as TSpace,
  Button as TButton,
  Dialog as TDialog,
  Tabs as TTabs,
  TabPanel as TTabPanel,
  MessagePlugin,
  Loading as TLoading,
  Textarea as TTextarea,
  Form as TForm,
  FormItem as TFormItem,
  Descriptions as TDescriptions,
  Timeline as TTimeline,
  TimelineItem as TTimelineItem,
  Icon as TIcon
} from 'tdesign-vue-next';

// 表格加载状态
const tableLoading = ref(false);
const detailLoading = ref(false);

// 详情对话框
const detailVisible = ref(false);
const currentDocumentId = ref('');
const currentStatus = ref('');
const previewUrl = ref('');
const reviewComments = ref('');

// 驳回对话框
const rejectDialogVisible = ref(false);
const rejectReason = ref('');

// 当前活动的选项卡
const activeTab = ref('pending');

// 分页配置
const pagination = reactive({
  pageSize: 10,
  total: 0,
  current: 1,
  showTotal: true
});

// 待审核文件列
const pendingColumns = [
  { colKey: 'id', title: 'ID', width: 80 },
  { colKey: 'title', title: '文件标题', width: 200 },
  { colKey: 'type', title: '文件类型', width: 120 },
  { colKey: 'course', title: '适用课程', width: 150 },
  { colKey: 'teacher', title: '提交教师', width: 120 },
  { colKey: 'submitTime', title: '提交时间', width: 180 },
  { 
    colKey: 'operation', 
    title: '操作',
    width: 200,
    cell: (h, { row }) => {
      return [
        h(
          TButton,
          {
            theme: 'primary',
            variant: 'text',
            size: 'small',
            onClick: (e) => {
              e.stopPropagation();
              openDocumentDetail(row.id, 'pending');
            }
          },
          { default: () => '查看' }
        ),
        h(
          TButton,
          {
            theme: 'success',
            variant: 'text',
            size: 'small',
            onClick: (e) => {
              e.stopPropagation();
              quickApproveDocument(row.id);
            }
          },
          { default: () => '快速批准' }
        ),
        h(
          TButton,
          {
            theme: 'danger',
            variant: 'text',
            size: 'small',
            onClick: (e) => {
              e.stopPropagation();
              quickRejectDocument(row.id);
            }
          },
          { default: () => '驳回' }
        )
      ];
    }
  }
];

// 已处理文件列
const processedColumns = [
  { colKey: 'id', title: 'ID', width: 80 },
  { colKey: 'title', title: '文件标题', width: 200 },
  { colKey: 'type', title: '文件类型', width: 120 },
  { colKey: 'course', title: '适用课程', width: 150 },
  { colKey: 'teacher', title: '提交教师', width: 120 },
  { colKey: 'submitTime', title: '提交时间', width: 160 },
  { 
    colKey: 'status', 
    title: '状态',
    width: 120,
    cell: (h, { row }) => {
      const statusMap = {
        approved: { text: '已批准', color: '#00a870' },
        rejected: { text: '已驳回', color: '#e34d59' }
      };
      
      const status = statusMap[row.status] || { text: '未知', color: '#999' };
      
      return h(
        'span',
        {
          style: {
            color: status.color,
            padding: '2px 8px',
            backgroundColor: status.color === '#00a870' ? 'rgba(0, 168, 112, 0.1)' : 'rgba(227, 77, 89, 0.1)',
            borderRadius: '3px'
          }
        },
        status.text
      );
    }
  },
  { colKey: 'reviewTime', title: '审核时间', width: 160 },
  { 
    colKey: 'operation',
    title: '操作',
    width: 100,
    cell: (h, { row }) => {
      return h(
        TButton,
        {
          theme: 'primary',
          variant: 'text',
          size: 'small',
          onClick: (e) => {
            e.stopPropagation();
            openDocumentDetail(row.id, row.status);
          }
        },
        { default: () => '查看' }
      );
    }
  }
];

// 待审核文件数据
const pendingDocuments = ref([
  {
    id: '2001',
    title: '数据结构教学大纲2024版',
    type: '课程大纲',
    course: '数据结构',
    teacher: '张三',
    submitTime: '2024-04-20 14:30:22'
  },
  {
    id: '2002',
    title: '计算机网络课程教案',
    type: '教学计划',
    course: '计算机网络',
    teacher: '李四',
    submitTime: '2024-04-18 09:15:36'
  },
  {
    id: '2003',
    title: '操作系统实验指导',
    type: '教学材料',
    course: '操作系统',
    teacher: '王五',
    submitTime: '2024-04-16 16:42:19'
  }
]);

// 已处理文件数据
const processedDocuments = ref([
  {
    id: '2004',
    title: '编译原理期末考试方案',
    type: '考核方案',
    course: '编译原理',
    teacher: '赵六',
    submitTime: '2024-04-10 11:28:45',
    status: 'approved',
    reviewTime: '2024-04-12 09:15:20'
  },
  {
    id: '2005',
    title: '软件工程课程标准',
    type: '课程大纲',
    course: '软件工程',
    teacher: '钱七',
    submitTime: '2024-04-08 15:36:42',
    status: 'rejected',
    reviewTime: '2024-04-09 14:26:38'
  },
  {
    id: '2006',
    title: '人工智能导论教学计划',
    type: '教学计划',
    course: '人工智能导论',
    teacher: '孙八',
    submitTime: '2024-04-05 10:12:56',
    status: 'approved',
    reviewTime: '2024-04-07 11:30:15'
  }
]);

// 文件详情项
const documentDetailItems = ref([]);

// 审核历史记录
const reviewHistory = ref([]);

// 处理选项卡切换
const handleTabChange = (value) => {
  activeTab.value = value;
};

// 处理行点击
const handleRowClick = (row) => {
  openDocumentDetail(row.id, activeTab.value === 'pending' ? 'pending' : row.status);
};

// 打开文件详情
const openDocumentDetail = (id, status) => {
  currentDocumentId.value = id;
  currentStatus.value = status;
  detailVisible.value = true;
  detailLoading.value = true;
  reviewComments.value = '';
  previewUrl.value = '';
  
  // 模拟加载详情数据
  setTimeout(() => {
    // 模拟详情数据
    const isProcessed = status !== 'pending';
    
    // 获取文档详情
    const doc = isProcessed 
      ? processedDocuments.value.find(item => item.id === id)
      : pendingDocuments.value.find(item => item.id === id);
    
    if (doc) {
      documentDetailItems.value = [
        { label: '文件ID', content: doc.id },
        { label: '文件标题', content: doc.title },
        { label: '文件类型', content: doc.type },
        { label: '适用课程', content: doc.course },
        { label: '提交教师', content: doc.teacher },
        { label: '提交时间', content: doc.submitTime },
        ...(isProcessed ? [
          { label: '状态', content: doc.status === 'approved' ? '已批准' : '已驳回' },
          { label: '审核时间', content: doc.reviewTime }
        ] : [])
      ];
      
      // 模拟审核历史
      if (isProcessed) {
        reviewHistory.value = [
          {
            time: doc.reviewTime,
            reviewer: '李主任',
            role: '院长',
            status: doc.status,
            comments: doc.status === 'approved' 
              ? '文档符合教学要求，批准通过。' 
              : '文档格式有误，请修改后重新提交。'
          }
        ];
      } else {
        reviewHistory.value = [];
      }
    }
    
    detailLoading.value = false;
  }, 800);
};

// 批准文档
const approveDocument = () => {
  if (!reviewComments.value.trim()) {
    MessagePlugin.warning('请输入审核意见');
    return;
  }
  
  // 模拟处理逻辑
  const doc = pendingDocuments.value.find(item => item.id === currentDocumentId.value);
  if (doc) {
    const now = new Date();
    const formattedDate = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}-${String(now.getDate()).padStart(2, '0')} ${String(now.getHours()).padStart(2, '0')}:${String(now.getMinutes()).padStart(2, '0')}:${String(now.getSeconds()).padStart(2, '0')}`;
    
    // 添加到已处理列表
    processedDocuments.value.unshift({
      ...doc,
      status: 'approved',
      reviewTime: formattedDate
    });
    
    // 从待处理列表移除
    pendingDocuments.value = pendingDocuments.value.filter(item => item.id !== currentDocumentId.value);
    
    MessagePlugin.success('文件已批准，已发送给学院管理员进行最终审核');
    detailVisible.value = false;
  }
};

// 显示驳回对话框
const showRejectDialog = () => {
  rejectDialogVisible.value = true;
  rejectReason.value = reviewComments.value;
};

// 驳回文档
const rejectDocument = () => {
  if (!rejectReason.value.trim()) {
    MessagePlugin.warning('请输入驳回原因');
    return;
  }
  
  // 模拟处理逻辑
  const doc = pendingDocuments.value.find(item => item.id === currentDocumentId.value);
  if (doc) {
    const now = new Date();
    const formattedDate = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}-${String(now.getDate()).padStart(2, '0')} ${String(now.getHours()).padStart(2, '0')}:${String(now.getMinutes()).padStart(2, '0')}:${String(now.getSeconds()).padStart(2, '0')}`;
    
    // 添加到已处理列表
    processedDocuments.value.unshift({
      ...doc,
      status: 'rejected',
      reviewTime: formattedDate
    });
    
    // 从待处理列表移除
    pendingDocuments.value = pendingDocuments.value.filter(item => item.id !== currentDocumentId.value);
    
    MessagePlugin.success('已驳回文件，已通知提交教师');
    rejectDialogVisible.value = false;
    detailVisible.value = false;
  }
};

// 快速批准文档
const quickApproveDocument = (id) => {
  MessagePlugin.success(`已批准文件ID: ${id}`);
  
  // 模拟处理逻辑
  const doc = pendingDocuments.value.find(item => item.id === id);
  if (doc) {
    const now = new Date();
    const formattedDate = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}-${String(now.getDate()).padStart(2, '0')} ${String(now.getHours()).padStart(2, '0')}:${String(now.getMinutes()).padStart(2, '0')}:${String(now.getSeconds()).padStart(2, '0')}`;
    
    // 添加到已处理列表
    processedDocuments.value.unshift({
      ...doc,
      status: 'approved',
      reviewTime: formattedDate
    });
    
    // 从待处理列表移除
    pendingDocuments.value = pendingDocuments.value.filter(item => item.id !== id);
  }
};

// 快速驳回文档
const quickRejectDocument = (id) => {
  currentDocumentId.value = id;
  rejectDialogVisible.value = true;
  rejectReason.value = '';
};

// 预览文档
const previewDocument = () => {
  // 模拟预览URL
  previewUrl.value = 'https://mozilla.github.io/pdf.js/web/viewer.html';
  MessagePlugin.success('正在加载文件预览');
};

// 下载文档
const downloadDocument = () => {
  // 实际下载逻辑
  MessagePlugin.success('文件下载中...');
};

// 获取数据
onMounted(() => {
  tableLoading.value = true;
  
  // 模拟加载数据
  setTimeout(() => {
    pagination.total = pendingDocuments.value.length;
    tableLoading.value = false;
  }, 1000);
});
</script>

<style lang="less" scoped>
.document-review-container {
  padding: 20px;
}

.document-detail {
  padding: 10px;
}

.document-preview {
  margin-top: 24px;
  border: 1px solid #e7e7e7;
  border-radius: 6px;
  overflow: hidden;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #e7e7e7;
  background-color: #f9f9f9;
  
  h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 500;
  }
}

.preview-content {
  height: 300px;
  overflow: auto;
  background-color: #f5f5f5;
}

.preview-placeholder {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: #999;
  
  p {
    margin-top: 16px;
  }
}

.preview-frame {
  width: 100%;
  height: 100%;
  border: none;
}

.review-comments, .review-history {
  margin-top: 24px;
  
  h3 {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 16px;
  }
}

.timeline-content {
  margin-bottom: 16px;
  
  .reviewer {
    font-weight: 500;
    margin: 0 0 4px 0;
  }
  
  .action {
    color: #666;
    margin: 0 0 4px 0;
  }
  
  .comments {
    background-color: #f5f5f5;
    padding: 8px 12px;
    border-radius: 4px;
    margin: 8px 0 0 0;
    color: #333;
    font-size: 14px;
  }
}

.mt-4 {
  margin-top: 16px;
}
</style> 
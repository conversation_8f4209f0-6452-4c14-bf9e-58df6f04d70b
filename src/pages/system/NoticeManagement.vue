
<template>
  <div class="notice-center-container">
    <t-card title="通知中心">
      <template #actions>
        <t-button theme="primary" variant="text">
          <template #icon><t-icon name="check-circle" /></template>
          全部标记为已读
        </t-button>
      </template>

      <t-tabs default-value="all">
        <t-tab-panel value="all" label="全部通知">
          <t-loading :loading="loading">
            <t-table
              :data="data"
              :columns="columns"
              row-key="id"
              hover
              stripe
              :pagination="pagination"
            >
              <template #isRead="{ row }">
                <t-tag :theme="row.isRead ? 'default' : 'primary'" variant="light-outline">
                  {{ row.isRead ? '已读' : '未读' }}
                </t-tag>
              </template>

              <template #title="{ row }">
                <div class="notice-title" :class="{ 'notice-unread': !row.isRead }">{{ row.title }}</div>
              </template>

              <template #content="{ row }">
                <div class="notice-content">{{ row.content }}</div>
              </template>

              <template #type="{ row }">
                <t-tag :theme="getTypeTagTheme(row.type)">{{ row.type }}</t-tag>
              </template>

              <template #operation="{ row }">
                <t-space>
                  <t-tooltip content="查看">
                    <t-button size="small" shape="circle" variant="text" theme="primary" @click="viewNotice(row)">
                      <template #icon><t-icon name="browse" /></template>
                    </t-button>
                  </t-tooltip>
                  <t-tooltip content="标记为已读">
                    <t-button
                      size="small"
                      shape="circle"
                      variant="text"
                      theme="success"
                      :disabled="row.isRead"
                      @click="markAsRead(row)"
                    >
                      <template #icon><t-icon name="check-circle" /></template>
                    </t-button>
                  </t-tooltip>
                </t-space>
              </template>
            </t-table>
          </t-loading>
        </t-tab-panel>

        <t-tab-panel value="unread" label="未读通知">
          <div class="empty-tab-content">此标签页内容待实现</div>
        </t-tab-panel>

        <t-tab-panel value="system" label="系统通知">
          <div class="empty-tab-content">此标签页内容待实现</div>
        </t-tab-panel>

        <t-tab-panel value="work" label="工作通知">
          <div class="empty-tab-content">此标签页内容待实现</div>
        </t-tab-panel>
      </t-tabs>
    </t-card>
  </div>
</template>
<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { MessagePlugin } from 'tdesign-vue-next';

interface NoticeItem {
  id: string;
  title: string;
  content: string;
  type: string;
  status: string;
  createTime: string;
  isRead: boolean;
}

// 表格数据
const data = ref<NoticeItem[]>([]);
const loading = ref(false);

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  pageSizeOptions: [10, 20, 50],
  showJumper: true,
  showPageSize: true,
  onChange: (pageInfo: any) => {
    pagination.current = pageInfo.current;
    pagination.pageSize = pageInfo.pageSize;
    fetchNoticeList();
  },
});

// 模拟数据
const mockData: NoticeItem[] = [
  {
    id: 'notice_001',
    title: '系统更新通知',
    content: '系统将于今晚22:00-23:00进行例行维护，请各位用户提前做好准备。',
    type: '系统通知',
    status: '已发布',
    createTime: '2024-03-25 10:00:00',
    isRead: false,
  },
  {
    id: 'notice_002',
    title: '教师绩效考核开始',
    content: '2024年教师绩效考核即将开始，请各位教师在4月30日前完成相关资料提交。',
    type: '重要通知',
    status: '已发布',
    createTime: '2024-03-24 14:30:00',
    isRead: true,
  },
  {
    id: 'notice_003',
    title: '学生信息核对',
    content: '请各位辅导员组织学生在本周内完成学生信息核对工作，确保学生信息准确无误。',
    type: '工作通知',
    status: '已发布',
    createTime: '2024-03-23 09:15:00',
    isRead: false,
  },
  {
    id: 'notice_004',
    title: '教务系统功能更新',
    content: '教务系统新增成绩分析功能，可按院系、专业、课程等多维度进行成绩统计分析。',
    type: '系统通知',
    status: '已发布',
    createTime: '2024-03-22 16:45:00',
    isRead: false,
  },
  {
    id: 'notice_005',
    title: '教师资格认证',
    content: '2024年上半年教师资格认证工作即将开始，请有需要的教师关注相关通知。',
    type: '工作通知',
    status: '已发布',
    createTime: '2024-03-21 11:20:00',
    isRead: true,
  },
];

// 表格列配置
const columns = [
  { colKey: 'isRead', title: '状态', width: 80 },
  { colKey: 'title', title: '标题', width: 200 },
  { colKey: 'content', title: '内容' },
  { colKey: 'type', title: '类型', width: 120 },
  { colKey: 'createTime', title: '发布时间', width: 180 },
  { colKey: 'operation', title: '操作', width: 120, align: 'center' as const }
];

// 获取通知列表
const fetchNoticeList = () => {
  loading.value = true;

  try {
    // 模拟API请求
    setTimeout(() => {
      const start = (pagination.current - 1) * pagination.pageSize;
      const end = start + pagination.pageSize;
      data.value = mockData.slice(start, end);
      pagination.total = mockData.length;
      loading.value = false;
    }, 500);
  } catch (error) {
    console.error('获取通知列表失败:', error);
    MessagePlugin.error('获取通知列表失败');
    loading.value = false;
  }
};

// 查看通知详情
const viewNotice = (row: NoticeItem) => {
  // 标记为已读
  if (!row.isRead) {
    const index = mockData.findIndex(item => item.id === row.id);
    if (index > -1) {
      mockData[index].isRead = true;
      // 当前视图数据也需要更新
      const dataIndex = data.value.findIndex(item => item.id === row.id);
      if (dataIndex > -1) {
        data.value[dataIndex].isRead = true;
      }
    }
  }

  // 显示详情对话框
  MessagePlugin.info(`查看通知: ${row.title}`);
};

// 标记为已读
const markAsRead = (row: NoticeItem) => {
  if (!row.isRead) {
    const index = mockData.findIndex(item => item.id === row.id);
    if (index > -1) {
      mockData[index].isRead = true;
      // 当前视图数据也需要更新
      const dataIndex = data.value.findIndex(item => item.id === row.id);
      if (dataIndex > -1) {
        data.value[dataIndex].isRead = true;
      }

      MessagePlugin.success('已标记为已读');
    }
  } else {
    MessagePlugin.info('该通知已读');
  }
};

// 初始化
onMounted(() => {
  fetchNoticeList();
});
</script>

<script lang="ts">
// 辅助函数，放在setup外部
function getTypeTagTheme(type: string): "default" | "primary" | "warning" | "danger" | "success" {
  switch (type) {
    case '系统通知':
      return 'primary';
    case '重要通知':
      return 'danger';
    case '工作通知':
      return 'warning';
    default:
      return 'default';
  }
}
</script>

<style scoped lang="less">
.notice-center-container {
  padding: 20px;
  background-color: var(--td-bg-color-container);
  border-radius: var(--td-radius-medium);

  .notice-title {
    font-weight: normal;

    &.notice-unread {
      font-weight: bold;
    }
  }

  .notice-content {
    color: var(--td-text-color-secondary);
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .empty-tab-content {
    padding: 40px 0;
    text-align: center;
    color: var(--td-text-color-secondary);
  }
}
</style>

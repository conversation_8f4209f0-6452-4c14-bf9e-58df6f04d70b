<template>
  <div class="user-management">
    <t-card>
      <template #header>
        <t-space>
          <t-button theme="primary" @click="handleAdd">
            <template #icon><plus-icon /></template>
            新增用户
          </t-button>
          <t-button
            theme="danger"
            variant="outline"
            :disabled="selectedRowKeys.length === 0"
            @click="handleBatchDelete"
          >
            <template #icon><delete-icon /></template>
            批量删除
          </t-button>
          <t-button theme="default" variant="outline" @click="handleImportClick">
            <template #icon><upload-icon /></template>
            导入用户
          </t-button>
        </t-space>
      </template>

      <!-- 搜索表单 -->
      <t-form :data="searchForm" layout="inline" @submit="handleSearch" @reset="handleReset">
        <t-form-item label="用户名" name="username">
          <t-input v-model="searchForm.username" placeholder="请输入用户名" clearable />
        </t-form-item>
        <t-form-item label="姓名" name="realName">
          <t-input v-model="searchForm.realName" placeholder="请输入姓名" clearable />
        </t-form-item>
        <t-form-item label="手机号" name="phone">
          <t-input v-model="searchForm.phone" placeholder="请输入手机号" clearable />
        </t-form-item>
        <t-form-item label="邮箱" name="email">
          <t-input v-model="searchForm.email" placeholder="请输入邮箱" clearable />
        </t-form-item>
        <t-form-item label="状态" name="status">
          <t-select v-model="searchForm.status" placeholder="请选择状态" clearable>
            <t-option :value="0" label="正常" />
            <t-option :value="99" label="禁用" />
          </t-select>
        </t-form-item>
        <t-form-item>
          <t-space>
            <t-button theme="primary" type="submit">搜索</t-button>
            <t-button theme="default" type="reset">重置</t-button>
          </t-space>
        </t-form-item>
      </t-form>

      <t-table
        :columns="columns"
        :data="userList"
        :loading="loading"
        :pagination="pagination"
        :selected-row-keys="selectedRowKeys"
        row-key="id"
        @select-change="handleSelectChange"
        @page-change="handlePageChange"
      >
        <template #username="{ row }">
          {{ row.username || row.name || '-' }}
        </template>
        <template #name="{ row }">
          {{ row.name || row.realName || '-' }}
        </template>
        <template #status="{ row }">
          <t-tag :theme="row.status === 0 ? 'success' : 'danger'">
            {{ row.status === 0 ? '正常' : '禁用' }}
          </t-tag>
        </template>
        <template #roles="{ row }">
          <div style="display: flex; flex-wrap: wrap; gap: 4px;">
            <t-tag v-for="role in row.roles" :key="role.id" theme="primary" size="small">
              {{ role.name }}
            </t-tag>
            <span v-if="!row.roles || row.roles.length === 0" style="color: #999;">无</span>
          </div>
        </template>
        <template #action="{ row }">
          <t-space size="small">
            <t-button theme="primary" variant="text" size="small" @click="handleDetail(row)">详情</t-button>
            <t-button theme="primary" variant="text" size="small" @click="handleEdit(row)">编辑</t-button>
            <t-button theme="primary" variant="text" size="small" @click="handleAssignRoles(row)">分配角色</t-button>
            <t-button theme="warning" variant="text" size="small" @click="handleResetPassword(row)">重置密码</t-button>
            <t-button
              :theme="row.status === 0 ? 'danger' : 'success'"
              variant="text"
              size="small"
              @click="handleToggleStatus(row)"
            >
              {{ row.status === 0 ? '禁用' : '启用' }}
            </t-button>
            <t-popconfirm
              content="确定要删除该用户吗？"
              @confirm="handleDelete(row)"
            >
              <t-button theme="danger" variant="text" size="small">删除</t-button>
            </t-popconfirm>
          </t-space>
        </template>
      </t-table>
    </t-card>

    <!-- 用户表单对话框 -->
    <t-dialog
      v-model:visible="modalVisible"
      :header="modalTitle"
      :width="680"
      :footer="false"
    >
      <t-form
        ref="formRef"
        :data="formState"
        :rules="rules"
        :label-width="100"
        @submit="handleSubmit"
      >
        <t-form-item label="用户名" name="username">
          <t-input v-model="formState.username" placeholder="请输入用户名" />
        </t-form-item>
        <t-form-item label="姓名" name="name">
          <t-input v-model="formState.name" placeholder="请输入姓名" />
        </t-form-item>
        <t-form-item label="密码" name="password" v-if="!formState.id">
          <t-input
            v-model="formState.password"
            type="password"
            placeholder="请输入密码"
          />
        </t-form-item>
        <t-form-item label="邮箱" name="email">
          <t-input v-model="formState.email" placeholder="请输入邮箱" />
        </t-form-item>
        <t-form-item label="手机号" name="phone">
          <t-input v-model="formState.phone" placeholder="请输入手机号" />
        </t-form-item>
        <t-form-item label="性别" name="gender">
          <t-radio-group v-model="formState.gender">
            <t-radio :value="1">男</t-radio>
            <t-radio :value="2">女</t-radio>
            <t-radio :value="0">保密</t-radio>
          </t-radio-group>
        </t-form-item>
        <t-form-item>
          <t-space>
            <t-button theme="primary" type="submit">确定</t-button>
            <t-button theme="default" @click="modalVisible = false">取消</t-button>
          </t-space>
        </t-form-item>
      </t-form>
    </t-dialog>

    <!-- 分配角色对话框 -->
    <t-dialog
      v-model:visible="roleModalVisible"
      header="分配角色"
      :width="680"
      :footer="false"
    >
      <t-form
        ref="roleFormRef"
        :data="roleFormState"
        :label-width="100"
        @submit="handleRoleSubmit"
      >
        <t-form-item label="角色">
          <t-checkbox-group v-model="roleFormState.roleIds">
            <t-checkbox
              v-for="role in roleList"
              :key="role.id"
              :value="role.id"
            >
              {{ role.name }}
            </t-checkbox>
          </t-checkbox-group>
        </t-form-item>
        <t-form-item>
          <t-space>
            <t-button theme="primary" type="submit">确定</t-button>
            <t-button theme="default" @click="roleModalVisible = false">取消</t-button>
          </t-space>
        </t-form-item>
      </t-form>
    </t-dialog>

    <!-- 用户详情对话框 -->
    <t-dialog
      v-model:visible="detailModalVisible"
      header="用户详情"
      :width="680"
      :footer="false"
    >
      <t-descriptions :data="detailData" />
    </t-dialog>

    <!-- 导入用户对话框 -->
    <t-dialog
      v-model:visible="importModalVisible"
      header="导入用户"
      :width="600"
      :footer="false"
    >
      <div class="import-content">
        <div class="import-tips">
          <t-alert theme="info" message="请按照模板格式填写用户信息，支持批量导入" />
        </div>

        <div class="template-download">
          <t-link theme="primary" @click="downloadTemplate">
            <template #prefixIcon><download-icon /></template>
            下载导入模板
          </t-link>
        </div>

        <div class="upload-area">
          <t-upload
            v-model="uploadFiles"
            :show-upload-list="true"
            :before-upload="beforeUpload"
            @remove="handleFileRemove"
            drag
            accept=".xlsx,.xls"
            :multiple="false"
            :auto-upload="false"
            ref="uploadRef"
          >
            <div class="upload-dragger">
              <upload-icon size="48px" style="color: #0052d9;" />
              <div class="upload-text">
                <div class="upload-tip">点击上传或将文件拖拽到此区域</div>
                <div class="upload-hint">支持 .xlsx、.xls 格式文件</div>
              </div>
            </div>
          </t-upload>
        </div>

        <div class="upload-progress" v-if="uploadProgress > 0 && uploadProgress < 100">
          <t-progress :percentage="uploadProgress" :label="true" />
          <div class="progress-text">正在上传... {{ uploadProgress }}%</div>
        </div>

        <div class="import-actions">
          <t-space>
            <t-button
              theme="primary"
              @click="handleStartUpload"
              :disabled="uploadFiles.length === 0 || uploading"
              :loading="uploading"
            >
              开始导入
            </t-button>
            <t-button theme="default" @click="handleImportCancel">取消</t-button>
          </t-space>
        </div>
      </div>
    </t-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, computed, getCurrentInstance } from 'vue';
import { PlusIcon, DeleteIcon, UploadIcon, DownloadIcon } from 'tdesign-icons-vue-next';
import type { FormInstanceFunctions, FormRule, SubmitContext, PaginationProps, UploadInstanceFunctions } from 'tdesign-vue-next';
import {
  getUserList,
  addUser,
  updateUser,
  deleteUser,
  getUserDetail,
  assignUserRoles,
  resetUserPassword,
  batchDeleteUsers,
  toggleUserStatus,
  importUsers,
  type User,
  type Role,
  type UserQuery,
  type UserForm
} from '@/api/system/baseUser';
import { getRoleList } from '@/api/system/role';
import { getToken } from '@/utils/token';
import * as XLSX from 'xlsx';

const columns = [
  {
    title: '用户名',
    colKey: 'username',
    width: 120,
    ellipsis: true,
  },
  {
    title: '姓名',
    colKey: 'name',
    width: 100,
    ellipsis: true,
  },
  {
    title: '邮箱',
    colKey: 'email',
    width: 180,
    ellipsis: true,
  },
  {
    title: '手机号',
    colKey: 'phone',
    width: 120,
  },
  {
    title: '角色',
    colKey: 'roles',
    width: 150,
  },
  {
    title: '状态',
    colKey: 'status',
    width: 80,
  },
  {
    title: '操作',
    colKey: 'action',
    width: 380,
    fixed: 'right' as const,
  },
];

const userList = ref<User[]>([]);
const roleList = ref<Role[]>([]);
const loading = ref(false);
const modalVisible = ref(false);
const roleModalVisible = ref(false);
const detailModalVisible = ref(false);
const importModalVisible = ref(false);
const modalTitle = ref('新增用户');
const currentUser = ref<User | null>(null);
const selectedRowKeys = ref<number[]>([]);

// 搜索表单
const searchForm = reactive({
  username: '',
  realName: '',
  phone: '',
  email: '',
  status: undefined as number | undefined,
});

// 分页配置
const pagination = ref<PaginationProps>({
  current: 1,
  pageSize: 10,
  total: 0,
  showJumper: true,
  showPageSize: true,
});

// 移除原有的上传配置，改为手动上传

const formRef = ref<FormInstanceFunctions>();
const formState = reactive<UserForm>({
  id: undefined,
  username: '',
  name: '',
  password: '',
  email: '',
  phone: '',
  status: true,
  gender: 0,
});

const roleFormRef = ref<FormInstanceFunctions>();
const roleFormState = reactive({
  userId: null as number | null,
  roleIds: [] as number[],
});

const detailData = ref<any[]>([]);

// 导入相关状态
const uploadFiles = ref<any[]>([]);
const uploadProgress = ref(0);
const uploading = ref(false);
const uploadRef = ref<UploadInstanceFunctions>();

const { proxy } = getCurrentInstance();

const rules: Record<string, FormRule[]> = {
  username: [
    { required: true, message: '请输入用户名', type: 'error' as const },
    { min: 3, max: 20, message: '用户名长度在3-20个字符之间', type: 'error' as const },
  ],
  name: [
    { required: true, message: '请输入姓名', type: 'error' as const },
  ],
  password: [
    { required: true, message: '请输入密码', type: 'error' as const },
    { min: 6, message: '密码长度不能小于6个字符', type: 'error' as const },
  ],
  email: [
    { required: true, message: '请输入邮箱', type: 'error' as const },
    {
      validator: (val: string) => /^[\w-]+(\.[\w-]+)*@[\w-]+(\.[\w-]+)+$/.test(val),
      message: '请输入正确的邮箱地址',
      type: 'error' as const
    },
  ],
  phone: [
    { required: true, message: '请输入手机号', type: 'error' as const },
    {
      validator: (val: string) => /^1[3-9]\d{9}$/.test(val),
      message: '请输入正确的手机号',
      type: 'error' as const
    },
  ],
  gender: [
    { required: true, message: '请选择性别', type: 'error' as const },
  ],
};

// 获取用户列表
const fetchUserList = async () => {
  loading.value = true;
  try {
    const query: UserQuery = {
      page: {
        current: pagination.value.current,
        size: pagination.value.pageSize,
      },
      ...searchForm,
    };

    const response = await getUserList(query);
    userList.value = response.data.records;
    pagination.value.total = response.data.total;
  } catch (error) {
    console.log('获取用户列表失败', error);
  } finally {
    loading.value = false;
  }
};

// 获取角色列表
const fetchRoleList = async () => {
  try {
    const response = await getRoleList({
      page: { current: 1, size: 100 }
    });
    roleList.value = response.data.records;
  } catch (error) {
    console.log('获取角色列表失败', error);
  }
};

// 搜索
const handleSearch = () => {
  pagination.value.current = 1;
  fetchUserList();
};

// 重置搜索
const handleReset = () => {
  Object.assign(searchForm, {
    username: '',
    realName: '',
    phone: '',
    email: '',
    status: undefined,
  });
  pagination.value.current = 1;
  fetchUserList();
};

// 分页变化
const handlePageChange = (pageInfo: any) => {
  pagination.value.current = pageInfo.current;
  pagination.value.pageSize = pageInfo.pageSize;
  fetchUserList();
};

// 选择变化
const handleSelectChange = (keys: (string | number)[]) => {
  selectedRowKeys.value = keys.map(key => Number(key));
};

// 新增用户
const handleAdd = () => {
  modalTitle.value = '新增用户';
  modalVisible.value = true;
  Object.assign(formState, {
    id: undefined,
    username: '',
    name: '',
    password: '',
    email: '',
    phone: '',
    status: true,
    gender: 0,
  });
};

// 编辑用户
const handleEdit = (record: User) => {
  modalTitle.value = '编辑用户';
  modalVisible.value = true;
  currentUser.value = record;
  Object.assign(formState, {
    id: record.id,
    username: record.username,
    name: record.name,
    email: record.email,
    phone: record.phone,
    status: record.status,
    gender: record.gender || 0,
  });
};

// 用户详情
const handleDetail = async (record: User) => {
  try {
    const response = await getUserDetail(record.id);
    const user = response.data;
    detailData.value = [
      { label: '用户ID', value: user.id },
      { label: '用户名', value: user.username },
      { label: '姓名', value: user.name },
      { label: '邮箱', value: user.email },
      { label: '手机号', value: user.phone },
      { label: '性别', value: user.gender === 1 ? '男' : user.gender === 2 ? '女' : '保密' },
      { label: '状态', value: user.status === 0 ? '正常' : '禁用' },
      { label: '角色', value: user.roles?.map((r: any) => r.name).join(', ') || '无' },
    ];
    detailModalVisible.value = true;
  } catch (error) {
    console.log('获取用户详情失败', error);
  }
};

// 分配角色
const handleAssignRoles = (record: User) => {
  currentUser.value = record;
  roleModalVisible.value = true;
  roleFormState.userId = record.id;
  roleFormState.roleIds = record.roles?.map(role => role.id) || [];
};

// 重置密码
const handleResetPassword = async (record: User) => {
  try {
    await resetUserPassword(record.id);
    proxy.$baseMessage('密码重置成功，默认密码为：123456', 'success');
  } catch (error) {
    console.log('重置密码失败', error);
  }
};

// 切换用户状态
const handleToggleStatus = async (record: User) => {
  try {
    await toggleUserStatus(record.id);
    proxy.$baseMessage(`${record.status === 0 ? '禁用' : '启用'}成功`, 'success');
    fetchUserList();
  } catch (error) {
    console.log(`${record.status === 0 ? '禁用' : '启用'}失败`, error);
  }
};

// 删除用户
const handleDelete = async (record: User) => {
  try {
    await deleteUser(record.id);
    proxy.$baseMessage('删除成功', 'success');
    fetchUserList();
  } catch (error) {
    console.log('删除失败', error);
  }
};

// 批量删除
const handleBatchDelete = async () => {
  if (selectedRowKeys.value.length === 0) {
    proxy.$baseMessage('请选择要删除的用户', 'warning');
    return;
  }

  try {
    await batchDeleteUsers({ ids: selectedRowKeys.value });
    proxy.$baseMessage('批量删除成功', 'success');
    selectedRowKeys.value = [];
    fetchUserList();
  } catch (error) {
    console.log('批量删除失败', error);
  }
};

// 提交用户表单
const handleSubmit = async ({ validateResult }: SubmitContext) => {
  if (validateResult === true) {
    try {
      const isEdit = !!formState.id;
      isEdit ? await updateUser(formState) : await addUser(formState);
      proxy.$baseMessage(`${isEdit ? '更新' : '新增'}成功`, 'success');
      modalVisible.value = false;
      fetchUserList();
    } catch (error) {
      console.log(`${formState.id ? '更新' : '新增'}失败`, error);
    }
  }
};

// 提交角色分配
const handleRoleSubmit = async ({ validateResult }: SubmitContext) => {
  if (validateResult === true) {
    try {
      await assignUserRoles({
        userId: roleFormState.userId!,
        roleIds: roleFormState.roleIds,
      });
      proxy.$baseMessage('角色分配成功', 'success');
      roleModalVisible.value = false;
      fetchUserList();
    } catch (error) {
      console.log('角色分配失败', error);
    }
  }
};

// 上传前验证
const beforeUpload = (file: any) => {
  const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
                  file.type === 'application/vnd.ms-excel';
  if (!isExcel) {
    proxy.$baseMessage('只能上传Excel文件', 'error');
    return false;
  }
  return true;
};

// 上传进度处理
const handleUploadProgress = (progressEvent: any) => {
  uploadProgress.value = Math.round((progressEvent.loaded / progressEvent.total) * 100);
};

// 文件移除
const handleFileRemove = () => {
  uploadFiles.value = [];
  uploadProgress.value = 0;
};

// 开始上传 - 改为手动上传
const handleStartUpload = async () => {
  if (uploadFiles.value.length === 0) {
    proxy.$baseMessage('请选择要上传的文件', 'warning');
    return;
  }

  const file = uploadFiles.value[0].raw || uploadFiles.value[0];
  if (!file) {
    proxy.$baseMessage('文件获取失败', 'error');
    return;
  }

  uploading.value = true;
  uploadProgress.value = 0;

  try {
    await importUsers(file, handleUploadProgress);
    uploadProgress.value = 100;
    proxy.$baseMessage('导入成功', 'success');
    fetchUserList();
    handleImportCancel();
  } catch (error: any) {
    console.log('导入失败', error);
  } finally {
    uploading.value = false;
  }
};

// 导入点击
const handleImportClick = () => {
  importModalVisible.value = true;
};

// 下载模板
const downloadTemplate = () => {
  // 创建模板数据
  const templateData = [
    ['姓名', '邮箱', '手机号', '性别'],
  ];

  // 创建工作簿
  const ws = XLSX.utils.aoa_to_sheet(templateData);
  const wb = XLSX.utils.book_new();
  XLSX.utils.book_append_sheet(wb, ws, '用户基本信息导入模板');

  // 下载文件
  XLSX.writeFile(wb, '用户基本信息导入模板.xlsx');
};

// 取消导入
const handleImportCancel = () => {
  importModalVisible.value = false;
  uploadFiles.value = [];
  uploadProgress.value = 0;
  uploading.value = false;
};

onMounted(() => {
  fetchUserList();
  fetchRoleList();
});
</script>

<style scoped>
.user-management {
  padding: 24px;
}

.import-content {
  padding: 16px 0;
}

.import-tips {
  margin-bottom: 16px;
}

.template-download {
  margin-bottom: 24px;
  text-align: center;
}

.upload-area {
  margin-bottom: 16px;
  :deep(.t-upload__trigger){
    width: 100%;
  }
}

.upload-dragger {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  border: 2px dashed #d0d7de;
  border-radius: 6px;
  background-color: #f6f8fa;
  transition: all 0.3s ease;
}

.upload-dragger:hover {
  border-color: #0052d9;
  background-color: #f0f5ff;
}

.upload-text {
  margin-top: 16px;
  text-align: center;
}

.upload-tip {
  font-size: 16px;
  color: #1d2129;
  margin-bottom: 8px;
}

.upload-hint {
  font-size: 14px;
  color: #86909c;
}

.upload-progress {
  margin-bottom: 16px;
}

.progress-text {
  text-align: center;
  margin-top: 8px;
  font-size: 14px;
  color: #0052d9;
}

.import-actions {
  text-align: center;
  margin-top: 24px;
}
</style>

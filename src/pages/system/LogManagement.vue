<template>
  <div class="log-management-container">
    <t-card title="操作日志管理" bordered>
      <!-- 查询区域 -->
      <t-form :data="searchForm" labelWidth="80px" :colon="true" layout="inline">
        <t-form-item label="用户名">
          <t-input v-model="searchForm.username" placeholder="请输入用户名" clearable />
        </t-form-item>

        <t-form-item label="时间范围">
          <t-date-range-picker
            v-model="searchForm.dateRange"
            mode="date"
            :placeholder="['开始日期', '结束日期']"
            clearable
          />
        </t-form-item>

        <t-form-item label="操作类型">
          <t-select
            v-model="searchForm.operationType"
            :options="operationTypeOptions"
            placeholder="请选择操作类型"
            clearable
          />
        </t-form-item>

        <t-form-item label="操作模块">
          <t-input v-model="searchForm.module" placeholder="请输入操作模块" clearable />
        </t-form-item>

        <t-form-item label="操作结果">
          <t-select
            v-model="searchForm.status"
            :options="operationResultOptions"
            placeholder="请选择操作结果"
            clearable
          />
        </t-form-item>

        <t-form-item>
          <t-space>
            <t-button theme="primary" @click="handleSearch">查询</t-button>
            <t-button theme="default" @click="resetSearch">重置</t-button>
            <t-button theme="default" @click="exportLog">导出</t-button>
          </t-space>
        </t-form-item>
      </t-form>

      <!-- 表格区域 -->
      <t-table
        :data="data"
        :columns="columns"
        :pagination="pagination"
        :loading="loading"
        stripe
        hover
        row-key="id"
        style="margin-top: 16px;"
      >
        <template #serial-number="{ rowIndex }">
          {{ (pagination.current - 1) * pagination.pageSize + rowIndex + 1 }}
        </template>

        <template #operationType="{ row }">
          <t-tag
            :theme="getOperationTypeTheme(row.operationType)"
            variant="light"
          >
            {{ row.operationType }}
          </t-tag>
        </template>

        <template #status="{ row }">
          <t-tag :theme="row.status === 1 ? 'success' : 'danger'" variant="light">
            {{ row.status === 1 ? '成功' : '失败' }}
          </t-tag>
        </template>

        <template #operation="{ row }">
          <t-space>
            <t-button theme="primary" variant="text" size="small" @click="viewDetail(row)">
              详情
            </t-button>
            <t-button theme="danger" variant="text" size="small" @click="handleDelete([row.id])">
              删除
            </t-button>
          </t-space>
        </template>
      </t-table>
    </t-card>

    <!-- 日志详情对话框 -->
    <t-dialog
      v-model:visible="detailVisible"
      header="操作日志详情"
      :width="700"
      :footer="false"
    >
      <template v-if="currentLog">
        <t-descriptions bordered size="medium">
          <t-descriptions-item label="操作用户">{{ currentLog.username }}</t-descriptions-item>
          <t-descriptions-item label="操作类型">{{ currentLog.operationType }}</t-descriptions-item>
          <t-descriptions-item label="操作模块">{{ currentLog.module }}</t-descriptions-item>
          <t-descriptions-item label="操作描述">{{ currentLog.description }}</t-descriptions-item>
          <t-descriptions-item label="操作状态">
            <t-tag :theme="currentLog.status === 1 ? 'success' : 'danger'">
              {{ currentLog.status === 1 ? '成功' : '失败' }}
            </t-tag>
          </t-descriptions-item>
          <t-descriptions-item label="IP地址">{{ currentLog.ip }}</t-descriptions-item>
          <t-descriptions-item label="位置">{{ currentLog.location }}</t-descriptions-item>
          <t-descriptions-item label="浏览器">{{ currentLog.browser }}</t-descriptions-item>
          <t-descriptions-item label="操作系统">{{ currentLog.os }}</t-descriptions-item>
          <t-descriptions-item label="操作时间">{{ currentLog.operationTime }}</t-descriptions-item>
          <t-descriptions-item label="耗时">{{ currentLog.costTime }}ms</t-descriptions-item>
          <t-descriptions-item label="请求地址">{{ currentLog.requestUrl }}</t-descriptions-item>
          <t-descriptions-item label="请求方法">{{ currentLog.requestMethod }}</t-descriptions-item>
          <t-descriptions-item label="请求参数" :span="2">
            <pre style="white-space: pre-wrap; word-break: break-all;">{{ formatJson(currentLog.requestParams) }}</pre>
          </t-descriptions-item>
          <t-descriptions-item label="响应结果" :span="2">
            <pre style="white-space: pre-wrap; word-break: break-all;">{{ formatJson(currentLog.responseResult) }}</pre>
          </t-descriptions-item>
          <t-descriptions-item v-if="currentLog.errorMsg" label="错误信息" :span="2">
            <pre style="white-space: pre-wrap; color: #e34d59;">{{ currentLog.errorMsg }}</pre>
          </t-descriptions-item>
        </t-descriptions>
      </template>
    </t-dialog>
  </div>
</template>
<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { MessagePlugin, DialogPlugin } from 'tdesign-vue-next';
import { getLogList, getLogDetail, deleteLog, exportLog } from '@/api/system/log';

// 查询条件
const searchForm = reactive({
  username: '',
  dateRange: [] as string[],
  operationType: '',
  module: '',
  status: null as number | null
});

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  pageSizeOptions: [10, 20, 50],
  showJumper: true,
  showPageSize: true,
  onChange: (pageInfo: any) => {
    pagination.current = pageInfo.current;
    pagination.pageSize = pageInfo.pageSize;
    fetchLogList();
  },
});

// 操作类型选项
const operationTypeOptions = ref([
  { label: '全部', value: '' },
  { label: '新增', value: '新增' },
  { label: '修改', value: '修改' },
  { label: '删除', value: '删除' },
  { label: '查询', value: '查询' },
  { label: '导入', value: '导入' },
  { label: '导出', value: '导出' },
  { label: '登录', value: '登录' },
  { label: '登出', value: '登出' }
]);

// 操作结果选项
const operationResultOptions = ref([
  { label: '全部', value: null },
  { label: '成功', value: 1 },
  { label: '失败', value: 0 }
]);

// 表格数据
const data = ref([]);
const loading = ref(false);

// 日志详情
const detailVisible = ref(false);
const currentLog = ref(null);

// 表格列配置
const columns = [
  { colKey: 'serial-number', title: '序号', width: 70 },
  { colKey: 'username', title: '用户名', width: 120 },
  { colKey: 'operationType', title: '操作类型', width: 100, cell: 'operationType' },
  { colKey: 'module', title: '操作模块', width: 120 },
  { colKey: 'description', title: '操作描述', width: 200 },
  { colKey: 'ip', title: 'IP地址', width: 120 },
  { colKey: 'status', title: '操作结果', width: 100, cell: 'status' },
  { colKey: 'operationTime', title: '操作时间', width: 180 },
  { colKey: 'costTime', title: '耗时(ms)', width: 100 },
  { colKey: 'operation', title: '操作', width: 120, fixed: 'right', cell: 'operation' }
];

// 获取操作类型对应的主题色
const getOperationTypeTheme = (type: string) => {
  switch (type) {
    case '新增':
      return 'success';
    case '修改':
      return 'primary';
    case '删除':
      return 'danger';
    case '查询':
      return 'default';
    case '导入':
      return 'warning';
    case '导出':
      return 'warning';
    case '登录':
      return 'success';
    case '登出':
      return 'warning';
    default:
      return 'default';
  }
};

// 获取日志列表
const fetchLogList = async () => {
  loading.value = true;
  try {
    const params = {
      current: pagination.current,
      pageSize: pagination.pageSize,
      username: searchForm.username || undefined,
      operationType: searchForm.operationType || undefined,
      module: searchForm.module || undefined,
      status: searchForm.status,
      startTime: searchForm.dateRange[0] || undefined,
      endTime: searchForm.dateRange[1] || undefined
    };

    const response = await getLogList(params);
    if (response.code === 200) {
      data.value = response.data.list;
      pagination.total = response.data.total;
    } else {
      MessagePlugin.error(`获取日志列表失败：${response.message}`);
    }
  } catch (error) {
    console.error('获取日志列表失败:', error);
    MessagePlugin.error('获取日志列表失败');
  } finally {
    loading.value = false;
  }
};

// 查看日志详情
const viewDetail = async (row: any) => {
  try {
    loading.value = true;
    const response = await getLogDetail(row.id);
    if (response.code === 200) {
      currentLog.value = response.data;
      detailVisible.value = true;
    } else {
      MessagePlugin.error(`获取日志详情失败：${response.message}`);
    }
  } catch (error) {
    console.error('获取日志详情失败:', error);
    MessagePlugin.error('获取日志详情失败');
  } finally {
    loading.value = false;
  }
};

// 删除日志
const handleDelete = (ids: number[]) => {
  DialogPlugin.confirm({
    header: '确认删除',
    body: `确定要删除选中的${ids.length}条日志记录吗？`,
    onConfirm: async () => {
      try {
        loading.value = true;
        const response = await deleteLog(ids);
        if (response.code === 200) {
          MessagePlugin.success('删除成功');
          fetchLogList();
        } else {
          MessagePlugin.error(`删除失败：${response.message}`);
        }
      } catch (error) {
        console.error('删除日志失败:', error);
        MessagePlugin.error('删除日志失败');
      } finally {
        loading.value = false;
      }
    }
  });
};

// 重置查询条件
const resetSearch = () => {
  searchForm.username = '';
  searchForm.dateRange = [];
  searchForm.operationType = '';
  searchForm.module = '';
  searchForm.status = null;

  pagination.current = 1;
  fetchLogList();
};

// 执行查询
const handleSearch = () => {
  pagination.current = 1;
  fetchLogList();
};

// 导出日志
const exportLog = async () => {
  try {
    loading.value = true;
    const params = {
      username: searchForm.username || undefined,
      operationType: searchForm.operationType || undefined,
      module: searchForm.module || undefined,
      status: searchForm.status,
      startTime: searchForm.dateRange[0] || undefined,
      endTime: searchForm.dateRange[1] || undefined
    };

    const response = await exportLog(params);
    
    // 处理文件下载
    const blob = new Blob([response], { type: 'application/vnd.ms-excel' });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `操作日志导出_${new Date().toISOString().split('T')[0]}.csv`;
    link.click();
    window.URL.revokeObjectURL(url);
    
    MessagePlugin.success('日志导出成功');
  } catch (error) {
    console.error('导出日志失败:', error);
    MessagePlugin.error('导出日志失败');
  } finally {
    loading.value = false;
  }
};

// 格式化JSON字符串
const formatJson = (jsonStr: string) => {
  if (!jsonStr) return '';
  try {
    const obj = JSON.parse(jsonStr);
    return JSON.stringify(obj, null, 2);
  } catch (e) {
    return jsonStr;
  }
};

// 初始化
onMounted(() => {
  fetchLogList();
});
</script>

<style scoped lang="less">
.log-management-container {
  padding: 20px;
  background-color: var(--td-bg-color-container);
  border-radius: var(--td-radius-medium);

  :deep(.t-card__body) {
    padding-top: 16px;
  }

  :deep(.t-form) {
    margin-bottom: 16px;
  }
}
</style>

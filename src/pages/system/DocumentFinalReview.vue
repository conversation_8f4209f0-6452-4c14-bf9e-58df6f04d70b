<template>
  <div class="document-final-review-container">
    <t-card title="文件最终审核与发布" bordered>
      <template #actions>
        <t-select v-model="filterStatus" @change="handleFilterChange" class="filter-select">
          <t-option value="all" label="全部状态" />
          <t-option value="pending" label="待审核" />
          <t-option value="approved" label="已发布" />
          <t-option value="rejected" label="已驳回" />
        </t-select>
      </template>
      
      <t-space direction="vertical" size="large" style="width: 100%">
        <t-alert
          v-if="pendingCount > 0"
          theme="warning"
          message="待审核提示"
          :description="`您有 ${pendingCount} 个文件需要进行最终审核和发布`"
          icon
          close
        />
        
        <t-table
          :data="filteredDocuments"
          :columns="columns"
          row-key="id"
          hover
          stripe
          :loading="tableLoading"
          :pagination="pagination"
        />
      </t-space>
    </t-card>
    
    <!-- 文件详情对话框 -->
    <t-dialog
      v-model:visible="detailVisible"
      header="文件最终审核"
      :footer="currentDocument?.status === 'pending' ? true : false"
      width="800px"
      :confirm-btn="{ content: '批准并发布', theme: 'primary' }"
      :cancel-btn="{ content: '驳回', theme: 'danger' }"
      @confirm="approveAndPublish"
      @cancel="showRejectDialog"
    >
      <div class="document-detail">
        <t-loading :loading="detailLoading">
          <div class="status-timeline">
            <t-steps :current="getStepIndex()" :status="getStepStatus()">
              <t-step-item title="教师提交" content="文件已由教师提交" />
              <t-step-item title="院长审核" :content="getDirectorContent()" />
              <t-step-item title="最终审核与发布" content="学院管理员审核并发布" />
            </t-steps>
          </div>
          
          <t-divider></t-divider>
          
          <div class="two-column-layout">
            <div class="left-column">
              <t-descriptions
                :data="documentBasicItems"
                :column="1"
                label-align="right"
                title="基本信息"
                bordered
                size="medium"
              />
              
              <div class="approval-section" v-if="approvalHistory.length > 0">
                <h3>院长审核意见</h3>
                <div class="approval-content">
                  <div class="approval-info">
                    <div class="approval-header">
                      <span class="approver-name">{{ approvalHistory[0].reviewer }}</span>
                      <span class="approval-time">{{ approvalHistory[0].time }}</span>
                    </div>
                    <div class="approval-status" :class="approvalHistory[0].status === 'approved' ? 'status-approved' : 'status-rejected'">
                      {{ approvalHistory[0].status === 'approved' ? '批准' : '驳回' }}
                    </div>
                  </div>
                  <div class="approval-comments">
                    {{ approvalHistory[0].comments || '无审核意见' }}
                  </div>
                </div>
              </div>
              
              <div class="review-section" v-if="currentDocument?.status === 'pending'">
                <h3>最终审核意见</h3>
                <t-textarea
                  v-model="finalReviewComments"
                  placeholder="请输入最终审核意见或发布说明..."
                  :autosize="{ minRows: 3, maxRows: 5 }"
                />
                
                <h3 class="mt-4">发布设置</h3>
                <t-form>
                  <t-form-item label="发布范围">
                    <t-radio-group v-model="publishScope">
                      <t-radio value="department">仅本学院</t-radio>
                      <t-radio value="school">全校</t-radio>
                    </t-radio-group>
                  </t-form-item>
                  <t-form-item label="发布时间">
                    <t-date-picker
                      v-model="publishDate"
                      mode="date"
                      :defaultValue="new Date()"
                      format="YYYY-MM-DD"
                      enable-time-picker
                    />
                  </t-form-item>
                  <t-form-item label="是否通知">
                    <t-switch v-model="notifyUsers" size="large" />
                  </t-form-item>
                </t-form>
              </div>
            </div>
            
            <div class="right-column">
              <div class="document-preview">
                <div class="preview-header">
                  <h3>文件预览</h3>
                  <t-space>
                    <t-button theme="primary" variant="text" @click="previewDocument">
                      <template #icon><t-icon name="preview-open" /></template>
                      预览
                    </t-button>
                    <t-button theme="primary" variant="text" @click="enlargeDocument">
                      <template #icon><t-icon name="zoom-in" /></template>
                      放大
                    </t-button>
                    <t-button theme="default" variant="text" @click="downloadDocument">
                      <template #icon><t-icon name="download" /></template>
                      下载
                    </t-button>
                  </t-space>
                </div>
                
                <div class="preview-content">
                  <div class="preview-placeholder" v-if="!previewUrl">
                    <t-icon name="file-copy" size="48px" />
                    <p>点击"预览"按钮查看文件内容</p>
                  </div>
                  
                  <!-- PDF文件预览 -->
                  <iframe 
                    v-else-if="getFileType(previewUrl) === 'pdf'" 
                    :src="previewUrl"
                    class="preview-frame" 
                    frameborder="0"
                  ></iframe>
                  
                  <!-- Office文档预览 -->
                  <iframe 
                    v-else-if="getFileType(previewUrl) === 'doc' || getFileType(previewUrl) === 'docx'" 
                    :src="previewUrl"
                    class="preview-frame" 
                    frameborder="0"
                  ></iframe>
                  
                  <!-- 图片预览 -->
                  <div v-else-if="getFileType(previewUrl) === 'image'" class="image-preview">
                    <img :src="previewUrl" alt="文件预览" />
                  </div>
                  
                  <!-- 其他文件类型预览 -->
                  <div v-else class="unsupported-preview">
                    <t-icon name="file-unknown" size="48px" />
                    <p>此文件类型无法直接预览，请下载后查看</p>
                    <t-button theme="primary" @click="downloadDocument">下载文件</t-button>
                  </div>
                </div>
              </div>
              
              <div class="published-info" v-if="currentDocument?.status === 'approved'">
                <h3>发布信息</h3>
                <t-descriptions
                  :data="publishInfoItems"
                  :column="1"
                  label-align="right"
                  bordered
                  size="medium"
                />
              </div>
            </div>
          </div>
        </t-loading>
      </div>
    </t-dialog>
    
    <!-- 驳回对话框 -->
    <t-dialog
      v-model:visible="rejectDialogVisible"
      header="驳回原因"
      width="500px"
      :confirm-btn="{ content: '确认驳回', theme: 'danger' }"
      :cancel-btn="{ content: '取消' }"
      @confirm="rejectDocument"
    >
      <t-form>
        <t-form-item label="驳回原因">
          <t-textarea
            v-model="rejectReason"
            placeholder="请输入驳回原因..."
            :autosize="{ minRows: 3, maxRows: 5 }"
          />
        </t-form-item>
        <t-form-item label="通知提交人">
          <t-switch v-model="notifyReject" size="large" />
        </t-form-item>
      </t-form>
    </t-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue';
import {
  Card as TCard,
  Space as TSpace,
  Table as TTable,
  Button as TButton,
  Dialog as TDialog,
  Alert as TAlert,
  Loading as TLoading,
  Descriptions as TDescriptions,
  Form as TForm,
  FormItem as TFormItem,
  Textarea as TTextarea,
  Switch as TSwitch,
  RadioGroup as TRadioGroup,
  Radio as TRadio,
  DatePicker as TDatePicker,
  Steps as TSteps,
  StepItem as TStep,
  Divider as TDivider,
  Select as TSelect,
  Option as TOption,
  MessagePlugin,
  Icon as TIcon
} from 'tdesign-vue-next';

// 加载状态
const tableLoading = ref(false);
const detailLoading = ref(false);

// 对话框可见性
const detailVisible = ref(false);
const rejectDialogVisible = ref(false);

// 预览和下载
const previewUrl = ref('');

// 详情对话框
const currentDocument = ref(null);
const finalReviewComments = ref('');

// 发布设置
const publishScope = ref('department');
const publishDate = ref(new Date());
const notifyUsers = ref(true);

// 驳回对话框
const rejectReason = ref('');
const notifyReject = ref(true);

// 筛选
const filterStatus = ref('all');

// 分页配置
const pagination = reactive({
  pageSize: 10,
  total: 0,
  current: 1,
  showTotal: true
});

// 院长审核历史
const approvalHistory = ref([]);

// 表格列定义
const columns = [
  { colKey: 'id', title: 'ID', width: 80 },
  { colKey: 'title', title: '文件标题', width: 180 },
  { colKey: 'type', title: '文件类型', width: 120 },
  { colKey: 'course', title: '适用课程', width: 120 },
  { colKey: 'teacher', title: '提交教师', width: 100 },
  { colKey: 'submitTime', title: '提交时间', width: 150 },
  { colKey: 'directorApproveTime', title: '院长审核时间', width: 150 },
  { 
    colKey: 'status', 
    title: '状态', 
    width: 100,
    cell: (h, { row }) => {
      const statusMap = {
        pending: { text: '待审核', color: '#ed7b2f' },
        approved: { text: '已发布', color: '#00a870' },
        rejected: { text: '已驳回', color: '#e34d59' }
      };
      
      const status = statusMap[row.status] || { text: '未知', color: '#999' };
      
      return h(
        'span',
        {
          style: {
            color: status.color,
            padding: '2px 8px',
            backgroundColor: `${status.color}15`,
            borderRadius: '3px'
          }
        },
        status.text
      );
    }
  },
  { 
    colKey: 'operation', 
    title: '操作',
    width: 200,
    cell: (h, { row }) => {
      let buttons = [
        h(
          TButton,
          {
            theme: 'primary',
            variant: 'text',
            size: 'small',
            onClick: () => openDocumentDetail(row)
          },
          { default: () => '查看' }
        )
      ];
      
      if (row.status === 'pending') {
        buttons = [
          ...buttons,
          h(
            TButton,
            {
              theme: 'success',
              variant: 'text',
              size: 'small',
              onClick: () => quickApproveDocument(row)
            },
            { default: () => '发布' }
          ),
          h(
            TButton,
            {
              theme: 'danger',
              variant: 'text',
              size: 'small',
              onClick: () => quickRejectDocument(row)
            },
            { default: () => '驳回' }
          )
        ];
      } else if (row.status === 'approved') {
        buttons.push(
          h(
            TButton,
            {
              theme: 'default',
              variant: 'text',
              size: 'small',
              onClick: () => revokeDocument(row)
            },
            { default: () => '撤回' }
          )
        );
      }
      
      return buttons;
    }
  }
];

// 模拟数据
const documents = ref([
  {
    id: '3001',
    title: '数据结构教学大纲2024版',
    type: '课程大纲',
    course: '数据结构',
    teacher: '张三',
    submitTime: '2024-04-20 14:30:22',
    directorApproveTime: '2024-04-21 09:45:18',
    directorComments: '大纲内容详实，符合教学要求，批准通过。',
    status: 'pending',
    approver: '李主任',
    directorStatus: 'approved',
    fileUrl: 'https://mozilla.github.io/pdf.js/web/compressed.tracemonkey-pldi-09.pdf'
  },
  {
    id: '3002',
    title: '计算机网络课程教案',
    type: '教学计划',
    course: '计算机网络',
    teacher: '李四',
    submitTime: '2024-04-18 09:15:36',
    directorApproveTime: '2024-04-19 11:20:45',
    directorComments: '教案结构清晰，教学目标明确，批准通过。',
    status: 'pending',
    approver: '王主任',
    directorStatus: 'approved',
    fileUrl: 'https://view.officeapps.live.com/op/view.aspx?src=https://file-examples.com/wp-content/uploads/2017/02/file-sample_100kB.doc'
  },
  {
    id: '3003',
    title: '操作系统实验指导',
    type: '教学材料',
    course: '操作系统',
    teacher: '王五',
    submitTime: '2024-04-16 16:42:19',
    directorApproveTime: '2024-04-17 10:15:36',
    directorComments: '实验指导全面，步骤清晰，批准通过。',
    status: 'approved',
    approver: '李主任',
    directorStatus: 'approved',
    publishTime: '2024-04-18 09:30:22',
    publishScope: 'department',
    publisher: '赵管理员',
    fileUrl: 'https://view.officeapps.live.com/op/view.aspx?src=https://file-examples.com/wp-content/uploads/2017/02/file-sample_500kB.docx'
  },
  {
    id: '3004',
    title: '人工智能期末考核方案',
    type: '考核方案',
    course: '人工智能',
    teacher: '赵六',
    submitTime: '2024-04-14 14:25:38',
    directorApproveTime: '2024-04-15 16:10:20',
    directorComments: '考核方案合理，难度适中，批准通过。',
    status: 'rejected',
    approver: '王主任',
    directorStatus: 'approved',
    rejectReason: '考核方案需要调整试卷结构，请修改后重新提交。',
    rejectTime: '2024-04-16 11:45:30',
    rejector: '赵管理员',
    fileUrl: 'https://cdn.pixabay.com/photo/2017/08/05/11/16/logo-2582748_640.png'
  },
  {
    id: '3005',
    title: '大数据分析课件',
    type: '教学材料',
    course: '大数据分析',
    teacher: '钱七',
    submitTime: '2024-04-13 10:30:15',
    directorApproveTime: '2024-04-14 13:25:40',
    directorComments: '课件内容丰富，示例详尽，批准通过。',
    status: 'pending',
    approver: '李主任',
    directorStatus: 'approved',
    fileUrl: 'https://file-examples.com/storage/fe2eb8762a7a064c49a184a/2017/10/file-example_PDF_500_kB.pdf'
  }
]);

// 根据筛选条件过滤文档
const filteredDocuments = computed(() => {
  if (filterStatus.value === 'all') {
    return documents.value;
  }
  return documents.value.filter(doc => doc.status === filterStatus.value);
});

// 待审核文档数量
const pendingCount = computed(() => {
  return documents.value.filter(doc => doc.status === 'pending').length;
});

// 文档基本信息
const documentBasicItems = ref([]);

// 发布信息
const publishInfoItems = ref([]);

// 处理筛选变化
const handleFilterChange = (value) => {
  filterStatus.value = value;
};

// 打开文档详情
const openDocumentDetail = (doc) => {
  currentDocument.value = doc;
  detailVisible.value = true;
  detailLoading.value = true;
  previewUrl.value = '';
  finalReviewComments.value = '';
  
  // 重置发布设置为默认值
  publishScope.value = 'department';
  publishDate.value = new Date();
  notifyUsers.value = true;
  
  // 模拟加载详情数据
  setTimeout(() => {
    documentBasicItems.value = [
      { label: '文件ID', content: doc.id },
      { label: '文件标题', content: doc.title },
      { label: '文件类型', content: doc.type },
      { label: '适用课程', content: doc.course },
      { label: '提交教师', content: doc.teacher },
      { label: '提交时间', content: doc.submitTime },
      { label: '院长审核时间', content: doc.directorApproveTime },
      { label: '院长审核人', content: doc.approver }
    ];
    
    // 如果是已发布状态，设置发布信息
    if (doc.status === 'approved') {
      publishInfoItems.value = [
        { label: '发布时间', content: doc.publishTime },
        { label: '发布范围', content: doc.publishScope === 'department' ? '仅本学院' : '全校' },
        { label: '发布人', content: doc.publisher },
        { label: '发布说明', content: doc.publishComments || '无' }
      ];
    }
    
    // 设置院长审核历史
    approvalHistory.value = [
      {
        reviewer: doc.approver,
        role: '院长',
        time: doc.directorApproveTime,
        status: doc.directorStatus,
        comments: doc.directorComments
      }
    ];
    
    detailLoading.value = false;
  }, 800);
};

// 快速批准并发布文档
const quickApproveDocument = (doc) => {
  currentDocument.value = doc;
  finalReviewComments.value = '文件内容符合要求，同意发布。';
  publishScope.value = 'department';
  publishDate.value = new Date();
  notifyUsers.value = true;
  
  // 显示确认对话框
  MessagePlugin.confirm({
    content: `确认要发布文件"${doc.title}"吗？`,
    confirmBtn: '确认发布',
    cancelBtn: '取消',
    onConfirm: () => {
      approveAndPublish();
    }
  });
};

// 快速驳回文档
const quickRejectDocument = (doc) => {
  currentDocument.value = doc;
  rejectDialogVisible.value = true;
  rejectReason.value = '';
};

// 批准并发布文档
const approveAndPublish = () => {
  if (!finalReviewComments.value.trim()) {
    MessagePlugin.warning('请输入审核意见');
    return;
  }
  
  // 模拟文档发布
  if (currentDocument.value) {
    const now = new Date();
    const formattedDate = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}-${String(now.getDate()).padStart(2, '0')} ${String(now.getHours()).padStart(2, '0')}:${String(now.getMinutes()).padStart(2, '0')}:${String(now.getSeconds()).padStart(2, '0')}`;
    
    // 更新文档状态
    const docIndex = documents.value.findIndex(doc => doc.id === currentDocument.value.id);
    if (docIndex !== -1) {
      documents.value[docIndex] = {
        ...documents.value[docIndex],
        status: 'approved',
        publishTime: formattedDate,
        publishScope: publishScope.value,
        publisher: '赵管理员',
        publishComments: finalReviewComments.value
      };
    }
    
    // 显示成功消息
    MessagePlugin.success('文件已成功发布');
    
    // 如果选择通知用户
    if (notifyUsers.value) {
      MessagePlugin.info('已向相关教师发送通知');
    }
    
    detailVisible.value = false;
  }
};

// 驳回文档
const rejectDocument = () => {
  if (!rejectReason.value.trim()) {
    MessagePlugin.warning('请输入驳回原因');
    return;
  }
  
  // 模拟文档驳回
  if (currentDocument.value) {
    const now = new Date();
    const formattedDate = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}-${String(now.getDate()).padStart(2, '0')} ${String(now.getHours()).padStart(2, '0')}:${String(now.getMinutes()).padStart(2, '0')}:${String(now.getSeconds()).padStart(2, '0')}`;
    
    // 更新文档状态
    const docIndex = documents.value.findIndex(doc => doc.id === currentDocument.value.id);
    if (docIndex !== -1) {
      documents.value[docIndex] = {
        ...documents.value[docIndex],
        status: 'rejected',
        rejectReason: rejectReason.value,
        rejectTime: formattedDate,
        rejector: '赵管理员'
      };
    }
    
    // 显示成功消息
    MessagePlugin.success('文件已驳回');
    
    // 如果选择通知提交人
    if (notifyReject.value) {
      MessagePlugin.info('已向提交教师发送驳回通知');
    }
    
    rejectDialogVisible.value = false;
    detailVisible.value = false;
  }
};

// 撤回已发布文档
const revokeDocument = (doc) => {
  MessagePlugin.confirm({
    content: `确认要撤回文件"${doc.title}"吗？撤回后文件将不再公开显示。`,
    confirmBtn: '确认撤回',
    cancelBtn: '取消',
    onConfirm: () => {
      // 模拟撤回操作
      const docIndex = documents.value.findIndex(item => item.id === doc.id);
      if (docIndex !== -1) {
        documents.value[docIndex] = {
          ...documents.value[docIndex],
          status: 'pending',
          revokeTime: new Date().toISOString().replace('T', ' ').substring(0, 19)
        };
      }
      
      MessagePlugin.success('文件已成功撤回');
    }
  });
};

// 下载文档
const downloadDocument = () => {
  if (!currentDocument.value || !currentDocument.value.fileUrl) {
    MessagePlugin.error('无法下载文件，文件不存在');
    return;
  }

  // 创建下载链接
  const link = document.createElement('a');
  link.href = currentDocument.value.fileUrl;
  link.download = currentDocument.value.title || `文档-${currentDocument.value.id}`;
  link.target = '_blank';
  
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  
  MessagePlugin.success('文件下载中...');
};

// 预览文档
const previewDocument = () => {
  if (!currentDocument.value || !currentDocument.value.fileUrl) {
    MessagePlugin.error('无法预览文件，文件不存在');
    return;
  }
  
  // 直接在预览区域显示文件
  const fileUrl = currentDocument.value.fileUrl;
  previewUrl.value = fileUrl;
  
  // 根据文件类型处理不同的预览方式
  const fileType = getFileType(fileUrl);
  if (fileType === 'pdf') {
    // PDF文件直接展示，无需处理
    MessagePlugin.success('PDF文件加载中...');
  } else if (fileType === 'doc' || fileType === 'docx') {
    // Word文档可能已经是Office Online Viewer的URL
    if (!fileUrl.includes('view.officeapps.live.com')) {
      // 如果不是，使用Office Online Viewer转换
      const origin = window.location.origin;
      const fullUrl = fileUrl.startsWith('http') 
        ? fileUrl 
        : `${origin}${fileUrl}`;
      
      previewUrl.value = `https://view.officeapps.live.com/op/view.aspx?src=${encodeURIComponent(fullUrl)}`;
    }
    MessagePlugin.success('Word文档加载中...');
  } else if (fileType === 'image') {
    // 图片文件直接显示
    MessagePlugin.success('图片加载中...');
  } else {
    // 其他类型文件，提示用户下载
    MessagePlugin.info('此类型文件无法直接预览，请下载后查看');
  }
};

// 在新窗口中放大预览文档
const enlargeDocument = () => {
  if (!currentDocument.value || !currentDocument.value.fileUrl) {
    MessagePlugin.error('无法预览文件，文件不存在');
    return;
  }
  
  const fileUrl = currentDocument.value.fileUrl;
  const fileType = getFileType(fileUrl);
  
  let previewFileUrl = fileUrl;
  
  // 针对不同文件类型处理预览URL
  if (fileType === 'doc' || fileType === 'docx') {
    // 确保Word文档使用Office Online Viewer
    if (!fileUrl.includes('view.officeapps.live.com')) {
      const origin = window.location.origin;
      const fullUrl = fileUrl.startsWith('http') 
        ? fileUrl 
        : `${origin}${fileUrl}`;
      
      previewFileUrl = `https://view.officeapps.live.com/op/view.aspx?src=${encodeURIComponent(fullUrl)}`;
    }
  }
  
  // 在新窗口中打开文件
  window.open(previewFileUrl, '_blank', 'noopener,noreferrer');
  MessagePlugin.success('在新窗口中打开文件预览');
};

// 获取审核步骤索引
const getStepIndex = () => {
  if (!currentDocument.value) return 0;
  
  const status = currentDocument.value.status;
  if (status === 'approved' || status === 'rejected') return 2;
  if (currentDocument.value.directorStatus === 'approved') return 1;
  return 0;
};

// 获取步骤状态
const getStepStatus = () => {
  if (!currentDocument.value) return '';
  
  const status = currentDocument.value.status;
  if (status === 'rejected') return 'error';
  if (status === 'approved') return 'finish';
  return 'process';
};

// 获取院长审核内容
const getDirectorContent = () => {
  if (!currentDocument.value) return '';
  
  const directorStatus = currentDocument.value.directorStatus;
  if (directorStatus === 'approved') return '院长已批准';
  if (directorStatus === 'rejected') return '院长已驳回';
  return '等待院长审核';
};

// 根据文件URL获取文件类型
const getFileType = (fileUrl: string): 'pdf' | 'doc' | 'docx' | 'image' | 'other' => {
  if (!fileUrl) return 'other';
  
  const extension = getFileExtension(fileUrl).replace('.', '').toLowerCase();
  
  if (['jpg', 'jpeg', 'png', 'gif', 'webp'].includes(extension)) {
    return 'image';
  } else if (extension === 'pdf') {
    return 'pdf';
  } else if (extension === 'doc') {
    return 'doc';
  } else if (extension === 'docx') {
    return 'docx';
  } else {
    return 'other';
  }
};

// 获取文件扩展名
const getFileExtension = (fileUrl: string): string => {
  if (!fileUrl) return '';
  const match = fileUrl.match(/\.([a-zA-Z0-9]+)(?:[\?#]|$)/);
  return match ? `.${match[1].toLowerCase()}` : '';
};

// 获取数据
onMounted(() => {
  tableLoading.value = true;
  
  // 模拟加载数据
  setTimeout(() => {
    pagination.total = documents.value.length;
    tableLoading.value = false;
  }, 1000);
});
</script>

<style lang="less" scoped>
.document-final-review-container {
  padding: 20px;
}

.filter-select {
  width: 150px;
}

.document-detail {
  padding: 10px;
}

.status-timeline {
  margin-bottom: 24px;
}

.two-column-layout {
  display: flex;
  gap: 24px;
  margin-top: 24px;
  
  .left-column {
    flex: 1;
  }
  
  .right-column {
    flex: 1;
  }
}

.approval-section, .review-section {
  margin-top: 24px;
  
  h3 {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 16px;
    color: #333;
  }
}

.approval-content {
  background-color: #f9f9f9;
  border-radius: 6px;
  overflow: hidden;
  border: 1px solid #e7e7e7;
}

.approval-info {
  display: flex;
  justify-content: space-between;
  padding: 12px 16px;
  background-color: #f3f3f3;
  border-bottom: 1px solid #e7e7e7;
}

.approval-header {
  display: flex;
  flex-direction: column;
}

.approver-name {
  font-weight: 500;
  color: #333;
}

.approval-time {
  font-size: 12px;
  color: #666;
  margin-top: 4px;
}

.approval-status {
  padding: 2px 8px;
  border-radius: 3px;
  font-size: 14px;
}

.status-approved {
  background-color: rgba(0, 168, 112, 0.1);
  color: #00a870;
}

.status-rejected {
  background-color: rgba(227, 77, 89, 0.1);
  color: #e34d59;
}

.approval-comments {
  padding: 16px;
  color: #333;
  line-height: 1.6;
}

.document-preview {
  border: 1px solid #e7e7e7;
  border-radius: 6px;
  overflow: hidden;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background-color: #f9f9f9;
  border-bottom: 1px solid #e7e7e7;
  
  h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 500;
  }
}

.preview-content {
  height: 400px;
  overflow: auto;
  background-color: #f5f5f5;
}

.preview-placeholder {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: #999;
  
  p {
    margin-top: 16px;
  }
}

.preview-frame {
  width: 100%;
  height: 100%;
  border: none;
}

.image-preview {
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #fff;
  
  img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
  }
}

.unsupported-preview {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: #999;
  
  p {
    margin: 16px 0;
  }
}

.published-info {
  margin-top: 24px;
  
  h3 {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 16px;
    color: #333;
  }
}

.mt-4 {
  margin-top: 16px;
}
</style> 

<template>
  <div class="dashboard-config-container">
    <t-card title="大屏配置管理" bordered>
      <template #actions>
        <t-button theme="default" @click="viewDashboard">
          <template #icon><t-icon name="view-module" /></template>
          查看大屏
        </t-button>
      </template>

      <t-alert
        theme="warning"
        message="此页面仅管理员可访问，修改配置后将影响所有用户的大屏展示"
        style="margin-bottom: 24px;"
      />

      <t-form>
        <t-form-item label="大屏展示URL" required>
          <t-input-adornment prepend="http://">
            <t-input
              v-model="dashboardUrl"
              placeholder="请输入大屏展示URL，如: *************:8080/dashboard"
              clearable
            />
          </t-input-adornment>
          <div class="form-tips">
            此URL将在系统首页直接展示，请确保URL可访问且配置正确
          </div>
        </t-form-item>

        <t-form-item>
          <t-button theme="primary" @click="saveUrl">保存配置</t-button>
        </t-form-item>
      </t-form>

      <t-divider></t-divider>

      <t-space direction="vertical" style="width: 100%">
        <h3>配置说明</h3>
        <t-list>
          <t-list-item>
            <template #content>
              <span>1. 大屏URL支持内网或外网地址，建议使用内网地址以提高访问速度</span>
            </template>
          </t-list-item>
          <t-list-item>
            <template #content>
              <span>2. 如大屏页面需要登录，请确保URL中包含必要的认证参数</span>
            </template>
          </t-list-item>
          <t-list-item>
            <template #content>
              <span>3. 大屏页面应支持iframe嵌入，否则可能无法正常显示</span>
            </template>
          </t-list-item>
          <t-list-item>
            <template #content>
              <span>4. 修改URL后，所有用户的系统首页将立即更新</span>
            </template>
          </t-list-item>
        </t-list>
      </t-space>
    </t-card>

    <!-- 管理员密码验证对话框 -->
    <t-dialog
      v-model:visible="isPasswordDialogVisible"
      header="管理员验证"
      :width="400"
      :footer="false"
      @close="cancelSave"
    >
      <t-space direction="vertical" size="large" style="width: 100%">
        <t-alert theme="info" message="请输入管理员密码进行验证" />

        <t-input
          v-model="adminPassword"
          type="password"
          placeholder="请输入管理员密码"
          clearable
        />

        <t-space>
          <t-button theme="primary" @click="verifyAndSave">确认</t-button>
          <t-button theme="default" variant="base" @click="cancelSave">取消</t-button>
        </t-space>
      </t-space>
    </t-dialog>
  </div>
</template>
<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { MessagePlugin } from 'tdesign-vue-next';
import { useRouter } from 'vue-router';

const router = useRouter();

// 大屏URL
const dashboardUrl = ref('');
const isPasswordDialogVisible = ref(false);
const adminPassword = ref('');
const correctPassword = 'admin123'; // 实际项目应从后端验证

// 从本地存储加载URL
onMounted(() => {
  const savedUrl = localStorage.getItem('dashboardUrl');
  if (savedUrl) {
    dashboardUrl.value = savedUrl;
  }
});

// 保存URL
const saveUrl = () => {
  if (!dashboardUrl.value) {
    MessagePlugin.warning('请输入大屏展示URL');
    return;
  }

  // 显示密码验证对话框
  isPasswordDialogVisible.value = true;
};

// 验证密码并保存
const verifyAndSave = () => {
  if (adminPassword.value === correctPassword) {
    // 确保URL格式正确
    let url = dashboardUrl.value;
    if (!url.startsWith('http://') && !url.startsWith('https://')) {
      url = 'http://' + url;
      dashboardUrl.value = url;
    }

    // 保存到本地存储
    localStorage.setItem('dashboardUrl', url);

    MessagePlugin.success('大屏URL配置已保存');
    isPasswordDialogVisible.value = false;
    adminPassword.value = '';

    // 跳转到首页查看效果
    router.push('/system/home');
  } else {
    MessagePlugin.error('管理员密码错误');
  }
};

// 取消保存
const cancelSave = () => {
  isPasswordDialogVisible.value = false;
  adminPassword.value = '';
};

// 查看大屏
const viewDashboard = () => {
  router.push('/system/home');
};
</script>

<style scoped lang="less">
.dashboard-config-container {
  padding: 20px;
  background-color: var(--td-bg-color-container);
  border-radius: var(--td-radius-medium);

  .form-tips {
    font-size: 12px;
    color: var(--td-text-color-secondary);
    margin-top: 4px;
  }
}
</style>

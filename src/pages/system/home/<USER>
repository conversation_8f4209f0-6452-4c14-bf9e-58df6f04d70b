<template>
  <div class="system-home-container">
    <t-card title="系统大屏" bordered>
      <template #actions>
        <t-space>
          <t-button theme="default" @click="refreshFrame">
            <template #icon><t-icon name="refresh" /></template>
            刷新
          </t-button>
          <t-button theme="success" @click="toggleFullscreen">
            <template #icon><t-icon name="fullscreen" /></template>
            全屏展示
          </t-button>
          <t-button v-if="isAdmin" theme="primary" variant="text" @click="configUrl">
            <template #icon><t-icon name="setting" /></template>
            配置
          </t-button>
        </t-space>
      </template>

      <div class="screen-display">
        <div class="iframe-container">
          <iframe
            ref="iframeRef"
            :src="dashboardUrl"
            frameborder="0"
            allow="fullscreen"
            style="width: 100%; height: 700px; border: 1px solid #eee;"
          ></iframe>
        </div>
      </div>
    </t-card>
  </div>
</template>
<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { MessagePlugin } from 'tdesign-vue-next';
import { useRouter } from 'vue-router';

const router = useRouter();

// 大屏URL从本地存储获取
const dashboardUrl = ref('http://*************:8080/#/'); // 默认URL

// 是否全屏
const isFullscreen = ref(false);
// iframe引用
const iframeRef = ref(null);
// 是否是管理员
const isAdmin = ref(false);

// 从存储中获取URL
onMounted(() => {
  const savedUrl = localStorage.getItem('dashboardUrl');
  if (savedUrl) {
    dashboardUrl.value = savedUrl;
  }
});

// 刷新iframe
const refreshFrame = () => {
  if (iframeRef.value) {
    iframeRef.value.src = dashboardUrl.value;
  }
  MessagePlugin.success('已刷新');
};

// 全屏展示
const toggleFullscreen = () => {
  if (!isFullscreen.value) {
    const iframe = iframeRef.value;
    if (iframe && iframe.requestFullscreen) {
      iframe.requestFullscreen().catch((err: Error) => {
        MessagePlugin.error(`全屏展示失败: ${err.message}`);
      });
    } else {
      MessagePlugin.warning('您的浏览器不支持全屏功能');
    }
  }
};

// 配置URL (仅管理员可见)
const configUrl = () => {
  router.push('/system-home-config');
};

// 监听全屏变化
onMounted(() => {
  document.addEventListener('fullscreenchange', () => {
    isFullscreen.value = !!document.fullscreenElement;
  });

  // 在这里可以判断当前用户是否为管理员
  // 示例代码，实际项目中应从用户信息或权限系统获取
  isAdmin.value = true; // 模拟为管理员
});
</script>

<style scoped lang="less">
.system-home-container {
  padding: 20px;
  background-color: var(--td-bg-color-container);
  border-radius: var(--td-radius-medium);
  height: 100%;
  .iframe-container {
    overflow: hidden;
    position: relative;
    width: 100%;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);

    iframe {
      border: none;
      width: 100%;
      transition: all 0.3s ease;
      background-color: #fff;
    }
  }
}
</style>

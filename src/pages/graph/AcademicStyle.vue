<template>
  <div class="academic-style-management custom-tree-container">
    <t-card class="main-card">
      <div class="kg-header">
        <div class="kg-title">
          <div class="title-icon-wrapper">
            <t-icon name="mind-mapping" size="28px" />
            <div class="icon-glow"></div>
          </div>
          <h2>学风知识图谱管理</h2>
          <div class="title-subtitle">支持多层级指标体系管理</div>
        </div>
        <div class="kg-actions">
          <t-dropdown :options="createOptions" @click="handleCreateAction" trigger="click">
            <t-button theme="primary">
              <template #icon><t-icon name="add" /></template>
              新增节点
              <template #suffix><t-icon name="chevron-down" /></template>
            </t-button>
          </t-dropdown>
          <t-button theme="default" @click="loadData" :loading="loading">
            <template #icon><t-icon name="refresh" /></template>
            刷新数据
          </t-button>
          <t-button theme="default" @click="openBatchManagement">
            <template #icon><t-icon name="setting" /></template>
            批量管理
          </t-button>
        </div>
      </div>

      <div class="kg-container">
        <!-- 手风琴式节点展示 -->
        <div class="nodes-accordion">
          <div class="panel-header">
            <t-icon name="view-list" />
            <span>节点列表</span>
            <div class="panel-actions">
              <t-input 
                v-model="searchKeyword" 
                placeholder="搜索节点..." 
                clearable
                @change="filterTreeData"
                class="search-input"
              >
                <template #prefix-icon>
                  <t-icon name="search" />
                </template>
              </t-input>
            </div>
          </div>
          <t-collapse v-model="activeCollapsePanels" :default-value="['center', 'indicator']">
            <!-- 中心节点面板 -->
            <t-collapse-panel value="center" :header="getCenterPanelHeader()">
              <div class="node-cards-container">
                <div v-for="node in centerNodes" :key="node.id" 
                     class="node-card" 
                     :class="{ 'node-card-active': selectedNode?.id === node.id }"
                     @click="handleNodeClick(node)">
                  <div class="node-card-header">
                    <div class="node-card-icon center-icon">
                      <t-icon name="root-list" />
                    </div>
                    <div class="node-card-info">
                      <div class="node-card-title">{{ node.name }}</div>
                      <div class="node-card-type">中心节点</div>
                    </div>
                    <div class="node-card-actions">
                      <t-button theme="primary" variant="text" size="small" @click.stop="editNode(node)">
                        <t-icon name="edit" />
                      </t-button>
                      <t-button theme="danger" variant="text" size="small" @click.stop="confirmDeleteNode(node)">
                        <t-icon name="delete" />
                      </t-button>
                    </div>
                  </div>
                  <div class="node-card-description" v-if="node.description">
                    {{ node.description }}
                  </div>
                </div>
                <div v-if="centerNodes.length === 0" class="empty-nodes">
                  暂无中心节点
                </div>
              </div>
            </t-collapse-panel>

            <!-- 一级指标面板 -->
            <t-collapse-panel value="indicator" :header="getIndicatorPanelHeader()">
              <div class="node-cards-container">
                <div v-for="node in indicatorNodes" :key="node.id" 
                     class="node-card" 
                     :class="{ 'node-card-active': selectedNode?.id === node.id }"
                     @click="handleNodeClick(node)">
                  <div class="node-card-header">
                    <div class="node-card-icon indicator-icon">
                      <t-icon name="chart" />
                    </div>
                    <div class="node-card-info">
                      <div class="node-card-title">{{ node.name }}</div>
                      <div class="node-card-type">一级指标</div>
                      <div class="node-card-parent" v-if="node.parentId">
                        父节点：{{ getNodeName(node.parentId) }}
                      </div>
                    </div>
                    <div class="node-card-actions">
                      <t-button theme="primary" variant="text" size="small" @click.stop="editNode(node)">
                        <t-icon name="edit" />
                      </t-button>
                      <t-button theme="danger" variant="text" size="small" @click.stop="confirmDeleteNode(node)">
                        <t-icon name="delete" />
                      </t-button>
                    </div>
                  </div>
                  <div class="node-card-description" v-if="node.description">
                    {{ node.description }}
                  </div>
                </div>
                <div v-if="indicatorNodes.length === 0" class="empty-nodes">
                  暂无一级指标
                </div>
              </div>
            </t-collapse-panel>

            <!-- 二级指标面板 -->
            <t-collapse-panel value="secondaryIndicator" :header="getSecondaryPanelHeader()">
              <div class="node-cards-container">
                <div v-for="node in secondaryNodes" :key="node.id" 
                     class="node-card" 
                     :class="{ 'node-card-active': selectedNode?.id === node.id }"
                     @click="handleNodeClick(node)">
                  <div class="node-card-header">
                    <div class="node-card-icon secondary-icon">
                      <t-icon name="check-rectangle" />
                    </div>
                    <div class="node-card-info">
                      <div class="node-card-title">{{ node.name }}</div>
                      <div class="node-card-type">二级指标</div>
                      <div class="node-card-parent" v-if="node.parentId">
                        父节点：{{ getNodeName(node.parentId) }}
                      </div>
                    </div>
                    <div class="node-card-actions">
                      <t-button theme="primary" variant="text" size="small" @click.stop="editNode(node)">
                        <t-icon name="edit" />
                      </t-button>
                      <t-button theme="danger" variant="text" size="small" @click.stop="confirmDeleteNode(node)">
                        <t-icon name="delete" />
                      </t-button>
                    </div>
                  </div>
                  <div class="node-card-description" v-if="node.description">
                    {{ node.description }}
                  </div>
                </div>
                <div v-if="secondaryNodes.length === 0" class="empty-nodes">
                  暂无二级指标
                </div>
              </div>
            </t-collapse-panel>

            <!-- 三级指标面板 -->
            <t-collapse-panel value="thirdIndicator" :header="getThirdPanelHeader()">
              <div class="node-cards-container">
                <div v-for="node in thirdNodes" :key="node.id" 
                     class="node-card" 
                     :class="{ 'node-card-active': selectedNode?.id === node.id }"
                     @click="handleNodeClick(node)">
                  <div class="node-card-header">
                    <div class="node-card-icon third-icon">
                      <t-icon name="view-agenda" />
                    </div>
                    <div class="node-card-info">
                      <div class="node-card-title">{{ node.name }}</div>
                      <div class="node-card-type">三级指标</div>
                      <div class="node-card-parent" v-if="node.parentId">
                        父节点：{{ getNodeName(node.parentId) }}
                      </div>
                    </div>
                    <div class="node-card-actions">
                      <t-button theme="primary" variant="text" size="small" @click.stop="editNode(node)">
                        <t-icon name="edit" />
                      </t-button>
                      <t-button theme="danger" variant="text" size="small" @click.stop="confirmDeleteNode(node)">
                        <t-icon name="delete" />
                      </t-button>
                    </div>
                  </div>
                  <div class="node-card-description" v-if="node.description">
                    {{ node.description }}
                  </div>
                </div>
                <div v-if="thirdNodes.length === 0" class="empty-nodes">
                  暂无三级指标
                </div>
              </div>
            </t-collapse-panel>

            <!-- 四级指标面板 -->
            <t-collapse-panel value="fourthIndicator" :header="getFourthPanelHeader()">
              <div class="node-cards-container">
                <div v-for="node in fourthNodes" :key="node.id" 
                     class="node-card" 
                     :class="{ 'node-card-active': selectedNode?.id === node.id }"
                     @click="handleNodeClick(node)">
                  <div class="node-card-header">
                    <div class="node-card-icon fourth-icon">
                      <t-icon name="format-list-bulleted" />
                    </div>
                    <div class="node-card-info">
                      <div class="node-card-title">{{ node.name }}</div>
                      <div class="node-card-type">四级指标</div>
                      <div class="node-card-parent" v-if="node.parentId">
                        父节点：{{ getNodeName(node.parentId) }}
                      </div>
                    </div>
                    <div class="node-card-actions">
                      <t-button theme="primary" variant="text" size="small" @click.stop="editNode(node)">
                        <t-icon name="edit" />
                      </t-button>
                      <t-button theme="danger" variant="text" size="small" @click.stop="confirmDeleteNode(node)">
                        <t-icon name="delete" />
                      </t-button>
                    </div>
                  </div>
                  <div class="node-card-description" v-if="node.description">
                    {{ node.description }}
                  </div>
                </div>
                <div v-if="fourthNodes.length === 0" class="empty-nodes">
                  暂无四级指标
                </div>
              </div>
            </t-collapse-panel>
          </t-collapse>
        </div>

        <!-- 节点详情区域 -->
        <div class="detail-panel" v-if="selectedNode">
          <div class="panel-header">
            <t-icon name="bulletpoint" />
            <span>节点详情</span>
          </div>
          <div class="detail-container">
            <t-tabs v-model="currentTab">
              <t-tab-panel value="info" label="节点信息">
                <div class="node-detail-header">
                  <div class="node-detail-title">
                    <span class="node-detail-name">{{ selectedNode?.name }}</span>
                    <span class="node-type-tag ml-2" :class="'node-type-' + selectedNode?.nodeType">
                      {{ getNodeTypeName(selectedNode?.nodeType) }}
                    </span>
                  </div>
                  <div class="node-detail-actions">
                    <t-button theme="primary" size="small" @click="editNode(selectedNode)">
                      <template #icon><t-icon name="edit" /></template>
                      编辑节点
                    </t-button>
                  </div>
                </div>
                <div class="node-detail-content">
                  <div class="node-info-card">
                    <div class="info-card-header">
                      <t-icon name="info-circle" />
                      <span>基本信息</span>
                    </div>
                    <div class="info-card-body">
                      <div class="info-item">
                        <div class="info-label"><t-icon name="edit-1" />节点名称</div>
                        <div class="info-value">{{ selectedNode?.name }}</div>
                      </div>
                      <div class="info-item">
                        <div class="info-label"><t-icon name="folder" />节点类型</div>
                        <div class="info-value">
                          <span class="node-type-dot" :class="'node-type-dot-' + selectedNode?.nodeType"></span>
                          {{ getNodeTypeName(selectedNode?.nodeType) }}
                        </div>
                      </div>
                      <div class="info-item">
                        <div class="info-label"><t-icon name="link" />父节点</div>
                        <div class="info-value">{{ getNodeName(selectedNode?.parentId) || '无' }}</div>
                      </div>
                    </div>
                  </div>
                  
                  <div class="node-info-card">
                    <div class="info-card-header">
                      <t-icon name="file-copy" />
                      <span>节点描述</span>
                    </div>
                    <div class="info-card-body">
                      <div class="description-content">
                        {{ selectedNode?.description || '暂无描述' }}
                      </div>
                    </div>
                  </div>
                  
                  <div class="node-info-card">
                    <div class="info-card-header">
                      <t-icon name="history" />
                      <span>时间信息</span>
                    </div>
                    <div class="info-card-body">
                      <div class="info-item">
                        <div class="info-label"><t-icon name="add-circle" />创建时间</div>
                        <div class="info-value">{{ formatDate(selectedNode?.createTime) }}</div>
                      </div>
                      <div class="info-item">
                        <div class="info-label"><t-icon name="edit-1" />更新时间</div>
                        <div class="info-value">{{ formatDate(selectedNode?.updateTime) }}</div>
                      </div>
                    </div>
                  </div>
                </div>
              </t-tab-panel>
              <t-tab-panel value="links" label="关联关系">
                <div class="links-container">
                  <div class="link-type">
                    <h4>出站连接 ({{ sourceLinks.length }})</h4>
                    <div v-if="sourceLinks.length === 0" class="empty-links">
                      暂无出站连接
                    </div>
                    <div v-else class="links-list">
                      <div v-for="link in sourceLinks" :key="link.id" class="link-item">
                        <div class="link-info">
                          <t-icon name="arrow-right" />
                          <span class="link-target">{{ getNodeName(link.target) }}</span>
                          <span class="link-description">{{ link.description }}</span>
                        </div>
                        <div class="link-actions">
                          <t-button theme="primary" variant="text" size="small" @click="editLink(link)">
                            <t-icon name="edit" />
                          </t-button>
                          <t-button theme="danger" variant="text" size="small" @click="confirmDeleteLink(link)">
                            <t-icon name="delete" />
                          </t-button>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="link-type">
                    <h4>入站连接 ({{ targetLinks.length }})</h4>
                    <div v-if="targetLinks.length === 0" class="empty-links">
                      暂无入站连接
                    </div>
                    <div v-else class="links-list">
                      <div v-for="link in targetLinks" :key="link.id" class="link-item">
                        <div class="link-info">
                          <t-icon name="arrow-left" />
                          <span class="link-source">{{ getNodeName(link.source) }}</span>
                          <span class="link-description">{{ link.description }}</span>
                        </div>
                        <div class="link-actions">
                          <t-button theme="primary" variant="text" size="small" @click="editLink(link)">
                            <t-icon name="edit" />
                          </t-button>
                          <t-button theme="danger" variant="text" size="small" @click="confirmDeleteLink(link)">
                            <t-icon name="delete" />
                          </t-button>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="add-link">
                    <t-space>
                      <t-button theme="primary" @click="openCreateOutgoingLinkDialog(selectedNode)">
                        <template #icon><t-icon name="arrow-right" /></template>
                        创建出站关联
                      </t-button>
                      <t-button theme="primary" @click="openCreateIncomingLinkDialog(selectedNode)">
                        <template #icon><t-icon name="arrow-left" /></template>
                        创建入站关联
                      </t-button>
                    </t-space>
                  </div>
                </div>
              </t-tab-panel>
            </t-tabs>
          </div>
        </div>
        
        <!-- 未选择节点时的提示 -->
        <div class="detail-panel empty-detail-panel" v-else>
          <div class="panel-header">
            <t-icon name="bulletpoint" />
            <span>节点详情</span>
          </div>
          <div class="empty-detail">
            <t-icon name="info-circle" size="large" />
            <p>请从左侧选择一个节点查看详情</p>
          </div>
        </div>
      </div>
    </t-card>

    <!-- 新增/编辑节点对话框 -->
    <t-dialog
      :visible="nodeDialogVisible"
      :header="nodeFormMode === 'create' ? '新增节点' : '编辑节点'"
      width="600px"
      @close="nodeDialogVisible = false"
      class="kg-dialog"
    >
      <div class="dialog-content">
        <t-form
          ref="nodeFormRef"
          :data="nodeForm"
          :rules="nodeRules"
          label-align="left"
          :label-width="120"
          @submit="submitNodeForm"
        >
          <t-form-item label="节点名称" name="name">
            <t-input v-model="nodeForm.name" placeholder="请输入节点名称" />
          </t-form-item>
          <t-form-item label="节点类型" name="nodeType">
            <t-radio-group v-model="nodeForm.nodeType">
              <t-radio-button value="center">
                <span class="radio-with-icon">
                  <t-icon name="root-list" />
                  <span>中心节点</span>
                </span>
              </t-radio-button>
              <t-radio-button value="indicator">
                <span class="radio-with-icon">
                  <t-icon name="chart" />
                  <span>一级指标</span>
                </span>
              </t-radio-button>
              <t-radio-button value="secondaryIndicator">
                <span class="radio-with-icon">
                  <t-icon name="check-rectangle" />
                  <span>二级指标</span>
                </span>
              </t-radio-button>
              <t-radio-button value="thirdIndicator">
                <span class="radio-with-icon">
                  <t-icon name="view-agenda" />
                  <span>三级指标</span>
                </span>
              </t-radio-button>
              <t-radio-button value="fourthIndicator">
                <span class="radio-with-icon">
                  <t-icon name="format-list-bulleted" />
                  <span>四级指标</span>
                </span>
              </t-radio-button>
            </t-radio-group>
          </t-form-item>
          <t-form-item label="父节点" name="parentId">
            <t-select v-model="nodeForm.parentId" placeholder="请选择父节点" clearable>
              <t-option
                v-for="node in parentNodeOptions"
                :key="node.id"
                :value="node.id"
                :label="node.name"
              />
            </t-select>
          </t-form-item>
          <t-form-item label="节点描述" name="description">
            <t-textarea v-model="nodeForm.description" placeholder="请输入节点描述" />
          </t-form-item>
        </t-form>
        
        <div class="node-preview" v-if="nodeForm.nodeType">
          <div class="preview-title">节点预览</div>
          <div class="preview-content">
            <div class="actual-preview">
              <div class="preview-node" :class="'preview-node-' + nodeForm.nodeType">
                <span class="node-label">{{ nodeForm.name || '节点名称' }}</span>
              </div>
              <div class="node-type-label">{{ getNodeTypeName(nodeForm.nodeType) }}</div>
            </div>
            <div class="preview-legend">
              <div class="legend-item">
                <span class="legend-color" :style="{ 
                  backgroundColor: 
                    nodeForm.nodeType === 'center' ? '#059669' : 
                    nodeForm.nodeType === 'indicator' ? '#4F46E5' : 
                    nodeForm.nodeType === 'secondaryIndicator' ? '#F59E0B' :
                    nodeForm.nodeType === 'thirdIndicator' ? '#EF4444' : 
                    nodeForm.nodeType === 'fourthIndicator' ? '#06B6D4' : '#059669'
                }"></span>
                <span>{{ getNodeTypeName(nodeForm.nodeType) }}</span>
              </div>
              <div class="legend-size" v-if="nodeForm.nodeType">
                尺寸：{{ 
                  nodeForm.nodeType === 'center' ? '60px' : 
                  nodeForm.nodeType === 'indicator' ? '40px' : 
                  nodeForm.nodeType === 'secondaryIndicator' ? '30px' :
                  nodeForm.nodeType === 'thirdIndicator' ? '25px' : '20px'
                }}
              </div>
            </div>
          </div>
        </div>
      </div>
      <template #footer>
        <t-button theme="default" @click="nodeDialogVisible = false">取消</t-button>
        <t-button theme="primary" @click="submitNodeForm">确认</t-button>
      </template>
    </t-dialog>

    <!-- 新增/编辑连接对话框 -->
    <t-dialog
      :visible="linkDialogVisible"
      :header="linkFormMode === 'create' 
        ? (linkForm.target ? '创建入站关联' : '创建出站关联') 
        : '编辑关联'"
      width="600px"
      @close="linkDialogVisible = false"
      class="kg-dialog"
    >
      <t-form
        ref="linkFormRef"
        :data="linkForm"
        :rules="linkRules"
        label-align="left"
        :label-width="120"
        @submit="submitLinkForm"
      >
        <t-form-item label="关联方向">
          <div class="link-direction-indicator">
            <div class="link-node" :class="{'link-current-node': !!linkForm.source && !linkForm.target}">
              {{ linkForm.source ? getNodeName(linkForm.source) : '源节点' }}
            </div>
            <t-icon name="arrow-right" class="link-arrow" />
            <div class="link-node" :class="{'link-current-node': !!linkForm.target && !linkForm.source}">
              {{ linkForm.target ? getNodeName(linkForm.target) : '目标节点' }}
            </div>
          </div>
        </t-form-item>
        <t-form-item label="源节点" name="source">
          <t-select v-model="linkForm.source" placeholder="请选择源节点" :disabled="linkFormMode === 'create' && !!selectedNode && !!linkForm.source">
            <t-option v-for="node in nodeList" :key="node.id" :value="node.id" :label="node.name" />
          </t-select>
        </t-form-item>
        <t-form-item label="目标节点" name="target">
          <t-select v-model="linkForm.target" placeholder="请选择目标节点" :disabled="linkFormMode === 'create' && !!selectedNode && !!linkForm.target">
            <t-option
              v-for="node in nodeList.filter((n: GraphNode) => n.id !== linkForm.source)"
              :key="node.id"
              :value="node.id"
              :label="node.name"
            />
          </t-select>
        </t-form-item>
        <t-form-item label="关联描述" name="description">
          <t-textarea v-model="linkForm.description" placeholder="请输入关联描述" />
        </t-form-item>
      </t-form>
      <template #footer>
        <t-button theme="default" @click="linkDialogVisible = false">取消</t-button>
        <t-button theme="primary" @click="submitLinkForm">确认</t-button>
      </template>
    </t-dialog>

    <!-- 删除确认对话框 -->
    <t-dialog
      :visible="deleteDialogVisible"
      header="删除确认"
      width="500px"
      @close="deleteDialogVisible = false"
      class="kg-dialog"
    >
      <div class="delete-content">
        <t-icon name="error-circle" class="delete-icon" />
        <p>{{ deleteDialogMessage }}</p>
      </div>
      <template #footer>
        <t-button theme="default" @click="deleteDialogVisible = false">取消</t-button>
        <t-button theme="danger" @click="confirmDelete">确认删除</t-button>
      </template>
    </t-dialog>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, reactive, computed, onMounted } from 'vue';
import { MessagePlugin } from 'tdesign-vue-next';
// @ts-ignore
import { format } from 'date-fns';
import {
  getKnowledgeGraph,
  addNode,
  updateNode,
  deleteNode,
  addLink,
  updateLink,
  deleteLink,
  type GraphNode,
  type GraphLink,
  type KnowledgeGraphData
} from '@/api/graph/nodes';

// 定义树节点类型
interface TreeNode {
  label: string;
  value: string;
  data: {
    nodeType: string;
    raw: GraphNode;
  };
  children?: TreeNode[];
}

export default defineComponent({
  name: 'AcademicStyleKnowledgeGraphManagement',
  setup() {
    // 节点数据
    const nodeList = ref<GraphNode[]>([]);
    const selectedNode = ref<GraphNode | null>(null);
    const selectedTreeNodeId = ref<string[]>([]);
    const currentTab = ref('info');
    const nodeDetailVisible = ref(false);
    const loading = ref(false);
    const searchKeyword = ref('');

    // 下拉菜单选项
    const createOptions = [
      { content: '新增一级指标', value: 'indicator', icon: 'layers' },
      { content: '新增二级指标', value: 'secondaryIndicator', icon: 'view-module' },
      { content: '新增三级指标', value: 'thirdIndicator', icon: 'view-agenda' },
      { content: '新增四级指标', value: 'fourthIndicator', icon: 'format-list-bulleted' },
    ];

    // 链接数据
    const linkList = ref<GraphLink[]>([]);
    const sourceLinks = computed(() => {
      if (!selectedNode.value) return [];
      return linkList.value.filter(link => link.source === selectedNode.value.id);
    });

    const targetLinks = computed(() => {
      if (!selectedNode.value) return [];
      return linkList.value.filter(link => link.target === selectedNode.value.id);
    });

    // 获取指定类型的节点数量
    const getNodeCountByType = (nodeType: string) => {
      return nodeList.value.filter(node => node.nodeType === nodeType).length;
    };

    // 处理创建操作
    const handleCreateAction = (data: any) => {
      nodeFormMode.value = 'create';
      Object.assign(nodeForm, {
        id: '',
        name: '',
        description: '',
        nodeType: data.value,
        parentId: ''
      });
      nodeDialogVisible.value = true;
    };

    // 打开批量管理
    const openBatchManagement = () => {
      MessagePlugin.info('批量管理功能开发中...');
    };

    // 过滤树形数据
    const filterTreeData = () => {
      // 实现搜索过滤逻辑
      if (!searchKeyword.value.trim()) {
        loadData(); // 重新加载数据
        return;
      }
      
      const filtered = nodeList.value.filter(node => 
        node.name.toLowerCase().includes(searchKeyword.value.toLowerCase()) ||
        (node.description && node.description.toLowerCase().includes(searchKeyword.value.toLowerCase()))
      );
      
      // 基于过滤结果重新构建树形数据
      const filteredTreeData = [];
      for (const node of filtered) {
        if (node.nodeType === 'center') {
          filteredTreeData.push({
            value: node.id,
            label: node.name,
            children: [],
            data: { nodeType: node.nodeType, raw: node }
          });
        }
      }
      // 简化处理，实际应该递归构建完整树结构
    };

    // 树形结构数据构建
    const treeData = computed<TreeNode[]>(() => {
      const tree: TreeNode[] = [];
      // 找到中心节点
      const centerNodes = nodeList.value.filter(node => node.nodeType === 'center');
      
      centerNodes.forEach(centerNode => {
        const centerTreeNode: TreeNode = {
          label: centerNode.name,
          value: centerNode.id,
          data: {
            nodeType: centerNode.nodeType,
            raw: centerNode
          },
          children: []
        };
        
        // 找到一级指标节点
        const indicatorNodes = nodeList.value.filter(node => 
          node.nodeType === 'indicator' && node.parentId === centerNode.id);
        
        indicatorNodes.forEach(indicatorNode => {
          const indicatorTreeNode: TreeNode = {
            label: indicatorNode.name,
            value: indicatorNode.id,
            data: {
              nodeType: indicatorNode.nodeType,
              raw: indicatorNode
            },
            children: []
          };
          
          // 找到二级指标节点
          const secondaryNodes = nodeList.value.filter(node => 
            node.nodeType === 'secondaryIndicator' && node.parentId === indicatorNode.id);
          
          secondaryNodes.forEach(secondaryNode => {
            const secondaryTreeNode: TreeNode = {
              label: secondaryNode.name,
              value: secondaryNode.id,
              data: {
                nodeType: secondaryNode.nodeType,
                raw: secondaryNode
              },
              children: []
            };
            
            // 找到三级指标节点
            const thirdNodes = nodeList.value.filter(node => 
              node.nodeType === 'thirdIndicator' && node.parentId === secondaryNode.id);
            
            thirdNodes.forEach(thirdNode => {
              const thirdTreeNode: TreeNode = {
                label: thirdNode.name,
                value: thirdNode.id,
                data: {
                  nodeType: thirdNode.nodeType,
                  raw: thirdNode
                },
                children: []
              };
              
              // 找到四级指标节点
              const fourthNodes = nodeList.value.filter(node => 
                node.nodeType === 'fourthIndicator' && node.parentId === thirdNode.id);
              
              fourthNodes.forEach(fourthNode => {
                thirdTreeNode.children?.push({
                  label: fourthNode.name,
                  value: fourthNode.id,
                  data: {
                    nodeType: fourthNode.nodeType,
                    raw: fourthNode
                  }
                });
              });
              
              secondaryTreeNode.children?.push(thirdTreeNode);
            });
            
            indicatorTreeNode.children?.push(secondaryTreeNode);
          });
          
          centerTreeNode.children?.push(indicatorTreeNode);
        });
        
        tree.push(centerTreeNode);
      });
      
      return tree;
    });

    // 父节点选项（根据节点类型过滤）
    const parentNodeOptions = computed(() => {
      if (nodeForm.nodeType === 'center') {
        return [];
      } else if (nodeForm.nodeType === 'indicator') {
        return nodeList.value.filter(node => node.nodeType === 'center');
      } else if (nodeForm.nodeType === 'secondaryIndicator') {
        return nodeList.value.filter(node => node.nodeType === 'indicator');
      } else if (nodeForm.nodeType === 'thirdIndicator') {
        return nodeList.value.filter(node => node.nodeType === 'secondaryIndicator');
      } else if (nodeForm.nodeType === 'fourthIndicator') {
        return nodeList.value.filter(node => node.nodeType === 'thirdIndicator');
      }
      return nodeList.value;
    });

    // 节点表单
    const nodeDialogVisible = ref(false);
    const nodeFormMode = ref<'create' | 'edit'>('create');
    const nodeForm = reactive<{
      id: string;
      name: string;
      description: string;
      nodeType: 'center' | 'indicator' | 'secondaryIndicator' | 'thirdIndicator' | 'fourthIndicator';
      parentId: string;
    }>({
      id: '',
      name: '',
      description: '',
      nodeType: 'center',
      parentId: ''
    });

    const nodeRules = {
      name: [{ required: true, message: '请输入节点名称', type: 'error' as const }],
      nodeType: [{ required: true, message: '请选择节点类型', type: 'error' as const }]
    };

    // 连接表单
    const linkDialogVisible = ref(false);
    const linkFormMode = ref<'create' | 'edit'>('create');
    const linkForm = reactive<{
      id: string;
      source: string;
      target: string;
      description: string;
    }>({
      id: '',
      source: '',
      target: '',
      description: ''
    });

    const linkRules = {
      source: [{ required: true, message: '请选择源节点', type: 'error' as const }],
      target: [{ required: true, message: '请选择目标节点', type: 'error' as const }]
    };

    // 删除对话框
    const deleteDialogVisible = ref(false);
    const deleteDialogMessage = ref('');
    const deleteType = ref<'node' | 'link'>('node');
    const deleteId = ref('');

    // 加载数据
    const loadData = async () => {
      loading.value = true;
      try {
        const data = await getKnowledgeGraph();
        nodeList.value = data.nodes;
        linkList.value = data.links;
      } catch (error) {
        MessagePlugin.error('获取知识图谱数据失败');
        console.error(error);
      } finally {
        loading.value = false;
      }
    };

    // 节点类型名称
    const getNodeTypeName = (type?: string) => {
      if (!type) return '';
      switch (type) {
        case 'center': return '中心节点';
        case 'indicator': return '一级指标';
        case 'secondaryIndicator': return '二级指标';
        case 'thirdIndicator': return '三级指标';
        case 'fourthIndicator': return '四级指标';
        default: return type;
      }
    };

    // 处理树节点点击
    const handleTreeNodeActive = (value: any, context: any) => {
      if (value && value.length > 0) {
        selectedTreeNodeId.value = value;
        const nodeId = value[0];
        const node = nodeList.value.find(n => n.id === nodeId);
        if (node) {
          selectedNode.value = node;
          currentTab.value = 'info';
          nodeDetailVisible.value = true;
        }
      }
    };

    // 节点操作
    const handleNodeClick = (node: GraphNode) => {
      selectedNode.value = node;
      currentTab.value = 'info';
      nodeDetailVisible.value = true;
    };

    const getNodeLinksCount = (nodeId: string) => {
      const sourceCount = linkList.value.filter(link => link.source === nodeId).length;
      const targetCount = linkList.value.filter(link => link.target === nodeId).length;
      return `${sourceCount} 出站 / ${targetCount} 入站`;
    };

    const openCreateNodeDialog = () => {
      nodeFormMode.value = 'create';
      Object.assign(nodeForm, {
        id: '',
        name: '',
        description: '',
        nodeType: 'center',
        parentId: ''
      });
      nodeDialogVisible.value = true;
    };

    const editNode = (node: GraphNode | null) => {
      if (!node || !node.id) {
        MessagePlugin.error('节点数据无效，无法编辑');
        return;
      }
      
      nodeFormMode.value = 'edit';
      Object.assign(nodeForm, {
        id: node.id,
        name: node.name,
        description: node.description || '',
        nodeType: node.nodeType,
        parentId: node.parentId || ''
      });
      nodeDialogVisible.value = true;
    };

    const confirmDeleteNode = (node: GraphNode | null) => {
      if (!node || !node.id) {
        MessagePlugin.error('节点数据无效，无法删除');
        return;
      }
      
      deleteType.value = 'node';
      deleteId.value = node.id;
      deleteDialogMessage.value = `确认删除节点"${node.name}"吗？该操作将同时删除与此节点相关的所有连接，且不可恢复。`;
      deleteDialogVisible.value = true;
    };

    const submitNodeForm = async () => {
      try {
        // 表单验证
        if (!nodeForm.name || !nodeForm.nodeType) {
          MessagePlugin.warning('请填写必填项');
          return;
        }

        if (nodeForm.nodeType !== 'center' && !nodeForm.parentId) {
          MessagePlugin.warning('请选择父节点');
          return;
        }
        if (nodeFormMode.value === 'create') {
          // 创建节点
          const { id, ...nodeData } = nodeForm;
          await addNode(nodeData);
          MessagePlugin.success('节点创建成功');
        } else {
          // 更新节点
          const { id, ...nodeData } = nodeForm;
          await updateNode(id, nodeData);
          MessagePlugin.success('节点更新成功');
        }

        // 重新加载数据
        await loadData();
        nodeDialogVisible.value = false;
      } catch (error) {
        console.error('保存节点失败:', error);
        MessagePlugin.error('保存节点失败');
      }
    };

    // 连接操作
    const getNodeName = (nodeId?: string) => {
      if (!nodeId) return '';
      const node = nodeList.value.find(n => n.id === nodeId);
      return node ? node.name : '未知节点';
    };

    const openCreateOutgoingLinkDialog = (node: GraphNode) => {
      linkFormMode.value = 'create';
      Object.assign(linkForm, {
        id: '',
        source: node.id,
        target: '',
        description: ''
      });
      linkDialogVisible.value = true;
    };

    const openCreateIncomingLinkDialog = (node: GraphNode) => {
      linkFormMode.value = 'create';
      Object.assign(linkForm, {
        id: '',
        source: '',
        target: node.id,
        description: ''
      });
      linkDialogVisible.value = true;
    };

    const editLink = (link: GraphLink) => {
      linkFormMode.value = 'edit';
      Object.assign(linkForm, {
        id: link.id,
        source: link.source,
        target: link.target,
        description: link.description || ''
      });
      linkDialogVisible.value = true;
    };

    const confirmDeleteLink = (link: GraphLink) => {
      deleteType.value = 'link';
      deleteId.value = link.id;
      deleteDialogMessage.value = `确认删除从"${getNodeName(link.source)}"到"${getNodeName(link.target)}"的连接吗？该操作不可恢复。`;
      deleteDialogVisible.value = true;
    };

    const submitLinkForm = async () => {
      try {
        // 表单验证
        if (!linkForm.source || !linkForm.target) {
          MessagePlugin.warning('请选择源节点和目标节点');
          return;
        }

        if (linkFormMode.value === 'create') {
          // 创建连接
          const { id, ...linkData } = linkForm;
          await addLink(linkData);
          MessagePlugin.success('连接创建成功');
        } else {
          // 更新连接
          const { id, ...linkData } = linkForm;
          await updateLink(id, linkData);
          MessagePlugin.success('连接更新成功');
        }

        // 重新加载数据
        await loadData();
        linkDialogVisible.value = false;
      } catch (error) {
        console.error('保存连接失败:', error);
        MessagePlugin.error('保存连接失败');
      }
    };

    // 通用删除确认
    const confirmDelete = async () => {
      try {
        if (deleteType.value === 'node') {
          // 删除节点
          await deleteNode(deleteId.value);
          MessagePlugin.success('节点删除成功');
        } else if (deleteType.value === 'link') {
          // 删除连接
          await deleteLink(deleteId.value);
          MessagePlugin.success('连接删除成功');
        }

        // 重新加载数据
        await loadData();

        // 如果当前选中的是被删除节点，取消选中
        if (deleteType.value === 'node' && selectedNode.value && selectedNode.value.id === deleteId.value) {
          selectedNode.value = null;
          nodeDetailVisible.value = false;
        }

        deleteDialogVisible.value = false;
      } catch (error) {
        console.error('删除失败:', error);
        MessagePlugin.error('删除失败');
      }
    };

    // 格式化日期
    const formatDate = (date?: string) => {
      if (!date) return '';
      return format(new Date(date), 'yyyy-MM-dd HH:mm:ss');
    };

    // 在setup函数中添加findNodeById辅助函数
    const findNodeById = (nodeId: string | number) => {
      if (!nodeId) return null;
      return nodeList.value.find(node => node.id === String(nodeId)) || null;
    };

    // 添加树折叠处理函数
    const handleTreeFold = (value: boolean, context: any) => {
      // 添加一个小延迟强制重新渲染
      setTimeout(() => {
        // 如果使用了tree.$forceUpdate可以调用，或者其他方式强制刷新DOM
        const treeContainer = document.querySelector('.tree-container');
        if (treeContainer) {
          treeContainer.classList.add('force-redraw');
          setTimeout(() => {
            treeContainer.classList.remove('force-redraw');
          }, 50);
        }
      }, 50);
    };

    onMounted(() => {
      loadData();
    });

    // 手风琴面板展开状态
    const activeCollapsePanels = ref(['center', 'indicator']);

    // 按节点类型分组的计算属性
    const centerNodes = computed(() => {
      return filteredNodes.value.filter(node => node.nodeType === 'center');
    });

    const indicatorNodes = computed(() => {
      return filteredNodes.value.filter(node => node.nodeType === 'indicator');
    });

    const secondaryNodes = computed(() => {
      return filteredNodes.value.filter(node => node.nodeType === 'secondaryIndicator');
    });

    const thirdNodes = computed(() => {
      return filteredNodes.value.filter(node => node.nodeType === 'thirdIndicator');
    });

    const fourthNodes = computed(() => {
      return filteredNodes.value.filter(node => node.nodeType === 'fourthIndicator');
    });

    // 过滤后的节点列表
    const filteredNodes = computed(() => {
      if (!searchKeyword.value.trim()) {
        return nodeList.value;
      }
      
      return nodeList.value.filter(node => 
        node.name.toLowerCase().includes(searchKeyword.value.toLowerCase()) ||
        (node.description && node.description.toLowerCase().includes(searchKeyword.value.toLowerCase()))
      );
    });

    // 面板标题生成函数
    const getCenterPanelHeader = () => {
      const count = centerNodes.value.length;
      return `中心节点 (${count})`;
    };

    const getIndicatorPanelHeader = () => {
      const count = indicatorNodes.value.length;
      return `一级指标 (${count})`;
    };

    const getSecondaryPanelHeader = () => {
      const count = secondaryNodes.value.length;
      return `二级指标 (${count})`;
    };

    const getThirdPanelHeader = () => {
      const count = thirdNodes.value.length;
      return `三级指标 (${count})`;
    };

    const getFourthPanelHeader = () => {
      const count = fourthNodes.value.length;
      return `四级指标 (${count})`;
    };

    return {
      // 数据
      nodeList,
      selectedNode,
      selectedTreeNodeId,
      currentTab,
      nodeDetailVisible,
      loading,
      treeData,
      linkList,
      sourceLinks,
      targetLinks,
      parentNodeOptions,
      nodeDialogVisible,
      nodeFormMode,
      nodeForm,
      nodeRules,
      linkDialogVisible,
      linkFormMode,
      linkForm,
      linkRules,
      deleteDialogVisible,
      deleteDialogMessage,
      deleteType,
      deleteId,
      searchKeyword,
      createOptions,
      
      // 手风琴相关数据
      activeCollapsePanels,
      centerNodes,
      indicatorNodes,
      secondaryNodes,
      thirdNodes,
      fourthNodes,
      filteredNodes,

      // 方法
      loadData,
      getNodeTypeName,
      handleTreeNodeActive,
      handleNodeClick,
      getNodeLinksCount,
      openCreateNodeDialog,
      editNode,
      confirmDeleteNode,
      getNodeName,
      openCreateOutgoingLinkDialog,
      openCreateIncomingLinkDialog,
      editLink,
      confirmDeleteLink,
      submitNodeForm,
      submitLinkForm,
      confirmDelete,
      formatDate,
      findNodeById,
      handleTreeFold,
      getNodeCountByType,
      handleCreateAction,
      openBatchManagement,
      filterTreeData,
      
      // 手风琴相关方法
      getCenterPanelHeader,
      getIndicatorPanelHeader,
      getSecondaryPanelHeader,
      getThirdPanelHeader,
      getFourthPanelHeader,
    };
  }
});
</script>

<style lang="less" scoped>
.academic-style-management {
  min-height: calc(100vh - 120px);
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 20px;
  
  .main-card {
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    border-radius: 16px;
    overflow: hidden;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.18);
  }

  .kg-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 24px 32px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
  }

  .kg-title {
    display: flex;
    align-items: center;
    flex-direction: column;
    align-items: flex-start;
    
    .title-icon-wrapper {
      position: relative;
      margin-right: 16px;
      display: inline-flex;
      align-items: center;
      
      .icon-glow {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 40px;
        height: 40px;
        background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
        border-radius: 50%;
        animation: pulse 2s infinite;
      }
      
      :deep(.t-icon) {
        position: relative;
        z-index: 1;
        filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
      }
    }
    
    h2 {
      margin: 0;
      font-size: 24px;
      font-weight: 600;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    }
    
    .title-subtitle {
      font-size: 14px;
      opacity: 0.9;
      margin-top: 4px;
      font-weight: 400;
    }
  }
  
  .kg-actions {
    display: flex;
    gap: 12px;
    align-items: center;
    
    :deep(.t-button) {
      border-radius: 8px;
      font-weight: 500;
      transition: all 0.3s ease;
      backdrop-filter: blur(10px);
      
      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
      }
    }
  }

  .kg-container {
    display: flex;
    height: calc(100vh - 220px);
    min-height: 500px;
  }

  .panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 1px solid #dee2e6;
    font-weight: 600;
    color: #495057;
    
    :deep(.t-icon) {
      margin-right: 8px;
      color: var(--td-brand-color);
    }
    
    .panel-actions {
      .search-input {
        width: 200px;
      }
    }
  }

  .tree-panel {
    width: 360px;
    border-right: 1px solid #eee;
    display: flex;
    flex-direction: column;
    background-color: #fff;
    overflow: hidden;
  }

  .tree-container {
    padding: 16px;
    overflow-y: auto;
    flex: 1;
    
    &.force-redraw {
      transform: translateZ(0);
    }
  }

  // 手风琴容器样式
  .nodes-accordion {
    width: 360px;
    border-right: 1px solid #eee;
    display: flex;
    flex-direction: column;
    background-color: #fff;
    height: 100%; // 设置明确的高度
    
    // 优化滚动条样式
    :deep(.t-collapse) {
      border: none;
      flex: 1; // 占据剩余空间
      display: flex;
      flex-direction: column;
      overflow-y: auto; // 允许垂直滚动
      
      // 滚动条样式
      &::-webkit-scrollbar {
        width: 6px;
      }
      
      &::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 3px;
      }
      
      &::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 3px;
        
        &:hover {
          background: #a8a8a8;
        }
      }
    }
    
    :deep(.t-collapse__item) {
      border: none;
      border-bottom: 1px solid #f0f0f0;
      flex-shrink: 0; // 防止收缩
      
      &:last-child {
        border-bottom: none;
      }
    }
    
    :deep(.t-collapse__header) {
      padding: 16px 20px;
      background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
      border-bottom: 1px solid #dee2e6;
      font-weight: 600;
      color: #495057;
      transition: all 0.3s ease;
      flex-shrink: 0; // 防止头部被压缩
      
      &:hover {
        background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
      }
    }
    
    :deep(.t-collapse__content) {
      padding: 0;
      background-color: #fff;
      flex: 1; // 占据可用空间
      overflow-y: auto; // 内容区域可滚动
      min-height: 0; // 重要：允许flex收缩
      
      // 内容区滚动条样式
      &::-webkit-scrollbar {
        width: 4px;
      }
      
      &::-webkit-scrollbar-track {
        background: transparent;
      }
      
      &::-webkit-scrollbar-thumb {
        background: #d1d5db;
        border-radius: 2px;
        
        &:hover {
          background: #9ca3af;
        }
      }
    }
  }

  // 节点卡片容器
  .node-cards-container {
    padding: 12px;
    display: flex;
    flex-direction: column;
    gap: 8px;
    min-height: min-content; // 确保内容不被压缩
    
    // 优化滚动条样式
    &::-webkit-scrollbar {
      width: 6px;
    }
    
    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 3px;
    }
    
    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 3px;
      
      &:hover {
        background: #a8a8a8;
      }
    }
  }

  // 节点卡片样式
  .node-card {
    background: #fff;
    border: 1px solid #e8e8e8;
    border-radius: 8px;
    padding: 12px;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    
    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 0;
      bottom: 0;
      width: 4px;
      background: transparent;
      transition: all 0.3s ease;
    }
    
    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      transform: translateY(-2px);
      border-color: #d0d0d0;
      
      &::before {
        background: var(--td-brand-color);
      }
    }
    
    &.node-card-active {
      border-color: var(--td-brand-color);
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
      transform: translateY(-1px);
      
      &::before {
        background: var(--td-brand-color);
      }
    }
  }

  // 节点卡片头部
  .node-card-header {
    display: flex;
    align-items: center;
    gap: 12px;
  }

  // 节点卡片图标
  .node-card-icon {
    width: 36px;
    height: 36px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    
    :deep(.t-icon) {
      font-size: 18px;
    }
    
    &.center-icon {
      background: linear-gradient(135deg, #059669, #047857);
    }
    
    &.indicator-icon {
      background: linear-gradient(135deg, #4F46E5, #4338CA);
    }
    
    &.secondary-icon {
      background: linear-gradient(135deg, #F59E0B, #D97706);
    }
    
    &.third-icon {
      background: linear-gradient(135deg, #EF4444, #DC2626);
    }
    
    &.fourth-icon {
      background: linear-gradient(135deg, #06B6D4, #0891B2);
    }
  }

  // 节点卡片信息
  .node-card-info {
    flex: 1;
    min-width: 0;
  }

  .node-card-title {
    font-weight: 600;
    color: #333;
    font-size: 14px;
    margin-bottom: 2px;
    word-break: break-all;
  }

  .node-card-type {
    font-size: 12px;
    color: #666;
    margin-bottom: 2px;
  }

  .node-card-parent {
    font-size: 11px;
    color: #999;
  }

  // 节点卡片操作
  .node-card-actions {
    display: flex;
    gap: 4px;
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  .node-card:hover .node-card-actions {
    opacity: 1;
  }

  // 节点卡片描述
  .node-card-description {
    margin-top: 8px;
    padding-top: 8px;
    border-top: 1px dashed #e0e0e0;
    font-size: 12px;
    color: #666;
    line-height: 1.4;
    word-break: break-all;
  }

  // 空状态
  .empty-nodes {
    text-align: center;
    padding: 24px;
    color: #999;
    font-size: 14px;
    background: #f9f9f9;
    border-radius: 6px;
    border: 1px dashed #ddd;
  }

  .detail-panel {
    flex: 1;
    display: flex;
    flex-direction: column;
    background-color: #fff;
    overflow: hidden;
  }

  .detail-container {
    padding: 0;
    flex: 1;
    overflow-y: auto;
  }

  .empty-detail {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #999;
    gap: 16px;
    
    :deep(.t-icon) {
      font-size: 48px;
      color: #ccc;
    }
    
    p {
      font-size: 16px;
    }
  }

  .node-detail-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 24px;
    border-bottom: 1px solid #eee;
    background-color: #f9f9f9;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  }

  .node-detail-title {
    display: flex;
    align-items: center;
  }

  .node-detail-name {
    font-size: 18px;
    font-weight: 600;
    text-shadow: 0 1px 0 rgba(255, 255, 255, 0.7);
  }

  .node-detail-content {
    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 20px;
  }
  
  .node-info-card {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    overflow: hidden;
    transition: all 0.3s;
    
    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
      transform: translateY(-2px);
    }
  }
  
  .info-card-header {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 14px 20px;
    background-color: #f5f7fa;
    border-bottom: 1px solid #eee;
    font-weight: 500;
    color: #333;
    
    :deep(.t-icon) {
      color: var(--td-brand-color);
    }
  }
  
  .info-card-body {
    padding: 16px 20px;
  }
  
  .info-item {
    display: flex;
    margin-bottom: 16px;
    padding-bottom: 16px;
    border-bottom: 1px dashed #eee;
    
    &:last-child {
      margin-bottom: 0;
      padding-bottom: 0;
      border-bottom: none;
    }
  }
  
  .info-label {
    width: 120px;
    color: #666;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 6px;
    
    :deep(.t-icon) {
      font-size: 16px;
      color: #999;
    }
  }
  
  .info-value {
    flex: 1;
    color: #333;
    display: flex;
    align-items: center;
    gap: 6px;
  }
  
  .node-type-dot {
    display: inline-block;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    
    &.node-type-dot-center {
      background-color: #059669;
    }
    
    &.node-type-dot-indicator {
      background-color: #4F46E5;
    }
    
    &.node-type-dot-secondaryIndicator {
      background-color: #F59E0B;
    }
    
    &.node-type-dot-thirdIndicator {
      background-color: #EF4444;
    }
    
    &.node-type-dot-fourthIndicator {
      background-color: #06B6D4;
    }
  }
  
  .description-content {
    line-height: 1.8;
    color: #333;
    white-space: pre-line;
    min-height: 80px;
    padding: 8px 12px;
    background: rgba(0, 0, 0, 0.02);
    border-radius: 4px;
    border-left: 3px solid var(--td-brand-color-light);
  }

  .tree-legend {
    padding: 16px 20px;
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    
    .legend-title {
      display: flex;
      align-items: center;
      margin-bottom: 12px;
      font-weight: 600;
      color: #495057;
      font-size: 14px;
      
      :deep(.t-icon) {
        margin-right: 8px;
      }
    }
    
    .legend-item {
      display: flex;
      align-items: center;
      margin-bottom: 8px;
      padding: 4px 0;
      transition: all 0.2s ease;
      
      &:hover {
        background: rgba(0, 0, 0, 0.03);
        border-radius: 6px;
        padding-left: 8px;
        padding-right: 8px;
      }
      
      .legend-color {
        display: inline-block;
        width: 16px;
        height: 16px;
        border-radius: 50%;
        margin-right: 12px;
        border: 2px solid rgba(255, 255, 255, 0.8);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        
        &.center-gradient {
          background: linear-gradient(135deg, #059669, #047857);
        }
        
        &.indicator-gradient {
          background: linear-gradient(135deg, #4F46E5, #4338CA);
        }
        
        &.secondary-gradient {
          background: linear-gradient(135deg, #F59E0B, #D97706);
        }
        
        &.third-gradient {
          background: linear-gradient(135deg, #EF4444, #DC2626);
        }
        
        &.fourth-gradient {
          background: linear-gradient(135deg, #06B6D4, #0891B2);
        }
      }
      
      .legend-text {
        flex: 1;
        font-size: 13px;
        color: #495057;
        font-weight: 500;
      }
      
      .legend-badge {
        background: linear-gradient(135deg, var(--td-brand-color), var(--td-brand-color-hover));
        color: white;
        padding: 2px 8px;
        border-radius: 12px;
        font-size: 12px;
        font-weight: 600;
        min-width: 20px;
        text-align: center;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }
    }
  }

  .node-content {
    width: 100%;
  }

  .node-main {
    display: flex;
    align-items: center;
    font-size: 14px;
  }

  .node-type-tag {
    &.node-type-center {
      background: linear-gradient(135deg, #059669, #047857);
      color: white;
      border: none;
    }
    
    &.node-type-indicator {
      background: linear-gradient(135deg, #4F46E5, #4338CA);
      color: white;
      border: none;
    }
    
    &.node-type-secondaryIndicator {
      background: linear-gradient(135deg, #F59E0B, #D97706);
      color: white;
      border: none;
    }
    
    &.node-type-thirdIndicator {
      background: linear-gradient(135deg, #EF4444, #DC2626);
      color: white;
      border: none;
    }
    
    &.node-type-fourthIndicator {
      background: linear-gradient(135deg, #06B6D4, #0891B2);
      color: white;
      border: none;
    }
  }

  .node-info {
    margin-top: 6px;
    padding: 8px 10px;
    font-size: 12px;
    background-color: rgba(0, 0, 0, 0.02);
    border-radius: 4px;
    color: #666;
    border-left: 3px solid #ddd;
  }

  .node-data-center .node-info {
    border-left-color: #059669;
  }
  
  .node-data-indicator .node-info {
    border-left-color: #4F46E5;
  }
  
  .node-data-secondaryIndicator .node-info {
    border-left-color: #F59E0B;
  }

  .node-data-thirdIndicator .node-info {
    border-left-color: #EF4444;
  }
  
  .node-data-fourthIndicator .node-info {
    border-left-color: #06B6D4;
  }

  .node-type-text {
    font-weight: 500;
    margin-bottom: 4px;
    color: #555;
  }

  .node-description {
    white-space: normal;
    word-break: break-all;
    line-height: 1.5;
  }

  // 重写树组件样式，完全覆盖默认行为
  :deep(.t-tree) {
    padding: 8px;
    
    .t-tree__item {
      padding: 8px;
      border-radius: 6px;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      border-left: 3px solid transparent;
      margin: 4px 0;
      position: relative;
      transform-origin: left center;
      
      &:hover {
        border-left-color: var(--td-brand-color-light);
        transform: translateX(4px);
      }
      
      &.t-is-active {
        border-left-color: var(--td-brand-color) !important;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        transform: translateX(6px);
      }
    }
    
    .t-tree__node {
      position: relative;
      background: transparent;
    }
    
    // 美化三角图标 - 根据节点类型设置不同颜色
    .t-tree__icon {
      width: 18px;
      height: 18px;
      border-radius: 50%;
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      font-size: 10px;
      margin-right: 8px;
      
      // 默认颜色（通用品牌色）
      background: linear-gradient(135deg, var(--td-brand-color-light), var(--td-brand-color));
      
      &:hover {
        transform: scale(1.15) rotate(10deg);
        box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
      }
      
      &.t-is-active {
        transform: rotate(90deg) scale(1.1);
      }
      
      // 自定义图标内容
      &::before {
        content: '▶';
        font-size: 8px;
        line-height: 1;
        transition: transform 0.3s ease;
      }
      
      &.t-is-active::before {
        transform: rotate(0deg);
        content: '▼';
      }
    }
    
    // 根据节点类型为三角图标设置不同颜色
    .node-data-center .t-tree__icon {
      background: linear-gradient(135deg, #059669, #047857);
      
      &.t-is-active {
        background: linear-gradient(135deg, #047857, #065f46);
      }
    }
    
    .node-data-indicator .t-tree__icon {
      background: linear-gradient(135deg, #4F46E5, #4338CA);
      
      &.t-is-active {
        background: linear-gradient(135deg, #4338CA, #3730A3);
      }
    }
    
    .node-data-secondaryIndicator .t-tree__icon {
      background: linear-gradient(135deg, #F59E0B, #D97706);
      
      &.t-is-active {
        background: linear-gradient(135deg, #D97706, #B45309);
      }
    }
    
    .node-data-thirdIndicator .t-tree__icon {
      background: linear-gradient(135deg, #EF4444, #DC2626);
      
      &.t-is-active {
        background: linear-gradient(135deg, #DC2626, #B91C1C);
      }
    }
    
    .node-data-fourthIndicator .t-tree__icon {
      background: linear-gradient(135deg, #06B6D4, #0891B2);
      
      &.t-is-active {
        background: linear-gradient(135deg, #0891B2, #0E7490);
      }
    }
    
    // 层级缩进和背景色区分 - 每层递增缩进和颜色深度
    .t-tree__children {
      position: relative;
      margin: 2px 0;
      transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
      overflow: hidden;
      
      // 第一层子节点 - 一级指标
      padding-left: 40px;
      background: linear-gradient(90deg, rgba(79, 70, 229, 0.03), transparent 60%);
      border-left: 2px solid rgba(79, 70, 229, 0.1);
      border-radius: 0 8px 8px 0;
      margin-left: 8px;
      
      .t-tree__item {
        transform-origin: left center;
        animation: slideInFromLeft 0.4s ease-out;
        position: relative;
        
        &::before {
          content: '';
          position: absolute;
          left: -20px;
          top: 50%;
          width: 16px;
          height: 2px;
          background: rgba(79, 70, 229, 0.2);
          transform: translateY(-50%);
        }
      }
      
      // 第二层子节点 - 二级指标
      .t-tree__children {
        padding-left: 48px;
        background: linear-gradient(90deg, rgba(245, 158, 11, 0.04), transparent 50%);
        border-left: 2px solid rgba(245, 158, 11, 0.15);
        border-radius: 0 6px 6px 0;
        margin-left: 12px;
        
        .t-tree__item {
          transform-origin: left center;
          animation: slideInFromLeft 0.5s ease-out 0.1s both;
          
          &::before {
            background: rgba(245, 158, 11, 0.2);
          }
        }
        
        // 第三层子节点 - 三级指标
        .t-tree__children {
          padding-left: 56px;
          background: linear-gradient(90deg, rgba(239, 68, 68, 0.05), transparent 40%);
          border-left: 2px solid rgba(239, 68, 68, 0.2);
          border-radius: 0 4px 4px 0;
          margin-left: 16px;
          
          .t-tree__item {
            transform-origin: left center;
            animation: slideInFromLeft 0.6s ease-out 0.2s both;
            
            &::before {
              background: rgba(239, 68, 68, 0.2);
            }
          }
          
          // 第四层子节点 - 四级指标
          .t-tree__children {
            padding-left: 64px;
            background: linear-gradient(90deg, rgba(6, 182, 212, 0.06), transparent 30%);
            border-left: 2px solid rgba(6, 182, 212, 0.25);
            border-radius: 0 2px 2px 0;
            margin-left: 20px;
            
            .t-tree__item {
              transform-origin: left center;
              animation: slideInFromLeft 0.7s ease-out 0.3s both;
              
              &::before {
                background: rgba(6, 182, 212, 0.2);
              }
            }
          }
        }
      }
      
      // 添加层级连接线
      &::before {
        content: '';
        position: absolute;
        left: 16px;
        top: 0;
        bottom: 0;
        width: 2px;
        background: linear-gradient(to bottom, 
          rgba(var(--td-brand-color), 0.3) 0%, 
          rgba(var(--td-brand-color), 0.1) 50%, 
          transparent 100%);
        opacity: 0.6;
        border-radius: 1px;
      }
      
      &.t-tree__children--folded {
        height: 0 !important;
        padding: 0 !important;
        margin: 0 !important;
        opacity: 0 !important;
        visibility: hidden !important;
        pointer-events: none !important;
        transform: translateX(-20px) scale(0.95);
        display: none !important;
      }
    }
    
    // 为不同节点类型的子级连接线设置对应颜色
    .node-data-center .t-tree__children::before {
      background: linear-gradient(to bottom, 
        rgba(5, 150, 105, 0.4) 0%, 
        rgba(5, 150, 105, 0.2) 50%, 
        transparent 100%);
    }
    
    .node-data-indicator .t-tree__children::before {
      background: linear-gradient(to bottom, 
        rgba(79, 70, 229, 0.4) 0%, 
        rgba(79, 70, 229, 0.2) 50%, 
        transparent 100%);
    }
    
    .node-data-secondaryIndicator .t-tree__children::before {
      background: linear-gradient(to bottom, 
        rgba(245, 158, 11, 0.4) 0%, 
        rgba(245, 158, 11, 0.2) 50%, 
        transparent 100%);
    }
    
    .node-data-thirdIndicator .t-tree__children::before {
      background: linear-gradient(to bottom, 
        rgba(239, 68, 68, 0.4) 0%, 
        rgba(239, 68, 68, 0.2) 50%, 
        transparent 100%);
    }
    
    // 节点标签样式优化 - 增加层级感
    .t-tree__label {
      padding: 6px 12px;
      border-radius: 6px;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      position: relative;
      
      &:hover {
        background-color: rgba(var(--td-brand-color), 0.08);
        transform: translateX(2px);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }
      
      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 0;
        bottom: 0;
        width: 3px;
        background: transparent;
        border-radius: 0 2px 2px 0;
        transition: all 0.3s ease;
      }
      
      &:hover::before {
        background: var(--td-brand-color);
      }
    }
  }
  
  // 添加展开动画关键帧
  @keyframes slideInFromLeft {
    0% {
      opacity: 0;
      transform: translateX(-30px) scale(0.9);
    }
    50% {
      opacity: 0.7;
      transform: translateX(-5px) scale(0.98);
    }
    100% {
      opacity: 1;
      transform: translateX(0) scale(1);
    }
  }

  // 使用高级CSS选择器保证列表最后一项无边距
  :deep(.t-tree__node:last-of-type) {
    margin-bottom: 0 !important;
  }
  
  // 添加脉冲动画给活跃节点
  @keyframes pulse {
    0%, 100% {
      opacity: 1;
      transform: scale(1);
    }
    50% {
      opacity: 0.8;
      transform: scale(1.05);
    }
  }
 
  // 链接相关样式
  .links-container {
    padding: 20px;
  }

  .link-type {
    margin-bottom: 20px;

    h4 {
      margin-bottom: 12px;
      font-weight: 600;
      color: #333;
      display: flex;
      align-items: center;
      
      &::before {
        content: '';
        display: inline-block;
        width: 4px;
        height: 16px;
        background-color: var(--td-brand-color);
        margin-right: 8px;
        border-radius: 2px;
      }
    }
  }

  .links-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .link-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px;
    background-color: #fff;
    border-radius: 6px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    border: 1px solid #eee;
    transition: all 0.2s;

    &:hover {
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
      border-color: #ddd;
    }
  }

  .link-info {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .link-target, .link-source {
    font-weight: 500;
  }

  .link-description {
    color: #666;
    margin-left: 8px;
  }

  .empty-links {
    padding: 20px;
    text-align: center;
    color: #999;
    background-color: #f9f9f9;
    border-radius: 6px;
    border: 1px dashed #ddd;
  }

  .add-link {
    margin-top: 20px;
    display: flex;
    justify-content: center;
  }

  :deep(.t-tabs) {
    height: 100%;
    
    .t-tabs__content {
      padding: 0;
      height: calc(100% - 48px);
      overflow-y: auto;
    }
    
    .t-tabs__nav {
      padding: 0 20px;
      background-color: #f5f7fa;
    }
    
    .t-tabs__nav-item {
      font-weight: 500;
    }
  }

  .ml-2 {
    margin-left: 8px;
  }

  // 对话框内容布局
  .dialog-content {
    display: flex;
    gap: 24px;
  }
  
  // 节点预览样式
  .node-preview {
    width: 180px;
    margin-left: auto;
    
    .preview-title {
      font-size: 14px;
      color: #666;
      margin-bottom: 12px;
      text-align: center;
    }
    
    .preview-content {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      height: 260px;
      background-color: #f5f7fa;
      border-radius: 8px;
      padding: 16px;
      position: relative;
    }
    
    .actual-preview {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 160px;
      width: 100%;
      position: relative;
    }
    
    .preview-node {
      border-radius: 50%;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      color: white;
      text-align: center;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.15);
      transition: all 0.3s;
      
      &:hover {
        transform: scale(1.05);
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.25);
      }
      
      &.preview-node-center {
        width: 60px;
        height: 60px;
        background-color: #059669;
      }
      
      &.preview-node-indicator {
        width: 40px;
        height: 40px;
        background-color: #4F46E5;
      }
      
      &.preview-node-secondaryIndicator {
        width: 30px;
        height: 30px;
        background-color: #F59E0B;
      }
      
      &.preview-node-thirdIndicator {
        width: 25px;
        height: 25px;
        background-color: #EF4444;
      }
      
      &.preview-node-fourthIndicator {
        width: 20px;
        height: 20px;
        background-color: #06B6D4;
      }
    }
    
    .node-label {
      position: absolute;
      bottom: 20px;
      font-size: 13px;
      font-weight: 600;
      color: #333;
      text-align: center;
      max-width: 140px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    
    .node-type-label {
      position: absolute;
      top: 20px;
      font-size: 12px;
      color: #666;
    }
    
    .preview-legend {
      margin-top: 20px;
      padding-top: 20px;
      width: 100%;
      border-top: 1px dashed #ddd;
      
      .legend-item {
        display: flex;
        align-items: center;
        margin-bottom: 8px;
        font-size: 12px;
        
        .legend-color {
          display: inline-block;
          width: 12px;
          height: 12px;
          border-radius: 50%;
          margin-right: 8px;
        }
      }
      
      .legend-size {
        font-size: 12px;
        color: #666;
        margin-top: 4px;
      }
    }
  }
  
  // 删除对话框样式
  .delete-content {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 8px;
    
    .delete-icon {
      font-size: 28px;
      color: var(--td-error-color);
    }
    
    p {
      margin: 0;
      font-size: 14px;
      line-height: 1.6;
    }
  }
  
  // 单选按钮图标样式
  .radio-with-icon {
    display: flex;
    align-items: center;
    gap: 4px;
    
    :deep(.t-icon) {
      font-size: 14px;
    }
  }
}

// 对话框样式优化
:deep(.t-dialog) {
  .t-dialog__header {
    padding: 16px 24px;
    border-bottom: 1px solid #eee;
  }
  
  .t-dialog__body {
    padding: 24px;
  }
  
  .t-dialog__footer {
    padding: 16px 24px;
    border-top: 1px solid #eee;
  }
  
  .t-form__item {
    margin-bottom: 20px;
  }
}

// 关联方向指示器样式
.link-direction-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16px;
  background-color: #f5f7fa;
  border-radius: 6px;
}

.link-node {
  padding: 8px 16px;
  border-radius: 4px;
  background-color: #fff;
  border: 1px solid #ddd;
  min-width: 120px;
  text-align: center;
  font-weight: 500;
}

.link-current-node {
  background-color: var(--td-brand-color-light);
  border-color: var(--td-brand-color);
  color: var(--td-brand-color);
}

.link-arrow {
  margin: 0 16px;
  font-size: 24px;
  color: #666;
}

// 动画效果
@keyframes pulse {
  0% {
    opacity: 0.6;
    transform: translate(-50%, -50%) scale(1);
  }
  50% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1.1);
  }
  100% {
    opacity: 0.6;
    transform: translate(-50%, -50%) scale(1);
  }
}
</style>


<template>
  <div class="knowledge-graph-container">
    <t-card title="专业指标知识图谱">
      <template #actions>
        <t-space>
          <t-button theme="primary" @click="showMajorSelect">选择专业</t-button>
          <t-button theme="primary" @click="() => expandAll()">全部展开</t-button>
          <t-button theme="primary" @click="exportImage">导出图片</t-button>
          <t-button theme="default" @click="resetGraph">重置</t-button>
        </t-space>
      </template>

      <div class="graph-container" ref="graphContainer"></div>

      <!-- 专业选择弹窗 -->
      <t-dialog
        v-model:visible="majorSelectVisible"
        header="选择专业"
        :width="500"
        :footer="false"
      >
        <t-space direction="vertical" style="width: 100%">
          <!-- 学院和搜索一行排列 -->
          <t-row :gutter="[16, 16]">
            <t-col :span="6">
              <t-form-item label="选择学院" style="margin-bottom: 0">
                <t-select
                  v-model="selectedCollegeId"
                  placeholder="请选择学院"
                  clearable
                  :options="collegeSelectOptions"
                  @change="handleCollegeChange"
                />
              </t-form-item>
            </t-col>
            <t-col :span="6">
              <t-form-item label="搜索专业" style="margin-bottom: 0">
                <t-input
                  v-model="searchKeyword"
                  placeholder="搜索专业名称"
                  clearable
                  :suffix-icon="() => h(SearchIcon)"
                />
              </t-form-item>
            </t-col>
          </t-row>

          <!-- 专业列表 -->
          <t-list :split="true" class="major-list">
            <template v-if="filteredMajors.length">
              <t-list-item
                v-for="major in filteredMajors"
                :key="major.id"
                @click="handleSelectMajor(major)"
              >
                <t-list-item-meta :title="major.name" :description="major.college" />
              </t-list-item>
            </template>
            <template v-else>
              <t-empty description="请先选择学院或搜索专业名称" />
            </template>
          </t-list>
        </t-space>
      </t-dialog>
    </t-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick, computed, h } from 'vue';
import { MessagePlugin } from 'tdesign-vue-next';
import * as echarts from 'echarts';
import type { EChartsOption, ECharts } from 'echarts';
import { SearchIcon } from 'tdesign-icons-vue-next';

// 自定义图谱节点类型
interface GraphNode {
  name: string;
  symbolSize: number;
  itemStyle: { color: string };
  x: number;
  y: number;
  nodeType: string;
  id?: string;
  parentId?: string;
  majorId?: string;
  description?: string;
  expanded?: boolean;
  objective?: string;
}

// 图表实例
let chartInstance: ECharts | null = null;

// 容器引用
const graphContainer = ref<HTMLElement | null>(null);

// 专业选择弹窗
const majorSelectVisible = ref(false);
const searchKeyword = ref('');

// 当前选中的专业
const currentMajor = ref<any>(null);

// 虚拟数据 - 学院列表和专业列表
const collegeList = reactive([
  { id: 1, name: '计算机学院' },
  { id: 2, name: '电子工程学院' },
  { id: 3, name: '机械工程学院' },
  { id: 4, name: '土木工程学院' },
  { id: 5, name: '建筑学院' },
  { id: 6, name: '经济管理学院' },
]);

// 虚拟数据 - 专业列表
const majorList = reactive([
  { id: '1', name: '软件工程', collegeId: 1, college: '计算机学院' },
  { id: '2', name: '计算机科学与技术', collegeId: 1, college: '计算机学院' },
  { id: '3', name: '人工智能', collegeId: 1, college: '计算机学院' },
  { id: '4', name: '数据科学与大数据技术', collegeId: 1, college: '计算机学院' },
  { id: '5', name: '电子信息工程', collegeId: 2, college: '电子工程学院' },
  { id: '6', name: '通信工程', collegeId: 2, college: '电子工程学院' },
  { id: '7', name: '自动化', collegeId: 2, college: '电子工程学院' },
  { id: '8', name: '机械工程', collegeId: 3, college: '机械工程学院' },
  { id: '9', name: '机械电子工程', collegeId: 3, college: '机械工程学院' },
  { id: '10', name: '土木工程', collegeId: 4, college: '土木工程学院' },
  { id: '11', name: '建筑学', collegeId: 5, college: '建筑学院' },
  { id: '12', name: '城市规划', collegeId: 5, college: '建筑学院' },
  { id: '13', name: '工商管理', collegeId: 6, college: '经济管理学院' },
  { id: '14', name: '会计学', collegeId: 6, college: '经济管理学院' },
  { id: '15', name: '经济学', collegeId: 6, college: '经济管理学院' },
]);

// 虚拟数据 - 毕业要求
const graduationRequirements = [
  { id: '1', name: '工程知识', description: '具备扎实的数学、自然科学、工程基础和专业知识，能够将其用于解决复杂工程问题。' },
  { id: '2', name: '问题分析', description: '能够应用数学、自然科学和工程科学的基本原理，识别、表达、并通过文献研究分析复杂工程问题，以获得有效结论。' },
  { id: '3', name: '设计/开发解决方案', description: '能够设计针对复杂工程问题的解决方案，设计满足特定需求的系统、单元或工艺流程，并能够在设计环节中体现创新意识，考虑社会、健康、安全、法律、文化以及环境等因素。' },
  { id: '4', name: '研究', description: '能够基于科学原理并采用科学方法对复杂工程问题进行研究，包括设计实验、分析与解释数据、并通过信息综合得到合理有效的结论。' },
  { id: '5', name: '使用现代工具', description: '能够针对复杂工程问题，开发、选择与使用恰当的技术、资源、现代工程工具和信息技术工具，包括对复杂工程问题的预测与模拟，并能够理解其局限性。' },
  { id: '6', name: '工程与社会', description: '能够基于工程相关背景知识进行合理分析，评价专业工程实践和复杂工程问题解决方案对社会、健康、安全、法律以及文化的影响，并理解应承担的责任。' },
  { id: '7', name: '环境和可持续发展', description: '能够理解和评价针对复杂工程问题的专业工程实践对环境、社会可持续发展的影响。' },
  { id: '8', name: '职业规范', description: '具有人文社会科学素养、社会责任感，能够在工程实践中理解并遵守工程职业道德和规范，履行责任。' },
  { id: '9', name: '个人和团队', description: '能够在多学科背景下的团队中承担个体、团队成员以及负责人的角色。' },
  { id: '10', name: '沟通', description: '能够就复杂工程问题与业界同行及社会公众进行有效沟通和交流，包括撰写报告和设计文稿、陈述发言、清晰表达或回应指令，并具备一定的国际视野，能够在跨文化背景下进行沟通和交流。' },
  { id: '11', name: '项目管理', description: '理解并掌握工程管理原理与经济决策方法，并能在多学科环境中应用。' },
  { id: '12', name: '终身学习', description: '具有自主学习和终身学习的意识，有不断学习和适应发展的能力。' }
];

// 二级指标模拟数据生成函数
const generateSecondaryIndicators = (requirementId: string, majorId: string) => {
  // 根据毕业要求和专业ID生成不同的二级指标
  const count = 3 + Math.floor(Math.random() * 3); // 每个一级指标有3-5个二级指标
  const indicators = [];

  for (let i = 1; i <= count; i++) {
    indicators.push({
      id: `${requirementId}-${i}`,
      name: `指标${requirementId}.${i}`,
      description: `${majorList.find(m => m.id === majorId)?.name}专业的毕业要求${requirementId}的细化指标${i}`,
    });
  }

  return indicators;
};

// 课程目标模拟数据生成函数
const generateCourseObjectives = (indicatorId: string, majorId: string) => {
  const count = 2 + Math.floor(Math.random() * 3); // 每个二级指标有2-4个课程目标
  const courses = [];

  for (let i = 1; i <= count; i++) {
    courses.push({
      id: `${indicatorId}-${i}`,
      name: `课程${i}`,
      objective: `通过本课程的学习，使学生能够掌握${indicatorId}相关的知识和技能`,
    });
  }

  return courses;
};

// 学院选择
const selectedCollegeId = ref<number | null>(null);

// 学院选择选项
const collegeSelectOptions = computed(() => {
  return collegeList.map(college => ({
    label: college.name,
    value: college.id
  }));
});

// 处理学院变更
const handleCollegeChange = (value: string | number | boolean | object) => {
  selectedCollegeId.value = value as number;
  searchKeyword.value = '';
};

// 过滤后的专业列表
const filteredMajors = computed(() => {
  let result = majorList;

  // 如果选择了学院，按学院过滤
  if (selectedCollegeId.value) {
    result = result.filter(major => major.collegeId === selectedCollegeId.value);
  }

  // 如果有搜索关键词，按名称或学院名称过滤
  if (searchKeyword.value) {
    result = result.filter(major =>
      major.name.includes(searchKeyword.value) ||
      major.college.includes(searchKeyword.value)
    );
  }

  return result;
});

// 添加类型断言函数
const asGraphOption = (option: any): EChartsOption => option as EChartsOption;
const asGraphNode = (data: any): GraphNode => data as GraphNode;
const asGraphNodes = (data: any[]): GraphNode[] => data as GraphNode[];

// 初始化图表
const initChart = () => {
  if (!graphContainer.value) return;

  // 创建图表实例
  chartInstance = echarts.init(graphContainer.value);

  // 设置默认配置
  const option = asGraphOption({
    title: {
      text: '点击中心选择专业',
      top: 'center',
      left: 'center',
    },
    tooltip: {
      trigger: 'item',
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      borderColor: '#e6e6e6',
      borderWidth: 1,
      borderRadius: 8,
      padding: [12, 16],
      textStyle: {
        color: '#333',
        fontSize: 13,
        lineHeight: 18
      },
      extraCssText: 'box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12); backdrop-filter: blur(8px);',
      formatter: function(params: any) {
        if (params.dataType === 'node') {
          // 节点tooltip
          const data = params.data;
          const nodeTypeMap: Record<string, string> = {
            'center': '中心节点',
            'indicator': '一级指标',
            'secondaryIndicator': '二级指标',
            'thirdIndicator': '三级指标',
            'fourthIndicator': '四级指标'
          };
          
          const nodeTypeName = nodeTypeMap[data.nodeType] || '专业选择';
          const description = data.description || data.objective || '点击选择专业以展开知识图谱';
          
          return `
            <div style="max-width: 300px;">
              <div style="display: flex; align-items: center; margin-bottom: 8px;">
                <div style="width: 12px; height: 12px; border-radius: 50%; background: ${data.itemStyle.color}; margin-right: 8px;"></div>
                <strong style="font-size: 14px; color: #1f2937;">${data.name}</strong>
              </div>
              <div style="margin-bottom: 6px;">
                <span style="color: #6b7280; font-size: 12px;">类型：</span>
                <span style="color: #374151; font-weight: 500;">${nodeTypeName}</span>
              </div>
              <div style="line-height: 1.5; color: #4b5563; font-size: 12px;">
                <span style="color: #6b7280;">描述：</span><br/>
                <span style="color: #374151;">${description}</span>
              </div>
            </div>
          `;
        } else if (params.dataType === 'edge') {
          // 连接线tooltip
          const data = params.data;
          const description = data.description || '暂无描述';
          
          return `
            <div style="max-width: 280px;">
              <div style="display: flex; align-items: center; margin-bottom: 8px;">
                <div style="width: 20px; height: 2px; background: ${data.lineStyle?.color || '#999'}; margin-right: 8px; ${
                  data.lineStyle?.type === 'dashed' ? 'background: repeating-linear-gradient(90deg, ' + (data.lineStyle?.color || '#999') + ' 0px, ' + (data.lineStyle?.color || '#999') + ' 3px, transparent 3px, transparent 6px);' : ''
                }"></div>
                <strong style="font-size: 14px; color: #1f2937;">连接关系</strong>
              </div>
              <div style="line-height: 1.5; color: #4b5563; font-size: 12px;">
                <span style="color: #6b7280;">描述：</span><br/>
                <span style="color: #374151;">${description}</span>
              </div>
            </div>
          `;
        }
      },
    },
    series: [
      {
        type: 'graph',
        layout: 'force',
        data: [
          {
            name: '选择专业',
            symbolSize: 70,
            itemStyle: {
              color: '#5470c6'
            },
            x: 0,
            y: 0,
            // 自定义属性
            nodeType: 'center'
          }
        ],
        links: [],
        roam: true,
        label: {
          show: true,
          position: 'inside',
          formatter: '{b}',
          fontSize: 12
        },
        lineStyle: {
          color: 'source',
          curveness: 0.3,
          width: 2
        },
        emphasis: {
          focus: 'adjacency',
          lineStyle: {
            width: 6
          }
        },
        force: {
          repulsion: 300,
          edgeLength: 100
        }
      }
    ],
    graphic: [
      {
        type: 'group',
        left: 20,
        top: 20,
        children: [
          {
            type: 'rect',
            z: 100,
            left: 0,
            top: 0,
            shape: {
              width: 180,
              height: 100  // 增加高度以容纳第三条线
            },
            style: {
              fill: 'rgba(255, 255, 255, 0.9)',
              stroke: '#ddd',
              lineWidth: 1
            }
          },
          {
            type: 'text',
            z: 100,
            left: 10,
            top: 10,
            style: {
              text: '连接线说明',
              textAlign: 'left',
              fontSize: 14,
              fontWeight: 'bold',
              fill: '#333'
            }
          },
          {
            type: 'line',
            z: 100,
            left: 10,
            top: 35,
            shape: {
              x1: 0,
              y1: 0,
              x2: 30,
              y2: 0
            },
            style: {
              stroke: '#5470c6',
              lineWidth: 2,
              lineDash: [0]
            }
          },
          {
            type: 'text',
            z: 100,
            left: 45,
            top: 30,
            style: {
              text: '出站连接（实线）',
              textAlign: 'left',
              fontSize: 12,
              fill: '#666'
            }
          },
          {
            type: 'line',
            z: 100,
            left: 10,
            top: 55,
            shape: {
              x1: 0,
              y1: 0,
              x2: 30,
              y2: 0
            },
            style: {
              stroke: '#ee6666',
              lineWidth: 2,
              lineDash: [5, 5]
            }
          },
          {
            type: 'text',
            z: 100,
            left: 45,
            top: 50,
            style: {
              text: '入站连接（虚线）',
              textAlign: 'left',
              fontSize: 12,
              fill: '#666'
            }
          },
          {
            type: 'line',
            z: 100,
            left: 10,
            top: 75,
            shape: {
              x1: 0,
              y1: 0,
              x2: 30,
              y2: 0
            },
            style: {
              stroke: '#8b5cf6',
              lineWidth: 2,
              lineDash: [5, 5]
            }
          },
          {
            type: 'text',
            z: 100,
            left: 45,
            top: 70,
            style: {
              text: '其他关联（紫色虚线）',
              textAlign: 'left',
              fontSize: 12,
              fill: '#666'
            }
          }
        ]
      }
    ]
  });

  // 应用配置
  chartInstance.setOption(option);

  // 添加点击事件
  chartInstance.on('click', 'series.graph.data', (params) => {
    const data = asGraphNode(params.data);

    // 根据节点类型处理点击事件
    if (data.nodeType === 'center') {
      // 点击中心节点，打开专业选择弹窗
      selectedCollegeId.value = null;
      searchKeyword.value = '';
      majorSelectVisible.value = true;
    } else if (data.nodeType === 'requirement') {
      // 点击毕业要求节点，展开/收起二级指标
      toggleRequirementChildren(data.id as string);
    } else if (data.nodeType === 'indicator') {
      // 点击二级指标节点，展开/收起课程目标
      toggleIndicatorChildren(data.id as string);
    }
  });

  // 窗口大小变化时调整图表大小
  window.addEventListener('resize', () => {
    chartInstance?.resize();
  });
};

// 选择专业
const handleSelectMajor = (major: any) => {
  currentMajor.value = major;
  majorSelectVisible.value = false;

  // 更新图表
  updateChartForMajor(major);
};

// 根据选中的专业更新图表
const updateChartForMajor = (major: any) => {
  if (!chartInstance) return;

  const graphData: GraphNode[] = [
    {
      name: major.name,
      symbolSize: 60,
      itemStyle: {
        color: '#91cc75'
      },
      x: 0,
      y: 0,
      // 自定义属性
      nodeType: 'center',
      id: 'center'
    }
  ];

  const links: any[] = [];

  // 添加毕业要求节点
  graduationRequirements.forEach((req, index) => {
    const angle = (Math.PI * 2 / graduationRequirements.length) * index;
    const x = Math.cos(angle) * 200;
    const y = Math.sin(angle) * 200;

    graphData.push({
      name: req.name,
      symbolSize: 40,
      itemStyle: {
        color: '#5470c6'
      },
      x,
      y,
      // 自定义属性
      nodeType: 'requirement',
      id: req.id,
      majorId: major.id,
      description: req.description,
      expanded: false
    });

    // 出站连接使用实线
    links.push({
      source: 'center',
      target: req.id,
      lineStyle: {
        width: 3,
        type: 'solid', // 出站连接实线
        color: '#5470c6'
      },
      description: `从中心节点到${req.name}的连接`
    });
  });

  // 更新图表
  chartInstance.setOption(asGraphOption({
    title: {
      text: `${major.name} - 知识图谱`,
    },
    tooltip: {
      trigger: 'item',
      formatter: function(params: any) {
        const data = params.data;
        let result = data.name;

        if (data.description) {
          result += '<br/>描述: ' + data.description;
        }

        if (data.objective) {
          result += '<br/>目标: ' + data.objective;
        }

        return result;
      }
    },
    series: [
      {
        type: 'graph',
        data: graphData,
        links: links
      }
    ]
  }));
};

// 切换毕业要求节点的子节点（二级指标）
const toggleRequirementChildren = (requirementId: string) => {
  if (!chartInstance || !currentMajor.value) return;

  // 获取当前图表数据
  const option = chartInstance.getOption() as any;
  let graphData = asGraphNodes(option.series[0].data);
  let links = option.series[0].links;

  // 查找当前节点
  const nodeIndex = graphData.findIndex((node: any) => node.id === requirementId);
  if (nodeIndex === -1) return;

  const node = graphData[nodeIndex];

  // 切换展开状态
  node.expanded = !node.expanded;

  if (node.expanded) {
    // 生成二级指标节点
    const indicators = generateSecondaryIndicators(requirementId, currentMajor.value.id);

    // 计算二级指标节点的位置
    indicators.forEach((indicator, index) => {
      const baseAngle = Math.atan2(node.y, node.x);
      const spreadAngle = 0.5; // 扇形角度范围
      const angle = baseAngle - spreadAngle/2 + (spreadAngle / (indicators.length - 1 || 1)) * index;
      const distance = 100;
      const x = node.x + Math.cos(angle) * distance;
      const y = node.y + Math.sin(angle) * distance;

      // 添加二级指标节点
      graphData.push({
        name: indicator.name,
        symbolSize: 30,
        itemStyle: {
          color: '#fac858'
        },
        x: x,
        y: y,
        // 自定义属性
        nodeType: 'indicator',
        id: indicator.id,
        parentId: requirementId,
        majorId: currentMajor.value.id,
        description: indicator.description,
        expanded: false
      });

      // 添加连接线
      links.push({
        source: requirementId,
        target: indicator.id,
        lineStyle: {
          width: 2
        },
        description: `从${node.name}到${indicator.name}的指标连接`
      });
    });
  } else {
    // 移除二级指标节点及其课程目标节点
    const indicatorNodes = graphData.filter((node: any) =>
      node.parentId === requirementId && node.nodeType === 'indicator'
    );

    // 获取所有需要移除的节点ID
    const removeIds = new Set<string>();

    indicatorNodes.forEach((node: any) => {
      removeIds.add(node.id);

      // 查找该指标关联的课程目标节点
      const courseNodes = graphData.filter((n: any) =>
        n.parentId === node.id && n.nodeType === 'course'
      );

      courseNodes.forEach((course: any) => {
        removeIds.add(course.id);
      });
    });

    // 移除节点
    graphData = graphData.filter((node: any) => !removeIds.has(node.id));

    // 移除连接线
    links = links.filter((link: any) =>
      !removeIds.has(link.source) && !removeIds.has(link.target)
    );
  }

  // 更新图表
  chartInstance.setOption({
    series: [
      {
        type: 'graph',
        data: graphData,
        links: links
      }
    ]
  });
};

// 切换二级指标节点的子节点（课程目标）
const toggleIndicatorChildren = (indicatorId: string) => {
  if (!chartInstance || !currentMajor.value) return;

  // 获取当前图表数据
  const option = chartInstance.getOption() as any;
  let graphData = asGraphNodes(option.series[0].data);
  let links = option.series[0].links;

  // 查找当前节点
  const nodeIndex = graphData.findIndex((node: any) => node.id === indicatorId);
  if (nodeIndex === -1) return;

  const node = graphData[nodeIndex];

  // 切换展开状态
  node.expanded = !node.expanded;

  if (node.expanded) {
    // 生成课程目标节点
    const courses = generateCourseObjectives(indicatorId, currentMajor.value.id);

    // 计算课程目标节点的位置
    courses.forEach((course, index) => {
      const baseAngle = Math.atan2(node.y, node.x);
      const spreadAngle = 0.5; // 扇形角度范围
      const angle = baseAngle - spreadAngle/2 + (spreadAngle / (courses.length - 1 || 1)) * index;
      const distance = 80;
      const x = node.x + Math.cos(angle) * distance;
      const y = node.y + Math.sin(angle) * distance;

      // 添加课程目标节点
      graphData.push({
        name: course.name,
        symbolSize: 25,
        itemStyle: {
          color: '#ee6666'
        },
        x: x,
        y: y,
        // 自定义属性
        nodeType: 'course',
        id: course.id,
        parentId: indicatorId,
        objective: course.objective
      });

      // 添加连接线
      links.push({
        source: indicatorId,
        target: course.id,
        lineStyle: {
          width: 1,
          type: 'dashed', // 入站连接虚线
          color: '#ee6666'
        }
      });
    });
  } else {
    // 移除课程目标节点
    graphData = graphData.filter((node: any) =>
      !(node.parentId === indicatorId && node.nodeType === 'course')
    );

    // 移除连接线
    links = links.filter((link: any) =>
      !(link.source === indicatorId && graphData.findIndex((node: any) => node.id === link.target) === -1)
    );
  }

  // 更新图表
  chartInstance.setOption({
    series: [
      {
        type: 'graph',
        data: graphData,
        links: links
      }
    ]
  });
};

// 导出图片
const exportImage = () => {
  if (!chartInstance) return;

  try {
    const url = chartInstance.getDataURL({
      pixelRatio: 2,
      backgroundColor: '#fff'
    });

    const link = document.createElement('a');
    link.download = `专业指标知识图谱_${currentMajor.value ? currentMajor.value.name : '未命名'}_${new Date().getTime()}.png`;
    link.href = url;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    MessagePlugin.success('导出成功');
  } catch (error) {
    console.error('导出图片失败:', error);
    MessagePlugin.error('导出失败');
  }
};

// 重置图表
const resetGraph = () => {
  currentMajor.value = null;
  selectedCollegeId.value = null;

  if (chartInstance) {
    chartInstance.dispose();
  }

  nextTick(() => {
    initChart();
  });
};

// 全部展开知识图谱节点
const expandAll = async (showMessages = true) => {
  if (!chartInstance || !currentMajor.value) {
    if (showMessages) {
      MessagePlugin.warning('请先选择专业');
    }
    return;
  }

  if (showMessages) {
    MessagePlugin.info('正在展开所有节点，请稍候...');
  }

  const option = chartInstance.getOption() as any;
  const graphData = asGraphNodes(option.series[0].data);

  // 找到所有毕业要求节点并展开
  const requirementNodes = graphData.filter((node: GraphNode) =>
    node.nodeType === 'requirement'
  );

  // 依次展开所有毕业要求节点
  if (requirementNodes.length) {
    for (const node of requirementNodes) {
      if (node.id && !node.expanded) {
        toggleRequirementChildren(node.id);
        await new Promise(resolve => setTimeout(resolve, 200));
      }
    }

    // 展开所有二级指标节点
    const updatedOption = chartInstance.getOption() as any;
    const updatedGraphData = asGraphNodes(updatedOption.series[0].data);
    const indicatorNodes = updatedGraphData.filter((n: GraphNode) =>
      n.nodeType === 'indicator'
    );

    for (const indicator of indicatorNodes) {
      if (indicator.id && !indicator.expanded) {
        toggleIndicatorChildren(indicator.id);
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }

    // 调整图表位置以适应所有节点
    chartInstance.setOption({
      series: [{
        type: 'graph',
        zoom: 0.7,
        center: [0, 0]
      }]
    });

    if (showMessages) {
      MessagePlugin.success('展开完成');
    }
  }
};

// 显示专业选择弹窗
const showMajorSelect = () => {
  majorSelectVisible.value = true;
};

// 初始化
onMounted(() => {
  nextTick(() => {
    initChart();

    // 在图表初始化后自动选择"软件工程"专业
    setTimeout(() => {
      // 自动选择计算机学院
      selectedCollegeId.value = 1;

      // 然后选择软件工程专业
      const softwareEngineering = majorList.find(m => m.name === '软件工程');
      if (softwareEngineering) {
        handleSelectMajor(softwareEngineering);

        // 自动展开所有节点
        setTimeout(() => {
          expandAll(false);
        }, 500);
      }
    }, 300);
  });
});
</script>

<style scoped lang="less">
.knowledge-graph-container {
  padding: 20px;
  background-color: var(--td-bg-color-container);
  border-radius: var(--td-radius-medium);

  .graph-container {
    width: 100%;
    height: 75vh;
    margin-top: 20px;
  }

  .major-list {
    max-height: 300px;
    overflow-y: auto;
    margin-top: 8px;

    :deep(.t-list-item) {
      cursor: pointer;
      transition: background-color 0.2s;

      &:hover {
        background-color: var(--td-bg-color-container-hover);
      }
    }
  }
}
</style>

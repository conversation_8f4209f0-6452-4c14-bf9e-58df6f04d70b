<template>
  <div class="knowledge-graph-container">
    <t-card class="knowledge-graph-card">
      <div class="kg-header">
        <div class="kg-title">
          <div class="title-icon-wrapper">
            <t-icon name="mind-mapping" size="32px" />
            <div class="icon-glow"></div>
          </div>
          <div class="title-content">
            <h2>学风知识图谱</h2>
            <div class="title-subtitle">点击节点展开多层级指标体系</div>
          </div>
        </div>
        <div class="kg-actions">
          <t-space size="large">
            <t-button theme="primary" variant="outline" @click="() => expandAll()">
              <template #icon><t-icon name="view-list" /></template>
              全部展开
            </t-button>
            <t-button theme="success" variant="outline" @click="exportImage">
              <template #icon><t-icon name="download" /></template>
              导出图片
            </t-button>
            <t-button theme="default" variant="outline" @click="resetGraph">
              <template #icon><t-icon name="refresh" /></template>
              重置视图
            </t-button>
          </t-space>
        </div>
      </div>

      <div class="graph-container" ref="graphContainer">
        <div class="graph-overlay" v-if="loading">
          <div class="loading-indicator">
            <t-loading size="large" text="正在加载知识图谱..." />
          </div>
        </div>
      </div>
      
      <div class="graph-legend">
        <div class="legend-title">
          <t-icon name="palette" />
          <span>节点类型说明</span>
        </div>
        <div class="legend-items">
          <div class="legend-item">
            <div class="legend-dot center-node"></div>
            <span>中心节点</span>
          </div>
          <div class="legend-item">
            <div class="legend-dot indicator-node"></div>
            <span>一级指标</span>
          </div>
          <div class="legend-item">
            <div class="legend-dot secondary-node"></div>
            <span>二级指标</span>
          </div>
          <div class="legend-item">
            <div class="legend-dot third-node"></div>
            <span>三级指标</span>
          </div>
          <div class="legend-item">
            <div class="legend-dot fourth-node"></div>
            <span>四级指标</span>
          </div>
        </div>
        
        <div class="legend-title" style="margin-top: 20px;">
          <t-icon name="link" />
          <span>连接线说明</span>
        </div>
        <div class="legend-items">
          <div class="legend-item">
            <div class="legend-line solid-line"></div>
            <span>出站连接（父→子）</span>
          </div>
          <div class="legend-item">
            <div class="legend-line dashed-line"></div>
            <span>入站连接（子→父）</span>
          </div>
          <div class="legend-item">
            <div class="legend-line dotted-line"></div>
            <span>其他关联（紫色虚线）</span>
          </div>
        </div>
      </div>
    </t-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick } from 'vue';
import { MessagePlugin } from 'tdesign-vue-next';
import * as echarts from 'echarts';
import type { EChartsOption, ECharts } from 'echarts';
import {
  getKnowledgeGraph,
  type GraphNode as ApiGraphNode,
  type GraphLink,
  type KnowledgeGraphData
} from '@/api/graph/nodes';

// 自定义图表选项接口
interface GraphOption {
  series?: Array<{
    data?: any[];
    links?: any[];
    [key: string]: any;
  }>;
  [key: string]: any;
}

// 自定义图谱节点类型
interface GraphNode {
  name: string;
  symbolSize: number;
  itemStyle: { color: string };
  x: number;
  y: number;
  nodeType: string;
  id?: string;
  parentId?: string;
  description?: string;
  expanded?: boolean;
}

// 图表实例
let chartInstance: ECharts | null = null;

// 容器引用
const graphContainer = ref<HTMLElement | null>(null);
const loading = ref(false);

// 添加类型断言函数
const asGraphOption = (option: any): EChartsOption => option as EChartsOption;
const asGraphNode = (data: any): GraphNode => data as GraphNode;
const asGraphNodes = (data: any[]): GraphNode[] => data as GraphNode[];

// 从API数据转换为图表节点数据
const convertApiNodeToGraphNode = (apiNode: ApiGraphNode, x = 0, y = 0): GraphNode => {
  let symbolSize = 40;
  let color = '#5470c6';

  switch (apiNode.nodeType) {
    case 'center':
      symbolSize = 60;
      color = '#91cc75';
      break;
    case 'indicator':
      symbolSize = 40;
      color = '#5470c6';
      break;
    case 'secondaryIndicator':
      symbolSize = 30;
      color = '#fac858';
      break;
    case 'thirdIndicator':
      symbolSize = 25;
      color = '#ee6666';
      break;
    case 'fourthIndicator':
      symbolSize = 20;
      color = '#73c0de';
      break;
  }

  return {
    ...apiNode,
    symbolSize,
    itemStyle: { color },
    x,
    y,
    expanded: false
  };
};

// 初始化图表
const initChart = async () => {
  if (!graphContainer.value) return;

  try {
    loading.value = true;
    // 从API获取知识图谱数据
    const graphData = await getKnowledgeGraph();

    // 创建图表实例
    chartInstance = echarts.init(graphContainer.value);

    // 只获取中心节点，初始状态只显示中心节点
    const centerNode = graphData.nodes.find(node => node.nodeType === 'center');

    // 生成图表节点数据
    const nodes: GraphNode[] = [];

    // 添加中心节点
    if (centerNode) {
      nodes.push(convertApiNodeToGraphNode(centerNode, 0, 0));
    } else {
      // 如果没有中心节点，创建一个默认的中心节点
      nodes.push({
        name: '学风知识图谱',
        symbolSize: 60,
        itemStyle: { color: '#91cc75' },
        x: 0,
        y: 0,
        nodeType: 'center',
        id: 'center',
        expanded: false
      });
    }

    // 添加一级指标节点（围绕中心点排列）
    const indicators = graphData.nodes.filter(n => n.nodeType === 'indicator');
    const angleStep = (2 * Math.PI) / indicators.length;
    const radius = 200;
    
    // 先添加一级指标节点到nodes
    const indicatorNodesWithPosition = indicators.map((indicator, index) => {
      const angle = index * angleStep;
      const x = radius * Math.cos(angle);
      const y = radius * Math.sin(angle);
      const indicatorNode = {
        ...convertApiNodeToGraphNode(indicator, x, y),
        expanded: true
      };
      nodes.push(indicatorNode);
      return { ...indicator, x, y }; // 返回带位置信息的节点用于子节点计算
    });
    
    // 添加二级、三级、四级指标节点（递归构建）
    const addChildNodes = (parentNodes: any[], parentType: string, childType: string, level: number) => {
      parentNodes.forEach((parent, parentIndex) => {
        const childNodes = graphData.nodes.filter(n => n.nodeType === childType && n.parentId === parent.id);
        
        if (childNodes.length > 0) {
          // 计算父节点位置
          let parentX = parent.x || 0;
          let parentY = parent.y || 0;
          
          // 计算子节点分布
          const spreadAngle = Math.max(0.6, 1.2 / level); // 根据层级调整扇形角度
          const childDistance = Math.max(80, 150 - level * 20); // 根据层级调整距离
          
          childNodes.forEach((child, childIndex) => {
            let angle;
            if (childNodes.length === 1) {
              // 只有一个子节点时，基于父节点的位置向外延伸
              if (parentType === 'indicator') {
                // 一级指标的子节点
                const indicatorIndex = indicators.findIndex(ind => ind.id === parent.id);
                angle = indicatorIndex * angleStep;
              } else {
                // 其他层级的子节点，基于父节点位置
                angle = Math.atan2(parentY, parentX);
              }
            } else {
              // 多个子节点时，扇形分布
              const baseAngle = parentType === 'indicator' ? 
                indicators.findIndex(ind => ind.id === parent.id) * angleStep : 
                Math.atan2(parentY, parentX);
              angle = baseAngle - spreadAngle/2 + (spreadAngle / (childNodes.length - 1)) * childIndex;
            }
            
            const x = parentX + Math.cos(angle) * childDistance;
            const y = parentY + Math.sin(angle) * childDistance;
            
            const childNode = {
              ...convertApiNodeToGraphNode(child, x, y),
              expanded: true
            };
            
            nodes.push(childNode);
            
            // 递归处理下一级，传递带位置信息的节点
            if (childType === 'secondaryIndicator') {
              addChildNodes([{ ...child, x, y }], 'secondaryIndicator', 'thirdIndicator', level + 1);
            } else if (childType === 'thirdIndicator') {
              addChildNodes([{ ...child, x, y }], 'thirdIndicator', 'fourthIndicator', level + 1);
            }
          });
        }
      });
    };
    
    // 添加一级指标的所有子节点
    addChildNodes(indicatorNodesWithPosition, 'indicator', 'secondaryIndicator', 2);
    
    // 验证和清理连接线数据
    const validNodeIds = new Set(nodes.map(node => node.id));
    
    // 过滤出有效的连接线（source和target都存在于节点列表中）
    const validLinks = graphData.links.filter(link => {
      const hasValidSource = link.source && validNodeIds.has(link.source);
      const hasValidTarget = link.target && validNodeIds.has(link.target);
      
      if (!hasValidSource || !hasValidTarget) {
        console.warn('发现无效连接线:', {
          linkId: link.id,
          source: link.source,
          target: link.target,
          validSource: hasValidSource,
          validTarget: hasValidTarget
        });
        return false;
      }
      
      return true;
    });
    
    // 创建所有连接线，确保数据格式正确
    const allLinks = validLinks.map(link => {
      // 获取源节点和目标节点
      const sourceNode = graphData.nodes.find(n => n.id === link.source);
      const targetNode = graphData.nodes.find(n => n.id === link.target);
      
      // 确定连接方向和样式
      let lineStyle = {
        width: 2,
        curveness: 0.2,
        type: 'solid', // 默认实线
        color: '#999'
      };
      
      if (sourceNode && targetNode) {
        // 检查是否为父子关系
        const isParentToChild = targetNode.parentId === sourceNode.id;
        const isChildToParent = sourceNode.parentId === targetNode.id;
        
        if (isParentToChild) {
          // 出站连接：父→子，使用实线
          lineStyle = {
            width: 2,
            curveness: 0.2,
            type: 'solid',
            color: getNodeColor(sourceNode.nodeType)
          };
        } else if (isChildToParent) {
          // 入站连接：子→父，使用虚线
          lineStyle = {
            width: 2,
            curveness: 0.2,
            type: 'dashed',
            color: getNodeColor(targetNode.nodeType)
          };
        } else {
          // 其他关系连接，使用紫色虚线
          lineStyle = {
            width: 2,
            curveness: 0.3,
            type: 'dashed',  // 改为虚线
            color: '#8b5cf6'  // 改为紫色
          };
        }
      }
      
      return {
        source: link.source ? String(link.source) : '',
        target: link.target ? String(link.target) : '',
        lineStyle: lineStyle,
        // 添加连接线的描述信息和额外数据
        description: link.description || '暂无描述',
        sourceNodeName: sourceNode?.name || '未知节点',
        targetNodeName: targetNode?.name || '未知节点',
        connectionType: lineStyle.type === 'solid' ? '出站连接' : 
                       lineStyle.type === 'dashed' ? '入站连接' : '其他关联'
      };
    });
    
    console.log('展开数据统计:', {
      节点数量: nodes.length,
      连接线数量: allLinks.length,
      原始连接线数量: graphData.links.length,
      过滤掉的连接线数量: graphData.links.length - validLinks.length
    });
    
    // 一次性更新图表，显示所有节点和连接 - 修复：使用allLinks而不是空数组
    chartInstance.setOption({
      // 添加tooltip配置
      tooltip: {
        trigger: 'item',
        backgroundColor: 'rgba(255, 255, 255, 0.95)',
        borderColor: '#e6e6e6',
        borderWidth: 1,
        borderRadius: 8,
        padding: [12, 16],
        textStyle: {
          color: '#333',
          fontSize: 13,
          lineHeight: 18
        },
        extraCssText: 'box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12); backdrop-filter: blur(8px);',
        formatter: function(params: any) {
          if (params.dataType === 'node') {
            // 节点tooltip
            const data = params.data;
            const nodeTypeMap: Record<string, string> = {
              'center': '中心节点',
              'indicator': '一级指标',
              'secondaryIndicator': '二级指标',
              'thirdIndicator': '三级指标',
              'fourthIndicator': '四级指标'
            };
            
            const nodeTypeName = nodeTypeMap[data.nodeType] || '未知类型';
            const description = data.description || '暂无描述信息';
            
            return `
              <div style="max-width: 300px;">
                <div style="display: flex; align-items: center; margin-bottom: 8px;">
                  <div style="width: 12px; height: 12px; border-radius: 50%; background: ${data.itemStyle.color}; margin-right: 8px;"></div>
                  <strong style="font-size: 14px; color: #1f2937;">${data.name}</strong>
                </div>
                <div style="margin-bottom: 6px;">
                  <span style="color: #6b7280; font-size: 12px;">类型：</span>
                  <span style="color: #374151; font-weight: 500;">${nodeTypeName}</span>
                </div>
                <div style="line-height: 1.5; color: #4b5563; font-size: 12px;">
                  <span style="color: #6b7280;">描述：</span><br/>
                  <span style="color: #374151;">${description}</span>
                </div>
              </div>
            `;
          } else if (params.dataType === 'edge') {
            // 连接线tooltip
            const data = params.data;
            
            return `
              <div style="max-width: 280px;">
                <div style="display: flex; align-items: center; margin-bottom: 8px;">
                  <div style="width: 20px; height: 2px; background: ${data.lineStyle.color}; margin-right: 8px; ${
                    data.lineStyle.type === 'dashed' ? 'background: repeating-linear-gradient(90deg, ' + data.lineStyle.color + ' 0px, ' + data.lineStyle.color + ' 3px, transparent 3px, transparent 6px);' :
                    data.lineStyle.type === 'dotted' ? 'background: repeating-linear-gradient(90deg, ' + data.lineStyle.color + ' 0px, ' + data.lineStyle.color + ' 1px, transparent 1px, transparent 3px);' : ''
                  }"></div>
                  <strong style="font-size: 14px; color: #1f2937;">${data.connectionType}</strong>
                </div>
                <div style="margin-bottom: 6px; font-size: 12px;">
                  <span style="color: #6b7280;">连接：</span>
                  <span style="color: #374151; font-weight: 500;">${data.sourceNodeName}</span>
                  <span style="color: #9ca3af; margin: 0 4px;">→</span>
                  <span style="color: #374151; font-weight: 500;">${data.targetNodeName}</span>
                </div>
                <div style="line-height: 1.5; color: #4b5563; font-size: 12px;">
                  <span style="color: #6b7280;">描述：</span><br/>
                  <span style="color: #374151;">${data.description}</span>
                </div>
              </div>
            `;
          }
        }
      },
      series: [{
        type: 'graph',
        layout: 'force',
        data: nodes,
        links: allLinks, // 修复：从空数组改为allLinks
        categories: [
          { name: '中心节点', itemStyle: { color: '#91cc75' } },
          { name: '一级指标', itemStyle: { color: '#5470c6' } },
          { name: '二级指标', itemStyle: { color: '#fac858' } },
          { name: '三级指标', itemStyle: { color: '#ee6666' } },
          { name: '四级指标', itemStyle: { color: '#73c0de' } }
        ],
        roam: true,
        label: {
          show: true,
          position: 'inside',
          formatter: '{b}',
          fontSize: 12,
          fontWeight: 'bold'
        },
        lineStyle: {
          color: 'source',
          curveness: 0.3,
          width: 2
        },
        emphasis: {
          focus: 'adjacency',
          lineStyle: {
            width: 4
          },
          itemStyle: {
            borderWidth: 3,
            borderColor: '#fff',
            shadowBlur: 10,
            shadowColor: 'rgba(0, 0, 0, 0.3)'
          }
        },
        force: {
          repulsion: 300,
          edgeLength: 150,
          gravity: 0.1
        },
        animation: true,
        animationDuration: 1000,
        animationEasing: 'cubicOut'
      }]
    });
    
    MessagePlugin.success('知识图谱加载完成');
    
  } catch (error) {
    console.error('加载知识图谱失败:', error);
    MessagePlugin.error('加载失败: ' + (error as Error).message);
  } finally {
    loading.value = false;
  }
};

// 重置图表
const resetGraph = () => {
  loading.value = true;
  nextTick(() => {
    initChart();
  });
};

// 导出图片
const exportImage = () => {
  if (!chartInstance) return;

  try {
    const dataURL = chartInstance.getDataURL({
      type: 'png',
      pixelRatio: 2,
      backgroundColor: '#fff'
    });

    const link = document.createElement('a');
    link.download = `学风知识图谱_${new Date().toISOString().split('T')[0]}.png`;
    link.href = dataURL;
    link.click();

    MessagePlugin.success('图片导出成功');
  } catch (error) {
    console.error('导出图片失败', error);
    MessagePlugin.error('图片导出失败');
  }
};

// 全部展开功能
const expandAll = async () => {
  if (!chartInstance) {
    MessagePlugin.warning('图表尚未初始化');
    return;
  }
  
  try {
    loading.value = true;
    MessagePlugin.info('正在展开所有节点...');
    
    // 重新初始化图表，这样会自动显示所有节点和连接线
    await initChart();
    
    // 调整视图以适应所有节点
    setTimeout(() => {
      if (chartInstance) {
        chartInstance.setOption({
          series: [{
            zoom: 0.6, // 缩小视图以适应更多节点
            center: [0, 0]
          }]
        });
      }
    }, 500);
    
  } catch (error) {
    console.error('展开所有节点失败:', error);
    MessagePlugin.error('展开失败');
  } finally {
    loading.value = false;
  }
};

// 组件挂载后初始化图表
onMounted(() => {
  nextTick(() => {
    initChart();
  });
});

// 添加获取节点颜色的辅助函数
const getNodeColor = (nodeType: string): string => {
  switch (nodeType) {
    case 'center': return '#91cc75';
    case 'indicator': return '#5470c6';
    case 'secondaryIndicator': return '#fac858';
    case 'thirdIndicator': return '#ee6666';
    case 'fourthIndicator': return '#73c0de';
    default: return '#666';
  }
};
</script>

<style scoped lang="less">
.knowledge-graph-container {
  min-height: calc(100vh - 120px);
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 24px;
  position: relative;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
      radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
      radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
      radial-gradient(circle at 40% 40%, rgba(120, 200, 255, 0.2) 0%, transparent 50%);
    pointer-events: none;
  }

  .knowledge-graph-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 20px;
    box-shadow: 
      0 20px 40px rgba(0, 0, 0, 0.1),
      0 0 0 1px rgba(255, 255, 255, 0.2);
    border: none;
    overflow: hidden;
    position: relative;
    z-index: 1;
  }

  .kg-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 32px 40px 24px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    position: relative;
    
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      opacity: 0.3;
    }
  }

  .kg-title {
    display: flex;
    align-items: center;
    gap: 20px;
    position: relative;
    z-index: 1;
    
    .title-icon-wrapper {
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      
      .icon-glow {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 60px;
        height: 60px;
        background: radial-gradient(circle, rgba(255, 255, 255, 0.4) 0%, transparent 70%);
        border-radius: 50%;
        animation: pulse-glow 3s ease-in-out infinite;
      }
      
      :deep(.t-icon) {
        position: relative;
        z-index: 1;
        filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3));
        color: #ffffff;
      }
    }
    
    .title-content {
      h2 {
        margin: 0 0 8px 0;
        font-size: 28px;
        font-weight: 700;
        text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
        letter-spacing: 0.5px;
      }
      
      .title-subtitle {
        font-size: 15px;
        opacity: 0.9;
        font-weight: 400;
        color: rgba(255, 255, 255, 0.85);
      }
    }
  }
  
  .kg-actions {
    position: relative;
    z-index: 1;
    
    :deep(.t-button) {
      border-radius: 12px;
      font-weight: 600;
      padding: 12px 24px;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.2);
      position: relative;
      overflow: hidden;
      
      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s;
      }
      
      &:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        
        &::before {
          left: 100%;
        }
      }
      
      &.t-button--theme-primary {
        background: rgba(255, 255, 255, 0.2);
        color: white;
        
        &:hover {
          background: rgba(255, 255, 255, 0.3);
        }
      }
      
      &.t-button--theme-success {
        background: rgba(82, 196, 26, 0.2);
        color: white;
        
        &:hover {
          background: rgba(82, 196, 26, 0.3);
        }
      }
      
      &.t-button--theme-default {
        background: rgba(255, 255, 255, 0.15);
        color: white;
        
        &:hover {
          background: rgba(255, 255, 255, 0.25);
        }
      }
    }
  }

  .graph-container {
    width: 100%;
    height: 70vh;
    min-height: 500px;
    position: relative;
    background: 
      radial-gradient(circle at 30% 70%, rgba(103, 126, 234, 0.05) 0%, transparent 50%),
      radial-gradient(circle at 70% 30%, rgba(118, 75, 162, 0.05) 0%, transparent 50%);
    border-radius: 0 0 20px 20px;
    
    .graph-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 10;
      pointer-events: none;
      
      .loading-indicator {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 16px;
        padding: 32px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        pointer-events: auto;
      }
    }
  }
  
  .graph-legend {
    position: absolute;
    top: 120px;
    right: 40px;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 16px;
    padding: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    z-index: 5;
    min-width: 200px;
    
    .legend-title {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 16px;
      font-weight: 600;
      color: #2c3e50;
      font-size: 14px;
      
      :deep(.t-icon) {
        color: #667eea;
      }
    }
    
    .legend-items {
      display: flex;
      flex-direction: column;
      gap: 10px;
    }
    
    .legend-item {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 8px 12px;
      border-radius: 8px;
      transition: all 0.2s ease;
      
      &:hover {
        background: rgba(103, 126, 234, 0.1);
      }
      
      .legend-dot {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        
        &.center-node {
          background: linear-gradient(135deg, #91cc75, #7fb95f);
        }
        
        &.indicator-node {
          background: linear-gradient(135deg, #5470c6, #4865b8);
        }
        
        &.secondary-node {
          background: linear-gradient(135deg, #fac858, #f5b73d);
        }
        
        &.third-node {
          background: linear-gradient(135deg, #ee6666, #d63447);
        }
        
        &.fourth-node {
          background: linear-gradient(135deg, #73c0de, #5b9bd5);
        }
      }
      
      .legend-line {
        width: 30px;
        height: 2px;
        
        &.solid-line {
          background: linear-gradient(90deg, #5470c6, #4865b8);
          border: none;
        }
        
        &.dashed-line {
          background: repeating-linear-gradient(
            90deg,
            #ee6666 0px,
            #ee6666 5px,
            transparent 5px,
            transparent 10px
          );
        }
        
        &.dotted-line {
          background: repeating-linear-gradient(
            90deg,
            #8b5cf6 0px,
            #8b5cf6 5px,
            transparent 5px,
            transparent 10px
          );
        }
      }
      
      span {
        font-size: 13px;
        color: #2c3e50;
        font-weight: 500;
      }
    }
  }
}

// 动画效果
@keyframes pulse-glow {
  0%, 100% {
    opacity: 0.6;
    transform: translate(-50%, -50%) scale(1);
  }
  50% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1.15);
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .knowledge-graph-container {
    padding: 16px;
    
    .kg-header {
      padding: 24px 32px 20px;
      
      .kg-title {
        .title-content h2 {
          font-size: 24px;
        }
      }
    }
    
    .graph-legend {
      right: 20px;
      top: 100px;
    }
  }
}

@media (max-width: 768px) {
  .knowledge-graph-container {
    padding: 12px;
    
    .kg-header {
      flex-direction: column;
      gap: 20px;
      padding: 20px;
      text-align: center;
    }
    
    .graph-legend {
      position: static;
      margin: 20px;
      align-self: center;
    }
    
    .graph-container {
      height: 60vh;
    }
  }
}
</style>

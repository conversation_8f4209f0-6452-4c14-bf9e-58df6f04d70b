<template>
  <div class="knowledge-graph-management">
    <t-card>
      <t-tabs v-model="currentTab">
        <t-tab-panel value="professional" label="专业知识图谱管理">
        </t-tab-panel>
      </t-tabs>
    </t-card>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref } from 'vue';


export default defineComponent({
  name: 'KnowledgeGraphManagement',
  components: {
  },
  setup() {
    const currentTab = ref('professional');

    return {
      currentTab
    };
  }
});
</script>

<style lang="less" scoped>
.knowledge-graph-management {
  padding: 20px;
}
</style> 
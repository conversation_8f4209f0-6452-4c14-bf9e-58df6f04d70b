<template>
  <div class="course-achievement">
    <t-card class="mb-4">
      <template #header>
        <div class="achievement-header">
          <div class="header-title">
            <t-icon name="chart-line" />
            课程目标达成度分析
          </div>
          <div class="header-info">
            课程ID: {{ courseId }}
          </div>
        </div>
      </template>
    </t-card>

    <t-loading :loading="loading">
      <t-card v-if="courseTargets.length > 0" class="mb-4">
        <t-tabs v-model="activeTab">
          <t-tab-panel
            v-for="target in courseTargets"
            :key="target.objectiveId"
            :value="target.objectiveId.toString()"
            :label="`课程目标${target.number}`"
          >
            <div class="chart-container">
              <ScatterChart
                :chart-id="`chart-${target.objectiveId}`"
                :title="`课程目标${target.number}成绩散点图`"
                :points="getScatterPoints(target)"
                x-label="成绩序号"
                y-label="成绩"
              />
              <div class="chart-info">
                <p><strong>课程目标名称:</strong> {{ target.objectiveName }}</p>
                <p><strong>对应毕业要求:</strong> {{ target.po?.title || '无' }}</p>
                <p><strong>学生总数:</strong> {{ getStudentCount(target.objectiveId) }}人</p>
              </div>
            </div>
          </t-tab-panel>
        </t-tabs>
      </t-card>

      <t-card v-else>
        <t-empty />
      </t-card>
    </t-loading>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, watch } from 'vue';
import { MessagePlugin } from 'tdesign-vue-next';
import ScatterChart from './components/ScatterChart.vue';
import { useRoute } from 'vue-router';
// API imports
import { getCourseTargetList } from '@/api/training/course';
import { getTargetScoresByCourseId } from '@/api/assessment/assessmentScore';

// Types
// 后端返回的学生成绩结构
interface StudentTargetScore {
  studentId: string;
  studentName: string;
  studentNumber: string;
  targetScores: Array<{
    courseTargetNo: string;
    methodScores: any[];
    objectiveId: string;
    poId: string;
    totalScore: number;
  }>;
}

interface CourseObjectiveVO {
  objectiveId: number;
  number: number; // 课程目标序号
  objectiveName: string;
  expectedScore: number; // 预期达成度
  description?: string; // 课程目标描述
  po?: {
    id: number;
    title?: string;
    description?: string;
  }; // 对应的毕业要求
}

// Props
const props = defineProps<{
  courseId: number;
}>();
// router

const route = useRoute();

// Reactive data
const loading = ref<boolean>(false);
const courseTargets = ref<CourseObjectiveVO[]>([]);
const studentScores = ref<StudentTargetScore[]>([]);
const activeTab = ref<string>('');

// Methods
const loadData = async () => {
  if (!courseId.value) {
    MessagePlugin.warning('请输入课程ID');
    return;
  }

  loading.value = true;
  try {
    // 获取课程目标列表
    const targets = await getCourseTargetList(courseId.value);
    courseTargets.value = targets;
    
    // 获取学生成绩数据
    const scoresResponse = await getTargetScoresByCourseId(courseId.value);
    studentScores.value = scoresResponse.data;
    console.log('学生成绩数据加载成功:', studentScores.value);
    if (targets.length > 0) {
      activeTab.value = targets[0].objectiveId.toString();
    }
    
    MessagePlugin.success('数据加载成功');
  } catch (err) {
    console.error('数据加载失败:', err);
    MessagePlugin.error('数据加载失败');
  } finally {
    loading.value = false;
  }
};

// Computed courseId from props and ensure number type
const courseId = computed<number>(() => {
  const id = props.courseId || Number(route.params.courseId) || 0;
  return typeof id === 'number' ? id : Number(id);
});

// 分别监听 props 和 route 参数的变化
watch([
  () => props.courseId,
  () => route.params.courseId
], ([newPropsCourseId, newRouteCourseId]) => {
  const effectiveCourseId = newPropsCourseId || Number(newRouteCourseId);
  if (effectiveCourseId && !isNaN(Number(effectiveCourseId))) {
    loadData();
  }
}, { immediate: true });

onMounted(() => {
  if (props.courseId) {
    loadData();
  }
});


const getStudentCount = (targetId: number) => {
  return studentScores.value.filter(student => {
    if (!student || !Array.isArray(student.targetScores)) return false;
    return student.targetScores.some(score => score && String(score.courseTargetNo) === String(targetId));
  }).length;
};

// Tab切换时无需额外渲染，ScatterChart组件自动响应数据变化

const getScatterPoints = (target: CourseObjectiveVO) => {
  const points: Array<{ index: number; value: number; name: string; number: string }> = [];
  let idx = 1;
  studentScores.value.forEach((student) => {
    if (!student || !Array.isArray(student.targetScores)) return;
    student.targetScores.forEach(scoreObj => {
      if (scoreObj && String(scoreObj.courseTargetNo) === String(target.number)) {
        points.push({
          index: idx,
          value: scoreObj.totalScore,
          name: student.studentName,
          number: student.studentNumber
        });
        idx++;
      }
    });
  });
  return points;
};

// ...已删除原生echarts渲染方法，全部由ScatterChart组件负责...
</script>

<style scoped>
.course-achievement {
  padding: 16px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.achievement-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

.header-info {
  color: #6b7280;
  font-size: 14px;
}

.chart-container {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.chart {
  width: 100%;
  height: 500px;
  margin-bottom: 20px;
}

.chart-info {
  width: 100%;
  padding: 16px;
  background-color: #f0f0f0;
  border-radius: 4px;
}

.mb-4 {
  margin-bottom: 16px;
}

.ml-4 {
  margin-left: 16px;
}

.w-40 {
  width: 10rem;
}

.flex {
  display: flex;
}

.items-center {
  align-items: center;
}
</style>
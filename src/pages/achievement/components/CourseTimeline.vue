<template>
  <div class="course-timeline-horizontal">
    <div class="timeline-header">
      <h3 class="timeline-title">
        <t-icon name="time" class="title-icon" />
        课程历史版本时间轴
      </h3>
      <div class="timeline-subtitle">点击任意版本卡片查看详细达成度分析</div>
    </div>

    <div class="timeline-container">
      <div class="timeline-line"></div>
      <div class="timeline-items">
        <div
          v-for="(course, index) in courses"
          :key="course.id"
          class="timeline-item"
          :class="{ 'timeline-item-top': index % 2 === 0, 'timeline-item-bottom': index % 2 === 1 }"
        >
          <!-- 时间点 -->
          <div class="timeline-dot">
            <div class="dot-inner">{{ index + 1 }}</div>
          </div>

          <!-- 连接箭头 -->
          <div class="timeline-arrow" :class="{ 'arrow-top': index % 2 === 0, 'arrow-bottom': index % 2 === 1 }">
            <div class="arrow-line"></div>
            <div class="arrow-head"></div>
          </div>

          <!-- 课程卡片 -->
          <div class="timeline-card" @click="handleCardClick(course)">
            <div class="card-header">
              <div class="version-badge">{{ course.version }}</div>
              <div class="card-status-indicator">
                <t-icon name="check-circle" class="status-icon" />
              </div>
            </div>
            <div class="card-content">
              <div class="course-info">
                <div class="info-row">
                  <span class="info-label">开课时间</span>
                  <span class="info-value">{{ course.year }}年{{ course.semester }}</span>
                </div>
                <div class="info-row">
                  <span class="info-label">课程版本</span>
                  <span class="info-value">{{ course.version }}</span>
                </div>
              </div>
            </div>
            <div class="card-footer">
              <t-button
                theme="primary"
                variant="outline"
                size="small"
                class="details-button"
                @click.stop="handleDetailsClick(course)"
              >
                <t-icon name="view" class="button-icon" />
                查看详情
              </t-button>
            </div>
          </div>

          <!-- 连接线 -->
          <div v-if="index < courses.length - 1" class="timeline-connector"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
defineProps<{ courses: Array<any> }>();

// 定义事件
const emit = defineEmits<{
  'card-click': [course: any];
  'details-click': [course: any];
}>();

// 处理卡片点击事件
const handleCardClick = (course: any) => {
  emit('card-click', course);
};

// 处理详情按钮点击事件
const handleDetailsClick = (course: any) => {
  emit('details-click', course);
};
</script>

<style scoped>
.course-timeline-horizontal {
  padding: 24px 0;
}

.timeline-header {
  text-align: center;
  margin-bottom: 32px;
}

.timeline-title {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 8px 0;
}

.title-icon {
  color: #3b82f6;
}

.timeline-subtitle {
  color: #6b7280;
  font-size: 14px;
}

.timeline-container {
  position: relative;
  padding: 60px 0;
  min-height: 280px;
}

.timeline-line {
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(to right, #e5e7eb, #3b82f6, #e5e7eb);
  transform: translateY(-50%);
  z-index: 1;
}

.timeline-items {
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  z-index: 2;
}

.timeline-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  flex: 1;
}

.timeline-item-top .timeline-card {
  margin-bottom: 60px;
  transform: translateY(-60px);
  position: relative;
  top: -20px;
}

.timeline-item-bottom .timeline-card {
  margin-top: 60px;
  transform: translateY(60px);
  position: relative;
  top: 20px;
}

.timeline-item-top .timeline-card:hover {
  transform: translateY(-64px);
  top: -20px;
}

.timeline-item-bottom .timeline-card:hover {
  transform: translateY(56px);
  top: 20px;
}

.timeline-dot {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  z-index: 4;
}

.dot-inner {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 14px;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
  border: 3px solid white;
  transition: all 0.3s ease;
}

.dot-inner:hover {
  transform: scale(1.1);
  box-shadow: 0 6px 16px rgba(59, 130, 246, 0.5);
}

.timeline-card {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08);
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  width: 220px;
  border: 2px solid #f1f5f9;
  position: relative;
  overflow: hidden;
}

.timeline-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #3b82f6, #8b5cf6);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.timeline-card:hover {
  transform: translateY(-6px);
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.12);
  border-color: #3b82f6;
}

.timeline-card:hover::before {
  opacity: 1;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.version-badge {
  background: linear-gradient(135deg, #eff6ff, #dbeafe);
  color: #1d4ed8;
  padding: 6px 14px;
  border-radius: 20px;
  font-size: 13px;
  font-weight: 700;
  border: 1px solid #bfdbfe;
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.1);
}

.card-status-indicator {
  display: flex;
  align-items: center;
}

.status-icon {
  color: #10b981;
  font-size: 18px;
  transition: all 0.3s ease;
}

.timeline-card:hover .status-icon {
  color: #059669;
  transform: scale(1.1);
}

.card-content {
  margin-bottom: 16px;
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.info-row:last-child {
  margin-bottom: 0;
}

.info-label {
  font-size: 12px;
  color: #6b7280;
}

.info-value {
  font-size: 14px;
  color: #1f2937;
  font-weight: 500;
}

.card-footer {
  text-align: center;
  border-top: 1px solid #f1f5f9;
  padding-top: 16px;
  margin-top: 16px;
  position: relative;
  z-index: 10;
}

.details-button {
  border-radius: 12px;
  padding: 8px 16px;
  font-weight: 600;
  transition: all 0.3s ease;
  position: relative;
  z-index: 11;
}

.details-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
}

.button-icon {
  margin-right: 4px;
  font-size: 14px;
}

.timeline-connector {
  position: absolute;
  top: 50%;
  right: -50%;
  width: 100%;
  height: 2px;
  background: #e5e7eb;
  transform: translateY(-50%);
  z-index: 1;
}

/* 弹出层样式 */
:deep(.t-popup) {
  padding: 0 !important;
  position: absolute;
  z-index: 1000;
}

:deep(.t-popup__content) {
  padding: 0 !important;
}

:deep(.t-popup__arrow) {
  background: white !important;
  border-color: #e5e7eb !important;
}

.timeline-item-top :deep(.t-popup) {
  bottom: 40px;
}

.timeline-item-bottom :deep(.t-popup) {
  top: 40px;
}

/* 连接箭头样式 */
.timeline-arrow {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  z-index: 2;
}

.arrow-top {
  top: 50%;
  transform: translateX(-50%) translateY(-50%) rotate(90deg);
}

.arrow-bottom {
  top: 50%;
  transform: translateX(-50%) translateY(-50%) rotate(-90deg);
}

.arrow-line {
  width: 2px;
  height: 30px;
  background: linear-gradient(to bottom, #3b82f6, #8b5cf6);
  margin: 0 auto;
  position: relative;
}

.arrow-head {
  width: 0;
  height: 0;
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  border-top: 8px solid #3b82f6;
  margin: -1px auto 0;
  position: relative;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .timeline-card {
    width: 180px;
    padding: 16px;
  }

  .timeline-container {
    padding: 40px 0;
    min-height: 240px;
  }

  .timeline-item-top .timeline-card {
    margin-bottom: 50px;
    transform: translateY(-50px);
    top: -15px;
  }

  .timeline-item-bottom .timeline-card {
    margin-top: 50px;
    transform: translateY(50px);
    top: 15px;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .timeline-items {
    flex-direction: column;
    gap: 32px;
  }

  .timeline-item {
    width: 100%;
  }

  .timeline-line {
    display: none;
  }

  .timeline-card {
    width: 100%;
    max-width: 300px;
  }

  .timeline-item-top .timeline-card,
  .timeline-item-bottom .timeline-card {
    margin: 0;
  }

  .timeline-dot {
    position: relative;
    top: auto;
    transform: none;
    margin-bottom: 16px;
  }
}
</style>

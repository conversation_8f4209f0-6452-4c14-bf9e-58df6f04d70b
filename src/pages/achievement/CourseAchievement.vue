<template>
  <div class="course-achievement">
  
    <!-- 第一行：课程历史版本时间轴 -->
    <t-card class="timeline-section-card mb-6" :bordered="false">
      <CourseTimeline
        :courses="courseHistoryList"
        @card-click="handleTimelineCardClick"
        @details-click="handleTimelineDetailsClick"
      />
    </t-card>

    <!-- 第二行：课程基本信息 -->
    <t-card class="course-info-card mb-6" :bordered="false">
      <template #header>
        <div class="card-header">
          <div class="header-title">
            <t-icon name="book" class="header-icon" />
            课程基本信息
          </div>
        </div>
      </template>
      <div class="course-info-content">
        <!-- 第一行：基本信息 -->
        <t-row :gutter="[24, 16]">
          <t-col :span="4">
            <div class="info-item">
              <div class="info-label">课程名称</div>
              <div class="info-value">{{ courseBase.courseName }}</div>
            </div>
          </t-col>
          <t-col :span="4">
            <div class="info-item">
              <div class="info-label">课程编号</div>
              <div class="info-value">{{ courseBase.courseCode }}</div>
            </div>
          </t-col>
          <t-col :span="4">
            <div class="info-item">
              <div class="info-label">课程学分</div>
              <div class="info-value">{{ courseBase.courseCredit }}</div>
            </div>
          </t-col>
          <t-col :span="4">
            <div class="info-item">
              <div class="info-label">课程负责人</div>
              <div class="info-value">{{ teacherName }}</div>
            </div>
          </t-col>
          <t-col :span="4">
            <div class="info-item">
              <div class="info-label">课程性质</div>
              <div class="info-value">{{ courseNatureLabel }}</div>
            </div>
          </t-col>
          <t-col :span="4">
            <div class="info-item">
              <div class="info-label">开课学期</div>
              <div class="info-value">{{ courseBase.courseSemester }}</div>
            </div>
          </t-col>
        </t-row>

        <!-- 第二行：学时分配 -->
        <t-row :gutter="[24, 16]" class="mt-4">
          <t-col :span="4">
            <div class="info-item">
              <div class="info-label">总学时</div>
              <div class="info-value">{{ courseBase.courseHoursTotal }}</div>
            </div>
          </t-col>
          <t-col :span="4">
            <div class="info-item">
              <div class="info-label">理论学时</div>
              <div class="info-value">{{ courseBase.courseHoursTheory }}</div>
            </div>
          </t-col>
          <t-col :span="4">
            <div class="info-item">
              <div class="info-label">实验学时</div>
              <div class="info-value">{{ courseBase.courseHoursExperiment }}</div>
            </div>
          </t-col>
          <t-col :span="4">
            <div class="info-item">
              <div class="info-label">其他学时</div>
              <div class="info-value">{{ courseBase.courseHoursOther }}</div>
            </div>
          </t-col>
          <t-col :span="4">
            <div class="info-item">
              <div class="info-label">课外学时</div>
              <div class="info-value">{{ courseBase.courseHoursExtracurricular }}</div>
            </div>
          </t-col>
          <t-col :span="4">
            <div class="info-item">
              <div class="info-label">课程版本</div>
              <div class="info-value">{{ courseBase.courseVersion }}</div>
            </div>
          </t-col>
        </t-row>

        <!-- 第三行：课程分类和属性 -->
        <t-row :gutter="[24, 16]" class="mt-4">
          <t-col :span="4">
            <div class="info-item">
              <div class="info-label">课程类别</div>
              <div class="info-value">{{ courseType1Label }}</div>
            </div>
          </t-col>
          <t-col :span="4">
            <div class="info-item">
              <div class="info-label">工程教育认证分类</div>
              <div class="info-value">{{ courseType2Label }}</div>
            </div>
          </t-col>
          <t-col :span="4">
            <div class="info-item">
              <div class="info-label">国标分类</div>
              <div class="info-value">{{ courseType3Label }}</div>
            </div>
          </t-col>
          <t-col :span="4">
            <div class="info-item">
              <div class="info-label">核心课程</div>
              <div class="info-value">
                <t-tag :theme="courseBase.courseCore ? 'success' : 'default'" variant="light">
                  {{ courseBase.courseCore ? '是' : '否' }}
                </t-tag>
              </div>
            </div>
          </t-col>
          <t-col :span="4">
            <div class="info-item">
              <div class="info-label">考试课程</div>
              <div class="info-value">
                <t-tag :theme="courseBase.courseExam ? 'warning' : 'default'" variant="light">
                  {{ courseBase.courseExam ? '是' : '否' }}
                </t-tag>
              </div>
            </div>
          </t-col>
          <t-col :span="4">
            <div class="info-item">
              <div class="info-label">培养方案ID</div>
              <div class="info-value">{{ courseBase.planId || '未设置' }}</div>
            </div>
          </t-col>
        </t-row>
      </div>
    </t-card>

    <!-- 第三行：课程目标-毕业要求卡片列表 -->
    <t-card class="objectives-mapping-card mb-6" :bordered="false">
      <template #header>
        <div class="card-header">
          <div class="header-title">
            <t-icon name="target" class="header-icon" />
            课程目标与毕业要求映射关系
          </div>
          <div class="header-info">
            共 {{ objectivePoList.length }} 个课程目标
          </div>
        </div>
      </template>
      <div class="objectives-grid">
        <div
          v-for="(item, index) in objectivePoList"
          :key="item.id"
          class="objective-mapping-item"
          :class="`objective-${index + 1}`"
        >
          <div class="objective-card">
            <div class="objective-header">
              <div class="objective-number">CO{{ index + 1 }}</div>
              <div class="objective-title">{{ item.objectiveName }}</div>
            </div>
            <div class="mapping-arrow">
              <t-icon name="arrow-right" />
            </div>
            <div class="po-section">
              <div class="po-label">支撑毕业要求</div>
              <div class="po-title">{{ item.poTitle }}</div>
            </div>
          </div>
        </div>
      </div>
    </t-card>

    <!-- 对话框：课程目标达成度详情 -->
    <t-dialog
      v-model:visible="achievementDialogVisible"
      :width="isFullScreen ? '100vw' : '900px'"
      :height="isFullScreen ? '100vh' : 'auto'"
      :top="isFullScreen ? '0' : '5vh'"
      :footer="false"
      :close-btn="false"
      :esc-closable="true"
      @close="handleCloseDialog"
    >
      <template #header>
        <div class="dialog-header">
          <div class="dialog-title">
            <t-icon name="chart-line" />
            课程目标达成度详情分析
          </div>
          <div class="dialog-actions">
            <t-button
              theme="default"
              variant="text"
              size="small"
              @click="toggleFullScreen"
            >
              <t-icon :name="isFullScreen ? 'fullscreen-exit' : 'fullscreen'" />
              {{ isFullScreen ? '退出全屏' : '全屏显示' }}
            </t-button>
            <t-button
              theme="default"
              variant="text"
              size="small"
              @click="handleCloseDialog"
            >
              <t-icon name="close" />
              关闭
            </t-button>
          </div>
        </div>
      </template>
      <ObjectiveAchievement :course-id="dialogCourseId" />
    </t-dialog>

    <!-- 第四行：数据可视化分析区域 -->
    <div class="visualization-section">
      <!-- 第一组：雷达图分析 -->
      <t-card class="chart-group-card mb-6" :bordered="false">
        <template #header>
          <div class="card-header">
            <div class="header-title">
              <t-icon name="chart-radar" class="header-icon" />
              达成度雷达分析
            </div>
            <div class="header-info">多维度达成度对比分析</div>
          </div>
        </template>
        <div class="radar-charts-container">
          <t-row :gutter="[24, 24]" class="radar-charts-row">
            <t-col :xl="12" :lg="12" :md="12" :sm="24" :xs="24">
              <div class="radar-chart-wrapper">
                <RadarChart
                  :title="'课程目标达成情况'"
                  :data="radarObjectiveData"
                />
              </div>
            </t-col>
            <t-col :xl="12" :lg="12" :md="12" :sm="24" :xs="24">
              <div class="radar-chart-wrapper">
                <RadarChart
                  :title="'毕业要求达成情况'"
                  :data="radarPoData"
                />
              </div>
            </t-col>
          </t-row>
        </div>
       
      </t-card>

      <!-- 第二组：柱状图分析 -->
      <t-card class="chart-group-card mb-6" :bordered="false">
        <template #header>
          <div class="card-header">
            <div class="header-title">
              <t-icon name="chart-bar" class="header-icon" />
              达成度结果统计
            </div>
            <div class="header-info">各项指标达成度数值统计</div>
          </div>
        </template>
        <div class="bar-charts-container">
          <t-row :gutter="[24, 24]" class="bar-charts-row">
            <t-col :xl="12" :lg="12" :md="12" :sm="24" :xs="24">
              <div class="bar-chart-wrapper">
                <BarChart
                  :title="'课程目标达成结果'"
                  :data="barObjectiveData"
                />
              </div>
            </t-col>
            <t-col :xl="12" :lg="12" :md="12" :sm="24" :xs="24">
              <div class="bar-chart-wrapper">
                <BarChart
                  :title="'毕业要求达成结果'"
                  :data="barPoData"
                />
              </div>
            </t-col>
          </t-row>
        </div>
      </t-card>

      <!-- 第三组：趋势分析 -->
      <t-card class="chart-group-card" :bordered="false">
        <template #header>
          <div class="card-header">
            <div class="header-title">
              <t-icon name="chart-line" class="header-icon" />
              达成度趋势分析
            </div>
            <div class="header-info">历年课程目标达成度变化趋势</div>
          </div>
        </template>
        <div class="chart-wrapper trend-chart">
          <LineChart
            :title="'课程目标达成度趋势'"
            :data="lineObjectiveTrendData"
          />
        </div>
      </t-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed } from 'vue';
import { MessagePlugin } from 'tdesign-vue-next';
import ObjectiveAchievement from './ObjectiveAchievement.vue';
import CourseTimeline from './components/CourseTimeline.vue';
import RadarChart from './components/RadarChart.vue';
import BarChart from './components/BarChart.vue';
import LineChart from './components/LineChart.vue';

// API imports
import { findCourseBaseInfoList, getCourseBaseInfo, getCourseTargetList, type CourseDetailInfo, type CourseObjectiveVO } from '@/api/training/course';
import { useDictByTypeTitle } from '@/hooks/useDict';

// 数据定义

// 课程基本信息 - 使用完整的CourseDetailInfo结构
const courseBase = ref<CourseDetailInfo>({
  courseId: 0,
  courseName: '加载中...',
  courseCode: '加载中...',
  courseLeader: 0,
  courseCredit: 0,
  courseCore: false,
  courseExam: false,
  courseHoursTotal: 0,
  courseHoursTheory: 0,
  courseHoursExperiment: 0,
  courseHoursOther: 0,
  courseHoursExtracurricular: 0,
  courseSemester: '加载中...',
  courseType1: '',
  courseType2: '',
  courseType3: '',
  courseNature: '',
  courseVersion: 1,
  planId: 0
});

// 字典数据
const { dictOptions: courseNatureOptions } = useDictByTypeTitle('课程性质');
const { dictOptions: courseType1Options } = useDictByTypeTitle('课程类别');
const { dictOptions: courseType2Options } = useDictByTypeTitle('工程教育认证分类');
const { dictOptions: courseType3Options } = useDictByTypeTitle('国标分类');

// 教师名称映射
const teacherNameMap = ref<Record<number, string>>({});

// 计算属性 - 用于模板显示
const teacherName = computed(() => {
  const teacherId = courseBase.value.courseLeader;
  if (!teacherId) return '未设置';
  return teacherNameMap.value[teacherId] || `教师ID: ${teacherId}`;
});

const courseNatureLabel = computed(() => {
  const value = courseBase.value.courseNature;
  if (!courseNatureOptions.value || !value) return value || '未设置';
  const option = courseNatureOptions.value.find((item: any) => item.value === value.toString());
  return option?.label || value;
});

const courseType1Label = computed(() => {
  const value = courseBase.value.courseType1;
  if (!courseType1Options.value || !value) return value || '未设置';
  const option = courseType1Options.value.find((item: any) => item.value === value.toString());
  return option?.label || value;
});

const courseType2Label = computed(() => {
  const value = courseBase.value.courseType2;
  if (!courseType2Options.value || !value) return value || '未设置';
  const option = courseType2Options.value.find((item: any) => item.value === value.toString());
  return option?.label || value;
});

const courseType3Label = computed(() => {
  const value = courseBase.value.courseType3;
  if (!courseType3Options.value || !value) return value || '未设置';
  const option = courseType3Options.value.find((item: any) => item.value === value.toString());
  return option?.label || value;
});

// 课程目标-毕业要求卡片列表
const objectivePoList = ref([
  { id: 1, objectiveName: '掌握软件工程基本理论和方法', poTitle: '工程知识' },
  { id: 2, objectiveName: '具备软件需求分析和设计能力', poTitle: '问题分析' },
  { id: 3, objectiveName: '能够设计和开发软件系统', poTitle: '设计/开发解决方案' },
  { id: 4, objectiveName: '具备软件测试和质量保证能力', poTitle: '研究' },
  { id: 5, objectiveName: '掌握现代软件开发工具和技术', poTitle: '使用现代工具' }
]);

// 课程历史版本列表（时间轴）- 从API获取
const courseHistoryList = ref<Array<{
  id: number;
  version: string;
  year: number;
  semester: string;
  courseId: number;
  courseName?: string;
  courseCode?: string;
}>>([]);

// 加载状态
const loading = ref(false);



// 获取教师名称的异步函数（可以在后续扩展中使用）
const loadTeacherName = async (teacherId: number) => {
  if (!teacherId || teacherNameMap.value[teacherId]) return;

  try {
    // 这里可以调用教师信息API来获取真实的教师姓名
    // const teacherInfo = await getTeacherInfo(teacherId);
    // teacherNameMap.value[teacherId] = teacherInfo.name;

    // 暂时使用占位符
    teacherNameMap.value[teacherId] = `教师ID: ${teacherId}`;
  } catch (error) {
    console.error('获取教师信息失败:', error);
    teacherNameMap.value[teacherId] = `教师ID: ${teacherId}`;
  }
};

// 对话框控制
const achievementDialogVisible = ref(false);
const dialogCourseId = ref(0);
const isFullScreen = ref(false);



const handleCloseDialog = () => {
  achievementDialogVisible.value = false;
  isFullScreen.value = false;
};

const toggleFullScreen = () => {
  isFullScreen.value = !isFullScreen.value;
};

// Keyboard event handling
const handleKeyDown = (event: KeyboardEvent) => {
  if (event.key === 'Escape' && achievementDialogVisible.value) {
    handleCloseDialog();
  }
};

// 图表数据（示例结构，后续可对接接口）
const radarObjectiveData = ref({
  indicators: [
    { name: '课程目标1', max: 100 },
    { name: '课程目标2', max: 100 },
    { name: '课程目标3', max: 100 },
    { name: '课程目标4', max: 100 },
    { name: '课程目标5', max: 100 }
  ],
  data: [85, 92, 88, 93, 85] // 最新学期的达成度数据
});

const radarPoData = ref({
  indicators: [
    { name: '工程知识', max: 100 },
    { name: '问题分析', max: 100 },
    { name: '设计开发', max: 100 },
    { name: '研究能力', max: 100 },
    { name: '工程工具', max: 100 },
    { name: '工程伦理', max: 100 }
  ],
  data: [92, 88, 93, 87, 91, 95] // 最新学期的达成度数据
});

const barObjectiveData = ref([
  { label: '课程目标1', value: 85, target: 80 },
  { label: '课程目标2', value: 92, target: 85 },
  { label: '课程目标3', value: 88, target: 82 },
  { label: '课程目标4', value: 93, target: 88 },
  { label: '课程目标5', value: 85, target: 80 }
]);

const barPoData = ref([
  { label: '工程知识', value: 92, target: 85 },
  { label: '问题分析', value: 88, target: 82 },
  { label: '设计开发', value: 93, target: 88 },
  { label: '研究能力', value: 87, target: 80 },
  { label: '工程工具', value: 91, target: 85 },
  { label: '工程伦理', value: 95, target: 90 }
]);

const lineObjectiveTrendData = ref([
  { label: '2019春', value: 78 },
  { label: '2019秋', value: 80 },
  { label: '2020春', value: 82 },
  { label: '2020秋', value: 84 },
  { label: '2021春', value: 81 },
  { label: '2021秋', value: 85 },
  { label: '2022春', value: 88 },
  { label: '2022秋', value: 87 },
  { label: '2023春', value: 90 }
]);

// 加载课程历史版本数据
const loadCourseHistoryData = async () => {
  try {
    loading.value = true;
    const response = await findCourseBaseInfoList();

    console.log('API响应数据:', response);

    // 处理API响应结构
    let courseList: CourseDetailInfo[] = [];

    if (response && typeof response === 'object') {
      // 检查是否是包含data字段的响应
      if ('data' in response) {
        if (Array.isArray(response.data)) {
          courseList = response.data as CourseDetailInfo[];
        } else if (response.data && typeof response.data === 'object') {
          // 如果data是单个对象，将其包装成数组
          courseList = [response.data as CourseDetailInfo];
        }
      }
      // 检查是否直接是数组
      else if (Array.isArray(response)) {
        courseList = response as CourseDetailInfo[];
      }
      // 检查是否是单个课程对象
      else if ((response as any).courseId || (response as any).courseName) {
        courseList = [response as CourseDetailInfo];
      }
      else {
        console.warn('API返回的数据结构无法识别:', response);
        MessagePlugin.warning('未找到课程历史数据');
        return;
      }
    } else {
      console.warn('无效的API响应:', response);
      MessagePlugin.warning('获取课程历史数据失败');
      return;
    }

    console.log('解析后的课程列表:', courseList);

    if (!Array.isArray(courseList) || courseList.length === 0) {
      console.warn('课程列表为空或不是数组');
      MessagePlugin.info('暂无课程历史数据');
      return;
    }

    // 转换数据格式为时间轴需要的格式
    courseHistoryList.value = courseList.map((course: CourseDetailInfo) => ({
      id: course.courseId,
      version: `${course.courseVersion}版`,
      year: course.courseVersion, // 使用版本号作为年份显示
      semester: course.courseSemester || '春',
      courseId: course.courseId,
      courseName: course.courseName,
      courseCode: course.courseCode
    })).sort((a, b) => a.year - b.year); // 按版本号排序

    console.log('转换后的时间轴数据:', courseHistoryList.value);

    // 如果有数据，加载第一个课程的详细信息
    if (courseList.length > 0) {
      await loadCourseDetails(courseList[0].courseId);
    }

    MessagePlugin.success(`课程历史数据加载成功，共${courseList.length}个版本`);
  } catch (error) {
    console.error('加载课程历史数据失败:', error);
    MessagePlugin.error('加载课程历史数据失败');
  } finally {
    loading.value = false;
  }
};

// 加载课程详细信息
const loadCourseDetails = async (courseId: number) => {
  try {
    // 获取课程基本信息
    const response = await getCourseBaseInfo(courseId);

    // 处理API响应结构
    let courseInfo: CourseDetailInfo;
    if (response && typeof response === 'object') {
      if ('data' in response) {
        courseInfo = response.data;
      } else {
        courseInfo = response;
      }
    } else {
      throw new Error('无效的API响应');
    }

    // 直接使用CourseDetailInfo结构
    courseBase.value = {
      ...courseInfo,
      // 确保所有必需字段都有默认值
      courseId: courseInfo.courseId || courseId,
      courseName: courseInfo.courseName || '未知课程',
      courseCode: courseInfo.courseCode || '未知编号',
      courseLeader: courseInfo.courseLeader || 0,
      courseCredit: courseInfo.courseCredit || 0,
      courseCore: courseInfo.courseCore || false,
      courseExam: courseInfo.courseExam || false,
      courseHoursTotal: courseInfo.courseHoursTotal || 0,
      courseHoursTheory: courseInfo.courseHoursTheory || 0,
      courseHoursExperiment: courseInfo.courseHoursExperiment || 0,
      courseHoursOther: courseInfo.courseHoursOther || 0,
      courseHoursExtracurricular: courseInfo.courseHoursExtracurricular || 0,
      courseSemester: courseInfo.courseSemester || '未设置',
      courseType1: courseInfo.courseType1 || '',
      courseType2: courseInfo.courseType2 || '',
      courseType3: courseInfo.courseType3 || '',
      courseNature: courseInfo.courseNature || '',
      courseVersion: courseInfo.courseVersion || 1,
      planId: courseInfo.planId
    };

    // 如果有课程目标数据，直接使用
    if (courseInfo.courseObjectives && courseInfo.courseObjectives.length > 0) {
      objectivePoList.value = courseInfo.courseObjectives.map((obj: CourseObjectiveVO) => ({
        id: obj.objectiveId,
        objectiveName: obj.objectiveName,
        poTitle: obj.po?.title || '未知毕业要求'
      }));
    } else {
      // 否则单独获取课程目标列表
      try {
        const objectives = await getCourseTargetList(courseId);
        objectivePoList.value = objectives.map((obj: CourseObjectiveVO) => ({
          id: obj.objectiveId,
          objectiveName: obj.objectiveName,
          poTitle: obj.po?.title || '未知毕业要求'
        }));
      } catch (objectiveError) {
        console.error('获取课程目标失败:', objectiveError);
        objectivePoList.value = [];
      }
    }

    // 获取教师名称（如果需要的话，这里可以添加教师信息获取逻辑）
    if (courseInfo.courseLeader) {
      teacherNameMap.value[courseInfo.courseLeader] = `教师ID: ${courseInfo.courseLeader}`;
    }

  } catch (error) {
    console.error('加载课程详细信息失败:', error);
    MessagePlugin.error('加载课程详细信息失败');
  }
};

// 处理时间轴卡片点击事件
const handleTimelineCardClick = async (course: any) => {
  await loadCourseDetails(course.courseId);
  MessagePlugin.success(`已切换到${course.version}课程数据`);
};

// 处理时间轴详情按钮点击事件
const handleTimelineDetailsClick = (course: any) => {
  dialogCourseId.value = course.courseId;
  achievementDialogVisible.value = true;
};

onMounted(() => {
  // 加载课程历史版本数据
  loadCourseHistoryData();

  console.log('CourseAchievement页面已加载，包含以下功能：');
  console.log('1. 课程基本信息展示');
  console.log('2. 课程目标与毕业要求映射关系');
  console.log('3. 课程历史版本时间轴（水平布局）');
  console.log('4. 数据可视化分析（雷达图、柱状图、趋势图）');
  console.log('5. 响应式设计和现代化UI');

  // Add keyboard event listener
  document.addEventListener('keydown', handleKeyDown);
});

onUnmounted(() => {
  // Remove keyboard event listener
  document.removeEventListener('keydown', handleKeyDown);
});
</script>

<style scoped>
.course-achievement {
  padding: 24px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  min-height: 100vh;
}

/* 卡片通用样式 */
.course-info-card,
.objectives-mapping-card,
.timeline-section-card,
.chart-group-card {
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.course-info-card:hover,
.objectives-mapping-card:hover,
.timeline-section-card:hover,
.chart-group-card:hover {
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  transform: translateY(-2px);
}

/* 卡片头部样式 */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0;
}

.header-title {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
}

.header-icon {
  font-size: 20px;
  color: #3b82f6;
}

.header-info {
  color: #6b7280;
  font-size: 14px;
}

/* 课程基本信息样式 */
.course-info-content {
  padding: 8px 0;
}

.info-item {
  padding: 16px 20px;
  background: #f8fafc;
  border-radius: 12px;
  border-left: 4px solid #3b82f6;
  transition: all 0.2s ease;
}

.info-item:hover {
  background: #eff6ff;
  transform: translateX(4px);
}

.info-label {
  font-size: 12px;
  color: #6b7280;
  font-weight: 500;
  margin-bottom: 4px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.info-value {
  font-size: 16px;
  color: #1f2937;
  font-weight: 600;
}

/* 课程目标映射样式 */
.objectives-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  padding: 8px 0;
}

.objective-mapping-item {
  position: relative;
}

.objective-card {
  display: flex;
  align-items: center;
  padding: 20px;
  background: linear-gradient(135deg, #f8fafc 0%, #eff6ff 100%);
  border-radius: 12px;
  border: 2px solid #e5e7eb;
  transition: all 0.3s ease;
}

.objective-card:hover {
  border-color: #3b82f6;
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(59, 130, 246, 0.15);
}

.objective-header {
  flex: 1;
}

.objective-number {
  display: inline-block;
  padding: 6px 12px;
  background: #3b82f6;
  color: white;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  margin-bottom: 8px;
}

.objective-title {
  font-size: 14px;
  color: #1f2937;
  font-weight: 500;
  line-height: 1.4;
}

.mapping-arrow {
  margin: 0 16px;
  color: #9ca3af;
  font-size: 18px;
}

.po-section {
  flex: 1;
  text-align: right;
}

.po-label {
  font-size: 12px;
  color: #6b7280;
  margin-bottom: 4px;
}

.po-title {
  font-size: 14px;
  color: #1f2937;
  font-weight: 600;
  padding: 8px 12px;
  background: white;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

/* 可视化分析区域样式 */
.visualization-section {
  margin-top: 8px;
}

/* 雷达图容器样式 */
.radar-charts-container {
  width: 100%;
  padding: 0;
}

.radar-charts-row {
  width: 100%;
  margin: 0;
  display: flex;
  flex-wrap: wrap;
}

.radar-charts-row .t-col {
  display: flex;
  flex-direction: column;
}

/* 默认大屏幕保持两列布局 */
@media (min-width: 769px) {
  .radar-charts-row,
  .bar-charts-row {
    flex-direction: row !important;
  }

  .radar-charts-row .t-col,
  .bar-charts-row .t-col {
    flex: 1;
    width: auto !important;
  }
}

.radar-chart-wrapper {
  background: white;
  border-radius: 12px;
  padding: 24px;
  border: 1px solid #f3f4f6;
  transition: all 0.3s ease;
  height: 100%;
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 450px;
}

.radar-chart-wrapper:hover {
  border-color: #e5e7eb;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08);
  transform: translateY(-2px);
}

/* 柱状图容器样式 */
.bar-charts-container {
  width: 100%;
  padding: 0;
}

.bar-charts-row {
  width: 100%;
  margin: 0;
  display: flex;
  flex-wrap: wrap;
}

.bar-charts-row .t-col {
  display: flex;
  flex-direction: column;
}

.bar-chart-wrapper {
  background: white;
  border-radius: 12px;
  padding: 24px;
  border: 1px solid #f3f4f6;
  transition: all 0.3s ease;
  height: 100%;
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 450px;
}

.bar-chart-wrapper:hover {
  border-color: #e5e7eb;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08);
  transform: translateY(-2px);
}

/* 通用图表容器样式 */
.chart-wrapper {
  background: white;
  border-radius: 12px;
  padding: 20px;
  border: 1px solid #f3f4f6;
  transition: all 0.2s ease;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.chart-wrapper:hover {
  border-color: #e5e7eb;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.trend-chart {
  min-height: 400px;
}

/* 对话框样式 */
.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0;
}

.dialog-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

.dialog-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* Full-screen dialog styles */
:deep(.t-dialog__ctx) {
  transition: all 0.3s ease;
}

:deep(.t-dialog__body) {
  max-height: none !important;
  overflow-y: auto;
}

/* 间距工具类 */
.mb-6 {
  margin-bottom: 24px;
}

.mt-4 {
  margin-top: 16px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .objectives-grid {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 16px;
  }
}

@media (max-width: 768px) {
  .course-achievement {
    padding: 16px;
  }

  .header-title {
    font-size: 16px;
  }

  .header-icon {
    font-size: 18px;
  }

  .objectives-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .objective-card {
    flex-direction: column;
    text-align: center;
    gap: 12px;
  }

  .mapping-arrow {
    transform: rotate(90deg);
    margin: 8px 0;
  }

  .po-section {
    text-align: center;
  }

  .radar-chart-wrapper,
  .bar-chart-wrapper {
    padding: 16px;
    min-height: 350px;
  }

  .chart-wrapper {
    padding: 16px;
  }

  /* 小屏幕下切换为垂直布局 */
  .radar-charts-row,
  .bar-charts-row {
    flex-direction: column !important;
  }

  .radar-charts-row .t-col,
  .bar-charts-row .t-col {
    width: 100% !important;
    flex: none !important;
    margin-bottom: 16px;
  }

  .radar-charts-row .t-col:last-child,
  .bar-charts-row .t-col:last-child {
    margin-bottom: 0;
  }
}

/* 中等屏幕适配 - 保持两列布局 */
@media (max-width: 1200px) and (min-width: 769px) {
  .radar-chart-wrapper,
  .bar-chart-wrapper {
    min-height: 400px;
    padding: 20px;
  }

  .radar-charts-container,
  .bar-charts-container {
    padding: 0 12px;
  }

  /* 确保中等屏幕仍然是两列布局 */
  .radar-charts-row,
  .bar-charts-row {
    flex-direction: row;
  }

  .radar-charts-row .t-col,
  .bar-charts-row .t-col {
    flex: 1;
  }
}

@media (max-width: 480px) {
  .course-achievement {
    padding: 12px;
  }

  .info-item {
    padding: 12px 16px;
  }

  .info-value {
    font-size: 14px;
  }

  .objective-card {
    padding: 16px;
  }

  .radar-chart-wrapper {
    padding: 12px;
    min-height: 280px;
  }

  .radar-charts-container {
    padding: 0 8px;
  }
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.course-info-card,
.objectives-mapping-card,
.timeline-section-card,
.chart-group-card {
  animation: fadeInUp 0.6s ease-out;
}

.objectives-mapping-card {
  animation-delay: 0.1s;
}

.timeline-section-card {
  animation-delay: 0.2s;
}

.chart-group-card:nth-child(1) {
  animation-delay: 0.3s;
}

.chart-group-card:nth-child(2) {
  animation-delay: 0.4s;
}

.chart-group-card:nth-child(3) {
  animation-delay: 0.5s;
}
</style>
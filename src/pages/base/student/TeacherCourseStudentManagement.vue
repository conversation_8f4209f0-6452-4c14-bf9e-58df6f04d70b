<template>
  <div class="teacher-course-student-management">
    <!-- 面包屑导航 -->
    <div class="breadcrumb-section">
      <t-breadcrumb>
        <t-breadcrumb-item>课程管理</t-breadcrumb-item>
        <t-breadcrumb-item>{{ courseInfo.name }}</t-breadcrumb-item>
        <t-breadcrumb-item>{{ currentClass.name }}</t-breadcrumb-item>
      </t-breadcrumb>
    </div>

    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="class-info">
          <h2 class="page-title">
            {{ courseInfo.name }} - {{ currentClass.name }}
            <t-tag 
              :theme="currentClass.type === 0 ? 'primary' : 'success'"
              variant="light"
              style="margin-left: 12px;"
            >
              {{ currentClass.type === 0 ? '教学计划班' : '自建班级' }}
            </t-tag>
          </h2>
        </div>
        
        <div class="header-actions">
          <t-select 
            v-model="selectedClassId" 
            placeholder="切换班级"
            style="width: 200px; margin-right: 16px;"
            @change="handleClassChange"
          >
            <t-option 
              v-for="classItem in classList" 
              :key="classItem.id"
              :value="classItem.id" 
              :label="classItem.name"
            />
          </t-select>
          
          <t-button theme="primary" @click="handleAddStudent">
            <template #icon>
              <t-icon name="user-add" />
            </template>
            添加学生
          </t-button>
        </div>
      </div>
      
      <!-- 统计数据 -->
      <div class="stats-section">
        <div class="stat-item">
          <div class="stat-value">{{ studentList.length }}</div>
          <div class="stat-label">总人数</div>
        </div>
        <div class="stat-item">
          <div class="stat-value">{{ maleCount }}</div>
          <div class="stat-label">男生</div>
        </div>
        <div class="stat-item">
          <div class="stat-value">{{ femaleCount }}</div>
          <div class="stat-label">女生</div>
        </div>
        <div class="stat-item">
          <div class="stat-value">{{ assistantCount }}</div>
          <div class="stat-label">助教</div>
        </div>
      </div>
    </div>

    <!-- 工具栏 -->
    <div class="toolbar">
      <div class="toolbar-left">
        <t-input 
          v-model="searchKeyword" 
          placeholder="搜索学号、姓名、专业"
          clearable
          style="width: 300px;"
        >
          <template #prefix-icon>
            <t-icon name="search" />
          </template>
        </t-input>
        
        <t-select 
          v-model="filterGender" 
          placeholder="性别筛选"
          clearable
          style="width: 120px; margin-left: 16px;"
        >
          <t-option value="" label="全部" />
          <t-option value="male" label="男" />
          <t-option value="female" label="女" />
        </t-select>
        
        <t-select 
          v-model="filterAssistant" 
          placeholder="助教筛选"
          clearable
          style="width: 120px; margin-left: 16px;"
        >
          <t-option value="" label="全部" />
          <t-option value="true" label="助教" />
          <t-option value="false" label="普通学生" />
        </t-select>
      </div>
      
      <div class="toolbar-right">
        <t-button variant="outline" @click="handleImportStudents">
          <template #icon>
            <t-icon name="upload" />
          </template>
          批量导入
        </t-button>
        
        <t-button variant="outline" @click="handleExportList" style="margin-left: 8px;">
          <template #icon>
            <t-icon name="download" />
          </template>
          导出名单
        </t-button>
        
        <t-button 
          variant="outline" 
          theme="danger" 
          @click="handleBatchDelete"
          :disabled="selectedStudents.length === 0"
          style="margin-left: 8px;"
        >
          <template #icon>
            <t-icon name="delete" />
          </template>
          批量删除
        </t-button>
      </div>
    </div>

    <!-- 学生列表表格 -->
    <div class="table-container">
      <t-table
        :data="filteredStudents"
        :columns="tableColumns"
        row-key="id"
        :selected-row-keys="selectedStudents"
        @select-change="handleSelectChange"
        :pagination="pagination"
        @page-change="handlePageChange"
        @page-size-change="handlePageSizeChange"
        stripe
        hover
      >
        <template #gender="{ row }">
          <t-tag :theme="row.gender === 'male' ? 'primary' : 'warning'" variant="light">
            {{ row.gender === 'male' ? '男' : '女' }}
          </t-tag>
        </template>
        
        <template #isAssistant="{ row }">
          <t-tag v-if="row.isAssistant" theme="success" variant="light">
            助教
          </t-tag>
          <span v-else class="text-placeholder">-</span>
        </template>
        
        <template #operation="{ row }">
          <t-space>
            <t-button 
              variant="text" 
              size="small" 
              @click="handleSetAssistant(row)"
              :theme="row.isAssistant ? 'warning' : 'primary'"
            >
              {{ row.isAssistant ? '取消助教' : '设为助教' }}
            </t-button>
            
            <t-button 
              variant="text" 
              size="small" 
              theme="primary"
              @click="handleEditStudent(row)"
            >
              编辑
            </t-button>
            
            <t-button 
              variant="text" 
              size="small" 
              theme="danger"
              @click="handleDeleteStudent(row)"
            >
              删除
            </t-button>
          </t-space>
        </template>
      </t-table>
    </div>

    <!-- 添加/编辑学生对话框 -->
    <t-dialog
      v-model:visible="studentDialogVisible"
      :title="isEditMode ? '编辑学生' : '添加学生'"
      width="600px"
      @confirm="handleConfirmStudent"
    >
      <t-form ref="studentFormRef" :model="studentForm" :rules="studentFormRules">
        <t-row :gutter="16">
          <t-col :span="6">
            <t-form-item label="学号" name="studentId">
              <t-input v-model="studentForm.studentId" placeholder="请输入学号" />
            </t-form-item>
          </t-col>
          <t-col :span="6">
            <t-form-item label="姓名" name="name">
              <t-input v-model="studentForm.name" placeholder="请输入姓名" />
            </t-form-item>
          </t-col>
        </t-row>
        
        <t-row :gutter="16">
          <t-col :span="6">
            <t-form-item label="性别" name="gender">
              <t-select v-model="studentForm.gender" placeholder="请选择性别">
                <t-option value="male" label="男" />
                <t-option value="female" label="女" />
              </t-select>
            </t-form-item>
          </t-col>
          <t-col :span="6">
            <t-form-item label="专业" name="major">
              <t-input v-model="studentForm.major" placeholder="请输入专业" />
            </t-form-item>
          </t-col>
        </t-row>
        
        <t-row :gutter="16">
          <t-col :span="6">
            <t-form-item label="班级" name="className">
              <t-input v-model="studentForm.className" placeholder="请输入班级" />
            </t-form-item>
          </t-col>
          <t-col :span="6">
            <t-form-item label="学院" name="academy">
              <t-input v-model="studentForm.academy" placeholder="请输入学院" />
            </t-form-item>
          </t-col>
        </t-row>
        
        <t-form-item label="是否助教" name="isAssistant">
          <t-switch v-model="studentForm.isAssistant" />
        </t-form-item>
        
        <t-form-item label="备注" name="remark">
          <t-textarea 
            v-model="studentForm.remark"
            placeholder="请输入备注信息"
            :autosize="{ minRows: 3, maxRows: 5 }"
          />
        </t-form-item>
      </t-form>
    </t-dialog>

    <!-- 批量导入对话框 -->
    <ImportDialog
      v-model:visible="importDialogVisible"
      :config="importConfig"
      :callbacks="importCallbacks"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { MessagePlugin, DialogPlugin } from 'tdesign-vue-next'
import ImportDialog from '@/components/ImportDialog/index.vue'
import type { ImportConfig, ImportCallbacks } from '@/components/ImportDialog/types'
import { exportStudentList, parseStudentExcel, getStudentTemplateData } from '@/utils/studentFileHandler'

// 定义数据接口
interface StudentInfo {
  id: string
  studentId: string // 学号
  name: string
  gender: 'male' | 'female'
  major: string
  className: string
  academy: string
  isAssistant: boolean // 助教标识
  remark: string
}

interface ClassInfo {
  id: string
  name: string
  type: 0 | 1 // 0: 教学计划班, 1: 自建班级
  studentCount: number
  teacherName: string
  assistants: string[]
  courseId: string
  createTime: string
}

interface CourseInfo {
  id: string
  name: string
}

interface StudentForm {
  studentId: string
  name: string
  gender: 'male' | 'female' | ''
  major: string
  className: string
  academy: string
  isAssistant: boolean
  remark: string
}

const router = useRouter()
const route = useRoute()

// 响应式数据
const searchKeyword = ref('')
const filterGender = ref('')
const filterAssistant = ref('')
const selectedStudents = ref<string[]>([])
const selectedClassId = ref('')
const studentDialogVisible = ref(false)
const isEditMode = ref(false)
const editingStudentId = ref('')
const studentFormRef = ref()

// 分页配置
const pagination = ref({
  current: 1,
  pageSize: 10,
  total: 0,
  showJumper: true,
  showSizer: true
})

// 学生表单
const studentForm = ref<StudentForm>({
  studentId: '',
  name: '',
  gender: '',
  major: '',
  className: '',
  academy: '',
  isAssistant: false,
  remark: ''
})

// 表单验证规则
const studentFormRules = {
  studentId: [{ required: true, message: '请输入学号', trigger: 'blur' as const }],
  name: [{ required: true, message: '请输入姓名', trigger: 'blur' as const }],
  gender: [{ required: true, message: '请选择性别', trigger: 'change' as const }],
  major: [{ required: true, message: '请输入专业', trigger: 'blur' as const }],
  className: [{ required: true, message: '请输入班级', trigger: 'blur' as const }],
  academy: [{ required: true, message: '请输入学院', trigger: 'blur' as const }]
}

// 模拟数据
const courseInfo = ref<CourseInfo>({
  id: route.params.courseId as string || '1',
  name: '软件工程导论'
})

const classList = ref<ClassInfo[]>([
  {
    id: '1',
    name: '软件工程2021级1班',
    type: 0,
    studentCount: 35,
    teacherName: '张老师',
    assistants: ['李助教', '王助教'],
    courseId: '1',
    createTime: '2023-09-01'
  },
  {
    id: '2',
    name: '软件工程2021级2班',
    type: 0,
    studentCount: 32,
    teacherName: '李老师',
    assistants: ['刘助教'],
    courseId: '1',
    createTime: '2023-09-01'
  },
  {
    id: '3',
    name: '特色实验班',
    type: 1,
    studentCount: 25,
    teacherName: '王老师',
    assistants: ['陈助教', '赵助教', '孙助教'],
    courseId: '1',
    createTime: '2024-01-15'
  }
])

const studentList = ref<StudentInfo[]>([
  {
    id: '1',
    studentId: '2021001',
    name: '张三',
    gender: 'male',
    major: '软件工程',
    className: '软件工程2021级1班',
    academy: '计算机学院',
    isAssistant: true,
    remark: '学习委员'
  },
  {
    id: '2',
    studentId: '2021002',
    name: '李四',
    gender: 'female',
    major: '软件工程',
    className: '软件工程2021级1班',
    academy: '计算机学院',
    isAssistant: false,
    remark: ''
  },
  {
    id: '3',
    studentId: '2021003',
    name: '王五',
    gender: 'male',
    major: '软件工程',
    className: '软件工程2021级1班',
    academy: '计算机学院',
    isAssistant: true,
    remark: '班长'
  },
  {
    id: '4',
    studentId: '2021004',
    name: '赵六',
    gender: 'female',
    major: '软件工程',
    className: '软件工程2021级1班',
    academy: '计算机学院',
    isAssistant: false,
    remark: '团支书'
  },
  {
    id: '5',
    studentId: '2021005',
    name: '孙七',
    gender: 'male',
    major: '软件工程',
    className: '软件工程2021级1班',
    academy: '计算机学院',
    isAssistant: false,
    remark: ''
  }
])

// 计算属性
const currentClass = computed(() => {
  const classId = route.params.classId as string || selectedClassId.value || '1'
  return classList.value.find(c => c.id === classId) || classList.value[0]
})

const maleCount = computed(() => {
  return studentList.value.filter(s => s.gender === 'male').length
})

const femaleCount = computed(() => {
  return studentList.value.filter(s => s.gender === 'female').length
})

const assistantCount = computed(() => {
  return studentList.value.filter(s => s.isAssistant).length
})

const filteredStudents = computed(() => {
  let result = studentList.value

  // 关键词搜索
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    result = result.filter(item => 
      item.studentId.toLowerCase().includes(keyword) ||
      item.name.toLowerCase().includes(keyword) ||
      item.major.toLowerCase().includes(keyword)
    )
  }

  // 性别筛选
  if (filterGender.value) {
    result = result.filter(item => item.gender === filterGender.value)
  }

  // 助教筛选
  if (filterAssistant.value !== '') {
    const isAssistant = filterAssistant.value === 'true'
    result = result.filter(item => item.isAssistant === isAssistant)
  }

  // 更新分页总数
  pagination.value.total = result.length

  // 分页处理
  const start = (pagination.value.current - 1) * pagination.value.pageSize
  const end = start + pagination.value.pageSize
  return result.slice(start, end)
})

// 表格列配置
const tableColumns = [
  { colKey: 'row-select', type: 'multiple' as const, width: 50 },
  { colKey: 'studentId', title: '学号', width: 120 },
  { colKey: 'name', title: '姓名', width: 100 },
  { colKey: 'gender', title: '性别', width: 80 },
  { colKey: 'major', title: '专业', width: 150 },
  { colKey: 'className', title: '班级', width: 180 },
  { colKey: 'academy', title: '学院', width: 120 },
  { colKey: 'isAssistant', title: '助教标识', width: 100 },
  { colKey: 'remark', title: '备注', width: 150 },
  { colKey: 'operation', title: '操作', width: 200, fixed: 'right' as const }
]

// 方法
const handleClassChange = (classId: string) => {
  router.push(`/teacher/course/class/${courseInfo.value.id}/student/${classId}`)
}

const handleSelectChange = (value: string[]) => {
  selectedStudents.value = value
}

const handlePageChange = (pageInfo: any) => {
  pagination.value.current = pageInfo.current
}

const handlePageSizeChange = (pageInfo: any) => {
  pagination.value.pageSize = pageInfo.pageSize
  pagination.value.current = 1
}

const handleAddStudent = () => {
  isEditMode.value = false
  studentDialogVisible.value = true
  resetStudentForm()
}

const handleEditStudent = (student: StudentInfo) => {
  isEditMode.value = true
  editingStudentId.value = student.id
  studentDialogVisible.value = true
  
  // 填充表单数据
  studentForm.value = {
    studentId: student.studentId,
    name: student.name,
    gender: student.gender,
    major: student.major,
    className: student.className,
    academy: student.academy,
    isAssistant: student.isAssistant,
    remark: student.remark
  }
}

const handleConfirmStudent = async () => {
  try {
    await studentFormRef.value?.validate()
    
    if (isEditMode.value) {
      // 编辑学生
      const index = studentList.value.findIndex(s => s.id === editingStudentId.value)
      if (index !== -1) {
        studentList.value[index] = {
          ...studentList.value[index],
          ...studentForm.value
        }
        MessagePlugin.success('学生信息更新成功')
      }
    } else {
      // 添加学生
      const newStudent: StudentInfo = {
        id: Date.now().toString(),
        ...studentForm.value
      } as StudentInfo
      
      studentList.value.push(newStudent)
      MessagePlugin.success('学生添加成功')
    }
    
    studentDialogVisible.value = false
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

const handleSetAssistant = (student: StudentInfo) => {
  const index = studentList.value.findIndex(s => s.id === student.id)
  if (index !== -1) {
    studentList.value[index].isAssistant = !studentList.value[index].isAssistant
    const action = studentList.value[index].isAssistant ? '设置' : '取消'
    MessagePlugin.success(`${action}助教成功`)
  }
}

const handleDeleteStudent = (student: StudentInfo) => {
  const dialog = DialogPlugin.confirm({
    title: '确认删除',
    body: `确定要删除学生"${student.name}"吗？`,
    onConfirm: () => {
      const index = studentList.value.findIndex(s => s.id === student.id)
      if (index !== -1) {
        studentList.value.splice(index, 1)
        MessagePlugin.success('删除成功')
      }
      dialog.destroy()
    }
  })
}

const handleBatchDelete = () => {
  if (selectedStudents.value.length === 0) return
  
  const dialog = DialogPlugin.confirm({
    title: '确认批量删除',
    body: `确定要删除选中的 ${selectedStudents.value.length} 个学生吗？`,
    onConfirm: () => {
      studentList.value = studentList.value.filter(s => !selectedStudents.value.includes(s.id))
      selectedStudents.value = []
      MessagePlugin.success('批量删除成功')
      dialog.destroy()
    }
  })
}

// 导入导出相关状态
const importDialogVisible = ref(false)

// 导入配置
const importConfig: ImportConfig = {
  title: '批量导入学生',
  tips: '请按照模板格式填写学生信息，支持批量导入',
  templateFileName: '学生导入模板.xlsx',
  templateData: getStudentTemplateData(),
  acceptTypes: ['.xlsx', '.xls'],
  maxFileSize: 5
}

// 导入回调函数
const importCallbacks: ImportCallbacks = {
  onImport: async (file: File) => {
    try {
      const students = await parseStudentExcel(file)
      
      // 检查学号重复
      const existingStudentIds = studentList.value.map(s => s.studentId)
      const duplicateIds = students.filter(s => existingStudentIds.includes(s.studentId))
      
      if (duplicateIds.length > 0) {
        throw new Error(`以下学号已存在：${duplicateIds.map(s => s.studentId).join(', ')}`)
      }
      
      // 添加到学生列表
      studentList.value.push(...students)
      
      return {
        success: true,
        successMessage: `成功导入 ${students.length} 个学生`,
        successCount: students.length,
        failCount: 0
      }
    } catch (error: any) {
      return {
        success: false,
        errorMessages: [error.message],
        successCount: 0,
        failCount: 1
      }
    }
  },
  onSuccess: () => {
    importDialogVisible.value = false
    MessagePlugin.success('学生导入成功')
  },
  onError: (error: Error) => {
    MessagePlugin.error(`导入失败: ${error.message}`)
  }
}

const handleImportStudents = () => {
  importDialogVisible.value = true
}

const handleExportList = () => {
  // 获取当前筛选后的学生数据
  const studentsToExport = filteredStudents.value
  if (studentsToExport.length === 0) {
    MessagePlugin.warning('没有可导出的学生数据')
    return
  }
  
  // 构建完整的学生数据（包含分页外的数据）
  let allFilteredStudents = studentList.value
  
  // 应用筛选条件
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    allFilteredStudents = allFilteredStudents.filter(item => 
      item.studentId.toLowerCase().includes(keyword) ||
      item.name.toLowerCase().includes(keyword) ||
      item.major.toLowerCase().includes(keyword)
    )
  }
  
  if (filterGender.value) {
    allFilteredStudents = allFilteredStudents.filter(item => item.gender === filterGender.value)
  }
  
  if (filterAssistant.value !== '') {
    const isAssistant = filterAssistant.value === 'true'
    allFilteredStudents = allFilteredStudents.filter(item => item.isAssistant === isAssistant)
  }
  
  const fileName = `${currentClass.value.name}_学生名单`
  exportStudentList(allFilteredStudents, fileName)
}

const resetStudentForm = () => {
  studentForm.value = {
    studentId: '',
    name: '',
    gender: '',
    major: '',
    className: currentClass.value.name,
    academy: '',
    isAssistant: false,
    remark: ''
  }
}

onMounted(() => {
  // 初始化选中的班级
  selectedClassId.value = route.query.classId as string || '1'
  
  // 设置分页总数
  pagination.value.total = studentList.value.length
})
</script>

<style scoped lang="less">
.teacher-course-student-management {
  padding: 24px;
  background-color: var(--td-bg-color-page);
  min-height: calc(100vh - 64px);

  .breadcrumb-section {
    margin-bottom: 16px;
  }

  .page-header {
    background: var(--td-bg-color-container);
    border-radius: var(--td-radius-default);
    padding: 24px;
    margin-bottom: 24px;
    box-shadow: var(--td-shadow-1);

    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 24px;

      .class-info {
        .page-title {
          margin: 0;
          font-size: 20px;
          font-weight: 600;
          color: var(--td-text-color-primary);
          display: flex;
          align-items: center;
        }
      }

      .header-actions {
        display: flex;
        align-items: center;
      }
    }

    .stats-section {
      display: flex;
      gap: 32px;

      .stat-item {
        text-align: center;

        .stat-value {
          font-size: 24px;
          font-weight: 600;
          color: var(--td-text-color-primary);
          margin-bottom: 4px;
        }

        .stat-label {
          font-size: 14px;
          color: var(--td-text-color-secondary);
        }
      }
    }
  }

  .toolbar {
    background: var(--td-bg-color-container);
    border-radius: var(--td-radius-default);
    padding: 16px 24px;
    margin-bottom: 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: var(--td-shadow-1);

    .toolbar-left {
      display: flex;
      align-items: center;
    }

    .toolbar-right {
      display: flex;
      align-items: center;
    }
  }

  .table-container {
    background: var(--td-bg-color-container);
    border-radius: var(--td-radius-default);
    padding: 24px;
    box-shadow: var(--td-shadow-1);
  }

  .text-placeholder {
    color: var(--td-text-color-placeholder);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .teacher-course-student-management {
    padding: 16px;

    .page-header {
      padding: 16px;

      .header-content {
        flex-direction: column;
        gap: 16px;
        align-items: stretch;
      }

      .stats-section {
        justify-content: space-around;
        gap: 16px;
      }
    }

    .toolbar {
      padding: 16px;
      flex-direction: column;
      gap: 16px;
      align-items: stretch;

      .toolbar-left,
      .toolbar-right {
        flex-direction: column;
        gap: 12px;
        align-items: stretch;
      }
    }

    .table-container {
      padding: 16px;
      overflow-x: auto;
    }
  }
}
</style> 
<template>
  <div class="student-tree-container">
    <t-card>
      <template #title>
        <div class="card-title-container">
          <span>学生管理</span>
          <t-button 
            size="small" 
            variant="text" 
            @click="toggleLeftPanel"
            class="toggle-button"
            :title="leftPanelCollapsed ? '显示组织结构' : '隐藏组织结构'"
          >
            <template #icon>
              <t-icon :name="leftPanelCollapsed ? 'chevron-right' : 'chevron-left'" />
            </template>
            {{ leftPanelCollapsed ? '显示面板' : '隐藏面板' }}
          </t-button>
        </div>
      </template>
      
      <!-- 左右布局：左侧组织结构树，右侧学生列表 -->
      <div class="student-management-layout">
        
        <!-- 左侧组织结构树 -->
        <div class="left-panel" :class="{ 'collapsed': leftPanelCollapsed }">
          <t-card title="组织结构" :bordered="false" class="org-tree-card">
            <t-loading :loading="treeLoading">
              <div v-if="treeData.length === 0 && !treeLoading" class="empty-tree">
                <t-empty description="暂无组织结构数据" />
              </div>
              <t-tree
                v-else
                ref="treeRef"
                :data="treeData"
                :keys="treeKeys"
                hover
                expand-all
                line
                @click="handleTreeClick"
                :active="[activeNodeKey]"
                style="height: 600px; overflow-y: auto;"
              >
                <template #icon="{ node }">
                  <t-icon 
                    :name="node.data.type === 'school' ? 'home' : 
                           node.data.type === 'college' ? 'institution' : 
                           node.data.type === 'major' ? 'chart' : 'user'"
                    size="16px"
                  />
                </template>
                <template #label="{ node }">
                  <span class="tree-node-label">
                    {{ node.data.label }}
                    <span class="student-count" v-if="node.data.studentCount !== undefined">
                      ({{ node.data.studentCount }}人)
                    </span>
                  </span>
                </template>
              </t-tree>
            </t-loading>
          </t-card>
        </div>

        <!-- 右侧学生列表 -->
        <div class="right-panel">
          <t-card>
            <template #title>
              <!-- 层级路径显示 -->
            <div class="hierarchy-path" style="margin-bottom: 16px;">
              <t-alert theme="info">
                <template #message>
                  <div class="path-display">
                    <t-icon name="location" size="16px" style="margin-right: 8px;" />
                    <span class="path-text">{{ rightPanelTitle }}</span>
                  </div>
                </template>
              </t-alert>
            </div>
            </template>

            <!-- 搜索区域 -->
            <div class="search-container" style="margin-bottom: 20px;">
              <!-- 搜索切换按钮 -->
              <div class="search-toggle" style="margin-bottom: 12px;">
                <t-button 
                  variant="text" 
                  size="small" 
                  @click="toggleAdvancedSearch"
                  style="color: var(--td-brand-color);"
                >
                  <template #icon>
                    <t-icon :name="showAdvancedSearch ? 'chevron-up' : 'chevron-down'" />
                  </template>
                  {{ showAdvancedSearch ? '收起高级搜索' : '展开高级搜索' }}
                </t-button>
              </div>
              
              <t-form ref="form" :data="searchForm" layout="inline">
                <!-- 基础搜索项 -->
                <t-form-item label="学生姓名">
                  <t-input
                    v-model="searchForm.name"
                    placeholder="请输入学生姓名"
                    clearable
                  />
                </t-form-item>
                <t-form-item label="学号">
                  <t-input
                    v-model="searchForm.studentId"
                    placeholder="请输入学号"
                    clearable
                  />
                </t-form-item>
                
                <!-- 高级搜索项 -->
                <template v-if="showAdvancedSearch">
                <t-form-item label="学院" v-if="!isSpecificNodeSelected">
                  <t-select
                    v-model="searchForm.collegeId"
                    :options="collegeOptions"
                    placeholder="请选择学院"
                    clearable
                  />
                </t-form-item>
                <t-form-item label="专业" v-if="selectedCollegeId && !selectedMajorId">
                  <t-select
                    v-model="searchForm.majorId"
                    :options="majorOptions"
                    placeholder="请选择专业"
                    clearable
                  />
                </t-form-item>
                <t-form-item label="手机号">
                  <t-input
                    v-model="searchForm.phone"
                    placeholder="请输入手机号"
                    clearable
                  />
                </t-form-item>
                <t-form-item label="邮箱">
                  <t-input
                    v-model="searchForm.email"
                    placeholder="请输入邮箱"
                    clearable
                  />
                </t-form-item>
                <t-form-item label="班级">
                  <t-input
                    v-model="searchForm.className"
                    placeholder="请输入班级"
                    clearable
                  />
                </t-form-item>
                <t-form-item label="性别">
                  <t-select
                    v-model="searchForm.gender"
                    :options="genderOptions"
                    placeholder="请选择性别"
                    clearable
                  />
                </t-form-item>
                <t-form-item label="入学年份">
                  <t-select
                    v-model="searchForm.entranceYear"
                    :options="entranceYearOptions"
                    placeholder="请选择入学年份"
                    clearable
                  />
                </t-form-item>
                </template>
                <t-form-item>
                  <t-space>
                    <t-button theme="primary" @click="handleSearch">查询</t-button>
                    <t-button theme="default" @click="resetSearch">重置</t-button>
                  </t-space>
                </t-form-item>
              </t-form>
            </div>

            <!-- 列设置按钮 -->
            <div class="table-toolbar" style="display:flex;align-items:center;justify-content:space-between;margin-bottom:8px;">
              <div></div>
              <t-button variant="text" shape="round" style="color:var(--td-brand-color);" @click="showColumnSetting = true">
                <t-icon name="setting" /> 列设置
              </t-button>
            </div>

            <!-- 操作按钮区放到actions插槽中 -->
            <template #actions>
              <t-space>
                <t-button theme="primary" @click="handleAddStudent">
                  <template #icon><t-icon name="add" /></template>新增学生
                </t-button>
                <t-button theme="default" @click="handleSyncStudentCount">
                  <template #icon><t-icon name="refresh" /></template>同步班级人数
                </t-button>
                <t-button theme="default" @click="openImportDialog">
                  <template #icon><t-icon name="upload" /></template>导入
                </t-button>
                <t-button theme="default" @click="exportStudentData">
                  <template #icon><t-icon name="download" /></template>导出
                </t-button>
                <t-button theme="default" @click="refreshData">
                  <template #icon><t-icon name="refresh" /></template>刷新
                </t-button>
              </t-space>
            </template>

            <!-- 表格区域 -->
            <t-loading :loading="loading">
              <t-table
                :data="tableData"
                :columns="visibleColumns"
                row-key="id"
                hover
                stripe
                :pagination="pagination"
                :max-height="600"
                class="fade-table"
              >
                <template #serial-number="{ rowIndex }">
                  {{ (pagination.current - 1) * pagination.pageSize + rowIndex + 1 }}
                </template>
                <template #gender="{ row }">
                  <t-tag :theme="genderTagTheme(row.gender)" style="border-radius: 8px; transition: all 0.2s;">
                    {{ genderMap[row.gender] || row.gender }}
                  </t-tag>
                </template>
                <template #studentStatus="{ row }">
                  <t-tag :theme="studentStatusTagTheme(row.studentStatus)" style="border-radius: 8px; transition: all 0.2s;">
                    {{ studentStatusMap[row.studentStatus] || row.studentStatus }}
                  </t-tag>
                </template>
                <template #status="{ row }">
                  <t-tag :theme="recordStatusTagTheme(row.status)" style="border-radius: 8px; transition: all 0.2s;">
                    {{ recordStatusMap[row.status] || row.status }}
                  </t-tag>
                </template>
                <template #operation="{ row }">
                  <div class="operation-container">
                    <t-space size="small">
                      <t-button size="small" variant="outline" theme="primary" @click="viewStudent(row)">
                        <template #icon><t-icon name="browse" /></template>
                        查看
                      </t-button>
                      <t-button size="small" variant="outline" theme="primary" @click="editStudent(row)">
                        <template #icon><t-icon name="edit" /></template>
                        编辑
                      </t-button>
                      <t-button size="small" variant="outline" theme="danger" @click="deleteStudent(row)">
                        <template #icon><t-icon name="delete" /></template>
                        删除
                      </t-button>
                    </t-space>
                  </div>
                </template>
              </t-table>
            </t-loading>
          </t-card>
        </div>
      </div>
    </t-card>

    <!-- 查看详情对话框 -->
    <t-dialog
      v-model:visible="detailVisible"
      header="学生详情"
      :footer="false"
      width="900px"
      :close-on-overlay-click="true"
    >
      <template v-if="currentStudent">
        <!-- 基本信息 -->
        <div class="detail-section">
          <h4 class="detail-section-title">
            <t-icon name="user" style="margin-right: 8px;" />
            基本信息
          </h4>
          <t-descriptions
            layout="horizontal"
            bordered
            :column="2"
            size="medium"
          >
            <t-descriptions-item label="学生ID">{{ currentStudent.id }}</t-descriptions-item>
            <t-descriptions-item label="学生姓名">{{ currentStudent.name }}</t-descriptions-item>
            <t-descriptions-item label="学号">{{ currentStudent.studentNumber }}</t-descriptions-item>
            <t-descriptions-item label="性别">{{ getGenderText(currentStudent.gender) }}</t-descriptions-item>
            <t-descriptions-item label="手机号">{{ currentStudent.phone || '未填写' }}</t-descriptions-item>
            <t-descriptions-item label="邮箱">{{ currentStudent.email || '未填写' }}</t-descriptions-item>
          </t-descriptions>
        </div>
        
        <!-- 学籍信息 -->
        <div class="detail-section" style="margin-top: 20px;">
          <h4 class="detail-section-title">
            <t-icon name="education" style="margin-right: 8px;" />
            学籍信息
          </h4>
          <t-descriptions
            layout="horizontal"
            bordered
            :column="2"
            size="medium"
          >
            <t-descriptions-item label="学院">{{ currentStudent.college }}</t-descriptions-item>
            <t-descriptions-item label="专业">{{ currentStudent.majorName }}</t-descriptions-item>
            <t-descriptions-item label="班级">{{ currentStudent.classValue }}</t-descriptions-item>
            <t-descriptions-item label="入学年份">{{ currentStudent.entranceYear }}</t-descriptions-item>
            <t-descriptions-item label="学籍状态">
              <t-tag :theme="studentStatusTagTheme(currentStudent.studentStatus)" style="border-radius:8px;">{{ getStudentStatusText(currentStudent.studentStatus) }}</t-tag>
            </t-descriptions-item>
            <t-descriptions-item label="记录状态">
              <t-tag :theme="recordStatusTagTheme(currentStudent.status)" style="border-radius:8px;">{{ getRecordStatusText(currentStudent.status) }}</t-tag>
            </t-descriptions-item>
          </t-descriptions>
        </div>
        
        <!-- 系统信息 -->
        <div class="detail-section" style="margin-top: 20px;">
          <h4 class="detail-section-title">
            <t-icon name="setting" style="margin-right: 8px;" />
            系统信息
          </h4>
          <t-descriptions
            layout="horizontal"
            bordered
            :column="2"
            size="medium"
          >
            <t-descriptions-item label="创建人">{{ currentStudent.creator }}</t-descriptions-item>
            <t-descriptions-item label="创建时间">{{ formatDate(currentStudent.createTime, 'YYYY-MM-DD HH:mm') }}</t-descriptions-item>
            <t-descriptions-item label="修改人">{{ currentStudent.modifier }}</t-descriptions-item>
            <t-descriptions-item label="修改时间">{{ formatDate(currentStudent.modifyTime, 'YYYY-MM-DD HH:mm') }}</t-descriptions-item>
          </t-descriptions>
        </div>
      </template>
    </t-dialog>

    <!-- 编辑对话框 -->
    <t-dialog
      v-model:visible="editVisible"
      header="编辑学生"
      :footer="false"
      width="600px"
      :close-on-overlay-click="false"
    >
      <t-form
        ref="formRef"
        :data="formData"
        :rules="rules"
        label-width="100px"
        @submit="handleSubmit"
      >
        <t-form-item label="学生姓名" name="name">
          <t-input v-model="formData.name" placeholder="请输入学生姓名（必填）" />
        </t-form-item>

        <t-form-item label="学号" name="number">
          <t-input v-model="formData.number" placeholder="请输入学号（必填）" />
        </t-form-item>

        <t-form-item label="性别" name="gender">
          <t-radio-group v-model="formData.gender">
            <t-radio :value="1">男</t-radio>
            <t-radio :value="2">女</t-radio>
            <t-radio :value="0">保密</t-radio>
          </t-radio-group>
        </t-form-item>

        <t-form-item label="手机号" name="phone">
          <t-input v-model="formData.phone" placeholder="请输入手机号（必填）" />
        </t-form-item>

        <t-form-item label="邮箱" name="email">
          <t-input v-model="formData.email" placeholder="请输入邮箱（必填）" />
        </t-form-item>

        <t-form-item label="学院" name="collegeId">
          <t-space style="width: 100%;">
            <t-select 
              v-model="formData.collegeId" 
              :options="collegeOptions" 
              placeholder="请选择学院（必填）"
              style="flex: 1;"
              @change="handleCollegeChange"
            />
            <!-- <t-button size="small" variant="outline" @click="showAddCollegeDialog = true">
              <template #icon><t-icon name="add" /></template>
              添加学院
            </t-button> -->
          </t-space>
        </t-form-item>
        <t-form-item label="专业" name="majorId">
          <t-space style="width: 100%;">
            <t-select 
              v-model="formData.majorId" 
              :options="majorOptions" 
              placeholder="请选择专业（必填）"
              style="flex: 1;"
              @change="handleMajorChange"
            />
            <!-- <t-button size="small" variant="outline" @click="showAddMajorDialog = true">
              <template #icon><t-icon name="add" /></template>
              添加专业
            </t-button> -->
          </t-space>
        </t-form-item>
        <t-form-item label="班级" name="classId">
          <t-space style="width: 100%;">
            <t-select 
              v-model="formData.classId" 
              :options="classOptions" 
              placeholder="请选择班级（必填）"
              style="flex: 1;"
            />
            <!-- <t-button size="small" variant="outline" @click="showAddClassDialog = true">
              <template #icon><t-icon name="add" /></template>
              添加班级
            </t-button> -->
          </t-space>
        </t-form-item>

        <t-form-item label="入学年份" name="entranceYear">
          <t-select 
            v-model="formData.entranceYear" 
            :options="entranceYearOptions" 
            placeholder="请选择入学年份（必填）"
            style="width: 100%;"
          />
        </t-form-item>

        <t-form-item>
          <t-space>
            <t-button theme="primary" type="submit">保存</t-button>
            <t-button theme="default" variant="base" @click="editVisible = false">取消</t-button>
          </t-space>
        </t-form-item>
      </t-form>
    </t-dialog>

    <!-- 新增学生对话框 -->
    <t-dialog
      v-model:visible="addVisible"
      header="新增学生"
      :footer="false"
      width="600px"
      :close-on-overlay-click="false"
    >
      <t-form
        ref="addFormRef"
        :data="formData"
        :rules="rules"
        label-width="100px"
        @submit="handleAddSubmit"
      >
        <t-form-item label="学生姓名" name="name">
          <t-input v-model="formData.name" placeholder="请输入学生姓名（必填）" />
        </t-form-item>

        <t-form-item label="学号" name="number">
          <t-input v-model="formData.number" placeholder="请输入学号（必填）" />
        </t-form-item>

        <t-form-item label="性别" name="gender">
          <t-radio-group v-model="formData.gender">
            <t-radio :value="1">男</t-radio>
            <t-radio :value="2">女</t-radio>
            <t-radio :value="0">保密</t-radio>
          </t-radio-group>
        </t-form-item>

        <t-form-item label="手机号" name="phone">
          <t-input v-model="formData.phone" placeholder="请输入手机号（必填）" />
        </t-form-item>

        <t-form-item label="邮箱" name="email">
          <t-input v-model="formData.email" placeholder="请输入邮箱（必填）" />
        </t-form-item>

        <t-form-item label="学院" name="collegeId">
          <t-select 
            v-model="formData.collegeId" 
            :options="collegeOptions" 
            placeholder="请选择学院（必填）"
            style="width: 100%;"
            @change="handleCollegeChange"
          />
        </t-form-item>
        <t-form-item label="专业" name="majorId">
          <t-select 
            v-model="formData.majorId" 
            :options="majorOptions" 
            placeholder="请选择专业（必填）"
            @change="handleMajorChange"
            style="width: 100%;"
          />
        </t-form-item>
        <t-form-item label="班级" name="classId">
          <t-select 
            v-model="formData.classId" 
            :options="classOptions" 
            placeholder="请选择班级（必填）"
            style="width: 100%;"
          />
        </t-form-item>

        <t-form-item label="入学年份" name="entranceYear">
          <t-select 
            v-model="formData.entranceYear" 
            :options="entranceYearOptions" 
            placeholder="请选择入学年份（必填）"
            style="width: 100%;"
          />
        </t-form-item>

        <t-form-item>
          <t-space>
            <t-button theme="primary" type="submit">添加</t-button>
            <t-button theme="default" variant="base" @click="addVisible = false">取消</t-button>
          </t-space>
        </t-form-item>
      </t-form>
    </t-dialog>

    <!-- 导入对话框 -->
    <ImportDialog
      v-model:visible="importVisible"
      :config="importConfig"
      :callbacks="importCallbacks"
    />

    <!-- 添加学院对话框 -->
    <t-dialog
      v-model:visible="showAddCollegeDialog"
      header="添加学院"
      :footer="false"
      width="500px"
      :close-on-overlay-click="false"
    >
      <t-form
        ref="addCollegeFormRef"
        :data="addCollegeForm"
        :rules="addCollegeRules"
        label-width="100px"
        @submit="handleAddCollege"
      >
        <t-form-item label="学院名称" name="academyName">
          <t-input v-model="addCollegeForm.academyName" placeholder="请输入学院名称" />
        </t-form-item>

        <t-form-item>
          <t-space>
            <t-button theme="primary" type="submit">添加</t-button>
            <t-button theme="default" variant="base" @click="showAddCollegeDialog = false">取消</t-button>
          </t-space>
        </t-form-item>
      </t-form>
    </t-dialog>

    <!-- 添加专业对话框 -->
    <t-dialog
      v-model:visible="showAddMajorDialog"
      header="添加专业"
      :footer="false"
      width="500px"
      :close-on-overlay-click="false"
    >
      <t-form
        ref="addMajorFormRef"
        :data="addMajorForm"
        :rules="addMajorRules"
        label-width="100px"
        @submit="handleAddMajor"
      >
        <t-form-item label="专业名称" name="majorName">
          <t-input v-model="addMajorForm.majorName" placeholder="请输入专业名称" />
        </t-form-item>

        <t-form-item label="专业代码" name="majorCode">
          <t-input v-model="addMajorForm.majorCode" placeholder="请输入专业代码（可选）" />
        </t-form-item>

        <t-form-item label="所属学院" name="academyId">
          <t-select 
            v-model="addMajorForm.academyId" 
            :options="collegeOptions" 
            placeholder="请选择学院"
            style="width: 100%;"
          />
        </t-form-item>

        <t-form-item>
          <t-space>
            <t-button theme="primary" type="submit">添加</t-button>
            <t-button theme="default" variant="base" @click="showAddMajorDialog = false">取消</t-button>
          </t-space>
        </t-form-item>
      </t-form>
    </t-dialog>

    <!-- 添加班级对话框 -->
    <t-dialog
      v-model:visible="showAddClassDialog"
      header="添加班级"
      :footer="false"
      width="500px"
      :close-on-overlay-click="false"
    >
      <t-form
        ref="addClassFormRef"
        :data="addClassForm"
        :rules="addClassRules"
        label-width="100px"
        @submit="handleAddClass"
      >
        <t-form-item label="班级名称" name="className">
          <t-input v-model="addClassForm.className" placeholder="请输入班级名称" />
        </t-form-item>

        <t-form-item label="所属专业" name="majorId">
          <t-select 
            v-model="addClassForm.majorId" 
            :options="majorOptions" 
            placeholder="请选择专业"
            style="width: 100%;"
          />
        </t-form-item>

        <t-form-item label="入学年份" name="entranceYear">
          <t-input v-model="addClassForm.entranceYear" placeholder="请输入入学年份，如：2024" />
        </t-form-item>

        <t-form-item>
          <t-space>
            <t-button theme="primary" type="submit">添加</t-button>
            <t-button theme="default" variant="base" @click="showAddClassDialog = false">取消</t-button>
          </t-space>
        </t-form-item>
      </t-form>
    </t-dialog>

    <!-- 列设置弹窗 -->
    <t-dialog
      v-model:visible="showColumnSetting"
      header="列设置"
      :footer="false"
      width="600px"
      :close-on-overlay-click="true"
    >
      <div class="column-setting-content">
        <div class="column-setting-header">
          <t-checkbox 
            v-model="selectAllColumns" 
            @change="handleSelectAllColumns"
            style="margin-bottom: 16px;"
          >
            全选
          </t-checkbox>
        </div>
        <div class="column-setting-list">
          <t-checkbox-group v-model="selectedColumnKeys" style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 12px;">
            <t-checkbox 
              v-for="option in columnOptions" 
              :key="option.value" 
              :value="option.value"
              style="padding: 8px; border-radius: 6px; transition: background-color 0.2s;"
            >
              {{ option.label }}
            </t-checkbox>
          </t-checkbox-group>
        </div>
        <div class="column-setting-footer" style="margin-top: 16px; display: flex; gap: 8px; justify-content: flex-end;">
          <t-button theme="default" @click="resetColumnSettings">重置默认</t-button>
          <t-button theme="primary" @click="showColumnSetting = false">确定</t-button>
        </div>
      </div>
    </t-dialog>

          <!-- 同步班级人数预览弹窗 -->
      <t-dialog 
        v-model:visible="syncPreviewVisible" 
        header="同步班级人数预览" 
        width="700px"
        :confirm-btn="{
          content: '立即同步',
          variant: 'base',
          loading: syncLoading,
          onClick: confirmSyncStudentCount
        }"
        :cancel-btn="{
          content: '取消',
          variant: 'outline',
          onClick: () => syncPreviewVisible = false
        }"
      >
        <t-table :data="syncPreviewList" :columns="syncPreviewColumns" row-key="classId" bordered stripe size="small" />
      </t-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, defineExpose, watch } from 'vue';
import { MessagePlugin, DialogPlugin } from 'tdesign-vue-next';
import { PrimaryTableCol, TableRowData } from 'tdesign-vue-next';
import { formatDate } from '@/utils/date';
import { getAcademyOptions, addAcademy, checkAcademyNameExists } from '@/api/base/academy';
import { getMajorsByCollegeId, addMajor, checkMajorNameExists } from '@/api/base/major';
import { getClassesByMajorId, addClass, checkClassNameExists, syncStudentCount, previewSyncStudentCount } from '@/api/base/classes';
import {
  getStudentList,
  getStudentDetail,
  getStudentDetailById,
  addStudent as addStudentApi,
  updateStudent as updateStudentApi,
  deleteStudent as deleteStudentApi,
  importStudents,
  exportStudents,
  getStudentTree
} from '@/api/base/student';
import { StudentInfo, StudentQueryParams } from '@/api/model/student/studentModel';
import ImportDialog from '@/components/ImportDialog/index.vue';
import type { ImportConfig, ImportCallbacks } from '@/components/ImportDialog/types';
import SemesterUtils from '@/utils/semesterUtils';

// 扩展学生信息接口以包含前端特有字段
interface ExtendedStudentInfo extends StudentInfo {
  enrollmentYear?: string;
  updater?: string;
  updateTime?: string;
}

// 搜索条件
const searchForm = reactive({
  collegeId: '',
  majorId: '',
  name: '',
  studentId: '',
  className: '',
  gender: '',
  classId: '',
  studentStatus: '',
  entranceYear: '',
  status: '',
  phone: '',
  email: ''
});

// 组织结构树相关
const treeRef = ref();
const treeLoading = ref(false);
const treeData = ref([]);
const activeNodeKey = ref('all');
const showAdvancedSearch = ref(false);
const leftPanelCollapsed = ref(false);
const selectedCollegeId = ref<number | string>('');
const selectedMajorId = ref<number | string>('');
const selectedClassId = ref<number | string>('');
const selectedCollegeTreeId = ref<string>(''); // 存储学院的树形ID
const selectedMajorTreeId = ref<string>(''); // 存储专业的树形ID
const selectedClassTreeId = ref<string>(''); // 存储班级的树形ID
const selectedNodeInfo = ref('');

// 树形控件配置
const treeKeys = {
  value: 'id',
  label: 'label',
  children: 'children'
};

// 专业选项数据
const majorOptions = ref<{ label: string; value: string }[]>([]);

// 班级选项数据
const classOptions = ref<{ label: string; value: string }[]>([]);

// 入学年份选项数据
const entranceYearOptions = ref<{ label: string; value: string }[]>([]);

// 计算属性
const isSpecificNodeSelected = computed(() => {
  return selectedCollegeId.value || selectedMajorId.value || selectedClassId.value;
});

const rightPanelTitle = computed(() => {
  let title = '学生列表';
  
  if (selectedClassTreeId.value) {
    // 选择了班级，显示：学院-专业-班级-学生列表
    const classPath = findNodePath(treeData.value, selectedClassTreeId.value);
    if (classPath.length >= 3) {
      const college = classPath[1]; // 学院
      const major = classPath[2];   // 专业
      const classNode = classPath[3]; // 班级
      title = `${college.label}-${major.label}-${classNode.label}-学生列表`;
    }
  } else if (selectedMajorTreeId.value) {
    // 选择了专业，显示：学院-专业-学生列表
    const majorPath = findNodePath(treeData.value, selectedMajorTreeId.value);
    if (majorPath.length >= 3) {
      const college = majorPath[1]; // 学院
      const major = majorPath[2];   // 专业
      title = `${college.label}-${major.label}-学生列表`;
    }
  } else if (selectedCollegeTreeId.value) {
    // 选择了学院，显示：学院-学生列表
    const collegePath = findNodePath(treeData.value, selectedCollegeTreeId.value);
    if (collegePath.length >= 2) {
      const college = collegePath[1]; // 学院
      title = `${college.label}-学生列表`;
    }
  } else {
    // 未选择具体节点，显示全部
    title = '全部-学生列表';
  }
  
  return title;
});

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  pageSizeOptions: [10, 20, 50],
  showJumper: true,
  showPageSize: true,
  onChange: (pageInfo: any) => {
    pagination.current = pageInfo.current;
    pagination.pageSize = pageInfo.pageSize;
    fetchStudentList();
  },
});

// 表格数据
const tableData = ref<ExtendedStudentInfo[]>([]);
const loading = ref(false);

// 学院数据
const colleges = ref<{ id: string; name: string }[]>([]);

// 计算属性 - 学院选项
const collegeOptions = computed(() => {
  return colleges.value.map(college => ({
    label: college.name,
    value: String(college.id)
  }));
});

// 性别选项
const genderOptions = [
  { label: '男', value: '男' },
  { label: '女', value: '女' }
];

// 状态映射工具函数
const genderMap: Record<string | number, string> = {
  0: '保密',
  1: '男',
  2: '女',
  '男': '男',
  '女': '女',
  '保密': '保密',
};
const genderTagTheme = (val: any) => {
  if (val === 1 || val === '男') return 'primary';
  if (val === 2 || val === '女') return 'danger';
  return 'default';
};
const studentStatusMap: Record<string | number, string> = {
  '-1': '毕业',
  0: '在读',
  1: '休学',
  2: '退学',
  3: '毕业',
};
const studentStatusTagTheme = (val: any) => {
  if (val === 0) return 'success';
  if (val === 1) return 'warning';
  if (val === 2) return 'danger';
  if (val === -1 || val === 3) return 'default';
  return 'default';
};
const recordStatusMap: Record<string | number, string> = {
  0: '正常',
  1: '停用',
  '-1': '删除',
};
const recordStatusTagTheme = (val: any) => {
  if (val === 0) return 'success';
  if (val === 1) return 'warning';
  if (val === -1) return 'default';
  return 'default';
};

// 从API获取学院数据
const fetchColleges = async () => {
  try {
    const result = await getAcademyOptions();
    if (result && result.code === 200 && result.data) {
      colleges.value = result.data.map((item: any) => ({
        id: String(item.value),
        name: item.label
      }));
    } else {
      colleges.value = [];
      MessagePlugin.warning(result?.msg || '获取学院数据失败');
    }
  } catch (error) {
    console.error('获取学院数据失败:', error);
    MessagePlugin.error('获取学院数据失败');
    colleges.value = [];
  }
};

// 表格列配置
const columns: PrimaryTableCol<TableRowData>[] = [
  { colKey: 'serial-number', title: '序号', width: 80, align: 'center' },
  { colKey: 'id', title: '学生ID', width: 100 },
  { colKey: 'name', title: '学生姓名', width: 120 },
  { colKey: 'studentNumber', title: '学号', width: 120 },
  { colKey: 'gender', title: '性别', width: 80, align: 'center' },
  { colKey: 'phone', title: '手机号', width: 120 },
  { colKey: 'email', title: '邮箱', width: 180 },
  { colKey: 'college', title: '学院', width: 180 },
  { colKey: 'majorName', title: '专业', width: 140 },
  { colKey: 'classValue', title: '班级', width: 120 },
  { colKey: 'entranceYear', title: '入学年份', width: 100 },
  { colKey: 'studentStatus', title: '学籍状态', width: 100, align: 'center' },
  { colKey: 'creator', title: '创建人', width: 100 },
  {
    colKey: 'createTime',
    title: '创建时间',
    width: 140,
    cell: (h, params) => formatDate(params.row.createTime as string, 'YYYY-MM-DD HH:mm')
  },
  { colKey: 'modifier', title: '修改人', width: 100 },
  {
    colKey: 'modifyTime',
    title: '修改时间',
    width: 140,
    cell: (h, params) => formatDate(params.row.modifyTime as string, 'YYYY-MM-DD HH:mm')
  },
  { colKey: 'status', title: '记录状态', width: 100, align: 'center' },
  { colKey: 'operation', title: '操作', width: 300, fixed: 'right', align: 'center' },
];

// 获取学生列表 - 使用封装的API调用
const fetchStudentList = async () => {
  loading.value = true;
  try {
    const queryParams: StudentQueryParams = {
      current: pagination.current,
      size: pagination.pageSize,
      academyId: searchForm.collegeId ? Number(searchForm.collegeId) : undefined,
      majorId: searchForm.majorId ? Number(searchForm.majorId) : undefined,
      classId: searchForm.classId ? Number(searchForm.classId) : undefined,
      studentStatus: searchForm.studentStatus ? Number(searchForm.studentStatus) : undefined,
      gender: searchForm.gender === '男' ? 1 : searchForm.gender === '女' ? 2 : undefined,
      entranceYear: searchForm.entranceYear || undefined,
      status: searchForm.status ? Number(searchForm.status) : undefined,
      studentName: searchForm.name || undefined,
      studentNumber: searchForm.studentId || undefined,
      phone: searchForm.phone || undefined,
      email: searchForm.email || undefined
    };
    
    // 过滤掉空字符串的参数，避免类型转换错误
    Object.keys(queryParams).forEach(key => {
      const typedKey = key as keyof StudentQueryParams;
      if (queryParams[typedKey] === '' || queryParams[typedKey] === null || queryParams[typedKey] === undefined) {
        delete queryParams[typedKey];
      }
    });
    
    // 调用封装的API
    const result = await getStudentList(queryParams);
    
    if (result.code === 200) {
      tableData.value = result.data.records || [];
      pagination.total = result.data.total || 0;
      
      // 如果没有数据，显示友好提示
      if (tableData.value.length === 0) {
        const currentFilter = selectedNodeInfo.value || '当前筛选条件';
        console.log(`${currentFilter}下暂无学生数据`);
      }
    } else {
      throw new Error(result.message || '获取数据失败');
    }
  } catch (error) {
    console.error('获取学生列表失败:', error);
    
    // 检查是否是类型转换错误
    const errorMessage = (error as Error).message;
    if (errorMessage.includes('Failed to convert property value')) {
      MessagePlugin.warning('查询参数格式错误，请重新选择筛选条件');
    } else {
      MessagePlugin.error('获取学生列表失败: ' + errorMessage);
    }
    
    tableData.value = [];
    pagination.total = 0;
  } finally {
    loading.value = false;
  }
};

// 重置搜索条件
const resetSearch = () => {
  searchForm.collegeId = '';
  searchForm.majorId = '';
  searchForm.name = '';
  searchForm.studentId = '';
  searchForm.className = '';
  searchForm.gender = '';
  searchForm.phone = '';
  searchForm.email = '';
  searchForm.entranceYear = '';
  pagination.current = 1;
  fetchStudentList();
};

// 搜索
const handleSearch = () => {
  pagination.current = 1;
  fetchStudentList();
};

// 对话框控制
const detailVisible = ref(false);
const editVisible = ref(false);
const addVisible = ref(false);
const currentStudent = ref<ExtendedStudentInfo | null>(null);
const formRef = ref(null);
const addFormRef = ref(null);
const addCollegeFormRef = ref(null);
const addMajorFormRef = ref(null);
const addClassFormRef = ref(null);

// 表单数据
const formData = reactive({
  id: '',
  name: '',
  number: '',
  gender: 0,
  collegeId: '',
  majorId: '',
  className: '',
  phone: '',
  email: '',
  classId: '',
  entranceYear: '',
  studentStatus: 0
});

// 重置表单数据
const resetFormData = () => {
  formData.id = '';
  formData.name = '';
  formData.number = '';
  formData.gender = 0;
  formData.collegeId = '';
  formData.majorId = '';
  formData.className = '';
  formData.phone = '';
  formData.email = '';
  formData.classId = '';
  formData.entranceYear = '';
  formData.studentStatus = 0;
};

// 表单校验规则
const rules = {
  name: [
    { required: true, message: '请输入学生姓名', trigger: 'blur' as const },
    { max: 50, message: '学生姓名不能超过50个字符', trigger: 'blur' as const }
  ],
  number: [
    { required: true, message: '请输入学号', trigger: 'blur' as const },
    { max: 20, message: '学号不能超过20个字符', trigger: 'blur' as const }
  ],
  gender: [
    { required: true, message: '请选择性别', trigger: 'change' as const }
  ],
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' as const },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号格式', trigger: 'blur' as const }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' as const },
    { pattern: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/, message: '请输入正确的邮箱格式', trigger: 'blur' as const }
  ],
  collegeId: [
    { required: true, message: '请选择学院', trigger: 'change' as const }
  ],
  majorId: [
    { required: true, message: '请选择专业', trigger: 'change' as const }
  ],
  classId: [
    { required: true, message: '请选择班级', trigger: 'change' as const }
  ],
  entranceYear: [
    { required: true, message: '请选择入学年份', trigger: 'change' as const }
  ],
};

// 查看学生详情
const viewStudent = (row: ExtendedStudentInfo) => {
  // 直接使用row数据，无需向后端发送请求
  currentStudent.value = {
    ...row,
    // 确保数据格式正确
    id: row.id,
    name: row.name,
    studentNumber: row.studentNumber,
    gender: row.gender,
    phone: row.phone,
    email: row.email,
    college: row.college,
    majorName: row.majorName,
    classValue: row.classValue,
    entranceYear: row.entranceYear,
    studentStatus: row.studentStatus,
    status: row.status,
    creator: row.creator,
    createTime: row.createTime,
    modifier: row.modifier,
    modifyTime: row.modifyTime
  };
  detailVisible.value = true;
};

// 编辑学生
const editStudent = async (row: ExtendedStudentInfo) => {
  try {
    await fetchColleges();
    currentStudent.value = row;
    Object.assign(formData, row);
    formData.collegeId = String(row.collegeId || '');
    formData.majorId = String(row.majorId || '');
    formData.classId = String(row.classId || '');
    formData.phone = row.phone || '';
    formData.email = row.email || '';
    formData.gender = row.gender || 0;
    formData.number = row.studentNumber || '';
    formData.entranceYear = row.entranceYear || '';
    if (formData.collegeId) {
      await fetchMajorsByCollege(formData.collegeId);
    }
    if (formData.majorId) {
      await fetchClassesByMajor(formData.majorId);
    }
    editVisible.value = true;
  } catch (error) {
    console.error('获取学生详情失败:', error);
    MessagePlugin.error('获取学生详情失败');
  }
};

// 删除学生
const deleteStudent = (row: ExtendedStudentInfo) => {
  const dialog = DialogPlugin.confirm({
    header: '删除确认',
    body: `确定要删除学生"${row.name}"吗？`,
    confirmBtn: '确认删除',
    cancelBtn: '取消',
    onConfirm: async ({ e }) => {
      try {
        const result = await deleteStudentApi(row.id);
        
        if (result.code === 200) {
          MessagePlugin.success('删除成功');
          await afterStudentChange();
          return true;
        } else {
          throw new Error(result.message || '删除学生失败');
        }
      } catch (error) {
        console.error('删除学生失败:', error);
        MessagePlugin.error('删除学生失败: ' + (error as Error).message);
        return false;
      }finally{
        dialog.destroy();
      }
    }
  });
};

// 处理添加学生提交
const handleAddSubmit = async (submitContext: any) => {
  if (submitContext.validateResult !== true) {
    return;
  }
  try {
    const studentData = {
      number: formData.number,
      user: {
        realName: formData.name,
        gender: formData.gender,
        phone: formData.phone || '',
        email: formData.email || '',
        username: formData.number
      },
      classId: formData.classId || null,
      majorId: formData.majorId,
      academyId: formData.collegeId,
      entranceYear: formData.entranceYear || new Date().getFullYear().toString(),
      studentStatus: 0
    };
    await addStudentApi(studentData);
    MessagePlugin.success('添加学生成功');
    addVisible.value = false;
    resetFormData();
    await afterStudentChange();
  } catch (error) {
    console.error('添加学生失败:', error);
  }
};

// 处理更新学生提交
const handleSubmit = async (submitContext: any) => {
  if (submitContext.validateResult !== true) {
    return;
  }
  try {
    const studentData = {
      studentId: formData.id,
      number: formData.number,
      user: {
        realName: formData.name,
        gender: formData.gender,
        phone: formData.phone || '',
        email: formData.email || '',
        username: formData.number
      },
      classId: formData.classId || null,
      majorId: formData.majorId,
      academyId: formData.collegeId,
      entranceYear: formData.entranceYear || new Date().getFullYear().toString(),
      studentStatus: formData.studentStatus || 0
    };
    const result = await updateStudentApi(studentData);
    MessagePlugin.success('更新学生成功');
    editVisible.value = false;
    await afterStudentChange();
  } catch (error) {
    console.error('更新学生失败:', error);
  }
};

// 打开新增学生对话框
const handleAddStudent = async () => {
  resetFormData();
  await fetchColleges();
  if (selectedCollegeId.value) {
    formData.collegeId = String(selectedCollegeId.value);
    await fetchMajorsByCollege(formData.collegeId);
  }
  if (selectedMajorId.value) {
    formData.majorId = String(selectedMajorId.value);
    await fetchClassesByMajor(formData.majorId);
  }
  if (selectedClassId.value) {
    formData.classId = String(selectedClassId.value);
  }
  addVisible.value = true;
};

// 导入对话框控制
const importVisible = ref(false);

// 导入配置
const importConfig: ImportConfig = {
  title: '导入学生数据',
  tips: '请按照模板格式填写学生信息，支持批量导入。注意：学院、专业、班级请填写准确的名称，手机号和邮箱为必填项，入学年份为必填项',
  templateFileName: '学生信息导入模板.xlsx',
  templateData: [
    ['学号', '姓名', '性别', '手机号', '邮箱', '学院名称', '专业名称', '班级名称', '入学年份', '学籍状态'],
    ['2021001001', '张三', '男', '13800138000', '<EMAIL>', '计算机科学与技术学院', '计算机科学与技术', '计科2101', '2021', '在读'],
    ['2021001002', '李四', '女', '13800138001', '<EMAIL>', '计算机科学与技术学院', '软件工程', '软工2101', '2021', '在读'],
    ['2021001003', '王五', '男', '13800138002', '<EMAIL>', '电子信息工程学院', '电子信息工程', '电信2101', '2021', '在读']
  ],
  acceptTypes: ['.xlsx', '.xls', '.csv'],
  maxFileSize: 5
};

// 导入回调函数
const importCallbacks: ImportCallbacks = {
  onImport: async (file: File) => {
    // 将File转换为FormData
    const formData = new FormData();
    formData.append('file', file);
    return await importStudents(formData);
  },
  onSuccess: (result: any) => {
    afterStudentChange();
  },
  onError: (error: Error) => {
    console.error('导入失败:', error);
  },
  onComplete: () => {
    // 导入完成后的处理
  }
};

// 打开导入对话框
const openImportDialog = () => {
  importVisible.value = true;
};

// 同步班级人数
const handleSyncStudentCount = async () => {
  try {
    // 构建同步参数，使用当前选中的节点
    const syncParams = {
      academyId: selectedCollegeId.value ? Number(selectedCollegeId.value) : undefined,
      majorId: selectedMajorId.value ? Number(selectedMajorId.value) : undefined,
      classId: selectedClassId.value ? Number(selectedClassId.value) : undefined
    };
    syncLoading.value = true;
    const res = await previewSyncStudentCount(syncParams);
    syncPreviewList.value = res.data || [];
    syncPreviewVisible.value = true;
  } catch (error) {
    MessagePlugin.error('获取同步预览失败');
  } finally {
    syncLoading.value = false;
  }
};

// 确认同步
const confirmSyncStudentCount = async () => {
  try {
    syncLoading.value = true;
    // 构建同步参数
    const syncParams = {
      academyId: selectedCollegeId.value ? Number(selectedCollegeId.value) : undefined,
      majorId: selectedMajorId.value ? Number(selectedMajorId.value) : undefined,
      classId: selectedClassId.value ? Number(selectedClassId.value) : undefined
    };
    const res = await syncStudentCount(syncParams);
    syncPreviewVisible.value = false;
    if (res.code === 200 && res.data && res.data.success) {
      MessagePlugin.success(res.data.message || '同步成功');
      await Promise.all([
        fetchStudentList(),
        buildTreeData()
      ]);
    } else {
      MessagePlugin.error(res.data?.message || '同步失败');
      // 可选：弹窗展示详细失败信息
    }
  } catch (error) {
    MessagePlugin.error('同步失败，请重试');
  } finally {
    syncLoading.value = false;
  }
};

// 导出学生数据
const exportStudentData = async () => {
  try {
    const queryParams: StudentQueryParams = {
      academyId: searchForm.collegeId ? Number(searchForm.collegeId) : undefined,
      majorId: searchForm.majorId ? Number(searchForm.majorId) : undefined,
      classId: searchForm.classId ? Number(searchForm.classId) : undefined,
      studentStatus: searchForm.studentStatus ? Number(searchForm.studentStatus) : undefined,
      gender: searchForm.gender === '男' ? 1 : searchForm.gender === '女' ? 2 : undefined,
      entranceYear: searchForm.entranceYear || undefined,
      status: searchForm.status ? Number(searchForm.status) : undefined,
      studentName: searchForm.name || undefined,
      studentNumber: searchForm.studentId || undefined,
      phone: searchForm.phone || undefined,
      email: searchForm.email || undefined
    };
    
    // 过滤掉空字符串的参数
    Object.keys(queryParams).forEach(key => {
      const typedKey = key as keyof StudentQueryParams;
      if (queryParams[typedKey] === '' || queryParams[typedKey] === null || queryParams[typedKey] === undefined) {
        delete queryParams[typedKey];
      }
    });
    
    const blob = await exportStudents(queryParams);
    
    // 创建下载链接
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `学生信息_${new Date().toISOString().slice(0, 10)}.xlsx`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
    
    MessagePlugin.success('导出成功');
  } catch (error) {
    console.error('导出学生数据失败:', error);
    MessagePlugin.error('导出失败: ' + (error as Error).message);
  }
};

// 构建组织结构树数据
const buildTreeData = async () => {
  treeLoading.value = true;
  try {
    console.log('开始获取学生组织结构树数据...');
    const result = await getStudentTree();
    console.log('学生组织结构树API返回:', result);

    if (result && result.code === 200 && result.data) {
      treeData.value = result.data;
      console.log('设置树形数据:', treeData.value);
      console.log('树数据长度:', treeData.value.length);
    } else {
      console.error('获取组织结构树失败:', result?.message);
      MessagePlugin.warning('获取组织结构树失败，使用默认数据');
      // 使用备用的简单数据结构
      treeData.value = [{
        id: 'all',
        label: '全部学生',
        type: 'school',
        studentCount: 0,
        children: []
      }];
    }
    
    // 同时构建专业选项数据
    buildMajorOptions();
    
  } catch (error) {
    console.error('构建树形数据失败:', error);
    MessagePlugin.warning('加载组织结构失败，使用默认数据');
    // 使用备用的简单数据结构
    treeData.value = [{
      id: 'all',
      label: '全部学生',
      type: 'school',
      studentCount: 0,
      children: []
    }];
  } finally {
    treeLoading.value = false;
    console.log('树数据构建完成，当前数据:', treeData.value);
  }
};

// 构建专业选项数据
const buildMajorOptions = () => {
  const majors: any[] = [];
  
  const extractMajors = (nodes: any[]) => {
    nodes.forEach(node => {
      if (node.type === 'major') {
        majors.push({
          label: node.label,
          value: node.dbId // 使用数据库ID作为值
        });
      }
      if (node.children && node.children.length > 0) {
        extractMajors(node.children);
      }
    });
  };
  
  extractMajors(treeData.value);
  majorOptions.value = majors;
};

// 查找树节点
const findNodeById = (nodes: any[], id: string | number): any => {
  for (const node of nodes) {
    if (node.id === id) {
      return node;
    }
    if (node.children && node.children.length > 0) {
      const found = findNodeById(node.children, id);
      if (found) return found;
    }
  }
  return null;
};

// 查找节点的完整路径
const findNodePath = (nodes: any[], targetId: string | number): any[] => {
  const path: any[] = [];
  
  const findPath = (nodeList: any[], target: string | number, currentPath: any[]): boolean => {
    for (const node of nodeList) {
      const newPath = [...currentPath, node];
      
      if (node.id === target) {
        path.push(...newPath);
        return true;
      }
      
      if (node.children && node.children.length > 0) {
        if (findPath(node.children, target, newPath)) {
          return true;
        }
      }
    }
    return false;
  };
  
  findPath(nodes, targetId, []);
  return path;
};

// 处理树节点点击
const handleTreeClick = (context: any) => {
  const { node } = context;
  const nodeData = node.data;
  
  activeNodeKey.value = nodeData.id;
  
  // 重置选择状态
  selectedCollegeId.value = '';
  selectedMajorId.value = '';
  selectedClassId.value = '';
  selectedCollegeTreeId.value = '';
  selectedMajorTreeId.value = '';
  selectedClassTreeId.value = '';
  selectedNodeInfo.value = '';
  
  // 根据节点类型设置过滤条件
  if (nodeData.type === 'school') {
    // 选择根节点，显示所有学生
    searchForm.collegeId = '';
    searchForm.majorId = '';
    searchForm.classId = '';
    selectedNodeInfo.value = '当前显示：全部学生';
  } else if (nodeData.type === 'college') {
    // 选择学院节点
    selectedCollegeId.value = nodeData.dbId; // 数据库ID用于API查询
    selectedCollegeTreeId.value = nodeData.id; // 树形ID用于标题显示
    searchForm.collegeId = String(nodeData.dbId);
    searchForm.majorId = '';
    searchForm.classId = '';
    selectedNodeInfo.value = `当前显示：${nodeData.label} 的所有学生`;
  } else if (nodeData.type === 'major') {
    // 选择专业节点
    selectedMajorId.value = nodeData.dbId;
    selectedMajorTreeId.value = nodeData.id;
    selectedCollegeId.value = nodeData.collegeDbId;
    searchForm.collegeId = String(nodeData.collegeDbId);
    searchForm.majorId = String(nodeData.dbId);
    searchForm.classId = '';
    selectedNodeInfo.value = `当前显示：${nodeData.label} 专业的学生`;
  } else if (nodeData.type === 'class') {
    // 选择班级节点
    selectedClassId.value = nodeData.dbId;
    selectedClassTreeId.value = nodeData.id;
    selectedMajorId.value = nodeData.majorDbId;
    selectedCollegeId.value = nodeData.collegeDbId;
    searchForm.collegeId = String(nodeData.collegeDbId);
    searchForm.majorId = String(nodeData.majorDbId);
    searchForm.classId = String(nodeData.dbId);
    selectedNodeInfo.value = `当前显示：${nodeData.label} 班级的学生`;
  }
  
  // 重置分页并重新加载数据
  pagination.current = 1;
  fetchStudentList();
};

// 刷新数据
const refreshData = async () => {
  await Promise.all([
    buildTreeData(),
    fetchStudentList(),
    fetchColleges()
  ]);
  MessagePlugin.success('数据已刷新');
};

// 班级选项数据

// 获取名称的工具函数
const getCollegeNameById = (id: string | number) => {
  const college = colleges.value.find(c => c.id == id);
  return college ? college.name : '';
};

const getMajorNameById = (id: string | number) => {
  const major = majorOptions.value.find(m => m.value == id);
  return major ? major.label : '';
};

const getClassNameById = (id: string | number) => {
  const classItem = classOptions.value.find(c => c.value == id);
  return classItem ? classItem.label : '';
};

// 树操作函数
const expandAll = () => {
  if (treeRef.value) {
    treeRef.value.expandAll();
  }
};

const collapseAll = () => {
  if (treeRef.value) {
    treeRef.value.collapseAll();
  }
};

// 切换高级搜索
const toggleAdvancedSearch = () => {
  showAdvancedSearch.value = !showAdvancedSearch.value;
};

// 切换左侧面板显示/隐藏
const toggleLeftPanel = () => {
  leftPanelCollapsed.value = !leftPanelCollapsed.value;
  console.log('左侧面板状态:', leftPanelCollapsed.value ? '已隐藏' : '已显示');
};

// 动态添加相关
const showAddCollegeDialog = ref(false);
const showAddMajorDialog = ref(false);
const showAddClassDialog = ref(false);

// 动态添加表单数据
const addCollegeForm = reactive({
  academyName: ''
});

const addMajorForm = reactive({
  majorName: '',
  majorCode: '',
  academyId: ''
});

const addClassForm = reactive({
  className: '',
  majorId: '',
  entranceYear: ''
});

// 重置动态添加表单数据
const resetAddCollegeForm = () => {
  addCollegeForm.academyName = '';
};

const resetAddMajorForm = () => {
  addMajorForm.majorName = '';
  addMajorForm.majorCode = '';
  addMajorForm.academyId = '';
};

const resetAddClassForm = () => {
  addClassForm.className = '';
  addClassForm.majorId = '';
  addClassForm.entranceYear = '';
};

// 动态添加表单校验规则
const addCollegeRules = {
  academyName: [
    { required: true, message: '请输入学院名称', trigger: 'blur' as const },
    { max: 50, message: '学院名称不能超过50个字符', trigger: 'blur' as const }
  ]
};

const addMajorRules = {
  majorName: [
    { required: true, message: '请输入专业名称', trigger: 'blur' as const },
    { max: 50, message: '专业名称不能超过50个字符', trigger: 'blur' as const }
  ],
  academyId: [
    { required: true, message: '请选择学院', trigger: 'change' as const }
  ]
};

const addClassRules = {
  className: [
    { required: true, message: '请输入班级名称', trigger: 'blur' as const },
    { max: 50, message: '班级名称不能超过50个字符', trigger: 'blur' as const }
  ],
  majorId: [
    { required: true, message: '请选择专业', trigger: 'change' as const }
  ],
  entranceYear: [
    { required: true, message: '请输入入学年份', trigger: 'blur' as const }
  ]
};

// 根据学院ID获取专业列表
const fetchMajorsByCollege = async (collegeId: string) => {
  try {
    const result = await getMajorsByCollegeId(collegeId);
    if (result && result.code === 200 && result.data) {
      majorOptions.value = result.data.map((item: any) => ({
        label: item.majorName || item.name || item.label,
        value: String(item.majorId || item.id || item.value)
      }));
    } else {
      majorOptions.value = [];
    }
  } catch (error) {
    console.error('获取专业列表失败:', error);
    majorOptions.value = [];
  }
};

// 根据专业ID获取班级列表
const fetchClassesByMajor = async (majorId: string) => {
  try {
    const result = await getClassesByMajorId(majorId);
    if (result && result.code === 200 && result.data) {
      classOptions.value = result.data.map((item: any) => ({
        label: item.className || item.name || item.label,
        value: String(item.classId || item.id || item.value)
      }));
    } else {
      classOptions.value = [];
    }
  } catch (error) {
    console.error('获取班级列表失败:', error);
    classOptions.value = [];
  }
};

// 监听学院选择变化（兼容TDesign类型）
const handleCollegeChange = async (value: any) => {
  if (typeof value !== 'string') return;
  formData.majorId = '';
  formData.classId = '';
  majorOptions.value = [];
  classOptions.value = [];
  if (value) {
    await fetchMajorsByCollege(value);
  }
};

// 监听专业选择变化（兼容TDesign类型）
const handleMajorChange = async (value: any) => {
  if (typeof value !== 'string') return;
  formData.classId = '';
  classOptions.value = [];
  if (value) {
    await fetchClassesByMajor(value);
  }
};

// 添加学院后自动选中并刷新专业下拉
const handleAddCollege = async (submitContext: any) => {
  if (submitContext.validateResult !== true) {
    return;
  }
  try {
    const checkResult = await checkAcademyNameExists(addCollegeForm.academyName);
    if (checkResult && checkResult.data) {
      MessagePlugin.warning('学院名称已存在，请使用其他名称');
      return;
    }
    const result = await addAcademy({
      academyName: addCollegeForm.academyName,
      status: 0
    });
    if (result && result.code === 200) {
      MessagePlugin.success('添加学院成功');
      showAddCollegeDialog.value = false;
      resetAddCollegeForm();
      await fetchColleges();
      formData.collegeId = result.data?.id || '';
      await fetchMajorsByCollege(formData.collegeId);
      await buildTreeData();
    } else {
      throw new Error(result?.message || '添加学院失败');
    }
  } catch (error) {
    console.error('添加学院失败:', error);
    MessagePlugin.error('添加学院失败: ' + (error as Error).message);
  }
};

// 添加专业后自动选中并刷新班级下拉
const handleAddMajor = async (submitContext: any) => {
  if (submitContext.validateResult !== true) {
    return;
  }
  try {
    const checkResult = await checkMajorNameExists(addMajorForm.majorName, addMajorForm.academyId);
    if (checkResult && checkResult.data) {
      MessagePlugin.warning('该学院下专业名称已存在，请使用其他名称');
      return;
    }
    const result = await addMajor({
      majorName: addMajorForm.majorName,
      majorCode: addMajorForm.majorCode || '',
      academyId: Number(addMajorForm.academyId),
      status: 0
    });
    if (result && result.code === 200) {
      MessagePlugin.success('添加专业成功');
      showAddMajorDialog.value = false;
      resetAddMajorForm();
      await fetchMajorsByCollege(formData.collegeId);
      formData.majorId = result.data?.id || '';
      await fetchClassesByMajor(formData.majorId);
      await buildTreeData();
    } else {
      throw new Error(result?.message || '添加专业失败');
    }
  } catch (error) {
    console.error('添加专业失败:', error);
    MessagePlugin.error('添加专业失败: ' + (error as Error).message);
  }
};

// 添加班级后自动选中
const handleAddClass = async (submitContext: any) => {
  if (submitContext.validateResult !== true) {
    return;
  }
  try {
    const checkResult = await checkClassNameExists(addClassForm.className, addClassForm.majorId);
    if (checkResult && checkResult.data) {
      MessagePlugin.warning('该专业下班级名称已存在，请使用其他名称');
      return;
    }
    const result = await addClass({
      majorId: Number(addClassForm.majorId),
      className: addClassForm.className,
      entranceYear: addClassForm.entranceYear,
      studentNumber: 0,
      classStatus: 0,
      status: 0
    });
    if (result && result.code === 200) {
      MessagePlugin.success('添加班级成功');
      showAddClassDialog.value = false;
      resetAddClassForm();
      await fetchClassesByMajor(formData.majorId);
      formData.classId = result.data?.id || '';
      await buildTreeData();
    } else {
      throw new Error(result?.message || '添加班级失败');
    }
  } catch (error) {
    console.error('添加班级失败:', error);
    MessagePlugin.error('添加班级失败: ' + (error as Error).message);
  }
};

// 新增/编辑/删除学生后自动刷新树和表格，并同步班级学生数
const afterStudentChange = async () => {
  await Promise.all([
    fetchStudentList(),
    buildTreeData()
  ]);
};

// 详情弹窗性别、学籍状态、记录状态映射
const getGenderText = (val: any) => genderMap[val] || val;
const getStudentStatusText = (val: any) => studentStatusMap[val] || val;
const getRecordStatusText = (val: any) => recordStatusMap[val] || val;
defineExpose({ getGenderText, getStudentStatusText, getRecordStatusText });

// 列设置相关
const showColumnSetting = ref(false);
const defaultColumnKeys = [
  'serial-number','id','name','studentNumber','gender','phone','email','college','majorName','classValue','entranceYear','studentStatus','creator','createTime','modifier','modifyTime','status','operation'
];
const columnOptions = [
  { label: '序号', value: 'serial-number' },
  { label: '学生ID', value: 'id' },
  { label: '学生姓名', value: 'name' },
  { label: '学号', value: 'studentNumber' },
  { label: '性别', value: 'gender' },
  { label: '手机号', value: 'phone' },
  { label: '邮箱', value: 'email' },
  { label: '学院', value: 'college' },
  { label: '专业', value: 'majorName' },
  { label: '班级', value: 'classValue' },
  { label: '入学年份', value: 'entranceYear' },
  { label: '学籍状态', value: 'studentStatus' },
  { label: '创建人', value: 'creator' },
  { label: '创建时间', value: 'createTime' },
  { label: '修改人', value: 'modifier' },
  { label: '修改时间', value: 'modifyTime' },
  { label: '记录状态', value: 'status' },
  { label: '操作', value: 'operation' },
];
const selectedColumnKeys = ref<string[]>(JSON.parse(localStorage.getItem('student_table_columns') || 'null') || defaultColumnKeys);
const selectAllColumns = computed({
  get: () => selectedColumnKeys.value.length === columnOptions.length,
  set: (val: boolean) => {
    if (val) {
      selectedColumnKeys.value = columnOptions.map(opt => opt.value);
    } else {
      selectedColumnKeys.value = [];
    }
  }
});
const handleSelectAllColumns = (checked: boolean) => {
  if (checked) {
    selectedColumnKeys.value = columnOptions.map(opt => opt.value);
  } else {
    selectedColumnKeys.value = [];
  }
};
const resetColumnSettings = () => {
  selectedColumnKeys.value = [...defaultColumnKeys];
  localStorage.setItem('student_table_columns', JSON.stringify(selectedColumnKeys.value));
};
watch(selectedColumnKeys, (val) => {
  localStorage.setItem('student_table_columns', JSON.stringify(val));
});
const visibleColumns = computed(() => {
  return columns.filter(col => selectedColumnKeys.value.includes(col.colKey as string));
});

// 初始化
onMounted(async () => {
  // 获取基础数据
  await Promise.all([
    buildTreeData(),
    fetchColleges()
  ]);
  // 初始化入学年份选项
  entranceYearOptions.value = SemesterUtils.getAcademicYearList().map(year => ({
    label: String(year),
    value: String(year)
  }));
  fetchStudentList();
});

// 新增：同步班级人数预览弹窗相关
const syncPreviewVisible = ref(false);
const syncPreviewList = ref<any[]>([]);
const syncLoading = ref(false);
const syncPreviewColumns = [
  { colKey: 'classId', title: '班级ID', width: 100 },
  { colKey: 'className', title: '班级名称', width: 200 },
  { colKey: 'oldStudentNumber', title: '班级人数(原)', width: 120 },
  { colKey: 'actualStudentNumber', title: '实际统计人数', width: 140 }
];
</script>

<style scoped lang="less">
.student-tree-container {
  padding: 20px;
  background-color: var(--td-bg-color-container);
  border-radius: var(--td-radius-medium);

  // 标题容器样式
  .card-title-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    
    span {
      font-size: 16px;
      font-weight: 600;
      color: var(--td-text-color-primary);
    }
    
    .toggle-button {
      color: var(--td-brand-color);
      transition: all 0.2s;
      font-weight: 500;
      border-radius: 6px;
      padding: 4px 8px;
      
      &:hover {
        background-color: var(--td-brand-color-light);
        transform: scale(1.05);
      }
      
      &:active {
        transform: scale(0.95);
      }
    }
  }

  .student-management-layout {
    display: flex;
    gap: 20px;
    min-height: 700px;
    
    .left-panel {
      width: 300px;
      flex-shrink: 0;
      transition: all 0.3s ease;
      
      &.collapsed {
        width: 0;
        overflow: hidden;
        margin-right: 0;
      }
      
      .org-tree-card {
        height: 100%;
        
        :deep(.t-card__body) {
          padding: 16px;
          height: calc(100% - 60px);
        }
      }
      
      .tree-node-label {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
        
        .student-count {
          font-size: 12px;
          color: var(--td-text-color-placeholder);
          margin-left: 8px;
        }
      }
    }
    
    .right-panel {
      flex: 1;
      min-width: 0; // 防止flex子项溢出
      transition: all 0.3s ease;
      
      // 当左侧面板隐藏时，右侧面板占据更多空间
      .left-panel.collapsed + & {
        margin-left: 0;
      }
      
      :deep(.t-card__header){
        display: flex;
        flex-direction: column;
        align-items: normal;
        justify-content: normal;
      }
      :deep(.t-card__header-wrapper){
        display: block;
      }
      :deep(.t-card__actions){
        display: flex;
        flex-direction: row;
        justify-content: flex-end;
      }
    }
  }

  .current-selection {
    :deep(.t-alert) {
      border-radius: 6px;
    }
  }

  .operation-container {
    display: flex;
    justify-content: center;
    gap: 8px;

    :deep(.t-button) {
      min-width: unset;
      transition: all 0.2s ease;

      &:hover {
        transform: translateY(-2px);
      }
    }

    @media (max-width: 768px) {
      gap: 4px;
    }
  }
  
  // 响应式布局
  @media (max-width: 1200px) {
    .student-management-layout {
      .left-panel {
        width: 280px;
      }
    }
  }
  
  @media (max-width: 768px) {
    .card-title-container {
      flex-direction: column;
      gap: 8px;
      align-items: flex-start;
      
      .toggle-button {
        align-self: flex-end;
      }
    }
    
    .student-management-layout {
      flex-direction: column;
      
      .left-panel {
        width: 100%;
        
        &.collapsed {
          height: 0;
          overflow: hidden;
        }
        
        .org-tree-card {
          height: 300px;
        }
      }
    }
  }
}

.search-container {
  margin-bottom: 20px;
  padding: 16px;
  background-color: var(--td-bg-color-container-hover);
  border-radius: var(--td-radius-medium);
}

.hierarchy-path {
  .path-display {
    display: flex;
    align-items: center;
    font-weight: 500;
    
    .path-text {
      color: var(--td-text-color-primary);
      font-size: 14px;
    }
  }
  
  :deep(.t-alert) {
    border-radius: 6px;
    border-left: 4px solid var(--td-brand-color);
  }
}

// 必填字段样式
:deep(.t-form__label) {
  &.t-is-required::before {
    content: '*';
    color: var(--td-error-color);
    margin-right: 4px;
  }
}

.fade-table {
  transition: opacity 0.3s;
}

// 详情弹窗样式
.detail-section {
  .detail-section-title {
    display: flex;
    align-items: center;
    font-size: 16px;
    font-weight: 600;
    color: var(--td-text-color-primary);
    margin-bottom: 16px;
    padding-bottom: 8px;
    border-bottom: 2px solid var(--td-brand-color-light);
  }
}

// 选中名称显示样式
.selected-name {
  display: inline-block;
  padding: 4px 8px;
  background-color: var(--td-brand-color-light);
  color: var(--td-brand-color);
  border-radius: 4px;
  font-size: 12px;
  margin-left: 8px;
  transition: all 0.2s;
  
  &:hover {
    background-color: var(--td-brand-color);
    color: white;
  }
}

// 搜索切换按钮样式
.search-toggle {
  .t-button {
    transition: all 0.2s;
    
    &:hover {
      transform: translateY(-1px);
    }
  }
}

// 树操作按钮样式
:deep(.t-card__actions) {
  .t-button {
    transition: all 0.2s;
    
    &:hover {
      background-color: var(--td-brand-color-light);
      color: var(--td-brand-color);
    }
  }
}

// 表格动画效果
.fade-table {
  :deep(.t-table__body) {
    tr {
      transition: all 0.2s;
      
      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }
    }
  }
}

// 空状态样式
.empty-tree {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  color: var(--td-text-color-placeholder);
}

// 响应式优化
@media (max-width: 768px) {
  .selected-name {
    display: block;
    margin-left: 0;
    margin-top: 4px;
  }
  
  .detail-section {
    .detail-section-title {
      font-size: 14px;
    }
  }
  
  .left-panel {
    &.collapsed {
      width: 0;
    }
  }
}

// 确保下拉框宽度正确显示
:deep(.t-form__controls) {
  .t-select {
    width: 100% !important;
  }
  
  .t-space {
    width: 100%;
    
    .t-select {
      flex: 1;
      min-width: 0;
    }
  }
}

// 弹窗中的表单控件样式
:deep(.t-dialog__body) {
  .t-form__controls {
    .t-select {
      width: 100% !important;
    }
    
    .t-space {
      width: 100%;
      
      .t-select {
        flex: 1;
        min-width: 0;
      }
    }
  }
}
</style> 
<template>
    <div class="student-detail-container">
        <!-- 顶部导航区域 -->
        <div class="page-header">
            <t-button theme="default" @click="goBack">
                <template #icon><t-icon name="arrow-left" /></template>
                返回学生列表
            </t-button>
            <h2>{{ studentInfo.name }} - 成绩详情</h2>
        </div>

        <!-- 学生基本信息卡片 -->
        <t-card class="student-info-card">
            <div class="student-info-header">
                <div class="student-avatar">
<!--                    <img :src="studentInfo.avatar || '/image/avatar/default-avatar.jpg'" alt="学生头像">-->
                  <img :src="studentInfo.avatar">
                </div>
                <div class="student-basic-info">
                    <h3>{{ studentInfo.name }}</h3>
                    <div class="info-grid">
                        <div class="info-item">
                            <label>学号：</label>
                            <span>{{ studentInfo.studentId }}</span>
                        </div>
                        <div class="info-item">
                            <label>班级：</label>
                            <span>{{ studentInfo.className }}</span>
                        </div>
                        <div class="info-item">
                            <label>年级：</label>
                            <span>{{ studentInfo.grade }}</span>
                        </div>
                        <div class="info-item">
                            <label>性别：</label>
                            <span>{{ studentInfo.gender }}</span>
                        </div>
                    </div>
                </div>
                <div class="student-total-score">
                    <div class="score-circle" :class="getScoreClass(studentInfo.totalScore)">
                        {{ studentInfo.totalScore }}
                    </div>
                    <div class="score-label">综合成绩</div>
                </div>
            </div>
        </t-card>

        <!-- 成绩图表分析 -->
        <div class="charts-section">
            <!-- 课程目标达成度雷达图 -->
            <t-card title="课程目标达成度" class="chart-card">
                <div ref="radarChart" class="chart"></div>
            </t-card>

            <!-- 作业成绩趋势 -->
            <t-card title="作业成绩趋势" class="chart-card">
                <div ref="homeworkChart" class="chart"></div>
            </t-card>

            <!-- 考试成绩趋势 -->
            <t-card title="考试成绩趋势" class="chart-card">
                <div ref="examChart" class="chart"></div>
            </t-card>
        </div>

        <!-- 详细成绩记录 -->
        <t-card title="详细成绩记录" class="score-records-card">
            <t-tabs>
                <t-tab-panel value="assignments" label="作业记录">
                    <t-table :data="assignmentRecords" :columns="assignmentColumns" :row-key="rowKey" stripe hover>
                        <template #score="{ row }">
                            <span :class="getScoreClass(row.score)">{{ row.score }}</span>
                        </template>
                    </t-table>
                </t-tab-panel>
                <t-tab-panel value="exams" label="考试记录">
                    <t-table :data="examRecords" :columns="examColumns" :row-key="rowKey" stripe hover>
                        <template #score="{ row }">
                            <span :class="getScoreClass(row.score)">{{ row.score }}</span>
                        </template>
                    </t-table>
                </t-tab-panel>
            </t-tabs>
        </t-card>
    </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import * as echarts from 'echarts'
import {
    Button as TButton,
    Card as TCard,
    Table as TTable,
    Tabs as TTabs,
    TabPanel as TTabPanel,
    Icon as TIcon
} from 'tdesign-vue-next'

const route = useRoute()
const router = useRouter()
const studentIdd = ref(route.params.id as string)
//暂时处理方法，后续为网络请求获取
  const studentId = ref(studentIdd.value.match(/\d+/g) as number);



const courseId = ref(route.query.courseId as string)

// 图表引用
const radarChart = ref<HTMLElement | null>(null)
const homeworkChart = ref<HTMLElement | null>(null)
const examChart = ref<HTMLElement | null>(null)

// 学生信息
const studentInfo = ref({
    id: '',
    name: '加载中...',
    studentId: '',
    avatar: '/image/avatar/student.jpg',
    className: '',
    grade: '',
    gender: '',
    totalScore: 0
})

// 作业记录列定义
const assignmentColumns = [
    { colKey: 'title', title: '作业名称', width: 300 },
    { colKey: 'date', title: '提交日期', width: 150 },
    { colKey: 'score', title: '得分', width: 100 },
    { colKey: 'totalScore', title: '总分', width: 100 },
    { colKey: 'comment', title: '评语', width: 300 }
]

// 考试记录列定义
const examColumns = [
    { colKey: 'title', title: '考试名称', width: 300 },
    { colKey: 'date', title: '考试日期', width: 150 },
    { colKey: 'score', title: '得分', width: 100 },
    { colKey: 'totalScore', title: '总分', width: 100 },
    { colKey: 'rank', title: '班级排名', width: 100 }
]

// 行唯一标识
const rowKey = 'id'

// 作业记录数据
const assignmentRecords = ref([])

// 考试记录数据
const examRecords = ref([])

// 返回上一页
const goBack = () => {
    router.push(`/teachers/coursedetail/${courseId.value}`)
}

// 获取分数CSS类
const getScoreClass = (score: number) => {
    if (score >= 80) return 'score-excellent'
    if (score >= 60) return 'score-pass'
    return 'score-fail'
}

// 初始化雷达图
const initRadarChart = () => {
    if (!radarChart.value) return

    const chart = echarts.init(radarChart.value)

    // 模拟课程目标达成度数据
    const goals = [
        { name: '目标1：掌握基本理论', max: 100, value: 85 },
        { name: '目标2：运用解决问题', max: 100, value: 78 },
        { name: '目标3：数据分析能力', max: 100, value: 92 },
        { name: '目标4：逻辑推理能力', max: 100, value: 80 },
        { name: '目标5：编程实现能力', max: 100, value: 75 }
    ]

    const option = {
        tooltip: {
            trigger: 'item'
        },
        radar: {
            indicator: goals.map(goal => ({ name: goal.name, max: goal.max })),
            radius: '65%',
            center: ['50%', '50%'],
            splitNumber: 5,
            name: {
                textStyle: {
                    color: '#333',
                    fontSize: 12
                }
            },
            splitArea: {
                areaStyle: {
                    color: ['rgba(255, 255, 255, 0.5)', 'rgba(240, 240, 240, 0.5)']
                }
            }
        },
        series: [
            {
                name: '课程目标达成度',
                type: 'radar',
                data: [
                    {
                        value: goals.map(goal => goal.value),
                        name: '学生达成度',
                        areaStyle: {
                            color: 'rgba(0, 112, 251, 0.4)'
                        },
                        lineStyle: {
                            color: '#0070fb'
                        },
                        itemStyle: {
                            color: '#0070fb'
                        }
                    },
                    {
                        value: [70, 70, 70, 70, 70], // 期望达成度
                        name: '期望达成度',
                        lineStyle: {
                            color: '#ff9300',
                            type: 'dashed'
                        },
                        itemStyle: {
                            color: '#ff9300'
                        }
                    }
                ]
            }
        ]
    }

    chart.setOption(option)
    window.addEventListener('resize', () => chart.resize())
}

// 初始化作业成绩趋势图
const initHomeworkChart = () => {
    if (!homeworkChart.value) return

    const chart = echarts.init(homeworkChart.value)

    // 模拟作业成绩数据
    const assignments = [
        '作业1', '作业2', '作业3', '作业4', '作业5', '作业6', '作业7', '作业8'
    ]

    const scores = [85, 92, 78, 90, 88, 96, 82, 94]

    const option = {
        tooltip: {
            trigger: 'axis',
            formatter: '{b}: {c}分'
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            top: '8%',
            containLabel: true
        },
        xAxis: {
            type: 'category',
            data: assignments,
            axisLabel: {
                interval: 0,
                rotate: 30
            }
        },
        yAxis: {
            type: 'value',
            min: Math.max(0, Math.min(...scores) - 10),
            max: 100
        },
        series: [
            {
                type: 'line',
                data: scores,
                lineStyle: {
                    color: '#0070fb',
                    width: 3
                },
                itemStyle: {
                    color: '#0070fb'
                },
                symbol: 'circle',
                symbolSize: 8,
                markLine: {
                    data: [
                        {
                            type: 'average',
                            name: '平均分',
                            lineStyle: {
                                color: '#ff9300',
                                type: 'dashed'
                            }
                        }
                    ]
                }
            }
        ]
    }

    chart.setOption(option)
    window.addEventListener('resize', () => chart.resize())
}

// 初始化考试成绩趋势图
const initExamChart = () => {
    if (!examChart.value) return

    const chart = echarts.init(examChart.value)

    // 模拟考试成绩数据
    const exams = ['第一次月考', '期中考试', '第二次月考', '期末考试']
    const scores = [76, 82, 85, 90]
    const classAvg = [72, 75, 78, 80]

    const option = {
        tooltip: {
            trigger: 'axis'
        },
        legend: {
            data: ['学生成绩', '班级平均']
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            top: '12%',
            containLabel: true
        },
        xAxis: {
            type: 'category',
            data: exams
        },
        yAxis: {
            type: 'value',
            min: Math.max(0, Math.min(...scores, ...classAvg) - 10),
            max: 100
        },
        series: [
            {
                name: '学生成绩',
                type: 'line',
                data: scores,
                lineStyle: {
                    color: '#0070fb',
                    width: 3
                },
                itemStyle: {
                    color: '#0070fb'
                },
                symbol: 'circle',
                symbolSize: 8,
            },
            {
                name: '班级平均',
                type: 'line',
                data: classAvg,
                lineStyle: {
                    color: '#ff9300',
                    type: 'dashed',
                    width: 2
                },
                itemStyle: {
                    color: '#ff9300'
                },
                symbol: 'circle',
                symbolSize: 6,
            }
        ]
    }

    chart.setOption(option)
    window.addEventListener('resize', () => chart.resize())
}

// 获取学生信息
const fetchStudentInfo = async () => {

    // 模拟API调用
    setTimeout(() => {

        studentInfo.value = {
            id: studentId.value,
            name: `学生${studentId.value}`,
            studentId: `202220122222`,
            // avatar: `/image/avatar/student-${(parseInt(studentId.value) % 5) + 1}.jpg`,
            avatar: `/image/avatar/student.jpg`,//此处图片后续放图床
            className: '计算机科学与技术2022-1班',
            grade: '2022级',
            gender: studentId.value % 2 === 0 ? '女' : '男',
            totalScore: 75 + Math.floor(Math.random() * 20)
        }

        // 模拟获取作业记录
        assignmentRecords.value = [
            { id: 1, title: '第1章作业：基础概念与理论', date: '2024-03-05', score: 85, totalScore: 100, comment: '完成得不错，基础概念掌握扎实。' },
            { id: 2, title: '第2章作业：数学模型构建', date: '2024-03-12', score: 92, totalScore: 100, comment: '很好，模型构建思路清晰，推导过程完整。' },
            { id: 3, title: '第3章作业：算法实现与分析', date: '2024-03-19', score: 78, totalScore: 100, comment: '算法分析部分不够深入，时间复杂度分析有误。' },
            { id: 4, title: '第4章作业：数据结构应用', date: '2024-03-26', score: 90, totalScore: 100, comment: '数据结构选择合理，代码实现规范。' },
            { id: 5, title: '第5章作业：高级特性与优化', date: '2024-04-02', score: 88, totalScore: 100, comment: '优化方案有创新，但可以考虑更多边界条件。' },
            { id: 6, title: '第6章作业：综合案例分析', date: '2024-04-09', score: 96, totalScore: 100, comment: '案例分析全面，解决方案优秀。' },
            { id: 7, title: '第7章作业：前沿技术探索', date: '2024-04-16', score: 82, totalScore: 100, comment: '对前沿技术理解有待加深，但探索精神值得肯定。' },
            { id: 8, title: '第8章作业：课程综合复习', date: '2024-04-23', score: 94, totalScore: 100, comment: '复习全面，知识点掌握牢固。' }
        ]

        // 模拟获取考试记录
        examRecords.value = [
            { id: 1, title: '第一次月考', date: '2024-03-15', score: 76, totalScore: 100, rank: '25/68' },
            { id: 2, title: '期中考试', date: '2024-04-10', score: 82, totalScore: 100, rank: '18/68' },
            { id: 3, title: '第二次月考', date: '2024-05-15', score: 85, totalScore: 100, rank: '12/68' },
            { id: 4, title: '期末考试', date: '2024-06-20', score: 90, totalScore: 100, rank: '5/68' }
        ]

        // 初始化图表
        initRadarChart()
        initHomeworkChart()
        initExamChart()
      console.log(studentInfo.value )
      console.log(studentId.value )

    }, 500)

}

onMounted(() => {
    fetchStudentInfo()
})
</script>

<style scoped lang="less">
.student-detail-container {
    padding: 20px;
}

.page-header {
    display: flex;
    align-items: center;
    margin-bottom: 20px;

    h2 {
        margin: 0 0 0 20px;
    }
}

.student-info-card {
    margin-bottom: 20px;

    .student-info-header {
        display: flex;
        align-items: center;
    }

    .student-avatar {
        width: 100px;
        height: 100px;
        border-radius: 50%;
        overflow: hidden;
        margin-right: 24px;
        border: 2px solid #f0f0f0;

        img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
    }

    .student-basic-info {
        flex: 1;

        h3 {
            margin: 0 0 16px 0;
            font-size: 20px;
        }

        .info-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
        }

        .info-item {
            display: flex;
            align-items: center;

            label {
                color: #666;
                width: 60px;
            }

            span {
                color: #333;
                font-weight: 500;
            }
        }
    }

    .student-total-score {
        text-align: center;
        margin-left: 24px;

        .score-circle {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 28px;
            font-weight: bold;
            margin: 0 auto 8px;
        }

        .score-label {
            font-size: 14px;
            color: #666;
        }
    }
}

.charts-section {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
    margin-bottom: 20px;

    .chart-card {
        .chart {
            height: 300px;
        }
    }
}

.score-records-card {
    margin-bottom: 20px;
}

/* 分数样式 */
.score-excellent {
    color: #00a870;
    font-weight: bold;
    background-color: #f0fff9;
    padding: 4px 8px;
    border-radius: 4px;
}

.score-pass {
    color: #0052d9;
    background-color: #f0f8ff;
    padding: 4px 8px;
    border-radius: 4px;
}

.score-fail {
    color: #e34d59;
    font-weight: bold;
    background-color: #fff0f0;
    padding: 4px 8px;
    border-radius: 4px;
}

.student-total-score {
    .score-excellent {
        background-color: #f0fff9;
        border: 2px solid #00a870;
        color: #00a870;
    }

    .score-pass {
        background-color: #f0f8ff;
        border: 2px solid #0052d9;
        color: #0052d9;
    }

    .score-fail {
        background-color: #fff0f0;
        border: 2px solid #e34d59;
        color: #e34d59;
    }
}

// 响应式布局
@media screen and (max-width: 1200px) {
    .charts-section {
        grid-template-columns: repeat(2, 1fr);
    }

    .student-info-header {
        flex-wrap: wrap;
    }

    .student-basic-info {
        flex: 0 0 calc(100% - 124px);
    }

    .student-total-score {
        margin-top: 16px;
        margin-left: 124px;
    }
}

@media screen and (max-width: 768px) {
    .charts-section {
        grid-template-columns: 1fr;
    }

    .student-info-header {
        flex-direction: column;
        align-items: flex-start;
    }

    .student-avatar {
        margin-bottom: 16px;
    }

    .student-basic-info {
        width: 100%;
        margin-bottom: 16px;
    }

    .student-total-score {
        margin-left: 0;
        align-self: center;
    }
}
</style>

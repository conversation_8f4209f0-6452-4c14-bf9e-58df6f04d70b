<template>
  <div class="homework-dashboard">
    <!-- 顶部操作栏 -->
    <div class="action-bar">
      <t-button theme="primary" @click="createNewExam">
        <template #icon>
          <t-icon name="add" />
        </template>
        新建试卷
      </t-button>
      <t-button variant="outline" @click="openQuestionBank">试卷库</t-button>
      <t-button variant="outline" @click="importExam">
        <template #icon>
          <t-icon name="upload" />
        </template>
        导入试卷
      </t-button>
    </div>

    <!-- 筛选区域 -->
    <div class="filter-section">
      <div class="status-filter">
        <t-radio-group v-model="status" variant="primary-filled">
          <t-radio-button value="all">全部</t-radio-button>
          <t-radio-button value="not-started">未开始</t-radio-button>
          <t-radio-button value="in-progress">进行中</t-radio-button>
          <t-radio-button value="finished">已结束</t-radio-button>
        </t-radio-group>
      </div>

      <t-input v-model="searchKeyword" placeholder="搜索" class="search-input">
        <template #prefix-icon>
          <t-icon name="search" />
        </template>
      </t-input>
    </div>

    <!-- 试卷列表 -->
    <div class="homework-list">
      <div v-for="exam in displayExamList" :key="exam.id" class="homework-item">
        <div class="homework-info">
          <h3>{{ exam.title }}</h3>
          <div class="meta-info">
            <span class="class-info">{{ exam.classes }}</span>
            <span class="time-info">
              <t-icon name="time" />
              考试时间: {{ exam.startTimeDisplay }} 至 {{ exam.endTimeDisplay }}
            </span>
          </div>
        </div>

        <div class="homework-stats">
          <div class="stat">
            <span class="number">{{ exam.submissionCount }}</span>
            <span class="label">待批</span>
          </div>
          <div class="stat">
            <span class="number">{{ exam.viewCount }}</span>
            <span class="label">已交</span>
          </div>
          <div class="stat">
            <span class="number">{{ exam.commentCount }}</span>
            <span class="label">未交</span>
          </div>
        </div>

        <div class="actions">
          <t-button theme="primary" variant="text" @click="goToGrading(exam)">批阅</t-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import {
  Button as TButton,
  Select as TSelect,
  RadioGroup as TRadioGroup,
  RadioButton as TRadioButton,
  Input as TInput,
  Icon as TIcon,
  MessagePlugin
} from 'tdesign-vue-next'
import { useRouter, useRoute } from 'vue-router'
import { TeacherExam, getExamInfo } from "@/api/base/teacher/teacherHome";

const router = useRouter()
const route = useRoute()
const selectedClass = ref('')
const status = ref('all')
const searchKeyword = ref('')

const classOptions = [
  { label: '全部班级', value: 'all' },
  { label: 'R8软工1班241', value: '241' },
  { label: 'R8软工1班242', value: '242' },
  { label: 'R8软工1班243', value: '243' },
  { label: 'R8软工1班244', value: '244' },
]

const examList = ref<TeacherExam[]>([])
const fetchExamList = async () => {
  try {
    const course_id = route.params.courseId;
    // 从URL查询参数获取班级ID，如果没有则默认为'all'
    const class_id = route.query.classId || 'all';
    
    // 将班级ID传递给API
    const exams = await getExamInfo(Number(course_id), class_id as string);
    examList.value = exams;
    
    // 根据路由参数同步UI选择状态
    selectedClass.value = class_id as string;
  } catch (error) {
    console.error('Error fetching exam list:', error);
    MessagePlugin.warning('获取试卷列表失败');
  }
}
// 获取试卷列表
onMounted(() => {
  fetchExamList();
})


const filteredExamList = computed(() => {
  return examList.value.filter(item => {
    // 班级过滤
    if (selectedClass.value !== 'all' && !item.classId.includes(selectedClass.value)) {
      return false
    }

    // 状态过滤
    if (status.value !== 'all' && item.status !== status.value) {
      return false
    }

    // 关键词搜索
    if (searchKeyword.value && !item.title.toLowerCase().includes(searchKeyword.value.toLowerCase())) {
      return false
    }

    return true
  })
})

// 日期格式化辅助函数
const formatDate = (dateStr: string): string => {
  const date = new Date(dateStr)
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  return `${month}-${day} ${hours}:${minutes}`
}

// 更新列表中显示的日期格式
const displayExamList = computed(() => {
  return filteredExamList.value.map(item => ({
    ...item,
    startTimeDisplay: formatDate(item.startTime),
    endTimeDisplay: formatDate(item.endTime)
  }))
})

const createNewExam = () => {
  // 使用新的路由结构，保持导航一致性
  const courseId = route.params.courseId;
  router.push(`/teachers/course/${courseId}/newtask?type=exam`);
}

const importExam = () => {
  // 使用新的路由结构，保持导航一致性
  const courseId = route.params.courseId;
  router.push(`/teachers/course/${courseId}/import-task?type=exam`);
}

// 打开题库
const openQuestionBank = () => {
  // 使用新的路由结构，保持导航一致性
  const courseId = route.params.courseId;
  router.push(`/teachers/course/${courseId}/question?mode=exam`);
}

// 自动判断试卷状态
const updateExamStatus = () => {
  const now = new Date().getTime()

  examList.value.forEach(item => {
    const startTime = new Date(item.startTime).getTime()
    const endTime = new Date(item.endTime).getTime()

    if (now < startTime) {
      item.status = 'not-started'
    } else if (now >= startTime && now <= endTime) {
      item.status = 'in-progress'
    } else {
      item.status = 'finished'
    }
  })
}

// 页面加载时更新状态
onMounted(() => {
  updateExamStatus()
  // 可选：定期更新状态
  setInterval(updateExamStatus, 60000) // 每分钟更新一次
})

// 添加跳转函数
const goToGrading = (exam: TeacherExam) => {
  // 使用新的路由结构，保持导航一致性
  const courseId = route.params.courseId;
  router.push(`/teachers/course/${courseId}/grading-list/${exam.id}?type=exam&examMode=true`);
}
</script>

<style scoped>
.search-icon {
  width: 16px;
  height: 16px;
}

.homework-dashboard {
  padding: 20px;
  background: #f5f5f5;
}

.action-bar {
  display: flex;
  gap: 12px;
  margin-bottom: 20px;
}

.filter-section {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  flex-wrap: wrap;
  gap: 16px;
}

.status-filter {
  flex-grow: 1;
}

:deep(.t-radio-group) {
  display: flex;
}

:deep(.t-radio-button) {
  flex: 1;
  max-width: 120px;
  justify-content: center;
}

.search-input {
  width: 260px;
}

.homework-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
}

.homework-item {
  display: flex;
  flex-direction: column;
  padding: 16px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.homework-info {
  flex-grow: 1;
}

.homework-info h3 {
  margin: 0 0 8px;
  font-size: 18px;
}

.meta-info {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin-bottom: 12px;
  font-size: 14px;
  color: #666;
}

.time-info {
  display: flex;
  align-items: center;
  gap: 4px;
}

.time-info .t-icon {
  color: #999;
}

.class-info {
  display: inline-block;
  padding: 2px 8px;
  border-radius: 4px;
  background: rgba(0, 82, 217, 0.1);
  color: #0052d9;
  font-weight: 500;
}

.homework-stats {
  display: flex;
  margin-top: auto;
  padding-top: 16px;
  border-top: 1px solid #eee;
}

.stat {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.stat .number {
  font-size: 20px;
  font-weight: 500;
  color: #0052d9;
}

.stat .label {
  font-size: 12px;
  color: #666;
}

.actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 16px;
}
</style>

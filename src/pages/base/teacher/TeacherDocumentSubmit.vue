<template>
  <div class="document-submit-container">
    <t-card title="文件提交与审核" bordered>
      <template #actions>
        <t-button theme="primary" @click="submitDocument">提交文件</t-button>
      </template>
      
      <t-space direction="vertical" size="large" style="width: 100%">
        <t-alert
          theme="info"
          message="文件提交说明"
          description="请上传需要审核的文件（如教学计划、课程大纲等）。文件将经过院长审核和学院管理员审核后发布。"
        >
          <template #icon>
            <t-icon name="info-circle" />
          </template>
        </t-alert>
        
        <t-form ref="form" :data="formData" :rules="formRules" label-align="top" @submit="onSubmit">
          <t-row :gutter="[16, 16]">
            <t-col :span="4">
          <t-form-item label="文件标题" name="title">
            <t-input v-model="formData.title" placeholder="请输入文件标题" />
          </t-form-item>
            </t-col>
            <t-col :span="4">
          <t-form-item label="文件类型" name="type">
            <t-select v-model="formData.type" clearable placeholder="请选择文件类型">
              <t-option v-for="item in documentTypes" :key="item.value" :label="item.label" :value="item.value" />
            </t-select>
          </t-form-item>
            </t-col>
            <t-col :span="4">
          <t-form-item label="适用课程" name="courseId">
                <t-select 
                  v-model="formData.courseId" 
                  clearable 
                  placeholder="请选择适用课程" 
                  :disabled="!!inCourseContext"
                >
              <t-option v-for="course in courseList" :key="course.id" :label="course.name" :value="course.id" />
            </t-select>
          </t-form-item>
            </t-col>
          </t-row>
          
          <t-form-item label="文件描述" name="description">
            <t-textarea
              v-model="formData.description"
              placeholder="请简要描述文件内容和用途"
              :autosize="{ minRows: 3, maxRows: 5 }"
            />
          </t-form-item>
          
          <t-form-item label="上传文件" name="file">
            <t-upload
              v-model="formData.fileList"
              :action="uploadAction"
              theme="file"
              accept="application/pdf,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document"
              :headers="uploadHeaders"
              :max="1"
              :size-limit="{ size: 10, unit: 'MB' }"
              @success="onUploadSuccess"
              @fail="onUploadFail"
            >
              <t-button>
                <template #icon>
                  <t-icon name="upload" />
                </template>
                点击上传
              </t-button>
              <template #tip>
                <p class="tip">支持 .pdf, .doc, .docx 格式文件，不超过10MB</p>
              </template>
            </t-upload>
          </t-form-item>
        </t-form>
        
        <div class="status-section">
          <h3>审核状态</h3>
          <t-table
            :data="filteredDocumentHistory"
            :columns="columns"
            row-key="id"
            hover
            stripe
            :loading="tableLoading"
            :pagination="pagination"
          />
        </div>
      </t-space>
    </t-card>
    
    <!-- 提交确认对话框 -->
    <t-dialog
      v-model:visible="dialogVisible"
      header="确认提交"
      :body="dialogMessage"
      :confirm-btn="{ content: '确定', theme: 'primary' }"
      :cancel-btn="{ content: '取消' }"
      @confirm="confirmSubmit"
    />
    
    <!-- 文档详情对话框 -->
    <t-dialog
      v-model:visible="documentDetailVisible"
      header="文档详情"
      width="80%"
      top="5%"
      :footer="false"
      class="document-detail-dialog"
      :z-index="2000"
      attach="body"
    >
      <div v-if="currentDocument" class="document-detail">
        <div class="status-timeline mb-4">
          <t-steps :current="getStepIndex(currentDocument.status)" :status="getStepStatus(currentDocument.status)">
            <t-step-item title="教师提交" content="文件已由教师提交" />
            <t-step-item title="院长审核" :content="getDirectorContent(currentDocument.status)" />
            <t-step-item title="最终审核与发布" content="学院管理员审核并发布" />
          </t-steps>
        </div>
        
        <t-divider></t-divider>
        
        <t-descriptions bordered size="medium">
          <t-descriptions-item label="文件标题">{{ currentDocument.title }}</t-descriptions-item>
          <t-descriptions-item label="文件类型">{{ currentDocument.type }}</t-descriptions-item>
          <t-descriptions-item label="提交时间">{{ currentDocument.submitTime }}</t-descriptions-item>
          <t-descriptions-item label="审核状态">
            <span
              :style="{
                color: getStatusColor(currentDocument.status),
                padding: '2px 8px',
                borderRadius: '3px',
                backgroundColor: `${getStatusColor(currentDocument.status)}15`
              }"
            >
              {{ getStatusText(currentDocument.status) }}
            </span>
          </t-descriptions-item>
          <t-descriptions-item label="适用课程">{{ getCourseName(currentDocument.courseId) }}</t-descriptions-item>
          <t-descriptions-item label="文件描述" :span="2">{{ currentDocument.description || '暂无描述' }}</t-descriptions-item>
        </t-descriptions>
        
        <div v-if="currentDocument.reviewComments" class="document-comments mt-4">
          <h4 class="comments-title">审核意见</h4>
          <t-alert
            theme="warning"
            :title="'审核意见 (' + (currentDocument.status === 'rejected' ? '已驳回' : '已审核') + ')'"
            :description="currentDocument.reviewComments"
          />
        </div>
        
        <div class="document-preview mt-4">
          <h4 class="preview-title">文件预览</h4>
          <div class="file-viewer-wrapper">
            <file-viewer
              :file-url="currentDocument.fileUrl"
              :file-name="currentDocument.title"
              :file-type="getFileType(currentDocument.fileUrl)"
            />
          </div>
        </div>
        
        <div class="document-actions mt-4">
          <t-space>
            <t-button theme="default" @click="downloadDocument(currentDocument.id)" :loading="isDownloading" :disabled="isDownloading">
              <template #icon><t-icon name="download" /></template>
              <template #content>
                <span v-if="!isDownloading">下载文件</span>
                <span v-else>下载中 ({{ downloadProgress }}%)</span>
              </template>
            </t-button>
            <t-progress
              v-if="isDownloading"
              :percentage="downloadProgress"
              :stroke-width="6"
              theme="line"
              status="active"
              style="width: 240px; margin-left: 16px;"
            />
            <t-button v-if="currentDocument.status === 'rejected'" theme="danger" @click="handleResubmit(currentDocument.id)">
              <template #icon><t-icon name="refresh" /></template>
              重新提交
            </t-button>
          </t-space>
        </div>
      </div>
    </t-dialog>
    
    <!-- 文件预览弹窗 -->
    <file-preview-dialog
      v-model="filePreviewVisible"
      :file-url="previewFile?.fileUrl || ''"
      :file-name="previewFile?.fileName || '文件预览'"
      :file-type="previewFile?.fileType || 'other'"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue';
import { 
  MessagePlugin, 
  DialogPlugin,
  Form as TForm,
  FormItem as TFormItem,
  Input as TInput,
  Textarea as TTextarea,
  Button as TButton,
  Card as TCard,
  Upload as TUpload,
  Table as TTable,
  Space as TSpace,
  Dialog as TDialog,
  Alert as TAlert,
  Select as TSelect,
  Option as TOption,
  Icon as TIcon,
  Tag as TTag,
  Descriptions as TDescriptions,
  DescriptionsItem as TDescriptionsItem,
  Comment as TComment,
  Image as TImage,
  Row as TRow,
  Col as TCol,
  Progress as TProgress,
  Steps as TSteps,
  StepItem as TStepItem,
  Divider as TDivider,
} from 'tdesign-vue-next';
import { useRouter, useRoute } from 'vue-router';
import type { FormRule } from 'tdesign-vue-next';
import FileViewer from '@/components/FileViewer.vue';
import FilePreviewDialog from '@/components/FilePreviewDialog.vue';

const router = useRouter();
const route = useRoute();

// 检查是否在课程上下文中
const inCourseContext = computed(() => {
  return route.path.includes('/course/') && route.params.courseId;
});

// 获取当前课程ID
const currentCourseId = computed(() => {
  if (inCourseContext.value) {
    return route.params.courseId.toString();
  }
  return '';
});

// 表单数据
const formData = reactive({
  title: '',
  type: '',
  courseId: '',
  description: '',
  fileList: []
});

// 表单验证规则
const formRules = {
  title: [{ required: true, message: '请输入文件标题', type: 'error' as const }],
  type: [{ required: true, message: '请选择文件类型', type: 'error' as const }],
  courseId: [{ required: true, message: '请选择适用课程', type: 'error' as const }],
  description: [{ required: true, message: '请输入文件描述', type: 'error' as const }],
  file: [{ required: true, message: '请上传文件', type: 'error' as const }]
};

const form = ref(null);
const dialogVisible = ref(false);
const dialogMessage = ref('');
const uploadAction = '/api/upload';
const tableLoading = ref(false);
const documentDetailVisible = ref(false);
const currentDocument = ref<any>(null);
const previewType = ref<any>(null);

// 上传请求头
const uploadHeaders = {
  Authorization: 'Bearer token'
};

// 文件类型列表
const documentTypes = [
  { label: '教学计划', value: 'teaching_plan' },
  { label: '课程大纲', value: 'course_outline' },
  { label: '考核方案', value: 'assessment_plan' },
  { label: '教学材料', value: 'teaching_materials' },
  { label: '其他文档', value: 'other' }
];

// 课程列表
const courseList = ref([
  { id: '1', name: '数据结构' },
  { id: '2', name: '计算机网络' },
  { id: '3', name: '操作系统' },
  { id: '4', name: '编译原理' }
]);

// 表格列定义
const columns = [
  { colKey: 'id', title: 'ID', width: 80 },
  { colKey: 'title', title: '文件标题', width: 200 },
  { colKey: 'type', title: '文件类型', width: 120 },
  { colKey: 'submitTime', title: '提交时间', width: 180 },
  { 
    colKey: 'status', 
    title: '状态', 
    width: 150,
    cell: (h: any, { row }: { row: any }) => {
      const statusMap: Record<string, { text: string; color: string }> = {
        pending: { text: '待审核', color: '#ed7b2f' },
        director_approved: { text: '院长已审核', color: '#0052D9' },
        admin_approved: { text: '已发布', color: '#00a870' },
        rejected: { text: '已驳回', color: '#e34d59' }
      };
      const status = statusMap[row.status] || { text: '未知', color: '#999' };
      return h(
        'span',
        {
          style: {
            color: status.color,
            padding: '2px 8px',
            borderRadius: '3px',
            backgroundColor: `${status.color}15`
          }
        },
        status.text
      );
    }
  },
  { 
    colKey: 'operation', 
    title: '操作', 
    width: 160,
    cell: (h: any, { row }: { row: any }) => {
      return h(
        'div',
        {
          style: {
            display: 'flex',
            gap: '8px'
          }
        },
        [
          h(
            TButton,
            {
              theme: 'primary',
              variant: 'text',
              size: 'small',
              onClick: () => viewDocumentDetail(row.id)
            },
            '查看'
          ),
          h(
            TButton,
            {
              theme: 'default',
              variant: 'text',
              size: 'small',
              onClick: () => openPreview(row)
            },
            '预览'
          ),
          row.status === 'rejected' ? h(
            TButton,
            {
              theme: 'danger',
              variant: 'text',
              size: 'small',
              onClick: () => resubmitDocument(row.id)
            },
            '重新提交'
          ) : null
        ].filter(Boolean)
      );
    }
  }
];

// 分页配置
const pagination = {
  pageSize: 5,
  total: 0,
  current: 1,
  pageSizeOptions: [5, 10, 20],
  showPageSize: true,
  showTotal: true
};

// 历史提交记录
const documentHistory = ref([
  { 
    id: '1001', 
    title: '数据结构教学大纲2023', 
    type: '课程大纲', 
    courseId: '1',
    description: '2023年数据结构课程教学大纲，包含教学目标、教学内容、考核方式等。',
    submitTime: '2023-10-15 14:30:22',
    status: 'admin_approved',
    fileUrl: '/files/example-syllabus.pdf.txt'
  },
  {
    id: '1002',
    title: '计算机网络实验指导书',
    type: '教学材料',
    courseId: '2',
    description: '计算机网络课程实验指导书，包含10个实验内容与步骤说明。',
    submitTime: '2023-11-20 09:15:36',
    status: 'director_approved',
    fileUrl: '/files/teaching-plan.pdf'
  },
  {
    id: '1003',
    title: '操作系统期末考核方案',
    type: '考核方案',
    courseId: '3',
    description: '操作系统课程期末考核方案，包含考试内容、形式及评分标准。',
    submitTime: '2023-12-05 16:42:19',
    status: 'pending',
    fileUrl: '/files/example-docx.docx'
  },
  {
    id: '1004',
    title: '编译原理教学计划修订',
    type: '教学计划',
    courseId: '4',
    description: '编译原理课程教学计划修订稿，包含教学进度安排与课时分配。',
    submitTime: '2024-01-10 11:28:45',
    status: 'rejected',
    reviewComments: '请按照新版教学大纲调整教学内容比例，加强实践环节，减少理论课时。',
    fileUrl: '/files/teaching-plan.txt'
  },
  {
    id: '1005',
    title: '数据结构教学计划',
    type: '教学计划',
    courseId: '1',
    description: '数据结构课程教学计划，包含教学目标、教学内容、考核方式和进度安排。',
    submitTime: '2024-02-20 10:15:30',
    status: 'pending',
    fileUrl: '/files/teaching-plan.txt'
  }
]);

// 根据当前课程过滤文档列表
const filteredDocumentHistory = computed(() => {
  if (inCourseContext.value && currentCourseId.value) {
    return documentHistory.value.filter(doc => doc.courseId === currentCourseId.value);
  }
  return documentHistory.value;
});

// 提交表单
const onSubmit = ({ validateResult, firstError }: { validateResult: any; firstError?: any }) => {
  if (validateResult === true) {
    dialogVisible.value = true;
    dialogMessage.value = '确认提交此文件进行审核吗？提交后将发送给院长进行审批。';
  } else {
    MessagePlugin.error(firstError);
  }
};

// 确认提交
const confirmSubmit = () => {
  // 模拟提交数据
  setTimeout(() => {
    MessagePlugin.success('文件已提交，等待审核');
    
    // 添加到历史记录
    const now = new Date();
    const formattedDate = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}-${String(now.getDate()).padStart(2, '0')} ${String(now.getHours()).padStart(2, '0')}:${String(now.getMinutes()).padStart(2, '0')}:${String(now.getSeconds()).padStart(2, '0')}`;
    
    // 获取文件类型名称
    const typeObj = documentTypes.find(item => item.value === formData.type);
    const typeName = typeObj ? typeObj.label : formData.type;
    
    documentHistory.value.unshift({
      id: `100${documentHistory.value.length + 1}`,
      title: formData.title,
      type: typeName,
      courseId: formData.courseId,
      description: formData.description,
      submitTime: formattedDate,
      status: 'pending',
      fileUrl: '/files/new_document.pdf'
    });
    
    // 重置表单
    formData.title = '';
    formData.type = '';
    formData.description = '';
    formData.fileList = [];
    
    // 如果在课程上下文中，保持课程ID
    if (!inCourseContext.value) {
    formData.courseId = '';
    }
  }, 1000);
};

// 文件上传成功
const onUploadSuccess = (context: any) => {
  MessagePlugin.success('文件上传成功');
};

// 文件上传失败
const onUploadFail = (context: any) => {
  MessagePlugin.error('文件上传失败');
};

// 查看文档详情
const viewDocumentDetail = (id: string) => {
  // 查找对应文档
  const document = documentHistory.value.find(doc => doc.id === id);
  if (document) {
    currentDocument.value = document;
    documentDetailVisible.value = true;
    console.log('打开文档详情弹窗:', document.title);
  } else {
    MessagePlugin.error('找不到此文档');
  }
};

// 根据文件URL获取文件类型
const getFileType = (fileUrl: string): 'pdf' | 'doc' | 'docx' | 'image' | 'other' => {
  if (!fileUrl) return 'other';
  
  const extension = getFileExtension(fileUrl).replace('.', '').toLowerCase();
  
  if (['jpg', 'jpeg', 'png', 'gif', 'webp'].includes(extension)) {
    return 'image';
  } else if (extension === 'pdf') {
    return 'pdf';
  } else if (extension === 'doc') {
    return 'doc';
  } else if (extension === 'docx') {
    return 'docx';
  } else {
    return 'other';
  }
};

// 根据课程ID获取课程名称
const getCourseName = (courseId: string) => {
  const course = courseList.value.find(c => c.id === courseId);
  return course ? course.name : '未知课程';
};

// 添加下载状态和进度
const isDownloading = ref(false);
const downloadProgress = ref(0);

// 下载文档
const downloadDocument = async (id: string) => {
  const docItem = documentHistory.value.find(doc => doc.id === id);
  if (!docItem || !docItem.fileUrl) {
    MessagePlugin.error('无法下载文件，文件不存在');
    return;
  }
  
  isDownloading.value = true;
  downloadProgress.value = 0;
  
  try {
    // 在实际项目中，这应该是一个API调用，获取带有鉴权的下载URL
    // 例如: const response = await fetch(`/api/documents/${id}/download-url`, { headers: { Authorization: `Bearer ${token}` } });
    // const { downloadUrl } = await response.json();
    
    // 模拟API调用延迟
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // 模拟获取下载URL - 在实际项目中，这应该是API返回的URL
    const downloadUrl = docItem.fileUrl;
    
    if (downloadUrl.startsWith('http')) {
      // 如果是直接下载的远程URL，使用fetch API下载并监控进度
      const response = await fetch(downloadUrl, {
        headers: {
          'Authorization': `Bearer token` // 实际项目中应该使用真实token
        }
      });
      
      if (!response.ok) {
        throw new Error(`服务器返回错误: ${response.status}`);
      }
      
      // 获取文件大小用于计算进度
      const contentLength = response.headers.get('content-length');
      const total = contentLength ? parseInt(contentLength, 10) : 0;
      let loaded = 0;
      
      // 创建响应流的reader
      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error('无法读取文件流');
      }
      
      // 创建存储区块的数组
      const chunks: Uint8Array[] = [];
      
      // 循环读取数据块
      while (true) {
        const { done, value } = await reader.read();
        
        if (done) {
          break;
        }
        
        chunks.push(value);
        loaded += value.length;
        
        // 更新进度
        if (total > 0) {
          downloadProgress.value = Math.round((loaded / total) * 100);
        }
      }
      
      // 合并所有区块
      const blob = new Blob(chunks);
      
      // 获取适当的文件名和扩展名
      const fileName = docItem.title || `文档-${id}`;
      const fileExtension = getFileExtension(downloadUrl);
      const fullFileName = `${fileName}${fileExtension}`;
      
      // 创建下载链接
      const url = URL.createObjectURL(blob);
      const link = window.document.createElement('a');
      link.href = url;
      link.download = fullFileName;
      
      // 触发下载
      window.document.body.appendChild(link);
      link.click();
      
      // 清理
      window.document.body.removeChild(link);
      URL.revokeObjectURL(url);
      
      MessagePlugin.success('文件下载成功');
    } else {
      // 对于本地路径，直接使用简单的链接下载方式
      const fileName = docItem.title || `文档-${id}`;
      const fileExtension = getFileExtension(downloadUrl);
      
      const link = window.document.createElement('a');
      link.href = downloadUrl;
      link.setAttribute('download', `${fileName}${fileExtension}`);
      link.setAttribute('target', '_blank');
      
      window.document.body.appendChild(link);
      link.click();
      window.document.body.removeChild(link);
      
      // 模拟下载进度
      const updateProgress = () => {
        if (downloadProgress.value < 100) {
          downloadProgress.value += 10;
          setTimeout(updateProgress, 200);
        }
      };
      
      updateProgress();
      MessagePlugin.success('文件下载已开始');
    }
  } catch (error) {
    console.error('下载文件时出错:', error);
    MessagePlugin.error('下载文件失败，请稍后重试');
  } finally {
    // 下载完成后更新状态
    setTimeout(() => {
      isDownloading.value = false;
      downloadProgress.value = 0;
    }, 1000);
  }
};

// 获取文件扩展名
const getFileExtension = (fileUrl: string): string => {
  if (!fileUrl) return '';
  const match = fileUrl.match(/\.([a-zA-Z0-9]+)(?:[\?#]|$)/);
  return match ? `.${match[1].toLowerCase()}` : '';
};

// 重新提交文档
const resubmitDocument = (id: string) => {
  handleResubmit(id);
};

// 处理重新提交
const handleResubmit = (id: string) => {
  // 查找对应文档
  const docItem = documentHistory.value.find(doc => doc.id === id);
  if (docItem) {
    // 将文档信息填充到表单
    formData.title = docItem.title;
    formData.type = documentTypes.find(type => type.label === docItem.type)?.value || '';
    formData.courseId = docItem.courseId;
    formData.description = docItem.description;
    
    // 关闭对话框
    documentDetailVisible.value = false;
    
    // 提示用户
    MessagePlugin.info('已填充原文档信息，请修改后重新提交');
    
    // 滚动到表单位置
    setTimeout(() => {
      const formElement = window.document.querySelector('.t-form');
      if (formElement) {
        formElement.scrollIntoView({ behavior: 'smooth' });
      }
    }, 300);
  }
};

// 提交文件按钮点击事件
const submitDocument = () => {
  form.value?.submit();
};

// 页面加载时获取数据
onMounted(() => {
  // 确保弹窗初始状态为关闭
  documentDetailVisible.value = false;
  
  // 如果在课程上下文中，自动填充课程ID
  if (inCourseContext.value && currentCourseId.value) {
    formData.courseId = currentCourseId.value;
    
    // 设置对应的课程名称
    const selectedCourse = courseList.value.find(course => course.id === currentCourseId.value);
    if (selectedCourse) {
      // 如果标题为空，可以自动填充标题前缀
      if (!formData.title) {
        formData.title = `${selectedCourse.name} - `;
      }
    }
  }
  
  // 实际应该从API获取数据
  tableLoading.value = true;
  setTimeout(() => {
    pagination.total = filteredDocumentHistory.value.length;
    tableLoading.value = false;
  }, 1000);
});

// 文件预览相关
const filePreviewVisible = ref(false);
const previewFile = ref<any>(null);

// 预览文件
const openPreview = (document: any) => {
  if (document && document.fileUrl) {
    previewFile.value = {
      fileUrl: document.fileUrl,
      fileName: document.title,
      fileType: getFileType(document.fileUrl)
    };
    filePreviewVisible.value = true;
  } else {
    MessagePlugin.error('无法预览文件，文件不存在');
  }
};

// 获取状态颜色
const getStatusColor = (status: string): string => {
  const statusMap: Record<string, string> = {
    pending: '#ed7b2f',
    director_approved: '#0052D9',
    admin_approved: '#00a870',
    rejected: '#e34d59'
  };
  return statusMap[status] || '#999';
};

// 获取状态文本
const getStatusText = (status: string): string => {
  const statusMap: Record<string, string> = {
    pending: '待审核',
    director_approved: '院长已审核',
    admin_approved: '已发布',
    rejected: '已驳回'
  };
  return statusMap[status] || '未知状态';
};

// 获取步骤索引
const getStepIndex = (status: string): number => {
  switch (status) {
    case 'pending':
      return 0;
    case 'director_approved':
      return 1;
    case 'admin_approved':
      return 2;
    case 'rejected':
      return status === 'rejected' ? 1 : 0; // 如果是驳回状态，显示到院长审核步骤
    default:
      return 0;
  }
};

// 获取步骤状态
const getStepStatus = (status: string): 'process' | 'finish' | 'error' | 'warning' => {
  switch (status) {
    case 'pending':
      return 'process';
    case 'director_approved':
      return 'process';
    case 'admin_approved':
      return 'finish';
    case 'rejected':
      return 'error';
    default:
      return 'warning';
  }
};

// 获取院长审核内容
const getDirectorContent = (status: string): string => {
  switch (status) {
    case 'pending':
      return '等待院长审核';
    case 'director_approved':
      return '院长已批准';
    case 'admin_approved':
      return '院长已批准';
    case 'rejected':
      return '院长已驳回';
    default:
      return '未知状态';
  }
};
</script>

<style lang="less" scoped>
.document-submit-container {
  padding: 20px;
}

.tip {
  font-size: 12px;
  color: #999;
  line-height: 1.5;
  margin-top: 8px;
}

.status-section {
  margin-top: 24px;
  
  h3 {
    font-size: 16px;
    margin-bottom: 16px;
    font-weight: 500;
    color: #333;
  }
}

.document-detail {
  .mt-4 {
    margin-top: 16px;
  }
  
  .mb-4 {
    margin-bottom: 16px;
  }
  
  .status-timeline {
    padding: 16px 0;
  }
  
  .preview-title, .comments-title {
    font-size: 16px;
    margin-bottom: 12px;
    font-weight: 500;
    color: #333;
  }
  
  .file-viewer-wrapper {
    height: 600px;
    margin-bottom: 16px;
  }
  
  .preview-content {
    background-color: #f5f5f5;
    border-radius: 4px;
    padding: 16px;
    display: flex;
    justify-content: center;
    min-height: 200px;
    
    .preview-placeholder {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      text-align: center;
      color: #999;
      padding: 32px;
      width: 100%;
      
      .t-icon {
        font-size: 48px;
        color: #aaa;
        margin-bottom: 16px;
      }
      
      p {
        margin: 8px 0;
        font-size: 14px;
        line-height: 1.5;
      }
    }
  }
}

.document-detail-dialog {
  :deep(.t-dialog__body) {
    max-height: 80vh;
    overflow-y: auto;
    padding: 16px;
  }
  
  :deep(.t-dialog__header) {
    padding: 16px;
    border-bottom: 1px solid #e7e7e7;
  }
  
  :deep(.t-dialog__content) {
    padding: 0;
  }
}
</style> 
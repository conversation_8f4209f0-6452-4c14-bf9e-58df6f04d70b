<template>
  <div class="course-detail-container">
    <!-- 顶部导航区域 -->
    <div class="page-header">
      <t-button theme="default" @click="goBack">
        <template #icon><t-icon name="arrow-left" /></template>
        返回课程列表
      </t-button>
      <h2>{{ courseInfo.name }} - 课程详情</h2>
    </div>
    <!-- 课程基本信息卡片 -->
    <t-card class="course-info-card" title="课程基本信息">
      <div class="course-info-grid">
        <div class="info-item">
          <label>课程名称：</label>
          <span>{{ courseInfo.name }}</span>
        </div>
        <div class="info-item">
          <label>授课班级：</label>
          <span>{{ courseInfo.class }}</span>
        </div>
        <div class="info-item">
          <label>学生人数：</label>
          <span>{{ courseInfo.studentCount }}</span>
        </div>
        <div class="info-item">
          <label>授课学期：</label>
          <span>{{ courseInfo.semester }}</span>
        </div>
        <div class="info-item">
          <label>课程状态：</label>
          <t-tag theme="primary" variant="light" v-if="courseInfo.status === '进行中'">进行中</t-tag>
          <t-tag theme="default" variant="light" v-else>已结束</t-tag>
        </div>
        <div class="info-item">
          <label>课程代码：</label>
          <span>{{ courseInfo.code }}</span>
        </div>
      </div>
    </t-card>

    <!-- 标签页导航 -->
    <t-tabs v-model="activeTab" class="course-tabs">
      <!-- <t-tab-panel value="basicData" label="基础数据">
        <div class="tab-content">
          <p>基础数据内容待完善</p>
        </div>
      </t-tab-panel>

      <t-tab-panel value="classReport" label="课堂报告">
        <div class="tab-content">
          <p>课堂报告内容待完善</p>
      </div>
      </t-tab-panel> -->

      <t-tab-panel value="studentStats" label="学情统计">
        <div class="tab-content charts-container">
          <!-- 课程目标达成度图表组件 - 临时注释 -->
          <div class="placeholder-content">
            <p>课程目标达成度图表功能开发中...</p>
          </div>
          <!-- <CourseGoalsCharts :course-goals="courseInfo.goals" /> -->
        </div>
      </t-tab-panel>

      <t-tab-panel value="studentGrades" label="学生成绩">
        <div class="tab-content">
          <!-- 学生成绩表格组件 - 临时注释 -->
          <div class="placeholder-content">
            <p>学生成绩表格功能开发中...</p>
          </div>
          <!-- <StudentGradesTable :course-id="courseId" /> -->
        </div>
      </t-tab-panel>

      <!-- <t-tab-panel value="learningMonitor" label="学习监控">
        <div class="tab-content">
          <p>学习监控内容待完善</p>
    </div>
      </t-tab-panel> -->
    </t-tabs>
    
    <!-- 添加加载状态显示 -->
    <t-loading :loading="loading" fullscreen></t-loading>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import {
  Button as TButton,
  Card as TCard,
  Tag as TTag,
  Icon as TIcon,
  Tabs as TTabs,
  TabPanel as TTabPanel,
  Loading as TLoading,
  MessagePlugin
} from 'tdesign-vue-next'
// 导入子组件 - 临时注释以解决编译错误
// import CourseGoalsCharts from './components/CourseGoalsCharts.vue'
// import StudentGradesTable from './components/StudentGradesTable.vue'
import { getCourseDetails } from '@/api/base/teacher/CourseDetails'

const route = useRoute()
const router = useRouter()
const courseId = ref(route.params.id as string)
const loading = ref(false)

// 默认选中学生成绩标签页
const activeTab = ref('studentGrades')

// 课程信息
const courseInfo = ref({
  id: 0,
  name: '加载中...',
  class: '加载中...',
  code: '',
  studentCount: 0,
  semester: '',
  status: '',
  goals: []
})

// 返回上一页
const goBack = () => {
  router.push('/teachers/main')
}

// 获取课程信息
const fetchCourseInfo = async (id: string) => {
  loading.value = true
  try {
    // 使用API服务获取课程详情
    const data = await getCourseDetails(id)
    courseInfo.value = data
  } catch (error) {
    console.error('获取课程详情出错:', error)
    MessagePlugin.error('获取课程信息失败，请稍后重试')
  } finally {
    loading.value = false
  }
}

// 监听路由变化，重新获取课程信息
watch(() => route.params.id, (newId) => {
  if (newId) {
    courseId.value = newId as string
    fetchCourseInfo(courseId.value)
  }
})

onMounted(() => {
  fetchCourseInfo(courseId.value)
})
</script>

<style scoped lang="less">
.course-detail-container {
  padding: 20px;
}

.page-header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;

  h2 {
    margin: 0 0 0 20px;
  }
}

.course-info-card {
  margin-bottom: 20px;
}

.course-info-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;

  .info-item {
    display: flex;
    align-items: center;

    label {
      color: #666;
      margin-right: 10px;
      min-width: 80px;
    }

    span {
      color: #333;
      font-weight: 500;
    }
  }
}

.course-tabs {
  background: white;
  padding: 10px;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);

  .tab-content {
    padding: 20px 0;
    min-height: 400px;
  }

  .placeholder-content {
    text-align: center;
    padding: 40px 20px;
    color: #999;
    background: #f8f9fa;
    border-radius: 8px;
    border: 2px dashed #e0e0e0;
    margin: 20px 0;
  }
}

// 响应式布局
@media screen and (max-width: 1200px) {
  .course-info-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media screen and (max-width: 768px) {
  .course-info-grid {
    grid-template-columns: 1fr;
  }
}
</style>

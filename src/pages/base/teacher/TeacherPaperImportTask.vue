<template>
    <div class="import-task-container">
        <div class="page-header">
            <t-button theme="default" @click="goBack">
                <template #icon><t-icon name="arrow-left" /></template>
                {{ returnText }}
            </t-button>
            <h2>{{ pageTitle }}</h2>
        </div>

        <t-card title="选择文件">
            <div class="upload-section">
                <t-upload v-model="fileList" :action="''" theme="file" :accept="'.docx,.pdf'"
                    :max-size="10 * 1024 * 1024" :before-upload="beforeUploadFile" :auto-upload="false"
                    :placeholder="placeholder" @fail="onFail" @success="onSuccess" @progress="onProgress" draggable>
                    <template #dragContent>
                        <div class="upload-drag-content">
                            <div class="upload-icon">
                                <t-icon name="upload" size="36px" />
                            </div>
                            <div class="upload-text">
                                <p class="title">点击或拖拽文件到此区域上传</p>
                                <p class="sub-title">支持 PDF、Word (docx) 文件</p>
                            </div>
                        </div>
                    </template>
                </t-upload>
            </div>

            <div class="extract-button-container" v-if="fileList.length > 0">
                <t-button theme="primary" @click="extractQuestions">解析题目</t-button>
            </div>
        </t-card>

        <!-- 题目预览区域 -->
        <t-card title="题目预览" v-if="parsedQuestions.length > 0">
            <div class="questions-preview">
                <div v-for="(group, groupName) in groupedQuestions" :key="groupName" class="question-group">
                    <div class="group-title">{{ getGroupTitle(groupName) }}</div>

                    <!-- 单选题 -->
                    <template v-if="groupName === 'single'">
                        <div v-for="(question, index) in group" :key="question.id" class="question-item-container">
                            <div class="question-header">
                                <span class="question-number">{{ getQuestionNumber(groupName, index) }}.</span>
                                <span class="question-title">{{ question.title }}</span>
                                <span class="question-score">{{ question.score }}分</span>
                            </div>
                            <div class="question-content">
                                <div v-for="(option, optIndex) in question.options" :key="optIndex" class="option-item">
                                    <span class="option-label">{{ String.fromCharCode(65 + optIndex) }}.</span>
                                    <span class="option-content">{{ option.content }}</span>
                                    <span v-if="option.isCorrect" class="option-correct">✓</span>
                                </div>
                            </div>
                            <div class="question-answer">
                                <span class="answer-label">正确答案:</span>
                                <span class="answer-content">
                                    {{question.options.filter(opt => opt.isCorrect).map((_, i) => String.fromCharCode(65
                                        +
                                        i)).join(', ')}}
                                </span>
                            </div>
                        </div>
                    </template>

                    <!-- 多选题 -->
                    <template v-if="groupName === 'multiple'">
                        <div v-for="(question, index) in group" :key="question.id" class="question-item-container">
                            <div class="question-header">
                                <span class="question-number">{{ getQuestionNumber(groupName, index) }}.</span>
                                <span class="question-title">{{ question.title }}</span>
                                <span class="question-score">{{ question.score }}分</span>
                            </div>
                            <div class="question-content">
                                <div v-for="(option, optIndex) in question.options" :key="optIndex" class="option-item">
                                    <span class="option-label">{{ String.fromCharCode(65 + optIndex) }}.</span>
                                    <span class="option-content">{{ option.content }}</span>
                                    <span v-if="option.isCorrect" class="option-correct">✓</span>
                                </div>
                            </div>
                            <div class="question-answer">
                                <span class="answer-label">正确答案:</span>
                                <span class="answer-content">
                                    {{question.options.filter(opt => opt.isCorrect).map((_, i) => String.fromCharCode(65
                                        +
                                        i)).join(', ')}}
                                </span>
                            </div>
                        </div>
                    </template>

                    <!-- 判断题 -->
                    <template v-if="groupName === 'truefalse'">
                        <div v-for="(question, index) in group" :key="question.id" class="question-item-container">
                            <div class="question-header">
                                <span class="question-number">{{ getQuestionNumber(groupName, index) }}.</span>
                                <span class="question-title">{{ question.title }}</span>
                                <span class="question-score">{{ question.score }}分</span>
                            </div>
                            <div class="question-answer">
                                <span class="answer-label">正确答案:</span>
                                <span class="answer-content">{{ question.answer === 'true' ? '正确' : '错误' }}</span>
                            </div>
                        </div>
                    </template>

                    <!-- 填空题 -->
                    <template v-if="groupName === 'fillblank'">
                        <div v-for="(question, index) in group" :key="question.id" class="question-item-container">
                            <div class="question-header">
                                <span class="question-number">{{ getQuestionNumber(groupName, index) }}.</span>
                                <span class="question-title" v-html="formatFillBlankTitle(question)"></span>
                                <span class="question-score">{{ question.score }}分</span>
                            </div>
                            <div class="question-answer">
                                <div v-for="(blank, blankIndex) in question.blanks" :key="blankIndex"
                                    class="blank-answer">
                                    <span class="answer-label">空格{{ blankIndex + 1 }}:</span>
                                    <span class="answer-content">{{ blank.answer }}</span>
                                </div>
                            </div>
                        </div>
                    </template>

                    <!-- 简答题 -->
                    <template v-if="groupName === 'essay'">
                        <div v-for="(question, index) in group" :key="question.id" class="question-item-container">
                            <div class="question-header">
                                <span class="question-number">{{ getQuestionNumber(groupName, index) }}.</span>
                                <span class="question-title">{{ question.title }}</span>
                                <span class="question-score">{{ question.score }}分</span>
                            </div>
                            <div class="question-answer" v-if="question.referenceAnswer">
                                <span class="answer-label">参考答案:</span>
                                <div class="reference-answer">{{ question.referenceAnswer }}</div>
                            </div>
                        </div>
                    </template>
                </div>
            </div>

            <div class="action-buttons">
                <t-button theme="primary" @click="importToAssignment">
                    {{ isExam.value ? '确认导入到考试' : '确认导入到作业' }}
                </t-button>
                <t-button theme="default" @click="cancelImport">取消</t-button>
            </div>
        </t-card>
    </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import {
    Button as TButton,
    Card as TCard,
    Icon as TIcon,
    Upload as TUpload,
    MessagePlugin,
    UploadFile
} from 'tdesign-vue-next'

const router = useRouter()
const route = useRoute()
const fileList = ref<UploadFile[]>([])
const theme = ref('file')
const placeholder = ref('支持 PDF、Word (docx) 文件')
const parsedQuestions = ref<any[]>([])

const taskType = ref(route.query.type?.toString() || 'homework')
const isExam = computed(() => taskType.value === 'exam')

// 动态标题
const pageTitle = computed(() => isExam.value ? '导入考试' : '导入作业')
const returnText = computed(() => isExam.value ? '返回考试列表' : '返回作业列表')

// 题型分组
const groupedQuestions = computed(() => {
    const grouped: Record<string, any[]> = {
        single: [],
        multiple: [],
        truefalse: [],
        fillblank: [],
        essay: []
    }

    parsedQuestions.value.forEach(question => {
        const type = question.type as string
        if (grouped[type]) {
            grouped[type].push(question)
        }
    })

    return grouped
})

// 获取题型标题
const getGroupTitle = (type: string): string => {
    const titles: { [key: string]: string } = {
        single: '单选题',
        multiple: '多选题',
        truefalse: '判断题',
        fillblank: '填空题',
        essay: '简答题'
    }
    return titles[type] || '未知题型'
}

// 获取题目编号
const getQuestionNumber = (type: string, index: number): string => {
    return `${index + 1}`
}

// 返回按钮
const goBack = () => {
    if (isExam.value) {
        router.push('/teachers/exam')
    } else {
        router.push('/teachers/task')
    }
}

// 上传前文件检查
const beforeUploadFile = (file: UploadFile): boolean => {
    // 验证文件类型
    const fileType = file.raw?.type
    const isValidType = fileType === 'application/pdf' ||
        fileType === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'

    if (!isValidType) {
        MessagePlugin.error('只支持上传PDF或Word文档')
        return false
    }

    return true
}

// 解析题目
const extractQuestions = () => {
    if (fileList.value.length === 0) {
        MessagePlugin.warning('请先上传文件')
        return
    }

    // 模拟解析过程
    MessagePlugin.info('正在解析文件中的题目...')

    // 这里应该是实际的文件解析逻辑
    setTimeout(() => {
        // 模拟解析结果
        parsedQuestions.value = [
            {
                id: Date.now(),
                type: 'single',
                title: '以下哪种数据结构适合用于实现队列？',
                content: '以下哪种数据结构适合用于实现队列？',
                score: 5,
                options: [
                    { content: '栈', isCorrect: false },
                    { content: '链表', isCorrect: true },
                    { content: '二叉树', isCorrect: false },
                    { content: '哈希表', isCorrect: false }
                ],
                blanks: [],
                referenceAnswer: '',
                answer: ''
            },
            {
                id: Date.now() + 1,
                type: 'multiple',
                title: '以下哪些排序算法的平均时间复杂度是O(nlogn)？',
                content: '以下哪些排序算法的平均时间复杂度是O(nlogn)？',
                score: 5,
                options: [
                    { content: '冒泡排序', isCorrect: false },
                    { content: '快速排序', isCorrect: true },
                    { content: '归并排序', isCorrect: true },
                    { content: '堆排序', isCorrect: true }
                ],
                blanks: [],
                referenceAnswer: '',
                answer: ''
            },
            {
                id: Date.now() + 2,
                type: 'truefalse',
                title: '快速排序在最坏情况下的时间复杂度是O(n²)。',
                content: '快速排序在最坏情况下的时间复杂度是O(n²)。',
                score: 3,
                options: [],
                blanks: [],
                referenceAnswer: '',
                answer: 'true'
            },
            {
                id: Date.now() + 3,
                type: 'fillblank',
                title: '在数据结构中，一个节点最多有两个子节点的树叫做__树。',
                content: '在数据结构中，一个节点最多有两个子节点的树叫做__树。',
                score: 4,
                options: [],
                blanks: [
                    { answer: '二叉' }
                ],
                referenceAnswer: '',
                answer: ''
            },
            {
                id: Date.now() + 4,
                type: 'essay',
                title: '请解释快速排序的基本原理和实现思路。',
                content: '请解释快速排序的基本原理和实现思路。',
                score: 10,
                options: [],
                blanks: [],
                referenceAnswer: '快速排序是一种分治算法，基本思想是：选择一个基准元素，通过一趟排序将要排序的数据分割成独立的两部分，其中一部分的所有数据都比另外一部分的所有数据都要小，然后再按此方法对这两部分数据分别进行快速排序，整个排序过程可以递归进行，以此达到整个数据变成有序序列。',
                answer: ''
            }
        ]

        MessagePlugin.success('解析完成，共提取出5道题目')
    }, 1500)
}

// 文件上传相关回调
const onSuccess = (context: any) => {
    MessagePlugin.success('文件上传成功')
}

const onFail = (context: any) => {
    MessagePlugin.error('文件上传失败')
}

const onProgress = (context: any) => {
    console.log('上传进度:', context)
}

// 确认导入
const importToAssignment = () => {
    // 构建导入数据
    const importData = {
        questions: parsedQuestions.value,
        type: taskType.value // 添加类型字段
    }

    // 显示成功消息
    MessagePlugin.success(isExam.value ? '考试导入成功' : '作业导入成功')

    // 跳转到相应页面，并传递类型参数
    setTimeout(() => {
        router.push(`/teachers/newtask?type=${taskType.value}`)
    }, 1000)
}

// 取消导入
const cancelImport = () => {
    if (confirm('确定要取消导入吗？已解析的题目将丢失。')) {
        parsedQuestions.value = []
        fileList.value = []
    }
}

// 格式化填空题标题，将__替换为下划线
const formatFillBlankTitle = (question: any): string => {
    if (!question.content) return '';

    // 替换所有的__为下划线空格
    let formattedContent = question.content.replace(/__(.*?)__/g, '<span class="blank-underline">______</span>');

    // 如果没有显式的__标记，则寻找单独的__
    if (formattedContent === question.content) {
        formattedContent = question.content.replace(/__/g, '<span class="blank-underline">______</span>');
    }

    return formattedContent;
}
</script>

<style scoped>
.import-task-container {
    padding: 20px;
}

.page-header {
    display: flex;
    align-items: center;
    margin-bottom: 20px;

    h2 {
        margin: 0 0 0 20px;
    }
}

.upload-section {
    margin-bottom: 20px;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
}

.upload-drag-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 0;
}

.upload-icon {
    margin-bottom: 16px;
    color: #0052d9;
}

.upload-text .title {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 8px;
}

.upload-text .sub-title {
    font-size: 14px;
    color: #666;
}

.extract-button-container {
    margin-top: 20px;
    text-align: center;
}

.questions-preview {
    margin-top: 20px;
}

.question-group {
    margin-bottom: 20px;
}

.group-title {
    font-weight: bold;
    margin-bottom: 10px;
    padding: 8px;
    background-color: #f5f5f5;
    border-radius: 4px;
}

.question-item-container {
    border: 1px solid #eaeaea;
    border-radius: 6px;
    margin-bottom: 16px;
    padding: 16px;
    background-color: #ffffff;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.question-header {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    border-bottom: 1px dashed #eaeaea;
    padding-bottom: 8px;
}

.question-number {
    font-weight: bold;
    margin-right: 8px;
    min-width: 24px;
}

.question-title {
    flex: 1;
    font-weight: 500;
}

.question-content {
    margin-bottom: 12px;
    padding-left: 32px;
}

.option-item {
    display: flex;
    margin-bottom: 8px;
    align-items: flex-start;
}

.option-label {
    width: 24px;
    margin-right: 8px;
    font-weight: 500;
}

.option-content {
    flex: 1;
}

.option-correct {
    margin-left: 8px;
    color: #0abb87;
    font-weight: bold;
}

.question-answer {
    margin-top: 12px;
    padding: 8px 12px;
    background-color: #f9f9f9;
    border-radius: 4px;
}

.answer-label {
    font-weight: 500;
    margin-right: 8px;
    color: #666;
}

.answer-content {
    color: #0052d9;
    font-weight: 500;
}

.blank-answer {
    margin-bottom: 4px;
}

.reference-answer {
    margin-top: 8px;
    padding: 8px;
    background-color: rgba(0, 82, 217, 0.05);
    border-radius: 4px;
    color: #333;
    white-space: pre-line;
}

.blank-underline {
    display: inline-block;
    min-width: 60px;
    border-bottom: 1px solid #000;
    margin: 0 4px;
    position: relative;
    text-align: center;
}

.question-score {
    margin-left: 16px;
    color: #0052d9;
    font-weight: 500;
    padding: 2px 8px;
    background-color: rgba(0, 82, 217, 0.05);
    border-radius: 12px;
    font-size: 12px;
}

.action-buttons {
    display: flex;
    gap: 12px;
    margin-top: 20px;
    justify-content: center;
}
</style>

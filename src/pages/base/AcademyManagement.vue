<template>
  <div class="college-management-container">
    <t-card>
      <!-- 搜索表单区域 -->
      <div class="search-container">
        <t-form ref="searchFormRef" :data="searchFormData" layout="inline">
          <t-form-item label="学院名称">
            <t-input
              v-model="searchFormData.academyName"
              placeholder="请输入学院名称"
              clearable
              class="fixed-width-input"
            />
          </t-form-item>
          <t-form-item label="院长">
            <t-select
              v-model="searchFormData.academyPresidentId"
              placeholder="请选择院长"
              clearable
              :options="teacherOptions"
              value-field="value"
              label-field="label"
              class="fixed-width-select"
            />
          </t-form-item>
          <t-form-item label="状态">
            <t-select
              v-model="searchFormData.status"
              placeholder="请选择状态"
              clearable
              :options="statusOptions"
              class="fixed-width-select"
            />
          </t-form-item>
          <t-form-item>
            <t-space>
              <t-button theme="primary" @click="handleSearch">查询</t-button>
              <t-button theme="default" @click="resetSearch">重置</t-button>
            </t-space>
          </t-form-item>
        </t-form>
      </div>

      <!-- 操作按钮区域 -->
      <div class="action-buttons">
        <t-space>
          <t-button theme="primary" @click="handleAdd">
            <template #icon><t-icon name="add" /></template>新增学院
          </t-button>
          <t-button theme="default" @click="openImportDialog">
            <template #icon><t-icon name="upload" /></template>导入
          </t-button>
          <t-button theme="default" @click="handleExport">
            <template #icon><t-icon name="download" /></template>导出
          </t-button>
          <t-button theme="default" @click="refreshData">
            <template #icon><t-icon name="refresh" /></template>刷新
          </t-button>
        </t-space>
      </div>

      <!-- 表格区域 -->
      <t-loading :loading="loading">
        <t-table
          :data="tableData"
          :columns="columns"
          row-key="id"
          hover
          stripe
          :pagination="pagination"
          :max-height="600"
        >
          <template #serial-number="{ rowIndex }">
            {{ (pagination.current - 1) * pagination.pageSize + rowIndex + 1 }}
          </template>
          <template #status="{ row }">
            <t-tag
              :theme="row.status === 0 || row.status === '正常' ? 'success' : row.status === 1 ? 'warning' : 'danger'"
              variant="light"
            >
              {{
                row.status === '正常' ? '正常' :
                row.status === 0 ? '启用' :
                row.status === 1 ? '停用' :
                  '未知'
              }}
            </t-tag>
          </template>
          <template #dean="{ row }">
            <span v-if="row.dean && row.dean !== ''" class="dean-name">{{ row.dean }}</span>
            <t-tag v-else theme="warning" variant="light">待设置</t-tag>
          </template>
          <template #operation="{ row }">
            <div class="operation-container">
              <t-space size="small">
                <t-button size="small" variant="outline" theme="primary" @click="viewDetail(row)">
                  <template #icon><t-icon name="browse" /></template>
                  查看
                </t-button>
                <t-button size="small" variant="outline" theme="primary" @click="handleEdit(row)">
                  <template #icon><t-icon name="edit" /></template>
                  编辑
                </t-button>
                <t-button size="small" variant="outline" theme="success" @click="setDean(row)">
                  <template #icon><t-icon name="user" /></template>
                  设置院长
                </t-button>
                <t-dropdown trigger="click" placement="bottom-right">
                  <t-button size="small" variant="outline">
                    <template #icon><t-icon name="more" /></template>
                    更多
                  </t-button>
                  <t-dropdown-menu>
                    <t-dropdown-item v-if="row.status === 0" @click="toggleStatus(row)">
                      <t-icon name="poweroff" />
                      停用
                    </t-dropdown-item>
                    <t-dropdown-item v-if="row.status === 1" @click="toggleStatus(row)">
                      <t-icon name="play-circle" />
                      启用
                    </t-dropdown-item>
                    <t-dropdown-item @click="handleDelete(row)">
                      <t-icon name="delete" />
                      删除
                    </t-dropdown-item>
                  </t-dropdown-menu>
                </t-dropdown>
              </t-space>
            </div>
          </template>
        </t-table>
      </t-loading>
    </t-card>

    <!-- 新增/编辑对话框 -->
    <t-dialog
      v-model:visible="dialogVisible"
      :header="dialogTitle"
      :width="500"
      :footer="false"
      @close="dialogVisible = false"
    >
      <t-form
        ref="formRef"
        :data="formData"
        :rules="rules"
        label-width="100px"
        @submit="handleSubmit"
      >
        <t-form-item label="学院名称" name="academyName">
          <t-input v-model="formData.academyName" placeholder="请输入学院名称"></t-input>
        </t-form-item>

        <t-form-item label="院长" name="academyPresidentId">
          <t-select
            v-model="formData.academyPresidentId"
            placeholder="请选择院长"
            value-field="value"
            label-field="label"
            :key="teacherOptions.length"
            clearable
          >
            <t-option v-for="item in teacherOptions" :key="item.value" :value="item.value" :label="item.label">
              <div class="dean-option-container">
                <span class="teacher-name">{{ item.label }}</span>
                <span v-if="item.currentAcademyName" class="dean-badge">
                  {{ item.currentAcademyName }}院长
                </span>
              </div>
            </t-option>
          </t-select>
        </t-form-item>

        <t-form-item label="状态" name="status">
          <t-radio-group v-model="formData.status">
            <t-radio :value="0">正常</t-radio>
            <t-radio :value="1">停用</t-radio>
          </t-radio-group>
        </t-form-item>

        <t-form-item>
          <t-space>
            <t-button theme="primary" type="submit">确认</t-button>
            <t-button theme="default" variant="base" @click="dialogVisible = false">取消</t-button>
          </t-space>
        </t-form-item>
      </t-form>
    </t-dialog>

    <!-- 设置院长对话框 -->
    <t-dialog
      v-model:visible="deanDialogVisible"
      header="设置院长"
      :width="400"
      :footer="false"
      @close="deanDialogVisible = false"
    >
      <t-form
        ref="deanFormRef"
        :data="deanFormData"
        :rules="deanRules"
        label-width="80px"
        @submit="handleSetDean"
      >
        <t-form-item label="学院名称">
          <t-input :value="currentCollege?.name" disabled />
        </t-form-item>

        <t-form-item label="院长" name="academyPresidentId">
          <t-select
            v-model="deanFormData.academyPresidentId"
            placeholder="请选择院长"
            value-field="value"
            label-field="label"
            :key="teacherOptions.length"
          >
            <t-option v-for="item in teacherOptions" :key="item.value" :value="item.value" :label="item.label">
              <div class="dean-option-container">
                <span class="teacher-name">{{ item.label }}</span>
                <span v-if="item.currentAcademyName" class="dean-badge">
                  {{ item.currentAcademyName }}院长
                </span>
              </div>
            </t-option>
          </t-select>
        </t-form-item>

        <t-form-item>
          <t-space>
            <t-button theme="primary" type="submit">确认</t-button>
            <t-button theme="default" variant="base" @click="deanDialogVisible = false">取消</t-button>
          </t-space>
        </t-form-item>
      </t-form>
    </t-dialog>

    <!-- 导入对话框 -->
    <ImportDialog
      v-model:visible="importVisible"
      :config="importConfig"
      :callbacks="importCallbacks"
    />

    <!-- 详情对话框 -->
    <t-dialog
      v-model:visible="detailVisible"
      header="学院详情"
      width="70vw"
      :footer="false"
      @close="detailVisible = false"
    >
      <t-loading :loading="detailLoading">
        <div class="detail-container" v-if="collegeDetail">
          <t-descriptions bordered size="medium" :column="2">
            <t-descriptions-item label="学院ID">{{ collegeDetail.id }}</t-descriptions-item>
            <t-descriptions-item label="学院名称">{{ collegeDetail.name }}</t-descriptions-item>
            <t-descriptions-item label="院长">
              <span v-if="collegeDetail.dean && collegeDetail.dean !== ''">{{ collegeDetail.dean }}</span>
              <t-tag v-else theme="warning" variant="light">待设置</t-tag>
            </t-descriptions-item>
            <t-descriptions-item label="状态">
              <t-tag :theme="collegeDetail.status === 0 || collegeDetail.status === '正常' ? 'success' : 'danger'">
                {{
                  collegeDetail.status === '正常' ? '正常' :
                  collegeDetail.status === 0 ? '启用' :
                  collegeDetail.status === 1 ? '停用' : '未知'
                }}
              </t-tag>
            </t-descriptions-item>
            <t-descriptions-item label="专业数量">{{ collegeDetail.majors || 0 }}</t-descriptions-item>
            <t-descriptions-item label="班级数量">{{ collegeDetail.classes || 0 }}</t-descriptions-item>
            <t-descriptions-item label="创建人">{{ collegeDetail.creator || '未知' }}</t-descriptions-item>
            <t-descriptions-item label="创建时间">{{ formatDate(collegeDetail.createTime, 'YYYY-MM-DD HH:mm') }}</t-descriptions-item>
            <t-descriptions-item label="修改人">{{ collegeDetail.updater || '未知' }}</t-descriptions-item>
            <t-descriptions-item label="修改时间">{{ formatDate(collegeDetail.updateTime, 'YYYY-MM-DD HH:mm') }}</t-descriptions-item>
          </t-descriptions>
        </div>
        <div v-else>
          <t-alert theme="info" message="无法获取学院详情" />
        </div>
      </t-loading>
    </t-dialog>
  </div>
</template>

<script setup lang="ts">
import { addAcademy, deleteAcademy, disableAcademy, enableAcademy, exportAcademies, getAcademyDetail, getAcademyList, getDeanOptions, importAcademies, updateAcademy, setAcademyDean } from '@/api/base/academy';
import { formatDate } from '@/utils/date';
import { getCurrentInstance, onMounted, reactive, ref, computed } from 'vue';
import ImportDialog from '@/components/ImportDialog/index.vue';
import type { ImportConfig, ImportCallbacks } from '@/components/ImportDialog/types';
const { proxy } = getCurrentInstance();
// 院长选项类型定义
interface DeanOption {
  label: string;   // 院长姓名
  value: string;   // 院长ID
  title?: string;  // 职称 (可选)
  currentAcademyId?: string | number;  // 当前担任院长的学院ID
  currentAcademyName?: string;  // 当前担任院长的学院名称
}

interface CollegeItem {
  id: string | number;
  name: string;
  dean: string;
  academyPresidentId: string | number;
  creator: string;
  createTime: string;
  status: string | number;
  updater: string;
  updateTime: string;
  majors: number;
  classes: number;
}

// 表格数据
const tableData = ref<CollegeItem[]>([]);
const loading = ref(false);

// 搜索条件
const searchFormData = reactive({
  academyName: '',
  academyPresidentId: '',
  status: ''
});

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  pageSizeOptions: [10, 20, 50],
  showJumper: true,
  showPageSize: true,
  onChange: (pageInfo: any) => {
    pagination.current = pageInfo.current;
    pagination.pageSize = pageInfo.pageSize;
    fetchCollegeList();
  },
});

// 表格列配置
const columns = ref([
  {
    colKey: 'serial-number',
    title: '序号',
    width: 80,
    align: 'center'
  },
  {
    colKey: 'name',
    title: '学院名称',
    width: 200,
    ellipsis: true
  },
  {
    colKey: 'dean',
    title: '院长',
    width: 120
  },
  {
    colKey: 'majors',
    title: '专业数量',
    width: 100,
    align: 'center'
  },
  {
    colKey: 'classes',
    title: '班级数量',
    width: 100,
    align: 'center'
  },
  {
    colKey: 'status',
    title: '状态',
    width: 100,
    align: 'center'
  },
  {
    colKey: 'operation',
    title: '操作',
    width: 300,
    align: 'center',
    fixed: 'right'
  }
]);

// 状态选项
const statusOptions = [
  { label: '正常', value: 0 },
  { label: '停用', value: 1 }
];

// 状态转换函数
// const getStatusText = (status: number | string): string => {
//   const statusValue = typeof status === 'string' ? parseInt(status) : status;
//   switch (statusValue) {
//     case 0:
//       return '正常';
//     case 1:
//       return '停用';
//     case -1:
//       return '删除';
//     default:
//       return '未知';
//   }
// };

// 获取状态标签主题
// const getStatusTheme = (status: number | string): string => {
//   const statusValue = typeof status === 'string' ? parseInt(status) : status;
//   switch (statusValue) {
//     case 0:
//       return 'success';
//     case 1:
//       return 'warning';
//     case -1:
//       return 'danger';
//     default:
//       return 'default';
//   }
// };

// 教师选项
const teacherOptions = ref<DeanOption[]>([]);

// 对话框控制
const dialogVisible = ref(false);
const detailVisible = ref(false);
const detailLoading = ref(false);
const deanDialogVisible = ref(false);

// 表单数据
const formData = reactive({
  id: null,
  academyName: '',
  academyPresidentId: '',
  status: 0
});

// 设置院长表单数据
const deanFormData = reactive({
  id: null,
  academyPresidentId: ''
});

// 当前操作的学院
const currentCollege = ref<CollegeItem | null>(null);

// 学院详情
const collegeDetail = ref<any>(null);

// 表单验证规则
const rules = {
  academyName: [
    { required: true, message: '请输入学院名称', type: 'error' }
  ],
  status: [
    { required: true, message: '请选择状态', type: 'error' }
  ]
};

// 设置院长表单验证规则
const deanRules = {
  academyPresidentId: [
    { required: true, message: '请选择院长', type: 'error' }
  ]
};

// 计算属性
const dialogTitle = computed(() => {
  return formData.id ? '编辑学院' : '新增学院';
});

// 导入对话框控制
const importVisible = ref(false);

// 导入配置
const importConfig: ImportConfig = {
  title: '导入学院数据',
  tips: '请按照模板格式填写学院信息，支持批量导入。注意：学院名称为必填项，院长可留空后续设置',
  templateFileName: '学院信息导入模板.xlsx',
  templateData: [
    ['学院名称'],
    ['计算机科学与技术学院'],
    ['电子信息工程学院'],
    ['机械工程学院']
  ],
  acceptTypes: ['.xlsx', '.xls'],
  maxFileSize: 5
};
// 设置院长
const setDean = async (row: CollegeItem) => {
  currentCollege.value = row;
  Object.assign(deanFormData, {
    id: row.id,
    academyPresidentId: ''
  });
  // 确保打开对话框前教师选项是最新的
  await fetchTeacherOptions();
  deanDialogVisible.value = true;
};
// 导入回调函数
const importCallbacks: ImportCallbacks = {
  onImport: async (file: File) => {
    const formData = new FormData();
    formData.append('file', file);
    return await importAcademies(formData);
  },
  onSuccess: (result: any) => {
    fetchCollegeList(); // 刷新列表
  },
  onError: (error: Error) => {
    console.error('导入失败:', error);
  },
  onComplete: () => {
    // 导入完成后的处理
  }
};

// 获取学院列表
const fetchCollegeList = async () => {
  loading.value = true;
  try {
    const params = {
      current: pagination.current,
      pageSize: pagination.pageSize,
      academyName: searchFormData.academyName,
      academyPresidentId: searchFormData.academyPresidentId,
      status: searchFormData.status
    };
    console.log('查询学院列表参数:', params);
    const { data, msg} = await getAcademyList(params);
    console.log('查询学院列表结果:', data);
    if (data) {
      // 优先使用records字段（MyBatis Plus Page格式），其次使用list字段（Mock格式）
      tableData.value = data.records || [];
      pagination.total = data.total || 0;

      // 如果没有数据，给出友好提示
      if (tableData.value.length === 0) {
        proxy.$baseMessage('学院列表为空，请检查数据库中是否有学院数据','warning');
      }
    } else {
      console.error('API响应格式异常:', data);
      proxy.$baseMessage(msg || '获取学院列表失败','error');
      // 清空数据
      tableData.value = [];
      pagination.total = 0;
    }
  } catch (error) {
    console.error('获取学院列表失败:', error);
    proxy.$baseMessage('获取学院列表失败','error');
    // 清空数据
    tableData.value = [];
    pagination.total = 0;
  } finally {
    loading.value = false;
  }
};

// 获取教师选项
const fetchTeacherOptions = async () => {
  try {
    const response = await getDeanOptions();
    const options = response.data || [];
    // 前端排序：未担任院长的教师排在前面
    teacherOptions.value = options.sort((a: DeanOption, b: DeanOption) => {
      // 如果a没有担任院长，b担任了院长，a排在前面
      if (!a.currentAcademyId && b.currentAcademyId) return -1;
      // 如果a担任了院长，b没有担任院长，b排在前面
      if (a.currentAcademyId && !b.currentAcademyId) return 1;
      // 如果都担任或都不担任院长，按姓名排序
      return a.label.localeCompare(b.label);
    });
  } catch (error) {
    console.error('获取教师选项失败:', error);
  }
};

// 搜索
const handleSearch = () => {
  pagination.current = 1;
  fetchCollegeList();
};

// 重置搜索
const resetSearch = () => {
  Object.assign(searchFormData, {
    academyName: '',
    academyPresidentId: '',
    status: ''
  });
  pagination.current = 1;
  fetchCollegeList();
};

// 刷新数据
const refreshData = () => {
  fetchCollegeList();
  proxy.$baseMessage('数据已刷新','success');
};

// 打开导入对话框
const openImportDialog = () => {
  importVisible.value = true;
};

// 新增学院
const handleAdd = async () => {
  Object.assign(formData, {
    id: null,
    academyName: '',
    academyPresidentId: '',
    status: 0
  });
  // 确保打开对话框前教师选项是最新的
  await fetchTeacherOptions();
  dialogVisible.value = true;
};

// 编辑学院
const handleEdit = async (row: CollegeItem) => {
  Object.assign(formData, {
    id: row.id,
    academyName: row.name,
    academyPresidentId: row.academyPresidentId, // 需要从详情中获取
    status: row.status // 现在后端直接返回数值，无需转换
  });
  // 确保打开对话框前教师选项是最新的
  await fetchTeacherOptions();
  dialogVisible.value = true;
};

// 删除学院
const handleDelete = (row: CollegeItem) => {
  proxy.$baseConfirm(`确定要删除学院"${row.name}"吗？`, '删除确认', '确认删除', '取消',async () => {
    try {
      const {msg} = await deleteAcademy(row.id);
      proxy.$baseMessage(msg || '删除成功', 'success');
      await fetchCollegeList();
    } catch (error) {
      console.error('删除学院失败:', error);
    }
  })
};

// 提交表单
const handleSubmit = async () => {
  try {
    const submitData = {
      ...formData,
      status: formData.status
    };

    if (formData.id) {
      // 编辑模式：检查院长冲突
      if (formData.academyPresidentId) {
        const selectedTeacher = teacherOptions.value.find(option => option.value === formData.academyPresidentId);

        if (selectedTeacher?.currentAcademyName && Number(selectedTeacher.currentAcademyId) !== Number(formData.id)) {
          proxy.$baseConfirm(
            `确定要将${selectedTeacher.label}设置为${formData.academyName}的院长吗？\n\n确认后，${selectedTeacher.label}将不再担任${selectedTeacher.currentAcademyName}的院长。`,
            '院长职位冲突',
            '确认更新',
            '取消',
            async () => {
            await updateAcademy(submitData);
            dialogVisible.value = false;
            fetchCollegeList();
            // 重新获取教师选项以更新院长职位信息
            await fetchTeacherOptions();
          })
    } else {
      // 没有冲突，直接使用更新接口
      await updateAcademy(submitData);
      proxy.$baseMessage('更新成功','success');
      dialogVisible.value = false;
      fetchCollegeList();
    }
  } else {
    // 没有设置院长，直接使用更新接口
    await updateAcademy(submitData);
    proxy.$baseMessage('更新成功','success');
    dialogVisible.value = false;
    fetchCollegeList();
  }
} else {
  // 新增模式：直接添加
  await addAcademy(submitData);
      proxy.$baseMessage('新增成功', 'success');
      dialogVisible.value = false;
      fetchCollegeList();
    }
  } catch (error) {
    console.error('提交失败:', error);
  }
};

// 提交设置院长
const handleSetDean = async () => {
  try {
    // 检查选中的教师是否已经是其他学院的院长
    const selectedTeacher = teacherOptions.value.find(option => option.value === deanFormData.academyPresidentId);
    if (selectedTeacher?.currentAcademyName && Number(selectedTeacher.currentAcademyId) !== Number(deanFormData.id)) {
      proxy.$baseConfirm(
        `确定要将${selectedTeacher.label}设置为${formData.academyName}的院长吗？\n\n确认后，${selectedTeacher.label}将不再担任${selectedTeacher.currentAcademyName}的院长。`,
        '院长职位冲突',
        '确认设置',
        '取消',
        async () => {
          await performSetDean();
        })
    } else {
      // 没有冲突，直接设置
      await performSetDean();
    }
  } catch (error) {
    console.error('设置院长失败:', error);
  }
};

// 执行设置院长操作
const performSetDean = async () => {
  const submitData = {
    academyId: deanFormData.id,
    deanUserId: deanFormData.academyPresidentId
  };

  await setAcademyDean(submitData);
  proxy.$baseMessage('院长设置成功','success');
  deanDialogVisible.value = false;
  fetchCollegeList();
  // 重新获取教师选项以更新院长职位信息
  await fetchTeacherOptions();
};

// 查看详情
const viewDetail = async (row: CollegeItem) => {
  detailLoading.value = true;
  detailVisible.value = true;
  try {
    const {data} = await getAcademyDetail(row.id);
    console.log('获取学院详情结果:', data);
    collegeDetail.value = data;
  } catch (error) {
    console.error('获取详情失败:', error);
  } finally {
    detailLoading.value = false;
  }
};

// 切换状态
const toggleStatus = (row: CollegeItem) => {
  const action = row.status === 0 ? '停用' : '启用';
  proxy.$baseConfirm(`确定要${action}学院"${row.name}"吗？`, `${action}确认`, `确认${action}`, '取消', async () => {
    try {
      if (row.status === 0) {
        await disableAcademy(row.id);
      } else {
        // 启用操作，使用专门的启用接口
        await enableAcademy(row.id);
      }
      proxy.$baseMessage(`${action}成功`,"success");
      fetchCollegeList();
    } catch (error) {
      console.error(`${action}失败:`, error);
    }
  })
};

// 删除学院（已在上方定义，此处删除以避免重复声明）

// 导出
const handleExport = async () => {
  try {
   proxy.$baseMessage('正在导出...');
    const params = { ...searchFormData };
    const blob = await exportAcademies(params);

    if (!(blob instanceof Blob)) {
      throw new Error('导出失败，返回格式错误');
    }

    // 创建下载链接并触发下载
    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.setAttribute("href", url);
    link.setAttribute("download", `学院数据_${new Date().toLocaleDateString()}.xlsx`);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    proxy.$baseMessage('导出成功','success');
  } catch (error) {
    console.error('导出失败:', error);
    proxy.$baseMessage('导出失败','error');
  }
};

// 初始化
onMounted(async () => {
  await Promise.all([
    fetchCollegeList(),
    fetchTeacherOptions()
  ]);
});
</script>

<style scoped>
.college-management-container {
  padding: 0;
}

.search-container {
  margin-bottom: 16px;
  padding: 16px;
  background: var(--td-bg-color-container);
  border-radius: 6px;
}

.action-buttons {
  margin-bottom: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.operation-container {
  display: flex;
  gap: 8px;
  justify-content: center;
}

.dean-name {
  color: var(--td-brand-color);
  font-weight: 500;
}

.detail-container {
  padding: 16px 0;
}

/* 固定宽度样式 */
.fixed-width-input :deep(.t-input) {
  width: 180px !important;
}

.fixed-width-select :deep(.t-select) {
  width: 180px !important;
}

.fixed-width-input :deep(.t-input__inner) {
  width: 180px !important;
}

.fixed-width-select :deep(.t-select__single) {
  width: 180px !important;
}

/* 院长选项容器样式 */
.dean-option-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.teacher-name {
  flex: 1;
  margin-right: 8px;
  font-size: 14px;
  color: var(--td-text-color-primary);
}

.dean-badge {
  background-color: var(--td-brand-color-light);
  color: var(--td-brand-color);
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 12px;
  border: 1px solid var(--td-brand-color-focus);
  white-space: nowrap;
  font-weight: 500;
  transition: all 0.2s ease;
}

.dean-badge:hover {
  background-color: var(--td-brand-color-focus);
  transform: scale(1.05);
}

/* 选项容器hover效果 */
.dean-option-container:hover .dean-badge {
  background-color: var(--td-brand-color-focus);
}
</style>

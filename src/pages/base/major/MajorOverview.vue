<template>
  <div class="major-all-management-container">
    <t-card>
      <!-- 搜索区域 -->
      <div class="search-area">
        <t-form ref="searchForm" :data="searchFormData" layout="inline">
          <t-form-item label="专业名称">
            <t-input
              v-model="searchFormData.name"
              placeholder="请输入专业名称"
              clearable
              style="width: 200px"
            />
          </t-form-item>
          <t-form-item label="专业代码">
            <t-input
              v-model="searchFormData.code"
              placeholder="请输入专业代码"
              clearable
              style="width: 200px"
            />
          </t-form-item>
          <t-form-item label="所属学院">
            <t-select
              v-model="searchFormData.collegeId"
              placeholder="请选择学院"
              clearable
              :loading="collegeLoading"
              :options="collegeOptions"
              style="width: 200px"
            />
          </t-form-item>
          <t-form-item label="专业负责人">
            <t-select
              v-model="searchFormData.directorId"
              placeholder="请选择专业负责人"
              clearable
              filterable
              :options="leaderOptions"
              style="width: 200px"
            />
          </t-form-item>
          <t-form-item label="状态">
            <t-select
              v-model="searchFormData.status"
              placeholder="请选择状态"
              clearable
              :options="statusOptions"
              style="width: 200px"
            />
          </t-form-item>
          <t-form-item label="创建时间">
            <t-date-range-picker
              v-model="searchFormData.createTimeRange"
              placeholder="请选择创建时间范围"
              clearable
              style="width: 280px"
            />
          </t-form-item>
          <t-form-item>
            <t-space>
              <t-button theme="primary" @click="handleSearch">
                <template #icon><t-icon name="search" /></template>
                搜索
              </t-button>
              <t-button theme="default" @click="resetSearch">
                <template #icon><t-icon name="refresh" /></template>
                重置
              </t-button>

            </t-space>
          </t-form-item>
        </t-form>

        <!-- 搜索条件统计 -->
        <div v-if="searchConditionCount > 0" class="search-condition-count">
          <t-tag theme="primary" variant="light">
            已设置 {{ searchConditionCount }} 个搜索条件
          </t-tag>
        </div>
      </div>

      <!-- 操作按钮区域 -->
      <div class="operation-area">
        <t-space>
          <t-button theme="primary" @click="handleAdd">新增专业</t-button>
          <t-button theme="default" @click="handleImport">导入</t-button>
          <t-button theme="default" @click="handleExport">导出</t-button>
          <t-button theme="default" @click="handleColumnSetting">
            <template #icon><t-icon name="setting" /></template>
            列设置
          </t-button>
        </t-space>
      </div>

      <!-- 表格区域 -->
      <div class="table-header">
        <div class="table-info">
          <span class="total-count">共 {{ pagination.total }} 条记录</span>
          <span v-if="searchConditionCount > 0" class="search-result">
            （筛选结果）
          </span>
        </div>
      </div>

      <t-loading :loading="loading">
        <t-table
          :data="data"
          :columns="visibleColumns"
          row-key="id"
          hover
          stripe
          table-layout="fixed"
          :horizontal-scroll-bar="true"
          :max-height="'calc(100vh - 280px)'"
          :pagination="pagination"
        >
          <template #serial-number="{ rowIndex }">
            {{ (pagination.current - 1) * pagination.pageSize + rowIndex + 1 }}
          </template>
          <template #collegeName="{ row }">
            <t-tooltip :content="row.collegeName || '-'">
              <span class="ellipsis">{{ row.collegeName || '-' }}</span>
            </t-tooltip>
          </template>
          <template #status="{ row }">
            <t-tag
              :theme="row.status === 0 ? 'success' : row.status === 1 ? 'warning' : 'danger'"
              variant="light"
            >
              {{ enumData?.map?.majorStatus?.[row.status] || '未知' }}
            </t-tag>
          </template>
          <template #operation="slotProps">
            <div class="operation-container">
              <t-space>
                <t-button theme="primary" variant="text" @click="handleViewDetail(slotProps.row)">
                  <template #icon><t-icon name="browse" /></template>
                  详情
                </t-button>
                <t-button theme="primary" variant="text" @click="handleClassManage(slotProps.row)">
                  <template #icon><t-icon name="usergroup" /></template>
                  班级管理
                </t-button>
                <t-button theme="primary" variant="text" @click="handleEdit(slotProps.row)">
                  <template #icon><t-icon name="edit" /></template>
                  编辑
                </t-button>
                <t-button theme="danger" variant="text" @click="handleDelete(slotProps.row)">
                  <template #icon><t-icon name="delete" /></template>
                  删除
                </t-button>
              </t-space>
            </div>
          </template>
        </t-table>
      </t-loading>
    </t-card>

    <!-- 列设置对话框 -->
    <t-dialog
      v-model:visible="columnSettingVisible"
      header="列设置"
      :width="400"
      :footer="true"
      @close="columnSettingVisible = false"
    >
      <div class="column-setting-container">
        <div class="column-setting-tip">
          <t-icon name="info-circle" />
          <span>请选择要显示的列，带*号的列为必显示列</span>
        </div>
        <div class="column-list">
          <div
            v-for="config in columnConfig"
            :key="config.key"
            class="column-item"
          >
            <t-checkbox
              :checked="config.visible"
              :disabled="config.disabled"
              @change="(checked: boolean) => handleColumnChange(config.key, checked)"
            >
              {{ config.title }}{{ config.disabled ? ' *' : '' }}
            </t-checkbox>
          </div>
        </div>
      </div>
      <template #footer>
        <t-space>
          <t-button theme="primary" @click="columnSettingVisible = false">确定</t-button>
          <t-button theme="default" @click="resetColumnConfig">重置</t-button>
        </t-space>
      </template>
    </t-dialog>

    <!-- 新增/编辑对话框 -->
    <t-dialog
      v-model:visible="dialogVisible"
      :header="dialogTitle"
      :width="500"
      :footer="false"
      @close="dialogVisible = false"
    >
      <t-form
        ref="formRef"
        :data="formData"
        :rules="rules"
        label-width="100px"
        @submit="handleSubmit"
      >
        <t-form-item label="专业名称" name="name">
          <t-input v-model="formData.name" placeholder="请输入专业名称"></t-input>
        </t-form-item>

        <t-form-item label="专业代码" name="code">
          <t-input v-model="formData.code" placeholder="请输入专业代码"></t-input>
        </t-form-item>

        <t-form-item label="专业类型" name="discipline">
          <t-select
            v-model="formData.discipline"
            placeholder="请选择专业类型"
            clearable
            :options="majorTypeOptions"
          ></t-select>
        </t-form-item>

        <t-form-item label="所属学院" name="collegeId">
          <t-select
            v-model="formData.collegeId"
            placeholder="请选择所属学院"
            :loading="collegeLoading"
            :options="collegeOptions"
          ></t-select>
        </t-form-item>

        <t-form-item label="专业负责人" name="directorId">
          <t-select
            v-model="formData.directorId"
            placeholder="请选择专业负责人"
            :options="leaderOptions"
          ></t-select>
        </t-form-item>

        <t-form-item label="状态" name="status">
          <t-radio-group v-model="formData.status">
            <t-radio
              v-for="option in enumData?.list?.majorStatus"
              :key="option.value"
              :value="parseInt(option.value)"
            >
              {{ option.label }}
            </t-radio>
          </t-radio-group>
        </t-form-item>

        <t-form-item>
          <t-space>
            <t-button theme="primary" type="submit">确认</t-button>
            <t-button theme="default" variant="base" @click="dialogVisible = false">取消</t-button>
          </t-space>
        </t-form-item>
      </t-form>
    </t-dialog>

    <!-- 导入对话框 -->
    <ImportDialog
      v-model:visible="importVisible"
      :config="importConfig"
      :callbacks="importCallbacks"
    />

    <!-- 详情对话框 -->
    <t-dialog
      v-model:visible="detailDialogVisible"
      header="专业详情"
      :width="600"
      :footer="false"
      @close="detailDialogVisible = false"
    >
      <t-loading :loading="detailLoading">
        <div class="detail-container" v-if="detailData">
          <t-descriptions layout="vertical" size="large" bordered>
            <t-descriptions-item label="专业ID">{{ detailData.id }}</t-descriptions-item>
            <t-descriptions-item label="专业名称">{{ detailData.name }}</t-descriptions-item>
            <t-descriptions-item label="专业代码">{{ detailData.code }}</t-descriptions-item>
            <t-descriptions-item label="专业类型">
              {{ getMajorTypeLabel(detailData.discipline) }}
            </t-descriptions-item>
            <t-descriptions-item label="所属学院">{{ detailData.collegeName || '-' }}</t-descriptions-item>
            <t-descriptions-item label="专业负责人">{{ detailData.director || '-' }}</t-descriptions-item>
            <t-descriptions-item label="状态">
              <t-tag :theme="detailData.status === 0 ? 'success' : 'danger'" variant="light">
                {{ enumData?.map?.majorStatus?.[detailData.status] || '未知' }}
              </t-tag>
            </t-descriptions-item>
            <t-descriptions-item label="课程数量">{{ detailData.courses || 0 }}</t-descriptions-item>
            <t-descriptions-item label="班级数量">{{ detailData.classes || 0 }}</t-descriptions-item>
            <t-descriptions-item label="学生数量">{{ detailData.students || 0 }}</t-descriptions-item>
            <t-descriptions-item label="创建人">{{ detailData.creator || '-' }}</t-descriptions-item>
            <t-descriptions-item label="创建时间">{{ detailData.createTime ? formatDate(detailData.createTime, 'YYYY-MM-DD HH:mm') : '-' }}</t-descriptions-item>
            <t-descriptions-item label="更新人">{{ detailData.updater || '-' }}</t-descriptions-item>
            <t-descriptions-item label="更新时间">{{ detailData.updateTime ? formatDate(detailData.updateTime, 'YYYY-MM-DD HH:mm') : '-' }}</t-descriptions-item>
          </t-descriptions>
        </div>
        <t-empty v-else description="暂无详情数据" />
      </t-loading>
      <template #footer>
        <t-space>
          <t-button theme="default" @click="detailDialogVisible = false">关闭</t-button>
        </t-space>
      </template>
    </t-dialog>
  </div>
</template>

<script setup lang="ts">
import {ref, reactive, onMounted, computed, getCurrentInstance} from 'vue';
import { MessagePlugin, DialogPlugin } from 'tdesign-vue-next';
import { useRouter } from 'vue-router';
import { getDictOptionsByTypeTitle } from '@/utils/dictUtil';
import ImportDialog from '@/components/ImportDialog/index.vue';
import type { ImportConfig, ImportCallbacks } from '@/components/ImportDialog/types';
const { proxy } = getCurrentInstance();
import {
  getMajorList,
  MajorItem,
  addMajor,
  updateMajor,
  deleteMajor,
  importMajors,
  exportMajors,
  getMajorDetail,
  getAcademyLeaderOptions
} from '@/api/system/major';

import { getAcademyOptions } from '@/api/base/academy';
import { getEnum } from '@/api/system/enum';
import { formatDate } from '@/utils/date';

// 表格数据
const data = ref<MajorItem[]>([]);
const loading = ref(false);

// 学院数据
const collegeOptions = ref([]);
const collegeLoading = ref(false);

// 枚举数据
const enumData = ref<any>(null);

// 搜索条件
const searchFormData = reactive({
  name: '',
  code: '',
  collegeId: '',
  directorId: '',
  status: '',
  createTimeRange: []
});

// 计算属性：搜索条件计数
const searchConditionCount = computed(() => {
  let count = 0;
  if (searchFormData.name) count++;
  if (searchFormData.code) count++;
  if (searchFormData.collegeId) count++;
  if (searchFormData.directorId) count++;
  if (searchFormData.status !== '') count++;
  if (searchFormData.createTimeRange && searchFormData.createTimeRange.length > 0) count++;
  return count;
});

// 计算属性：状态选项
const statusOptions = computed(() => {
  return enumData.value?.list?.majorStatus?.map((item: any) => ({
    label: item.label,
    value: parseInt(item.value) // 转换为数字
  })) || [];
});

// 获取枚举值
const fetchEnumData = async () => {
  try {
    const res = await getEnum();
    enumData.value = res.data;
  } catch (error: any) {
    console.error('获取枚举值失败:', error);
  }
};

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  pageSizeOptions: [10, 20, 50],
  showJumper: true,
  showPageSize: true,
  onChange: (pageInfo: any) => {
    pagination.current = pageInfo.current;
    pagination.pageSize = pageInfo.pageSize;
    fetchMajorList();
  },
});

// 表格列配置
const columns = [
  { colKey: 'serial-number', title: '序号', width: 60 },
  //{ colKey: 'id', title: '专业ID', width: 80 },
  { colKey: 'name', title: '专业名称', width: 140 },
  { colKey: 'code', title: '专业代码', width: 120 },
  {
    colKey: 'discipline',
    title: '专业类型',
    width: 120,
    cell: (h: any, { row }: any) => {
      const typeOption = majorTypeOptions.value.find(option => option.value == row.discipline);
      return typeOption ? typeOption.label : row.discipline || '-';
    }
  },
  { colKey: 'collegeName', title: '所属学院', width: 140 },
  { colKey: 'director', title: '专业负责人', width: 120 },
  { colKey: 'courses', title: '课程数', width: 80 },
  { colKey: 'classes', title: '班级数', width: 80 },
  { colKey: 'students', title: '学生数', width: 80 },
  { colKey: 'status', title: '状态', width: 100 },
  {
    colKey: 'createTime',
    title: '创建时间',
    width: 140,
    cell: (h: any, params: any) => params.row.createTime ? formatDate(params.row.createTime, 'YYYY-MM-DD HH:mm') : '-'
  },
  {
    colKey: 'updateTime',
    title: '更新时间',
    width: 140,
    cell: (h: any, params: any) => params.row.updateTime ? formatDate(params.row.updateTime, 'YYYY-MM-DD HH:mm') : '-'
  },
  { colKey: 'operation', title: '操作', width: 320, align: 'center' as const },
];

// 专业负责人选项
const leaderOptions = ref<{ label: string; value: string | number }[]>([]);

// 专业类型选项
const majorTypeOptions = ref<{ label: string; value: string | number }[]>([]);

// 获取专业类型标签
const getMajorTypeLabel = (value: string | number) => {
  if (!value) return '-';
  const typeOption = majorTypeOptions.value.find(option => option.value == value);
  return typeOption ? typeOption.label : value;
};

// 表单数据
const formData = reactive({
  id: '',
  name: '',
  code: '',
  discipline: '',
  collegeId: '',
  directorId: '',
  status: 0, // 使用数字状态值
});

// 表单校验规则
const rules = {
  name: [
    { required: true, message: '请输入专业名称', trigger: 'blur' as const },
    { min: 2, message: '专业名称不能少于2个字符', trigger: 'blur' as const },
    { max: 50, message: '专业名称不能超过50个字符', trigger: 'blur' as const },
    {
      pattern: /^[\u4e00-\u9fa5a-zA-Z0-9\s\-（）()]+$/,
      message: '专业名称只能包含中文、英文、数字、空格、连字符和括号',
      trigger: 'blur' as const
    }
  ],
  code: [
    { required: true, message: '请输入专业代码', trigger: 'blur' as const },
    { min: 2, message: '专业代码不能少于2个字符', trigger: 'blur' as const },
    { max: 20, message: '专业代码不能超过20个字符', trigger: 'blur' as const },
    {
      pattern: /^[A-Z0-9\-]+$/,
      message: '专业代码只能包含大写字母、数字和连字符',
      trigger: 'blur' as const
    }
  ],
  discipline: [
    { required: true, message: '请选择专业类型', trigger: 'change' as const },
  ],
  collegeId: [
    { required: true, message: '请选择所属学院', trigger: 'change' as const }
  ],
  directorId: [
    { required: true, message: '请选择专业负责人', trigger: 'change' as const }
  ],
  status: [
    { required: true, message: '请选择状态', trigger: 'change' as const }
  ],
};

// 表单引用
const formRef = ref(null);

// 对话框控制
const dialogVisible = ref(false);
const dialogTitle = ref('新增专业');

// 导入对话框控制
const importVisible = ref(false);

// 导入配置
const importConfig: ImportConfig = {
  title: '导入专业数据',
  tips: '请按照模板格式填写专业信息，支持批量导入。注意：专业名称和专业代码为必填项，学院名称需要与系统中已有学院匹配',
  templateFileName: '专业信息导入模板.xlsx',
  templateData: [
    ['专业名称', '专业代码', '专业类型', '学院名称', '专业负责人工号', '专业负责人姓名', '专业概述'],
    ['计算机科学与技术', 'CS001', 'XXX', '计算机学院', 'T001', '张教授', '培养计算机科学与技术专业人才'],
    ['软件工程', 'SE001', 'XXX', '计算机学院', 'T002', '李教授', '培养软件工程专业人才'],
    ['电子信息工程', 'EE001', 'XXX', '电子学院', 'T003', '王教授', '培养电子信息工程专业人才']
  ],
  acceptTypes: ['.xlsx', '.xls'],
  maxFileSize: 5
};

// 导入回调函数
const importCallbacks: ImportCallbacks = {
  onImport: async (file: File) => {
    const formData = new FormData();
    formData.append('file', file);
    return await importMajors(formData);
  },
  onSuccess: (result: any) => {
    fetchMajorList(); // 刷新列表
  },
  onError: (error: Error) => {
    console.error('导入失败:', error);
  },
  onComplete: () => {
    // 导入完成后的处理
  }
};

// 详情对话框控制
const detailDialogVisible = ref(false);
const detailLoading = ref(false);
const detailData = ref<MajorItem | null>(null);

// 列设置控制
const columnSettingVisible = ref(false);

// 列配置数据 - 确保初始状态与期望显示一致
const columnConfig = ref([
  { key: 'serial-number', title: '序号', visible: true, disabled: true },
  { key: 'id', title: '专业ID', visible: true, disabled: false },
  { key: 'name', title: '专业名称', visible: true, disabled: true },
  { key: 'code', title: '专业代码', visible: true, disabled: true },
  { key: 'discipline', title: '专业类型', visible: true, disabled: false },
  { key: 'collegeName', title: '所属学院', visible: true, disabled: false },
  { key: 'director', title: '专业负责人', visible: true, disabled: false },
  { key: 'courses', title: '课程数', visible: true, disabled: false },
  { key: 'classes', title: '班级数', visible: true, disabled: false },
  { key: 'students', title: '学生数', visible: true, disabled: false },
  { key: 'status', title: '状态', visible: true, disabled: false },
  { key: 'createTime', title: '创建时间', visible: true, disabled: false },
  { key: 'updateTime', title: '更新时间', visible: true, disabled: false }, // 将在initializeColumnConfig中设置为false
  { key: 'operation', title: '操作', visible: true, disabled: true }
]);

// 计算属性：可见的列配置
const visibleColumns = computed(() => {
  return columns.filter(column => {
    const config = columnConfig.value.find(c => c.key === column.colKey);
    return config ? config.visible : true;
  });
});



const router = useRouter();

// 获取学院列表
const fetchCollegeList = async () => {
  collegeLoading.value = true;
  try {
    // 直接使用getCollegeOptions获取格式化的学院选项列表
    const res = await getAcademyOptions();
    collegeOptions.value = res.data;
  } catch (error: any) {
    console.error('获取学院列表错误:', error);
  } finally {
    collegeLoading.value = false;
  }
};

// 获取专业列表
const fetchMajorList = async () => {
  loading.value = true;
  try {
    const params: any = {
      current: pagination.current,
      pageSize: pagination.pageSize,
      majorName: searchFormData.name || undefined,
      majorCode: searchFormData.code || undefined,
      academyId: searchFormData.collegeId || undefined,
      academyLeaderId: searchFormData.directorId || undefined,
      status: searchFormData.status !== '' ? searchFormData.status : undefined
    };

    // 处理创建时间范围
    if (searchFormData.createTimeRange && searchFormData.createTimeRange.length === 2) {
      params.createTimeStart = searchFormData.createTimeRange[0];
      params.createTimeEnd = searchFormData.createTimeRange[1];
    }

    const result = await getMajorList(params);
    data.value = result.data.records || [];
    pagination.total = result.data.total || 0;
  } catch (error: any) {
    console.error('获取专业列表错误:', error);
  } finally {
    loading.value = false;
  }
};

// 处理搜索
const handleSearch = () => {
  loading.value = true;
  pagination.current = 1; // 重置到第一页
  try {
    fetchMajorList();
  } catch (error) {
    console.error('搜索失败:', error);
  }
};

// 重置搜索
const resetSearch = () => {
  loading.value = true;
  // 清空搜索条件
  Object.assign(searchFormData, {
    name: '',
    code: '',
    collegeId: '',
    directorId: '',
    status: '',
    createTimeRange: []
  });
  pagination.current = 1;
  try {
    fetchMajorList();
    proxy.$baseMessage('重置成功', 'success');
  } catch (error) {
    console.error('重置失败:', error);
  }
};

// 打开新增对话框
const handleAdd = async () => {
  dialogTitle.value = '新增专业';

  // 确保专业类型数据已加载


  if (majorTypeOptions.value.length === 0) {

    try {
      majorTypeOptions.value = await getDictOptionsByTypeTitle('专业类型');
      if (majorTypeOptions.value.length === 0) {
        // 使用默认数据
        majorTypeOptions.value = [
          { label: '工学', value: '1' },
          { label: '理学', value: '2' },
          { label: '文学', value: '3' },
          { label: '管理学', value: '4' },
          { label: '经济学', value: '5' }
        ];
      }
    } catch (error) {
      console.error('重新加载专业类型数据失败:', error);
      majorTypeOptions.value = [
        { label: '工学', value: '1' },
        { label: '理学', value: '2' },
        { label: '文学', value: '3' },
        { label: '管理学', value: '4' },
        { label: '经济学', value: '5' }
      ];
    }
  }

  dialogVisible.value = true;

  // 初始化表单数据
  formData.id = '';
  formData.name = '';
  formData.code = '';
  formData.discipline = ''; // 重置专业类型
  formData.collegeId = '';
  formData.directorId = '';
  formData.status = 0;

  console.log('对话框打开后，majorTypeOptions:', majorTypeOptions.value);
};

// 打开编辑对话框
const handleEdit = (row: MajorItem) => {
  dialogTitle.value = '编辑专业';
  dialogVisible.value = true;

    // 设置表单数据
  formData.id = row.id as string;
  formData.name = row.name;
  formData.code = row.code;
  formData.discipline = row.discipline;
  formData.collegeId = (row.collegeId || '') as string;
  formData.directorId = (row.directorId || '') as string;
  formData.status = row.status;
};

// 切换专业状态
const handleToggleStatus = (row: MajorItem) => {
  const newStatus = row.status === 0 ? 1 : 0;
  const statusText = newStatus === 0 ? '启用' : '停用';

  const confirmDialog = DialogPlugin.confirm({
    header: `${statusText}确认`,
    body: `确定要${statusText}专业"${row.name}"吗？`,
    confirmBtn: `确认${statusText}`,
    cancelBtn: '取消',
    onConfirm: async () => {
      try {
        // 构造更新参数
        const params = {
          majorName: row.name,
          majorCode: row.code,
          academyId: row.collegeId || null,
          academyLeaderId: row.directorId || null,
          status: newStatus
        };

        const res = await updateMajor({
          id: row.id,
          ...params
        });
        if (res.code === 200) {
          MessagePlugin.success(`${statusText}成功`);
          fetchMajorList();
        } else {
          MessagePlugin.error(res.message || `${statusText}失败`);
        }
      } catch (error: any) {
        console.error(`${statusText}失败:`, error);
        MessagePlugin.error(error.message || `${statusText}失败`);
      } finally {
        confirmDialog.hide();
      }
    }
  });
};

// 删除专业
const handleDelete = (row: MajorItem) => {
  proxy.$baseConfirm(`确定要删除专业"${row.name}"吗？`,
    '删除确认',
    '确认删除',
    '取消',async () => {
      try {
        const res = await deleteMajor(row.id);
        proxy.$baseMessage(res.message || '删除成功','success');
        fetchMajorList();
      } catch (error: any) {
        console.error('删除专业失败:', error);
      }
    });
};

// 提交表单
const handleSubmit = async (context: any) => {
  if (context.validateResult === true) {
    // 显示确认弹窗
    const operation = !formData.id ? '新增' : '更新';
    proxy.$baseConfirm(`确定要${operation}专业"${formData.name}"吗？`,
      `${operation}确认`,
      `确认${operation}`,
      '取消', async () => {
      // 显示加载状态
      const load = proxy.$baseLoading('正在保存专业信息...');

      try {
        const params = {
          majorName: formData.name,
          majorCode: formData.code,
          academyId: formData.collegeId,
          academyLeaderId: formData.directorId,
          discipline: formData.discipline,
          status: formData.status
        };

        let res;
        if (!formData.id) {
          // 新增专业
          console.log('准备添加专业:', params);
          res = await addMajor(params);
        } else {
          // 更新专业
          res = await updateMajor({
            ...params,
            id: formData.id
          });
        }

        proxy.$baseMessage(res.message || (formData.id ? '更新成功' : '添加成功'), "success");
        dialogVisible.value = false;
        // 重置表单数据
        Object.assign(formData, {
          id: '',
          name: '',
          code: '',
          discipline: '',
          collegeId: '',
          directorId: '',
          status: 0
        });
        // 刷新列表
        await fetchMajorList();
        dialogVisible.value = false;
      } catch (error: any) {
        console.error('保存专业失败:', error);
        // 根据错误类型提供更具体的错误信息
      } finally {
        load.close()
      }
    });
  } else {
    // 显示第一个验证错误
    const firstError = context.firstError || '表单验证失败，请检查输入内容';
    proxy.$baseMessage(firstError, 'warning');
  }
};

// 导入相关功能
const handleImport = () => {
  importVisible.value = true;
};



// 导出专业
const handleExport = async () => {
  try {
    const params = {
      majorName: searchFormData.name || undefined,
      majorCode: searchFormData.code || undefined,
      academyId: searchFormData.collegeId || undefined,
      academyLeaderId: searchFormData.directorId || undefined,
      status: searchFormData.status,
      createTimeRange: searchFormData.createTimeRange,
      fileType: 'xlsx' // 默认导出为Excel格式
    };

    const res = await exportMajors(params);

    if (res instanceof Blob) {
      // 创建下载链接
      const link = document.createElement('a');
      const url = URL.createObjectURL(res);
      link.href = url;
      link.download = `专业列表_${new Date().toISOString().split('T')[0]}.xlsx`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      proxy.$baseMessage('导出成功','success');
    } else {
      proxy.$baseMessage('导出失败，返回数据格式错误','error');
    }
  } catch (error: any) {
    console.error('导出失败:', error);
  }
};

// 查看专业详情
const handleViewDetail = async (row: MajorItem) => {
  detailLoading.value = true;
  detailDialogVisible.value = true;

  try {
    const res = await getMajorDetail(row.id);
    detailData.value = res.data;
  } catch (error: any) {
    console.error('获取专业详情失败:', error);
  } finally {
    detailLoading.value = false;
  }
};

// 查看班级管理
const handleClassManage = (row: MajorItem) => {
  // 添加防护性检查
  if (!row || !row.id) {
    proxy.$baseMessage('专业信息不完整，无法进入班级管理','error');
    return;
  }
  console.log('跳转到班级管理，专业ID:', row.id, '专业名称:', row.name);
  try {
    router.push({
      name: 'BaseClassManagement',
      params: { majorId: row.id },
      query: { majorName: row.name || '未知专业' }
    });
  } catch (error) {
    console.error('路由跳转失败:', error);
    proxy.$baseMessage('页面跳转失败','error');
  }
};

// 列设置相关方法
const handleColumnSetting = () => {
  columnSettingVisible.value = true;
};

const handleColumnChange = (key: string, visible: boolean) => {
  const config = columnConfig.value.find(c => c.key === key);
  if (config && !config.disabled) {
    config.visible = visible;
  }
};

const resetColumnConfig = () => {
  columnConfig.value.forEach(config => {
    config.visible = true;
  });
  proxy.$baseMessage('列设置已重置','success');
};

// 初始化列配置，确保与实际显示一致
const initializeColumnConfig = () => {
  // 如果希望某些列默认隐藏，可以在这里设置
  const defaultHiddenColumns = ['updateTime']; // 默认隐藏更新时间列

  columnConfig.value.forEach(config => {
    if (defaultHiddenColumns.includes(config.key)) {
      config.visible = false;
    }
  });
};

// 初始化
onMounted(async () => {
  // 初始化列配置
  initializeColumnConfig();

  // 加载专业负责人选项
  const res = await getAcademyLeaderOptions();
  leaderOptions.value = res.data || [];

  // 加载专业类型字典数据
  try {
    majorTypeOptions.value = await getDictOptionsByTypeTitle('专业类型');

    // 如果没有数据，添加一些测试数据
    if (majorTypeOptions.value.length === 0) {
      console.warn('字典中没有专业类型数据，使用默认数据');
      majorTypeOptions.value = [
        { label: '工学', value: '1' },
        { label: '理学', value: '2' },
        { label: '文学', value: '3' },
        { label: '管理学', value: '4' },
        { label: '经济学', value: '5' }
      ];
    }
  } catch (error) {
    console.error('加载专业类型字典数据失败:', error);
    MessagePlugin.error('加载专业类型数据失败，使用默认数据');

    // 加载失败时使用默认数据
    majorTypeOptions.value = [
      { label: '工学', value: '1' },
      { label: '理学', value: '2' },
      { label: '文学', value: '3' },
      { label: '管理学', value: '4' },
      { label: '经济学', value: '5' }
    ];
  }

  // 加载枚举值
  await fetchEnumData();

  // 加载学院列表
  await fetchCollegeList();

  // 加载专业列表
  fetchMajorList();
});
</script>

<style lang="less" scoped>
.major-all-management-container {
  padding: 20px;
}

.search-area {
  margin-bottom: 20px;

  .search-condition-count {
    margin-top: 12px;
  }
}

.operation-area {
  margin-bottom: 20px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;

  .table-info {
    display: flex;
    align-items: center;
    gap: 8px;

    .total-count {
      font-size: 14px;
      color: rgba(0, 0, 0, 0.6);
    }

    .search-result {
      font-size: 12px;
      color: var(--td-brand-color);
    }
  }
}



.detail-container {
  .t-descriptions {
    :deep(.t-descriptions-item__label) {
      font-weight: 500;
    }
  }
}

.ellipsis {
  display: inline-block;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.operation-container {
  display: flex;
  justify-content: center;

  // Vben风格的按钮样式
  :deep(.t-button) {
    margin: 0 2px;
    border-radius: 4px;
    padding: 0 6px;
    height: 28px;
    font-size: 12px;
    font-weight: 500;

    // 确保图标和文字间距合适
    .t-icon {
      margin-right: 2px;
      font-size: 14px;
    }

    // 提高按钮颜色的饱和度
    &.t-button--theme-primary {
      background-color: rgba(33, 150, 243, 0.1);
      border-color: #1890ff;
      color: #1890ff;

      &:hover {
        background-color: rgba(33, 150, 243, 0.2);
        border-color: #40a9ff;
        color: #40a9ff;
      }
    }

    &.t-button--theme-danger {
      background-color: rgba(244, 67, 54, 0.1);
      border-color: #f5222d;
      color: #f5222d;

      &:hover {
        background-color: rgba(244, 67, 54, 0.2);
        border-color: #ff4d4f;
        color: #ff4d4f;
      }
    }

    &.t-button--theme-warning {
      background-color: rgba(255, 152, 0, 0.1);
      border-color: #fa8c16;
      color: #fa8c16;

      &:hover {
        background-color: rgba(255, 152, 0, 0.2);
        border-color: #ffa940;
        color: #ffa940;
      }
    }

    &.t-button--theme-success {
      background-color: rgba(76, 175, 80, 0.1);
      border-color: #52c41a;
      color: #52c41a;

      &:hover {
        background-color: rgba(76, 175, 80, 0.2);
        border-color: #73d13d;
        color: #73d13d;
      }
    }
  }

  // 调整t-space组件内部间距
  :deep(.t-space) {
    gap: 4px !important;
  }
}

/* 列设置样式 */
.column-setting-container {
  .column-setting-tip {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 16px;
    padding: 8px;
    background-color: #f3f9ff;
    border-radius: 4px;
    color: #1890ff;
    font-size: 12px;
  }

  .column-list {
    display: flex;
    flex-wrap: wrap;
    gap: 12px 16px;
    max-height: 300px;
    overflow-y: auto;

    .column-item {
      flex: 0 0 auto;
      min-width: 120px;

      :deep(.t-checkbox) {
        .t-checkbox__label {
          font-size: 14px;
          white-space: nowrap;
        }

        &.t-is-disabled .t-checkbox__label {
          color: #1890ff;
          font-weight: 500;
        }
      }
    }
  }
}
</style>

<template>
  <div class="subject-home-container">
    <t-card title="课题负责人工作台" subtitle="欢迎使用课题管理系统" class="welcome-card">
      <template #actions>
        <t-button theme="primary">管理课题</t-button>
        <t-button>查看报告</t-button>
      </template>
      <div class="welcome-content">
        <div class="welcome-header">
          <t-avatar size="large">{{ userName.substring(0, 1) }}</t-avatar>
          <div class="welcome-info">
            <h3>{{ userName }}，您好！</h3>
            <p>今天是 {{ currentDate }}，{{ getWelcomeText() }}</p>
          </div>
        </div>
      </div>
    </t-card>

    <div class="dashboard-section">
      <t-row :gutter="16">
        <t-col :span="6">
          <t-card title="当前课题" hover-shadow>
            <div class="stat-card">
              <div class="stat-number">6</div>
              <div class="stat-text">个进行中</div>
            </div>
          </t-card>
        </t-col>
        <t-col :span="6">
          <t-card title="团队成员" hover-shadow>
            <div class="stat-card">
              <div class="stat-number">12</div>
              <div class="stat-text">名成员</div>
            </div>
          </t-card>
        </t-col>
        <t-col :span="6">
          <t-card title="待审阅报告" hover-shadow>
            <div class="stat-card">
              <div class="stat-number">8</div>
              <div class="stat-text">份待处理</div>
            </div>
          </t-card>
        </t-col>
        <t-col :span="6">
          <t-card title="近期截止" hover-shadow>
            <div class="stat-card">
              <div class="stat-number">3</div>
              <div class="stat-text">个即将截止</div>
            </div>
          </t-card>
        </t-col>
      </t-row>
    </div>

    <t-row :gutter="16" class="project-section">
      <t-col :span="16">
        <t-card title="课题进展" subtitle="近期课题进展情况">
          <t-table
            :data="projectData"
            :columns="projectColumns"
            :pagination="{ pageSize: 5 }"
            stripe
            hover
            row-key="id"
          >
            <template #status="{ row }">
              <t-tag :theme="getStatusTheme(row.status)" variant="light">
                {{ row.status }}
              </t-tag>
            </template>
            <template #progress="{ row }">
              <t-progress :percentage="row.progress" :status="getProgressStatus(row.progress)" />
            </template>
          </t-table>
        </t-card>
      </t-col>
      <t-col :span="8">
        <t-card title="待办事项" subtitle="近期待办">
          <t-list>
            <t-list-item v-for="(todo, index) in todoList" :key="index">
              <div class="todo-item">
                <t-checkbox :checked="todo.completed" @change="toggleTodo(index)">
                  {{ todo.content }}
                </t-checkbox>
                <span class="todo-date">{{ todo.date }}</span>
              </div>
            </t-list-item>
          </t-list>
        </t-card>
      </t-col>
    </t-row>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useUserStore } from '@/store';

const userStore = useUserStore();
const userName = computed(() => userStore.userInfo?.name || '用户');

// 当前日期
const currentDate = computed(() => {
  const now = new Date();
  return `${now.getFullYear()}年${now.getMonth() + 1}月${now.getDate()}日`;
});

// 根据时间生成欢迎语
const getWelcomeText = () => {
  const hour = new Date().getHours();
  if (hour < 6) return '夜深了，注意休息！';
  if (hour < 9) return '早上好，新的一天开始了！';
  if (hour < 12) return '上午好，工作顺利！';
  if (hour < 14) return '中午好，别忘了休息！';
  if (hour < 18) return '下午好，继续加油！';
  if (hour < 22) return '晚上好，今天辛苦了！';
  return '夜深了，注意休息！';
};

// 项目数据
const projectData = ref([
  { id: 1, name: '智能教育系统研发', leader: '张明', status: '进行中', progress: 75, deadline: '2023-06-30' },
  { id: 2, name: '大数据分析平台', leader: '李华', status: '计划中', progress: 30, deadline: '2023-07-15' },
  { id: 3, name: 'AR教学应用开发', leader: '王刚', status: '进行中', progress: 60, deadline: '2023-06-20' },
  { id: 4, name: '教育资源整合系统', leader: '赵静', status: '已完成', progress: 100, deadline: '2023-05-30' },
  { id: 5, name: '学生行为分析工具', leader: '刘芳', status: '进行中', progress: 45, deadline: '2023-08-10' },
  { id: 6, name: '在线考试系统升级', leader: '陈明', status: '已延期', progress: 20, deadline: '2023-05-15' },
]);

// 项目表格列定义
const projectColumns = [
  { colKey: 'name', title: '课题名称', width: 200 },
  { colKey: 'leader', title: '负责人' },
  { colKey: 'status', title: '状态', cell: 'status' },
  { colKey: 'progress', title: '进度', cell: 'progress' },
  { colKey: 'deadline', title: '截止日期' },
];

// 获取状态对应的主题色
const getStatusTheme = (status: string) => {
  const themeMap: Record<string, string> = {
    '进行中': 'primary',
    '计划中': 'warning',
    '已完成': 'success',
    '已延期': 'danger',
  };
  return themeMap[status] || 'default';
};

// 获取进度状态
const getProgressStatus = (progress: number) => {
  if (progress >= 100) return 'success';
  if (progress > 60) return 'normal';
  if (progress > 30) return 'warning';
  return 'error';
};

// 待办事项
const todoList = ref([
  { content: '提交《智能教育系统》第三阶段报告', completed: false, date: '今天' },
  { content: '审核团队成员提交的研发计划', completed: true, date: '今天' },
  { content: '参加教育部课题汇报会', completed: false, date: '明天' },
  { content: '准备月度教研会议材料', completed: false, date: '3天后' },
  { content: '与合作方交流项目对接事宜', completed: false, date: '下周一' },
]);

// 切换待办完成状态
const toggleTodo = (index: number) => {
  todoList.value[index].completed = !todoList.value[index].completed;
};

onMounted(() => {
  console.log('课题负责人首页加载完成');
});
</script>

<style lang="less" scoped>
.subject-home-container {
  padding: 20px;

  .welcome-card {
    margin-bottom: 20px;

    .welcome-content {
      padding: 10px 0;
    }

    .welcome-header {
      display: flex;
      align-items: center;
      margin-bottom: 20px;

      .welcome-info {
        margin-left: 16px;

        h3 {
          margin: 0;
          font-weight: 500;
          font-size: 18px;
        }

        p {
          margin: 8px 0 0;
          color: var(--td-text-color-secondary);
        }
      }
    }
  }

  .dashboard-section {
    margin-bottom: 20px;

    .stat-card {
      text-align: center;
      padding: 10px 0;

      .stat-number {
        font-size: 36px;
        font-weight: 500;
        color: var(--td-brand-color);
      }

      .stat-text {
        color: var(--td-text-color-secondary);
        margin-top: 8px;
      }
    }
  }

  .project-section {
    .todo-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 100%;

      .todo-date {
        font-size: 12px;
        color: var(--td-text-color-secondary);
      }
    }
  }
}
</style> 
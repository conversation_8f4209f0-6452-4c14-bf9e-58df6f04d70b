<template>
  <div class="class-detail-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <t-card :bordered="false">
        <template #title>
          <div class="header-wrapper">
            <div class="back-button">
              <t-button theme="default" variant="text" @click="goBack">
                <template #icon><t-icon name="chevron-left" /></template>
                返回班级列表
              </t-button>
            </div>
            <div class="title-container">
              <h2 class="page-title">{{ className }} 达成度分析</h2>
              <span class="subtitle">{{ majorName }}</span>
            </div>
          </div>
        </template>
      </t-card>
    </div>

    <!-- 班级基本信息卡片 -->
    <t-row :gutter="[16, 16]" class="card-row">
      <t-col :span="3">
        <t-card :bordered="false" class="stat-card students-count" hover-shadow>
          <t-space direction="vertical" align="center">
            <t-icon name="user-circle" size="28px" />
            <div class="stat-title">学生总数</div>
            <div class="stat-value">{{ classInfo.studentCount }}</div>
          </t-space>
        </t-card>
      </t-col>
      <t-col :span="3">
        <t-card :bordered="false" class="stat-card teacher-name" hover-shadow>
          <t-space direction="vertical" align="center">
            <t-icon name="user" size="28px" />
            <div class="stat-title">班主任</div>
            <div class="stat-value">{{ classInfo.teacherName }}</div>
          </t-space>
        </t-card>
      </t-col>
      <t-col :span="3">
        <t-card :bordered="false" class="stat-card pass-rate" hover-shadow>
          <t-space direction="vertical" align="center">
            <t-icon name="chart-pie" size="28px" />
            <div class="stat-title">平均及格率</div>
            <div class="stat-value">{{ classInfo.avgPassRate }}%</div>
          </t-space>
        </t-card>
      </t-col>
      <t-col :span="3">
        <t-card :bordered="false" class="stat-card achieve-rate" hover-shadow>
          <t-space direction="vertical" align="center">
            <t-icon name="check-circle" size="28px" />
            <div class="stat-title">目标达成度</div>
            <div class="stat-value">{{ classInfo.avgAchieveRate }}%</div>
          </t-space>
        </t-card>
      </t-col>
    </t-row>

    <!-- 图表区域 -->
    <t-row :gutter="[16, 16]" class="chart-row">
      <!-- 达成度雷达图 -->
      <t-col :span="6">
        <t-card :bordered="false" hover-shadow class="chart-card goal-chart">
          <template #title>
            <div class="card-title-with-select">
              <span>课程目标达成度</span>
            </div>
          </template>
          <template #actions>
            <t-select v-model="selectedCourse" size="small" style="width: 180px" placeholder="选择课程" @change="updateRadarChart">
              <t-option v-for="course in coursesData" :key="course.id" :value="course.id" :label="course.name" />
            </t-select>
          </template>
          <div ref="radarChart" class="chart-container"></div>
        </t-card>
      </t-col>
      <!-- 分段统计 -->
      <t-col :span="6">
        <t-card :bordered="false" title="分段达成度统计" hover-shadow class="chart-card segment-chart">
          <div ref="pieChart" class="chart-container"></div>
        </t-card>
      </t-col>
      <!-- 毕业要求达成度 -->
      <t-col :span="12">
        <t-card :bordered="false" title="毕业要求达成度" hover-shadow class="chart-card">
          <div ref="barChart" class="chart-container"></div>
        </t-card>
      </t-col>
    </t-row>

    <t-row :gutter="[16, 16]" class="chart-row">
      <!-- 学生个体达成度散点图 -->
      <t-col :span="12">
        <t-card :bordered="false" hover-shadow class="chart-card">
          <template #title>
            <div class="card-title-with-select">
              <span>学生个体达成度分布</span>
            </div>
          </template>
          <template #actions>
            <t-select v-model="selectedGoal" size="small" style="width: 180px" placeholder="选择课程目标" @change="updateScatterChart">
              <t-option v-for="goal in courseGoals" :key="goal.id" :value="goal.id" :label="goal.name" />
            </t-select>
          </template>
          <div ref="scatterChart" class="chart-container"></div>
        </t-card>
      </t-col>


    </t-row>

    <!-- 课程列表 -->
    <t-card :bordered="false" title="课程达成度列表" hover-shadow class="course-card">
      <t-table
        :data="coursesData"
        :columns="columns"
        :row-key="rowKey"
        stripe
        bordered
        hover
      >
        <template #achieveRate="{ row }">
          <t-progress
            :percentage="row.achieveRate"
            :color="getProgressColor(row.achieveRate)"
            :label="true"
          />
        </template>
        <template #status="{ row }">
          <t-tag :theme="row.status === '已完成' ? 'success' : 'warning'" variant="light">
            {{ row.status }}
          </t-tag>
        </template>
      </t-table>
    </t-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import * as echarts from 'echarts';
import { MessagePlugin } from 'tdesign-vue-next';

// 路由相关
const route = useRoute();
const router = useRouter();
const majorId = route.params.majorId as string;
const classId = route.params.classId as string;
const className = computed(() => route.query.className as string || '未知班级');
const majorName = computed(() => route.query.majorName as string || '未知专业');

// 返回班级列表
const goBack = () => {
  router.push({
    path: '/base/classes',
    query: {
      majorId: majorId.value,
      majorName: majorName.value
    }
  });
};

// 表格相关
const rowKey = 'id';

// 图表引用
const radarChart = ref(null);
const barChart = ref(null);
const scatterChart = ref(null);
const pieChart = ref(null);

// 班级基本信息
const classInfo = reactive({
  id: classId,
  name: className,
  teacherName: '张老师',
  studentCount: 45,
  avgPassRate: 88,
  avgAchieveRate: 85
});

// 课程目标选择
const courseGoals = ref([
  { id: '1', name: '目标1: 掌握基本理论' },
  { id: '2', name: '目标2: 运用解决问题' },
  { id: '3', name: '目标3: 数据分析能力' },
  { id: '4', name: '目标4: 逻辑推理能力' },
  { id: '5', name: '目标5: 编程实现能力' }
]);
const selectedGoal = ref('1');

// 课程表格数据
interface CourseItem {
  id: string;
  name: string;
  teacher: string;
  achieveRate: number;
  status: string;
  semester: string;
}

// 表格列配置
const columns = [
  { colKey: 'name', title: '课程名称', width: 200 },
  { colKey: 'teacher', title: '任课教师', width: 120 },
  { colKey: 'semester', title: '学期', width: 120 },
  { colKey: 'achieveRate', title: '达成度', width: 250 },
  { colKey: 'status', title: '状态', width: 100 },
];

// 课程数据
const coursesData = ref<CourseItem[]>([
  { id: '1', name: '数据结构', teacher: '张教授', achieveRate: 92, status: '已完成', semester: '2022-2023-1' },
  { id: '2', name: '计算机网络', teacher: '李教授', achieveRate: 85, status: '已完成', semester: '2022-2023-1' },
  { id: '3', name: '操作系统', teacher: '王教授', achieveRate: 78, status: '已完成', semester: '2022-2023-2' },
  { id: '4', name: '数据库系统', teacher: '刘教授', achieveRate: 88, status: '已完成', semester: '2022-2023-2' },
  { id: '5', name: '软件工程', teacher: '孙教授', achieveRate: 91, status: '已完成', semester: '2023-2024-1' },
  { id: '6', name: '人工智能', teacher: '钱教授', achieveRate: 82, status: '已完成', semester: '2023-2024-1' },
  { id: '7', name: '计算机组成原理', teacher: '周教授', achieveRate: 79, status: '已完成', semester: '2023-2024-1' },
  { id: '8', name: '编译原理', teacher: '吴教授', achieveRate: 76, status: '进行中', semester: '2023-2024-2' },
  { id: '9', name: '分布式系统', teacher: '郑教授', achieveRate: 80, status: '进行中', semester: '2023-2024-2' },
]);

// 当前选中的课程
const selectedCourse = ref(coursesData.value.length > 0 ? coursesData.value[0].id : '');

// 获取进度条颜色
const getProgressColor = (value: number) => {
  if (value >= 85) return '#00a870'; // 优秀
  if (value >= 70) return '#0052d9'; // 良好
  if (value >= 60) return '#ff9d00'; // 及格
  return '#e34d59'; // 不及格
};

// 初始化雷达图
const initRadarChart = () => {
  if (radarChart.value) {
    updateRadarChart();
  }
};

// 更新雷达图数据
const updateRadarChart = () => {
  if (radarChart.value) {
    const chart = echarts.init(radarChart.value);

    // 根据选中的课程获取数据
    const courseId = selectedCourse.value;
    // 如果没有选中课程，使用默认数据
    let courseGoalValues = [85, 78, 92, 80, 75];
    let courseName = "班级平均";

    // 如果选中了特定课程，生成/获取该课程的数据
    if (courseId) {
      const course = coursesData.value.find(c => c.id === courseId);
      if (course) {
        courseName = course.name;
        // 为选中的课程生成随机的目标达成度数据
        // 实际应用中这里应该从API获取真实数据
        courseGoalValues = [
          Math.floor(Math.random() * 15) + 70,
          Math.floor(Math.random() * 15) + 70,
          Math.floor(Math.random() * 15) + 70,
          Math.floor(Math.random() * 15) + 70,
          Math.floor(Math.random() * 15) + 70
        ];
      }
    }

    const option = {
      tooltip: {
        trigger: 'item'
      },
      legend: {
        data: [courseName, '预期目标'],
        bottom: 5
      },
      radar: {
        indicator: [
          { name: '目标1: 掌握基本理论', max: 100 },
          { name: '目标2: 运用解决问题', max: 100 },
          { name: '目标3: 数据分析能力', max: 100 },
          { name: '目标4: 逻辑推理能力', max: 100 },
          { name: '目标5: 编程实现能力', max: 100 }
        ],
        radius: 120,
        center: ['50%', '50%'],
        splitArea: {
          areaStyle: {
            color: ['rgba(114, 172, 209, 0.05)', 'rgba(114, 172, 209, 0.1)']
          }
        }
      },
      series: [
        {
          name: '课程达成度',
          type: 'radar',
          data: [
            {
              value: courseGoalValues,
              name: courseName,
              areaStyle: {
                color: 'rgba(0, 112, 251, 0.4)'
              },
              lineStyle: {
                color: 'rgba(0, 112, 251, 0.8)',
                width: 2
              },
              symbol: 'circle',
              symbolSize: 6
            },
            {
              value: [70, 70, 70, 70, 70],
              name: '预期目标',
              lineStyle: {
                color: 'rgba(255, 157, 0, 0.8)',
                width: 2,
                type: 'dashed'
              },
              symbol: 'circle',
              symbolSize: 6
            }
          ]
        }
      ]
    };

    chart.setOption(option);
    window.addEventListener('resize', () => chart.resize());
  }
};

// 监听选中的课程变化
watch(selectedCourse, () => {
  updateRadarChart();
});

// 初始化柱状图
const initBarChart = () => {
  if (barChart.value) {
    const chart = echarts.init(barChart.value);

    const option = {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        },
        formatter: '{b}: {c}%'
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: ['工程知识', '问题分析', '设计/开发', '研究', '现代工具', '工程与社会', '环境/可持续', '职业规范'],
        axisLabel: {
          interval: 0,
          rotate: 30,
          fontSize: 12
        }
      },
      yAxis: {
        type: 'value',
        name: '达成度(%)',
        max: 100,
        axisLabel: {
          formatter: '{value}%'
        }
      },
      series: [
        {
          name: '毕业要求达成度',
          type: 'bar',
          data: [82, 89, 78, 85, 90, 76, 82, 88],
          itemStyle: {
            color: function(params: any) {
              const colorList = ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de', '#3ba272', '#fc8452', '#9a60b4'];
              return colorList[params.dataIndex % colorList.length];
            }
          },
          label: {
            show: true,
            position: 'top',
            formatter: '{c}%'
          }
        }
      ]
    };

    chart.setOption(option);
    window.addEventListener('resize', () => chart.resize());
  }
};

// 初始化散点图
const initScatterChart = () => {
  if (scatterChart.value) {
    const chart = echarts.init(scatterChart.value);
    updateScatterChart();
    window.addEventListener('resize', () => chart.resize());
  }
};

// 更新散点图
const updateScatterChart = () => {
  if (scatterChart.value) {
    const chart = echarts.init(scatterChart.value);

    // 生成模拟学生数据
    const generateStudentData = () => {
      const data = [];
      for (let i = 1; i <= classInfo.studentCount; i++) {
        const score = Math.floor(Math.random() * 40) + 60;
        data.push({
          name: `学生${i}`,
          score
        });
      }
      return data;
    };

    const studentData = generateStudentData();
    const goalName = courseGoals.value.find(g => g.id === selectedGoal.value)?.name || '未知目标';

    const option = {
      title: {
        text: `${goalName} - 学生个体达成度分布`,
        left: 'center',
        textStyle: {
          fontSize: 14
        }
      },
      tooltip: {
        trigger: 'item',
        formatter: function(params: any) {
          return `${params.data.name}<br/>达成度: ${params.data.score}%`;
        }
      },
      xAxis: {
        type: 'category',
        data: studentData.map(s => s.name),
        axisLabel: {
          interval: function(index: number, value: string) {
            return index % 5 === 0;
          },
          rotate: 45
        }
      },
      yAxis: {
        type: 'value',
        name: '达成度(%)',
        min: 0,
        max: 100
      },
      series: [
        {
          name: '达成度',
          type: 'scatter',
          data: studentData.map(s => ({
            name: s.name,
            value: s.name,
            score: s.score,
            itemStyle: {
              color: getProgressColor(s.score)
            }
          })),
          symbolSize: 10
        },
        {
          name: '平均线',
          type: 'line',
          markLine: {
            data: [
              {
                type: 'average',
                name: '平均值',
                lineStyle: {
                  color: '#5470c6',
                  type: 'dashed'
                },
                label: {
                  formatter: '平均: {c}%',
                  position: 'middle'
                }
              }
            ]
          }
        }
      ]
    };

    chart.setOption(option);
  }
};

// 初始化饼图
const initPieChart = () => {
  if (pieChart.value) {
    const chart = echarts.init(pieChart.value);

    // 生成模拟数据
    const generateSegmentData = () => {
      // 学生数量根据班级信息获取
      const totalStudents = classInfo.studentCount || 45;

      // 模拟不同分数段的人数
      const excellent = Math.floor(Math.random() * 20) + 15; // 优秀学生人数 (85%以上)
      const good = Math.floor(Math.random() * 15) + 10; // 良好学生人数 (70-84%)
      const pass = Math.floor(Math.random() * 8) + 6; // 及格学生人数 (60-69%)
      const fail = Math.max(1, totalStudents - excellent - good - pass); // 不及格学生人数 (低于60%)

      // 生成详细的学生分数数据用于计算统计值
      const studentScores = [];
      // 优秀学生分数 (85-100)
      for (let i = 0; i < excellent; i++) {
        studentScores.push(Math.floor(Math.random() * 16) + 85);
      }
      // 良好学生分数 (70-84)
      for (let i = 0; i < good; i++) {
        studentScores.push(Math.floor(Math.random() * 15) + 70);
      }
      // 及格学生分数 (60-69)
      for (let i = 0; i < pass; i++) {
        studentScores.push(Math.floor(Math.random() * 10) + 60);
      }
      // 不及格学生分数 (<60)
      for (let i = 0; i < fail; i++) {
        studentScores.push(Math.floor(Math.random() * 59) + 1);
      }

      // 计算统计值
      const average = studentScores.reduce((a, b) => a + b, 0) / studentScores.length;
      // 按分数排序
      const sortedScores = [...studentScores].sort((a, b) => a - b);
      
      // 中位数计算
      const mid = Math.floor(sortedScores.length / 2);
      const median = sortedScores.length % 2 === 0
        ? (sortedScores[mid - 1] + sortedScores[mid]) / 2
        : sortedScores[mid];
      
      // 四分位数计算
      const q1Index = Math.floor(sortedScores.length * 0.25);
      const q3Index = Math.floor(sortedScores.length * 0.75);
      const q1 = sortedScores[q1Index]; // 第一四分位数
      const q3 = sortedScores[q3Index]; // 第三四分位数
      const iqr = q3 - q1; // 四分位距
      
      // 标准差计算
      const variance = studentScores.reduce((a, b) => a + Math.pow(b - average, 2), 0) / studentScores.length;
      const stdDev = Math.sqrt(variance);
      
      // 最高分和最低分
      const max = Math.max(...studentScores);
      const min = Math.min(...studentScores);
      
      // 计算众数
      const frequencyMap: Record<number, number> = {};
      let mode: number | null = null;
      let maxFrequency = 0;
      
      sortedScores.forEach(score => {
        if (!frequencyMap[score]) frequencyMap[score] = 0;
        frequencyMap[score]++;
        
        if (frequencyMap[score] > maxFrequency) {
          maxFrequency = frequencyMap[score];
          mode = score;
        }
      });
      
      // 计算偏度和峰度 (用简化公式)
      const skewness = sortedScores.reduce((sum, score) => 
        sum + Math.pow((score - average) / stdDev, 3), 0) / sortedScores.length;
      
      const kurtosis = sortedScores.reduce((sum, score) => 
        sum + Math.pow((score - average) / stdDev, 4), 0) / sortedScores.length - 3;

      return {
        segments: [
          { value: excellent, name: '优秀(>=85%)', itemStyle: { color: '#00a870' } },
          { value: good, name: '良好(70-84%)', itemStyle: { color: '#0052d9' } },
          { value: pass, name: '及格(60-69%)', itemStyle: { color: '#ff9d00' } },
          { value: fail, name: '不及格(<60%)', itemStyle: { color: '#e34d59' } }
        ],
        stats: {
          average: average.toFixed(1),
          median: median.toFixed(1),
          stdDev: stdDev.toFixed(1),
          max,
          min,
          q1,
          q3,
          iqr: iqr.toFixed(1),
          mode,
          skewness: skewness.toFixed(2),
          kurtosis: kurtosis.toFixed(2),
          totalStudents,
          passRate: (((excellent + good + pass) / totalStudents) * 100).toFixed(1)
        }
      };
    };

    const data = generateSegmentData();

    const option = {
      tooltip: {
        trigger: 'item',
        formatter: (params: any) => {
          // 为中心圆形添加特殊提示
          if (params.name === 'stats') {
            return `
              <div style="padding: 8px 12px;">
                <div style="font-weight: bold; margin-bottom: 8px; text-align: center; font-size: 14px;">班级达成度详细统计</div>
                <table style="width: 100%; border-collapse: collapse;">
                  <tr>
                    <td style="padding: 3px 0;"><strong>平均分</strong>: ${data.stats.average}%</td>
                    <td style="padding: 3px 0;"><strong>中位数</strong>: ${data.stats.median}%</td>
                  </tr>
                  <tr>
                    <td style="padding: 3px 0;"><strong>Q1</strong>: ${data.stats.q1}%</td>
                    <td style="padding: 3px 0;"><strong>Q3</strong>: ${data.stats.q3}%</td>
                  </tr>
                  <tr>
                    <td style="padding: 3px 0;"><strong>标准差</strong>: ${data.stats.stdDev}</td>
                    <td style="padding: 3px 0;"><strong>四分位距</strong>: ${data.stats.iqr}</td>
                  </tr>
                  <tr>
                    <td style="padding: 3px 0;"><strong>最高分</strong>: ${data.stats.max}%</td>
                    <td style="padding: 3px 0;"><strong>最低分</strong>: ${data.stats.min}%</td>
                  </tr>
                  <tr>
                    <td style="padding: 3px 0;"><strong>通过率</strong>: ${data.stats.passRate}%</td>
                    <td style="padding: 3px 0;"><strong>分布偏度</strong>: ${data.stats.skewness}</td>
                  </tr>
                </table>
              </div>
            `;
          }
          // 普通数据项提示
          return `
            <div style="padding: 8px 12px;">
              <div style="font-weight: bold; margin-bottom: 4px;">${params.name}</div>
              <div>人数: ${params.value}人</div>
              <div>占比: ${params.percent}%</div>
            </div>
          `;
        },
        extraCssText: 'box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15); border-radius: 4px;'
      },
      legend: {
        orient: 'vertical',
        right: 10,
        top: 'center',
        itemWidth: 14,
        itemHeight: 14,
        itemGap: 12,
        textStyle: {
          fontSize: 12
        }
      },
      series: [
        {
          name: '达成度分段',
          type: 'pie',
          radius: ['40%', '70%'],
          center: ['40%', '50%'],
          roseType: 'radius', // 使用玫瑰图展示
          avoidLabelOverlap: false,
          itemStyle: {
            borderRadius: 8,
            borderColor: '#fff',
            borderWidth: 2,
            shadowBlur: 5,
            shadowColor: 'rgba(0, 0, 0, 0.1)'
          },
          label: {
            show: true,
            position: 'outside',
            formatter: '{b}: {c}人',
            fontSize: 12,
            color: '#333'
          },
          emphasis: {
            label: {
              show: true,
              fontSize: 14,
              fontWeight: 'bold',
              formatter: '{b}: {c}人 ({d}%)'
            },
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          },
          labelLine: {
            show: true,
            smooth: true
          },
          data: data.segments
        },
        // 添加中心文本图表
        {
          type: 'pie',
          radius: ['0%', '30%'],
          center: ['40%', '50%'],
          silent: false,
          label: {
            show: true,
            position: 'center',
            formatter: function () {
              return `平均: ${data.stats.average}%\n中位数: ${data.stats.median}%\n通过率: ${data.stats.passRate}%`;
            },
            fontSize: 13,
            fontWeight: 'bold',
            lineHeight: 20,
            color: '#333',
            backgroundColor: 'rgba(255, 255, 255, 0.8)',
            borderRadius: 4,
            padding: [10, 10]
          },
          data: [{
            value: 100,
            name: 'stats',
            itemStyle: {
              color: '#f9f9f9',
              shadowBlur: 5,
              shadowColor: 'rgba(0, 0, 0, 0.1)'
            }
          }]
        }
      ]
    };

    chart.setOption(option);
    window.addEventListener('resize', () => chart.resize());

    // 创建图表下方的统计信息区域
    const oldStat = pieChart.value.parentNode.querySelector('.segment-stats');
    if (oldStat) oldStat.remove();

    const statsDiv = document.createElement('div');
    statsDiv.className = 'segment-stats';
    statsDiv.style.marginTop = '10px';
    statsDiv.style.fontSize = '12px';
    statsDiv.style.color = '#666';
    statsDiv.innerHTML = `
      <div style="border-top: 1px solid #eee; padding-top: 10px;">
        <div style="display: flex; flex-wrap: wrap; justify-content: space-around;">
          <div style="flex: 1; min-width: 80px; padding: 4px; text-align: center;">
            <div style="font-weight: bold;">总人数</div>
            <div>${data.stats.totalStudents}人</div>
          </div>
          <div style="flex: 1; min-width: 80px; padding: 4px; text-align: center;">
            <div style="font-weight: bold;">Q1 ~ Q3</div>
            <div>${data.stats.q1}% ~ ${data.stats.q3}%</div>
          </div>
          <div style="flex: 1; min-width: 80px; padding: 4px; text-align: center;">
            <div style="font-weight: bold;">标准差</div>
            <div>${data.stats.stdDev}</div>
          </div>
        </div>
        <div style="display: flex; margin-top: 5px;">
          <div style="flex: 1; height: 6px; border-radius: 3px; background: linear-gradient(to right, #e34d59, #ff9d00, #0052d9, #00a870); margin: 0 10px;"></div>
        </div>
        <div style="display: flex; justify-content: space-between; margin: 2px 10px; font-size: 11px;">
          <span>${data.stats.min}%</span>
          <span>${data.stats.q1}%</span>
          <span>${data.stats.median}%</span>
          <span>${data.stats.q3}%</span>
          <span>${data.stats.max}%</span>
        </div>
      </div>
    `;
    pieChart.value.parentNode.appendChild(statsDiv);
  }
};

// 监听选中的目标变化
watch(selectedGoal, () => {
  updateScatterChart();
});

// 初始化
onMounted(() => {
  initRadarChart();
  initBarChart();
  initScatterChart();
  initPieChart();
});
</script>

<style scoped lang="less">
.class-detail-container {
  padding: 20px;

  .page-header {
    margin-bottom: 20px;

    .header-wrapper {
      display: flex;
      align-items: center;

      .back-button {
        margin-right: 16px;
      }

      .title-container {
        .page-title {
          margin: 0;
          font-size: 20px;
          font-weight: 600;
        }

        .subtitle {
          color: rgba(0, 0, 0, 0.6);
          font-size: 14px;
          margin-left: 8px;
        }
      }
    }
  }

  .card-row {
    margin-bottom: 16px;
  }

  .stat-card {
    height: 140px;
    display: flex;
    justify-content: center;
    align-items: center;

    .stat-title {
      font-size: 14px;
      color: rgba(0, 0, 0, 0.6);
      margin: 8px 0;
    }

    .stat-value {
      font-size: 24px;
      font-weight: 600;
    }

    &.students-count {
      .t-icon {
        color: #0052d9;
      }
      .stat-value {
        color: #0052d9;
      }
    }

    &.teacher-name {
      .t-icon {
        color: #13a8a8;
      }
      .stat-value {
        color: #13a8a8;
      }
    }

    &.pass-rate {
      .t-icon {
        color: #ff9d00;
      }
      .stat-value {
        color: #ff9d00;
      }
    }

    &.achieve-rate {
      .t-icon {
        color: #00a870;
      }
      .stat-value {
        color: #00a870;
      }
    }
  }

  .chart-card {
    margin-bottom: 16px;

    .card-title-with-select {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .chart-container {
      height: 350px;
      width: 100%;
    }
    
    &.goal-chart, &.segment-chart {
      height: 450px;
      
      .chart-container {
        height: 380px;
      }
    }
    
    :deep(.t-card__actions) {
      padding: 0;
      position: absolute;
      top: 16px;
      right: 16px;
    }
  }

  .course-card {
    margin-bottom: 20px;
  }
}
</style>

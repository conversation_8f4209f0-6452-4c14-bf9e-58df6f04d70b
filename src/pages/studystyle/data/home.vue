<template>
  <div class="study-style-data-home">
    <t-card class="main-card">
      <div class="page-header">
        <div class="header-left">
          <h2 class="page-title">
            <t-icon name="chart-line" class="title-icon" />
            学风建设数据分析
          </h2>
          <p class="page-subtitle">{{ pageSubtitle }}</p>
        </div>
        <div class="header-actions">
          <t-button theme="default" @click="exportData">
            <template #icon><t-icon name="download" /></template>
            导出数据
          </t-button>
          <t-button theme="default" @click="refreshData" :loading="loading">
            <template #icon><t-icon name="refresh" /></template>
            刷新
          </t-button>
        </div>
      </div>

      <!-- 参数显示区域 -->
      <div class="params-section" v-if="showParams">
        <t-alert theme="info" :close="false">
          <template #icon><t-icon name="info-circle" /></template>
          <div class="params-info">
            <span><strong>数据类型：</strong>{{ dataTypeText }}</span>
            <span v-if="departmentId"><strong>院系ID：</strong>{{ departmentId }}</span>
            <span v-if="departmentName"><strong>院系名称：</strong>{{ departmentName }}</span>
          </div>
        </t-alert>
      </div>

      <!-- 数据统计卡片 -->
      <div class="statistics-grid">
        <div class="stat-card" v-for="stat in statistics" :key="stat.key">
          <div class="stat-icon">
            <t-icon :name="stat.icon" />
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ stat.value }}</div>
            <div class="stat-label">{{ stat.label }}</div>
          </div>
        </div>
      </div>

      <!-- 数据表格 -->
      <div class="data-table-section">
        <div class="table-header">
          <h3>详细数据</h3>
          <div class="table-actions">
            <t-input 
              v-model="searchKeyword" 
              placeholder="搜索..." 
              clearable
              class="search-input"
            >
              <template #prefix-icon>
                <t-icon name="search" />
              </template>
            </t-input>
          </div>
        </div>
        
        <t-table
          :data="filteredTableData"
          :columns="tableColumns"
          :loading="loading"
          row-key="id"
          :pagination="pagination"
          @page-change="handlePageChange"
        />
      </div>
    </t-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useRoute } from 'vue-router';
import { MessagePlugin } from 'tdesign-vue-next';

// 获取路由参数
const route = useRoute();

// 响应式数据
const loading = ref(false);
const searchKeyword = ref('');
const departmentId = ref<string | null>(null);
const departmentName = ref<string>('');
const dataType = ref<string>('');

// 统计数据
const statistics = ref([
  {
    key: 'total',
    icon: 'user-group',
    label: '总人数',
    value: 0
  },
  {
    key: 'excellent',
    icon: 'thumb-up',
    label: '优秀率',
    value: '0%'
  },
  {
    key: 'good',
    icon: 'check-circle',
    label: '良好率',
    value: '0%'
  },
  {
    key: 'average',
    icon: 'info-circle',
    label: '平均分',
    value: 0
  }
]);

// 表格数据
const tableData = ref([]);

// 表格列配置
const tableColumns = ref([
  {
    colKey: 'name',
    title: '姓名',
    width: 120,
  },
  {
    colKey: 'studentId',
    title: '学号',
    width: 150,
  },
  {
    colKey: 'major',
    title: '专业',
    width: 150,
  },
  {
    colKey: 'grade',
    title: '年级',
    width: 100,
  },
  {
    colKey: 'score',
    title: '学风评分',
    width: 120,
  },
  {
    colKey: 'level',
    title: '等级',
    width: 100,
  },
  {
    colKey: 'updateTime',
    title: '更新时间',
    width: 180,
  },
]);

// 分页配置
const pagination = ref({
  current: 1,
  pageSize: 20,
  total: 0,
});

// 计算属性
const pageSubtitle = computed(() => {
  if (dataType.value === 'college' && departmentName.value) {
    return `${departmentName.value} 院系学风数据分析`;
  } else if (dataType.value === 'major') {
    return '专业学风数据分析';
  }
  return '学风建设数据概览';
});

const dataTypeText = computed(() => {
  switch (dataType.value) {
    case 'college':
      return '院系数据';
    case 'major':
      return '专业数据';
    default:
      return '综合数据';
  }
});

const showParams = computed(() => {
  return departmentId.value || dataType.value;
});

const filteredTableData = computed(() => {
  if (!searchKeyword.value) {
    return tableData.value;
  }
  return tableData.value.filter((item: any) => 
    item.name.includes(searchKeyword.value) ||
    item.studentId.includes(searchKeyword.value) ||
    item.major.includes(searchKeyword.value)
  );
});

// 方法
const loadData = async () => {
  loading.value = true;
  try {
    // 模拟数据加载
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // 模拟表格数据
    tableData.value = [
      {
        id: 1,
        name: '张三',
        studentId: '20210001',
        major: '计算机科学与技术',
        grade: '2021级',
        score: 85,
        level: '良好',
        updateTime: '2024-01-15 10:30:00'
      },
      {
        id: 2,
        name: '李四',
        studentId: '20210002',
        major: '软件工程',
        grade: '2021级',
        score: 92,
        level: '优秀',
        updateTime: '2024-01-15 10:35:00'
      },
      // 更多模拟数据...
    ] as any[];

    // 更新统计数据
    statistics.value[0].value = tableData.value.length;
    statistics.value[1].value = '15%';
    statistics.value[2].value = '35%';
    statistics.value[3].value = 87;

    pagination.value.total = tableData.value.length;
    
    MessagePlugin.success('数据加载成功');
  } catch (error) {
    MessagePlugin.error('数据加载失败');
    console.error('Load data error:', error);
  } finally {
    loading.value = false;
  }
};

const refreshData = () => {
  loadData();
};

const exportData = () => {
  MessagePlugin.info('导出功能开发中...');
};

const handlePageChange = (pageInfo: any) => {
  pagination.value.current = pageInfo.current;
  pagination.value.pageSize = pageInfo.pageSize;
};

// 获取部门名称（模拟数据，实际应该从API获取）
const getDepartmentName = (id: string) => {
  const departmentMap: Record<string, string> = {
    '1': '计算机科学学院',
    '2': '软件学院', 
    '3': '信息工程学院',
    '4': '电子工程学院',
    '5': '数学与统计学院',
    '6': '物理学院'
  };
  return departmentMap[id] || '未知院系';
};

// 初始化
onMounted(() => {
  // 获取路由参数
  departmentId.value = route.query.departmentId as string;
  dataType.value = route.query.dataType as string || '';
  
  if (departmentId.value) {
    departmentName.value = getDepartmentName(departmentId.value);
  }
  
  console.log('学风数据页面参数:', {
    departmentId: departmentId.value,
    dataType: dataType.value,
    departmentName: departmentName.value
  });
  
  // 加载数据
  loadData();
});
</script>

<style scoped>
.study-style-data-home {
  padding: 24px;
}

.main-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid var(--td-border-level-1-color);
}

.header-left .page-title {
  display: flex;
  align-items: center;
  font-size: 24px;
  font-weight: 600;
  color: var(--td-text-color-primary);
  margin: 0 0 8px 0;
}

.title-icon {
  margin-right: 12px;
  color: var(--td-brand-color);
}

.page-subtitle {
  font-size: 14px;
  color: var(--td-text-color-secondary);
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.params-section {
  margin-bottom: 24px;
}

.params-info {
  display: flex;
  gap: 24px;
  flex-wrap: wrap;
}

.statistics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 32px;
}

.stat-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  padding: 20px;
  color: white;
  display: flex;
  align-items: center;
  gap: 16px;
  transition: transform 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
}

.stat-card:nth-child(2) {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-card:nth-child(3) {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-card:nth-child(4) {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-icon {
  font-size: 32px;
  opacity: 0.9;
}

.stat-content .stat-value {
  font-size: 28px;
  font-weight: 600;
  line-height: 1.2;
  margin-bottom: 4px;
}

.stat-content .stat-label {
  font-size: 14px;
  opacity: 0.9;
}

.data-table-section {
  margin-top: 24px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.table-header h3 {
  font-size: 18px;
  font-weight: 600;
  color: var(--td-text-color-primary);
  margin: 0;
}

.search-input {
  width: 250px;
}
</style>

<template>
  <div class="grade-management-container">
    <!-- 头部信息 -->
    <div class="page-header">
      <div class="header-content">
        <div class="title-info">
          <h1 class="page-title">学生成绩管理</h1>
          <p class="page-subtitle">管理课程学生成绩数据与统计分析</p>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="page-content">
      <t-card>
        <template #title>
          <div class="card-header">
            <t-icon name="user-circle" class="title-icon" />
            <span>学生成绩管理</span>
            <div class="header-right">
              <t-space>
                <t-button theme="default">
                  <template #icon>
                    <t-icon name="upload" />
                  </template>
                  导入成绩
                </t-button>
                <t-button theme="default">
                  <template #icon>
                    <t-icon name="download" />
                  </template>
                  导出成绩
                </t-button>
              </t-space>
            </div>
          </div>
        </template>
        
        <!-- 筛选区域 -->
        <div class="filter-container">
          <t-form layout="inline">
            <t-form-item label="学期">
              <t-select v-model="filterOptions.semester" :options="semesterOptions" style="width: 180px;"></t-select>
            </t-form-item>
            <t-form-item label="班级">
              <t-select v-model="filterOptions.class" :options="classOptions" style="width: 180px;"></t-select>
            </t-form-item>
            <t-form-item label="学号/姓名">
              <t-input v-model="filterOptions.keyword" placeholder="请输入学号或姓名" style="width: 200px;"></t-input>
            </t-form-item>
            <t-form-item>
              <t-button theme="primary" @click="handleSearch">
                <template #icon>
                  <t-icon name="search" />
                </template>
                查询
              </t-button>
              <t-button theme="default" @click="handleReset" style="margin-left: 8px;">
                <template #icon>
                  <t-icon name="refresh" />
                </template>
                重置
              </t-button>
            </t-form-item>
          </t-form>
        </div>
        
        <!-- 成绩表格 -->
        <t-table
          :data="studentGrades"
          :columns="gradeColumns"
          :bordered="true"
          :hover="true"
          :loading="loading"
          row-key="id"
          :pagination="pagination"
          @page-change="onPageChange"
          style="margin-top: 16px;"
        >
          <template #totalScore="{ row }">
            <div :class="getScoreClass(row.totalScore)">{{ row.totalScore }}</div>
          </template>
          <template #status="{ row }">
            <t-tag :theme="row.status === 'pass' ? 'success' : 'danger'">
              {{ row.status === 'pass' ? '通过' : '未通过' }}
            </t-tag>
          </template>
          <template #operation="{ row }">
            <t-space size="small">
              <t-button theme="primary" variant="text" size="small" @click="editGrade(row)">
                <template #icon>
                  <t-icon name="edit" />
                </template>
                编辑
              </t-button>
              <t-button theme="primary" variant="text" size="small" @click="viewGradeDetail(row)">
                <template #icon>
                  <t-icon name="view-module" />
                </template>
                详情
              </t-button>
            </t-space>
          </template>
        </t-table>
      </t-card>
      
      <!-- 成绩统计卡片 -->
      <t-row :gutter="[16, 16]" style="margin-top: 16px;">
        <t-col :span="6">
          <t-card class="stat-card">
            <div class="stat-content">
              <div class="stat-number">{{ statistics.averageScore }}</div>
              <div class="stat-label">平均分</div>
            </div>
            <div class="stat-icon">
              <t-icon name="chart-bubble" />
            </div>
          </t-card>
        </t-col>
        
        <t-col :span="6">
          <t-card class="stat-card">
            <div class="stat-content">
              <div class="stat-number">{{ statistics.passRate }}%</div>
              <div class="stat-label">通过率</div>
            </div>
            <div class="stat-icon">
              <t-icon name="check-circle" />
            </div>
          </t-card>
        </t-col>
        
        <t-col :span="6">
          <t-card class="stat-card">
            <div class="stat-content">
              <div class="stat-number">{{ statistics.excellentRate }}%</div>
              <div class="stat-label">优秀率</div>
            </div>
            <div class="stat-icon">
              <t-icon name="star" />
            </div>
          </t-card>
        </t-col>
        
        <t-col :span="6">
          <t-card class="stat-card">
            <div class="stat-content">
              <div class="stat-number">{{ statistics.failRate }}%</div>
              <div class="stat-label">不及格率</div>
            </div>
            <div class="stat-icon">
              <t-icon name="close-circle" />
            </div>
          </t-card>
        </t-col>
      </t-row>
      
      <!-- 成绩分布图 -->
      <t-card style="margin-top: 16px;">
        <template #title>
          <div class="card-header">
            <t-icon name="chart-bar" class="title-icon" />
            <span>成绩分布统计</span>
          </div>
        </template>
        
        <div class="chart-container">
          <div id="grade-distribution-chart" class="chart"></div>
        </div>
      </t-card>
    </div>
    
    <!-- 编辑成绩对话框 -->
    <t-dialog
      v-model:visible="editDialogVisible"
      header="编辑学生成绩"
      :on-confirm="saveGrade"
      :confirm-btn="{ content: '保存', theme: 'primary' }"
      :cancel-btn="{ content: '取消', theme: 'default' }"
      width="600px"
    >
      <t-form :data="editFormData" label-align="right" label-width="120px">
        <t-form-item label="学生姓名">
          <t-input v-model="editFormData.studentName" disabled></t-input>
        </t-form-item>
        <t-form-item label="学号">
          <t-input v-model="editFormData.studentId" disabled></t-input>
        </t-form-item>
        <t-form-item label="班级">
          <t-input v-model="editFormData.className" disabled></t-input>
        </t-form-item>
        
        <t-divider>成绩信息</t-divider>
        
        <t-form-item v-for="item in assessmentItems" :key="item.id" :label="item.name">
          <t-input-number
            v-model="editFormData.scores[item.id]"
            :min="0"
            :max="item.maxScore"
            :step="0.5"
          ></t-input-number>
          <span class="max-score">满分{{ item.maxScore }}</span>
        </t-form-item>
        
        <t-form-item label="总分">
          <t-input-number v-model="editFormData.totalScore" :min="0" :max="100" disabled></t-input-number>
        </t-form-item>
      </t-form>
    </t-dialog>
    
    <!-- 成绩详情对话框 -->
    <t-dialog
      v-model:visible="detailDialogVisible"
      header="学生成绩详情"
      :on-confirm="closeDetailDialog"
      :confirm-btn="{ content: '关闭', theme: 'primary' }"
      width="700px"
      :footer="false"
    >
      <div class="grade-detail">
        <t-row :gutter="[16, 16]">
          <t-col :span="8">
            <div class="detail-item">
              <div class="detail-label">学生姓名</div>
              <div class="detail-value">{{ detailData.studentName }}</div>
            </div>
          </t-col>
          <t-col :span="8">
            <div class="detail-item">
              <div class="detail-label">学号</div>
              <div class="detail-value">{{ detailData.studentId }}</div>
            </div>
          </t-col>
          <t-col :span="8">
            <div class="detail-item">
              <div class="detail-label">班级</div>
              <div class="detail-value">{{ detailData.className }}</div>
            </div>
          </t-col>
        </t-row>
        
        <t-divider>成绩详情</t-divider>
        
        <t-table
          :data="detailData.detailScores"
          :columns="detailColumns"
          :bordered="true"
          row-key="id"
          style="margin-bottom: 24px;"
        ></t-table>
        
        <t-row justify="space-around">
          <t-col :span="8">
            <div class="total-score-card">
              <div class="total-score" :class="getScoreClass(detailData.totalScore)">{{ detailData.totalScore }}</div>
              <div class="total-score-label">总分</div>
            </div>
          </t-col>
        </t-row>
        
        <t-divider>教师评语</t-divider>
        
        <div class="comment-section">
          <p>{{ detailData.comment || '暂无评语' }}</p>
        </div>
        
        <div class="dialog-footer">
          <t-button theme="primary" @click="closeDetailDialog">关闭</t-button>
        </div>
      </div>
    </t-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue';
import { useRoute } from 'vue-router';
import { MessagePlugin } from 'tdesign-vue-next';

const route = useRoute();
const courseId = ref(route.params.id);
const loading = ref(false);

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 100,
  showJumper: true,
  showPageSize: true,
  pageSizeOptions: [10, 20, 50],
});

// 筛选选项
const filterOptions = reactive({
  semester: '2024-spring',
  class: 'all',
  keyword: '',
});

// 选项数据
const semesterOptions = [
  { label: '2025春季学期', value: '2025-spring' },
  { label: '2024秋季学期', value: '2024-autumn' },
  { label: '2024春季学期', value: '2024-spring' },
  { label: '2023秋季学期', value: '2023-autumn' }
];

const classOptions = [
  { label: '全部班级', value: 'all' },
  { label: '计算机2022-1班', value: 'cs-2022-1' },
  { label: '计算机2022-2班', value: 'cs-2022-2' },
  { label: '软件工程2022-1班', value: 'se-2022-1' }
];

// 成绩统计数据
const statistics = reactive({
  averageScore: 82.5,
  passRate: 93.5,
  excellentRate: 35.8,
  failRate: 6.5,
});

// 表格列配置
const gradeColumns = [
  { colKey: 'studentId', title: '学号', width: 120 },
  { colKey: 'studentName', title: '姓名', width: 100 },
  { colKey: 'className', title: '班级', width: 150 },
  { colKey: 'attendance', title: '出勤成绩', width: 100 },
  { colKey: 'homework', title: '作业成绩', width: 100 },
  { colKey: 'midterm', title: '期中成绩', width: 100 },
  { colKey: 'final', title: '期末成绩', width: 100 },
  { colKey: 'totalScore', title: '总评成绩', width: 100 },
  { colKey: 'status', title: '状态', width: 100 },
  { colKey: 'operation', title: '操作', width: 150, fixed: 'right' }
];

// 详情表格列配置
const detailColumns = [
  { colKey: 'assessmentName', title: '考核项目', width: 180 },
  { colKey: 'score', title: '得分', width: 100 },
  { colKey: 'maxScore', title: '满分', width: 100 },
  { colKey: 'weight', title: '权重', width: 100 },
  { colKey: 'weightedScore', title: '加权得分', width: 120 }
];

// 成绩数据
const studentGrades = ref([
  { 
    id: 1, 
    studentId: '2022001001', 
    studentName: '张三', 
    className: '计算机2022-1班', 
    attendance: 95, 
    homework: 85, 
    midterm: 82, 
    final: 88, 
    totalScore: 87, 
    status: 'pass' 
  },
  { 
    id: 2, 
    studentId: '2022001002', 
    studentName: '李四', 
    className: '计算机2022-1班', 
    attendance: 90, 
    homework: 92, 
    midterm: 78, 
    final: 91, 
    totalScore: 88, 
    status: 'pass' 
  },
  { 
    id: 3, 
    studentId: '2022001003', 
    studentName: '王五', 
    className: '计算机2022-1班', 
    attendance: 75, 
    homework: 70, 
    midterm: 65, 
    final: 72, 
    totalScore: 70, 
    status: 'pass' 
  },
  { 
    id: 4, 
    studentId: '2022001004', 
    studentName: '赵六', 
    className: '计算机2022-1班', 
    attendance: 60, 
    homework: 55, 
    midterm: 50, 
    final: 58, 
    totalScore: 55, 
    status: 'fail' 
  },
  { 
    id: 5, 
    studentId: '2022001005', 
    studentName: '钱七', 
    className: '计算机2022-1班', 
    attendance: 98, 
    homework: 95, 
    midterm: 92, 
    final: 96, 
    totalScore: 95, 
    status: 'pass' 
  }
]);

// 考核项目数据
const assessmentItems = [
  { id: 'attendance', name: '出勤成绩', maxScore: 100, weight: 10 },
  { id: 'homework', name: '作业成绩', maxScore: 100, weight: 20 },
  { id: 'midterm', name: '期中成绩', maxScore: 100, weight: 30 },
  { id: 'final', name: '期末成绩', maxScore: 100, weight: 40 }
];

// 编辑对话框相关
const editDialogVisible = ref(false);
const editFormData = reactive({
  id: 0,
  studentName: '',
  studentId: '',
  className: '',
  scores: {
    attendance: 0,
    homework: 0,
    midterm: 0,
    final: 0
  },
  totalScore: 0
});

// 详情对话框相关
const detailDialogVisible = ref(false);
const detailData = reactive({
  studentName: '',
  studentId: '',
  className: '',
  totalScore: 0,
  comment: '',
  detailScores: [] as any[]
});

// 获取成绩样式类
const getScoreClass = (score: number) => {
  if (score >= 90) return 'score-excellent';
  if (score >= 80) return 'score-good';
  if (score >= 70) return 'score-medium';
  if (score >= 60) return 'score-pass';
  return 'score-fail';
};

// 初始化图表
const initCharts = () => {
  // 这里是图表初始化的代码，实际项目中可以使用ECharts等图表库
  console.log('初始化成绩分布图表');
};

// 方法: 加载成绩数据
const loadGradeData = () => {
  loading.value = true;
  setTimeout(() => {
    // 模拟API调用
    loading.value = false;
  }, 500);
};

// 方法: 处理搜索
const handleSearch = () => {
  pagination.current = 1;
  loadGradeData();
};

// 方法: 处理重置
const handleReset = () => {
  filterOptions.semester = '2024-spring';
  filterOptions.class = 'all';
  filterOptions.keyword = '';
  handleSearch();
};

// 方法: 分页变化
const onPageChange = (pageInfo: { current: number; pageSize: number }) => {
  pagination.current = pageInfo.current;
  pagination.pageSize = pageInfo.pageSize;
  loadGradeData();
};

// 方法: 编辑成绩
const editGrade = (row: any) => {
  editFormData.id = row.id;
  editFormData.studentName = row.studentName;
  editFormData.studentId = row.studentId;
  editFormData.className = row.className;
  editFormData.scores = {
    attendance: row.attendance,
    homework: row.homework,
    midterm: row.midterm,
    final: row.final
  };
  editFormData.totalScore = row.totalScore;
  
  editDialogVisible.value = true;
};

// 计算总分
const calculateTotalScore = () => {
  let total = 0;
  let weightSum = 0;
  
  assessmentItems.forEach(item => {
    total += (editFormData.scores[item.id] * item.weight);
    weightSum += item.weight;
  });
  
  return weightSum > 0 ? (total / weightSum).toFixed(1) : 0;
};

// 方法: 保存成绩
const saveGrade = () => {
  // 计算总分
  editFormData.totalScore = Number(calculateTotalScore());
  
  // 在这里实现保存逻辑
  MessagePlugin.success('成绩保存成功');
  editDialogVisible.value = false;
  loadGradeData();
};

// 方法: 查看成绩详情
const viewGradeDetail = (row: any) => {
  detailData.studentName = row.studentName;
  detailData.studentId = row.studentId;
  detailData.className = row.className;
  detailData.totalScore = row.totalScore;
  detailData.comment = '该学生在本课程中表现优秀，课堂参与度高，作业按时完成且质量好，期中期末考试成绩稳定。';
  
  // 生成详情数据
  detailData.detailScores = assessmentItems.map(item => ({
    id: item.id,
    assessmentName: item.name,
    score: row[item.id],
    maxScore: item.maxScore,
    weight: `${item.weight}%`,
    weightedScore: (row[item.id] * item.weight / 100).toFixed(1)
  }));
  
  detailDialogVisible.value = true;
};

// 方法: 关闭详情对话框
const closeDetailDialog = () => {
  detailDialogVisible.value = false;
};

// 生命周期钩子
onMounted(() => {
  loadGradeData();
  initCharts();
});
</script>

<style lang="less" scoped>
.grade-management-container {
  padding: 20px;
  
  .page-header {
    margin-bottom: 24px;
    
    .header-content {
      .title-info {
        .page-title {
          font-size: 24px;
          font-weight: 600;
          margin: 0 0 8px 0;
        }
        
        .page-subtitle {
          font-size: 14px;
          color: var(--td-text-color-secondary);
          margin: 0;
        }
      }
    }
  }
  
  .page-content {
    .card-header {
      display: flex;
      align-items: center;
      width: 100%;
      
      .title-icon {
        margin-right: 8px;
        color: var(--td-brand-color);
      }
      
      span {
        flex: 1;
      }
      
      .header-right {
        display: flex;
        align-items: center;
      }
    }
    
    .filter-container {
      margin-bottom: 16px;
    }
    
    .stat-card {
      position: relative;
      overflow: hidden;
      
      .stat-content {
        position: relative;
        z-index: 1;
        
        .stat-number {
          font-size: 28px;
          font-weight: 700;
          color: var(--td-text-color-primary);
        }
        
        .stat-label {
          font-size: 14px;
          color: var(--td-text-color-secondary);
          margin-top: 4px;
        }
      }
      
      .stat-icon {
        position: absolute;
        right: 16px;
        top: 16px;
        font-size: 32px;
        opacity: 0.2;
        color: var(--td-brand-color);
      }
    }
    
    .chart-container {
      height: 400px;
      
      .chart {
        height: 100%;
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #f9f9f9;
        border-radius: 3px;
        
        &::after {
          content: '成绩分布图表区域';
          color: var(--td-text-color-secondary);
        }
      }
    }
  }
  
  // 对话框样式
  .max-score {
    margin-left: 8px;
    color: var(--td-text-color-secondary);
    font-size: 12px;
  }
  
  .grade-detail {
    .detail-item {
      margin-bottom: 16px;
      
      .detail-label {
        font-size: 14px;
        color: var(--td-text-color-secondary);
        margin-bottom: 4px;
      }
      
      .detail-value {
        font-size: 16px;
        font-weight: 500;
      }
    }
    
    .total-score-card {
      text-align: center;
      padding: 16px;
      
      .total-score {
        font-size: 48px;
        font-weight: 700;
      }
      
      .total-score-label {
        margin-top: 8px;
        font-size: 14px;
        color: var(--td-text-color-secondary);
      }
    }
    
    .comment-section {
      background-color: var(--td-bg-color-container-select);
      padding: 16px;
      border-radius: 6px;
      margin-bottom: 24px;
      
      p {
        margin: 0;
        line-height: 1.6;
      }
    }
    
    .dialog-footer {
      text-align: right;
    }
  }
  
  // 成绩样式
  .score-excellent {
    color: #ed7b2f;
    font-weight: 600;
  }
  
  .score-good {
    color: #00a870;
    font-weight: 600;
  }
  
  .score-medium {
    color: #0052d9;
  }
  
  .score-pass {
    color: #888;
  }
  
  .score-fail {
    color: #e34d59;
    font-weight: 600;
  }
}
</style>

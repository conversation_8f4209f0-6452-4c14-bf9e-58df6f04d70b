<template>
  <div class="course-target-container">
    <!-- 头部信息 -->
    <div class="page-header">
      <div class="header-content">
        <div class="title-info">
          <h1 class="page-title">课程目标配置</h1>
          <p class="page-subtitle">配置课程目标与毕业要求指标点的支撑关系</p>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="page-content">
      <t-card>
        <template #title>
          <div class="card-header">
            <t-icon name="target" class="title-icon" />
            <span>课程目标与毕业要求支撑关系配置</span>
          </div>
        </template>
        
        <div class="empty-placeholder">
          <t-empty
            description="暂无课程目标配置数据"
          >
            <template #image>
              <t-icon name="file-paste" size="64px" />
            </template>
            <span slot="description">请先选择课程，然后进行课程目标配置</span>
            <t-button theme="primary">开始配置</t-button>
          </t-empty>
        </div>
      </t-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useCourseId } from '@/hooks/useCourseId';

// 使用课程ID钩子函数
const { courseId, loading } = useCourseId({
  onCourseIdChange: (id) => {
    console.log('课程ID变更:', id);
    loadCourseTargetData(id);
  }
});

// 加载课程目标数据
const loadCourseTargetData = async (id: string | undefined) => {
  if (!id) return;
  
  loading.value = true;
  try {
    console.log('开始加载课程目标数据，课程ID:', id);
    // TODO: 调用API加载数据
    // const response = await api.getCourseTarget(id);
    // targetData.value = response.data;
    
    // 模拟加载延迟
    setTimeout(() => {
      loading.value = false;
    }, 500);
  } catch (error) {
    console.error('加载课程目标数据失败:', error);
    loading.value = false;
  }
};

onMounted(() => {
  console.log('课程目标配置页面加载，课程ID:', courseId.value);
  if (courseId.value) {
    loadCourseTargetData(courseId.value);
  }
});
</script>

<style lang="less" scoped>
.course-target-container {
  padding: 20px;
  
  .page-header {
    margin-bottom: 24px;
    
    .header-content {
      .title-info {
        .page-title {
          font-size: 24px;
          font-weight: 600;
          margin: 0 0 8px 0;
        }
        
        .page-subtitle {
          font-size: 14px;
          color: var(--td-text-color-secondary);
          margin: 0;
        }
      }
    }
  }
  
  .page-content {
    .card-header {
      display: flex;
      align-items: center;
      gap: 8px;
      
      .title-icon {
        color: var(--td-brand-color);
      }
    }
    
    .empty-placeholder {
      padding: 60px 0;
      text-align: center;
    }
  }
}
</style>

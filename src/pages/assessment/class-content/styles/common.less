// 班级考核管理共享样式

// 页面布局
.assessment-page-layout {
  padding: 24px;
  background: var(--td-bg-color-page);
  min-height: 100vh;
}

// 卡片样式
.assessment-card {
  background: var(--td-bg-color-container);
  border-radius: 6px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: var(--td-shadow-1);
}

// 统计卡片
.stat-card {
  display: flex;
  align-items: center;
  padding: 20px;
  background: var(--td-bg-color-page);
  border-radius: 6px;
  border: 1px solid var(--td-border-level-1-color);
  transition: all 0.3s ease;

  &:hover {
    box-shadow: var(--td-shadow-2);
    transform: translateY(-2px);
  }

  .stat-icon {
    width: 48px;
    height: 48px;
    background: var(--td-brand-color);
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 16px;

    :deep(.t-icon) {
      color: white;
      font-size: 24px;
    }
  }

  .stat-content {
    .stat-number {
      font-size: 24px;
      font-weight: 600;
      color: var(--td-text-color-primary);
      line-height: 1;
      margin-bottom: 4px;
    }

    .stat-label {
      font-size: 14px;
      color: var(--td-text-color-secondary);
    }
  }
}

// 空状态样式
.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: var(--td-text-color-secondary);

  .t-icon {
    color: var(--td-text-color-placeholder);
    margin-bottom: 16px;
  }

  p {
    margin: 0;
    font-size: 14px;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .assessment-page-layout {
    padding: 16px;
  }

  .assessment-card {
    padding: 16px;
  }

  .stat-card {
    padding: 16px;

    .stat-icon {
      width: 40px;
      height: 40px;
      margin-right: 12px;

      :deep(.t-icon) {
        font-size: 20px;
      }
    }

    .stat-content {
      .stat-number {
        font-size: 20px;
      }

      .stat-label {
        font-size: 12px;
      }
    }
  }
} 
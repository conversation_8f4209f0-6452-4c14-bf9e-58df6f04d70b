// 考核内容类型
export interface AssessmentContent {
  id: string
  sectionName: string
  title: string
  inputMode: 'direct' | 'detailed'
  inputCount: number
  totalCount: number
  completionRate: number
  lastUpdate: string
}

// 考核统计类型
export interface AssessmentStats {
  totalStudents: number
  totalAssessments: number
  pendingInput: number
  completionRate: number
}

// 学生成绩类型
export interface StudentGrade {
  id: string
  studentNumber: string
  name: string
  score?: number
  totalScore?: number
  questionScores: (number | null)[]
  status: 'completed' | 'pending' | 'not_started'
}

// 题目类型
export interface Question {
  id: number
  title: string
  maxScore: number
  type: 'choice' | 'blank' | 'short' | 'programming' | 'essay'
}

// 录入模式类型
export type InputMode = 'direct' | 'detailed' 
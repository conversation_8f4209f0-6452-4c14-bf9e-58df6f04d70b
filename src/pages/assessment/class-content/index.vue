<template>
  <div class="class-assessment-list">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">
          <t-icon name="layers" />
          班级考核管理
        </h1>
        <p class="page-description">选择班级进入考核内容管理，查看和录入考核成绩</p>
      </div>
      <div class="header-actions">
        <t-button theme="default" variant="outline" @click="refreshData">
          <template #icon>
            <t-icon name="refresh" />
          </template>
          刷新
        </t-button>
      </div>
    </div>

    <!-- 班级列表 -->
    <div class="class-list-section">
      <div class="section-header">
        <h2>选择要管理的班级</h2>
        <div class="class-count">
          共 {{ classList.length }} 个班级
        </div>
      </div>
      
      <div class="class-grid">
        <t-loading :loading="loading">
          <div v-if="classList.length === 0" class="empty-state">
            <t-icon name="inbox" size="48px" />
            <p>暂无班级数据</p>
            <p class="empty-hint">请联系管理员添加班级信息</p>
          </div>
          <div v-else class="class-cards">
            <ClassCardWithSelection
              v-for="classItem in classList"
              :key="classItem.id"
              :worklist-data="classItem"
              @card-click="handleClassClick"
            />
          </div>
        </t-loading>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { MessagePlugin } from 'tdesign-vue-next'
import { ClassCardWithSelection } from './components'
import type { WorklistItem } from '@/api/base/classes'

// 路由相关
const route = useRoute()
const router = useRouter()

// 响应式数据
const loading = ref(false)

// 班级列表数据
const classList = ref<WorklistItem[]>([])

// 模拟班级数据
const mockClassList: WorklistItem[] = [
  {
    id: '1',
    courseId: '1',
    courseName: '数据结构与算法',
    taskNumber: 1,
    classId: '1',
    className: 'RB软工数231',
    teacherId: '1001',
    teacherName: '张教授',
    teacherTitle: '教授',
    teacherAcademyName: '计算机科学与技术学院',
    taskYear: 2024,
    taskTerm: 1,
    studentCount: 52,
    teachWeek: 18,
    weekHours: 4,
    totalHours: 72,
    courseLeaderId: '1001',
    courseLeaderName: '张教授',
    scheduleInfo: [
      {
        time: '周一 8:00-11:40',
        location: '教学楼A-301'
      },
      {
        time: '周三 14:00-15:40',
        location: '实验楼B-205'
      }
    ]
  },
  {
    id: '2',
    courseId: '1',
    courseName: '数据结构与算法',
    taskNumber: 2,
    classId: '2',
    className: 'RB软工数232',
    teacherId: '1002',
    teacherName: '李副教授',
    teacherTitle: '副教授',
    teacherAcademyName: '计算机科学与技术学院',
    taskYear: 2024,
    taskTerm: 1,
    studentCount: 48,
    teachWeek: 18,
    weekHours: 4,
    totalHours: 72,
    courseLeaderId: '1001',
    courseLeaderName: '张教授',
    scheduleInfo: [
      {
        time: '周三 14:00-17:40',
        location: '教学楼B-205'
      }
    ]
  },
  {
    id: '3',
    courseId: '1',
    courseName: '数据结构与算法',
    taskNumber: 3,
    classId: '3',
    className: 'RB软工数233',
    teacherId: '1001',
    teacherName: '张教授',
    teacherTitle: '教授',
    teacherAcademyName: '计算机科学与技术学院',
    taskYear: 2024,
    taskTerm: 1,
    studentCount: 56,
    teachWeek: 18,
    weekHours: 4,
    totalHours: 72,
    courseLeaderId: '1001',
    courseLeaderName: '张教授',
    scheduleInfo: [
      {
        time: '周五 8:00-11:40',
        location: '教学楼C-102'
      },
      {
        time: '周五 14:00-15:40',
        location: '教学楼C-108'
      }
    ]
  }
]

// 加载班级数据
const loadClassData = async () => {
  loading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))
    classList.value = [...mockClassList]
  } catch (error) {
    console.error('加载班级数据失败:', error)
    MessagePlugin.error('加载班级数据失败')
  } finally {
    loading.value = false
  }
}

// 刷新数据
const refreshData = () => {
  loadClassData()
}

// 班级卡片点击
const handleClassClick = (classId: string | number) => {
  console.log('点击班级:', classId, typeof classId)
  
  // 检查当前路由是否在教师课程模块下
  const isTeacherCourse = route.path.includes('/teacher/course')
  const courseId = route.params.courseId as string
  
  if (isTeacherCourse && courseId) {
    // 如果在教师课程模块下，使用教师课程的路由
    router.push({
      name: 'TeacherCourseAssessmentClassContent',
      params: { courseId, classId: String(classId) },
      query: { from: route.fullPath }
    })
  } else {
    // 否则使用通用的班级考核管理路由
    router.push({
      name: 'ClassAssessmentManagement',
      params: { classId: String(classId) },
      query: { from: route.fullPath }
    })
  }
}

// 页面初始化
onMounted(() => {
  loadClassData()
})
</script>

<style lang="less" scoped>
.class-assessment-list {
  padding: 24px;
  background: var(--td-bg-color-page);
  min-height: 100vh;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 24px;

    .header-content {
      .page-title {
        display: flex;
        align-items: center;
        gap: 8px;
        margin: 0 0 8px 0;
        font-size: 24px;
        font-weight: 600;
        color: var(--td-text-color-primary);
      }

      .page-description {
        margin: 0;
        color: var(--td-text-color-secondary);
        font-size: 14px;
      }
    }

    .header-actions {
      display: flex;
      gap: 12px;
    }
  }

  .class-list-section {
    background: var(--td-bg-color-container);
    border-radius: 6px;
    padding: 24px;
    box-shadow: var(--td-shadow-1);

    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;

      h2 {
        margin: 0;
        font-size: 18px;
        font-weight: 600;
        color: var(--td-text-color-primary);
      }

      .class-count {
        color: var(--td-text-color-secondary);
        font-size: 14px;
      }
    }

    .class-grid {
      .empty-state {
        text-align: center;
        padding: 60px 20px;
        color: var(--td-text-color-secondary);

        .t-icon {
          color: var(--td-text-color-placeholder);
          margin-bottom: 16px;
        }

        p {
          margin: 0 0 8px 0;
          font-size: 16px;
          font-weight: 500;
        }

        .empty-hint {
          font-size: 14px;
          color: var(--td-text-color-placeholder);
          margin: 0;
        }
      }

      .class-cards {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
        gap: 20px;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .class-assessment-list {
    padding: 16px;

    .page-header {
      flex-direction: column;
      gap: 16px;
      align-items: stretch;
    }

    .class-list-section {
      padding: 16px;

      .section-header {
        flex-direction: column;
        gap: 8px;
        align-items: flex-start;
      }

      .class-grid .class-cards {
        grid-template-columns: 1fr;
      }
    }
  }
}
</style> 
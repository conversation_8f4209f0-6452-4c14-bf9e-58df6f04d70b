<template>
  <div 
    class="class-card-with-selection" 
    :class="{ 'current-class': isCurrent }"
    @click="handleCardClick"
  >
    <!-- 当前班级角标 -->
    <div v-if="isCurrent" class="current-class-corner">
      <div class="corner-content">
        <t-icon name="check-circle-filled" size="12px" />
        <span>当前</span>
      </div>
    </div>

    <div class="card-header">
      <div class="class-info">
        <h3 class="class-name">
          {{ classData.className }}
          <t-tag theme="primary" size="small" class="grade-tag">{{ classData.entranceYear }}级</t-tag>
          <t-tag theme="success" size="small" class="count-tag">{{ classData.studentCount }}人</t-tag>
        </h3>
      </div>
    </div>

    <!-- 教师信息单独放在header外面，保持完整宽度 -->
    <div class="teacher-info">
      <t-avatar size="small" class="teacher-avatar">
        {{ teacherData.name.charAt(0) }}
      </t-avatar>
      <span class="teacher-name">{{ teacherData.name }}</span>
      <span class="teacher-label">授课教师</span>
    </div>

    <div class="card-content">
      <!-- 上课安排信息 -->
      <div class="schedule-section" v-if="scheduleData.length > 0">
        <div class="section-title">
          <t-icon name="time" size="16px" />
          <span>上课安排</span>
        </div>
        <div class="schedule-info">
          <div class="schedule-item" v-for="(item, index) in scheduleData" :key="index">
            <div class="schedule-time">{{ item.time }}</div>
            <div class="schedule-location">{{ item.location }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部区域 -->
    <div class="card-footer">
      <!-- 课程统计信息 -->
      <div class="stats-section">
        <div class="stat-item">
          <span class="stat-label">总学时</span>
          <span class="stat-value">{{ worklistData.totalHours }}h</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">周学时</span>
          <span class="stat-value">{{ worklistData.weekHours }}h</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">授课周数</span>
          <span class="stat-value">{{ worklistData.teachWeek }}周</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import {
  Tag as TTag,
  Button as TButton,
  Icon as TIcon,
  Avatar as TAvatar
} from 'tdesign-vue-next'
import type { WorklistItem } from '@/api/base/classes'

// 定义 props
interface Props {
  worklistData: WorklistItem
  currentClassId?: string | number
}

const props = withDefaults(defineProps<Props>(), {
  currentClassId: ''
})

// 定义事件
const emit = defineEmits<{
  cardClick: [classId: string | number]
}>()

// 计算属性 - 是否为当前班级
const isCurrent = computed(() => {
  return String(props.worklistData.classId) === String(props.currentClassId)
})

// 计算属性 - 班级数据
const classData = computed(() => ({
  className: props.worklistData.className,
  entranceYear: Math.floor(props.worklistData.taskYear - (props.worklistData.taskTerm - 1) / 2),
  studentCount: props.worklistData.studentCount
}))

// 计算属性 - 教师数据
const teacherData = computed(() => ({
  name: props.worklistData.teacherName,
  title: props.worklistData.teacherTitle,
  academyName: props.worklistData.teacherAcademyName,
  avatar: '' // 暂时为空，后续可以添加头像功能
}))

// 计算属性 - 上课安排数据
const scheduleData = computed(() => {
  const schedule = props.worklistData.scheduleInfo
  if (Array.isArray(schedule)) {
    return schedule
  } else if (schedule) {
    return [schedule]
  }
  return []
})

// 事件处理函数
const handleCardClick = () => {
  emit('cardClick', props.worklistData.classId)
}
</script>

<style lang="less" scoped>
.class-card-with-selection {
  background: var(--td-bg-color-container);
  border: 1px solid var(--td-border-level-1-color);
  border-radius: 8px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;

  &:hover {
    border-color: var(--td-brand-color);
    box-shadow: 0 4px 16px rgba(0, 82, 217, 0.1);
    transform: translateY(-2px);
  }

  &.current-class {
    border: 2px solid var(--td-warning-color);
    box-shadow: var(--td-shadow-2), 0 0 0 4px var(--td-warning-color-1);
    
    .class-name {
      color: var(--td-warning-color);
    }

    &:hover {
      box-shadow: var(--td-shadow-3), 0 0 0 4px var(--td-warning-color-1), 0 0 20px var(--td-warning-color-2);
    }
  }

  .current-class-corner {
    position: absolute;
    top: -5px;
    left: -5px;
    width: 48px;
    height: 48px;
    overflow: hidden;
    z-index: 4;
    border-top-left-radius: 8px;

    .corner-content {
      position: absolute;
      top: 10px;
      left: -16px;
      width: 70px;
      height: 20px;
      background: linear-gradient(135deg, var(--td-warning-color), var(--td-warning-color-8));
      color: var(--td-text-color-anti);
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 3px;
      font-size: 10px;
      font-weight: 600;
      transform: rotate(-45deg);
      box-shadow: 0 2px 8px rgba(255, 153, 0, 0.3);

      span {
        font-size: 9px;
        letter-spacing: 0.5px;
      }
    }
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 16px;
    position: relative;
    z-index: 1;

    .class-info {
      flex: 1;

      .class-name {
        margin: 0;
        font-size: 18px;
        font-weight: 600;
        color: var(--td-text-color-primary);
        line-height: 1.4;
        display: flex;
        align-items: center;
        gap: 8px;
        flex-wrap: wrap;
        transition: color 0.3s ease;

        .grade-tag,
        .count-tag {
          font-size: 12px;
          margin-left: 0;
        }
      }
    }
  }

  .teacher-info {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 16px;
    padding: 8px 12px;
    background: var(--td-bg-color-page);
    border-radius: 6px;
    border-left: 3px solid var(--td-brand-color);

    .teacher-avatar {
      flex-shrink: 0;
    }

    .teacher-name {
      color: var(--td-text-color-primary);
      font-weight: 500;
      font-size: 14px;
      flex: 1;
    }

    .teacher-label {
      color: var(--td-text-color-placeholder);
      font-size: 12px;
      background: var(--td-bg-color-container);
      padding: 2px 6px;
      border-radius: 3px;
    }
  }

  .card-content {
    flex: 1;
    margin-bottom: 16px;

    .schedule-section {
      .section-title {
        display: flex;
        align-items: center;
        gap: 6px;
        font-size: 14px;
        font-weight: 500;
        color: var(--td-text-color-secondary);
        margin-bottom: 8px;
      }

      .schedule-info {
        .schedule-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          font-size: 13px;
          color: var(--td-text-color-secondary);
          margin-bottom: 4px;
          padding: 4px 8px;
          background: var(--td-bg-color-page);
          border-radius: 4px;

          &:last-child {
            margin-bottom: 0;
          }

          .schedule-time {
            color: var(--td-text-color-primary);
            font-weight: 500;
          }

          .schedule-location {
            color: var(--td-text-color-secondary);
          }
        }
      }
    }
  }

  .card-footer {
    border-top: 1px solid var(--td-border-level-1-color);
    padding-top: 16px;
    flex-shrink: 0;

    .stats-section {
      display: flex;
      justify-content: space-between;
      padding: 12px;
      background: var(--td-bg-color-page);
      border-radius: 6px;
      margin-bottom: 16px;

      .stat-item {
        text-align: center;

        .stat-label {
          display: block;
          font-size: 12px;
          color: var(--td-text-color-secondary);
          margin-bottom: 4px;
        }

        .stat-value {
          font-size: 14px;
          font-weight: 600;
          color: var(--td-text-color-primary);
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .class-card-with-selection {
    padding: 16px;

    .card-header .class-info .class-name {
      font-size: 16px;
    }

    .stats-section {
      padding: 8px;

      .stat-item .stat-value {
        font-size: 13px;
      }
    }
  }
}
</style> 
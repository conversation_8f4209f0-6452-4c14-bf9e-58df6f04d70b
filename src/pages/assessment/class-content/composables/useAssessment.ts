import { ref, computed } from 'vue'
import { MessagePlugin } from 'tdesign-vue-next'
import type { AssessmentContent, AssessmentStats, StudentGrade } from '../types'

export function useAssessment() {
  // 响应式数据
  const loading = ref(false)
  const assessmentContents = ref<AssessmentContent[]>([])
  const assessmentStats = ref<AssessmentStats>({
    totalStudents: 0,
    totalAssessments: 0,
    pendingInput: 0,
    completionRate: 0
  })

  // 加载考核数据
  const loadAssessmentData = async (classId: string) => {
    loading.value = true
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 500))
      
      // 模拟数据
      const mockData: AssessmentContent[] = [
        {
          id: '1',
          sectionName: '期中考试',
          title: '数据结构期中测试',
          inputMode: 'direct',
          inputCount: 45,
          totalCount: 52,
          completionRate: 87,
          lastUpdate: '2024-11-20 14:30:00'
        },
        {
          id: '2',
          sectionName: '课程作业',
          title: '链表实现作业',
          inputMode: 'detailed',
          inputCount: 38,
          totalCount: 52,
          completionRate: 73,
          lastUpdate: '2024-11-19 16:45:00'
        },
        {
          id: '3',
          sectionName: '实验',
          title: '二叉树遍历实验',
          inputMode: 'detailed',
          inputCount: 52,
          totalCount: 52,
          completionRate: 100,
          lastUpdate: '2024-11-18 10:20:00'
        }
      ]
      
      assessmentContents.value = mockData
      
      // 计算统计数据
      const totalAssessments = mockData.length
      const totalStudents = 52 // 从班级信息获取
      const pendingInput = mockData.reduce((sum, item) => sum + (item.totalCount - item.inputCount), 0)
      const totalInputs = mockData.reduce((sum, item) => sum + item.inputCount, 0)
      const totalPossible = mockData.reduce((sum, item) => sum + item.totalCount, 0)
      const completionRate = totalPossible > 0 ? Math.round((totalInputs / totalPossible) * 100) : 0
      
      assessmentStats.value = {
        totalStudents,
        totalAssessments,
        pendingInput,
        completionRate
      }
      
    } catch (error) {
      console.error('加载考核数据失败:', error)
      MessagePlugin.error('加载考核数据失败')
    } finally {
      loading.value = false
    }
  }

  // 获取进度条主题色
  const getProgressTheme = (ratio: number) => {
    if (ratio >= 0.8) return 'success'
    if (ratio >= 0.5) return 'warning'
    return 'danger'
  }

  // 格式化日期
  const formatDate = (dateStr: string) => {
    return dateStr.split(' ')[0]
  }

  // 格式化时间
  const formatTime = (dateStr: string) => {
    return dateStr.split(' ')[1]
  }

  return {
    loading,
    assessmentContents,
    assessmentStats,
    loadAssessmentData,
    getProgressTheme,
    formatDate,
    formatTime
  }
}

export function useGradeManagement() {
  // 响应式数据
  const students = ref<StudentGrade[]>([])
  const loading = ref(false)
  const saving = ref(false)

  // 加载学生数据
  const loadStudentData = async (classId: string, assessmentId: string) => {
    loading.value = true
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 500))
      
      // 模拟学生数据
      const mockStudents: StudentGrade[] = Array.from({ length: 52 }, (_, index) => ({
        id: String(index + 1),
        studentNumber: `2021${String(index + 1).padStart(3, '0')}`,
        name: `学生${index + 1}`,
        score: Math.random() > 0.3 ? Math.round(Math.random() * 40 + 60) : undefined,
        totalScore: undefined as number | undefined,
        questionScores: new Array(4).fill(null),
        status: Math.random() > 0.3 ? 'completed' : 'pending'
      }))
      
      students.value = mockStudents
      
    } catch (error) {
      console.error('加载学生数据失败:', error)
      MessagePlugin.error('加载学生数据失败')
    } finally {
      loading.value = false
    }
  }

  // 保存成绩
  const saveGrades = async (inputMode: 'direct' | 'detailed') => {
    saving.value = true
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      MessagePlugin.success('成绩保存成功')
      return true
      
    } catch (error) {
      console.error('保存成绩失败:', error)
      MessagePlugin.error('保存成绩失败')
      return false
    } finally {
      saving.value = false
    }
  }

  // 计算总分
  const calculateTotalScore = (student: StudentGrade) => {
    const total = student.questionScores.reduce((sum, score) => {
      return sum + (score || 0)
    }, 0)
    student.totalScore = total
  }

  // 检查行是否完成录入
  const isRowCompleted = (student: StudentGrade, inputMode: 'direct' | 'detailed') => {
    if (inputMode === 'direct') {
      return student.score !== null && student.score !== undefined
    } else {
      return student.totalScore !== null && student.totalScore !== undefined
    }
  }

  return {
    students,
    loading,
    saving,
    loadStudentData,
    saveGrades,
    calculateTotalScore,
    isRowCompleted
  }
} 
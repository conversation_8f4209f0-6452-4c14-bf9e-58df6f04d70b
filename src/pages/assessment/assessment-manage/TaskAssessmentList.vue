<template>
  <div class="assessment-content-publish">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">
          考核环节概览
          <span v-if="displayCourseInfo" class="course-info">
            - <span class="course-name">{{ displayCourseInfo.courseName }}</span>
            <span class="course-code">（{{ displayCourseInfo.courseCode }}）</span>
            <span class="semester-info">- {{ displayCourseInfo.semesterText }}</span>
          </span>
        </h1>
      </div>
    </div>

    <!-- 考核环节概览（只读） -->
    <div class="assessment-sections-overview">

      <t-loading :loading="loading">
        <div v-if="assessmentSections.length === 0" class="empty-state">
          <t-icon name="inbox" size="48px" />
          <p>暂无考核环节配置</p>
        </div>

        <div v-else class="section-cards">
          <div
            v-for="section in assessmentSections"
            :key="section.id"
            class="section-card"
            :class="{ active: selectedSectionId === section.id }"
            @click="selectSection(section)"
          >
            <div class="card-header">
              <div class="section-info">
                <h4 class="section-name">{{ section.name }}</h4>
              </div>
                              <div class="section-weight">
                  <span class="weight-value">{{ section.weight }}%</span>
                </div>
            </div>

            <div class="card-content">
              <div class="section-description">
                {{ section.description || '暂无描述' }}
              </div>

                              <!-- 课程目标占比 -->
                <div class="objective-distribution">
                  <h5>课程目标占比</h5>
                  <div class="objective-tags">
                    <t-tooltip
                      v-for="objective in section.objectiveDistribution"
                      :key="objective.objectiveId"
                      placement="top"
                      :destroy-on-close="false"
                      :show-arrow="false"
                      :overlay-style="{
                        maxWidth: '320px',
                        backgroundColor: 'transparent',
                        border: 'none',
                        boxShadow: 'none',
                        padding: '0',
                        pointerEvents: 'none'
                      }"
                      theme="light"
                    >
                      <template #content>
                        <div class="objective-tooltip-content">
                          <div class="objective-tooltip-title">
                            {{ getObjectiveName(objective.objectiveId) }}
                          </div>
                          <div class="objective-tooltip-description">
                            {{ getObjectiveDescription(objective.objectiveId) || '暂无具体描述' }}
                          </div>
                        </div>
                      </template>
                      <t-tag
                        size="small"
                        :theme="selectedSectionId === section.id ? 'primary' : 'default'"
                        :variant="selectedSectionId === section.id ? 'light' : 'outline'"
                        class="objective-tag"
                        :class="{ 'selected': selectedSectionId === section.id }"
                      >
                        {{ objective.name }}
                        <span class="percentage-separator">·</span>
                        <span class="percentage">{{ objective.percentage }}%</span>
                      </t-tag>
                    </t-tooltip>
                  </div>
                </div>
            </div>
          </div>
        </div>
      </t-loading>
    </div>

    <!-- 考核内容管理 -->
    <div class="assessment-content-management">
      <div class="content-header">
        <h3>考核内容管理</h3>
        <div class="header-actions">
          <t-button theme="primary" @click="handleCreateContent">
            <template #icon>
              <t-icon name="add" />
            </template>
            新建考核内容
          </t-button>
        </div>
      </div>

      <!-- Tab切换 -->
      <t-tabs
        v-model:value="activeTab"
        @change="handleTabChange"
        class="content-tabs"
      >
        <t-tab-panel
          v-for="section in assessmentSections"
          :key="section.id"
          :value="section.id"
          :label="section.name"
        >
          <!-- 考核内容表格 -->
          <div class="content-table-container">
            <t-loading :loading="contentLoading">
              <div v-if="assessmentContents.length === 0" class="empty-content">
                <t-icon name="file" size="48px" />
                <p>该考核环节暂无考核内容</p>
                <t-button theme="primary" variant="outline" @click="handleCreateContent">
                  创建考核内容
                </t-button>
              </div>

              <t-table
                v-else
                :data="assessmentContents"
                :columns="contentTableColumns"
                :pagination="contentPagination"
                row-key="id"
                hover
                size="medium"
                :bordered="false"
                :row-class-name="getTableRowClassName"
                @page-change="handleContentPageChange"
              >
                <template #creator="{ row }">
                  <div class="creator-info">
                    <span class="creator-name">{{ row.creatorName || row.creator }}</span>
                  </div>
                </template>

                <template #term="{ row }">
                  <span class="term-value">{{ row.assessmentYear }}-{{ row.assessmentTerm === 1 ? '春季学期' : '秋季学期' }}</span>
                </template>

                <template #achievement="{ row }">
                  <t-tag
                    size="small"
                    :theme="row.achievement ? 'success' : 'default'"
                    :variant="row.achievement ? 'light' : 'outline'"
                  >
                    {{ row.achievement ? '参与' : '不参与' }}
                  </t-tag>
                </template>

                <template #examTime="{ row }">
                  <div class="exam-time-info">
                    <div v-if="row.examTime" class="exam-time">
                      <div class="time-item">
                        <span class="time-label">开始:</span>
                        <t-tooltip :content="formatTimeDetail(row.examTime.startTime)" placement="top">
                          <span class="time-value">{{ formatDateOnly(row.examTime.startTime) }}</span>
                        </t-tooltip>
                      </div>
                      <div class="time-item">
                        <span class="time-label">结束:</span>
                        <t-tooltip :content="formatTimeDetail(row.examTime.endTime)" placement="top">
                          <span class="time-value">{{ formatDateOnly(row.examTime.endTime) }}</span>
                        </t-tooltip>
                      </div>
                    </div>
                    <div v-else class="no-exam-time">
                      <span class="no-time-text">未设置考核时间</span>
                    </div>
                  </div>
                </template>

                <template #scoreType="{ row }">
                  <t-tag
                    size="small"
                    :theme="row.scoreType === 0 ? 'primary' : 'success'"
                    :variant="row.scoreType === 0 ? 'light' : 'light'"
                  >
                    <template #icon>
                      <t-icon :name="row.scoreType === 0 ? 'edit' : 'layers'" />
                    </template>
                    {{ getInputModeText(row.scoreType) }}
                  </t-tag>
                </template>

                <template #status="{ row }">
                  <t-tag size="small" :theme="getContentStatusTheme(row.status) as any">
                    {{ getContentStatusTextSync(row.status) }}
                  </t-tag>
                </template>

                <template #actions="{ row }">
                  <t-space>
                    <t-button
                      theme="primary"
                      variant="text"
                      size="small"
                      @click="handleViewDetail(row)"
                    >
                      详情
                    </t-button>
                    <t-button
                      theme="primary"
                      variant="text"
                      size="small"
                      :disabled="row.source === 'unified'"
                      @click="handleEditContent(row)"
                    >
                      编辑
                    </t-button>
                    <t-button
                      theme="primary"
                      variant="text"
                      size="small"
                      :disabled="row.source === 'unified'"
                      @click="handleConfigContent(row)"
                    >
                      配置
                    </t-button>
                    <t-button
                      theme="success"
                      variant="text"
                      size="small"
                      :disabled="row.source === 'unified'"
                      @click="handlePublishContent(row)"
                    >
                      {{ row.status === 'published' ? '重新发布' : '发布' }}
                    </t-button>
                    <t-button
                      theme="danger"
                      variant="text"
                      size="small"
                      :disabled="row.source === 'unified'"
                      @click="handleDeleteContent(row)"
                    >
                      删除
                    </t-button>
                    <t-button
                      theme="primary"
                      variant="text"
                      size="small"
                      @click="handleGradeManagement(row)"
                    >
                      成绩管理
                    </t-button>
                  </t-space>
                </template>
              </t-table>
            </t-loading>
          </div>
        </t-tab-panel>
      </t-tabs>
    </div>
    <!-- 创建考核内容弹窗 -->
    <AddAssessmentDialog
      v-model:visible="addAssessmentDialogVisible"
      :course-id="currentCourseId"
      :task-id="selectedSectionId"
      :course-info="currentCourseInfo"
      :default-assessment-type="addAssessmentDialogDefaultType"
      @submit="handleAssessmentSubmit"
      @cancel="handleAssessmentCancel"
    />

    <!-- 编辑考核内容弹窗 -->
    <AddAssessmentDialog
      v-model:visible="editAssessmentDialogVisible"
      :course-id="currentCourseId"
      :task-id="selectedSectionId"
      :course-info="currentCourseInfo"
      :mode="'edit'"
      :assessmentData="editingContentData"
      @submit="handleAssessmentEditSubmit"
      @cancel="handleAssessmentEditCancel"
    />

    <!-- 直接录入配置弹窗组件 -->
    <DirectConfigDialog :data="directConfigData" />

    <!-- 详细录入配置全屏弹窗组件 -->
    <DetailedConfigDialog :data="detailedConfigData" />

    <!-- 考核内容详情弹窗 -->
    <t-dialog
      v-model:visible="detailDialogVisible"
      :header="`考核内容详情 - ${detailDialogData.assessmentName || detailDialogData.name || '-'}`"
      width="900px"
      :footer="false"
    >
      <div class="detail-dialog-content">
        <!-- 基本信息卡片 -->
        <div class="detail-section">
          <div class="section-header">
            <t-icon name="info-circle" />
            <span>基本信息</span>
          </div>
          <div class="section-content">
            <!-- 第一行：考核名称、录入方式、达成度计算 -->
            <div class="info-row">
              <div class="info-item">
                <span class="label">考核名称</span>
                <span class="value">{{ detailDialogData.assessmentName || detailDialogData.name || '-' }}</span>
              </div>
              <!-- 详情弹窗录入方式字段修正 -->
              <div class="info-item">
                <span class="label">录入方式</span>
                <span class="value">{{ getInputModeText(detailDialogData.scoreType) }}</span>
              </div>
              <div class="info-item">
                <span class="label">达成度计算</span>
                <span class="value">
                  <t-tag
                    size="small"
                    :theme="detailDialogData.achievement ? 'success' : 'default'"
                    :variant="detailDialogData.achievement ? 'light' : 'outline'"
                  >
                    {{ detailDialogData.achievement ? '参与' : '不参与' }}
                  </t-tag>
                </span>
              </div>
            </div>
            <!-- 第二行：考核描述 -->
            <div class="info-row description-row">
              <div class="info-item full-width">
                <span class="label">考核描述</span>
                <span class="value">{{ detailDialogData.description || '-' }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 考核配置卡片 -->
        <div class="detail-section">
          <div class="section-header">
            <t-icon name="setting" />
            <span>考核配置</span>
          </div>
          <div class="section-content">
            <div class="info-grid">
              <div class="info-item">
                <span class="label">考核方式</span>
                <span class="value">{{ getAssessmentMethodText(detailDialogData.assessmentMethod) }}</span>
              </div>
              <div class="info-item">
                <span class="label">考核学期</span>
                <span class="value">{{ getAssessmentTermText(detailDialogData.assessmentTerm) }}</span>
              </div>
              <div class="info-item">
                <span class="label">考核年份</span>
                <span class="value">{{ detailDialogData.assessmentYear || '-' }}</span>
              </div>
              <div class="info-item">
                <span class="label">考核权重</span>
                <span class="value">{{ detailDialogData.assessmentWeight ? `${detailDialogData.assessmentWeight}%` : '-' }}</span>
              </div>
              <div class="info-item">
                <span class="label">试卷总分</span>
                <span class="value">{{ detailDialogData.totalScore ? `${detailDialogData.totalScore}分` : '-' }}</span>
              </div>
              <div class="info-item">
                <span class="label">考核状态</span>
                <span class="value">
                  <t-tag size="small" :theme="getContentStatusTheme(detailDialogData.assessmentStatus) as any">
                    {{ getAssessmentStatusText(detailDialogData.assessmentStatus) }}
                  </t-tag>
                </span>
              </div>
            </div>
          </div>
        </div>

        <!-- 考核时间卡片 -->
        <div class="detail-section" v-if="detailDialogData.assessmentDate">
          <div class="section-header">
            <t-icon name="time" />
            <span>考核时间</span>
          </div>
          <div class="section-content">
            <div class="time-info">
              <div class="time-item">
                <span class="time-label">开始时间</span>
                <span class="time-value">{{ formatTimeDetail(detailDialogData.assessmentDate.startTime) }}</span>
              </div>
              <div class="time-item">
                <span class="time-label">结束时间</span>
                <span class="time-value">{{ formatTimeDetail(detailDialogData.assessmentDate.endTime) }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 分数配置卡片 -->
        <div class="detail-section" v-if="detailDialogData.assessmentDetail && detailDialogData.assessmentDetail.length > 0">
          <div class="section-header">
            <t-icon name="chart" />
            <span>分数配置</span>
          </div>
          <div class="section-content">
            <div class="score-config">
              <div
                v-for="(item, index) in detailDialogData.assessmentDetail"
                :key="index"
                class="score-item"
              >
                <div class="objective-info">
                  <span class="objective-name">{{ getObjectiveName(item.courseObjectiveId) }}</span>
                  <span class="objective-score">{{ item.totalScore }}分</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 记录信息卡片 -->
        <div class="detail-section">
          <div class="section-header">
            <t-icon name="user" />
            <span>记录信息</span>
          </div>
          <div class="section-content">
            <div class="info-row">
              <div class="info-item">
                <span class="label">创建者</span>
                <span class="value">{{ detailDialogData.creatorName || '-' }}</span>
              </div>
              <div class="info-item">
                <span class="label">创建时间</span>
                <span class="value">{{ formatTimeDetail(detailDialogData.createTime) }}</span>
              </div>
              <div class="info-item">
                <span class="label">修改者</span>
                <span class="value">{{ detailDialogData.modifierName || '-' }}</span>
              </div>
              <div class="info-item">
                <span class="label">修改时间</span>
                <span class="value">{{ formatTimeDetail(detailDialogData.modifyTime) }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </t-dialog>

    <!-- 发布考核内容弹窗 -->
    <t-dialog
      v-model:visible="publishDialogVisible"
      :header="`发布考核内容 - ${publishDialogData.content?.name || ''}`"
      width="90vw"
      :confirm-btn="{
        content: `发布到选中班级 (${publishDialogData.selectedCount})`,
        loading: publishSubmitting,
        disabled: publishDialogData.selectedCount === 0
      }"
      @confirm="handleConfirmPublish"
      @cancel="handleCancelPublish"
      @close="handleCancelPublish"
    >
      <div class="publish-dialog-content">
        <!-- 考核内容基本信息 -->
        <div class="content-info-section">
          <h4>考核内容信息</h4>
          <div class="content-info-card">
            <div class="info-row">
              <span class="label">考核名称：</span>
              <span class="value">{{ publishDialogData.content?.name }}</span>
            </div>
            <div class="info-row">
              <span class="label">考核描述：</span>
              <span class="value">{{ publishDialogData.content?.description }}</span>
            </div>
            <div class="info-row">
              <span class="label">录入方式：</span>
              <span class="value">{{ publishDialogData.content?.inputMode === 'direct' ? '直接录入' : '详细录入' }}</span>
            </div>
            <div class="info-row" v-if="publishDialogData.content?.examTime">
              <span class="label">考核时间：</span>
              <span class="value">
                {{ publishDialogData.content.examTime.startTime }} - {{ publishDialogData.content.examTime.endTime }}
              </span>
            </div>
          </div>
        </div>

        <!-- 班级选择区域 -->
        <div class="class-selection-section">
          <div class="section-header">
            <h4>选择发布班级</h4>
            <div class="header-actions">
              <!-- 搜索框 -->
              <t-input
                v-model="publishDialogData.searchKeyword"
                placeholder="搜索班级名称、专业或教师"
                style="width: 300px"
                clearable
              >
                <template #prefix-icon>
                  <t-icon name="search" />
                </template>
              </t-input>

              <!-- 操作按钮 -->
              <t-space>
                <t-button
                  theme="primary"
                  variant="outline"
                  size="small"
                  @click="handleSelectAllClasses(true)"
                >
                  全选
                </t-button>
                <t-button
                  theme="default"
                  variant="outline"
                  size="small"
                  @click="handleSelectAllClasses(false)"
                >
                  取消全选
                </t-button>
                <t-button
                  theme="warning"
                  variant="outline"
                  size="small"
                  @click="handleReverseSelection"
                >
                  反选
                </t-button>
              </t-space>
            </div>
          </div>

          <!-- 班级列表表格 -->
          <t-loading :loading="classTableLoading">
            <t-table
              :data="publishDialogData.filteredClasses"
              :columns="classTableColumns"
              row-key="classId"
              stripe
              hover
              size="small"
              max-height="400px"
            >
              <!-- 复选框列 -->
              <template #selection="{ row }">
                <t-checkbox
                  :checked="publishDialogData.selectedClasses.includes(row.classId)"
                  :disabled="row.publishStatus === 'published'"
                  @change="(checked) => handleClassSelectionChange(row.classId, checked)"
                />
              </template>

              <!-- 班级名称 -->
              <template #className="{ row }">
                <div class="class-name-cell">
                  <span class="class-name">{{ row.className }}</span>
                  <span class="major-name">{{ row.majorName }}</span>
                </div>
              </template>

              <!-- 学生人数 -->
              <template #studentCount="{ row }">
                <span class="student-count">{{ row.studentCount }}人</span>
              </template>

              <!-- 上课时间 -->
              <template #scheduleInfo="{ row }">
                <div class="schedule-info">
                  <div class="time-info">{{ row.scheduleInfo.classTime }}</div>
                  <div class="classroom-info">{{ row.scheduleInfo.classroom }}</div>
                </div>
              </template>

              <!-- 授课教师 -->
              <template #teacherInfo="{ row }">
                <div class="teacher-info">
                  <span class="teacher-name">{{ row.teacherInfo.teacherName }}</span>
                  <span class="teacher-role">{{ row.teacherInfo.role }}</span>
                </div>
              </template>

              <!-- 发布状态 -->
              <template #publishStatus="{ row }">
                <t-tag
                  :theme="row.publishStatus === 'published' ? 'success' :
                          row.publishStatus === 'ended' ? 'default' : 'warning'"
                  size="small"
                >
                  {{ row.publishStatus === 'published' ? '已发布' :
                     row.publishStatus === 'ended' ? '已结束' : '未发布' }}
                </t-tag>
                <div v-if="row.publishTime" class="publish-time">
                  {{ formatDateOnly(row.publishTime) }}
                </div>
              </template>

              <!-- 操作 -->
              <template #actions="{ row }">
                <t-button
                  v-if="row.publishStatus === 'published'"
                  theme="warning"
                  variant="text"
                  size="small"
                  @click="handleUnpublishClass(row.classId)"
                >
                  取消发布
                </t-button>
                <span v-else class="no-action">-</span>
              </template>
            </t-table>
          </t-loading>
        </div>

        <!-- 统计信息 -->
        <div class="statistics-section">
          <div class="statistics-card">
            <div class="stat-item">
              <span class="stat-label">已选择班级：</span>
              <span class="stat-value highlight">{{ publishDialogData.selectedCount }} 个</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">涉及学生：</span>
              <span class="stat-value highlight">{{ publishDialogData.selectedStudentCount }} 人</span>
            </div>
          </div>
        </div>
      </div>
    </t-dialog>
     <!-- 成绩管理弹窗 -->
     <GradeManagementDialog
      :visible="gradeManagementVisible"
      :assessmentContent="assessmentContent"
      :classInfo="classInfo"
      :scoreStatus="scoreStatus"
      :scoreParams="scoreParams"
      :visibleQuestionTypes="visibleQuestionTypes"
      :allQuestionTypes="allQuestionTypes"
      :allQuestionTypeNames="allQuestionTypeNames"
      @close="gradeManagementVisible = false"
    />
  </div>
</template>

<script setup lang="ts">
import { onMounted, reactive, ref, computed } from 'vue'
import { useRoute } from 'vue-router'
import { MessagePlugin, DialogPlugin } from 'tdesign-vue-next'
import DetailedConfigDialog from '@/pages/assessment/components/DetailedConfigDialog.vue'
import DirectConfigDialog from '@/pages/assessment/components/DirectConfigDialog.vue'
import AddAssessmentDialog from '@/pages/assessment/components/AddAssessmentDialog.vue'
import { useAssessmentContent } from '@/pages/assessment/AssessmentContent/composables/useAssessmentContent'
import { mockClassList } from '@/pages/assessment/AssessmentContent/data'
import { storage } from '@/utils/storage';
import { CourseCacheInfo } from '@/types/course';
import { getDictLabelByTypeTitle } from '@/utils/dictUtil';
import GradeManagementDialog from '@/pages/assessment/class-content/components/GradeManagementDialog.vue'

// 使用 composable
const {
  // 响应式数据
  loading,
  currentCourseInfo,
  assessmentSections,
  selectedSectionId,
  assessmentFormVisible,
  editingSection,
  submitting,
  courseObjectives,
  activeTab,
  contentLoading,
  assessmentContents,
  configFormVisible,
  configSubmitting,
  currentCourseId,
  publishDialogVisible,
  publishDialogData,
  publishSubmitting,
  classTableLoading,
  contentPagination,
  assessmentForm,
  contentTableColumns,
  currentContent,
  classTableColumns,
  setAssessmentType,
  currentAssessmentType,
  // 基础函数
  loadAssessmentData,
  loadAssessmentContents,
  // 初始化函数
  initialize,
  formatDateOnly,
  formatTimeDetail,
  // 发布相关函数
  handleConfirmPublish,
  handleCancelPublish,
  handleSelectAllClasses,
  handleReverseSelection,
  handleClassSelectionChange,
} = useAssessmentContent()

// 新增详细录入配置弹窗数据对象
const detailedConfigData = reactive({
  visible: false,
  description: '',
  title: '',
  courseId: '',
  sectionId: '',
  contentId: '',
  assessmentId: '',
  submitting: false
})

// 新增直接录入配置弹窗数据对象
const directConfigData = reactive({
  visible: false,
  content: null as any,
  courseObjectives: [] as any[],
  submitting: false
})

// 新增：读取课程缓存信息
const cachedCourseInfo = storage.get('lastCourseDetail') as CourseCacheInfo | null;

// 新增考核内容创建弹窗数据
const addAssessmentDialogVisible = ref(false);
const addAssessmentDialogDefaultType = ref<number | string>();

// 新增编辑弹窗数据
const editAssessmentDialogVisible = ref(false);
const editingContentData = ref<any>(null);

// 新增详情弹窗数据
const detailDialogVisible = ref(false);
const detailDialogData = ref<any>({});

// 成绩管理相关状态
const gradeManagementVisible = ref(false)
const assessmentContent = ref({ title: '期末考试', inputMode: 'detailed' })
const classInfo = ref({ className: 'RB软工数241', studentCount: 2 })
const scoreStatus = ref(1)

// 成绩参数
const scoreParams = ref({
  taskId: 0,
  assessmentId: 0
})

// 从路由参数获取 taskId
const route = useRoute()
const taskId = computed(() => {
  // 从路由参数中获取 taskId，如果没有则使用默认值
  return Number(route.params.taskId) || 0
})

// 题目类型相关状态
const visibleQuestionTypes = ref<string[]>([])
const allQuestionTypes = ref<string[]>([])
const allQuestionTypeNames = ref<Record<string, string>>({})



// 计算属性：显示课程信息（优先使用缓存）
const displayCourseInfo = computed(() => {
  const info = cachedCourseInfo || currentCourseInfo.value;
  if (!info) return null;

  // 构建学期信息
  let semesterText = '';
  if (info.academicYear && info.semester) {
    const semesterMap = { 1: '春季学期', 2: '秋季学期' };
    semesterText = `${info.academicYear} ${semesterMap[info.semester as keyof typeof semesterMap] || ''}`;
  }

  return {
    ...info,
    semesterText
  };
});



const handleCreateContent = () => {
  // 直接用currentCourseInfo，保证包含assessmentMethodList、学年、学期等
  // 传递当前tab的考核类型
  addAssessmentDialogDefaultType.value = assessmentSections.value.find(sec => sec.id === selectedSectionId.value)?.type.toString();
  addAssessmentDialogVisible.value = true;
}

// 获取课程目标名称
const getObjectiveName = (objectiveId: string) => {
  const objective = courseObjectives.value.find(obj => obj.id === objectiveId)
  return objective ? objective.objectiveName : ''
}

// 获取课程目标描述
const getObjectiveDescription = (objectiveId: string) => {
  const objective = courseObjectives.value.find(obj => obj.id === objectiveId)
  return objective ? objective.description : ''
}

// 根据sectionId获取assessmentType
const getAssessmentTypeBySectionId = (sectionId: string) => {
  const section = assessmentSections.value.find(sec => sec.id === sectionId)
  return section ? section.type : undefined
}

const selectSection = (section: any) => {
  selectedSectionId.value = section.id
  activeTab.value = section.id
  // 设置当前assessmentType
  const type = getAssessmentTypeBySectionId(section.id)
  setAssessmentType(type)
  // 选择考核环节时重新加载内容
  loadAssessmentContents(type)
}

const handleCreateAssessment = () => {
  editingSection.value = null
  Object.assign(assessmentForm, {
    name: '',
    type: '',
    weight: 0,
    description: '',
    inputMode: 'direct'
  })
  assessmentFormVisible.value = true
}

const handleTabChange = (value: any) => {
  selectedSectionId.value = value as string
  // 设置当前assessmentType
  const type = getAssessmentTypeBySectionId(value)
  setAssessmentType(type)
  // 切换tab时重新加载当前考核环节的内容
  loadAssessmentContents(type)
}

const handleContentPageChange = (pageInfo: any) => {
  Object.assign(contentPagination, {
    current: pageInfo.current,
    pageSize: pageInfo.pageSize
  })
  // 分页变化时重新加载数据，带上assessmentType
  loadAssessmentContents(currentAssessmentType.value)
}

const handleEditContent = (content: any) => {
  // 设置编辑数据
  console.log('handleEditContent - 设置编辑数据:', content);
  editingContentData.value = content;
  editAssessmentDialogVisible.value = true;
}

const handleViewDetail = (content: any) => {
  detailDialogData.value = content;
  detailDialogVisible.value = true;
}

const handleConfigContent = (content: any) => {
  // 修复linter错误：避免直接赋值只读属性
  currentContent.value = { ...content }

  // 根据录入模式选择不同的配置弹窗
  if (content.scoreType === 0) {
    // 直接录入模式：显示直接录入配置弹窗
    directConfigData.visible = true
    directConfigData.content = content
    directConfigData.courseObjectives = courseObjectives.value
    directConfigData.submitting = configSubmitting.value
    // 确保详细录入弹窗关闭
    detailedConfigData.visible = false
  } else if (content.scoreType === 1) {
    // 详细录入模式：显示详细录入配置全屏弹窗
    detailedConfigData.visible = true
    detailedConfigData.description = content.description
    detailedConfigData.title = `配置考核内容 - ${content.name}`
    detailedConfigData.courseId = currentCourseId.value
    detailedConfigData.sectionId = selectedSectionId.value
    detailedConfigData.contentId = content.id
    detailedConfigData.submitting = configSubmitting.value
    // 确保直接录入弹窗关闭
    directConfigData.visible = false
  }
}

const handlePublishContent = (content: any) => {
  publishDialogData.value.content = content
  publishDialogData.value.allClasses = mockClassList as any
  publishDialogData.value.filteredClasses = mockClassList as any
  publishDialogData.value.selectedClasses = []
  publishDialogData.value.searchKeyword = ''
  publishDialogData.value.selectedCount = 0
  publishDialogData.value.selectedStudentCount = 0
  publishDialogVisible.value = true
}

const handleDeleteContent = async (content: any) => {
  const confirmDialog = DialogPlugin.confirm({
    header: '确认删除',
    body: `确定要删除考核内容 "${content.name}" 吗？此操作不可逆。`,
    confirmBtn: '确认删除',
    cancelBtn: '取消',
    onConfirm: async () => {
      try {
        // TODO: 调用删除API
        // await deleteAssessment(content.id);
        MessagePlugin.success('考核内容删除成功');
        loadAssessmentContents();
        confirmDialog.destroy();
      } catch (error) {
        console.error('删除失败:', error);
        MessagePlugin.error('删除失败，请重试');
      }
    },
    onCancel: () => {
      console.log('取消删除');
      confirmDialog.destroy();
    },
  });
}

const handleSaveAssessment = async () => {
  try {
    submitting.value = true
    await new Promise(resolve => setTimeout(resolve, 1000))
    MessagePlugin.success(editingSection.value ? '考核环节更新成功' : '考核环节创建成功')
    assessmentFormVisible.value = false
    loadAssessmentData()
  } catch (error) {
    console.error('保存失败:', error)
    MessagePlugin.error('保存失败，请重试')
  } finally {
    submitting.value = false
  }
}

const handleCancelEdit = () => {
  assessmentFormVisible.value = false
  editingSection.value = null
  // 取消编辑时重置表单
  // TODO: 重置考核环节表单
}



const handleSaveConfig = async () => {
  try {
    configSubmitting.value = true
    await new Promise(resolve => setTimeout(resolve, 1000))
    MessagePlugin.success('配置保存成功')
    configFormVisible.value = false
    // 配置保存成功后重新加载数据
    loadAssessmentContents()
  } catch (error) {
    console.error('配置保存失败:', error)
    MessagePlugin.error('配置保存失败，请重试')
  } finally {
    configSubmitting.value = false
  }
}

const handleCancelConfig = () => {
  configFormVisible.value = false
  // 取消配置时重置表单
  // TODO: 重置配置表单
}

const getSourceTheme = (source: string) => {
  return source === 'unified' ? 'success' : 'primary'
}

const getInputModeText = (scoreType: number) => {
  return scoreType === 0 ? '直接录入' : '详细录入'
}

const getContentStatusTheme = (status: string) => {
  switch (status) {
    case 'published': return 'success'
    case 'ended': return 'default'
    default: return 'warning'
  }
}

const getContentStatusText = async (status: string) => {
  try {
    // 尝试从字典表获取状态文本
    const statusText = await getDictLabelByTypeTitle('考核状态', status);
    if (statusText) {
      return statusText;
    }
  } catch (error) {
    console.warn('无法从字典表获取状态文本，使用默认值:', error);
  }

  // 如果无法从字典表获取，使用默认映射
  switch (status) {
    case 'published': return '已发布'
    case 'ended': return '已结束'
    default: return '草稿'
  }
}

// 同步版本，用于模板中直接调用
const getContentStatusTextSync = (status: string) => {
  switch (status) {
    case 'published': return '已发布'
    case 'ended': return '已结束'
    default: return '草稿'
  }
}

// 获取考核方式文本
const getAssessmentMethodText = (method: number) => {
  const methodMap: Record<number, string> = {
    1: '考试',
    2: '考查',
    3: '实验',
    4: '课程设计',
    5: '实习',
    6: '毕业设计'
  }
  return methodMap[method] || '未知'
}

// 获取考核学期文本
const getAssessmentTermText = (term: number) => {
  const termMap: Record<number, string> = {
    1: '春季学期',
    2: '秋季学期'
  }
  return termMap[term] || '未知'
}

// 获取考核状态文本
const getAssessmentStatusText = (status: number) => {
  const statusMap: Record<number, string> = {
    0: '配置中',
    1: '编辑中',
    2: '进行中',
    3: '已结束'
  }
  return statusMap[status] || '未知'
}

const getDirectModeRowClassName = ({ row }: { row: any }) => {
  return row.objectiveId === 'total' ? 'total-row' : ''
}

// 表格行样式
const getTableRowClassName = ({ row }: { row: any }) => {
  const classes = ['modern-table-row']
  if (row.status === 'published') {
    classes.push('published-row')
  } else if (row.status === 'ended') {
    classes.push('ended-row')
  }
  return classes.join(' ')
}

const handleUnpublishClass = (classId: string) => {
  MessagePlugin.warning('取消发布功能待实现')
  // TODO: 实现取消发布功能
}

// 处理考核内容提交
const handleAssessmentSubmit = (data: any) => {
  console.log('考核内容创建成功:', data)
  // 重新加载考核内容列表
  loadAssessmentContents()
}

// 处理考核内容取消
const handleAssessmentCancel = () => {
  console.log('取消创建考核内容')
}

// 处理考核内容编辑提交
const handleAssessmentEditSubmit = (data: any) => {
  console.log('考核内容编辑成功:', data)
  // 重新加载考核内容列表
  loadAssessmentContents()
  editAssessmentDialogVisible.value = false;
}

// 处理考核内容编辑取消
const handleAssessmentEditCancel = () => {
  console.log('取消编辑考核内容')
  editAssessmentDialogVisible.value = false;
}

const handleGradeManagement = (row: any) => {
  console.log('handleGradeManagement - row:', row)
  
  // 设置考核内容信息
  assessmentContent.value = row
  
  // 设置成绩参数
  scoreParams.value = {
    taskId: taskId.value, // 使用从路由参数获取的 taskId
    assessmentId: row.id // 使用考核内容的ID作为assessmentId
  }
  
  // 设置班级信息（这里可以根据实际情况调整）
  classInfo.value = { 
    className: row.className || '默认班级', 
    studentCount: row.studentCount || 0 
  }
  
  // 显示成绩管理弹窗
  gradeManagementVisible.value = true
}

// 成绩管理相关事件处理函数
const handleSaveGradeChanges = () => {
  console.log('保存成绩变更')
  MessagePlugin.success('成绩变更已保存')
}

const handleCancelGradeChanges = () => {
  console.log('取消成绩变更')
  MessagePlugin.info('已取消成绩变更')
}

const handleSubmitGrades = () => {
  console.log('提交成绩')
  MessagePlugin.success('成绩提交成功')
}

const handleRefreshGrades = () => {
  console.log('刷新成绩数据')
  MessagePlugin.success('成绩数据已刷新')
}

// 初始化
onMounted(() => {
  initialize()
  // 页面初始化时，默认选中第一个tab并设置assessmentType
  if (assessmentSections.value.length > 0) {
    const firstSection = assessmentSections.value[0]
    selectSection(firstSection)
  }
})
</script>

<style lang="less" scoped>
@import './styles/common.less';

// 新增样式
.semester-info {
  color: var(--td-text-color-secondary);
  font-size: 14px;
  font-weight: normal;
}

.exam-time-info {
  .exam-time {
    .time-item {
      display: flex;
      align-items: center;
      margin-bottom: 4px;

      &:last-child {
        margin-bottom: 0;
      }

      .time-label {
        font-size: 12px;
        color: var(--td-text-color-secondary);
        margin-right: 8px;
        min-width: 40px;
      }

      .time-value {
        font-size: 12px;
        color: var(--td-text-color-primary);
      }
    }
  }

  .no-exam-time {
    .no-time-text {
      font-size: 12px;
      color: var(--td-text-color-placeholder);
      font-style: italic;
    }
  }
}

.detail-dialog-content {
  padding: 16px 0;
}

// 详情弹窗样式
.detail-section {
  margin-bottom: 24px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid var(--td-border-level-1-color);

  .section-header {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 16px;
    background: var(--td-brand-color);
    color: white;
    font-weight: 600;
    font-size: 14px;
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg,var(--td-brand-color)  0%,var(--td-brand-color-light)  100%);
      opacity: 0.8;
    }

    .t-icon,
    span {
      position: relative;
      z-index: 1;
    }

    .t-icon {
      font-size: 16px;
    }
  }

  .section-content {
    padding: 16px;
    background: var(--td-bg-color-container);
  }

  .info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
  }

  .info-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
    margin-bottom: 16px;

    &:last-child {
      margin-bottom: 0;
    }

    &.description-row {
      grid-template-columns: 1fr;
    }
  }

  .info-item {
    display: flex;
    flex-direction: column;
    gap: 4px;

    &.full-width {
      grid-column: 1 / -1;
    }

    .label {
      font-size: 12px;
      color: var(--td-text-color-secondary);
      font-weight: 500;
    }

    .value {
      font-size: 14px;
      color: var(--td-text-color-primary);
      font-weight: 500;
    }
  }

  .time-info {
    display: flex;
    flex-direction: column;
    gap: 12px;

    .time-item {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 12px;
      background: var(--td-bg-color-page);
      border-radius: 6px;
      border: 1px solid var(--td-border-level-1-color);

      .time-label {
        font-size: 12px;
        color: var(--td-text-color-secondary);
        min-width: 60px;
      }

      .time-value {
        font-size: 14px;
        color: var(--td-text-color-primary);
        font-weight: 500;
      }
    }
  }

  .score-config {
    display: flex;
    flex-direction: column;
    gap: 8px;

    .score-item {
      padding: 12px;
      background: var(--td-bg-color-page);
      border-radius: 6px;
      border-left: 4px solid var(--td-brand-color);
      border: 1px solid var(--td-border-level-1-color);

      .objective-info {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .objective-name {
          font-size: 14px;
          color: var(--td-text-color-primary);
          font-weight: 500;
        }

        .objective-score {
          font-size: 14px;
          color: var(--td-brand-color);
          font-weight: 600;
        }
      }
    }
  }
}

// 现代化表格样式
.modern-table-row {
  transition: all 0.2s ease;

  &:hover {
    background: linear-gradient(135deg, var(--td-brand-color-light) 0%, rgba(0, 82, 204, 0.02) 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 82, 204, 0.1);
  }
}

.published-row {
  background: linear-gradient(135deg, rgba(0, 135, 90, 0.05) 0%, rgba(0, 135, 90, 0.02) 100%);

  &:hover {
    background: linear-gradient(135deg, rgba(0, 135, 90, 0.08) 0%, rgba(0, 135, 90, 0.05) 100%);
  }
}

.ended-row {
  background: linear-gradient(135deg, rgba(128, 128, 128, 0.05) 0%, rgba(128, 128, 128, 0.02) 100%);

  &:hover {
    background: linear-gradient(135deg, rgba(128, 128, 128, 0.08) 0%, rgba(128, 128, 128, 0.05) 100%);
  }
}

// 录入方式标签样式
.creator-info {
  .creator-name {
    font-size: 14px;
    color: var(--td-text-color-primary);
    font-weight: 500;
  }
}

// 考核时间样式优化
.exam-time-info {
  .exam-time {
    .time-item {
      display: flex;
      align-items: center;
      margin-bottom: 4px;

      &:last-child {
        margin-bottom: 0;
      }

      .time-label {
        font-size: 12px;
        color: var(--td-text-color-secondary);
        margin-right: 8px;
        min-width: 40px;
      }

      .time-value {
        font-size: 12px;
        color: var(--td-text-color-primary);
        font-weight: 500;
      }
    }
  }

  .no-exam-time {
    .no-time-text {
      font-size: 12px;
      color: var(--td-text-color-placeholder);
      font-style: italic;
    }
  }
}
</style>

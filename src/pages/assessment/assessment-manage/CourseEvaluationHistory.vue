<template>
  <div class="course-evaluation-container">
    <!-- 头部信息 -->
    <div class="page-header">
      <div class="header-content">
        <div class="title-info">
          <h1 class="page-title">课程全部考核数据</h1>
          <!-- <p class="page-subtitle">管理课程考核任务与成绩评定</p> -->
        </div>
        <div class="header-actions">
          <t-button theme="primary" size="large" @click="openAddAssessmentDialog">
            <template #icon>
              <t-icon name="add" />
            </template>
            新增学期考核
          </t-button>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="page-content">
      <EvaluationTasksTab
        ref="evaluationTasksTabRef"
        :course-id="courseId"
        :task-id="taskId"
        :course-name="courseName"
        :from-module="fromModel"
        :course-info = "courseDetailInfo"
        @edit-exam="handleEditExam"
        @content-config="handleContentConfig"
      />
    </div>
    
    <!-- 编辑考核对话框 -->
    <EditExamDialog
      v-model:visible="editExamDialogVisible"
      :exam-info="currentEditExam ? {
        courseName: currentEditExam.courseName,
        semester: currentEditExam.semester,
        examType: '期末考核'
      } : null"
      :score-type="currentEditExam?.scoreType"
      @confirm="handleExamConfigConfirm"
    />

    <!-- 直接录入模式-考核内容配置对话框 -->
    <DirectEntryConfigDialog
      v-if="currentTaskForContentConfig"
      v-model:visible="directEntryDialogVisible"
      :course-info="{ 
        id: String(courseId), 
        name: currentTaskForContentConfig.courseName,
        semester: currentTaskForContentConfig.semester
      }"
      :task-id="String(currentTaskForContentConfig.id)"
      :assessment-id="String(currentTaskForContentConfig.id)"
      :course-objectives="courseObjectives"
      :initial-scores="currentTaskForContentConfig.objectiveScores"
      :exam-status="currentTaskForContentConfig.examStatus"
      @save="handleContentConfigSave"
    />
    
    <!-- 添加考核内容对话框 -->
    <AddAssessmentDialog
      v-model:visible="addAssessmentDialogVisible"
      :course-id="courseId"
      :task-id="taskId"
      :from-module="fromModel"
      :default-assessment-type="defaultAssessmentType"
      @submit="handleAddAssessmentSubmit"
      @cancel="addAssessmentDialogVisible = false"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { useRoute } from 'vue-router';
import {  MessagePlugin } from 'tdesign-vue-next';
import EditExamDialog from './components/EditExamDialog.vue';
import DirectEntryConfigDialog from '@/pages/assessment/components/DirectEntryConfigDialog.vue';
import EvaluationTasksTab from './components/EvaluationTasksTab.vue';
import AddAssessmentDialog from '@/pages/assessment/components/AddAssessmentDialog.vue';

import { type AssessmentInfo } from '@/api/assessment/assessment';

import { type CourseDetailInfo, getCourseBaseInfo } from '@/api/training/course';
const route = useRoute();
// 确保类型转换正确
const fromModel = ref('history'); // 默认值为 'history'
const courseId = computed(() => {
  const id = route.params.courseId;
  return typeof id === 'string' ? id : (Array.isArray(id) ? id[0] : '');
});
const courseDetailInfo = ref<CourseDetailInfo>(); // 用于存储课程基本信息
const taskId = computed(() => {
  const id = route.params.taskId;
  return id ? (typeof id === 'string' ? id : (Array.isArray(id) ? id[0] : '')) : '-1';
});

// 从本地缓存查询参数中获取其他参数
const courseName = ref('未知课程');



const addAssessmentDialogVisible = ref(false);
const defaultAssessmentType = ref('');
const courseObjectives = ref<{ id: string; identifier: string; description: string; number: number }[]>([]);
const evaluationTasksTabRef = ref<InstanceType<typeof EvaluationTasksTab>>();

// 编辑考核对话框相关状态
const editExamDialogVisible = ref(false);
const currentEditExam = ref<AssessmentInfo | null>(null);

// 直接录入内容配置对话框相关状态
const directEntryDialogVisible = ref(false);
const currentTaskForContentConfig = ref<AssessmentInfo | null>(null);

const openAddAssessmentDialog = () => {
  addAssessmentDialogVisible.value = true;
};

const handleAddAssessmentSubmit = (assessmentData: any) => {
  if (evaluationTasksTabRef.value) {
    evaluationTasksTabRef.value.addNewAssessmentTask(assessmentData);
    MessagePlugin.success('成功创建新的考核内容');
  }
  addAssessmentDialogVisible.value = false;
};

const handleEditExam = (row: AssessmentInfo) => {
  currentEditExam.value = row;
  editExamDialogVisible.value = true;
};

const handleContentConfig = (row: AssessmentInfo) => {
  console.log('点击考核内容按钮，任务数据:', row);
  console.log('考核模式:', row.scoreType === 0 ? '直接录入' : '详细录入');
  console.log('考核状态:', row.assessmentStatus);

  // 直接录入模式：打开直接录入配置对话框
  if (row.scoreType === 0) {
    console.log('准备打开直接录入配置对话框');
    currentTaskForContentConfig.value = row;
    directEntryDialogVisible.value = true;
    return;
  }

  // 如果不是直接录入模式，这个方法不应该被调用
  MessagePlugin.warning('该功能仅适用于"直接录入"模式的考核');
};

const handleExamConfigConfirm = (config: { configMode: number }) => {
  if (currentEditExam.value && evaluationTasksTabRef.value) {
    // 通过子组件的方法更新数据
    evaluationTasksTabRef.value.updateTask(currentEditExam.value.id, {
      scoreType: config.configMode as 0 | 1,
      assessmentStatus: 1 // 配置完成后，状态变为编辑中
    });
  }
  editExamDialogVisible.value = false;
};

onMounted(async () => {
  try {
    courseDetailInfo.value = await getCourseBaseInfo(Number(courseId.value))
    .then(res => res.data);
    console.log('history课程基本信息:', courseDetailInfo.value);
    fromModel.value = 'history';
  } catch (err) {
    console.error('课程目标获取失败:', err);
  }
});
</script>

<style lang="less" scoped>
.course-evaluation-container {
  padding: 10px 0;
  
  .page-header {
    background: white;
    border-radius: 12px;
    padding: 20px 16px;
    margin-bottom: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    border: 1px solid rgba(0, 0, 0, 0.06);
    
    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      
      .title-info {
        .page-title {
          font-size: 24px;
          font-weight: 600;
          margin: 0 0 8px 0;
          color: var(--td-text-color-primary);
        }
        
        .page-subtitle {
          font-size: 14px;
          color: var(--td-text-color-secondary);
          margin: 0;
        }
      }
      
      .header-actions {
        display: flex;
        align-items: center;
        gap: 12px;
      }
    }
  }
  
  .page-content {
    :deep(.t-tabs__nav) {
      margin-bottom: 16px;
    }
  }
}

// 动画效果
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .course-evaluation-container {
    .page-content {
      .statistics-section {
        .stats-grid {
          grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
          gap: 16px;
          
          .stat-card {
            .stat-card-inner {
              padding: 18px 14px;
              gap: 14px;
              
              .stat-icon-wrapper {
                width: 48px;
                height: 48px;
                
                :deep(.t-icon) {
                  font-size: 24px;
                }
              }
              
              .stat-content {
                .stat-number {
                  font-size: 24px;
                }
                
                .stat-label {
                  font-size: 13px;
                }
              }
            }
          }
        }
      }
    }
  }
}

// 表格单元格支持换行
:deep(.t-table) {
  td {
    white-space: normal;
  }
}

@media (max-width: 768px) {
  .course-evaluation-container {
    padding: 12px 0;
    
    .page-content {
      .statistics-section {
        .stats-grid {
          grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
          gap: 12px;
          
          .stat-card {
            .stat-card-inner {
              padding: 16px 12px;
              gap: 12px;
              
              .stat-icon-wrapper {
                width: 40px;
                height: 40px;
                
                :deep(.t-icon) {
                  font-size: 20px;
                }
              }
              
              .stat-content {
                .stat-number {
                  font-size: 20px;
                }
                
                .stat-label {
                  font-size: 12px;
                }
                
                .stat-trend {
                  :deep(.t-tag) {
                    font-size: 11px;
                    padding: 2px 6px;
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .course-evaluation-container {
    .page-content {
      .statistics-section {
        .stats-grid {
          grid-template-columns: 1fr;
          
          .stat-card {
            .stat-card-inner {
              padding: 16px 12px;
              
              .stat-icon-wrapper {
                width: 48px;
                height: 48px;
                
                :deep(.t-icon) {
                  font-size: 24px;
                }
              }
              
              .stat-content {
                .stat-number {
                  font-size: 24px;
                }
                
                .stat-label {
                  font-size: 14px;
                }
              }
            }
          }
        }
      }
    }
  }
}
</style>

<template>
  <div class="leader-task-assessment-manage">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <t-button theme="default" variant="outline" @click="goBack" class="back-button">
            <template #icon>
              <t-icon name="chevron-left" />
            </template>
            返回
          </t-button>
          <h1 class="page-title">
            教学任务考核管理
            <span v-if="displayCourseInfo" class="course-info">
              - <span class="course-name">{{ displayCourseInfo.courseName }}</span>
              <span class="course-code">（{{ displayCourseInfo.courseCode }}）</span>
              <span class="semester-info">- {{ displayCourseInfo.semesterText }}</span>
            </span>
          </h1>
        </div>
      </div>
    </div>

    <!-- 课程目标指标卡片列表 -->
    <div class="objective-cards-section">
      <h3 class="section-title">课程目标指标</h3>
      <t-loading :loading="loading">
        <div v-if="courseObjectives.length === 0" class="empty-state">
          <t-icon name="inbox" size="48px" />
          <p>暂无课程目标数据</p>
        </div>
        
        <div v-else class="objective-cards">
          <t-card 
            v-for="objective in courseObjectives" 
            :key="objective.id"
            class="objective-card"
            :class="{ 'highlight-difference': Math.abs(objective.totalPercentage - objective.expectedPercentage) > 5 }"
          >
            <template #header>
              <div class="card-header">
                <h4 class="objective-name">{{ objective.identifier }} {{ objective.name }}</h4>
              </div>
            </template>
            
            <div class="card-content">
              <div class="percentage-info">
                <div class="percentage-item">
                  <div class="percentage-label">考核总占比</div>
                  <div class="percentage-value actual">{{ objective.totalPercentage }}%</div>
                </div>
                <div class="percentage-item">
                  <div class="percentage-label">应占比</div>
                  <div class="percentage-value expected">{{ objective.expectedPercentage }}%</div>
                </div>
                <div 
                  class="percentage-difference" 
                  :class="getDifferenceClass(objective.totalPercentage, objective.expectedPercentage)"
                >
                  <t-icon :name="getDifferenceIcon(objective.totalPercentage, objective.expectedPercentage)" />
                  {{ Math.abs(objective.totalPercentage - objective.expectedPercentage) }}%
                </div>
              </div>
              
              <!-- <div class="progress-container">
                <t-progress 
                  :percentage="objective.totalPercentage" 
                  :color="getProgressColor(objective.totalPercentage, objective.expectedPercentage)"
                  :trackColor="getTrackColor(objective.totalPercentage, objective.expectedPercentage)"
                  :strokeWidth="6"
                  theme="line"
                />
              </div> -->
            </div>
          </t-card>
        </div>
      </t-loading>
    </div>

    <!-- 考核内容列表 -->
    <div class="assessment-list-section">
      <div class="section-header">
        <h3 class="section-title">考核内容列表</h3>
        <div class="filter-actions">
          <t-space>
            <!-- 考核类型筛选 -->
            <t-select
              v-model="filters.assessmentType"
              clearable
              placeholder="考核类型"
              :options="assessmentTypeOptions"
              style="width: 150px"
              @change="handleFilterChange"
            />
            
            <!-- 达成度计算筛选 -->
            <t-select
              v-model="filters.achievementCalculation"
              clearable
              placeholder="是否参与达成度计算"
              :options="[
                { label: '参与', value: true },
                { label: '不参与', value: false }
              ]"
              style="width: 180px"
              @change="handleFilterChange"
            />
            
            <!-- 重置按钮 -->
            <t-button theme="default" variant="outline" @click="resetFilters">
              <template #icon>
                <t-icon name="refresh" />
              </template>
              重置
            </t-button>
          </t-space>
        </div>
      </div>
      
      <t-loading :loading="contentLoading">
        <div v-if="filteredAssessmentContents.length === 0" class="empty-state">
          <t-icon name="file" size="48px" />
          <p>暂无考核内容</p>
        </div>
        
        <t-table
          v-else
          :data="tableDataWithSummary"
          :columns="assessmentColumns"
          row-key="id"
          hover
          size="medium"
          :bordered="false"
          class="assessment-table"
          :footData="summaryData"
        >
          <template #name="{ row }">
            <div class="assessment-name-cell">
              <span class="assessment-name">{{ row.name }}</span>
              <t-tag v-if="row.source === 'unified'" size="small" theme="success" variant="light">统一</t-tag>
            </div>
          </template>
          
          <template #type="{ row }">
            <t-tag 
              size="small" 
              :theme="getAssessmentTypeTheme(row.type)"
            >
              {{ getAssessmentTypeName(row.type) }}
            </t-tag>
          </template>
          
          <template #weight="{ row }">
            <span class="weight-value">{{ row.weight }}%</span>
          </template>
          
          <!-- 动态生成课程目标占比列的模板 -->
          <template 
            v-for="objective in courseObjectives" 
            :key="objective.id" 
            #[`objective_${objective.id}`]="{ row }"
          >
            <span>{{ getObjectiveWeight(row, objective.id) }}</span>
          </template>
          
          <template #achievement="{ row }">
            <t-tag
              size="small"
              :theme="row.achievement ? 'success' : 'default'"
              :variant="row.achievement ? 'light' : 'outline'"
            >
              {{ row.achievement ? '参与' : '不参与' }}
            </t-tag>
          </template>
          
          <template #status="{ row }">
            <t-tag size="small" :theme="getContentStatusTheme(row.status)">
              {{ getContentStatusText(row.status) }}
            </t-tag>
          </template>
          
          <template #actions="{ row }">
            <t-space :size="8" style="justify-content: center; width: 100%;">
              <t-button
                theme="primary"
                variant="text"
                size="small"
                @click="handleViewDetail(row)"
              >
                详情
              </t-button>
              <t-button
                theme="primary"
                variant="text"
                size="small"
                :disabled="row.source === 'unified'"
                @click="handleEdit(row)"
              >
                编辑
              </t-button>
              <!-- <t-button
                theme="success"
                variant="text"
                size="small"
                :disabled="row.source === 'unified'"
                @click="handlePublish(row)"
              >
                {{ row.status === 'published' ? '重新发布' : '发布' }}
              </t-button> -->
            </t-space>
          </template>
        </t-table>
      </t-loading>
    </div>
    
    <!-- 考核内容详情弹窗 -->
    <t-dialog
      v-model:visible="detailDialogVisible"
      :header="`考核内容详情 - ${detailDialogData.name || ''}`"
      width="800px"
      :footer="false"
    >
      <div class="detail-dialog-content">
        <div class="detail-section">
          <div class="section-header">
            <t-icon name="info-circle" />
            <span>基本信息</span>
          </div>
          <div class="section-content">
            <div class="info-grid">
              <div class="info-item">
                <span class="label">考核名称</span>
                <span class="value">{{ detailDialogData.name || '-' }}</span>
              </div>
              <div class="info-item">
                <span class="label">考核类型</span>
                <span class="value">{{ getAssessmentTypeName(detailDialogData.type) }}</span>
              </div>
              <div class="info-item">
                <span class="label">考核权重</span>
                <span class="value">{{ detailDialogData.weight ? `${detailDialogData.weight}%` : '-' }}</span>
              </div>
              <div class="info-item">
                <span class="label">达成度计算</span>
                <span class="value">
                  <t-tag
                    size="small"
                    :theme="detailDialogData.achievement ? 'success' : 'default'"
                    :variant="detailDialogData.achievement ? 'light' : 'outline'"
                  >
                    {{ detailDialogData.achievement ? '参与' : '不参与' }}
                  </t-tag>
                </span>
              </div>
              <div class="info-item">
                <span class="label">考核状态</span>
                <span class="value">
                  <t-tag size="small" :theme="getContentStatusTheme(detailDialogData.status)">
                    {{ getContentStatusText(detailDialogData.status) }}
                  </t-tag>
                </span>
              </div>
              <div class="info-item full-width">
                <span class="label">考核描述</span>
                <span class="value">{{ detailDialogData.description || '暂无描述' }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </t-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, reactive, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { MessagePlugin } from 'tdesign-vue-next'
import { storage } from '@/utils/storage'
import { CourseCacheInfo } from '@/types/course'
import { getDictLabelByTypeTitle,getDictOptionsByTypeTitle } from '@/utils/dictUtil'
import { getAssessmentListByTaskId } from '@/api/assessment/taskAssessment'
import { getCourseTargetList, getCourseAssessmentProportion } from '@/api/training/course'



// 响应式数据
const loading = ref(false)
const contentLoading = ref(false)
const courseObjectives = ref<any[]>([])
const assessmentContents = ref<any[]>([])
const assessmentTypeOptions = ref<any[]>([])
const detailDialogVisible = ref(false)
const detailDialogData = ref<any>({})
const proportionsData = ref<any[]>([]) // 用于存储比例数据

// 表格列配置
const assessmentColumns = ref<any[]>([])

// 基础列定义
const baseColumns = [
  { colKey: 'name', title: '考核名称', width: 200, align: 'center' as const },
  { colKey: 'type', title: '考核类型', width: 120, align: 'center' as const },
  { colKey: 'weight', title: '权重', width: 100, align: 'center' as const }
];

// 其它列定义
const otherColumns = [
  { colKey: 'achievement', title: '达成度计算', width: 120, align: 'center' as const },
  { colKey: 'status', title: '状态', width: 100, align: 'center' as const },
  { colKey: 'actions', title: '操作', width: 180, fixed: 'right' as const, align: 'center' as const }
];

// 过滤条件
const filters = reactive({
  assessmentType: undefined as number | undefined,
  achievementCalculation: undefined as boolean | undefined
})

// 从路由参数获取 taskId
const route = useRoute()
const taskId = computed(() => {
  return Number(route.params.taskId) || 0
})

// 读取课程缓存信息
const cachedCourseInfo = storage.get('lastCourseDetail') as CourseCacheInfo | null

// 计算属性：显示课程信息
const displayCourseInfo = computed(() => {
  const info = cachedCourseInfo || {
    courseName: '数据结构',
    courseCode: 'CS201',
    academicYear: '2024-2025',
    semester: 1
  }
  
  if (!info) return null

  // 构建学期信息
  let semesterText = ''
  if (info.academicYear && info.semester) {
    const semesterMap = { 1: '春季学期', 2: '秋季学期' }
    semesterText = `${info.academicYear} ${semesterMap[info.semester as keyof typeof semesterMap] || ''}`
  }

  return {
    ...info,
    semesterText
  }
})

// 计算属性：过滤后的考核内容
const filteredAssessmentContents = computed(() => {
  return assessmentContents.value.filter(item => {
    // 考核类型筛选
    if (filters.assessmentType !== undefined && item.type !== filters.assessmentType) {
      return false
    }
    
    // 达成度计算筛选
    if (filters.achievementCalculation !== undefined && item.achievement !== filters.achievementCalculation) {
      return false
    }
    
    return true
  })
})

// 计算属性：带汇总行的表格数据
const tableDataWithSummary = computed(() => {
  return [...filteredAssessmentContents.value];
});

// 计算属性：汇总行数据
const summaryData = computed(() => {
  if (courseObjectives.value.length === 0) return [];
  
  const summaryRow: any = {
    id: 'summary',
    name: '总占比',
    type: '',
    weight: '',
    achievement: '',
    status: ''
  };
  
  // 为每个课程目标计算总占比
  courseObjectives.value.forEach(objective => {
    summaryRow[`objective_${objective.id}`] = `${calculateTotalObjectiveWeight(objective.id)}%`;
  });
  
  // 添加居中样式类
  summaryRow.className = 'summary-row';
  
  return [summaryRow];
});

// 初始化和更新表格列配置
const updateTableColumns = () => {
  // 基础列
  const baseCols = baseColumns.map(col => ({ ...col }));
  
  // 课程目标占比列（复合列）
  const objectiveCols = courseObjectives.value.map(objective => ({
    colKey: `objective_${objective.id}`,
    title: objective.identifier,
    width: 100,
    align: 'center' as const,
    // 添加单元格渲染函数
    cell: (h: any, { row }: any) => {
      return getObjectiveWeight(row, objective.id);
    }
  }));
  
  // 其他列
  const otherCols = otherColumns.map(col => ({ ...col }));
  
  // 如果没有课程目标，使用基础列配置
  if (courseObjectives.value.length === 0) {
    assessmentColumns.value = [
      ...baseCols,
      ...otherCols
    ];
    return;
  }
  
  // 合并所有列
  assessmentColumns.value = [
    ...baseCols,
    {
      colKey: 'objectiveHeader',
      title: '课程目标占比',
      children: objectiveCols,
      align: 'center' as const // 添加居中对齐属性
    },
    ...otherCols
  ];
};

// 监听课程目标变化，更新表格列配置
watch(courseObjectives, () => {
  updateTableColumns();
}, { immediate: true });

// 获取课程目标占比数据
const getObjectiveWeight = (row: any, objectiveId: number) => {
  // 查找该考核内容在proportions中的数据
  const proportion = proportionsData.value.find((p: any) => p.examId === row.id);
  if (proportion && proportion.objectiveList) {
    const objWeight = proportion.objectiveList.find((obj: any) => obj.objectiveId === objectiveId);
    if (objWeight && objWeight.weight !== undefined) {
      return `${Math.round(objWeight.weight * 100) / 100}%`;
    }
  }
  return '0%';
};

// 计算课程目标总占比
const calculateTotalObjectiveWeight = (objectiveId: number) => {
  let total = 0;
  // 遍历所有考核内容，计算该课程目标的总占比
  filteredAssessmentContents.value.forEach(assessment => {
    const proportion = proportionsData.value.find((p: any) => p.examId === assessment.id);
    if (proportion && proportion.objectiveList) {
      const objWeight = proportion.objectiveList.find((obj: any) => obj.objectiveId === objectiveId);
      if (objWeight) {
        total += (proportion.examWeight || 0) * (objWeight.weight || 0) / 100;
      }
    }
  });
  return Math.round(total * 100) / 100;
};

// 获取差异类别
const getDifferenceClass = (actual: number, expected: number) => {
  const diff = Math.abs(actual - expected)
  if (diff > 10) return 'large-difference'
  if (diff > 5) return 'medium-difference'
  return 'small-difference'
}

// 获取差异图标
const getDifferenceIcon = (actual: number, expected: number) => {
  if (actual > expected) return 'arrow-up'
  if (actual < expected) return 'arrow-down'
  return 'minus'
}

// 获取进度条颜色
const getProgressColor = (actual: number, expected: number) => {
  const diff = Math.abs(actual - expected)
  if (diff <= 5) return '#00a870' // 绿色 - 正常
  if (diff <= 10) return '#ffb400' // 黄色 - 警告
  return '#e34d59' // 红色 - 严重偏差
}

// 获取轨道颜色
const getTrackColor = (actual: number, expected: number) => {
  const diff = Math.abs(actual - expected)
  if (diff <= 5) return '#e0f3ff' // 蓝色轨道
  if (diff <= 10) return '#fff3e9' // 橙色轨道
  return '#ffe9e9' // 红色轨道
}

// 获取考核类型主题
const getAssessmentTypeTheme = (type: number) => {
  const themeMap: Record<number, 'default' | 'primary' | 'success' | 'warning' | 'danger'> = {
    1: 'primary',   // 作业
    2: 'success',   // 考试
    3: 'warning',   // 实验
    4: 'default',   // 课程设计
    5: 'danger',    // 实习
    6: 'primary'    // 毕业设计
  }
  return themeMap[type] || 'default'
}

// 创建考核类型映射
const assessmentTypeMap = ref<Record<number, string>>({});

// 添加loadAssessmentTypes函数定义
const loadAssessmentTypes = async () => {
  try {
    const options = await getDictOptionsByTypeTitle('考核环节');
    return options.map((opt: any) => ({
      id: opt.value,
      name: opt.label
    }));
  } catch (error) {
    console.error('加载考核类型失败:', error);
    return [];
  }
};

// 在组件挂载时加载考核类型列表
onMounted(async () => {
  // 加载考核类型列表
  const assessmentTypes = await loadAssessmentTypes();
  assessmentTypes.forEach(type => {
    assessmentTypeMap.value[type.id] = type.name;
  });
});

// 同步返回考核类型名称
function getAssessmentTypeName(typeId: number): string {
  return assessmentTypeMap.value[typeId] || '未知类型';
}

// 获取内容状态主题
const getContentStatusTheme = (status: string) => {
  switch (status) {
    case 'published': return 'success'
    case 'draft': return 'warning'
    case 'ended': return 'default'
    default: return 'default'
  }
}

// 获取内容状态文本
const getContentStatusText = (status: string) => {
  switch (status) {
    case 'published': return '已发布'
    case 'draft': return '草稿'
    case 'ended': return '已结束'
    default: return '未知'
  }
}

// 处理筛选变化
const handleFilterChange = () => {
  // 筛选逻辑已在 computed 中实现
  console.log('筛选条件变化:', filters)
}

// 重置筛选
const resetFilters = () => {
  filters.assessmentType = undefined
  filters.achievementCalculation = undefined
}

// 查看详情
const handleViewDetail = (row: any) => {
  detailDialogData.value = row
  detailDialogVisible.value = true
}

// 编辑
const handleEdit = (row: any) => {
  console.log('编辑考核内容:', row)
}

// 发布
const handlePublish = (row: any) => {
  console.log('发布考核内容:', row)
}

// 路由
const router = useRouter()
// const route = useRoute() // Duplicate, already declared above

// 返回上一页
const goBack = () => {
  const courseId = route.params.courseId || cachedCourseInfo?.courseId
  if (courseId) {
    router.push(`/course/leader/${courseId}/task`)
  } else {
    router.go(-1)
  }
}

// 加载数据
const loadData = async () => {
  try {
    loading.value = true
    contentLoading.value = true
    
    // 获取路由中的taskId
    const routeTaskId = route.params.taskId
    if (!routeTaskId) {
      MessagePlugin.error('未找到任务ID')
      throw new Error('未找到任务ID')
    }
    
    // 并行请求数据
    const [assessments, objectives, proportions] = await Promise.all([
      getAssessmentListByTaskId(Number(routeTaskId)),
      cachedCourseInfo?.courseId ? getCourseTargetList(Number(cachedCourseInfo.courseId)) : Promise.resolve([]),
      cachedCourseInfo?.courseId ? getCourseAssessmentProportion(Number(cachedCourseInfo.courseId)) : Promise.resolve([])
    ])
    
    // 保存比例数据
    proportionsData.value = proportions || []
    
    // 处理考核内容数据
    assessmentContents.value = assessments.map(item => ({
      id: item.id,
      name: item.assessmentName,
      type: item.assessmentMethod,
      weight: item.assessmentWeight,
      achievement: item.achievement,
      status: getAssessmentStatusString(item.assessmentStatus),
      description: item.description,
      source: item.taskId === -1 ? 'unified' : 'local'
    }))
    
    // 处理课程目标数据
    if (objectives && proportions) {
      courseObjectives.value = objectives.map(objective => {
        // 计算该课程目标在所有考核中的总占比
        let totalPercentage = 0;
        proportions.forEach((proportion: any) => {
          if (proportion.objectiveList) {
            const objWeight = proportion.objectiveList.find((obj: any) => obj.objectiveId === objective.objectiveId);
            if (objWeight) {
              totalPercentage += (proportion.examWeight || 0) * (objWeight.weight || 0) / 100;
            }
          }
        });
        
        return {
          id: objective.objectiveId,
          identifier: `CO${objective.number}`,
          name: objective.objectiveName,
          description: objective.description,
          totalPercentage: Math.round(totalPercentage * 100) / 100, // 保留两位小数
          expectedPercentage: objective.expectedScore ? objective.expectedScore * 100 : 0
        };
      });
    }
    
    // 设置考核类型选项（使用系统字典）
    assessmentTypeOptions.value = await getDictOptionsByTypeTitle('考核环节');

  } catch (error) {
    console.error('加载数据失败:', error)
    MessagePlugin.error('加载数据失败: ' + (error.message || '未知错误'))
  } finally {
    loading.value = false
    contentLoading.value = false
  }
}

// 辅助函数：将数字状态转换为字符串
const getAssessmentStatusString = (status: number): string => {
  switch (status) {
    case 0: return 'draft'
    case 1: return 'draft' // 编辑中也视为草稿状态
    case 2: return 'published' // 进行中视为已发布
    case 3: return 'ended'
    default: return 'draft'
  }
}

// 初始化
onMounted(() => {
  loadData()
})
</script>

<style lang="less" scoped>
.leader-task-assessment-manage {
  padding: 16px 24px;
  background-color: var(--td-bg-color-container);
  min-height: calc(100vh - 64px - 60px);
  
  .page-header {
    margin-bottom: 24px;
    
    .header-content {
      display: flex;
      align-items: center;
      justify-content: space-between;
      flex-wrap: wrap;
      gap: 12px;
      
      .header-left {
        display: flex;
        align-items: center;
        gap: 12px;
      }
      
      .back-button {
        height: 32px;
      }
      
      .page-title {
        font-size: 20px;
        font-weight: 600;
        color: var(--td-text-color-primary);
        margin: 0;
        
        .course-info {
          font-weight: normal;
          font-size: 16px;
          color: var(--td-text-color-secondary);
          
          .course-name {
            font-weight: 500;
          }
          
          .course-code {
            color: var(--td-text-color-placeholder);
          }
          
          .semester-info {
            margin-left: 8px;
          }
        }
      }
    }
  }
  
  .section-title {
    font-size: 18px;
    font-weight: 500;
    color: var(--td-text-color-primary);
    margin: 0 0 16px 0;
  }
  
  // 课程目标指标卡片部分
  .objective-cards-section {
    margin-bottom: 32px;
    
    .objective-cards {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 20px;
      
      .objective-card {
        transition: all 0.3s ease;
        min-width: 0; // 允许卡片收缩以适应容器

        &.highlight-difference {
          border: 1px solid var(--td-error-color);
          box-shadow: 0 4px 12px rgba(227, 77, 89, 0.1);
        }
        
        :deep(.t-card__header) {
          padding: 16px;
          border-bottom: 1px solid var(--td-border-level-1-color);
        }
        
        :deep(.t-card__body) {
          padding: 16px;
        }
        
        .card-header {
          .objective-name {
            font-size: 16px;
            font-weight: 500;
            color: var(--td-text-color-primary);
            margin: 0;
          }
        }
        
        .card-content {
          .percentage-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
            
            .percentage-item {
              text-align: center;
              
              .percentage-label {
                font-size: 12px;
                color: var(--td-text-color-secondary);
                margin-bottom: 4px;
              }
              
              .percentage-value {
                font-size: 18px;
                font-weight: 600;
                
                &.actual {
                  color: var(--td-brand-color);
                }
                
                &.expected {
                  color: var(--td-text-color-primary);
                }
              }
            }
            
            .percentage-difference {
              display: flex;
              align-items: center;
              gap: 4px;
              font-weight: 600;
              
              &.small-difference {
                color: var(--td-success-color);
              }
              
              &.medium-difference {
                color: var(--td-warning-color);
              }
              
              &.large-difference {
                color: var(--td-error-color);
              }
            }
          }
          
          .progress-container {
            margin-top: 8px;
          }
        }
      }
    }
  }
  
  // 考核内容列表部分
  .assessment-list-section {
    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      flex-wrap: wrap;
      gap: 12px;
      
      .filter-actions {
        display: flex;
        align-items: center;
        gap: 12px;
      }
    }
    
    .assessment-table {
      :deep(.t-table__th) {
        background-color: var(--td-bg-color-page);
        text-align: center; // 表头居中
      }
      
      :deep(.t-table__td) {
        text-align: center; // 表格内容居中
      }
      
      :deep(.t-table__th--fixed-left),
      :deep(.t-table__td--fixed-left) {
        text-align: left; // 左侧固定列保持左对齐
      }
      
      :deep(.t-table__th--fixed-right),
      :deep(.t-table__td--fixed-right) {
        text-align: center; // 右侧固定列居中
      }
      
      :deep(.t-table__th--colspan) {
        text-align: center; // 复合表头居中
      }
      
      .assessment-name-cell {
        display: flex;
        align-items: center;
        gap: 8px;
        justify-content: flex-start;
      }
      
      .weight-value {
        font-weight: 600;
        color: var(--td-brand-color);
      }
    }
  }
  
  // 详情弹窗
  .detail-dialog-content {
    .detail-section {
      margin-bottom: 24px;
      border-radius: 6px;
      overflow: hidden;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
      border: 1px solid var(--td-border-level-1-color);
      
      .section-header {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 12px 16px;
        background: var(--td-brand-color);
        color: white;
        font-weight: 600;
        font-size: 14px;
        
        .t-icon {
          font-size: 16px;
        }
      }
      
      .section-content {
        padding: 16px;
        background: var(--td-bg-color-container);
        
        .info-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
          gap: 16px;
        }
        
        .info-item {
          display: flex;
          flex-direction: column;
          gap: 4px;
          
          &.full-width {
            grid-column: 1 / -1;
          }
          
          .label {
            font-size: 12px;
            color: var(--td-text-color-secondary);
          }
          
          .value {
            font-size: 14px;
            color: var(--td-text-color-primary);
            font-weight: 500;
          }
        }
      }
    }
  }
  
  // 空状态
  .empty-state {
    text-align: center;
    padding: 48px 0;
    color: var(--td-text-color-placeholder);
    
    .t-icon {
      margin-bottom: 16px;
      color: var(--td-text-color-secondary);
    }
    
    p {
      margin: 0;
      font-size: 14px;
    }
  }
}

/* 表格样式优化，确保所有内容居中显示 */
.assessment-table {
  :deep(.t-table__cell) {
    text-align: center;
  }
  
  /* 确保表头也居中 */
  :deep(.t-table__th) {
    text-align: center;
  }
  
  /* 特殊处理操作列，保持内容居中 */
  :deep(.t-table__cell[colkey="actions"]) {
    text-align: center;
  }
  
  /* 确保表格底部汇总行数据居中 */
  :deep(.t-table__foot) {
    .t-table__cell {
      text-align: center;
    }
  }
  
  /* 确保"总占比"文本在汇总行中居中 */
  :deep(.t-table__row--summary) {
    .t-table__cell:first-child {
      text-align: center;
    }
  }
}

/* 汇总行样式 */
.summary-row {
  text-align: center;
  font-weight: bold;
  background-color: var(--td-bg-color-page);
}
</style>
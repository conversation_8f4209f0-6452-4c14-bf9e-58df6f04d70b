<template>
  <t-dialog
    v-model:visible="internalVisible"
    :header="dialogTitle"
    attach="body"
    :fullscreen="fullscreen"
    width="min(1200px, 90vw)"
    :on-confirm="handleSave"
    :on-close="handleCancel"
    :confirm-btn="{ content: '保存配置', theme: 'primary', loading: isSaving }"
    :cancel-btn="{ content: '取消' }"
  >
    <template #header>
      <div class="flex justify-between items-center w-full">
        <span>{{ dialogTitle }}</span>
        <t-button variant="text" @click="fullscreen = !fullscreen">
          <template #icon>
            <t-icon :name="fullscreen ? 'fullscreen-exit' : 'fullscreen'" />
          </template>
        </t-button>
      </div>
    </template>

    <div class="dialog-content p-4 space-y-4">
      <!-- 课程目标信息卡片展示（使用统一的数据变量 objectiveScores） -->
      <!-- 响应式自动填满布局 -->
      <div class="grid gap-4 mb-4" style="grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));">
        <t-card v-for="item in objectiveScores" :key="item.id" size="small" class="p-4 h-full" hoverable>
          <template #header>
            <span class="font-semibold">课程目标{{ item.number }} （分值: {{ item.score }}）</span>
          </template>
          <p class="text-sm text-gray-600">{{ item.description }}</p>
        </t-card>
      </div>
      <!-- 配置说明 -->
      <t-alert theme="warning" title="配置说明">
        <template #message>
          以下课程目标已自动同步当前课程的所有课程目标。请为每个课程目标设置分值，系统将自动计算占比。
        </template>
      </t-alert>

      <!-- 配置表格 (现代化设计) -->
      <div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
        <!-- 表头 -->
        <div class="grid grid-cols-12 bg-gradient-to-r from-blue-50 to-indigo-50 border-b border-gray-200">
          <div class="col-span-2 px-6 py-4 text-center font-semibold text-gray-800 border-r border-gray-200">
            <div class="flex items-center justify-center space-x-2 whitespace-nowrap">
              <svg class="w-4 h-4 text-blue-500" fill="currentColor" viewBox="0 0 20 20">
                <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
              </svg>
              <span>课程目标编号</span>
            </div>
          </div>
          <div class="col-span-3 px-6 py-4 text-center font-semibold text-gray-800 border-r border-gray-200">
            <div class="flex items-center justify-center space-x-2 whitespace-nowrap">
              <svg class="w-4 h-4 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                <path d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z"/>
                <path fill-rule="evenodd" d="M4 5a2 2 0 012-2 3 3 0 003 3h2a3 3 0 003-3 2 2 0 012 2v6a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm3 4a1 1 0 000 2h.01a1 1 0 100-2H7zm3 0a1 1 0 000 2h3a1 1 0 100-2h-3z" clip-rule="evenodd"/>
              </svg>
              <span>分值分布</span>
            </div>
          </div>
          <div class="col-span-2 px-6 py-4 text-center font-semibold text-gray-800 border-r border-gray-200">
            <div class="flex items-center justify-center space-x-2 whitespace-nowrap">
              <svg class="w-4 h-4 text-purple-500" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M3 3a1 1 0 000 2v8a2 2 0 002 2h2.586l-1.293 1.293a1 1 0 101.414 1.414L10 15.414l2.293 2.293a1 1 0 001.414-1.414L12.414 15H15a2 2 0 002-2V5a1 1 0 100-2H3zm11.707 4.707a1 1 0 00-1.414-1.414L10 9.586 8.707 8.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
              </svg>
              <span>占比百分比</span>
            </div>
          </div>
          <div class="col-span-5 px-6 py-4 text-center font-semibold text-gray-800">
            <div class="flex items-center justify-center space-x-2 whitespace-nowrap">
              <svg class="w-4 h-4 text-amber-500" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clip-rule="evenodd"/>
              </svg>
              <span>课程目标描述</span>
            </div>
          </div>
        </div>
        
        <!-- 数据行 -->
        <div v-for="(item, index) in objectiveScores" :key="item.id" 
             class="grid grid-cols-12 transition-all duration-200 hover:bg-blue-50/50 group border-b border-gray-100 last:border-b-0">
          <div class="col-span-2 px-6 py-5 flex items-center justify-center border-r border-gray-100">
            <div class="transform transition-transform group-hover:scale-105">
              <t-tag shape="round" theme="primary" size="large" class="px-4 py-2 font-medium shadow-sm">
                {{ item.identifier }}
              </t-tag>
            </div>
          </div>
          <div class="col-span-3 px-6 py-5 flex items-center justify-center border-r border-gray-100">
            <div class="relative">
              <t-input-number 
                v-model="item.score" 
                :min="0" 
                :step="1" 
                theme="normal"
                size="large"
                class="score-input"
                style="width: 140px;"
                @change="handleScoreChange"
              />
              <div class="absolute -top-1 -right-1 w-2 h-2 bg-green-400 rounded-full opacity-0 group-hover:opacity-100 transition-opacity"></div>
            </div>
          </div>
          <div class="col-span-2 px-6 py-5 flex items-center justify-center border-r border-gray-100">
            <div class="bg-gradient-to-r from-purple-100 to-pink-100 px-4 py-2 rounded-lg">
              <span class="font-mono font-semibold text-purple-700 whitespace-nowrap">{{ getPercentage(item.score) }}</span>
            </div>
          </div>
          <div class="col-span-5 px-6 py-5 flex items-center">
            <div class="text-sm text-gray-700 leading-relaxed line-clamp-2">
              {{ item.description }}
            </div>
          </div>
        </div>
        
        <!-- 合计行 -->
        <div class="grid grid-cols-12 bg-gradient-to-r from-amber-50 via-yellow-50 to-orange-50 border-t-2 border-amber-300">
          <div class="col-span-2 px-6 py-4 flex items-center justify-center border-r border-amber-200">
            <div class="flex items-center space-x-2 whitespace-nowrap">
              <svg class="w-5 h-5 text-amber-600" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd"/>
              </svg>
              <span class="font-bold text-amber-800 text-lg">合计</span>
            </div>
          </div>
          <div class="col-span-3 px-6 py-4 flex items-center justify-center border-r border-amber-200">
            <div class="bg-white px-4 py-2 rounded-lg shadow-sm border border-amber-200">
              <span class="font-mono font-bold text-amber-800 text-lg whitespace-nowrap">{{ totalScore }}</span>
            </div>
          </div>
          <div class="col-span-2 px-6 py-4 flex items-center justify-center border-r border-amber-200">
            <div class="bg-gradient-to-r from-green-100 to-emerald-100 px-4 py-2 rounded-lg border border-green-200">
              <span class="font-mono font-bold text-green-700 text-lg whitespace-nowrap">{{ totalPercentage }}%</span>
            </div>
          </div>
          <div class="col-span-5 px-6 py-4 flex items-center">
            <div class="text-xs text-amber-600 font-medium whitespace-nowrap">
              总计分值及占比
            </div>
          </div>
        </div>
      </div>
      <t-alert v-if="totalScore === 0 && hasInteracted" theme="warning">
        请为课程目标设置分值。
      </t-alert>
    </div>
  </t-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch, PropType } from 'vue';
import { MessagePlugin } from 'tdesign-vue-next';
import { onMounted } from 'vue';
import { saveDirectEntryConfig, type DirectEntryConfig, getDirectEntryConfig } from '@/api/assessment/assessment';
import { type CourseDetailInfo } from '@/api/training/course'
// 假设的课程目标和分数类型
interface CourseObjective {
  id: string;
  number:number
  identifier: string; // e.g., 'CO1'
  description: string;
}

interface ObjectiveScore {
  id: string;
  number: number; // 课程目标编号
  identifier:string;
  description: string;
  score: number;
}

// interface CourseInfo {
//   id: string;
//   name: string;
//   semester?: string; // 可选的学期字段
// }

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  courseInfo: {
    type: Object as PropType<CourseDetailInfo>,
    required: true,
  },
  courseObjectives: {
    type: Array as PropType<CourseObjective[]>,
    required: true,
  },
  initialScores: {
    type: Array as PropType<ObjectiveScore[]>,
    default: (): ObjectiveScore[] => [],
  },
  taskId: {
    type: String,
    default: '',
  },
  assessmentId: {
    type: String,
    required: true,
    default: '',
  },
  examStatus: {
    type: [String, Number],
    default: 1,
  },
});

const emit = defineEmits(['update:visible', 'save']);

const internalVisible = ref(false);
const fullscreen = ref(false);
const isSaving = ref(false);
const hasInteracted = ref(false); // To avoid showing error on initial load

// 对话框标题，根据课程信息和学期信息动态生成
const dialogTitle = computed(() => {
  const semesterInfo = props.courseInfo.courseVersion ? ` - ${props.courseInfo.courseSemester}` : '';
  const statusInfo = props.examStatus ? ` (${props.examStatus})` : '';
  return `直接录入模式 - ${props.courseInfo.courseName}${semesterInfo}${statusInfo} - 课程目标分值配置`;
});

const objectiveScores = ref<ObjectiveScore[]>([]);

// 表格列配置 (TDesign 表格)
const tableColumns = [
  { colKey: 'identifier', title: '课程目标编号', width: 150, cell: 'identifier' },
  { colKey: 'score', title: '分值分布', width: 150, cell: 'score' },
  { colKey: 'percentage', title: '占比百分比', width: 150, cell: 'percentage' },
  { colKey: 'description', title: '课程目标描述', cell: 'description' }
];

// 初始化分数
const initializeScores = () => {
  syncDirectEntryConfig();
};


const syncDirectEntryConfig = async () => {
  if (!props.assessmentId) return;
  try {
    const response = await getDirectEntryConfig(props.assessmentId);
    if (response.code === 0 || response.code === 200) {
      const configs = response.data?.configs || [];
      // 以 courseObjectives 为主，合并分值
      
      objectiveScores.value = props.courseInfo.courseObjectives.map(co => {
        const found = configs.find((cfg: any) => cfg.courseObjectiveId === co.objectiveId);
        return {
          id: co.objectiveId,
          number: co.number, // 提取课程目标编号
          identifier: co.objectiveName,
          description: co.description,
          score: found ? found.totalScore : 0
        };
      });
    }
  } catch (e) {
    console.error('获取直接录入配置失败', e);
  }
};


// 同步父组件 visible 状态，首次挂载时也初始化（immediate）
watch(
  () => props.visible,
  (val) => {
  console.log('DirectEntryConfigDialog visible changed:', val);
  internalVisible.value = val;
  if (val) {
    console.log('对话框正在打开，初始化数据');
    initializeScores();
    hasInteracted.value = false;
  }
  },
  { immediate: true }
);

watch(internalVisible, (val) => {
  console.log('DirectEntryConfigDialog internalVisible changed:', val);
  if (!val) {
    emit('update:visible', false);
  }
});

watch(objectiveScores, () => {
  hasInteracted.value = true;
}, { deep: true });

// 处理分值变化
const handleScoreChange = () => {
  hasInteracted.value = true;
};

const totalScore = computed(() => {
  return objectiveScores.value.reduce((sum, item) => sum + (item.score || 0), 0);
});

const totalPercentage = computed(() => {
  // 总占比永远是100%（当有分值时）
  return totalScore.value > 0 ? 100 : 0;
});

const getPercentage = (score: number) => {
  if (totalScore.value === 0) return '0%';
  const percentage = (score / totalScore.value) * 100;
  return `${percentage.toFixed(1)}%`;
};

const handleSave = async () => {
  if (totalScore.value === 0) {
    MessagePlugin.warning('您尚未配置任何分值');
    return;
  }
  if (totalPercentage.value !== 100) {
    MessagePlugin.warning('分值合计必须为100%，请调整各项分值。');
    return;
  }

  isSaving.value = true;
  try {
    // 构建API请求参数
    const configs: DirectEntryConfig[] = objectiveScores.value.map(item => ({
      number: item.number,
      courseObjectiveId: item.id,
      identifier: item.identifier,
      description: item.description,
      totalScore: item.score,
      percentage: parseFloat(getPercentage(item.score).replace('%', ''))
    }));


    // 调用API保存配置
    const response = await saveDirectEntryConfig({
      assessmentId: props.assessmentId,
      configs: configs
    });

    if (response.code === 0 || response.code === 200) {
      // 成功保存
      emit('save', objectiveScores.value);
      MessagePlugin.success('配置已成功保存');
      handleCancel();
    } else {
      // 服务器返回错误
      MessagePlugin.error(`保存失败: ${response.message || '未知错误'}`);
    }
  } catch (error) {
    console.error('保存直接录入配置失败:', error);
    MessagePlugin.error('保存失败，请检查网络连接后重试');
  } finally {
    isSaving.value = false;
  }
};

const handleCancel = () => {
  internalVisible.value = false;
  emit('update:visible', false);
};
</script>

<style scoped>
/* 现代化表格样式 */
.score-input :deep(.t-input-number) {
  border-radius: 8px;
  border: 2px solid #e5e7eb;
  transition: all 0.3s ease;
}

.score-input :deep(.t-input-number):hover {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.score-input :deep(.t-input-number):focus-within {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
}

/* 文本截断样式 */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 动画效果 */
@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.group:hover .score-input::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  animation: shimmer 1.5s ease-in-out;
}

/* 响应式表格样式 */
@media (max-width: 1024px) {
  .dialog-content {
    padding: 1rem;
  }
  
  .grid.grid-cols-12 {
    gap: 0.5rem;
  }
  
  .col-span-2, .col-span-3, .col-span-5 {
    padding: 0.75rem 1rem;
  }
}

@media (max-width: 768px) {
  .grid.grid-cols-12 {
    display: block;
  }
  
  .grid.grid-cols-12 > div {
    display: block;
    width: 100%;
    border-right: none !important;
    border-bottom: 1px solid #e5e7eb;
  }
}
</style>

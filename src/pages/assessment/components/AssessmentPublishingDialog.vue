<template>
  <t-dialog
    v-model:visible="dialogVisible"
    :header="false"
    :width="isFullscreen ? '100vw' : 'min(1200px, 90vw)'"
    :height="isFullscreen ? '100vh' : 'min(800px, 85vh)'"
    :top="isFullscreen ? '0' : '5vh'"
    attach="body"
    :confirm-btn="{
      content: confirmButtonText,
      loading: submitting,
      disabled: !canPublish
    }"
    :cancel-btn="{ content: '取消' }"
    @confirm="handleConfirm"
    @cancel="handleCancel"
    @close="handleClose"
  >
    <!-- 自定义头部 -->
    <template #header>
      <div class="dialog-header">
        <span class="dialog-title">发布考核到教学任务</span>
        <t-button
          variant="text"
          size="small"
          class="fullscreen-btn"
          @click="toggleFullscreen"
        >
          <template #icon>
            <t-icon :name="isFullscreen ? 'fullscreen-exit' : 'fullscreen'" />
          </template>
        </t-button>
      </div>
    </template>
    <div class="assessment-publishing-content">
      <!-- 考核信息展示 -->
      <div class="assessment-info-section">
        <t-card class="assessment-info-card" :bordered="false">
          <div class="assessment-header">
            <div class="assessment-title">
              <t-icon name="file-text" class="title-icon" />
              <h3>{{ assessmentData?.assessmentName }}</h3>
              <t-tag theme="primary" size="small">{{ getAssessmentMethodLabel(assessmentData?.assessmentMethod) }}</t-tag>
              <t-button
                theme="default"
                variant="outline"
                size="small"
                class="score-ratio-btn"
                @click="handleShowScoreRatio"
              >
                <template #icon>
                  <t-icon name="chart-pie" />
                </template>
                查看指标分值占比
              </t-button>
            </div>
            <div class="assessment-meta">
              <span class="meta-item">
                <t-icon name="calendar" />
                {{ assessmentData?.assessmentYear }}学年 第{{ assessmentData?.assessmentTerm }}学期
              </span>
              <span class="meta-item">
                <t-icon name="percentage" />
                权重: {{ assessmentData?.assessmentWeight }}%
              </span>
            </div>
          </div>
        </t-card>
      </div>

      <!-- 发布选项 -->
      <div class="publish-options-section">
        <t-card class="publish-options-card" :bordered="false">
          <div class="section-header">
            <h4>发布设置</h4>
            <div class="achievement-calculation-option">
              <t-checkbox
                v-model="participateInCalculation"
                class="calculation-checkbox"
                @change="handleCalculationModeChange"
              >
                <span class="checkbox-label">参与达成度计算</span>
              </t-checkbox>
              <div class="calculation-status">
                <t-icon :name="participateInCalculation ? 'check-circle' : 'info-circle'" />
                <span v-if="participateInCalculation" class="success-text">
                  选中后将发布到所有教学任务并参与达成度计算统计
                </span>
                <span v-else class="info-text">
                  可手动选择特定的教学任务进行发布，不参与达成度计算
                </span>
              </div>
            </div>
          </div>
        </t-card>
      </div>

      <!-- 教学任务选择区域 -->
      <div class="task-selection-section">
        <t-card class="task-selection-card" :bordered="false">
          <div class="section-header">
            <h4>选择教学任务</h4>
            <div v-if="participateInCalculation" class="locked-selection-notice">
              <t-icon name="lock" />
              <span>已自动选择所有教学任务（参与达成度计算模式）</span>
            </div>
            <div v-else class="selection-controls">
              <t-button size="small" variant="outline" @click="selectAll">
                <t-icon name="check-circle" />
                全选
              </t-button>
              <t-button size="small" variant="outline" @click="clearAll">
                <t-icon name="close-circle" />
                清空
              </t-button>
              <span class="selection-count">已选择 {{ selectedTaskIds.length }} 个任务</span>
            </div>
          </div>

          <!-- 加载状态 -->
          <div v-if="loadingTasks" class="loading-container">
            <t-loading size="large" text="正在加载教学任务..." />
          </div>

          <!-- 空状态 -->
          <div v-else-if="teachingTasks.length === 0" class="empty-state">
            <t-icon name="inbox" size="48px" />
            <p>暂无可用的教学任务</p>
            <t-button theme="primary" variant="outline" @click="loadTeachingTasks">
              <t-icon name="refresh" />
              重新加载
            </t-button>
          </div>

          <!-- 教学任务卡片网格 -->
          <div v-else class="task-grid">
            <TeachingTaskCard
              v-for="task in teachingTasks"
              :key="task.taskId"
              :task-data="task"
              :selected="selectedTaskIds.includes(task.taskId)"
              :disabled="participateInCalculation"
              @toggle-selection="handleTaskToggle"
            />
          </div>
        </t-card>
      </div>

      <!-- 已选择任务展示区域 -->
      <div v-if="!participateInCalculation && selectedTaskIds.length > 0" class="selected-tasks-section">
        <t-card class="selected-tasks-card" :bordered="false">
          <div class="section-header">
            <h4>已选择的教学任务</h4>
            <t-tag theme="success" size="small">{{ selectedTaskIds.length }} 个任务</t-tag>
          </div>
          
          <div class="selected-tasks-list">
            <div 
              v-for="taskId in selectedTaskIds" 
              :key="taskId"
              class="selected-task-item"
            >
              <div class="task-info">
                <span class="task-name">{{ getTaskName(taskId) }}</span>
                <span class="teacher-name">{{ getTeacherName(taskId) }}</span>
              </div>
              <t-button 
                size="small" 
                variant="text" 
                theme="danger"
                @click="removeSelectedTask(taskId)"
              >
                <t-icon name="close" />
              </t-button>
            </div>
          </div>
        </t-card>
      </div>
    </div>
  </t-dialog>

  <!-- 指标分值占比对话框 -->
  <Teleport to="body">
    <DirectEntryConfigDialog
      v-model:visible="scoreRatioDialogVisible"
      :course-info="courseInfo"
      :course-objectives="courseObjectives"
      :initial-scores="initialScores"
      :assessment-id="assessmentData?.id?.toString() || ''"
      :exam-status="1"
      class="score-ratio-dialog"
      @save="handleScoreRatioSave"
    />
  </Teleport>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, Teleport } from 'vue'
import { MessagePlugin } from 'tdesign-vue-next'
import TeachingTaskCard from './TeachingTaskCard.vue'
import DirectEntryConfigDialog from '@/pages/assessment/components/DirectEntryConfigDialog.vue'
import type { AssessmentInfo, AssessmentPublishRequest } from '@/api/assessment/assessment'
import { publishAssessmentToTasks } from '@/api/assessment/assessment'
import type { TaskWorkDetailVO } from '@/api/teaching/task'
import { getTaskWorkListByTaskYear } from '@/api/teaching/task'
import { getDictOptionsByTypeTitle } from '@/utils/dictUtil'

// 类型定义（与 DirectEntryConfigDialog 保持一致）
interface CourseInfo {
  id: string
  name: string
  semester?: string
}

interface CourseObjective {
  id: string
  number: number
  identifier: string
  description: string
}

interface ObjectiveScore {
  id: string
  number: number
  identifier: string
  description: string
  score: number
}

// Props 定义
interface Props {
  visible: boolean
  assessmentData: AssessmentInfo | null
}

const props = defineProps<Props>()

// Emits 定义
const emit = defineEmits<{
  'update:visible': [visible: boolean]
  'success': []
}>()

// 响应式数据
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 全屏状态
const isFullscreen = ref(false)

// 新的响应式数据
const participateInCalculation = ref(false)
const selectedTaskIds = ref<number[]>([])
const teachingTasks = ref<TaskWorkDetailVO[]>([])
const loadingTasks = ref(false)
const submitting = ref(false)
const previousSelectedTasks = ref<number[]>([]) // 保存用户之前的选择状态

// 指标分值占比对话框相关数据
const scoreRatioDialogVisible = ref(false)
const courseInfo = ref<CourseInfo>({
  id: '',
  name: '',
  semester: ''
})
const courseObjectives = ref<CourseObjective[]>([])
const initialScores = ref<ObjectiveScore[]>([])

// 考核方法字典
const assessmentMethodDict = ref<Record<string | number, string>>({})

// 计算属性
const canPublish = computed(() => {
  if (participateInCalculation.value) {
    return teachingTasks.value.length > 0
  }
  return selectedTaskIds.value.length > 0
})

const confirmButtonText = computed(() => {
  if (participateInCalculation.value) {
    return `发布到所有任务 (${teachingTasks.value.length}个)`
  }
  return `发布到 ${selectedTaskIds.value.length} 个任务`
})

// 方法
const getAssessmentMethodLabel = (value: string | number | undefined) => {
  if (!value) return '未知'
  return assessmentMethodDict.value[value] || value
}

const getTaskName = (taskId: number) => {
  const task = teachingTasks.value.find(t => t.taskId === taskId)
  return task?.taskName || '未知任务'
}

const getTeacherName = (taskId: number) => {
  const task = teachingTasks.value.find(t => t.taskId === taskId)
  const mainTeacher = task?.teachers?.find(t => t.role === 1)
  return mainTeacher?.teacherName || '未知教师'
}

// 切换全屏状态
const toggleFullscreen = () => {
  isFullscreen.value = !isFullscreen.value
  MessagePlugin.info(isFullscreen.value ? '已切换到全屏模式' : '已退出全屏模式')
}

const selectAll = () => {
  if (teachingTasks.value.length === 0) {
    MessagePlugin.info('暂无可选择的教学任务')
    return
  }

  selectedTaskIds.value = teachingTasks.value.map(task => task.taskId)
  MessagePlugin.success(`已选择 ${teachingTasks.value.length} 个教学任务`)
}

const clearAll = () => {
  if (selectedTaskIds.value.length === 0) {
    MessagePlugin.info('当前没有已选择的任务')
    return
  }

  const count = selectedTaskIds.value.length
  selectedTaskIds.value = []
  MessagePlugin.success(`已清空 ${count} 个选择的任务`)
}

// 处理达成度计算模式变化
const handleCalculationModeChange = (value: boolean) => {
  if (value) {
    // 选中达成度计算：保存当前选择状态，然后选择所有任务
    previousSelectedTasks.value = [...selectedTaskIds.value]
    selectedTaskIds.value = teachingTasks.value.map(task => task.taskId)
    MessagePlugin.info('已自动选择所有教学任务，将参与达成度计算')
  } else {
    // 取消达成度计算：恢复之前的选择状态
    selectedTaskIds.value = [...previousSelectedTasks.value]
    MessagePlugin.info('已恢复手动选择模式')
  }
}

const handleTaskToggle = (taskId: number, selected: boolean) => {
  // 如果是达成度计算模式，禁用任务切换
  if (participateInCalculation.value) {
    return
  }
  // 验证任务是否存在
  const task = teachingTasks.value.find(t => t.taskId === taskId)
  if (!task) {
    MessagePlugin.warning('该教学任务已失效，请刷新页面')
    return
  }

  if (selected) {
    if (!selectedTaskIds.value.includes(taskId)) {
      selectedTaskIds.value.push(taskId)
    }
  } else {
    const index = selectedTaskIds.value.indexOf(taskId)
    if (index > -1) {
      selectedTaskIds.value.splice(index, 1)
    }
  }
}

const removeSelectedTask = (taskId: number) => {
  const index = selectedTaskIds.value.indexOf(taskId)
  if (index > -1) {
    selectedTaskIds.value.splice(index, 1)
  }
}

// 处理查看指标分值占比
const handleShowScoreRatio = () => {
  if (!props.assessmentData) {
    MessagePlugin.warning('考核数据异常，无法查看指标分值占比')
    return
  }

  // 准备课程信息
  courseInfo.value = {
    id: props.assessmentData.courseId?.toString() || '',
    name: props.assessmentData.courseName || '未知课程',
    semester: `${props.assessmentData.assessmentYear}学年第${props.assessmentData.assessmentTerm}学期`
  }

  // 准备课程目标数据（这里需要根据实际的考核数据结构来获取）
  // 如果考核数据中包含课程目标信息，可以从中提取
  // 否则可能需要调用API获取课程目标数据
  //courseObjectives.value = generateMockCourseObjectives()

  // 准备初始分数数据
  //initialScores.value = generateMockInitialScores()

  // 降低当前对话框的层级
  adjustParentDialogZIndex(false)

  // 打开对话框
  scoreRatioDialogVisible.value = true
}

// 处理指标分值占比保存
const handleScoreRatioSave = (scores: ObjectiveScore[]) => {
  console.log('保存指标分值占比:', scores)
  MessagePlugin.success('指标分值占比已保存')
  scoreRatioDialogVisible.value = false

  // 恢复当前对话框的层级
  adjustParentDialogZIndex(true)
}

// 调整父对话框的层级
const adjustParentDialogZIndex = (restore: boolean) => {
  // 获取当前对话框的DOM元素
  const parentDialog = document.querySelector('.t-dialog__wrap')
  if (parentDialog) {
    if (restore) {
      // 恢复原始层级
      ;(parentDialog as HTMLElement).style.zIndex = ''
    } else {
      // 降低层级，让子对话框显示在上面
      ;(parentDialog as HTMLElement).style.zIndex = '1000'
    }
  }
}

// // 生成模拟的课程目标数据（实际项目中应该从API获取）
// const generateMockCourseObjectives = (): CourseObjective[] => {
//   // 根据考核数据生成相应的课程目标
//   const courseName = props.assessmentData?.courseName || '未知课程'

//   if (courseName.includes('Java') || courseName.includes('程序设计')) {
//     return [
//       {
//         id: '1',
//         number: 1,
//         identifier: '课程目标1',
//         description: '掌握Java基本语法、数据类型、运算符等基础知识'
//       },
//       {
//         id: '2',
//         number: 2,
//         identifier: '课程目标2',
//         description: '理解面向对象编程的基本概念，掌握类、对象、继承、封装等核心知识'
//       },
//       {
//         id: '3',
//         number: 3,
//         identifier: '课程目标3',
//         description: '掌握Java常用类库的使用方法，能够运用工程思维解决实际编程问题'
//       }
//     ]
//   } else if (courseName.includes('数据结构') || courseName.includes('算法')) {
//     return [
//       {
//         id: '1',
//         number: 1,
//         identifier: '课程目标1',
//         description: '掌握基本数据结构的概念、特点和应用场景'
//       },
//       {
//         id: '2',
//         number: 2,
//         identifier: '课程目标2',
//         description: '能够分析算法的时间复杂度和空间复杂度'
//       },
//       {
//         id: '3',
//         number: 3,
//         identifier: '课程目标3',
//         description: '具备运用数据结构解决实际问题的能力'
//       }
//     ]
//   } else {
//     // 通用课程目标
//     return [
//       {
//         id: '1',
//         number: 1,
//         identifier: '课程目标1',
//         description: '掌握基本理论知识和核心概念'
//       },
//       {
//         id: '2',
//         number: 2,
//         identifier: '课程目标2',
//         description: '具备实际应用和分析解决问题的能力'
//       },
//       {
//         id: '3',
//         number: 3,
//         identifier: '课程目标3',
//         description: '培养创新思维和综合运用知识的能力'
//       }
//     ]
//   }
// }

// // 生成模拟的初始分数数据
// const generateMockInitialScores = (): ObjectiveScore[] => {
//   const objectives = generateMockCourseObjectives()
//   const totalScore = 100
//   const averageScore = Math.floor(totalScore / objectives.length)
//   const remainder = totalScore % objectives.length

//   return objectives.map((obj, index) => ({
//     id: obj.id,
//     number: obj.number,
//     identifier: obj.identifier,
//     description: obj.description,
//     score: averageScore + (index < remainder ? 1 : 0)
//   }))
// }

const loadTeachingTasks = async () => {
  if (!props.assessmentData?.courseId) {
    MessagePlugin.warning('缺少课程信息，无法加载教学任务')
    return
  }

  if (!props.assessmentData.assessmentYear || !props.assessmentData.assessmentTerm) {
    MessagePlugin.warning('缺少学年学期信息，无法加载教学任务')
    return
  }

  try {
    loadingTasks.value = true
    const response = await getTaskWorkListByTaskYear(props.assessmentData.courseId, {
      taskYear: props.assessmentData.assessmentYear,
      taskTerm: props.assessmentData.assessmentTerm
    })
    participateInCalculation.value =  props.assessmentData.achievement

    console.log('考核发布：加载教学任务:', response)
    if (response?.code === 200 && response.data) {
      const tasks = response.data || []
      if (tasks.length === 0) {
        MessagePlugin.info('当前学年学期暂无教学任务，请先创建教学任务')
      }
      teachingTasks.value = tasks
    } else {
      const errorMsg = response?.message || '获取教学任务列表失败'
      MessagePlugin.error(errorMsg)
      teachingTasks.value = []
    }
  } catch (error) {
    console.error('加载教学任务失败:', error)
    if (error instanceof Error) {
      MessagePlugin.error(`加载教学任务失败: ${error.message}`)
    } else {
      MessagePlugin.error('网络连接异常，请检查网络后重试')
    }
    teachingTasks.value = []
  } finally {
    loadingTasks.value = false
  }
}

const handleConfirm = async () => {
  // 基础数据验证
  if (!props.assessmentData) {
    MessagePlugin.error('考核数据异常，请重新打开发布窗口')
    return
  }

  // 发布模式验证
  if (!participateInCalculation.value) {
    if (selectedTaskIds.value.length === 0) {
      MessagePlugin.warning('请至少选择一个教学任务进行发布')
      return
    }

    // 验证选中的任务是否仍然存在
    const invalidTaskIds = selectedTaskIds.value.filter(taskId =>
      !teachingTasks.value.find(t => t.taskId === taskId)
    )

    if (invalidTaskIds.length > 0) {
      MessagePlugin.error('部分选中的教学任务已失效，请重新选择')
      selectedTaskIds.value = selectedTaskIds.value.filter(taskId =>
        !invalidTaskIds.includes(taskId)
      )
      return
    }
  } else {
    // 达成度计算模式：确保所有任务都被选中
    selectedTaskIds.value = teachingTasks.value.map(task => task.taskId)
  }

  // 考核状态验证
  if (props.assessmentData.assessmentStatus === 0) {
    MessagePlugin.warning('考核尚未配置完成，无法发布')
    return
  }

  try {
    submitting.value = true

    // 构建发布数据
    const publishData: AssessmentPublishRequest = {
      assessmentId: props.assessmentData.id,
      taskIds: selectedTaskIds.value,
      courseId: props.assessmentData.courseId,
      publishToAll: participateInCalculation.value,
      // courseIds: participateInCalculation.value
      //   ? [props.assessmentData.courseId]
      //   : selectedTaskIds.value.map(taskId => {
      //       const task = teachingTasks.value.find(t => t.taskId === taskId)
      //       return task ? props.assessmentData!.courseId : null
      //     }).filter(Boolean) as number[]
    }

    // 调用发布API
    const response = await publishAssessmentToTasks(publishData)

    if (response.code === 200) {
      const taskCount = participateInCalculation.value ? '所有' : selectedTaskIds.value.length
      const calculationText = participateInCalculation.value ? '（参与达成度计算）' : ''
      MessagePlugin.success(`考核已成功发布到${taskCount}个教学任务${calculationText}`)
      emit('success')
    } else {
      const errorMsg = response.message || '发布失败，请重试'
      MessagePlugin.error(errorMsg)
    }
  } catch (error) {
    console.error('发布失败:', error)

    // 详细错误处理
    if (error instanceof Error) {
      if (error.message.includes('timeout')) {
        MessagePlugin.error('请求超时，请检查网络连接后重试')
      } else if (error.message.includes('401') || error.message.includes('403')) {
        MessagePlugin.error('权限不足，请联系管理员')
      } else if (error.message.includes('500')) {
        MessagePlugin.error('服务器内部错误，请稍后重试')
      } else {
        MessagePlugin.error(`发布失败: ${error.message}`)
      }
    } else {
      MessagePlugin.error('网络异常，请检查网络连接后重试')
    }
  } finally {
    submitting.value = false
  }
}

const handleCancel = () => {
  dialogVisible.value = false
}

const handleClose = () => {
  dialogVisible.value = false
}

// 监听弹窗显示状态
watch(() => props.visible, (newVal) => {
  if (newVal) {
    // 验证考核数据
    if (!props.assessmentData) {
      MessagePlugin.error('考核数据异常，无法打开发布窗口')
      emit('update:visible', false)
      return
    }

    // 验证考核状态
    if (props.assessmentData.assessmentStatus === 0) {
      MessagePlugin.warning('考核尚未配置完成，请先完成考核配置')
      emit('update:visible', false)
      return
    }

    // 重置状态
    participateInCalculation.value = false
    selectedTaskIds.value = []
    teachingTasks.value = []
    previousSelectedTasks.value = []

    // 加载教学任务
    loadTeachingTasks()
  }
})

// 监听教学任务变化，在达成度计算模式下自动选择所有任务
watch(teachingTasks, (newTasks) => {
  if (participateInCalculation.value && newTasks.length > 0) {
    selectedTaskIds.value = newTasks.map(task => task.taskId)
  }
}, { immediate: true })

// 监听指标分值占比对话框的关闭，确保层级恢复
watch(scoreRatioDialogVisible, (newValue) => {
  if (!newValue) {
    // 对话框关闭时恢复父对话框层级
    adjustParentDialogZIndex(true)
  }
})

// 组件挂载时加载字典数据
onMounted(async () => {
  try {
    const methodArr = await getDictOptionsByTypeTitle('考核环节')
    assessmentMethodDict.value = {}
    methodArr.forEach(item => {
      assessmentMethodDict.value[item.value] = item.label
    })
  } catch (error) {
    console.error('加载考核方法字典失败:', error)
  }
})
</script>

<style lang="less" scoped>
// 对话框头部样式
.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--td-comp-paddingTB-l) var(--td-comp-paddingLR-l);
  border-bottom: 1px solid var(--td-border-level-1-color);
  background: var(--td-bg-color-container);

  .dialog-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--td-text-color-primary);
  }

  .fullscreen-btn {
    :deep(.t-button) {
      color: var(--td-text-color-secondary);
      transition: all var(--td-transition);

      &:hover {
        color: var(--td-brand-color);
        background: var(--td-brand-color-1);
      }

      .t-icon {
        font-size: 16px;
      }
    }
  }
}

.assessment-publishing-content {
  padding: var(--td-comp-paddingTB-xl) var(--td-comp-paddingLR-xl);
  max-width: 1400px;
  width: 100%;
  margin: 0 auto;
}

.assessment-info-section {
  margin-bottom: var(--td-comp-margin-xl);
}

.assessment-info-card {
  background: linear-gradient(135deg, var(--td-brand-color) 0%, var(--td-brand-color-8) 100%);
  color: var(--td-text-color-anti);
  border-radius: var(--td-radius-large);
  box-shadow: var(--td-shadow-3);

  :deep(.t-card__body) {
    padding: var(--td-comp-paddingTB-xxl) var(--td-comp-paddingLR-xxl);
  }

  .assessment-header {
    .assessment-title {
      display: flex;
      align-items: center;
      gap: var(--td-comp-margin-m);
      margin-bottom: var(--td-comp-margin-l);

      .title-icon {
        font-size: 24px;
        color: var(--td-text-color-anti);
        opacity: 0.9;
      }

      h3 {
        margin: 0;
        font-size: 20px;
        font-weight: 600;
        color: var(--td-text-color-anti);
        letter-spacing: 0.5px;
        flex: 1;
      }

      :deep(.t-tag) {
        background: rgba(255, 255, 255, 0.2);
        color: var(--td-text-color-anti);
        border: 1px solid rgba(255, 255, 255, 0.3);
      }

      .score-ratio-btn {
        :deep(.t-button) {
          background: rgba(255, 255, 255, 0.15);
          border: 1px solid rgba(255, 255, 255, 0.3);
          color: var(--td-text-color-anti);
          font-size: var(--td-font-size-body-small);
          font-weight: 500;
          transition: all var(--td-transition);

          &:hover {
            background: rgba(255, 255, 255, 0.25);
            border-color: rgba(255, 255, 255, 0.5);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
          }

          .t-icon {
            font-size: 14px;
            margin-right: var(--td-comp-margin-xs);
          }
        }
      }
    }

    .assessment-meta {
      display: flex;
      gap: var(--td-comp-margin-xxl);

      .meta-item {
        display: flex;
        align-items: center;
        gap: var(--td-comp-margin-s);
        font-size: var(--td-font-size-body-medium);
        opacity: 0.9;
        color: var(--td-text-color-anti);

        :deep(.t-icon) {
          font-size: 16px;
          opacity: 0.8;
        }
      }
    }
  }
}

.publish-options-section {
  margin-bottom: var(--td-comp-margin-xl);
}

.publish-options-card {
  :deep(.t-card__body) {
    padding: var(--td-comp-paddingTB-xl) var(--td-comp-paddingLR-xl);
  }

  .section-header {
    margin-bottom: var(--td-comp-margin-xl);

    h4 {
      margin: 0 0 var(--td-comp-margin-l) 0;
      font-size: 18px;
      font-weight: 600;
      color: var(--td-text-color-primary);
    }

    .achievement-calculation-option {
      .calculation-checkbox {
        margin-bottom: var(--td-comp-margin-l);

        :deep(.t-checkbox__label) {
          font-size: 16px;
          font-weight: 500;
          color: var(--td-text-color-primary);
          margin-left: var(--td-comp-margin-s);
        }
      }

      .calculation-status {
        display: flex;
        align-items: center;
        gap: var(--td-comp-margin-m);
        padding: var(--td-comp-paddingTB-m) var(--td-comp-paddingLR-l);
        background: var(--td-bg-color-container-hover);
        border-radius: var(--td-radius-medium);
        font-size: var(--td-font-size-body-medium);
        border-left: 4px solid var(--td-brand-color);
        box-shadow: var(--td-shadow-1);

        :deep(.t-icon) {
          font-size: 16px;
        }

        .success-text {
          color: var(--td-success-color-7);
          font-weight: 500;
        }

        .info-text {
          color: var(--td-text-color-secondary);
          font-weight: 500;
        }
      }
    }
  }


}

.task-selection-section,
.selected-tasks-section {
  margin-bottom: var(--td-comp-margin-xl);
}

.task-selection-card,
.selected-tasks-card {
  :deep(.t-card__body) {
    padding: var(--td-comp-paddingTB-xl) var(--td-comp-paddingLR-xl);
  }

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--td-comp-margin-xl);

    h4 {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
      color: var(--td-text-color-primary);
    }

    .selection-controls {
      display: flex;
      align-items: center;
      gap: var(--td-comp-margin-m);

      :deep(.t-button) {
        font-size: var(--td-font-size-body-medium);
        padding: var(--td-comp-paddingTB-s) var(--td-comp-paddingLR-m);
      }

      .selection-count {
        font-size: var(--td-font-size-body-medium);
        color: var(--td-text-color-secondary);
        margin-left: var(--td-comp-margin-s);
        font-weight: 500;
      }
    }

    .locked-selection-notice {
      display: flex;
      align-items: center;
      gap: var(--td-comp-margin-m);
      padding: var(--td-comp-paddingTB-m) var(--td-comp-paddingLR-l);
      background: var(--td-warning-color-1);
      border: 1px solid var(--td-warning-color-3);
      border-radius: var(--td-radius-medium);
      color: var(--td-warning-color-8);
      font-size: var(--td-font-size-body-medium);
      font-weight: 500;
      box-shadow: var(--td-shadow-1);

      :deep(.t-icon) {
        color: var(--td-warning-color-6);
        font-size: 16px;
      }
    }
  }
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
  padding: var(--td-comp-paddingTB-xxl) 0;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  padding: var(--td-comp-paddingTB-xxl) 0;
  color: var(--td-text-color-placeholder);

  :deep(.t-icon) {
    color: var(--td-text-color-disabled);
    margin-bottom: var(--td-comp-margin-m);
  }

  p {
    margin: var(--td-comp-margin-m) 0;
    font-size: var(--td-font-size-body-medium);
    color: var(--td-text-color-secondary);
  }
}

.task-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
  gap: var(--td-comp-margin-l);
  margin-top: var(--td-comp-margin-l);
}

.selected-tasks-list {
  .selected-task-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--td-comp-paddingTB-s) var(--td-comp-paddingLR-m);
    background: var(--td-bg-color-container-hover);
    border-radius: var(--td-radius-medium);
    margin-bottom: var(--td-comp-margin-s);
    border: 1px solid var(--td-border-level-1-color);
    transition: all var(--td-transition);

    &:hover {
      background: var(--td-bg-color-container-active);
      border-color: var(--td-border-level-2-color);
    }

    .task-info {
      display: flex;
      flex-direction: column;
      gap: var(--td-comp-margin-xs);

      .task-name {
        font-weight: var(--td-font-weight-medium);
        font-size: var(--td-font-size-body-medium);
        color: var(--td-text-color-primary);
      }

      .teacher-name {
        font-size: var(--td-font-size-body-small);
        color: var(--td-text-color-secondary);
      }
    }
  }
}

// 指标分值占比对话框层级样式
:global(.score-ratio-dialog) {
  .t-dialog__wrap {
    z-index: 10000 !important; // 确保在当前对话框之上
  }

  .t-dialog {
    z-index: 10001 !important;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3) !important; // 增强阴影效果
  }

  .t-dialog__mask {
    z-index: 9999 !important;
    background-color: rgba(0, 0, 0, 0.7) !important; // 增强遮罩效果
  }

  // 确保对话框内容在最顶层
  .t-dialog__body {
    position: relative;
    z-index: 10002;
  }

  // 确保对话框头部和底部也在正确层级
  .t-dialog__header,
  .t-dialog__footer {
    position: relative;
    z-index: 10002;
  }
}
</style>

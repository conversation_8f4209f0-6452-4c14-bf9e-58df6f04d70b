<template>
  <div>
    <!-- 直接录入模式 - 普通弹窗 -->
    <t-dialog
      v-model:visible="directDialogVisible"
      :footer="false"
      :header="directDialogTitle"
      :top="50"
      class="grade-dialog-minwidth"
      width="90vw"
      @close="handleClose"
    >
      <div class="grade-management-dialog" @mousedown="handleDialogMouseDown">
        <!-- 考核内容信息 -->
        <div class="assessment-info">
          <div class="info-content">
            <div class="info-meta">
              <div class="meta-item">
                <span class="label">班级：</span>
                <span class="value">{{ classInfo?.className }}</span>
              </div>
              <div class="meta-item">
                <span class="label">人数：</span>
                <span class="value">{{ classInfo?.studentCount }}人</span>
              </div>
              <div class="meta-item">
                <span class="label">录入方式：</span>
                <t-tag size="small" theme="primary">直接录入</t-tag>
              </div>
            </div>
          </div>
          <div class="info-actions">
            <t-button theme="primary" @click="handleImportGrades">
              <template #icon>
                <t-icon name="upload" />
              </template>
              导入成绩
            </t-button>
            <t-button theme="default" variant="outline" @click="handleExportGrades">
              <template #icon>
                <t-icon name="file-export" />
              </template>
              导出成绩
            </t-button>
            <t-button theme="default" variant="outline" @click="handleRefresh">
              <template #icon>
                <t-icon name="refresh" />
              </template>
              刷新
            </t-button>
          </div>
        </div>

        <!-- 成绩统计卡片 -->
        <div v-if="classInfo" class="stats-cards">
          <div class="stat-card">
            <div class="stat-icon average">
              <t-icon name="chart-line" />
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ gradeStats.averageScore }}</div>
              <div class="stat-label">平均分</div>
            </div>
          </div>

          <div class="stat-card">
            <div class="stat-icon highest">
              <t-icon name="arrow-up" />
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ gradeStats.maxScore }}</div>
              <div class="stat-label">最高分</div>
            </div>
          </div>

          <div class="stat-card">
            <div class="stat-icon lowest">
              <t-icon name="arrow-down" />
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ gradeStats.minScore }}</div>
              <div class="stat-label">最低分</div>
            </div>
          </div>

          <div class="stat-card">
            <div class="stat-icon submitted">
              <t-icon name="check-circle" />
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ gradeStats.submittedCount }}</div>
              <div class="stat-label">已录入</div>
            </div>
          </div>

          <div class="stat-card">
            <div class="stat-icon pending">
              <t-icon name="time" />
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ gradeStats.pendingCount }}</div>
              <div class="stat-label">待录入</div>
            </div>
          </div>
        </div>

        <!-- 搜索工具栏 -->
        <div class="search-toolbar">
          <div class="search-section">
            <t-input
              v-model="searchKeyword"
              clearable
              placeholder="搜索学号、姓名或班级"
              style="width: 200px"
            >
              <template #prefix-icon>
                <t-icon name="search" />
              </template>
            </t-input>
            <span class="status-hint">{{ statusDescription }}</span>
          </div>
          <div class="toolbar-actions">
            <t-button
              :disabled="!hasChanges"
              theme="warning"
              @click="handleSaveChanges"
            >
              <template #icon>
                <t-icon name="save" />
              </template>
              保存修改{{ changedCellsCount > 0 ? ` (${changedCellsCount})` : '' }}
            </t-button>
            <t-button
              :disabled="!hasChanges"
              theme="default"
              variant="outline"
              @click="handleCancelChanges"
            >
              <template #icon>
                <t-icon name="close" />
              </template>
              取消保存
            </t-button>
            <t-button
              :disabled="hasChanges"
              theme="success"
              @click="handleSubmitGrades"
            >
              <template #icon>
                <t-icon name="check" />
              </template>
              提交成绩
            </t-button>
          </div>
        </div>

        <!-- 学生成绩表格 -->
        <div class="grade-table">
          <t-table
            :columns="directTableColumns"
            :data="filteredDirectGradeList"
            :loading="tableLoading"
            :pagination="directPagination"
            hover
            row-key="studentId"
            stripe
            @page-change="handleDirectPageChange"
          >

            <!-- 课程目标列的行内编辑 -->
            <template v-for="objective in assessmentContent?.courseObjectives || []" :key="objective.id" #[`courseTargetScores.${objective.id}`]="{ row }">
              <div
                :class="{ 'score-changed': hasScoreChanged(row.studentId, objective.id) }"
                :data-student-id="row.studentId"
                :data-target-no="objective.id"
                class="inline-edit-cell"
              >
                <t-input-number
                  v-if="editingCell?.studentId === row.studentId && editingCell?.targetNo === objective.id && scoreStatus === 1"
                  ref="inputNumberRef"
                  :decimal-places="1"
                  :min="0"
                  :model-value="getCourseTargetScore(row, objective.id)"
                  placeholder="请输入分数"
                  style="width: 120px"
                  @blur="handleCellBlur"
                  @enter="handleCellBlur"
                  @update:model-value="(value: number) => updateCourseTargetScore(row, objective.id, value)"
                />
                <t-tooltip v-else-if="hasScoreChanged(row.studentId, objective.id)" :content="`原始成绩: ${getOriginalScore(row.studentId, objective.id)}`" theme="light">
                  <span
                    :class="{ 'score-changed': hasScoreChanged(row.studentId, objective.id) }"
                    :style="{
                      cursor: scoreStatus === 1 ? 'pointer' : 'default',
                      backgroundColor: hasScoreChanged(row.studentId, objective.id) ? 'var(--td-warning-color-light)' : 'transparent',
                      color: hasScoreChanged(row.studentId, objective.id) ? 'var(--td-warning-color)' : 'inherit'
                    }"
                    class="score-display"
                    @click="scoreStatus === 1 && handleCellClick(row.studentId, objective.id)"
                  >
                    {{ getCourseTargetScoreDisplay(row, objective.id) }}
                  </span>
                </t-tooltip>
                <span
                  v-else
                  :class="{ 'score-changed': hasScoreChanged(row.studentId, objective.id) }"
                  :style="{
                    cursor: scoreStatus === 1 ? 'pointer' : 'default',
                    backgroundColor: hasScoreChanged(row.studentId, objective.id) ? 'var(--td-warning-color-light)' : 'transparent',
                    color: hasScoreChanged(row.studentId, objective.id) ? 'var(--td-warning-color)' : 'inherit'
                  }"
                  class="score-display"
                  @click="scoreStatus === 1 && handleCellClick(row.studentId, objective.id)"
                >
                  {{ getCourseTargetScoreDisplay(row, objective.id) }}
                </span>
              </div>
            </template>

            <template #totalScore="{ row }">
              <t-tooltip v-if="hasTotalScoreChanged(row.studentId)" :content="`原始总分: ${getOriginalTotalScore(row.studentId)}`" theme="light">
                <span
                  :class="[getTotalScoreClass(row.totalScore), { 'score-changed': hasTotalScoreChanged(row.studentId) } ]"
                  :style="{
                  backgroundColor: hasTotalScoreChanged(row.studentId) ? 'var(--td-warning-color-light)' : 'transparent',
                  color: hasTotalScoreChanged(row.studentId) ? 'var(--td-warning-color)' : 'inherit'
                }"
                  class="total-score"
                >
                {{ getTotalScoreDisplay(row.totalScore) }}
              </span>
              </t-tooltip>
              <span
                v-else
                :class="[getTotalScoreClass(row.totalScore), { 'score-changed': hasTotalScoreChanged(row.studentId) } ]"
                :style="{
                  backgroundColor: hasTotalScoreChanged(row.studentId) ? 'var(--td-warning-color-light)' : 'transparent',
                  color: hasTotalScoreChanged(row.studentId) ? 'var(--td-warning-color)' : 'inherit'
                }"
                class="total-score"
              >
                {{ getTotalScoreDisplay(row.totalScore) }}
              </span>
            </template>
          </t-table>
        </div>
      </div>
    </t-dialog>
    <!-- 文件导入对话框 -->
    <ImportDialog
      v-model:visible="importDialogVisible"
      :callbacks="importCallbacks"
      :config="importConfig"
      :dialog-props="{ zIndex: 3100 }"
    />
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, watch, reactive, nextTick, h } from 'vue'
import { MessagePlugin } from 'tdesign-vue-next'
import ImportDialog from '@/components/ImportDialog/index.vue'
import type { ImportConfig, ImportCallbacks } from '@/components/ImportDialog/types'
import * as XLSX from 'xlsx'
import {
  Button as TButton,
  Icon as TIcon,
  Tag as TTag,
  Dialog as TDialog,
  Input as TInput,
  InputNumber as TInputNumber,
  Table as TTable,
} from 'tdesign-vue-next'
// 引入样式文件
import './styles/grade-management.less'
// 引入 API
import { getAssessmentTaskScores, batchSaveTargetScores, importDirectEntryScore } from '@/api/assessment/assessmentScore'
import {getCourseBaseInfo } from '@/api/training/course'
import { getAssessmentById } from '@/api/assessment/assessment'
// 定义Props和Emits
interface Props {
  visible: boolean
  assessmentId: number
  taskId: number
  courseId: number | string
  classInfo?: any
  scoreStatus?: number // 0: 未开始, 1: 进行中, 2: 已提交
}

const props = defineProps<Props>()

// 记录当前编辑单元格的信息
const editingCell = ref<{ studentId: number; targetNo: number } | null>(null)

const inputNumberRef = ref<any>(null)

let lastInput: HTMLInputElement | null = null;

const isInputReallyFocused = ref(false);

let globalKeydownListener: ((e: KeyboardEvent) => void) | null = null;

function addGlobalKeydownListener() {
  if (globalKeydownListener) return;
  globalKeydownListener = (e: KeyboardEvent) => {
    if (!editingCell.value) return;
    // Shift+方向键
    if (["ArrowUp", "ArrowDown", "ArrowLeft", "ArrowRight"].includes(e.key) && e.shiftKey) {
      e.preventDefault();
      const { studentId, targetNo } = editingCell.value;
      const next = findEdgeCellByDirection(studentId, targetNo, e.key);
      if (next) {
        handleCellClick(next.studentId, next.targetNo);
      }
      return;
    }
    // Shift+Tab
    if (e.key === 'Tab' && e.shiftKey) {
      e.preventDefault();
      const { studentId, targetNo } = editingCell.value;
      const next = findPrevCell(studentId, targetNo);
      if (next) {
        handleCellClick(next.studentId, next.targetNo);
      } else {
        isInputReallyFocused.value = false;
        exitEditMode();
      }
      return;
    }
    // 普通Tab
    if (e.key === 'Tab') {
      e.preventDefault();
      const { studentId, targetNo } = editingCell.value;
      const next = findNextCell(studentId, targetNo);
      if (next) {
        handleCellClick(next.studentId, next.targetNo);
      } else {
        isInputReallyFocused.value = false;
        exitEditMode();
      }
      return;
    }
    // 普通方向键
    if (["ArrowUp", "ArrowDown", "ArrowLeft", "ArrowRight"].includes(e.key)) {
      e.preventDefault();
      const { studentId, targetNo } = editingCell.value;
      const next = findNextCellByDirection(studentId, targetNo, e.key);
      if (next) {
        handleCellClick(next.studentId, next.targetNo);
      }
      return;
    }
    // Enter
    if (e.key === 'Enter') {
      e.preventDefault();
      isInputReallyFocused.value = false;
      exitEditMode();
      return;
    }
  };
  window.addEventListener('keydown', globalKeydownListener, true);
}

function removeGlobalKeydownListener() {
  if (globalKeydownListener) {
    window.removeEventListener('keydown', globalKeydownListener, true);
    globalKeydownListener = null;
  }
}

function findNextCell(studentId: number, targetNo: number) {
  // 获取当前表格的可见数据和目标列
  const rows = filteredDirectGradeList.value;
  const targets = assessmentContent.value?.courseObjectives?.map((obj: any) => obj.id) || [];

  // 找到当前行和列的索引
  const rowIdx = rows.findIndex(row => row.studentId === studentId);
  const colIdx = targets.findIndex((id: number) => id === targetNo);

  // 先尝试同一行下一个目标
  if (colIdx < targets.length - 1) {
    return { studentId, targetNo: targets[colIdx + 1] };
  }
  // 否则跳到下一行的第一个目标
  if (rowIdx < rows.length - 1) {
    return { studentId: rows[rowIdx + 1].studentId, targetNo: targets[0] };
  }
  // 已经是最后一个单元格，循环到第一个成绩单元格
  if (rows.length > 0 && targets.length > 0) {
    return { studentId: rows[0].studentId, targetNo: targets[0] };
  }
  return null;
}

// 方向键查找下一个单元格
function findNextCellByDirection(studentId: number, targetNo: number, direction: string) {
  const rows = filteredDirectGradeList.value;
  const targets = assessmentContent.value?.courseObjectives?.map((obj: any) => obj.id) || [];
  const rowIdx = rows.findIndex(row => row.studentId === studentId);
  const colIdx = targets.findIndex((id: number) => id === targetNo);
  let nextRowIdx = rowIdx, nextColIdx = colIdx;
  if (direction === 'ArrowRight') {
    nextColIdx = (colIdx + 1) % targets.length;
  } else if (direction === 'ArrowLeft') {
    nextColIdx = (colIdx - 1 + targets.length) % targets.length;
  } else if (direction === 'ArrowDown') {
    nextRowIdx = (rowIdx + 1) % rows.length;
  } else if (direction === 'ArrowUp') {
    nextRowIdx = (rowIdx - 1 + rows.length) % rows.length;
  }
  // 只变动一维，另一维保持
  if (direction === 'ArrowRight' || direction === 'ArrowLeft') {
    return { studentId, targetNo: targets[nextColIdx] };
  } else if (direction === 'ArrowUp' || direction === 'ArrowDown') {
    return { studentId: rows[nextRowIdx].studentId, targetNo };
  }
  return null;
}

// Shift+方向键跳到边界
function findEdgeCellByDirection(studentId: number, targetNo: number, direction: string) {
  const rows = filteredDirectGradeList.value;
  const targets = assessmentContent.value?.courseObjectives?.map((obj: any) => obj.id) || [];
  const rowIdx = rows.findIndex(row => row.studentId === studentId);
  const colIdx = targets.findIndex((id: number) => id === targetNo);
  if (direction === 'ArrowRight') {
    return { studentId, targetNo: targets[targets.length - 1] };
  } else if (direction === 'ArrowLeft') {
    return { studentId, targetNo: targets[0] };
  } else if (direction === 'ArrowDown') {
    return { studentId: rows[rows.length - 1].studentId, targetNo };
  } else if (direction === 'ArrowUp') {
    return { studentId: rows[0].studentId, targetNo };
  }
  return null;
}

// Shift+Tab 反向查找
function findPrevCell(studentId: number, targetNo: number) {
  const rows = filteredDirectGradeList.value;
  const targets = assessmentContent.value?.courseObjectives?.map((obj: any) => obj.id) || [];
  const rowIdx = rows.findIndex(row => row.studentId === studentId);
  const colIdx = targets.findIndex((id: number) => id === targetNo);
  // 先尝试同一行前一个目标
  if (colIdx > 0) {
    return { studentId, targetNo: targets[colIdx - 1] };
  }
  // 否则跳到上一行最后一个目标
  if (rowIdx > 0) {
    return { studentId: rows[rowIdx - 1].studentId, targetNo: targets[targets.length - 1] };
  }
  // 已经是第一个单元格，循环到最后一个
  if (rows.length > 0 && targets.length > 0) {
    return { studentId: rows[rows.length - 1].studentId, targetNo: targets[targets.length - 1] };
  }
  return null;
}

const emit = defineEmits<{
  'update:visible': [visible: boolean]
}>()

// ==================== 响应式数据 ====================

/** 加载状态 */
const loading = ref(false)
/** 表格数据加载状态 */
const tableLoading = ref(false)
/** 操作加载状态 */
const operationLoading = ref(false)

/** 课程数据 */
const courseBaseInfo = ref<any>({})

/** 考核内容数据 */
const assessmentContent = ref<any>(null)

/** 考核任务成绩数据 */
const assessmentTaskScores = ref<any>({})

/** 成绩统计数据 */
const gradeStats = ref<any>({
  averageScore: 0,
  maxScore: 0,
  minScore: 0,
  submittedCount: 0,
  pendingCount: 0
})

/** 成绩明细数据 */
const gradeList = ref<any[]>([])

/** 搜索关键词 */
const searchKeyword = ref('')

// 弹窗显示状态
const directDialogVisible = ref(false)
const importDialogVisible = ref(false)

/** 原始成绩数据 */
const originalGradeData = ref<StudentGradeData[]>([])

/** 编辑中的成绩数据 */
const editingGradeData = ref<StudentGradeData[]>([])

/** 数据变更状态 */
const hasChanges = ref(false)

/** 直接录入分页配置 */
const directPagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showJumper: true,
  showSizeChanger: true,
  pageSizeOptions: [10, 20, 50, 100]
})







// ==================== 数据接口定义 ====================

/** 批量保存成绩数据结构 */
interface BatchSaveTargetScoresDTO {
  assessmentId: number        // 当前考核ID
  taskId: number             // 当前教学任务ID
  studentScores: StudentTargetScoreDTO[]
}

interface StudentTargetScoreDTO {
  studentId: number          // 学生ID
  objectiveId: number        // 课程目标ID
  courseTargetNo: number     // 课程目标编号
  repositoryAnswerId?: number // 题目答案ID（可选）
  score: number              // 学生得分
  remark?: string            // 备注（可选）
}

/** 课程目标成绩接口 */
interface CourseTargetScore {
  courseTargetNo: number
  objectiveId: number
  courseTargetName: string
  score?: number
  fullScore?: number
  scoreRate?: number
  poId?: number
}

/** 学生成绩数据接口 */
interface StudentGradeData {
  studentId: number
  studentNumber: string
  studentName: string
  classId: number
  className: string
  totalScore: number
  fullScore: number
  scoreRate: number
  scoreGrade: string
  courseTargetScores: CourseTargetScore[]
  detailScores: any[]
  entryStatus: string
  entryTime: string
  lastModifyTime: string
  entryUserId: number
  entryUserName: string
}



// ==================== 计算属性 ====================

/** 直接录入弹窗标题 */
const directDialogTitle = computed(() => {
  return `成绩管理 - ${assessmentContent.value?.title || assessmentContent.value?.assessmentName || ''}`
})

/** 过滤后的直接录入成绩列表 */
const filteredDirectGradeList = computed(() => {
  let filtered = [...editingGradeData.value]

  // 搜索过滤
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    filtered = filtered.filter(item =>
      item.studentNumber.toLowerCase().includes(keyword) ||
      item.studentName.toLowerCase().includes(keyword) ||
      item.className.toLowerCase().includes(keyword)
    )
  }

  // 更新分页总数
  directPagination.total = filtered.length

  // 分页处理
  const start = (directPagination.current - 1) * directPagination.pageSize
  const end = start + directPagination.pageSize

  return filtered.slice(start, end)
})

/** 直接录入表格列定义 */
const directTableColumns = computed(() => {
  const columns: any[] = [
    {
      colKey: 'studentNumber',
      title: '学号',
      width: 120,
      align: 'center' as const
    },
    {
      colKey: 'studentName',
      title: '姓名',
      width: 100,
      align: 'center' as const
    },
    {
      colKey: 'className',
      title: '班级',
      width: 150,
      align: 'center' as const
    }
  ];

  // 添加课程目标列
  if (assessmentContent.value?.courseObjectives) {
    assessmentContent.value.courseObjectives.forEach((objective: any) => {
      columns.push({
        colKey: `courseTargetScores.${objective.id}`,
        title: objective.name,

        width: 120,
        align: 'center' as const
      });
    });
  }

  columns.push({
    colKey: 'totalScore',
    title: '总分',
    width: 100,
    align: 'center' as const
  });
  return columns;
});

// ==================== 导入配置 ====================

/** 生成导入模板数据 */
const generateImportTemplateData = () => {
  const headers = ['学号', '姓名', '班级'];

  // 添加课程目标列（使用id作为编号，并包含名称注释）
  if (assessmentContent.value?.courseObjectives) {
    assessmentContent.value.courseObjectives.forEach((objective: any) => {
      headers.push(`课程目标${objective.id}`);
    });
  }
  headers.push('总分');

  // 生成学生数据行
  const studentRows: string[][] = [];
  if (gradeList.value && gradeList.value.length > 0) {
    gradeList.value.forEach((student: any) => {
      const row = [
        student.studentNumber || '',
        student.studentName || '',
        student.className || ''
      ];

      // 为每个课程目标添加空成绩列
      if (assessmentContent.value?.courseObjectives) {
        assessmentContent.value.courseObjectives.forEach((objective: any) => {
          row.push(''); // 成绩列留空
        });
      }

      // 总分列放在最后，添加条件计算公式
      const courseObjectiveCount = assessmentContent.value?.courseObjectives?.length || 0;
      if (courseObjectiveCount > 0) {
        // 生成条件公式：只要有一个课程目标有成绩就显示总分
        const startCol = 'D'; // 课程目标列起始列（第4列：A=0, B=1, C=2, D=3）
        const endCol = String.fromCharCode(67 + courseObjectiveCount); // 计算结束列（67是'C'的ASCII码，+课程目标数量）
        // Excel条件公式说明：
        // COUNTBLANK(D2:F2) - 统计D2到F2范围内空白单元格的数量
        // < courseObjectiveCount - 如果空白单元格数量小于总课程目标数量，说明至少有一个成绩
        // SUM(D2:F2) - 计算D2到F2范围内所有数值的总和
        // "" - 如果所有单元格都为空，则显示空白
        const formula = `=IF(COUNTBLANK(${startCol}2:${endCol}2)<${courseObjectiveCount},SUM(${startCol}2:${endCol}2),"")`;
        row.push(formula);
      } else {
        row.push(''); // 如果没有课程目标，总分列为空
      }

      studentRows.push(row);
    });
  }

  return [headers, ...studentRows];
};

/** 下载Excel模板 - 使用标准xlsx实现 */
const downloadProtectedTemplate = () => {
  try {
    // 生成模板数据 - 包含表头和学生信息
    const templateData = generateImportTemplateData();

    // 使用xlsx创建基础工作簿和工作表
    const wb = XLSX.utils.book_new(); // 创建新的Excel工作簿
    const ws = XLSX.utils.aoa_to_sheet(templateData); // 将二维数组转换为工作表

    // 设置列宽 - 定义每列的显示宽度
    const colWidths = [
      { width: 15 }, // 学号列宽度：15个字符
      { width: 12 }, // 姓名列宽度：12个字符
      { width: 20 }, // 班级列宽度：20个字符
    ];

    // 为课程目标列添加宽度 - 动态添加课程目标列的宽度
    if (assessmentContent.value?.courseObjectives) {
      assessmentContent.value.courseObjectives.forEach(() => {
        colWidths.push({ width: 15 }); // 每个课程目标列宽度：15个字符
      });
    }
    colWidths.push({ width: 10 }); // 总分列宽度：10个字符
    ws['!cols'] = colWidths; // 将列宽配置应用到工作表

    // 设置单元格保护 - 控制哪些单元格可以编辑
    const range = XLSX.utils.decode_range(ws['!ref'] || 'A1'); // 获取工作表的单元格范围
    const courseObjectiveCount = assessmentContent.value?.courseObjectives?.length || 0; // 获取课程目标数量
  
    // 遍历所有单元格，设置锁定状态
    for (let row = range.s.r; row <= range.e.r; row++) {
      for (let col = range.s.c; col <= range.e.c; col++) {
        const cellAddress = XLSX.utils.encode_cell({ r: row, c: col }); // 生成单元格地址（如A1, B2等）
        if (!ws[cellAddress]) {
          ws[cellAddress] = { v: templateData[row][col] }; // 如果单元格不存在，创建并设置值
        }
        
        // 根据单元格位置设置锁定状态
        if (row === 0) {
          // 表头行（第1行）全部锁定 - 防止用户修改表头
          if (!ws[cellAddress].s) ws[cellAddress].s = {};
          ws[cellAddress].s.protection = { locked: true };
        } else {
          // 数据行（第2行开始）
          if (col === 0 || col === 1 || col === 2) {
            // 学号列(0)、姓名列(1)、班级列(2)锁定 - 防止修改学生基本信息
            if (!ws[cellAddress].s) ws[cellAddress].s = {};
            ws[cellAddress].s.protection = { locked: true };
          } else if (col >= 3 && col < 3 + courseObjectiveCount) {
            // 课程目标列（从第4列开始）不锁定 - 允许用户输入成绩
            if (!ws[cellAddress].s) ws[cellAddress].s = {};
            ws[cellAddress].s.protection = { locked: false };
          } else if (col === 3 + courseObjectiveCount) {
            // 总分列（课程目标列之后）锁定，并设置条件公式
            if (courseObjectiveCount > 0) {
              const startCol = 'D'; // 课程目标列起始列（第4列）
              const endCol = String.fromCharCode(67 + courseObjectiveCount); // 计算结束列（67是'C'的ASCII码）
              // 条件公式：只要有一个课程目标有成绩就显示总分，否则显示空白
              const formula = `=IF(COUNTBLANK(${startCol}${row + 1}:${endCol}${row + 1})<${courseObjectiveCount},SUM(${startCol}${row + 1}:${endCol}${row + 1}),"")`;
              ws[cellAddress] = { 
                f: formula, 
                s: { protection: { locked: true } } 
              };
            } else {
              if (!ws[cellAddress].s) ws[cellAddress].s = {};
              ws[cellAddress].s.protection = { locked: true }; // 如果没有课程目标，总分列也锁定
            }
          }
        }
      }
    }

    // 设置工作表保护以激活单元格锁定
    // ws['!protect'] = {
    //   password: '', // 无密码保护 - 用户无需输入密码即可编辑未锁定的单元格
    //   objects: false, // 禁止编辑图形对象
    //   scenarios: false, // 禁止编辑方案
    //   formatCells: false, // 禁止格式化单元格
    //   formatColumns: false, // 禁止格式化列
    //   formatRows: false, // 禁止格式化行
    //   insertColumns: false, // 禁止插入列
    //   insertRows: false, // 禁止插入行
    //   insertHyperlinks: false, // 禁止插入超链接
    //   deleteColumns: false, // 禁止删除列
    //   deleteRows: false, // 禁止删除行
    //   selectLockedCells: true, // 允许选择锁定的单元格
    //   selectUnlockedCells: true, // 允许选择未锁定的单元格
    //   sort: false, // 禁止排序
    //   autoFilter: false, // 禁止自动筛选
    //   pivotTables: false // 禁止数据透视表
    // };

    // 为课程目标列添加注释信息 - 鼠标悬停时显示课程目标的具体名称
    if (assessmentContent.value?.courseObjectives) {
      assessmentContent.value.courseObjectives.forEach((objective: any, index: number) => {
        const col = 3 + index; // 课程目标列从第4列开始（A=0, B=1, C=2, D=3）
        const headerCell = XLSX.utils.encode_cell({ r: 0, c: col }); // 生成表头单元格地址

        // 为表头添加注释（使用正确的注释格式）
        ws[headerCell].c = [{ // c 属性用于设置单元格注释
          a: '系统', // 注释作者（author）
          t: objective.name || `课程目标${objective.id}`, // 注释内容（text）- 显示课程目标的具体名称
          r: 0 // 注释位置（row）- 0表示在当前行
        }];
        // 设置注释为隐藏状态，鼠标悬停时显示
        ws[headerCell].c.hidden = true;
      });
    }

    // 将工作表添加到工作簿并设置工作表名称
    XLSX.utils.book_append_sheet(wb, ws, '成绩录入模板');

    // 生成文件名 - 使用考核内容名称作为文件名前缀
    const fileName = `${assessmentContent.value?.assessmentName || '考核内容'}-直接录入-成绩明细.xlsx`;

    // 使用标准xlsx下载文件
    XLSX.writeFile(wb, fileName);

    MessagePlugin.success('模板下载成功');
  } catch (error) {
    console.error('模板下载失败:', error);
    MessagePlugin.error('模板下载失败');
  }
};

/** 直接录入导入配置 - 定义导入对话框的配置参数 */
const importConfig: ImportConfig = {
  title: '导入成绩', // 导入对话框标题
  tips: '请按照模板格式填写学生成绩信息，支持批量导入成绩', // 导入提示信息
  templateFileName: `${assessmentContent.value?.assessmentName || '考核内容'}-直接录入-成绩明细.xlsx`, // 模板文件名
  templateData: generateImportTemplateData(), // 模板数据（用于默认下载）
  acceptTypes: ['.xlsx', '.xls', '.csv'], // 支持的文件类型
  maxFileSize: 10, // 最大文件大小（MB）
  customDownloadTemplate: downloadProtectedTemplate // 自定义下载模板函数
}

/** 直接录入导入回调函数 */
const importCallbacks: ImportCallbacks = {
  onImport: async (file: File) => {
    try {
      console.log('开始导入直接录入成绩文件:', file.name)
      
      // 调用后端API导入成绩
      const response = await importDirectEntryScore(
        Number(props.assessmentId), 
        Number(props.taskId), 
        file
      )
      
      console.log('导入API响应:', response)
      
      if (response.code === 200) {
        const result = response.data
        return {
          success: true,
          successMessage: `成功导入 ${result.successCount || 0} 名学生成绩`,
          successCount: result.successCount || 0,
          failCount: result.failCount || 0,
          errorMessages: result.failList || []
        }
      } else {
        return {
          success: false,
          successCount: 0,
          failCount: 0,
          errorMessages: [response.message || '导入失败']
        }
      }
    } catch (error) {
      console.error('导入成绩失败:', error)
      return {
        success: false,
        successCount: 0,
        failCount: 0,
        errorMessages: ['网络错误或服务器异常']
      }
    }
  },
  onSuccess: () => {
    loadGradeData()
    MessagePlugin.success('成绩导入成功')
  },
  onError: (error: Error) => {
    console.error('导入失败:', error)
    MessagePlugin.error('成绩导入失败')
  },
  onComplete: () => {
    importDialogVisible.value = false
  }
}
// ==================== 方法 ====================

/** 加载课程及课程目标数据 */
const loadCourseObjectiveData = async () => {
  const { data} = await getCourseBaseInfo(Number(props.courseId))
  console.log('courseBaseInfo', data)
  courseBaseInfo.value = data
}

const loadAssessmentInfoData = async () => {
  const { data} = await getAssessmentById(props.assessmentId)
  assessmentContent.value = data
}

const loadAssessmentTaskScores = async () => {
  const { data} = await getAssessmentTaskScores(props.assessmentId, props.taskId)
  console.log('assessmentTaskScores', data)
  assessmentTaskScores.value = data
}

/** 加载成绩数据 */
const loadGradeData = async () => {
  if (!props.assessmentId || !props.taskId) {
    console.warn('缺少必要的参数：assessmentId 或 taskId')
    return
  }
  originalGradeData.value = []
  editingGradeData.value = []
  tableLoading.value = true
  try {
    // 加载课程目标数据
    await loadCourseObjectiveData()
    // 加载考核信息数据
    await loadAssessmentInfoData()
    // 加载考核任务成绩数据
    await loadAssessmentTaskScores()

    assessmentContent.value.courseObjectives = courseBaseInfo.value.courseObjectives.map((item: any) => ({
      id: item.number,
      objectiveId: item.objectiveId,
      name: item.objectiveName,
      description: item.description
    }))
    // Mock数据 - 成绩统计数据
    gradeStats.value = {
      averageScore: 78.5,
      maxScore: 95.0,
      minScore: 45.0,
      submittedCount: 28,
      pendingCount: 2
    }

    gradeList.value = assessmentTaskScores.value

    // 初始化编辑数据
    originalGradeData.value = JSON.parse(JSON.stringify(gradeList.value))
    editingGradeData.value = JSON.parse(JSON.stringify(gradeList.value))

    // 设置课程目标列标题的 title 属性
    setCourseObjectiveTitles()

    // 初始化学生成绩数据结构
    initializeStudentGradeStructure()





  } catch (error) {
    console.error('加载成绩数据失败:', error)
    MessagePlugin.error('加载成绩数据失败')
  } finally {
    tableLoading.value = false
  }
}

/** 处理单元格点击 */
const handleCellClick = (studentId: number, targetNo: number) => {
  if (props.scoreStatus !== 1) return;
  editingCell.value = { studentId, targetNo };
  isInputReallyFocused.value = true;
  addGlobalKeydownListener();
  nextTick(() => {
    // 解绑上一个 input 的事件
    if (lastInput) {
      lastInput = null;
    }
    // 直接 focus input
    if (inputNumberRef.value && inputNumberRef.value.$el) {
      const input = inputNumberRef.value.$el.querySelector('input');
      if (input) {
        input.focus();
        lastInput = input;
      }
    }
  });
};


const exitEditMode = () => {
  editingCell.value = null;
  isInputReallyFocused.value = false;
  removeGlobalKeydownListener();
};

/** 弹窗级监听 - 事件委托+属性判断（升级为 composedPath 方案） */
const handleDialogMouseDown = (event: MouseEvent) => {
  if (!editingCell.value) {
    exitEditMode();
    return;
  }
  const path = event.composedPath() as HTMLElement[];
  const match = path.find(
    el =>
      el instanceof HTMLElement &&
      el.classList?.contains('inline-edit-cell') &&
      String(el.dataset.studentId) === String(editingCell.value!.studentId) &&
      String(el.dataset.targetNo) === String(editingCell.value!.targetNo)
  );
  if (match) {
    // 点击在当前编辑单元格内
    return;
  }
  exitEditMode();
};

/** 处理单元格失焦 */
const handleCellBlur = () => {
  setTimeout(() => {
    // 如果还在编辑模式，且逻辑上应保持 focus
    if (editingCell.value && isInputReallyFocused.value && inputNumberRef.value && inputNumberRef.value.$el) {
      const input = inputNumberRef.value.$el.querySelector('input');
      const active = document.activeElement;
      // 只有当 active 不在 t-input-number 组件内时才 focus
      if (
        input &&
        (!active || !inputNumberRef.value.$el.contains(active))
      ) {
        input.focus();
      }
    }
    // 否则（已退出编辑模式），不做任何事
  }, 10);
};

/** 更新课程目标成绩 */
const updateCourseTargetScore = (row: StudentGradeData, targetNo: number, value: number) => {
  let targetScore = row.courseTargetScores.find((ts: CourseTargetScore) => ts.courseTargetNo === targetNo)

  // 如果课程目标成绩不存在，创建一个新的
  if (!targetScore) {
    const objective = assessmentContent.value?.courseObjectives?.find((obj: any) => obj.id === targetNo)
    targetScore = {
      courseTargetNo: targetNo,
      courseTargetName: objective?.name || `课程目标${targetNo}`,
      objectiveId: objective?.objectiveId,
      score: 0,
      fullScore: 100, // 默认满分100
      scoreRate: 0,
      poId: targetNo
    }
    row.courseTargetScores.push(targetScore)
  }

  // 更新成绩
  targetScore.score = value
  targetScore.scoreRate = value / targetScore.fullScore

  // 重新计算总分
  const totalScore = row.courseTargetScores.reduce((sum: number, ts: CourseTargetScore) => sum + (ts.score || 0), 0)
  row.totalScore = totalScore
  row.scoreRate = totalScore / row.fullScore
  hasChanges.value = true
}


/** 获取课程目标成绩 */
const getCourseTargetScore = (row: StudentGradeData, targetNo: number) => {
  return row.courseTargetScores.find((ts: CourseTargetScore) => ts.courseTargetNo === targetNo)?.score || 0
}

/** 保存修改 */
const handleSaveChanges = async () => {
  try {
    operationLoading.value = true

    // 收集变更的数据
    const changedData = collectChangedData()

    // 数据验证
    if (changedData.length === 0) {
      MessagePlugin.warning('没有需要保存的修改')
      return
    }

    // 转换为后端期望的数据格式
    const batchSaveData = convertToBatchSaveDTO(changedData)
    console.log('batchSaveData', batchSaveData)
    // 输出保存的数据到控制台
    console.log('=== 保存成绩数据 ===')
    console.log('考核ID:', props.assessmentId)
    console.log('任务ID:', props.taskId)
    console.log('课程ID:', props.courseId)
    console.log('班级信息:', props.classInfo)
    console.log('变更的学生数量:', changedData.length)
    console.log('变更详情:', changedData)
    console.log('转换后的批量保存数据:', batchSaveData)
    console.log('完整的编辑数据:', editingGradeData.value)
    console.log('==================')

    // 调用后端API保存成绩数据
    await batchSaveTargetScores(batchSaveData)
    // 保存成功
    originalGradeData.value = JSON.parse(JSON.stringify(editingGradeData.value))
    hasChanges.value = false
    MessagePlugin.success(`成绩修改已保存，共更新 ${batchSaveData.studentScores.length} 条成绩记录`)

  } catch (error) {
    console.error('保存失败:', error)
  } finally {
    operationLoading.value = false
  }
}

/** 取消修改 */
const handleCancelChanges = () => {
  try {
    // 恢复编辑数据为原始数据
    editingGradeData.value = JSON.parse(JSON.stringify(originalGradeData.value))
    hasChanges.value = false
    editingCell.value = null
    MessagePlugin.success('修改已取消')
  } catch (error) {
    MessagePlugin.error('取消失败')
  }
}

/** 提交成绩 */
const handleSubmitGrades = async () => {
  try {
    operationLoading.value = true
          // TODO: 调用后端API提交成绩
      // 使用现有的API接口进行成绩提交

    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))

    MessagePlugin.success('成绩提交成功')
    // 提交成功后可以关闭弹窗或刷新数据
    handleClose()
  } catch (error) {
    console.error('提交失败:', error)
    MessagePlugin.error('提交失败，请重试')
  } finally {
    operationLoading.value = false
  }
}


/** 检查成绩是否有变更 */
const hasScoreChanged = (studentId: number, targetNo: number) => {
  const original = originalGradeData.value.find(og => og.studentId === studentId)
  const editing = editingGradeData.value.find(eg => eg.studentId === studentId)

  if (!original || !editing) return false

  const originalScore = original.courseTargetScores.find(ts => ts.courseTargetNo === targetNo)?.score
  const editingScore = editing.courseTargetScores.find(ts => ts.courseTargetNo === targetNo)?.score

  return originalScore !== editingScore
}

/** 检查总分是否有变更 */
const hasTotalScoreChanged = (studentId: number) => {
  const original = originalGradeData.value.find(og => og.studentId === studentId)
  const editing = editingGradeData.value.find(eg => eg.studentId === studentId)

  if (!original || !editing) return false

  return original.totalScore !== editing.totalScore
}



/** 获取原始成绩 */
const getOriginalScore = (studentId: number, targetNo: number) => {
  const original = originalGradeData.value.find(og => og.studentId === studentId)
  return original?.courseTargetScores.find(ts => ts.courseTargetNo === targetNo)?.score || '-'
}



/** 获取原始总分 */
const getOriginalTotalScore = (studentId: number) => {
  const original = originalGradeData.value.find(og => og.studentId === studentId)
  return original?.totalScore || '-'
}

/** 计算变更的单元格数量 */
const changedCellsCount = computed(() => {
  let count = 0

  editingGradeData.value.forEach(editingRow => {
    const originalRow = originalGradeData.value.find(og => og.studentId === editingRow.studentId)
    if (!originalRow) return

    // 检查课程目标成绩变更
    editingRow.courseTargetScores.forEach(editingScore => {
      const originalScore = originalRow.courseTargetScores.find(ts => ts.courseTargetNo === editingScore.courseTargetNo)
      if (originalScore && editingScore.score !== originalScore.score) {
        count++
      }
    })
  })

  return count
})

/** 收集变更的数据 */
const collectChangedData = () => {
  const changedStudents: any[] = []

  editingGradeData.value.forEach(editingRow => {
    const originalRow = originalGradeData.value.find(og => og.studentId === editingRow.studentId)
    if (!originalRow) return

    const changedScores: any[] = []
    let hasChanges = false

    // 检查课程目标成绩变更
    editingRow.courseTargetScores.forEach(editingScore => {
      const originalScore = originalRow.courseTargetScores.find(ts => ts.courseTargetNo === editingScore.courseTargetNo)
      if (originalScore && editingScore.score !== originalScore.score) {
        changedScores.push({
          courseTargetNo: editingScore.courseTargetNo,
          courseTargetName: editingScore.courseTargetName,
          originalScore: originalScore.score,
          objectiveId: editingScore.objectiveId,
          newScore: editingScore.score,
          fullScore: editingScore.fullScore,
          scoreRate: editingScore.scoreRate
        })
        hasChanges = true
      }
    })

    // 检查总分变更
    if (editingRow.totalScore !== originalRow.totalScore) {
      hasChanges = true
    }

    if (hasChanges) {
      changedStudents.push({
        studentId: editingRow.studentId,
        studentNumber: editingRow.studentNumber,
        studentName: editingRow.studentName,
        className: editingRow.className,
        originalTotalScore: originalRow.totalScore,
        newTotalScore: editingRow.totalScore,
        changedScores: changedScores,
        entryStatus: editingRow.entryStatus,
        entryTime: new Date().toISOString(),
        lastModifyTime: new Date().toISOString()
      })
    }
  })

  return changedStudents
}

/** 将变更数据转换为BatchSaveTargetScoresDTO格式 */
const convertToBatchSaveDTO = (changedData: any[]): BatchSaveTargetScoresDTO => {
  const studentScores: StudentTargetScoreDTO[] = []

  changedData.forEach(studentData => {
    studentData.changedScores.forEach((scoreData: any) => {
      const studentScore: StudentTargetScoreDTO = {
        studentId: studentData.studentId,
        courseTargetNo: scoreData.courseTargetNo,
        objectiveId: scoreData.objectiveId,
        //repositoryAnswerId: 0, // 暂时设为0，后续可根据需要调整
        score: Number(scoreData.newScore),
        //remark: `修改时间: ${new Date().toISOString()}`
      }
      studentScores.push(studentScore)
    })
  })

  return {
    assessmentId: props.assessmentId,
    taskId: props.taskId,
    studentScores: studentScores
  }
}

/** 获取状态说明 */
const statusDescription = computed(() => {
  switch (props.scoreStatus) {
    case 0:
      return '（未开始状态：成绩录入尚未开始）'
    case 1:
      return '（进行中状态：可以编辑成绩）'
    case 2:
      return '（已提交状态：成绩已提交，不可编辑）'
    default:
      return ''
  }
})

/** 关闭弹窗 */
const handleClose = () => {
  directDialogVisible.value = false
  emit('update:visible', false)
}

/** 刷新数据 */
const handleRefresh = async () => {
  try {
    operationLoading.value = true
    await loadGradeData()
    MessagePlugin.success('数据已刷新')
  } catch (error) {
    console.error('刷新失败:', error)
    MessagePlugin.error('刷新失败，请重试')
  } finally {
    operationLoading.value = false
  }
}

/** 导出直接录入成绩 */
const handleExportGrades = () => {
  MessagePlugin.info('导出直接录入成绩')
}

/** 直接录入分页处理 */
const handleDirectPageChange = (pageInfo: any) => {
  directPagination.current = pageInfo.current
  directPagination.pageSize = pageInfo.pageSize
}

/** 设置课程目标列标题的 title 属性 */
const setCourseObjectiveTitles = () => {
  nextTick(() => {
    if (assessmentContent.value?.courseObjectives) {
      assessmentContent.value.courseObjectives.forEach((objective: any) => {
        const headerCell = document.querySelector(`[data-col-key="courseTargetScores.${objective.id}"]`)
        if (headerCell) {
          headerCell.setAttribute('title', objective.description || '暂无描述信息')
        }
      })
    }
  })
}

/** 初始化学生成绩数据结构 */
const initializeStudentGradeStructure = () => {
  if (!assessmentContent.value?.courseObjectives || !editingGradeData.value) return

  editingGradeData.value.forEach((student: StudentGradeData) => {
    // 确保 courseTargetScores 数组存在
    if (!student.courseTargetScores) {
      student.courseTargetScores = []
    }

    // 为每个课程目标创建成绩项（如果不存在）
    assessmentContent.value.courseObjectives.forEach((objective: any) => {
      const existingScore = student.courseTargetScores.find(
        (ts: CourseTargetScore) => ts.courseTargetNo === objective.id
      )

      if (!existingScore) {
        student.courseTargetScores.push({
          courseTargetNo: objective.id,
          courseTargetName: objective.name,
          objectiveId: objective.objectiveId,
          // score: 0,
          // fullScore: 100, // 默认满分100
          // scoreRate: 0
        })
      }
    })
  })

  // 同步更新原始数据
  originalGradeData.value = JSON.parse(JSON.stringify(editingGradeData.value))
}





/** 导入直接录入成绩 */
const handleImportGrades = () => {
  importDialogVisible.value = true
}



// 监听props变化，控制弹窗显示
watch([
  () => props.visible,
  () => props.assessmentId,
  () => props.taskId
], ([newVisible, newAssessmentId, newTaskId], [oldVisible, oldAssessmentId, oldTaskId]) => {
  if (newVisible && newAssessmentId && newTaskId) {
    directDialogVisible.value = true
    // 当弹窗显示且有必要的参数时，自动加载数据
    loadGradeData()
  } else {
    directDialogVisible.value = false
  }
}, { immediate: true })



// 暴露给父组件的方法
defineExpose({
  loadGradeData
})

// 新增方法
const getCourseTargetScoreDisplay = (row: StudentGradeData, targetNo: number) => {
  const score = row.courseTargetScores.find((ts: CourseTargetScore) => ts.courseTargetNo === targetNo)?.score
  if (score === 0) return 0
  if (score === null || score === undefined) return '-'
  return score
}

// 新增方法
const getTotalScoreDisplay = (score: number | null | undefined) => {
  if (score === 0) return 0
  if (score === null || score === undefined) return '-'
  return score
}

// 新增方法
const getTotalScoreClass = (score: number | null | undefined) => {
  if (score === null || score === undefined) return ''
  if (score >= 90) return 'score-brand' // 主色
  if (score >= 80) return 'score-info'  // 信息色
  if (score >= 60) return 'score-success' // 成功色
  return 'score-error' // 错误色
}
</script>

<style lang="less" scoped>
// :deep(.grade-dialog-minwidth .t-dialog) {
//   min-width: 980px !important;
// }
.score-brand {
  background: var(--td-brand-color-light) !important;
  color: var(--td-brand-color) !important;
}
.score-info {
  background: var(--td-info-color-light) !important;
  color: var(--td-info-color) !important;
}
.score-success {
  background: var(--td-success-color-light) !important;
  color: var(--td-success-color) !important;
}
.score-error {
  background: var(--td-error-color-light) !important;
  color: var(--td-error-color) !important;
}
// 隐藏 t-input-number 内部 input 的 spin button
:deep(.t-input-number input[type='number']) {
  -moz-appearance: textfield;
}
:deep(.t-input-number input[type='number']::-webkit-outer-spin-button),
:deep(.t-input-number input[type='number']::-webkit-inner-spin-button) {
  -webkit-appearance: none;
  margin: 0;
}
// 优化总分变更高亮优先级
:deep(.total-score.score-changed) {
  background: var(--td-warning-color-light) !important;
  color: var(--td-warning-color) !important;
  z-index: 1;
  position: relative;
}
.status-hint {
  margin-left: 16px;
  color: var(--td-text-color-secondary);
  font-size: 14px;
  @media (max-width: 960px) {
    display: none;
  }
}
.search-toolbar {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: space-between;
  gap: 8px;
  .search-section {
    flex: 1 1 auto;
    min-width: 180px;
    display: flex;
    align-items: center;
  }
  .toolbar-actions {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: flex-end;
    gap: 8px;
    min-width: 240px;
  }
}

// 课程目标标题样式
:deep(.t-table__header) {
  .t-table__cell {
    &[data-col-key*="courseTargetScores"] {
      position: relative;
      cursor: help;

      &::after {
        content: "?";
        position: absolute;
        top: 2px;
        right: 2px;
        width: 16px;
        height: 16px;
        background: var(--td-text-color-placeholder);
        color: white;
        border-radius: 50%;
        font-size: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        opacity: 0.7;
        font-weight: bold;
      }

      &:hover::after {
        opacity: 1;
      }
    }
  }
}
</style>

<template>
    <div>
      <!-- 详细录入模式 - 全屏弹窗 -->
      <FullScreenDialog
        v-model:visible="detailedDialogVisible"
        :header="detailedDialogTitle"
        @close="handleClose"
      >
        <div class="detailed-grade-management" @mousedown="handleDetailedDialogMouseDown">
          <!-- 考核内容信息 -->
          <div class="assessment-info">
            <div class="info-content">
              <div class="info-meta">
                <div class="meta-item">
                  <span class="label">班级：</span>
                  <span class="value">{{ classInfo?.className }}</span>
                </div>
                <div class="meta-item">
                  <span class="label">人数：</span>
                  <span class="value">{{ classInfo?.studentCount }}人</span>
                </div>
                <div class="meta-item">
                  <span class="label">录入方式：</span>
                  <t-tag size="small" theme="success">详细录入</t-tag>
                </div>
              </div>
            </div>
            <div class="info-actions">
              <t-button theme="primary" @click="handleImportDetailedGrades">
                <template #icon>
                  <t-icon name="upload" />
                </template>
                导入成绩
              </t-button>
              <t-button theme="default" variant="outline" @click="handleExportDetailedGrades">
                <template #icon>
                  <t-icon name="file-export" />
                </template>
                导出成绩
              </t-button>
              <t-button theme="default" variant="outline" @click="handleRefresh">
                <template #icon>
                  <t-icon name="refresh" />
                </template>
                刷新
              </t-button>
            </div>
          </div>

          <!-- 成绩统计卡片 -->
          <div class="stats-cards">
            <div class="stat-card">
              <div class="stat-icon average">
                <t-icon name="chart-line" />
              </div>
              <div class="stat-content">
                <div class="stat-value">{{ detailedGradeStats.averageScore }}</div>
                <div class="stat-label">平均分</div>
              </div>
            </div>

            <div class="stat-card">
              <div class="stat-icon highest">
                <t-icon name="arrow-up" />
              </div>
              <div class="stat-content">
                <div class="stat-value">{{ detailedGradeStats.maxScore }}</div>
                <div class="stat-label">最高分</div>
              </div>
            </div>

            <div class="stat-card">
              <div class="stat-icon lowest">
                <t-icon name="arrow-down" />
              </div>
              <div class="stat-content">
                <div class="stat-value">{{ detailedGradeStats.minScore }}</div>
                <div class="stat-label">最低分</div>
              </div>
            </div>

            <div class="stat-card">
              <div class="stat-icon submitted">
                <t-icon name="check-circle" />
              </div>
              <div class="stat-content">
                <div class="stat-value">{{ detailedGradeStats.submittedCount }}</div>
                <div class="stat-label">已录入</div>
              </div>
            </div>

            <div class="stat-card">
              <div class="stat-icon pending">
                <t-icon name="time" />
              </div>
              <div class="stat-content">
                <div class="stat-value">{{ detailedGradeStats.pendingCount }}</div>
                <div class="stat-label">待录入</div>
              </div>
            </div>
          </div>

          <!-- 搜索和筛选工具栏 -->
          <div class="search-toolbar">
            <div class="search-section">
              <t-input v-model="searchKeyword" placeholder="搜索学号或姓名" style="width: 200px" clearable>
                <template #prefix-icon>
                  <t-icon name="search" />
                </template>
              </t-input>
              <span class="status-hint">{{ statusDescription }}</span>
            </div>
            <div class="toolbar-actions">

              <t-button
                theme="warning"
                :disabled="!hasDetailedChanges"
                @click="handleSaveDetailedChanges"
              >
                <template #icon>
                  <t-icon name="save" />
                </template>
                保存修改{{ changedDetailedCellsCount > 0 ? ` (${changedDetailedCellsCount})` : '' }}
              </t-button>
              <t-button
                theme="default"
                variant="outline"
                :disabled="!hasDetailedChanges"
                @click="handleCancelDetailedChanges"
              >
                <template #icon>
                  <t-icon name="close" />
                </template>
                取消保存
              </t-button>
              <t-button
                theme="success"
                :disabled="hasDetailedChanges"
                @click="handleSubmitDetailedGrades"
              >
                <template #icon>
                  <t-icon name="check" />
                </template>
                提交成绩
              </t-button>
              <t-popup
                v-model:visible="columnSettingsPopupVisible"
                trigger="click"
                placement="bottom"
                :overlay-style="{ minWidth: '180px', padding: '8px 0' }"
              >
                <t-button variant="outline">
                  <template #icon>
                    <t-icon name="view-list" />
                  </template>
                  列设置
                </t-button>
                <template #content>
                  <div style="max-height: 260px; overflow-y: auto;">
                    <div v-for="type in assessmentContent?.questionStructure?.map((item: any) => item.questionType) || []" :key="type" style="display: flex; align-items: center; padding: 4px 16px;">
                      <t-checkbox
                        :checked="visibleQuestionTypes.includes(type)"
                        @change="(checked) => handleQuestionTypeVisibilityChange(type, checked)"
                      >
                        {{ type }}
                      </t-checkbox>
                    </div>
                  </div>
                </template>
              </t-popup>
            </div>
          </div>

          <!-- 详细成绩表格 -->
          <div class="detailed-grade-table">
            <t-table
              :data="filteredDetailedGradeList"
              :columns="detailedTableColumns"
              row-key="studentId"
              stripe
              hover
              :loading="loading"
              bordered
              :pagination="detailedPagination"
              @page-change="handleDetailedPageChange"
            >
              <template
                v-for="slot in detailedScoreSlots"
                :key="slot.repositoryAnswerId"
                #[`score.${slot.repositoryAnswerId}`]="{ row }"
              >
                <div
                  class="detailed-inline-edit-cell"
                  :data-student-id="row.studentId"
                  :data-repository-answer-id="slot.repositoryAnswerId"
                  :class="{ 'score-changed': hasDetailedScoreChanged(row.studentId, slot.repositoryAnswerId) }"
                >
                  <t-input-number
                    v-if="editingDetailedCell?.studentId === row.studentId && editingDetailedCell?.repositoryAnswerId === slot.repositoryAnswerId && scoreStatus === 1"
                    ref="detailedInputNumberRef"
                    :model-value="row.detailScores?.find((ds: any) => ds.repositoryAnswerId === slot.repositoryAnswerId)?.score || 0"
                    :min="0"
                    :max="slot.sub.maxScore || 100"
                    :decimal-places="1"
                    placeholder="请输入分数"
                    style="width: 60px; padding: 0 2px; vertical-align: middle;"
                    :theme="'normal'"
                    :auto-width="false"
                    :controls="false"
                    align="center"
                    @blur="handleDetailedCellBlur"
                    @enter="handleDetailedCellBlur"
                    @update:model-value="(value: number) => updateDetailedScore(row, slot.repositoryAnswerId, value)"
                  />
                  <t-tooltip theme="light" v-else-if="hasDetailedScoreChanged(row.studentId, slot.repositoryAnswerId)" :content="`原始成绩: ${getOriginalDetailedScore(row.studentId, slot.repositoryAnswerId)}`">
                    <span
                      class="score-display"
                      :class="{ 'score-changed': hasDetailedScoreChanged(row.studentId, slot.repositoryAnswerId) }"
                      :style="{
                        cursor: scoreStatus === 1 ? 'pointer' : 'default',
                        backgroundColor: hasDetailedScoreChanged(row.studentId, slot.repositoryAnswerId) ? 'var(--td-warning-color-light)' : 'transparent',
                        color: hasDetailedScoreChanged(row.studentId, slot.repositoryAnswerId) ? 'var(--td-warning-color)' : 'inherit'
                      }"
                      @click="scoreStatus === 1 && handleDetailedCellClick(row.studentId, slot.repositoryAnswerId)"
                    >
                      {{
                        (row.detailScores?.find((ds: any) => ds.repositoryAnswerId === slot.repositoryAnswerId)?.score ?? '') === '' ||
                        row.detailScores?.find((ds: any) => ds.repositoryAnswerId === slot.repositoryAnswerId)?.score === null ||
                        row.detailScores?.find((ds: any) => ds.repositoryAnswerId === slot.repositoryAnswerId)?.score === undefined
                          ? '-'
                          : row.detailScores?.find((ds: any) => ds.repositoryAnswerId === slot.repositoryAnswerId)?.score
                      }}
                    </span>
                  </t-tooltip>
                  <span
                    v-else
                    class="score-display"
                    :class="{ 'score-changed': hasDetailedScoreChanged(row.studentId, slot.repositoryAnswerId) }"
                    :style="{
                      cursor: scoreStatus === 1 ? 'pointer' : 'default',
                      backgroundColor: hasDetailedScoreChanged(row.studentId, slot.repositoryAnswerId) ? 'var(--td-warning-color-light)' : 'transparent',
                      color: hasDetailedScoreChanged(row.studentId, slot.repositoryAnswerId) ? 'var(--td-warning-color)' : 'inherit'
                    }"
                    @click="scoreStatus === 1 && handleDetailedCellClick(row.studentId, slot.repositoryAnswerId)"
                  >
                    {{
                      (row.detailScores?.find((ds: any) => ds.repositoryAnswerId === slot.repositoryAnswerId)?.score ?? '') === '' ||
                      row.detailScores?.find((ds: any) => ds.repositoryAnswerId === slot.repositoryAnswerId)?.score === null ||
                      row.detailScores?.find((ds: any) => ds.repositoryAnswerId === slot.repositoryAnswerId)?.score === undefined
                        ? '-'
                        : row.detailScores?.find((ds: any) => ds.repositoryAnswerId === slot.repositoryAnswerId)?.score
                    }}
                  </span>
                </div>
              </template>
              <template #totalScore="{ row }">
                <t-tooltip theme="light" v-if="hasDetailedTotalScoreChanged(row.studentId)" :content="`原始总分: ${getOriginalDetailedTotalScore(row.studentId)}`">
                  <span
                    class="total-score"
                    :class="[getTotalScoreClass(row.totalScore), { 'score-changed': hasDetailedTotalScoreChanged(row.studentId) } ]"
                    :style="{
                      backgroundColor: hasDetailedTotalScoreChanged(row.studentId) ? 'var(--td-warning-color-light)' : 'transparent',
                      color: hasDetailedTotalScoreChanged(row.studentId) ? 'var(--td-warning-color)' : 'inherit'
                    }"
                  >
                    {{ getTotalScoreDisplay(row.totalScore) }}
                  </span>
                </t-tooltip>
                <span
                  v-else
                  class="total-score"
                  :class="[getTotalScoreClass(row.totalScore), { 'score-changed': hasDetailedTotalScoreChanged(row.studentId) } ]"
                  :style="{
                    backgroundColor: hasDetailedTotalScoreChanged(row.studentId) ? 'var(--td-warning-color-light)' : 'transparent',
                    color: hasDetailedTotalScoreChanged(row.studentId) ? 'var(--td-warning-color)' : 'inherit'
                  }"
                >
                  {{ getTotalScoreDisplay(row.totalScore) }}
                </span>
              </template>
            </t-table>
          </div>
        </div>
      </FullScreenDialog>

      <!-- 详细录入导入对话框 -->
      <ImportDialog
        v-model:visible="detailedImportDialogVisible"
        :config="detailedImportConfig"
        :callbacks="detailedImportCallbacks"
        :dialog-props="{ zIndex: 3300 }"
      />
    </div>
  </template>

  <script setup lang="ts">
  import { ref, computed, watch, reactive, nextTick } from 'vue'
  import { MessagePlugin } from 'tdesign-vue-next'
  import FullScreenDialog from '@/components/FullScreenDialog/index.vue'
  import ImportDialog from '@/components/ImportDialog/index.vue'
  import type { ImportConfig, ImportCallbacks } from '@/components/ImportDialog/types'
  import * as XLSX from 'xlsx'
  import {
    Button as TButton,
    Icon as TIcon,
    Tag as TTag,
    Input as TInput,
    InputNumber as TInputNumber,
    Table as TTable,
    Checkbox as TCheckbox,
    Popup as TPopup
  } from 'tdesign-vue-next'

  // 引入样式文件
  import './styles/grade-management.less'

  // 引入 API
  import { getAssessmentTaskScores } from '@/api/assessment/assessmentScore'

  // 定义Props和Emits
  interface Props {
    visible: boolean
    assessmentId: number
    taskId: number
    courseId: number | string
    classInfo?: any
    scoreStatus?: number // 0: 未开始, 1: 进行中, 2: 已提交
  }

  const props = defineProps<Props>()

  // 记录当前编辑单元格的信息
  const editingCell = ref<{ studentId: number; targetNo: number } | null>(null)

  // 详细录入模式的编辑状态
  const editingDetailedCell = ref<{ studentId: number; repositoryAnswerId: number } | null>(null)

  const inputNumberRef = ref<any>(null)
  const detailedInputNumberRef = ref<any>(null)

  let lastInput: HTMLInputElement | null = null;
  let lastDetailedInput: HTMLInputElement | null = null;

  const isInputReallyFocused = ref(false);
  const isDetailedInputReallyFocused = ref(false);

  let globalKeydownListener: ((e: KeyboardEvent) => void) | null = null;
  let detailedGlobalKeydownListener: ((e: KeyboardEvent) => void) | null = null;

  // 详细录入模式的全局键盘监听
  function addDetailedGlobalKeydownListener() {
    if (detailedGlobalKeydownListener) return;
    detailedGlobalKeydownListener = (e: KeyboardEvent) => {
      if (!editingDetailedCell.value) return;
      // Shift+方向键
      if (["ArrowUp", "ArrowDown", "ArrowLeft", "ArrowRight"].includes(e.key) && e.shiftKey) {
        e.preventDefault();
        const { studentId, repositoryAnswerId } = editingDetailedCell.value;
        const next = findDetailedEdgeCellByDirection(studentId, repositoryAnswerId, e.key);
        if (next) {
          handleDetailedCellClick(next.studentId, next.repositoryAnswerId);
        }
        return;
      }
      // Shift+Tab
      if (e.key === 'Tab' && e.shiftKey) {
        e.preventDefault();
        const { studentId, repositoryAnswerId } = editingDetailedCell.value;
        const next = findDetailedPrevCell(studentId, repositoryAnswerId);
        if (next) {
          handleDetailedCellClick(next.studentId, next.repositoryAnswerId);
        } else {
          isDetailedInputReallyFocused.value = false;
          exitDetailedEditMode();
        }
        return;
      }
      // 普通Tab
      if (e.key === 'Tab') {
        e.preventDefault();
        const { studentId, repositoryAnswerId } = editingDetailedCell.value;
        const next = findDetailedNextCell(studentId, repositoryAnswerId);
        if (next) {
          handleDetailedCellClick(next.studentId, next.repositoryAnswerId);
        } else {
          isDetailedInputReallyFocused.value = false;
          exitDetailedEditMode();
        }
        return;
      }
      // 普通方向键
      if (["ArrowUp", "ArrowDown", "ArrowLeft", "ArrowRight"].includes(e.key)) {
        e.preventDefault();
        const { studentId, repositoryAnswerId } = editingDetailedCell.value;
        const next = findDetailedNextCellByDirection(studentId, repositoryAnswerId, e.key);
        if (next) {
          handleDetailedCellClick(next.studentId, next.repositoryAnswerId);
        }
        return;
      }
      // Enter
      if (e.key === 'Enter') {
        e.preventDefault();
        isDetailedInputReallyFocused.value = false;
        exitDetailedEditMode();
        return;
      }
    };
    window.addEventListener('keydown', detailedGlobalKeydownListener, true);
  }

  function removeGlobalKeydownListener() {
    if (globalKeydownListener) {
      window.removeEventListener('keydown', globalKeydownListener, true);
      globalKeydownListener = null;
    }
  }

  function removeDetailedGlobalKeydownListener() {
    if (detailedGlobalKeydownListener) {
      window.removeEventListener('keydown', detailedGlobalKeydownListener, true);
      detailedGlobalKeydownListener = null;
    }
  }
  // 详细录入模式的导航函数
  function findDetailedNextCell(studentId: number, repositoryAnswerId: number) {
    const rows = filteredDetailedGradeList.value;
    const allRepositoryAnswerIds = getAllRepositoryAnswerIds();
    const rowIdx = rows.findIndex(row => row.studentId === studentId);
    const colIdx = allRepositoryAnswerIds.findIndex(id => id === repositoryAnswerId);

    // 先尝试同一行下一个得分点
    if (colIdx < allRepositoryAnswerIds.length - 1) {
      return { studentId, repositoryAnswerId: allRepositoryAnswerIds[colIdx + 1] };
    }
    // 否则跳到下一行的第一个得分点
    if (rowIdx < rows.length - 1) {
      return { studentId: rows[rowIdx + 1].studentId, repositoryAnswerId: allRepositoryAnswerIds[0] };
    }
    // 已经是最后一个单元格，循环到第一个
    if (rows.length > 0 && allRepositoryAnswerIds.length > 0) {
      return { studentId: rows[0].studentId, repositoryAnswerId: allRepositoryAnswerIds[0] };
    }
    return null;
  }

  function findDetailedNextCellByDirection(studentId: number, repositoryAnswerId: number, direction: string) {
    const rows = filteredDetailedGradeList.value;
    const allRepositoryAnswerIds = getAllRepositoryAnswerIds();
    const rowIdx = rows.findIndex(row => row.studentId === studentId);
    const colIdx = allRepositoryAnswerIds.findIndex(id => id === repositoryAnswerId);
    let nextRowIdx = rowIdx, nextColIdx = colIdx;

    if (direction === 'ArrowRight') {
      nextColIdx = (colIdx + 1) % allRepositoryAnswerIds.length;
    } else if (direction === 'ArrowLeft') {
      nextColIdx = (colIdx - 1 + allRepositoryAnswerIds.length) % allRepositoryAnswerIds.length;
    } else if (direction === 'ArrowDown') {
      nextRowIdx = (rowIdx + 1) % rows.length;
    } else if (direction === 'ArrowUp') {
      nextRowIdx = (rowIdx - 1 + rows.length) % rows.length;
    }

    // 只变动一维，另一维保持
    if (direction === 'ArrowRight' || direction === 'ArrowLeft') {
      return { studentId, repositoryAnswerId: allRepositoryAnswerIds[nextColIdx] };
    } else if (direction === 'ArrowUp' || direction === 'ArrowDown') {
      return { studentId: rows[nextRowIdx].studentId, repositoryAnswerId };
    }
    return null;
  }

  function findDetailedEdgeCellByDirection(studentId: number, repositoryAnswerId: number, direction: string) {
    const rows = filteredDetailedGradeList.value;
    const allRepositoryAnswerIds = getAllRepositoryAnswerIds();

    if (direction === 'ArrowRight') {
      return { studentId, repositoryAnswerId: allRepositoryAnswerIds[allRepositoryAnswerIds.length - 1] };
    } else if (direction === 'ArrowLeft') {
      return { studentId, repositoryAnswerId: allRepositoryAnswerIds[0] };
    } else if (direction === 'ArrowDown') {
      return { studentId: rows[rows.length - 1].studentId, repositoryAnswerId };
    } else if (direction === 'ArrowUp') {
      return { studentId: rows[0].studentId, repositoryAnswerId };
    }
    return null;
  }

  function findDetailedPrevCell(studentId: number, repositoryAnswerId: number) {
    const rows = filteredDetailedGradeList.value;
    const allRepositoryAnswerIds = getAllRepositoryAnswerIds();
    const rowIdx = rows.findIndex(row => row.studentId === studentId);
    const colIdx = allRepositoryAnswerIds.findIndex(id => id === repositoryAnswerId);

    // 先尝试同一行前一个得分点
    if (colIdx > 0) {
      return { studentId, repositoryAnswerId: allRepositoryAnswerIds[colIdx - 1] };
    }
    // 否则跳到上一行最后一个得分点
    if (rowIdx > 0) {
      return { studentId: rows[rowIdx - 1].studentId, repositoryAnswerId: allRepositoryAnswerIds[allRepositoryAnswerIds.length - 1] };
    }
    // 已经是第一个单元格，循环到最后一个
    if (rows.length > 0 && allRepositoryAnswerIds.length > 0) {
      return { studentId: rows[rows.length - 1].studentId, repositoryAnswerId: allRepositoryAnswerIds[allRepositoryAnswerIds.length - 1] };
    }
    return null;
  }

  // 获取所有可见的 repositoryAnswerId
  function getAllRepositoryAnswerIds(): number[] {
    const ids: number[] = [];
    assessmentContent.value?.questionStructure?.forEach((typeGroup: any) => {
      if (!visibleQuestionTypes.value.includes(typeGroup.questionType)) return;
      (typeGroup.questions || []).forEach((q: any) => {
        (q.subItems || []).forEach((sub: any) => {
          ids.push(sub.repositoryAnswerId);
        });
      });
    });
    return ids;
  }

  const emit = defineEmits<{
    'update:visible': [visible: boolean]
  }>()

  // ==================== 响应式数据 ====================

  /** 加载状态 */
  const loading = ref(false)

  /** 考核内容数据 */
  const assessmentContent = ref<any>(null)

  /** 成绩统计数据 */
  const gradeStats = ref<any>({
    averageScore: 0,
    maxScore: 0,
    minScore: 0,
    submittedCount: 0,
    pendingCount: 0
  })

  /** 成绩明细数据 */
  const gradeList = ref<any[]>([])

  /** 搜索关键词 */
  const searchKeyword = ref('')

  /** 成绩筛选条件 */
  const gradeFilter = ref('all')

  // 弹窗显示状态
  const detailedDialogVisible = ref(false)
  const detailedImportDialogVisible = ref(false)
  const columnSettingsPopupVisible = ref(false)

  /** 可见的题目类型列表 */
  const visibleQuestionTypes = ref<string[]>([])

  /** 原始成绩数据 */
  const originalGradeData = ref<StudentGradeData[]>([])

  /** 编辑中的成绩数据 */
  const editingGradeData = ref<StudentGradeData[]>([])

  /** 原始详细成绩数据 */
  const originalDetailedGradeData = ref<any[]>([])

  /** 编辑中的详细成绩数据 */
  const editingDetailedGradeData = ref<any[]>([])

  /** 数据变更状态 */
  const hasChanges = ref(false)

  /** 详细录入数据变更状态 */
  const hasDetailedChanges = ref(false)

  /** 详细录入分页配置 */
  const detailedPagination = reactive({
    current: 1,
    pageSize: 10,
    total: 0,
    showJumper: true,
    showSizeChanger: true,
    pageSizeOptions: [10, 20, 50, 100]
  })

  // ==================== 数据接口定义 ====================

  /** 课程目标成绩接口 */
  interface CourseTargetScore {
    courseTargetNo: number
    courseTargetName: string
    score: number
    fullScore: number
    scoreRate: number
    poId: number
  }

  /** 学生成绩数据接口 */
  interface StudentGradeData {
    studentId: number
    studentNumber: string
    studentName: string
    classId: number
    className: string
    totalScore: number
    fullScore: number
    scoreRate: number
    scoreGrade: string
    courseTargetScores: CourseTargetScore[]
    detailScores: any[]
    entryStatus: string
    entryTime: string
    lastModifyTime: string
    entryUserId: number
    entryUserName: string
  }

  

  // ==================== 计算属性 ====================

  /** 详细录入弹窗标题 */
  const detailedDialogTitle = computed(() => {
    return `成绩管理 - ${assessmentContent.value?.title || assessmentContent.value?.assessmentName || ''}`
  })

  /** 详细录入成绩统计 */
  const detailedGradeStats = computed(() => {
    const submitted = editingDetailedGradeData.value.filter((item: any) => item.entryStatus === 'ENTERED' && item.totalScore !== null)
    const scores = submitted.map((item: any) => item.totalScore).filter((score: number | null) => score !== null) as number[]

    return {
      averageScore: scores.length > 0 ? (scores.reduce((a, b) => a + b, 0) / scores.length).toFixed(1) : '-',
      maxScore: scores.length > 0 ? Math.max(...scores).toFixed(1) : '-',
      minScore: scores.length > 0 ? Math.min(...scores).toFixed(1) : '-',
      submittedCount: submitted.length,
      pendingCount: editingDetailedGradeData.value.length - submitted.length
    }
  })

  /** 过滤后的详细成绩列表 */
  const filteredDetailedGradeList = computed(() => {
    let filtered = [...editingDetailedGradeData.value]

    // 搜索过滤
    if (searchKeyword.value) {
      const keyword = searchKeyword.value.toLowerCase()
      filtered = filtered.filter((item: any) =>
        item.studentNumber.toLowerCase().includes(keyword) ||
        item.studentName.toLowerCase().includes(keyword)
      )
    }

    // 状态过滤
    if (gradeFilter.value !== 'all') {
      filtered = filtered.filter(item => item.status === gradeFilter.value)
    }

    // 更新分页总数
    detailedPagination.total = filtered.length

    // 分页处理
    const start = (detailedPagination.current - 1) * detailedPagination.pageSize
    const end = start + detailedPagination.pageSize

    return filtered.slice(start, end)
  })

  /** 详细录入表格列定义 */
  const detailedTableColumns = computed(() => {
    const columns: any[] = [
      { colKey: 'studentNumber', title: '学号', width: 120, align: 'center', fixed: 'left' },
      { colKey: 'studentName', title: '姓名', width: 100, align: 'center', fixed: 'left' }
    ];
    
    if (assessmentContent.value?.questionStructure) {
      assessmentContent.value.questionStructure.forEach((typeGroup: any) => {
        if (!visibleQuestionTypes.value.includes(typeGroup.questionType)) return;
        const typeTitle = typeGroup.questionTypeName || typeGroup.questionType;
        const typeChildren: any[] = [];
        (typeGroup.questions || []).forEach((q: any) => {
          const qChildren = (q.subItems || []).map((sub: any) => ({
            colKey: `score.${sub.repositoryAnswerId}`,
            title: `${q.questionNumber}.${sub.subNumber}`,
            width: 100,
            align: 'center'
          }));
          typeChildren.push({
            colKey: `q_${q.questionNumber}`,
            title: q.questionNumber,
            align: 'center',
            children: qChildren
          });
        });
        columns.push({
          colKey: `type_${typeGroup.questionType}`,
          title: typeTitle,
          align: 'center',
          children: typeChildren
        });
      });
    }
    
    columns.push({ colKey: 'totalScore', title: '总分', width: 100, align: 'center', fixed: 'right' });
    return columns;
  });

  // ==================== 导入配置 ====================
  /** 生成详细导入模板 */
  function generateDetailedImportTemplate() {
    // 表头行
    const headers = ['学号', '姓名']

    // 添加题目列 - 按题型分组
    const questionTypeGroups = new Map<string, any[]>()

    assessmentContent.value?.questionStructure?.forEach((question: any) => {
      if (!questionTypeGroups.has(question.questionType)) {
        questionTypeGroups.set(question.questionType, [])
      }
      questionTypeGroups.get(question.questionType)!.push(question)
    })

          // 按题型顺序添加列
      questionTypeGroups.forEach((questions: any[], questionType: string) => {
      // 只为可见的题型添加列
      if (visibleQuestionTypes.value.includes(questionType)) {
        questions.forEach((question: any) => {
          question.subItems.forEach((subItem: any) => {
            headers.push(`${question.questionNumber}.${subItem.subNumber.split('.')[1]}(${subItem.maxScore}分)`)
          })
        })
      }
    })

    headers.push('备注')

    // 示例数据行
    const exampleRow1 = ['2021001', '张三']
    const exampleRow2 = ['2021002', '李四']

    // 添加示例分数 - 按题型分组
    questionTypeGroups.forEach((questions: any[], questionType: string) => {
      // 只为可见的题型添加示例分数
      if (visibleQuestionTypes.value.includes(questionType)) {
        questions.forEach((question: any) => {
          question.subItems.forEach((subItem: any) => {
            // 随机生成示例分数，但不超过最大分数
            const maxScore = subItem.maxScore
            exampleRow1.push(String(Math.floor(Math.random() * maxScore * 0.8 + maxScore * 0.2)))
            exampleRow2.push(String(Math.floor(Math.random() * maxScore * 0.8 + maxScore * 0.2)))
          })
        })
      }
    })

    exampleRow1.push('')
    exampleRow2.push('')

    return [headers, exampleRow1, exampleRow2]
  }

  /** 详细录入导入配置 */
  const detailedImportConfig: ImportConfig = {
    title: '导入详细成绩',
    tips: '请按照模板格式填写学生详细成绩信息，支持批量导入成绩',
    templateFileName: '详细成绩导入模板.xlsx',
    templateData: generateDetailedImportTemplate(),
    acceptTypes: ['.xlsx', '.xls', '.csv'],
    maxFileSize: 10
  }

  /** 详细录入导入回调函数 */
  const detailedImportCallbacks: ImportCallbacks = {
    onImport: async (file: File) => {
      // 模拟导入API调用
      return new Promise((resolve) => {
        setTimeout(() => {
          const random = Math.random()
          if (random > 0.2) {
            resolve({
              success: true,
              successMessage: `成功导入 ${Math.floor(random * 30 + 20)} 名学生详细成绩`,
              successCount: Math.floor(random * 30 + 20),
              failCount: 0
            })
          } else {
            resolve({
              success: false,
              successCount: Math.floor(random * 10),
              failCount: Math.floor(random * 5 + 3),
              errorMessages: [
                '第3行：学号不能为空',
                '第5行：题目1.2分数超出范围',
                '第8行：学号不存在'
              ]
            })
          }
        }, 2000)
      })
    },
    onSuccess: () => {
      loadGradeData()
      MessagePlugin.success('详细成绩导入成功')
    },
    onError: (error: Error) => {
      console.error('导入失败:', error)
      MessagePlugin.error('详细成绩导入失败')
    },
    onComplete: () => {
      detailedImportDialogVisible.value = false
    }
  }

  // ==================== 方法 ====================

  /** 加载成绩数据 */
  const loadGradeData = async () => {
    if (!props.assessmentId || !props.taskId) {
      console.warn('缺少必要的参数：assessmentId 或 taskId')
      return
    }

    loading.value = true
    try {
      // TODO: 调用后端API获取数据
      // const response = await getAssessmentTaskScores(props.assessmentId, props.taskId)
      
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 800))
      
      // Mock数据 - 考核内容数据
      assessmentContent.value = {
        id: props.assessmentId,
        title: '期末考试',
        assessmentName: '2024年春季学期期末考试',
        questionStructure: [
          {
            questionType: '单选题',
            questionTypeName: '单选题',
            questions: [
              {
                questionNumber: '1',
                subItems: [
                  { repositoryAnswerId: 101, subNumber: '1.1', maxScore: 4 },
                  { repositoryAnswerId: 102, subNumber: '1.2', maxScore: 4 },
                  { repositoryAnswerId: 103, subNumber: '1.3', maxScore: 4 },
                  { repositoryAnswerId: 104, subNumber: '1.4', maxScore: 4 },
                  { repositoryAnswerId: 105, subNumber: '1.5', maxScore: 4 }
                ]
              }
            ]
          },
          {
            questionType: '多选题',
            questionTypeName: '多选题',
            questions: [
              {
                questionNumber: '2',
                subItems: [
                  { repositoryAnswerId: 201, subNumber: '2.1', maxScore: 5 },
                  { repositoryAnswerId: 202, subNumber: '2.2', maxScore: 5 },
                  { repositoryAnswerId: 203, subNumber: '2.3', maxScore: 5 }
                ]
              }
            ]
          },
          {
            questionType: '填空题',
            questionTypeName: '填空题',
            questions: [
              {
                questionNumber: '3',
                subItems: [
                  { repositoryAnswerId: 301, subNumber: '3.1', maxScore: 3 },
                  { repositoryAnswerId: 302, subNumber: '3.2', maxScore: 3 },
                  { repositoryAnswerId: 303, subNumber: '3.3', maxScore: 3 },
                  { repositoryAnswerId: 304, subNumber: '3.4', maxScore: 3 }
                ]
              }
            ]
          },
          {
            questionType: '简答题',
            questionTypeName: '简答题',
            questions: [
              {
                questionNumber: '4',
                subItems: [
                  { repositoryAnswerId: 401, subNumber: '4.1', maxScore: 10 },
                  { repositoryAnswerId: 402, subNumber: '4.2', maxScore: 10 }
                ]
              }
            ]
          },
          {
            questionType: '论述题',
            questionTypeName: '论述题',
            questions: [
              {
                questionNumber: '5',
                subItems: [
                  { repositoryAnswerId: 501, subNumber: '5.1', maxScore: 20 }
                ]
              }
            ]
          },
          {
            questionType: '编程题',
            questionTypeName: '编程题',
            questions: [
              {
                questionNumber: '6',
                subItems: [
                  { repositoryAnswerId: 601, subNumber: '6.1', maxScore: 15 }
                ]
              }
            ]
          }
        ]
      }
      
      // Mock数据 - 成绩统计数据
      gradeStats.value = {
        averageScore: 78.5,
        maxScore: 95.0,
        minScore: 45.0,
        submittedCount: 28,
        pendingCount: 2
      }
      
      // Mock数据 - 成绩明细数据
      const mockGradeList = [
        {
          studentId: 1001,
          studentNumber: "2024001",
          studentName: "张三",
          classId: 1,
          className: "计算机科学1班",
          totalScore: 85,
          fullScore: 100,
          scoreRate: 0.85,
          scoreGrade: "良好",
          courseTargetScores: [] as any[],
          detailScores: [
            { repositoryAnswerId: 101, score: 4, questionScore: 4 },
            { repositoryAnswerId: 102, score: 3, questionScore: 4 },
            { repositoryAnswerId: 103, score: 4, questionScore: 4 },
            { repositoryAnswerId: 104, score: 4, questionScore: 4 },
            { repositoryAnswerId: 105, score: 3, questionScore: 4 },
            { repositoryAnswerId: 201, score: 5, questionScore: 5 },
            { repositoryAnswerId: 202, score: 4, questionScore: 5 },
            { repositoryAnswerId: 203, score: 5, questionScore: 5 },
            { repositoryAnswerId: 301, score: 3, questionScore: 3 },
            { repositoryAnswerId: 302, score: 3, questionScore: 3 },
            { repositoryAnswerId: 303, score: 2, questionScore: 3 },
            { repositoryAnswerId: 304, score: 3, questionScore: 3 },
            { repositoryAnswerId: 401, score: 8, questionScore: 10 },
            { repositoryAnswerId: 402, score: 9, questionScore: 10 },
            { repositoryAnswerId: 501, score: 18, questionScore: 20 },
            { repositoryAnswerId: 601, score: 13, questionScore: 15 }
          ],
          entryStatus: "ENTERED",
          entryTime: "2024-01-15 10:30:00",
          lastModifyTime: "2024-01-15 10:30:00",
          entryUserId: 1,
          entryUserName: "教师A"
        },
        {
          studentId: 1002,
          studentNumber: "2024002",
          studentName: "李四",
          classId: 1,
          className: "计算机科学1班",
          totalScore: 92,
          fullScore: 100,
          scoreRate: 0.92,
          scoreGrade: "优秀",
          courseTargetScores: [] as any[],
          detailScores: [
            { repositoryAnswerId: 101, score: 4, questionScore: 4 },
            { repositoryAnswerId: 102, score: 4, questionScore: 4 },
            { repositoryAnswerId: 103, score: 4, questionScore: 4 },
            { repositoryAnswerId: 104, score: 4, questionScore: 4 },
            { repositoryAnswerId: 105, score: 4, questionScore: 4 },
            { repositoryAnswerId: 201, score: 5, questionScore: 5 },
            { repositoryAnswerId: 202, score: 5, questionScore: 5 },
            { repositoryAnswerId: 203, score: 5, questionScore: 5 },
            { repositoryAnswerId: 301, score: 3, questionScore: 3 },
            { repositoryAnswerId: 302, score: 3, questionScore: 3 },
            { repositoryAnswerId: 303, score: 3, questionScore: 3 },
            { repositoryAnswerId: 304, score: 3, questionScore: 3 },
            { repositoryAnswerId: 401, score: 9, questionScore: 10 },
            { repositoryAnswerId: 402, score: 10, questionScore: 10 },
            { repositoryAnswerId: 501, score: 19, questionScore: 20 },
            { repositoryAnswerId: 601, score: 14, questionScore: 15 }
          ],
          entryStatus: "ENTERED",
          entryTime: "2024-01-15 11:20:00",
          lastModifyTime: "2024-01-15 11:20:00",
          entryUserId: 1,
          entryUserName: "教师A"
        },
        {
          studentId: 1003,
          studentNumber: "2024003",
          studentName: "王五",
          classId: 1,
          className: "计算机科学1班",
          totalScore: 75,
          fullScore: 100,
          scoreRate: 0.75,
          scoreGrade: "中等",
          courseTargetScores: [] as any[],
          detailScores: [
            { repositoryAnswerId: 101, score: 3, questionScore: 4 },
            { repositoryAnswerId: 102, score: 4, questionScore: 4 },
            { repositoryAnswerId: 103, score: 3, questionScore: 4 },
            { repositoryAnswerId: 104, score: 3, questionScore: 4 },
            { repositoryAnswerId: 105, score: 4, questionScore: 4 },
            { repositoryAnswerId: 201, score: 4, questionScore: 5 },
            { repositoryAnswerId: 202, score: 5, questionScore: 5 },
            { repositoryAnswerId: 203, score: 4, questionScore: 5 },
            { repositoryAnswerId: 301, score: 2, questionScore: 3 },
            { repositoryAnswerId: 302, score: 3, questionScore: 3 },
            { repositoryAnswerId: 303, score: 3, questionScore: 3 },
            { repositoryAnswerId: 304, score: 2, questionScore: 3 },
            { repositoryAnswerId: 401, score: 7, questionScore: 10 },
            { repositoryAnswerId: 402, score: 8, questionScore: 10 },
            { repositoryAnswerId: 501, score: 16, questionScore: 20 },
            { repositoryAnswerId: 601, score: 12, questionScore: 15 }
          ],
          entryStatus: "ENTERED",
          entryTime: "2024-01-15 14:15:00",
          lastModifyTime: "2024-01-15 14:15:00",
          entryUserId: 1,
          entryUserName: "教师A"
        },
        {
          studentId: 1004,
          studentNumber: "2024004",
          studentName: "赵六",
          classId: 1,
          className: "计算机科学1班",
          totalScore: null,
          fullScore: 100,
          scoreRate: null,
          scoreGrade: "未录入",
          courseTargetScores: [] as any[],
          detailScores: [
            { repositoryAnswerId: 101, score: null, questionScore: 4 },
            { repositoryAnswerId: 102, score: null, questionScore: 4 },
            { repositoryAnswerId: 103, score: null, questionScore: 4 },
            { repositoryAnswerId: 104, score: null, questionScore: 4 },
            { repositoryAnswerId: 105, score: null, questionScore: 4 },
            { repositoryAnswerId: 201, score: null, questionScore: 5 },
            { repositoryAnswerId: 202, score: null, questionScore: 5 },
            { repositoryAnswerId: 203, score: null, questionScore: 5 },
            { repositoryAnswerId: 301, score: null, questionScore: 3 },
            { repositoryAnswerId: 302, score: null, questionScore: 3 },
            { repositoryAnswerId: 303, score: null, questionScore: 3 },
            { repositoryAnswerId: 304, score: null, questionScore: 3 },
            { repositoryAnswerId: 401, score: null, questionScore: 10 },
            { repositoryAnswerId: 402, score: null, questionScore: 10 },
            { repositoryAnswerId: 501, score: null, questionScore: 20 },
            { repositoryAnswerId: 601, score: null, questionScore: 15 }
          ],
          entryStatus: "NOT_ENTERED",
          entryTime: null,
          lastModifyTime: null,
          entryUserId: null,
          entryUserName: null
        }
      ]
      
      gradeList.value = mockGradeList
      
      // 初始化编辑数据
      originalGradeData.value = JSON.parse(JSON.stringify(mockGradeList))
      editingGradeData.value = JSON.parse(JSON.stringify(mockGradeList))
      
      // 初始化详细录入数据
      originalDetailedGradeData.value = JSON.parse(JSON.stringify(mockGradeList))
      editingDetailedGradeData.value = JSON.parse(JSON.stringify(mockGradeList))
      
      // 初始化可见题目类型
      const allTypes = assessmentContent.value.questionStructure.map((item: any) => item.questionType)
      visibleQuestionTypes.value = [...allTypes]
      
      console.log('Mock数据加载成功:', {
        assessmentContent: assessmentContent.value,
        gradeStats: gradeStats.value,
        gradeList: gradeList.value
      })
      
    } catch (error) {
      console.error('加载成绩数据失败:', error)
      MessagePlugin.error('加载成绩数据失败')
    } finally {
      loading.value = false
    }
  }

  /** 处理详细录入单元格点击 */
  const handleDetailedCellClick = (studentId: number, repositoryAnswerId: number) => {
    if (props.scoreStatus !== 1) return;
    editingDetailedCell.value = { studentId, repositoryAnswerId };
    isDetailedInputReallyFocused.value = true;
    addDetailedGlobalKeydownListener();
    nextTick(() => {
      // 解绑上一个 input 的事件
      if (lastDetailedInput) {
        lastDetailedInput = null;
      }
      // 直接 focus input
      if (detailedInputNumberRef.value && detailedInputNumberRef.value.$el) {
        const input = detailedInputNumberRef.value.$el.querySelector('input');
        if (input) {
          input.focus();
          lastDetailedInput = input;
        }
      }
    });
  };

  const exitDetailedEditMode = () => {
    editingDetailedCell.value = null;
    isDetailedInputReallyFocused.value = false;
    removeDetailedGlobalKeydownListener();
  };

  /** 详细录入弹窗级监听 */
  const handleDetailedDialogMouseDown = (event: MouseEvent) => {
    if (!editingDetailedCell.value) {
      exitDetailedEditMode();
      return;
    }
    const path = event.composedPath() as HTMLElement[];
    const match = path.find(
      el =>
        el instanceof HTMLElement &&
        el.classList?.contains('detailed-inline-edit-cell') &&
        String(el.dataset.studentId) === String(editingDetailedCell.value!.studentId) &&
        String(el.dataset.repositoryAnswerId) === String(editingDetailedCell.value!.repositoryAnswerId)
    );
    if (match) {
      // 点击在当前编辑单元格内
      return;
    }
    exitDetailedEditMode();
  };

  /** 处理详细录入单元格失焦 */
  const handleDetailedCellBlur = () => {
    setTimeout(() => {
      // 如果还在编辑模式，且逻辑上应保持 focus
      if (editingDetailedCell.value && isDetailedInputReallyFocused.value && detailedInputNumberRef.value && detailedInputNumberRef.value.$el) {
        const input = detailedInputNumberRef.value.$el.querySelector('input');
        const active = document.activeElement;
        // 只有当 active 不在 t-input-number 组件内时才 focus
        if (
          input &&
          (!active || !detailedInputNumberRef.value.$el.contains(active))
        ) {
          input.focus();
        }
      }
      // 否则（已退出编辑模式），不做任何事
    }, 10);
  };


  /** 更新详细录入成绩 */
  const updateDetailedScore = (row: any, repositoryAnswerId: number, value: number) => {
    const scoreObj = row.detailScores?.find((ds: any) => ds.repositoryAnswerId === repositoryAnswerId)
    if (scoreObj) {
      scoreObj.score = value
      scoreObj.scoreRate = value / scoreObj.questionScore
      // 重新计算总分
      const totalScore = row.detailScores.reduce((sum: number, ds: any) => sum + (ds.score || 0), 0)
      row.totalScore = totalScore
      row.scoreRate = totalScore / row.fullScore
      hasDetailedChanges.value = true
    }
  }

  /** 保存详细录入修改 */
  const handleSaveDetailedChanges = async () => {
    try {
      loading.value = true
      // TODO: 调用后端API保存详细成绩数据
      // 使用现有的API接口进行详细成绩保存
      
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // 更新原始数据
      originalDetailedGradeData.value = JSON.parse(JSON.stringify(editingDetailedGradeData.value))
      hasDetailedChanges.value = false
      MessagePlugin.success('详细成绩修改已保存')
    } catch (error) {
      console.error('保存失败:', error)
      MessagePlugin.error('保存失败，请重试')
    } finally {
      loading.value = false
    }
  }

  /** 取消详细录入修改 */
  const handleCancelDetailedChanges = () => {
    try {
      // 恢复编辑数据为原始数据
      editingDetailedGradeData.value = JSON.parse(JSON.stringify(originalDetailedGradeData.value))
      hasDetailedChanges.value = false
      editingDetailedCell.value = null
      MessagePlugin.success('详细修改已取消')
    } catch (error) {
      MessagePlugin.error('取消失败')
    }
  }

  /** 提交详细成绩 */
  const handleSubmitDetailedGrades = async () => {
    try {
      loading.value = true
      // TODO: 调用后端API提交详细成绩
      // 使用现有的API接口进行详细成绩提交
      
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      MessagePlugin.success('详细成绩提交成功')
      // 提交成功后可以关闭弹窗或刷新数据
      handleClose()
    } catch (error) {
      console.error('提交失败:', error)
      MessagePlugin.error('提交失败，请重试')
    } finally {
      loading.value = false
    }
  }


  /** 检查详细录入成绩是否有变更 */
  const hasDetailedScoreChanged = (studentId: number, repositoryAnswerId: number) => {
    const original = originalDetailedGradeData.value.find(og => og.studentId === studentId)
    const editing = editingDetailedGradeData.value.find(eg => eg.studentId === studentId)

    if (!original || !editing) return false

    const originalScore = original.detailScores?.find((ds: any) => ds.repositoryAnswerId === repositoryAnswerId)?.score
    const editingScore = editing.detailScores?.find((ds: any) => ds.repositoryAnswerId === repositoryAnswerId)?.score

    return originalScore !== editingScore
  }

  /** 检查详细录入总分是否有变更 */
  const hasDetailedTotalScoreChanged = (studentId: number) => {
    const original = originalDetailedGradeData.value.find(og => og.studentId === studentId)
    const editing = editingDetailedGradeData.value.find(eg => eg.studentId === studentId)

    if (!original || !editing) return false

    return original.totalScore !== editing.totalScore
  }

  /** 获取原始详细成绩 */
  const getOriginalDetailedScore = (studentId: number, repositoryAnswerId: number) => {
    const original = originalDetailedGradeData.value.find(og => og.studentId === studentId)
    return original?.detailScores?.find((ds: any) => ds.repositoryAnswerId === repositoryAnswerId)?.score || '-'
  }

  /** 获取原始详细录入总分 */
  const getOriginalDetailedTotalScore = (studentId: number) => {
    const original = originalDetailedGradeData.value.find(og => og.studentId === studentId)
    return original?.totalScore || '-'
  }

  /** 计算详细录入变更的单元格数量 */
  const changedDetailedCellsCount = computed(() => {
    let count = 0

    editingDetailedGradeData.value.forEach(editingRow => {
      const originalRow = originalDetailedGradeData.value.find(og => og.studentId === editingRow.studentId)
      if (!originalRow) return

      // 检查详细成绩变更
      editingRow.detailScores?.forEach((editingScore: any) => {
        const originalScore = originalRow.detailScores?.find((ds: any) => ds.repositoryAnswerId === editingScore.repositoryAnswerId)
        if (originalScore && editingScore.score !== originalScore.score) {
          count++
        }
      })
    })

    return count
  })

  /** 获取状态说明 */
  const statusDescription = computed(() => {
    switch (props.scoreStatus) {
      case 0:
        return '（未开始状态：成绩录入尚未开始）'
      case 1:
        return '（进行中状态：可以编辑成绩）'
      case 2:
        return '（已提交状态：成绩已提交，不可编辑）'
      default:
        return ''
    }
  })

  /** 关闭弹窗 */
  const handleClose = () => {
    detailedDialogVisible.value = false
    emit('update:visible', false)
  }

  /** 刷新数据 */
  const handleRefresh = async () => {
    try {
      loading.value = true
      await loadGradeData()
      MessagePlugin.success('数据已刷新')
    } catch (error) {
      console.error('刷新失败:', error)
      MessagePlugin.error('刷新失败，请重试')
    } finally {
      loading.value = false
    }
  }

  /** 导出详细成绩 */
  const handleExportDetailedGrades = () => {
    try {
      MessagePlugin.info('正在导出详细成绩...')

      // 准备导出数据
      const exportData: string[][] = []

      // 添加表头行
      const headers = ['学号', '姓名']

      // 按题型分组添加列标题
      const questionTypeGroups = new Map<string, any[]>()

      assessmentContent.value?.questionStructure?.forEach((question: any) => {
        if (!questionTypeGroups.has(question.questionType)) {
          questionTypeGroups.set(question.questionType, [])
        }
        questionTypeGroups.get(question.questionType)!.push(question)
      })

      // 只导出可见题型的数据
      questionTypeGroups.forEach((questions, questionType) => {
        if (visibleQuestionTypes.value.includes(questionType)) {
          questions.forEach(question => {
            question.subItems.forEach((subItem: any) => {
              headers.push(`${question.questionNumber}.${subItem.subNumber.split('.')[1]}(${subItem.maxScore}分)`)
            })
          })
        }
      })

      headers.push('总分')
      headers.push('状态')

      exportData.push(headers)

      // 添加学生数据行
      editingDetailedGradeData.value.forEach((student: any) => {
        const row = [student.studentId, student.studentName]

        // 添加成绩数据
        questionTypeGroups.forEach((questions, questionType) => {
          if (visibleQuestionTypes.value.includes(questionType)) {
            questions.forEach(question => {
              question.subItems.forEach((subItem: any) => {
                const questions = student.questions as Record<string, number | null>
                const score = questions[subItem.id]
                row.push(score !== null && score !== undefined ? String(score) : '')
              })
            })
          }
        })

        // 添加总分和状态
        row.push(student.totalScore !== null ? String(student.totalScore) : '')
        row.push(student.status === 'submitted' ? '已录入' : '待录入')

        exportData.push(row)
      })

      // 使用xlsx库导出数据
      const ws = XLSX.utils.aoa_to_sheet(exportData)
      const wb = XLSX.utils.book_new()
      XLSX.utils.book_append_sheet(wb, ws, '详细成绩')

      // 生成文件名
      const fileName = `${assessmentContent.value?.title || '详细成绩'}_${props.classInfo?.className || ''}_${new Date().toISOString().slice(0, 10)}.xlsx`

      // 导出文件
      XLSX.writeFile(wb, fileName)

      MessagePlugin.success('详细成绩导出成功')
    } catch (error) {
      console.error('导出失败:', error)
      MessagePlugin.error('详细成绩导出失败')
    }
  }
  /** 详细录入分页处理 */
  const handleDetailedPageChange = (pageInfo: any) => {
    detailedPagination.current = pageInfo.current
    detailedPagination.pageSize = pageInfo.pageSize
  }

  /** 导入详细录入成绩 */
  const handleImportDetailedGrades = () => {
    // 更新模板数据，确保使用最新的题目结构
    detailedImportConfig.templateData = generateDetailedImportTemplate()
    detailedImportDialogVisible.value = true
  }

  /** 处理题目类型可见性变化 */
  const handleQuestionTypeVisibilityChange = (type: string, checked: boolean) => {
    let newArr = [...visibleQuestionTypes.value]
    if (checked && !newArr.includes(type)) {
      newArr.push(type)
    } else if (!checked) {
      newArr = newArr.filter(t => t !== type)
    }
    visibleQuestionTypes.value = newArr
  }

  

  // 监听props变化，控制弹窗显示
  watch([
    () => props.visible,
    () => props.assessmentId,
    () => props.taskId
  ], ([newVisible, newAssessmentId, newTaskId], [oldVisible, oldAssessmentId, oldTaskId]) => {
    if (newVisible && newAssessmentId && newTaskId) {
      detailedDialogVisible.value = true
      // 当弹窗显示且有必要的参数时，自动加载数据
      loadGradeData()
    } else {
      detailedDialogVisible.value = false
    }
  }, { immediate: true })

  // 暴露给父组件的方法
defineExpose({
  loadGradeData
})

  // 新增方法
  const getTotalScoreDisplay = (score: number | null | undefined) => {
    if (score === 0) return 0
    if (score === null || score === undefined) return '-'
    return score
  }

  // 新增方法
  const getTotalScoreClass = (score: number | null | undefined) => {
    if (score === null || score === undefined) return ''
    if (score >= 90) return 'score-brand' // 主色
    if (score >= 80) return 'score-info'  // 信息色
    if (score >= 60) return 'score-success' // 成功色
    return 'score-error' // 错误色
  }

  // 生成详细录入所有分数点的 slot 配置
  const detailedScoreSlots = computed(() => {
    const slots: Array<{ repositoryAnswerId: number, typeGroup: any, q: any, sub: any }> = [];
    assessmentContent.value?.questionStructure?.forEach((typeGroup: any) => {
      if (!visibleQuestionTypes.value.includes(typeGroup.questionType)) return;
      (typeGroup.questions || []).forEach((q: any) => {
        (q.subItems || []).forEach((sub: any) => {
          slots.push({ repositoryAnswerId: sub.repositoryAnswerId, typeGroup, q, sub });
        });
      });
    });
    return slots;
  });
  </script>

  <style lang="less" scoped>
  .score-brand {
    background: var(--td-brand-color-light) !important;
    color: var(--td-brand-color) !important;
  }
  .score-info {
    background: var(--td-info-color-light) !important;
    color: var(--td-info-color) !important;
  }
  .score-success {
    background: var(--td-success-color-light) !important;
    color: var(--td-success-color) !important;
  }
  .score-error {
    background: var(--td-error-color-light) !important;
    color: var(--td-error-color) !important;
  }
  // 隐藏 t-input-number 内部 input 的 spin button
  :deep(.t-input-number input[type='number']) {
    -moz-appearance: textfield;
  }
  :deep(.t-input-number input[type='number']::-webkit-outer-spin-button),
  :deep(.t-input-number input[type='number']::-webkit-inner-spin-button) {
    -webkit-appearance: none;
    margin: 0;
  }
  // 优化总分变更高亮优先级
  :deep(.total-score.score-changed) {
    background: var(--td-warning-color-light) !important;
    color: var(--td-warning-color) !important;
    z-index: 1;
    position: relative;
  }
  .status-hint {
    margin-left: 16px;
    color: var(--td-text-color-secondary);
    font-size: 14px;
    @media (max-width: 960px) {
      display: none;
    }
  }
  .search-toolbar {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: space-between;
    gap: 8px;
    .search-section {
      flex: 1 1 auto;
      min-width: 180px;
      display: flex;
      align-items: center;
    }
    .toolbar-actions {
      display: flex;
      flex-wrap: wrap;
      align-items: center;
      justify-content: flex-end;
      gap: 8px;
      min-width: 240px;
    }
  }
  </style>

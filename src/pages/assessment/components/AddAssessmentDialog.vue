<template>
  <t-dialog
    v-model:visible="dialogVisible"
    :header="title"
    :width="800"
    :footer="false"
    :top="60"
    class="assessment-dialog"
  >
    <template #body>
      <!-- 课程基本信息展示区域 -->
      <div class="form-section">
        <div class="section-title">课程信息</div>
        <div class="course-info-grid">
          <div class="course-info-item">
            <label class="info-label">课程号：</label>
            <span class="info-value">{{ courseInfo?.courseCode || '未设置' }}</span>
          </div>
          <div class="course-info-item">
            <label class="info-label">课程名称：</label>
            <span class="info-value">{{ courseInfo?.courseName || '未设置' }}</span>
          </div>
          <div class="course-info-item">
            <label class="info-label">学时：</label>
            <span class="info-value">{{ courseInfo?.courseHoursTotal || 0 }} 学时</span>
          </div>
          <div class="course-info-item">
            <label class="info-label">学分：</label>
            <span class="info-value">{{ courseInfo?.courseCredit || 0 }} 学分</span>
          </div>
          <div class="course-info-item">
            <label class="info-label">授课年份：</label>
            <span class="info-value">{{ formData.academicYear }}</span>
          </div>
          <div class="course-info-item">
            <label class="info-label">学期：</label>
            <span class="info-value">{{ getSemesterLabel(formData.semester) }}</span>
          </div>
        </div>
      </div>

      <t-form
        ref="formRef"
        :data="formData"
        :rules="rules"
        label-width="100px"
        @submit="onSubmit"
      >
        <!-- 考核信息区域 -->
        <div class="form-section">
          <div class="section-title">考核信息</div>
          <t-form-item name="assessmentName" label="考核名称">
            <t-input
              v-model="formData.assessmentName"
              placeholder="请输入考核名称，如：期中考试、期末考试等"
            />
          </t-form-item>

          <!-- 达成度计算和考核类型更换位置 -->
          <div class="form-row">
            <t-form-item name="achievement" label="达成度计算" class="half-width">
              <div class="calculation-container">
                <t-checkbox
                  v-model="formData.achievement"
                  class="calculation-checkbox"
                >
                  <span class="checkbox-label">参与达成度计算</span>
                </t-checkbox>
              </div>
            </t-form-item>
            <t-form-item name="assessmentType" label="考核类型" class="half-width">
              <t-select
                v-model="formData.assessmentType"
                placeholder="请选择考核类型"
                clearable
              >
                <t-option
                  v-for="type in assessmentTypes"
                  :key="type.value"
                  :value="type.value"
                  :label="type.label"
                />
              </t-select>
            </t-form-item>
          </div>
          <!-- 达成度计算提示信息整行显示 -->
          <div class="calculation-description">
            <t-icon name="info-circle" />
            <span v-if="formData.achievement" class="success-text">
              该考核将参与课程目标达成度的统计计算
            </span>
            <span v-else class="info-text">
              该考核不参与达成度计算，仅作为课程评价参考
            </span>
          </div>
        </div>

        <!-- 录入方式区域 -->
        <div class="form-section">
          <div class="section-title">配置方式</div>
          <t-form-item name="inputMode" label="录入方式">
            <div class="input-mode-cards-vertical">
              <div
                v-for="mode in inputModes"
                :key="mode.value"
                class="input-mode-card"
                :class="formData.inputMode === mode.value ? 'selected' : 'unselected'"
                @click="formData.inputMode = mode.value"
                style="margin-bottom: 12px;"
              >
                <div class="flex items-start gap-3">
                  <t-radio
                    :value="mode.value"
                    :checked="formData.inputMode === mode.value"
                    @change="formData.inputMode = mode.value"
                  />
                  <div class="flex-1">
                    <h4 class="option-title">{{ mode.label }}</h4>
                    <p class="option-desc">{{ mode.description }}</p>
                    <div class="mt-2">
                      <t-tag
                        v-for="tag in mode.tags"
                        :key="tag"
                        :theme="formData.inputMode === mode.value ? 'primary' : 'default'"
                        variant="outline"
                        size="small"
                        class="mr-2"
                      >
                        {{ tag }}
                      </t-tag>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </t-form-item>

          <t-form-item name="description" label="考核说明">
            <t-textarea
              v-model="formData.description"
              placeholder="请输入考核相关说明信息（选填）"
              :autosize="{ minRows: 3, maxRows: 5 }"
            />
          </t-form-item>
        </div>

        <!-- 操作按钮 -->
        <div class="dialog-footer">
          <t-button
            theme="default"
            @click="onCancel"
            class="cancel-button"
          >
            取消
          </t-button>
          <t-button
            theme="primary"
            type="submit"
            :loading="submitLoading"
          >
            {{ props.mode === 'edit' ? '确认修改' : '确认创建' }}
          </t-button>
        </div>
      </t-form>
    </template>
  </t-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, type PropType } from 'vue';
import { MessagePlugin } from 'tdesign-vue-next';
import type { SubmitContext } from 'tdesign-vue-next';
import { saveAssessment } from '@/api/assessment/assessment';
import { formatAcademicPeriod } from '@/utils/academicPeriodUtil';
import { getCourseBaseInfo, type CourseDetailInfo } from '@/api/training/course';

// 定义props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  courseId: {
    type: [String, Number],
    default: ''
  },
  taskId: {
    type: [String, Number],
    default: ''
  },
  defaultAssessmentType: {
    type: [String, Number],
    default: 0
  },
  year: {
    type: String,
    default: ''
  },
  term: {
    type: String,
    default: ''
  },
  // 新增：课程基本信息
  courseInfo: {
    type: Object as PropType<CourseDetailInfo | null>,
    default: (): CourseDetailInfo | null => null
  },
  // 新增：编辑模式相关props
  mode: {
    type: String,
    default: 'create', // 'create' | 'edit'
    validator: (value: string) => ['create', 'edit'].includes(value)
  },
  // 编辑时的考核数据
  assessmentData: {
    type: Object,
    default: null
  }
});

// 定义事件
const emit = defineEmits(['update:visible', 'submit', 'cancel']);

// 表单引用
const formRef = ref(null);

// 课程信息计算属性
const courseInfo = computed(() => {
  console.log('AddAssessmentDialog - courseInfo:', props.courseInfo);
  return props.courseInfo;
});

// 对话框可见性
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
});

// 提交加载状态
const submitLoading = ref(false);

// 动态标题计算
const title = computed(() => {
  const semesterText = `${formData.value.academicYear}${getSemesterLabel(formData.value.semester)}`;
  const courseName = props.courseInfo?.courseName || '未知课程';
  const actionText = props.mode === 'edit' ? '修改考核内容' : '添加考核内容';
  return `${actionText}-${semesterText}-${courseName}`;
});

// 生成当前学年选项（往前一个，当前，往后一个）
const currentYear = new Date().getFullYear();
const currentMonth = new Date().getMonth() + 1;

// 根据当前月份判断当前学年
// 8月-次年7月为一个学年，例如2024-2025学年
const determineCurrentAcademicYear = () => {
  if (currentMonth >= 8) {
    return `${currentYear}-${currentYear + 1}`;
  } else {
    return `${currentYear - 1}-${currentYear}`;
  }
};

// 学期选项
const semesters = [
  { value: 1, label: '春季学期' },
  { value: 2, label: '秋季学期' }
];

// 生成学年选项（简单年份格式）
const academicYears = computed(() => {
  const currentYear = new Date().getFullYear();
  const years = [];

  // 生成未来2年到过去2年的年份选项
  for (let i = currentYear + 2; i >= currentYear - 2; i--) {
    years.push(i);
  }

  return years;
});

// 考核类型选项（从字典获取）
const assessmentTypes = ref<{ value: string | number; label: string }[]>([]);

// 录入方式选项
const inputModes = [
  {
    value: 'direct',
    label: '直接录入',
    description: '教师直接录入最终的总成绩，适用于已有线下成绩记录或计算方式简单的场景。',
    tags: ['快速', '便捷', '总分录入']
  },
  {
    value: 'detailed',
    label: '详细录入',
    description: '教师需设置多个考核子项（如出勤、作业、期中、期末），并为每个子项录入成绩，系统将自动计算总成绩。',
    tags: ['灵活', '过程化', '自动计算']
  }
];


// 表单数据
const formData = ref({
  academicYear: props.courseInfo?.courseVersion?.toString() || currentYear.toString(),
  semester: props.courseInfo?.courseSemester || (currentMonth >= 2 && currentMonth <= 7 ? '2' : '1'), // 默认根据当前月份设置学期
  assessmentName: '',
  assessmentType: props.defaultAssessmentType || assessmentTypes.value[1]?.value, // 默认考核类型为期末考核
  inputMode: 'direct', // 默认直接录入
  achievement: false, // 默认不参与达成度计算
  description: '',
  assessmentMethod: 1, // 默认考核方式为期末考核
  assessmentYear: -1, // 格式化学年
  assessmentTerm: -1,
  assessmentStatus: 1, // 默认状态为1（编辑中）
  scoreType: 0 // 默认录入方式为直接录入
});

// 表单校验规则
const rules = {
  academicYear: [{ required: true, message: '请选择学年', type: 'error' as const }],
  semester: [{ required: true, message: '请选择学期', type: 'error' as const }],
  assessmentName: [{ required: true, message: '请输入考核名称', type: 'error' as const }],
  assessmentType: [{ required: true, message: '请选择考核类型', type: 'error' as const }],
  inputMode: [{ required: true, message: '请选择录入方式', type: 'error' as const }],
};

// 监听课程信息变化
watch(() => props.courseInfo, (newCourseInfo) => {
  if (newCourseInfo?.courseVersion) {
    formData.value.academicYear = newCourseInfo.courseVersion.toString();
  }
  if (newCourseInfo?.courseSemester) {
    formData.value.semester = newCourseInfo.courseSemester;
  }
}, { immediate: true });

// 填充编辑表单数据
const populateFormForEdit = (assessmentData: any) => {
  if (!assessmentData) return;
  console.log('AddAssessmentDialog - 填充编辑表单数据:', assessmentData);
  // 填充基本信息
  formData.value.assessmentName = assessmentData.assessmentName || '';
  formData.value.description = assessmentData.description || '';

  // 填充学年学期信息
  if (assessmentData.assessmentYear) {
    formData.value.academicYear = String(assessmentData.assessmentYear);
  }
  if (assessmentData.assessmentTerm) {
    formData.value.semester = String(assessmentData.assessmentTerm);
  }

  // 填充考核类型
  if (assessmentData.assessmentMethod !== undefined) {
    formData.value.assessmentType = String(assessmentData.assessmentMethod);
  }

  // 填充录入方式
  if (assessmentData.scoreType !== undefined) {
    formData.value.inputMode = assessmentData.scoreType === 0 ? 'direct' : 'detailed';
  }

  // 填充是否参与达成度计算
  if (assessmentData.achievement !== undefined) {
    formData.value.achievement = Boolean(assessmentData.achievement);
  }
};

// 重置表单为默认值
const resetFormToDefault = () => {
  const currentYear = new Date().getFullYear();
  const currentMonth = new Date().getMonth() + 1;

  formData.value = {
    academicYear: props.courseInfo?.courseVersion?.toString() || currentYear.toString(),
    semester: props.courseInfo?.courseSemester || (currentMonth >= 2 && currentMonth <= 7 ? '2' : '1'),
    assessmentName: '',
    assessmentType: props.defaultAssessmentType || assessmentTypes.value[1]?.value,
    inputMode: 'direct',
    achievement: false,
    description: '',
    assessmentMethod: 1,
    assessmentYear: -1,
    assessmentTerm: -1,
    assessmentStatus: 1,
    scoreType: 0
  };
};

// 监听props中默认考核类型的变化
watch(() => props.defaultAssessmentType, (newVal) => {
  if (newVal) {
    formData.value.assessmentType = newVal;
  }
});

// 监听编辑模式和考核数据变化，填充表单
watch([() => props.mode, () => props.assessmentData], ([newMode, newAssessmentData]) => {
  console.log('AddAssessmentDialog - 监听编辑模式和考核数据变化:', newMode, newAssessmentData);
  if (newMode === 'edit' && newAssessmentData) {
    populateFormForEdit(newAssessmentData);
  } else if (newMode === 'create') {
    resetFormToDefault();
  }
}, { immediate: true });

// 取消操作
const onCancel = () => {
  dialogVisible.value = false;
  emit('cancel');
};


// 提交表单
const onSubmit = async (context: SubmitContext<any>) => {
  if (context.validateResult === true) {
    submitLoading.value = true;

    try {
      // 录入方式转换为数字
      const scoreType = formData.value.inputMode === 'direct' || 0 ? 0 : 1;

      // 根据是否参与达成度计算设置 taskId
      const taskId = formData.value.achievement ? -1 : 0;

      // 构建符合 AssessmentDTO 的数据
      const assessmentData: any = {
        taskId: taskId,
        courseId: Number(props.courseId),
        assessmentName: formData.value.assessmentName,
        assessmentMethod: Number(formData.value.assessmentType) || 1, // 默认为期末考核，确保为 number 类型
        assessmentYear: formData.value.academicYear,
        assessmentTerm: formData.value.semester,
        achievement: formData.value.achievement,
        assessmentStatus: 1, // 默认状态为1（编辑中）
        scoreType: scoreType,
        // 可选字段
        description: formData.value.description
      };

      // 如果是编辑模式，添加ID字段
      if (props.mode === 'edit' && props.assessmentData?.id) {
        assessmentData.id = props.assessmentData.id;
      }
      console.log('提交的考核数据:', assessmentData);
      // 调用 API 保存考核信息
      const response = await saveAssessment(assessmentData);

      if (response.code === 200) {
        const successMessage = props.mode === 'edit' ? '考核内容修改成功' : '考核内容创建成功';
        MessagePlugin.success(successMessage);

        // 构建提交给父组件的数据（包含UI需要的额外字段）
        const submissionData = {
          //...formData.value,
          ...assessmentData,
          courseId: props.courseId,
          taskId: props.taskId,
          status: '编辑中', // 默认状态为编辑中
          fullSemester: `${formData.value.academicYear} ${getSemesterLabel(formData.value.semester)}`,
          // 添加保存后返回的数据
          id: response.data?.id || new Date().getTime()
        };

        emit('submit', submissionData);
        // 关闭对话框
        dialogVisible.value = false;
      } else {
        throw new Error(response.message || '保存考核信息失败');
      }
    } catch (error) {
      console.error('保存考核信息失败:', error);
      MessagePlugin.error(error instanceof Error ? error.message : '保存考核信息失败，请稍后重试');
    } finally {

      submitLoading.value = false;
    }
  } else if (context.firstError) {
    MessagePlugin.error(context.firstError);
  }
};

// 获取学期文本标签
const getSemesterLabel = (semesterValue: string): string => {
  const semester = semesters.find(s => String(s.value) === String(semesterValue));
  return semester ? semester.label : '';
};

watch(() => props.visible, async (newVisible) => {
  if (newVisible) {
    await initDialogData();
  }
});

const initDialogData = async () => {
  // 如果有默认的考核类型，设置表单值
  if (props.defaultAssessmentType) {
    formData.value.assessmentType = props.defaultAssessmentType;
  }
  // 如果有课程信息，优先使用课程信息
  if (props.courseInfo?.courseVersion) {
    formData.value.academicYear = props.courseInfo.courseVersion.toString();
  } else if (props.year) {
    formData.value.academicYear = props.year.toString();
  }
  if (props.courseInfo?.courseSemester) {
    formData.value.semester = props.courseInfo.courseSemester;
  } else if (props.term) {
    formData.value.semester = props.term;
  }
  

  // 注意：从这里为全新的业务逻辑，上述的业务逻辑将不再使用
  // 使用课程id通过course.ts中的getCourseDetailInfo，获取课程的基本面信息
  if (props.courseId) {
    if (props.courseInfo) {
      // 如果已经有课程信息，直接使用
      console.log('使用传入的课程信息:', props.courseInfo);
      // 拉取考核类型字典
      if (props.courseInfo.assessmentMethodList && Array.isArray(props.courseInfo.assessmentMethodList)) {
        const dict = props.courseInfo.assessmentMethodList.map((item: any) => ({
          value: item.id,
          label: item.name
        }));
        assessmentTypes.value = dict;
      }
      //formData.value.courseName = courseDetail.courseName;
      if (props.courseInfo.courseVersion) {
        formData.value.academicYear = props.courseInfo.courseVersion.toString();
      }
      if (props.courseInfo.courseSemester) {
        formData.value.semester = props.courseInfo.courseSemester;
      }
    } else {
      // 如果没有课程信息，尝试获取
      try {
        console.log('获取课程信息，courseId:', props.courseId);
        const response = await getCourseBaseInfo(Number(props.courseId));
        console.log('AddAssessmentDialog - API原始响应:', response);

        // 检查响应结构并正确提取数据
        let courseDetail: any = null;
        if (response && typeof response === 'object') {
          if ('data' in response) {
            courseDetail = (response as any).data;
            console.log('AddAssessmentDialog - 从response.data获取课程详情:', courseDetail);
          } else {
            courseDetail = response;
            console.log('AddAssessmentDialog - 直接使用响应作为课程详情:', courseDetail);
          }
        }

        if (courseDetail) {
          // 拉取考核类型字典
          if (courseDetail.assessmentMethodList && Array.isArray(courseDetail.assessmentMethodList)) {
            const dict = courseDetail.assessmentMethodList.map((item: any) => ({
              value: item.id,
              label: item.name
            }));
            assessmentTypes.value = dict;
          }
          if (courseDetail.courseVersion) {
            formData.value.academicYear = courseDetail.courseVersion.toString();
          }
          if (courseDetail.courseSemester) {
            formData.value.semester = courseDetail.courseSemester;
          }
        }
      } catch (error) {
        console.error('获取课程信息失败:', error);
      }
    }
  }
}
</script>

<style lang="less" scoped>
.assessment-dialog {
  :deep(.t-dialog__body) {
    padding: 24px;
  }
}

// 课程信息展示区域样式
.course-info-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-row-gap: 8px;
  grid-column-gap: 24px;
  margin-bottom: 8px;
}
.course-info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
}
.info-label {
  color: var(--td-text-color-secondary);
  min-width: 70px;
  text-align: right;
  margin-right: 8px;
}
.info-value {
  color: var(--td-text-color-primary);
  font-weight: 500;
  flex: 1;
  text-align: left;
}
.success-text {
  color: var(--td-success-color);
  font-weight: 500;
}
.info-text {
  color: var(--td-text-color-secondary);
}

// 录入方式卡片纵向整行显示
.input-mode-cards-vertical {
  display: flex;
  flex-direction: column;
  gap: 8px;
}
.input-mode-card {
  width: 100%;
  background: var(--td-bg-color-container);
  border: 1px solid var(--td-border-level-1);
  border-radius: 6px;
  padding: 14px 18px;
  cursor: pointer;
  transition: border-color 0.2s;
  display: flex;
  align-items: center;
  box-sizing: border-box;
}
.input-mode-card.selected {
  border-color: var(--td-brand-color);
  background: var(--td-brand-color-light);
}
.input-mode-card.unselected {
  border: 1.5px dashed var(--td-border-color);
  background: var(--td-bg-color-container);
  box-shadow: none;
  filter: none;
  opacity: 1;
}
.input-mode-card .flex-1 {
  width: 100%;
}
.option-title {
  font-size: 15px;
  font-weight: 500;
  margin-bottom: 2px;
}
.option-desc {
  font-size: 13px;
  color: var(--td-text-color-secondary);
}

// 只读选择器样式
.readonly-select {
  :deep(.t-select__single) {
    background-color: var(--td-bg-color-container-select);
    cursor: not-allowed;
  }

  :deep(.t-input__inner) {
    background-color: var(--td-bg-color-container-select);
    color: var(--td-text-color-disabled);
  }
}

.form-section {
  margin-bottom: 10px;
  padding-bottom: 10px;
  border-bottom: 1px solid var(--td-component-stroke);

  &:last-child {
    border-bottom: none;
    margin-bottom: 0;
  }

  .section-title {
    font-size: 16px;
    font-weight: 500;
    color: var(--td-text-color-primary);
    margin-bottom: 10px;
    position: relative;
    padding-left: 12px;

    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 4px;
      height: 16px;
      background-color: var(--td-brand-color);
      border-radius: 2px;
    }
  }
}

.form-row {
  display: flex;
  gap: 16px;

  :deep(.t-form__item) {
    flex: 1;
    margin-bottom: 10px;
  }
  :deep(.t-form__item.half-width) {
    margin-bottom: 0;
  }
}

.input-mode-option {
  display: flex;
  flex-direction: column;

  .option-title {
    font-weight: 500;
    color: var(--td-text-color-primary);
  }

  .option-desc {
    font-size: 12px;
    color: var(--td-text-color-secondary);
    margin-top: 4px;
  }
}

// 达成度计算选项样式
.calculation-container {
  display: flex;
  flex-direction: column;
  gap: var(--td-comp-margin-s);
  width: 100%;
}

.calculation-checkbox {
  .checkbox-label {
    font-size: var(--td-font-size-body-medium);
    font-weight: 500;
    color: var(--td-text-color-primary);
  }
}

.calculation-description {
  display: flex;
  align-items: center;
  gap: var(--td-comp-margin-s);
  padding: var(--td-comp-paddingTB-s) var(--td-comp-paddingLR-m);
  background: var(--td-bg-color-container-hover);
  border-radius: var(--td-radius-medium);
  font-size: var(--td-font-size-body-small);
  border-left: 3px solid var(--td-brand-color);
  margin-left: 10px; // 确保从复选框位置开始
  margin-top: 10px;
  :deep(.t-icon) {
    font-size: 14px;
    margin-top: 2px; // 图标稍微上移以对齐文本
  }

  .success-text {
    color: var(--td-success-color-7);
    font-weight: 500;
  }

  .info-text {
    color: var(--td-text-color-secondary);
    font-weight: 500;
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 24px;
  .cancel-button {
    min-width: 88px;
  }

  :deep(.t-button) {
    min-width: 88px;
  }
}

.input-mode-cards {
  display: flex;
  gap: 16px;
}
.input-mode-card {
  border: 1.5px solid var(--td-border-level-1);
  border-radius: 8px;
  padding: 16px 20px;
  cursor: pointer;
  transition: all 0.2s;
  background: var(--td-bg-color-container);
  min-width: 220px;
  flex: 1;
  box-sizing: border-box;
  &.unselected {
    border: 1px solid var(--td-brand-color-1);
    background: var(--td-bg-color-container);
    box-shadow: none;
    filter: none;
    opacity: 1;
  }
  &:hover {
    border-color: var(--td-brand-color-4);
    background: var(--td-bg-color-container-hover);
    box-shadow: 0 2px 8px var(--td-shadow-color-card);
    z-index: 2;
  }
  &.selected {
    border: 1px solid var(--td-brand-color);
    background: var(--td-brand-color-light);
    box-shadow: 0 4px 16px var(--td-shadow-color-card);
    filter: none;
    transform: none;
    opacity: 1;
    z-index: 3;
  }
  .option-title {
    font-size: 16px;
    font-weight: 500;
    color: var(--td-text-color-primary);
  }
  .option-desc {
    font-size: 13px;
    color: var(--td-text-color-secondary);
    margin-top: 4px;
  }
  .mt-2 {
    margin-top: 8px;
  }
  .mr-2 {
    margin-right: 8px;
  }
}

// 响应式调整
@media (max-width: 768px) {
  .form-row {
    flex-direction: column;
    gap: 0;
  }

  .assessment-dialog {
    :deep(.t-dialog) {
      width: 95vw !important;
      max-width: 680px;
    }
  }
}
</style>

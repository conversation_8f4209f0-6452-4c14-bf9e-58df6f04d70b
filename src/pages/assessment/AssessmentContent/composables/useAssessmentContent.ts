import { ref, reactive, computed } from 'vue'
import { useRoute } from 'vue-router'
import { MessagePlugin } from 'tdesign-vue-next'
import { getCourseAssessmentDetail, getCourseBaseInfo } from '@/api/training/course'
import { 
  pageAssessmentExamListByCourseId
} from '@/api/assessment/assessment'
import { mockClassList } from '../data'

// 1. 课程基本信息数据结构
interface CourseBasicInfo {
  courseId: string
  courseName: string
  courseCode: string
  courseLeader: string
  courseCredit: number
  courseNature: string
  courseSemester: string
  courseType: string
  courseDescription?: string
  academicYear?: string // 新增：学年信息
  semester?: string // 新增：学期信息
}

// 2. 考核环节数据结构（基于后端接口）
interface AssessmentSection {
  id: string
  name: string // 考核环节名称
  description?: string // 考核环节描述
  weight: number // 权重占比
  type: number // 考核类型（整数）
  objectiveDistribution: {
    objectiveId: string
    name: string
    percentage: number
  }[]
}

// 3. 课程目标数据结构
interface CourseObjective {
  id: string
  objectiveCode: string
  objectiveName: string
  description: string
  weight: number
  number: number // 课程目标序号
}

// 4. 考核内容数据结构（基于Assessment表）
interface AssessmentContent {
  id: string
  taskId: number // 教学任务ID，重要字段
  courseId: number // 课程ID
  sectionId: string // 所属考核环节ID
  name: string // 考核内容名称
  description: string // 考核描述
  source: 'unified' | 'teacher' // 来源：统一发放或教师自建
  sourceInfo: {
    type: string // 来源类型描述
    creatorId?: string // 创建者ID
    creatorName?: string // 创建者姓名
  }
  inputMode: 'direct' | 'detailed' // 录入方式
  time?: string // 考核时间（用于表单）
  examTime?: {
    startTime: string // 考核开始时间
    endTime: string // 考核结束时间
    duration: number // 考核时长（分钟）
  }
  status: 'draft' | 'published' | 'ended' // 状态
  publishTime?: string // 发布时间
  totalScore: number // 总分
  passScore: number // 及格分数
  configStatus: 'not_configured' | 'configured' | 'partially_configured' // 配置状态
  createTime: string
  updateTime: string
  creatorId: string
  creatorName: string
  achievement: boolean // 是否参与达成度计算
}

// 兼容性数据结构（临时保留）
interface CourseInfo {
  courseId: string
  courseName: string
  courseCode: string
  assessmentMethods: {
    methodId: string
    methodName: string
  }[]
}

// 模拟数据
const mockCourseBasicInfo: CourseBasicInfo = {
  courseId: '32',
  courseName: '高等数学',
  courseCode: 'MATH101',
  courseLeader: '张教授',
  courseCredit: 4,
  courseNature: '必修',
  courseSemester: '第一学期',
  courseType: '专业基础课',
  courseDescription: '高等数学是理工科专业的基础课程'
}

const mockCourseObjectives: CourseObjective[] = [
  {
    id: '1',
    objectiveCode: 'CO1',
    objectiveName: '掌握微积分基本概念',
    description: '理解极限、连续、导数、积分的概念和性质，能够运用这些基本概念进行数学分析和计算。掌握微积分的基本定理和重要公式，为后续专业课程学习奠定坚实的数学基础。',
    weight: 40,
    number: 1
  },
  {
    id: '2',
    objectiveCode: 'CO2',
    objectiveName: '应用微积分解决问题',
    description: '能够运用微积分知识解决实际问题，包括物理、工程、经济等领域中的数学建模问题。培养学生分析问题、建立数学模型、求解问题的综合能力。',
    weight: 35,
    number: 2
  },
  {
    id: '3',
    objectiveCode: 'CO3',
    objectiveName: '培养数学思维能力',
    description: '培养逻辑思维和抽象思维能力，提高数学推理和证明能力。通过微积分的学习，培养学生的严谨思维习惯和创新思维能力，为终身学习奠定基础。',
    weight: 25,
    number: 3
  }
]

const mockAssessmentSections: AssessmentSection[] = [
  {
    id: 'section1',
    name: '平时作业',
    description: '每周布置的练习题和作业',
    weight: 30,
    type: 1,
    objectiveDistribution: [
      { objectiveId: '1', name: '课程目标1', percentage: 40 },
      { objectiveId: '2', name: '课程目标2', percentage: 35 },
      { objectiveId: '3', name: '课程目标3', percentage: 25 }
    ]
  },
  {
    id: 'section2',
    name: '期中考试',
    description: '学期中期的综合测试',
    weight: 30,
    type: 2,
    objectiveDistribution: [
      { objectiveId: '1', name: '课程目标1', percentage: 50 },
      { objectiveId: '2', name: '课程目标2', percentage: 30 },
      { objectiveId: '3', name: '课程目标3', percentage: 20 }
    ]
  },
  {
    id: 'section3',
    name: '期末考试',
    description: '学期末的综合考试',
    weight: 40,
    type: 3,
    objectiveDistribution: [
      { objectiveId: '1', name: '课程目标1', percentage: 30 },
      { objectiveId: '2', name: '课程目标2', percentage: 50 },
      { objectiveId: '3', name: '课程目标3', percentage: 20 }
    ]
  }
]

const mockAssessmentContents: AssessmentContent[] = [
  {
    id: '1',
    taskId: 1, // 教学任务ID
    courseId: 32,
    sectionId: 'section1',
    name: '第一次作业',
    description: '函数与极限练习题',
    source: 'teacher',
    sourceInfo: {
      type: '教师自建',
      creatorId: 'teacher1',
      creatorName: '张老师'
    },
    inputMode: 'direct',
    status: 'published',
    publishTime: '2024-01-15 10:00:00',
    totalScore: 100,
    passScore: 60,
    configStatus: 'configured',
    createTime: '2024-01-10 14:30:00',
    updateTime: '2024-01-15 10:00:00',
    creatorId: 'teacher1',
    creatorName: '张老师',
    achievement: true,
    examTime: {
      startTime: '2024-01-15 09:00:00',
      endTime: '2024-01-15 11:00:00',
      duration: 120
    }
  },
  {
    id: '2',
    taskId: 1, // 教学任务ID
    courseId: 32,
    sectionId: 'section2',
    name: '期中考试',
    description: '微积分基础综合测试',
    source: 'teacher',
    sourceInfo: {
      type: '教师自建',
      creatorId: 'teacher1',
      creatorName: '张老师'
    },
    inputMode: 'detailed',
    status: 'published',
    publishTime: '2024-03-15 14:00:00',
    totalScore: 100,
    passScore: 60,
    configStatus: 'configured',
    createTime: '2024-03-10 09:00:00',
    updateTime: '2024-03-15 14:00:00',
    creatorId: 'teacher1',
    creatorName: '张老师',
    achievement: true,
    examTime: {
      startTime: '2024-03-15 14:00:00',
      endTime: '2024-03-15 16:00:00',
      duration: 120
    }
  }
]

export function useAssessmentContent() {
  const route = useRoute()

  // 响应式数据定义
  const loading = ref(false)
  const currentCourseInfo = ref<any>(null)
  const assessmentSections = ref<AssessmentSection[]>([])
  const selectedSectionId = ref<string>('')
  const selectedSection = ref<AssessmentSection | null>(null)
  const assessmentFormVisible = ref(false)
  const editingSection = ref<AssessmentSection | null>(null)
  const submitting = ref(false)
  const assessmentFormRef = ref()
  const courseObjectives = ref<CourseObjective[]>([])
  const activeTab = ref<string>('')
  const contentLoading = ref(false)
  const assessmentContents = ref<any[]>([])
  const contentFormVisible = ref(false)
  const editingContent = ref<AssessmentContent | null>(null)
  const currentContent = ref<AssessmentContent | null>(null)
  const contentSubmitting = ref(false)
  const contentFormRef = ref()
  const configFormVisible = ref(false)
  const configSubmitting = ref(false)
  const currentCourseId = ref<string>('')
  const legacyCourseInfo = ref<CourseInfo | null>(null)
  const directConfigVisible = ref(false)
  const detailedConfigVisible = ref(false)
  const currentAssessmentType = ref<number | null>(null)

  // 设置当前考核类型
  const setAssessmentType = (type: number | null) => {
    currentAssessmentType.value = type
  }

  // 发布相关数据
  const publishDialogVisible = ref(false)
  const publishDialogData = ref({
    content: null as any,
    allClasses: [] as any[],
    filteredClasses: [] as any[],
    selectedClasses: [] as string[],
    searchKeyword: '',
    selectedCount: 0,
    selectedStudentCount: 0
  })
  const publishSubmitting = ref(false)
  const classTableLoading = ref(false)



  // 表单数据
  const contentForm = reactive({
    sectionId: '',
    name: '',
    description: '',
    inputMode: 'direct' as 'direct' | 'detailed',
    time: ''
  })

  const contentFormRules = {
    sectionId: [
      { required: true, message: '请选择考核环节' }
    ],
    name: [
      { required: true, message: '请输入考核内容名称' }
    ],
    inputMode: [
      { required: true, message: '请选择录入方式' }
    ]
  }

  const assessmentForm = reactive({
    name: '',
    type: '',
    weight: 0,
    description: '',
    inputMode: 'direct' as 'direct' | 'detailed'
  })

  const assessmentFormRules = {
    name: [
      { required: true, message: '请输入考核环节名称' }
    ],
    type: [
      { required: true, message: '请选择考核类型' }
    ],
    weight: [
      { required: true, message: '请输入权重占比' }
    ],
    inputMode: [
      { required: true, message: '请选择录入模式' }
    ]
  }

  // 分页配置
  const contentPagination = reactive({
    current: 1,
    pageSize: 10,
    total: 0
  })

  // 配置相关数据
  const directModeData = ref([])
  const detailedModeData = ref([])
  const objectiveSummaryData = ref([])
  const summaryTableData = ref([])
  const defaultQuestionTypes = ref([])
  const customQuestionTypes = ref([])
  const questionTypes = ref([])
  const usedQuestionTypes = ref([])
  const summaryTableTips = ref('')
  const hasUnsavedData = ref(false)
  const configDialogTitle = ref('')
  const directConfigTitle = ref('')
  const detailedConfigTitle = ref('')
  const totalDirectScore = ref(0)
  const totalDirectPercentage = ref(0)
  const totalDetailedScore = ref(0)
  const totalDetailedPercentage = ref(0)
  const isConfigValid = ref(false)
  const isLoadingCache = ref(false)
  const questionTypeRefs = ref({})
  const isHandlingCancel = ref(false)
  const isHandlingDirectCancel = ref(false)

  // 表格列配置
  const contentTableColumns = [
    { colKey: 'term', title: '考核学期', width: 200 },
    { colKey: 'assessmentName', title: '考核内容名称', width: 200 },
    { colKey: 'creator', title: '创建者', width: 100 },
    { colKey: 'achievement', title: '达成度计算', width: 120 },
    { colKey: 'examTime', title: '考核时间', width: 180 },
    { colKey: 'scoreType', title: '录入方式', width: 100 },
    { colKey: 'status', title: '状态', width: 100 },
    { colKey: 'actions', title: '操作', width: 250, fixed: 'right' as const }
  ]

  // 班级表格列配置
  const classTableColumns = [
    { colKey: 'selection', title: '选择', width: 60, fixed: 'left' as const },
    { colKey: 'className', title: '班级名称', width: 200 },
    { colKey: 'studentCount', title: '学生人数', width: 100 },
    { colKey: 'scheduleInfo', title: '上课时间', width: 150 },
    { colKey: 'teacherInfo', title: '授课教师', width: 150 },
    { colKey: 'publishStatus', title: '发布状态', width: 120 },
    { colKey: 'actions', title: '操作', width: 100, fixed: 'right' as const }
  ]

  // 计算属性

  const isDirectConfigValid = computed(() => {
    return totalDirectPercentage.value === 100
  })

  const isDetailedConfigValid = computed(() => {
    return totalDetailedPercentage.value === 100
  })

  // 表格Footer数据
  const directModeFooterData = computed(() => {
    return [
      '合计',
      `${totalDirectScore.value}分`,
      `${totalDirectPercentage.value}%`,
      totalDirectPercentage.value === 100 ? '配置有效' : '占比总和必须为100%'
    ]
  })

  const summaryFooterData = computed(() => {
    return [
      '合计',
      `${totalDetailedScore.value}分`,
      `${totalDetailedPercentage.value}%`
    ]
  })

  // 配置弹窗表格列配置
  const directModeConfigColumns = [
    { 
      colKey: 'objectiveName', 
      title: '课程目标', 
      minWidth: 200, 
      align: 'left'
    },
    { 
      colKey: 'score', 
      title: '分值', 
      width: 180, 
      align: 'center'
    },
    { 
      colKey: 'percentage', 
      title: '占比(%)', 
      width: 100, 
      align: 'center'
    },
    { 
      colKey: 'description', 
      title: '说明', 
      minWidth: 300, 
      align: 'left'
    }
  ]

  const directModeFootData = computed(() => {
    return [
      '合计',
      `${totalDirectScore.value}分`,
      `${totalDirectPercentage.value}%`,
      totalDirectPercentage.value === 100 ? '配置有效' : '占比总和必须为100%'
    ]
  })

  const detailedModeConfigColumns = [
    { 
      colKey: 'questionType', 
      title: '题目类型', 
      width: 150, 
      align: 'left'
    },
    { 
      colKey: 'questionCount', 
      title: '题目数量', 
      width: 100, 
      align: 'center'
    },
    { 
      colKey: 'totalScore', 
      title: '总分', 
      width: 100, 
      align: 'center'
    },
    { 
      colKey: 'objectiveDistribution', 
      title: '目标分布', 
      minWidth: 300, 
      align: 'left'
    }
  ]

  const summaryColumns = [
    { 
      colKey: 'objectiveName', 
      title: '课程目标', 
      width: 200, 
      align: 'left'
    },
    { 
      colKey: 'totalScore', 
      title: '总分', 
      width: 100, 
      align: 'center'
    },
    { 
      colKey: 'percentage', 
      title: '占比(%)', 
      width: 100, 
      align: 'center'
    }
  ]

  const summaryFootData = computed(() => {
    return [
      '合计',
      `${totalDetailedScore.value}分`,
      `${totalDetailedPercentage.value}%`
    ]
  })

  // 根据考核类型获取考核环节ID
  const getSectionIdByAssessmentType = (assessmentType: number) => {
    // 这里需要根据实际的考核类型映射逻辑来实现
    // 暂时使用简单的映射，后续需要根据实际的考核类型和考核环节的对应关系来完善
    const typeMap: Record<number, string> = {
      1: 'section1', // 作业
      2: 'section2', // 考试
      3: 'section3'  // 实验
    }
    
    // 如果找不到对应的考核环节，返回第一个考核环节的ID
    const sectionId = typeMap[assessmentType]
    if (sectionId && assessmentSections.value.find(section => section.id === sectionId)) {
      return sectionId
    }
    
    // 如果找不到对应的考核环节，返回第一个考核环节的ID
    return assessmentSections.value.length > 0 ? assessmentSections.value[0].id : 'section1'
  }

  // 根据考核信息获取状态
  const getStatusByAssessment = (assessment: any): 'draft' | 'published' | 'ended' => {
    // 根据assessmentStatus字段判断状态
    // 1: 编辑中, 2: 已发布, 3: 已结束
    switch (assessment.assessmentStatus) {
      case 1:
        return 'draft'
      case 2:
        return 'published'
      case 3:
        return 'ended'
      default:
        return 'draft'
    }
  }

  // 加载考核内容数据，支持assessmentType参数
  const loadAssessmentContents = async (assessmentTypeParam?: number) => {
    if (!currentCourseId.value) return
    
    contentLoading.value = true
    try {
      // 取assessmentType，优先参数，其次currentAssessmentType，最后默认1
      let assessmentType = assessmentTypeParam ?? currentAssessmentType.value ?? 1
      if (!assessmentType || isNaN(assessmentType)) assessmentType = 1
      // 使用真实的API接口获取考核内容
      const response = await pageAssessmentExamListByCourseId({
        courseId: Number(currentCourseId.value),
        pageNum: contentPagination.current,
        pageSize: contentPagination.pageSize,
        assessmentType: assessmentType
      })

      if (response && response.data) {
        // 转换后端数据为前端格式
        assessmentContents.value = response.data.records
        contentPagination.total = response.data.total || 0
      }
    } catch (error) {
      console.error('加载考核内容失败:', error)
      // 使用模拟数据
      assessmentContents.value = mockAssessmentContents
    } finally {
      contentLoading.value = false
    }
  }

  // 基础函数定义
  const loadAssessmentData = async () => {
    if (!currentCourseId.value) return
    
    loading.value = true
    try {
      // 获取考核环节数据
      const courseId = Number(currentCourseId.value)
      const assessmentDetail = await getCourseAssessmentDetail(courseId)
      // 获取详细课程信息
      const { data } = await getCourseBaseInfo(courseId)
      // 合并信息，优先用courseDetail，补充assessmentDetail的assessmentMethods等
      currentCourseInfo.value = {
        ...data,
        //assessmentMethods: assessmentDetail.assessmentMethods,
        assessmentWeight: assessmentDetail.assessmentWeight,
        assessmentProportions: assessmentDetail.assessmentProportions,
        finalExamWeights: assessmentDetail.finalExamWeights,
        courseObjectiveList: assessmentDetail.courseObjectiveList
      }

      // 设置课程目标
      courseObjectives.value = assessmentDetail.courseObjectiveList.map(objective => ({
        id: objective.objectiveId.toString(),
        objectiveCode: `CO${objective.objectiveId}`,
        objectiveName: objective.objectiveName,
        description: objective.description || '',
        weight: objective.expectedScore || 0,
        number: objective.number // 添加number字段
      }))

      // 使用 assessmentWeight 作为考核环节列表，使用 assessmentProportions 获取课程目标占比
      assessmentSections.value = assessmentDetail.assessmentWeight.map((weightConfig, index) => {
        // 找到对应的占比配置
        const proportionConfig = assessmentDetail.assessmentProportions.find(
          prop => prop.methodId === weightConfig.methodId
        )
        
        return {
          id: weightConfig.methodId,
          name: weightConfig.methodName,
          description: weightConfig.description || `${weightConfig.methodName}考核环节`,
          weight: weightConfig.examWeight, // 使用权重分配中的权重
          type: Number(weightConfig.methodId), // 关键：用methodId作为考核类型整数
          objectiveDistribution: proportionConfig?.objectiveList?.map((objective, objIndex) => ({
            objectiveId: objective.objectiveId,
            name: `课程目标${objIndex + 1}`,
            percentage: objective.weight
          })) || []
        }
      })

      // 加载考核内容数据（从Assessment表）
      await loadAssessmentContents()

      // 为兼容性保留旧的数据结构
      legacyCourseInfo.value = null
      
      // 默认选择第一个考核环节
      if (assessmentSections.value.length > 0) {
        selectedSectionId.value = assessmentSections.value[0].id
        selectedSection.value = assessmentSections.value[0]
        activeTab.value = assessmentSections.value[0].id
      }
      
    } catch (error) {
      console.error('加载考核环节数据失败:', error)
    } finally {
      loading.value = false
    }
  }

  const initializeCourse = () => {
    const courseId = route.params.courseId as string
    
    if (courseId) {
      currentCourseId.value = courseId
      loadAssessmentData()
    } else {
      // 如果没有courseId，使用默认的第一个课程
      currentCourseId.value = mockCourseBasicInfo.courseId
      loadAssessmentData()
    }
  }

  // 辅助函数定义
  const getSummaryCount = (questionType: string): number => {
    return summaryTableData.value.reduce((total: number, row: any) => 
      total + (row.questionTypes[questionType]?.count || 0), 0)
  }

  const getSummaryScore = (questionType: string): number => {
    return Math.round(summaryTableData.value.reduce((total: number, row: any) => 
      total + (row.questionTypes[questionType]?.score || 0), 0) * 100) / 100
  }

  const getTotalScore = (): number => {
    return totalDirectScore.value
  }

  const formatDateOnly = (dateString: string): string => {
    if (!dateString) return ''
    return new Date(dateString).toLocaleDateString('zh-CN')
  }

  const formatTimeDetail = (dateString: string): string => {
    if (!dateString) return ''
    return new Date(dateString).toLocaleString('zh-CN')
  }

  // 发布相关函数
  const handleConfirmPublish = async () => {
    try {
      publishSubmitting.value = true
      await new Promise(resolve => setTimeout(resolve, 1000))
      MessagePlugin.success('发布成功')
      publishDialogVisible.value = false
      loadAssessmentContents()
    } catch (error) {
      console.error('发布失败:', error)
      MessagePlugin.error('发布失败，请重试')
    } finally {
      publishSubmitting.value = false
    }
  }

  const handleCancelPublish = () => {
    publishDialogVisible.value = false
  }

  const handleSelectAllClasses = (selectAll: boolean) => {
    if (selectAll) {
      publishDialogData.value.selectedClasses = publishDialogData.value.filteredClasses
        .filter(cls => cls.publishStatus !== 'published')
        .map(cls => cls.classId)
    } else {
      publishDialogData.value.selectedClasses = []
    }
    updateSelectionCount()
  }

  const handleReverseSelection = () => {
    const availableClasses = publishDialogData.value.filteredClasses
      .filter(cls => cls.publishStatus !== 'published')
      .map(cls => cls.classId)
    
    const currentSelected = publishDialogData.value.selectedClasses
    publishDialogData.value.selectedClasses = availableClasses.filter(id => !currentSelected.includes(id))
    updateSelectionCount()
  }

  const handleClassSelectionChange = (classId: string, checked: boolean) => {
    if (checked) {
      if (!publishDialogData.value.selectedClasses.includes(classId)) {
        publishDialogData.value.selectedClasses.push(classId)
      }
    } else {
      publishDialogData.value.selectedClasses = publishDialogData.value.selectedClasses.filter(id => id !== classId)
    }
    updateSelectionCount()
  }

  const updateSelectionCount = () => {
    publishDialogData.value.selectedCount = publishDialogData.value.selectedClasses.length
    publishDialogData.value.selectedStudentCount = publishDialogData.value.filteredClasses
      .filter(cls => publishDialogData.value.selectedClasses.includes(cls.classId))
      .reduce((sum, cls) => sum + cls.studentCount, 0)
  }

  // 配置相关函数
  const handleSaveDetailedConfig = async () => {
    try {
      configSubmitting.value = true
      await new Promise(resolve => setTimeout(resolve, 1000))
      MessagePlugin.success('配置保存成功')
      detailedConfigVisible.value = false
    } catch (error) {
      console.error('配置保存失败:', error)
      MessagePlugin.error('配置保存失败，请重试')
    } finally {
      configSubmitting.value = false
    }
  }

  const handleCancelDetailedConfig = () => {
    detailedConfigVisible.value = false
  }

  // 初始化函数
  const initialize = () => {
    initializeCourse()
  }

  return {
    // 响应式数据
    loading,
    currentCourseInfo,
    assessmentSections,
    selectedSectionId,
    selectedSection,
    assessmentFormVisible,
    editingSection,
    submitting,
    assessmentFormRef,
    courseObjectives,
    activeTab,
    contentLoading,
    assessmentContents,
    contentFormVisible,
    editingContent,
    currentContent,
    contentSubmitting,
    contentFormRef,
    configFormVisible,
    configSubmitting,
    currentCourseId,
    legacyCourseInfo,
    directConfigVisible,
    detailedConfigVisible,
    currentAssessmentType,
    setAssessmentType,
    contentForm,
    contentFormRules,
    directModeData,
    detailedModeData,
    objectiveSummaryData,
    summaryTableData,
    defaultQuestionTypes,
    customQuestionTypes,
    questionTypes,
    publishDialogVisible,
    publishDialogData,
    publishSubmitting,
    classTableLoading,
    usedQuestionTypes,
    summaryTableTips,
    hasUnsavedData,
    configDialogTitle,
    directConfigTitle,
    detailedConfigTitle,
    totalDirectScore,
    totalDirectPercentage,
    totalDetailedScore,
    totalDetailedPercentage,
    isConfigValid,
    isDirectConfigValid,
    isDetailedConfigValid,
    directModeFooterData,
    summaryFooterData,
    contentPagination,
    assessmentForm,
    assessmentFormRules,
   
    directModeConfigColumns,
    directModeFootData,
    detailedModeConfigColumns,
    summaryColumns,
    summaryFootData,
    contentTableColumns,
    classTableColumns,
    isLoadingCache,
    questionTypeRefs,
    isHandlingCancel,
    isHandlingDirectCancel,
    getTotalScore,

    // 基础函数
    loadAssessmentData,
    loadAssessmentContents,
    initializeCourse,

    // 初始化函数
    initialize,

    // 辅助函数
    getSummaryCount,
    getSummaryScore,
    formatDateOnly,
    formatTimeDetail,

    // 发布相关函数
    handleConfirmPublish,
    handleCancelPublish,
    handleSelectAllClasses,
    handleReverseSelection,
    handleClassSelectionChange,

    // 配置相关函数
    handleSaveDetailedConfig,
    handleCancelDetailedConfig
  }
} 
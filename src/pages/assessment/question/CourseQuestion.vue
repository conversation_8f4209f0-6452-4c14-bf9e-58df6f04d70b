<template>
  <div class="course-container">
    <!-- 顶部筛选栏 -->
    <div class="filter-bar">
      <t-input
        v-model="searchKeyword"
        placeholder="搜索课程名称"
        clearable
        class="search-input"
        @clear="handleSearch"
        @enter="handleSearch"
      >
        <template #suffix-icon>
          <t-icon name="search" @click="handleSearch" />
        </template>
      </t-input>

      <t-select
        v-model="filterParams.category"
        placeholder="全部类别"
        clearable
        class="filter-select"
        @change="handleFilterChange"
      >
        <t-option
          v-for="item in categoryOptions"
          :key="item.value"
          :value="item.value"
          :label="item.label"
        />
      </t-select>

      <t-select
        v-model="filterParams.difficulty"
        placeholder="全部难度"
        clearable
        class="filter-select"
        @change="handleFilterChange"
      >
        <t-option
          v-for="item in difficultyOptions"
          :key="item.value"
          :value="item.value"
          :label="item.label"
        />
      </t-select>

      <t-button theme="primary" @click="handleSearch">
        搜索
      </t-button>
      <t-button variant="outline" @click="handleReset">
        重置
      </t-button>
    </div>

    <!-- 课程列表 -->
    <div class="course-list">
      <t-row :gutter="[16, 16]">
        <t-col
          v-for="course in courseData"
          :key="course.id"
          :xs="12"
          :sm="8"
          :md="6"
          :lg="6"
          :xl="4"
        >
          <t-card
            class="course-card"
            hover-shadow
            :bordered="false"
            @click="goToQuestionBank(course.id)"
          >
            <template #cover>
              <img :src="course.cover || defaultCover" class="course-cover" />
              <t-tag
                v-if="course.isNew"
                theme="danger"
                class="new-tag"
              >
                新
              </t-tag>
            </template>

            <div class="course-content">
              <div class="course-title">
                {{ course.name }}
              </div>

              <div class="course-meta">
                <div class="meta-item">
                  <t-icon name="user" size="16px" />
                  <span>{{ course.studentCount }}人学习</span>
                </div>

                <div class="meta-item">
                  <t-icon name="file" size="16px" />
                  <span>{{ course.questionCount }}题</span>
                </div>
              </div>

              <div class="course-footer">
                <t-tag
                  :theme="getDifficultyTag(course.difficulty)"
                  variant="light"
                >
                  {{ course.difficulty }}
                </t-tag>

                <div class="course-category">
                  {{ course.category }}
                </div>
              </div>
            </div>
          </t-card>
        </t-col>
      </t-row>
    </div>

    <!-- 分页 -->
    <div class="pagination">
      <t-pagination
        v-model="pagination.current"
        v-model:pageSize="pagination.pageSize"
        :total="pagination.total"
        :page-size-options="[12, 24, 36, 48]"
        @change="handlePageChange"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()

// 搜索和筛选
const searchKeyword = ref('')
const filterParams = reactive({
  category: '',
  difficulty: ''
})

const categoryOptions = [
  { value: 'math', label: '数学' },
  { value: 'physics', label: '物理' },
  { value: 'chemistry', label: '化学' },
  { value: 'biology', label: '生物' },
  { value: 'language', label: '语文' },
  { value: 'english', label: '英语' },
  { value: 'history', label: '历史' },
  { value: 'geography', label: '地理' }
]

const difficultyOptions = [
  { value: 'easy', label: '初级' },
  { value: 'medium', label: '中级' },
  { value: 'hard', label: '高级' }
]

// 分页
const pagination = reactive({
  current: 1,
  pageSize: 12,
  total: 0
})

// 课程数据
const courseData = ref([
  {
    id: 1,
    name: '高等数学（上）',
    cover: '/image/course/math.jpg',
    category: '数学',
    difficulty: '高级',
    studentCount: 1250,
    questionCount: 356,
    isNew: true,
    description: '涵盖极限、导数、积分等基础内容'
  },
  {
    id: 2,
    name: '大学物理',
    cover: '/image/course/linear.jpg',
    category: '物理',
    difficulty: '中级',
    studentCount: 980,
    questionCount: 278,
    isNew: false,
    description: '力学、热学、电磁学基础知识'
  },
  {
    id: 3,
    name: '基础英语',
    cover: '/image/course/discrete.jpg',
    category: '英语',
    difficulty: '初级',
    studentCount: 2150,
    questionCount: 420,
    isNew: false,
    description: '英语基础语法与词汇'
  },
  {
    id: 4,
    name: '有机化学',
    cover: '/image/course/numerical.jpg',
    category: '化学',
    difficulty: '高级',
    studentCount: 760,
    questionCount: 312,
    isNew: true,
    description: '有机化合物结构与性质'
  },
  {
    id: 5,
    name: '中国近代史',
    cover: '/image/course/discrete.jpg',
    category: '历史',
    difficulty: '中级',
    studentCount: 890,
    questionCount: 195,
    isNew: false,
    description: '1840-1949年中国历史'
  },
  {
    id: 6,
    name: '世界地理',
    cover: '/image/course/prob.jpg',
    category: '地理',
    difficulty: '初级',
    studentCount: 670,
    questionCount: 230,
    isNew: false,
    description: '世界自然与人文地理'
  },
  {
    id: 7,
    name: '现代文学',
    cover: '/image/course/discrete.jpg',
    category: '语文',
    difficulty: '中级',
    studentCount: 540,
    questionCount: 180,
    isNew: false,
    description: '20世纪中国文学作品赏析'
  },
  {
    id: 8,
    name: '细胞生物学',
    cover: '/image/course/discrete.jpg',
    category: '生物',
    difficulty: '高级',
    studentCount: 430,
    questionCount: 265,
    isNew: true,
    description: '细胞结构与功能研究'
  },
  {
    id: 9,
    name: '高等数学（上）',
    cover: '/image/course/math.jpg',
    category: '数学',
    difficulty: '高级',
    studentCount: 1250,
    questionCount: 356,
    isNew: true,
    description: '涵盖极限、导数、积分等基础内容'
  },
  {
    id: 10,
    name: '大学物理',
    cover: '/image/course/linear.jpg',
    category: '物理',
    difficulty: '中级',
    studentCount: 980,
    questionCount: 278,
    isNew: false,
    description: '力学、热学、电磁学基础知识'
  },
  {
    id: 11,
    name: '基础英语',
    cover: '/image/course/discrete.jpg',
    category: '英语',
    difficulty: '初级',
    studentCount: 2150,
    questionCount: 420,
    isNew: false,
    description: '英语基础语法与词汇'
  },
  {
    id: 12,
    name: '有机化学',
    cover: '/image/course/numerical.jpg',
    category: '化学',
    difficulty: '高级',
    studentCount: 760,
    questionCount: 312,
    isNew: true,
    description: '有机化合物结构与性质'
  },
  {
    id: 13,
    name: '中国近代史',
    cover: '/image/course/discrete.jpg',
    category: '历史',
    difficulty: '中级',
    studentCount: 890,
    questionCount: 195,
    isNew: false,
    description: '1840-1949年中国历史'
  },
  {
    id: 14,
    name: '世界地理',
    cover: '/image/course/prob.jpg',
    category: '地理',
    difficulty: '初级',
    studentCount: 670,
    questionCount: 230,
    isNew: false,
    description: '世界自然与人文地理'
  },
  {
    id: 15,
    name: '现代文学',
    cover: '/image/course/discrete.jpg',
    category: '语文',
    difficulty: '中级',
    studentCount: 540,
    questionCount: 180,
    isNew: false,
    description: '20世纪中国文学作品赏析'
  },
  {
    id: 16,
    name: '细胞生物学',
    cover: '/image/course/discrete.jpg',
    category: '生物',
    difficulty: '高级',
    studentCount: 430,
    questionCount: 265,
    isNew: true,
    description: '细胞结构与功能研究'
  }
])

// 获取难度标签样式
const getDifficultyTag = (difficulty) => {
  const map = {
    '初级': 'success',
    '中级': 'warning',
    '高级': 'danger'
  }
  return map[difficulty] || 'default'
}

// 跳转到题库
const goToQuestionBank = (courseId) => {
  router.push(`/coursesQuestion/questionList/${courseId}`)
}

// 搜索
const handleSearch = () => {
  pagination.current = 1
  fetchCourseData()
}

// 筛选变化
const handleFilterChange = () => {
  pagination.current = 1
  fetchCourseData()
}

// 重置筛选
const handleReset = () => {
  searchKeyword.value = ''
  filterParams.category = ''
  filterParams.difficulty = ''
  pagination.current = 1
  fetchCourseData()
}

// 分页变化
const handlePageChange = (pageInfo) => {
  pagination.current = pageInfo.current
  pagination.pageSize = pageInfo.pageSize
  fetchCourseData()
}

// 获取课程数据（模拟API请求）
const fetchCourseData = () => {
  // 这里应该是API请求，我们模拟一下
  console.log('Fetching data with params:', {
    keyword: searchKeyword.value,
    ...filterParams,
    page: pagination.current,
    pageSize: pagination.pageSize
  })

  // 模拟API延迟
  setTimeout(() => {
    // 实际项目中这里应该是API返回的数据
    pagination.total = courseData.value.length * 3 // 模拟总数据量

    // 模拟筛选效果
    let filteredData = [...courseData.value]

    if (searchKeyword.value) {
      filteredData = filteredData.filter(item =>
        item.name.includes(searchKeyword.value) ||
        item.description.includes(searchKeyword.value)
      )
    }

    if (filterParams.category) {
      filteredData = filteredData.filter(item =>
        categoryOptions.find(opt => opt.value === filterParams.category)?.label === item.category
      )
    }

    if (filterParams.difficulty) {
      filteredData = filteredData.filter(item =>
        difficultyOptions.find(opt => opt.value === filterParams.difficulty)?.label === item.difficulty
      )
    }

    // 模拟分页
    const start = (pagination.current - 1) * pagination.pageSize
    const end = start + pagination.pageSize
    // courseData.value = filteredData.slice(start, end)
    // 注意：这里我们只是模拟，实际使用中应该替换courseData.value

    console.log('Filtered data:', filteredData)
  }, 300)
}

onMounted(() => {
  fetchCourseData()
})
</script>

<style scoped>
.course-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 20px;
}

.filter-bar {
  display: flex;
  gap: 12px;
  margin-bottom: 24px;
  flex-wrap: wrap;
}

.search-input {
  width: 240px;
}

.filter-select {
  width: 160px;
}

.course-list {
  margin-bottom: 24px;
}

.course-card {
  height: 100%;
  cursor: pointer;
  transition: transform 0.2s;
}

.course-card:hover {
  transform: translateY(-4px);
}

.course-cover {
  width: 100%;
  height: 160px;
  object-fit: cover;
  border-radius: 4px 4px 0 0;
  position: relative;
}

.new-tag {
  position: absolute;
  top: 8px;
  right: 8px;
}

.course-content {
  padding: 12px;
}

.course-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 8px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  height: 44px;
  line-height: 1.4;
}

.course-meta {
  display: flex;
  justify-content: space-between;
  margin: 12px 0;
  color: var(--td-text-color-secondary);
  font-size: 13px;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.course-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 12px;
}

.course-category {
  font-size: 13px;
  color: var(--td-text-color-placeholder);
}

.pagination {
  display: flex;
  justify-content: center;
  margin-top: 24px;
}

@media (max-width: 768px) {
  .filter-bar {
    flex-direction: column;
  }

  .search-input,
  .filter-select {
    width: 100%;
  }
}
</style>

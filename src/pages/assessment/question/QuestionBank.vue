<template>
<div>
  <t-card class="question-bank-container">
    <!-- 头部标题和筛选区域 -->
    <div class="header">
      <h2>题库列表</h2>
      <div class="filter-area">
        <t-select
          v-model="filter.type"
          placeholder="请选择题型"
          clearable
          style="width: 200px; margin-right: 16px"
        >
          <t-option
            v-for="item in questionTypes"
            :key="item.value"
            :value="item.value"
            :label="item.label"
          />
        </t-select>

        <t-input
          v-model="filter.creator"
          placeholder="请输入创作者搜索"
          clearable
          style="width: 200px; margin-right: 16px"
        >
          <template #suffix-icon>
            <t-icon name="search" />
          </template>
        </t-input>

        <t-input
          v-model="filter.keyword"
          placeholder="请输入关键词搜索"
          clearable
          style="width: 200px; margin-right: 16px"
        >
          <template #suffix-icon>
            <t-icon name="search" />
          </template>
        </t-input>

        <t-button theme="primary" @click="showAddDialog">
          <t-icon name="add" /> 添加题目
        </t-button>
      </div>
    </div>

    <!-- 题库列表 -->
    <t-table
      :data="filteredQuestions"
      :columns="columns"
      row-key="id"
      hover
      :pagination="pagination"
      @page-change="handlePageChange"
    >
      <template #operation="{ row }">
        <t-button
          theme="primary"
          variant="text"
          @click="handleViewDetail(row)"
        >
          查看详情
        </t-button>
      </template>

      <template #type="{ row }">
        <t-tag :theme="getTypeTagTheme(row.type)">
          {{ getTypeLabel(row.type) }}
        </t-tag>
      </template>

      <template #difficulty="{ row }">
        {{ row.score }}
      </template>
    </t-table>
  </t-card>

  <!-- 添加题目对话框 -->
  <t-dialog
    v-model:visible="addDialogVisible"
    header="添加题目"
    width="600px"
    :footer="false"
  >
    <div class="add-question-dialog">
      <h3>请选择题型</h3>
      <div class="type-options">
        <div
          v-for="type in questionTypes"
          :key="type.value"
          class="type-option"
          @click="handleSelectQuestionType(type.value)"
        >
          <div class="type-icon">
            <t-icon :name="type.icon" size="24px" />
          </div>
          <div class="type-name">{{ type.label }}</div>
        </div>
      </div>
    </div>
  </t-dialog>
</div>

</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import {useRoute, useRouter} from 'vue-router'
import {
  Icon as TIcon,
  Card as TCard,
  Select as TSelect,
  Option as TOption,
  Input as TInput,
  Table as TTable,
  Tag as TTag,
  Button as TButton,
  MessagePlugin
} from 'tdesign-vue-next'

const router = useRouter()
const route = useRoute()
const courseId = route.params.id
console.log(courseId)
// 题目类型选项
const questionTypes =  [
  { value: 'single_choice', label: '单选题', icon: 'view-list' },
  { value: 'multiple_choice', label: '多选题', icon: 'check-circle' },
  { value: 'true_false', label: '判断题', icon: 'check' },
  { value: 'fill_blank', label: '填空题', icon: 'edit-1' },
  { value: 'short_answer', label: '简答题', icon: 'edit-1' },
]

// 筛选条件
const filter = ref({
  type: '',
  creator: '',
  keyword: ''
})

// 分页配置
const pagination = ref({
  current: 1,
  pageSize: 10,
  total: 0
})

// 模拟题库数据
const questions = ref([])

// 获取题目类型标签
const getTypeLabel = (type) => {
  const found = questionTypes.find(item => item.value === type)
  return found ? found.label : type
}

// 获取题目类型标签主题
const getTypeTagTheme = (type) => {
  const themes = {
    single_choice: 'primary',
    multiple_choice: 'warning',
    true_false: 'success',
    fill_blank: 'default',
    short_answer: 'danger',
  }
  return themes[type] || 'default'
}

// 表格列配置
const columns = [
  { colKey: 'id', title: 'ID', width: 80 },
  { colKey: 'title', title: '题目', ellipsis: true },
  { colKey: 'type', title: '题型', width: 120 },
  { colKey: 'score', title: '分值', width: 100 },
  { colKey: 'creator', title: '创作者', width: 100 },
  { colKey: 'subject', title: '所属科目', width: 120 },
  { colKey: 'create_time', title: '创建时间', width: 180 },
  { colKey: 'operation', title: '操作', width: 120 }
]

// 根据筛选条件过滤题目
const filteredQuestions = computed(() => {
  let result = [...questions.value]

  // 按题型筛选
  if (filter.value.type) {
    result = result.filter(q => q.type === filter.value.type)
  }

  // 按创作者筛选
  if (filter.value.creator) {
    const creator = filter.value.creator.toLowerCase()
    result = result.filter(q =>
      q.creator.toLowerCase().includes(creator) ||
      (q.tags && q.tags.some(tag => tag.toLowerCase().includes(creator))))
  }

  // 按关键词筛选
  if (filter.value.keyword) {
    const keyword = filter.value.keyword.toLowerCase()
    result = result.filter(q =>
      q.title.toLowerCase().includes(keyword) ||
      (q.tags && q.tags.some(tag => tag.toLowerCase().includes(keyword))))
  }

  // 更新分页总数
  pagination.value.total = result.length

  // 返回当前页数据
  const start = (pagination.value.current - 1) * pagination.value.pageSize
  const end = start + pagination.value.pageSize
  return result.slice(start, end)
})

// 分页变化处理
const handlePageChange = (pageInfo) => {
  pagination.value.current = pageInfo.current
  pagination.value.pageSize = pageInfo.pageSize
}

// 查看题目详情
const handleViewDetail = (question) => {
  // 根据题目类型跳转到不同的详情页
  let routeName = ''

  switch (question.type) {
    case 'single_choice':
      routeName = 'SingleChoiceDetail'
      break
    case 'multiple_choice':
      routeName = 'MultipleChoiceDetail'
      break
    case 'true_false':
      routeName = 'TrueFalseDetail'
      break
    case 'fill_blank':
      routeName = 'FillBlankDetail'
      break
    case 'short_answer':
      routeName = 'ShortAnswerDetail'
      break
    default:
      MessagePlugin.error('未知的题目类型')
      return
  }

  router.push({
    name: routeName,
    params: {
      id: question.id,
    }
  })
}
// 添加题目对话框状态
const addDialogVisible = ref(false)

// 显示添加题目对话框
const showAddDialog = () => {
  addDialogVisible.value = true
}

// 选择题目类型
const handleSelectQuestionType = (type: string) => {
  addDialogVisible.value = false

  // 根据题目类型跳转到不同的添加页面
  let routeName = ''

  switch (type) {
    case 'single_choice':
      routeName = 'AddSingleChoice'
      break
    case 'multiple_choice':
      routeName = 'AddMultipleChoice'
      break
    case 'true_false':
      routeName = 'AddTrueFalse'
      break
    case 'fill_blank':
      routeName = 'AddFillBlank'
      break
    case 'short_answer':
      routeName = 'AddShortAnswer'
      break
    case 'programming':
      routeName = 'AddProgramming'
      break
    default:
      MessagePlugin.error('未知的题目类型')
      return
  }

  router.push({
    name: routeName,
    query: {
      id: route.params.id,
    }
  })
}

// 模拟获取题库数据
const fetchQuestions = () => {
  // 这里应该是API请求，我们模拟一些数据
  questions.value = [
    {
      id: 1,
      title: 'Vue3中Composition API的主要优势是什么？',
      type: 'single_choice',
      score: 10,
      subject: '前端开发',
      creator: '张三',
      create_time: '2023-05-10 14:30:22',
      tags: ['Vue', '前端']
    },
    {
      id: 3,
      title: 'JavaScript中let和var的主要区别是什么？',
      type: 'multiple_choice',
      score: 9,
      subject: 'JavaScript',
      creator: '张三',
      create_time: '2023-05-15 16:20:33',
      tags: ['JavaScript']
    },
    {
      id: 4,
      title: 'React是MVC框架吗？',
      type: 'true_false',
      score: 5,
      subject: '前端开发',
      creator: '李四',
      create_time: '2023-05-18 11:05:17',
      tags: ['React']
    },
    {
      id: 5,
      title: '请简述HTTP和HTTPS的主要区别',
      type: 'short_answer',
      score: 6,
      subject: '网络',
      creator: '王去',
      create_time: '2023-05-20 13:45:28',
      tags: ['HTTP', '网络']
    },
    {
      id: 6,
      title: 'CSS中，______属性用于设置元素之间的间距',
      type: 'fill_blank',
      score: 8,
      subject: 'CSS',
      creator: '张三',
      create_time: '2023-05-22 10:30:15',
      tags: ['CSS']
    }
  ]

  pagination.value.total = questions.value.length
}

onMounted(() => {
  fetchQuestions()
})
</script>

<style scoped>
.question-bank-container {
  margin: 16px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header h2 {
  margin: 0;
  white-space: nowrap;
}

.filter-area {
  display: flex;
  align-items: center;
  gap: 12px;
}

.filter-item {
  width: 200px;
}

.add-btn {
  white-space: nowrap;
}

/* 响应式调整 */
@media (max-width: 1200px) {
  .filter-item {
    width: 180px;
  }
}

@media (max-width: 992px) {
  .header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .filter-area {
    width: 100%;
    flex-wrap: wrap;
  }

  .filter-item {
    flex: 1 1 calc(50% - 6px);
    min-width: 0;
  }

  .add-btn {
    flex: 0 0 auto;
  }
}

@media (max-width: 576px) {
  .filter-item {
    flex: 1 1 100%;
  }
}

.add-question-dialog {
  padding: 16px 24px;
}

.dialog-title {
  margin: 0 0 24px 0;
  text-align: center;
  font-size: 18px;
  font-weight: 500;
  color: var(--td-text-color-primary);
}

.type-options {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
  padding: 8px;
}

.type-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 24px 16px;
  border-radius: 8px;
  background-color: var(--td-bg-color-container);
  border: 1px solid var(--td-component-stroke);
  cursor: pointer;
  transition: all 0.2s ease;
}

.type-option:hover {
  background-color: var(--td-bg-color-container-hover);
  border-color: var(--td-brand-color);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.type-option:active {
  transform: translateY(0);
}

.type-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  margin-bottom: 12px;
  border-radius: 50%;
  background-color: var(--td-brand-color-light);
  color: var(--td-brand-color);
}

.type-name {
  font-size: 14px;
  font-weight: 500;
  color: var(--td-text-color-primary);
}

/* 响应式调整 */
@media (max-width: 640px) {
  .type-options {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 480px) {
  .type-options {
    grid-template-columns: 1fr;
  }

  .type-option {
    flex-direction: row;
    justify-content: flex-start;
    padding: 16px;
    gap: 16px;
  }

  .type-icon {
    margin-bottom: 0;
  }
}

</style>

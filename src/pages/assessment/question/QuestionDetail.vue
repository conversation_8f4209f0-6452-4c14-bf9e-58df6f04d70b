<template>
  <div class="problem-detail-container">
    <!-- 头部导航和标题 -->
    <t-breadcrumb class="breadcrumb">
      <t-breadcrumb-item to="/courses/home">首页</t-breadcrumb-item>
      <t-breadcrumb-item to="/courses/questionList">题目管理</t-breadcrumb-item>
      <t-breadcrumb-item>题目详情</t-breadcrumb-item>
    </t-breadcrumb>
    <t-card class="question-detail-container" :bordered="false">
        <!-- 头部标题和操作区 -->
        <template #header>
          <div class="header-content">
            <h2>题目详情</h2>
            <t-space class="action-buttons">
              <t-button variant="outline" @click="$router.back()">
                <template #icon><t-icon name="arrow-left" /></template>
                返回
              </t-button>
              <t-button theme="primary" @click="handleEdit">
                <template #icon><t-icon name="edit" /></template>
                编辑题目
              </t-button>
            </t-space>
          </div>
        </template>

        <!-- 题目基本信息 -->
        <t-descriptions :title="`题目ID: ${question.id}`" :column="3" bordered>
          <t-descriptions-item label="题目类型">{{ question.type }}</t-descriptions-item>
          <t-descriptions-item label="难度">
            <t-tag :theme="getDifficultyTag(question.difficulty)" variant="light">
              {{ question.difficulty }}
            </t-tag>
          </t-descriptions-item>
          <t-descriptions-item label="创建时间">{{ question.createTime }}</t-descriptions-item>
          <t-descriptions-item label="更新时间">{{ question.updateTime }}</t-descriptions-item>
          <t-descriptions-item label="创建人">{{ question.creator }}</t-descriptions-item>
          <t-descriptions-item label="状态">
            <t-tag :theme="question.status === 'published' ? 'success' : 'warning'" variant="light">
              {{ question.status === 'published' ? '已发布' : '草稿' }}
            </t-tag>
          </t-descriptions-item>
        </t-descriptions>

        <!-- 题目内容 -->
        <div class="section">
          <h3>题干</h3>
          <div class="content-box">
            <t-textarea :value="question.stem" readonly />
          </div>
        </div>

        <!-- 题目选项（如果是选择题） -->
        <div v-if="question.type === 'choice'" class="section">
          <h3>选项</h3>
          <div class="content-box">
            <t-table :data="question.options" :columns="optionColumns" row-key="id" />
          </div>
        </div>

        <!-- 题目答案 -->
        <div class="section">
          <h3>答案</h3>
          <div class="content-box">
            <t-textarea :value="question.answer" readonly />
          </div>
        </div>

        <!-- 解析 -->
        <div class="section">
          <h3>解析</h3>
          <div class="content-box">
            <t-textarea :value="question.analysis" readonly />
          </div>
        </div>

        <!-- 关联课程目标 -->
        <div class="section">
          <h3>关联课程目标</h3>
          <div class="content-box">
            <t-tag
              v-for="objective in question.courseObjectives"
              :key="objective.id"
              theme="primary"
              variant="light"
              class="objective-tag"
            >
              {{ objective.code }}: {{ objective.name }}
            </t-tag>
            <t-empty v-if="question.courseObjectives.length === 0" description="暂无关联课程目标" />
          </div>
        </div>

        <!-- 编辑题目对话框 -->
        <t-dialog
          v-model:visible="editDialogVisible"
          header="编辑题目"
          width="800px"
          :on-confirm="handleSubmit"
          :on-close="handleCancelEdit"
        >
          <question-edit-form
            v-if="editDialogVisible"
            ref="editFormRef"
            :initial-data="question"
            @submit="handleSubmit"
          />
        </t-dialog>
      </t-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { MessagePlugin } from 'tdesign-vue-next'
import QuestionEditForm from './QuestionEditForm.vue'

const route = useRoute()
const questionId = route.params.id

// 题目数据
const question = ref({
  id: questionId,
  type: 'choice',
  stem: '在Vue 3中，以下哪个选项是组合式API的正确使用方式？',
  options: [
    { id: 1, label: 'A', content: '使用data()和methods定义数据和函数' },
    { id: 2, label: 'B', content: '使用setup()函数和ref/reactive定义响应式数据' },
    { id: 3, label: 'C', content: '使用Vue.extend创建组件' },
    { id: 4, label: 'D', content: '使用new Vue()创建应用实例' }
  ],
  answer: 'B',
  analysis: 'Vue 3的组合式API主要通过setup()函数和ref/reactive等API来组织代码逻辑。',
  difficulty: '中等',
  status: '已发布',
  creator: '张老师',
  createTime: '2023-05-15 10:30:00',
  updateTime: '2023-05-16 14:20:00',
  courseObjectives: [
    { id: 1, code: 'CO1', name: '掌握Vue 3核心概念' },
    { id: 2, code: 'CO2', name: '理解组合式API的使用' }
  ]
})

// 选项表格列配置
const optionColumns = [
  { colKey: 'label', title: '选项', width: '80px' },
  { colKey: 'content', title: '内容' }
]

// 编辑对话框相关
const editDialogVisible = ref(false)
const editFormRef = ref(null)

// 获取题目详情
const fetchQuestionDetail = async () => {
  try {
    // 这里应该是API调用，我们模拟数据
    console.log(`Fetching question ${questionId} details...`)
    // 实际项目中替换为:
    // const res = await getQuestionDetail(questionId)
    // question.value = res.data
  } catch (error) {
    MessagePlugin.error('获取题目详情失败')
    console.error(error)
  }
}

// 根据难度获取标签样式
const getDifficultyTag = (difficulty) => {
  const map = {
    easy: 'success',
    medium: 'warning',
    hard: 'danger'
  }
  return map[difficulty] || 'default'
}

// 编辑题目
const handleEdit = () => {
  editDialogVisible.value = true
}

// 提交编辑
const handleSubmit = async (formData) => {
  try {
    // 这里应该是API调用，我们模拟数据
    console.log('Submitting question edit:', formData)
    // 实际项目中替换为:
    // await updateQuestion(questionId, formData)

    // 更新本地数据
    question.value = { ...question.value, ...formData }
    MessagePlugin.success('题目更新成功')
    editDialogVisible.value = false
  } catch (error) {
    MessagePlugin.error('题目更新失败')
    console.error(error)
  }
}

// 取消编辑
const handleCancelEdit = () => {
  editDialogVisible.value = false
}

onMounted(() => {
  fetchQuestionDetail()
})
</script>

<style scoped lang="less">
.question-detail-container {
  margin: 16px;

  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;

    h2 {
      margin: 0;
      font-size: 20px;
      font-weight: 500;
    }

    .action-buttons {
      position: absolute;
      right: 10px;
      top: 50%;
      transform: translateY(-50%);
      display: flex;
      gap: 12px;
    }
  }

  .section {
    margin-top: 24px;

    h3 {
      margin-bottom: 12px;
      font-size: 16px;
      font-weight: 500;
      color: var(--td-text-color-primary);
    }

    .content-box {
      padding: 16px;
      background: var(--td-bg-color-container);
      border-radius: 3px;
      border: 1px solid var(--td-border-level-1-color);
    }
  }

  .objective-tag {
    margin-right: 8px;
    margin-bottom: 8px;
  }

  :deep(.t-descriptions) {
    margin-bottom: 24px;
  }
}
</style>

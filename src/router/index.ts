import {createRouter, createWebHashHistory, createWebHistory, RouteRecordRaw, RouteRecordName} from 'vue-router';
import { authentication, isHashRouterMode, publicPath } from '@/config'
// 直接导入系统路由，确保其优先加载
import {setupPermissions} from "@/router/permissions";
import {ObeRouteRecord} from "@/types/router";
import Layout from "@/layouts/index.vue";


// 固定路由
export const constantRoutes: Array<ObeRouteRecord> = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/pages/login/index.vue'),
    meta: {
      title: {
        zh_CN: '登录',
        en_US: 'Login',
      },
      hidden: true,
    },
  },
  {
    path: '/',
    name: 'Root',
    component: Layout,
    redirect: '/system/home',
    meta: {
      title: {
        zh_CN: '首页',
        en_US: 'Home',
      },
    },
  },
  {
    path: '/403',
    name: '403',
    component: () => import('@/pages/result/403/index.vue'),
    meta: {
      hidden: true,
    },
  },
  {
    path: '/404',
    name: '404',
    component: () => import('@/pages/result/404/index.vue'),
    meta: {
      hidden: true,
    },
  },
];

// 所有异步路由
export const asyncRoutes: Array<ObeRouteRecord> = [
  // 课程负责人模块
  // 工作台模块
  {
    path: '/dashboard',
    name: 'Dashboard',
    component: () => import('@/layouts/header-only.vue'),
    redirect: '/dashboard/major',
    meta: {
      title: {
        zh_CN: '工作台',
        en_US: 'Dashboard',
      },
      icon: 'dashboard',
      orderNo: 1,
    },
    children: [
      {
        path: 'major',
        name: 'DashboardMajor',
        component: () => import('@/pages/dashboard/TeacherDashboard_backup.vue'),
        meta: {
          title: {
            zh_CN: '教师工作台',
            en_US: 'Teacher Dashboard',
          },
        },
      },
      {
        path: 'teacher/:majorId?',
        name: 'DashboardTeacher',
        component: () => import('@/pages/dashboard/TeacherDashboard.vue'),
        meta: {
          title: {
            zh_CN: '教师工作台',
            en_US: 'Teacher Dashboard',
          },
        },
      },
      {
        path: 'course-leader',
        name: 'DashboardCourseLeader',
        component: () => import('@/pages/dashboard/CourseLeader.vue'),
        meta: {
          title: {
            zh_CN: '课程负责人工作台',
            en_US: 'Course Leader Dashboard',
          },
        },
      },
    ],
  },

  // 教师端课程详情模块
  {
    path: '/teacher/course',
    name: 'TeacherCourse',
    component: () => import('@/layouts/dynamic-sidebar.vue'),
    redirect: '/teacher/course/overview',
    meta: {
      title: {
        zh_CN: '教师端课程详情',
        en_US: 'Teacher Course Detail',
      },
      icon: 'book',
      orderNo: 1.5,
      hidden: true, // 不在侧边栏显示，通过工作台跳转
    },
    children: [
      {
        path: 'overview/:courseId',
        name: 'TeacherCourseOverview',
        component: () => import('@/pages/training/course/TeacherCourseOverview.vue'),
        meta: {
          title: {
            zh_CN: '课程首页',
            en_US: 'Course Overview',
          },
          icon: 'home',
        },
      },
      {
        path: 'info/:courseId',
        name: 'TeacherCourseInfo',
        component: () => import('@/pages/training/course/TeacherCourseInfo.vue'),
        meta: {
          title: {
            zh_CN: '课程基本信息',
            en_US: 'Course Information',
          },
          icon: 'file-text',
        },
      },

      {
        path: 'student/:courseId',
        name: 'TeacherCourseStudent',
        component: () => import('@/pages/training/course/TeacherStudentManagement.vue'),
        meta: {
          title: {
            zh_CN: '学生管理',
            en_US: 'Student Management',
          },
          icon: 'user',
        },
      },
      {
        path: 'grade/:courseId',
        name: 'TeacherCourseGrade',
        component: () => import('@/pages/training/course/TeacherGradeManagement.vue'),
        meta: {
          title: {
            zh_CN: '成绩管理',
            en_US: 'Grade Management',
          },
          icon: 'assignment-checked',
        },
      },
      {
        path: 'question/:courseId',
        name: 'TeacherCourseQuestion',
        component: () => import('@/pages/training/course/TeacherQuestionManagement.vue'),
        meta: {
          title: {
            zh_CN: '题库管理',
            en_US: 'Question Management',
          },
          icon: 'queue',
        },
      },
      {
        path: 'analysis/:courseId',
        name: 'TeacherCourseAnalysis',
        component: () => import('@/pages/training/course/TeacherAchievementAnalysis.vue'),
        meta: {
          title: {
            zh_CN: '达成度分析',
            en_US: 'Achievement Analysis',
          },
          icon: 'chart-line',
        },
      },
      {
        path: 'assessment/class-content/:courseId',
        name: 'TeacherCourseClassAssessmentList',
        component: () => import('@/pages/assessment/class-content/index.vue'),
        meta: {
          title: {
            zh_CN: '班级考核管理',
            en_US: 'Class Assessment Management',
          },
          icon: 'layers',
          hidden: true,
        },
      },
      {
        path: 'class/content/details/:courseId/:classId',
        name: 'TeacherCourseClassAssessmentManagement',
        component: () => import('@/pages/assessment/class-content/management.vue'),
        meta: {
          title: {
            zh_CN: '班级考核内容管理',
            en_US: 'Class Assessment Content Management',
          },
          hidden: true,
        },
      },
    ],
  },

  // 基础数据管理模块
  {
    path: '/base',
    name: 'Base',
    component: Layout,
    redirect: '/base/academy',
    meta: {
      title: {
        zh_CN: '基础数据管理',
        en_US: 'Base Data Management',
      },
      icon: 'database',
      orderNo: 2,
    },
    children: [
      {
        path: 'academy',
        name: 'BaseAcademy',
        component: () => import('@/pages/base/AcademyManagement.vue'),
        meta: {
          title: {
            zh_CN: '学院管理',
            en_US: 'Academy Management',
          },
          icon: 'building',
        },
      },
      {
        path: 'major',
        name: 'BaseMajor',
        component: () => import('@/pages/base/major/MajorIndex.vue'),
        meta: {
          title: {
            zh_CN: '专业管理',
            en_US: 'Major Management',
          },
          icon: 'chart-line',
        },
      },
      {
        path: 'major/management',
        name: 'BaseMajorManagement',
        component: () => import('@/pages/base/major/MajorManagement.vue'),
        meta: {
          title: {
            zh_CN: '专业信息管理',
            en_US: 'Major Information Management',
          },
          hidden: true,
        },
      },
      {
        path: 'major/overview',
        name: 'BaseMajorOverview',
        component: () => import('@/pages/base/major/MajorOverview.vue'),
        meta: {
          title: {
            zh_CN: '专业概览',
            en_US: 'Major Overview',
          },
          hidden: true,
        },
      },
      {
        path: 'classes',
        name: 'BaseClasses',
        component: () => import('@/pages/base/classes/ClassesManagement.vue'),
        meta: {
          title: {
            zh_CN: '班级管理',
            en_US: 'Classes Management',
          },
          icon: 'usergroup',
        },
      },
      {
        path: 'classes/detail/:classId',
        name: 'BaseClassesDetail',
        component: () => import('@/pages/base/classes/ClassesDetail.vue'),
        meta: {
          title: {
            zh_CN: '班级详情',
            en_US: 'Classes Detail',
          },
          hidden: true,
        },
      },
      {
        path: 'student',
        name: 'BaseStudent',
        component: () => import('@/pages/base/student/StudentModify.vue'),
        meta: {
          title: {
            zh_CN: '学生管理',
            en_US: 'Student Management',
          },
          icon: 'user-circle',
        },
      },
      {
        path: 'student/tree',
        name: 'BaseStudentTree',
        component: () => import('@/pages/base/student/StudentTree.vue'),
        meta: {
          title: {
            zh_CN: '学生树形管理',
            en_US: 'Student Tree Management',
          },
          hidden: true,
        },
      },
      {
        path: 'student/user',
        name: 'BaseStudentUser',
        component: () => import('@/pages/base/student/StudentUser.vue'),
        meta: {
          title: {
            zh_CN: '学生用户管理',
            en_US: 'Student User Management',
          },
          hidden: true,
        },
      },
      {
        path: 'student/question/list',
        name: 'BaseStudentQuestionList',
        component: () => import('@/pages/base/student/StudentQuestionList.vue'),
        meta: {
          title: {
            zh_CN: '学生问题列表',
            en_US: 'Student Question List',
          },
          hidden: true,
        },
      },
      {
        path: 'student/question/success',
        name: 'BaseStudentQuestionSuccess',
        component: () => import('@/pages/base/student/StudentQuestionSuccess.vue'),
        meta: {
          title: {
            zh_CN: '学生问题成功页',
            en_US: 'Student Question Success',
          },
          hidden: true,
        },
      },
      {
        path: 'teacher',
        name: 'BaseTeacher',
        component: () => import('@/pages/base/teacher/TeacherCourseDetail.vue'),
        meta: {
          title: {
            zh_CN: '教师管理',
            en_US: 'Teacher Management',
          },
          icon: 'user',
        },
      },
      {
        path: 'teacher/tree',
        name: 'BaseTeacherTree',
        component: () => import('@/pages/base/teacher/TeacherTree.vue'),
        meta: {
          title: {
            zh_CN: '教师树形管理',
            en_US: 'Teacher Tree Management',
          },
          hidden: true,
        },
      },
      {
        path: 'teacher/main',
        name: 'BaseTeacherMain',
        component: () => import('@/pages/base/teacher/TeacherMain.vue'),
        meta: {
          title: {
            zh_CN: '教师主页',
            en_US: 'Teacher Main',
          },
          hidden: true,
        },
      },
      {
        path: 'teacher/new-task',
        name: 'BaseTeacherNewTask',
        component: () => import('@/pages/base/teacher/TeacherNewTask.vue'),
        meta: {
          title: {
            zh_CN: '教师新建任务',
            en_US: 'Teacher New Task',
          },
          hidden: true,
        },
      },
      {
        path: 'teacher/paper-task',
        name: 'BaseTeacherPaperTask',
        component: () => import('@/pages/base/teacher/TeacherPaperTask.vue'),
        meta: {
          title: {
            zh_CN: '教师试卷任务',
            en_US: 'Teacher Paper Task',
          },
          hidden: true,
        },
      },
      {
        path: 'teacher/paper-grading-list',
        name: 'BaseTeacherPaperGradingList',
        component: () => import('@/pages/base/teacher/TeacherPaperGradingList.vue'),
        meta: {
          title: {
            zh_CN: '教师试卷评分列表',
            en_US: 'Teacher Paper Grading List',
          },
          hidden: true,
        },
      },
      {
        path: 'teacher/grade-submission',
        name: 'BaseTeacherGradeSubmission',
        component: () => import('@/pages/base/teacher/TeacherGradeSubmission.vue'),
        meta: {
          title: {
            zh_CN: '教师成绩提交',
            en_US: 'Teacher Grade Submission',
          },
          hidden: true,
        },
      },
      {
        path: 'teacher/document-submit',
        name: 'BaseTeacherDocumentSubmit',
        component: () => import('@/pages/base/teacher/TeacherDocumentSubmit.vue'),
        meta: {
          title: {
            zh_CN: '教师文档提交',
            en_US: 'Teacher Document Submit',
          },
          hidden: true,
        },
      },
      {
        path: 'teacher/student-grade-detail',
        name: 'BaseTeacherStudentGradeDetail',
        component: () => import('@/pages/base/teacher/TeacherStudentGradeDetail.vue'),
        meta: {
          title: {
            zh_CN: '学生成绩详情',
            en_US: 'Student Grade Detail',
          },
          hidden: true,
        },
      },
      {
        path: 'teacher/grading-list',
        name: 'BaseTeacherGradingList',
        component: () => import('@/pages/base/teacher/TeacherGradingList.vue'),
        meta: {
          title: {
            zh_CN: '教师评分列表',
            en_US: 'Teacher Grading List',
          },
          hidden: true,
        },
      },
      {
        path: 'teacher/import-task',
        name: 'BaseTeacherImportTask',
        component: () => import('@/pages/base/teacher/TeacherImportTask.vue'),
        meta: {
          title: {
            zh_CN: '教师导入任务',
            en_US: 'Teacher Import Task',
          },
          hidden: true,
        },
      },
      {
        path: 'teacher/paper-import-task',
        name: 'BaseTeacherPaperImportTask',
        component: () => import('@/pages/base/teacher/TeacherPaperImportTask.vue'),
        meta: {
          title: {
            zh_CN: '教师试卷导入任务',
            en_US: 'Teacher Paper Import Task',
          },
          hidden: true,
        },
      },
      {
        path: 'teacher/paper-new-task',
        name: 'BaseTeacherPaperNewTask',
        component: () => import('@/pages/base/teacher/TeacherPaperNewTask.vue'),
        meta: {
          title: {
            zh_CN: '教师试卷新建任务',
            en_US: 'Teacher Paper New Task',
          },
          hidden: true,
        },
      },
      {
        path: 'teacher/paper-grade-submission',
        name: 'BaseTeacherPaperGradeSubmission',
        component: () => import('@/pages/base/teacher/TeacherPaperGradeSubmission.vue'),
        meta: {
          title: {
            zh_CN: '教师试卷成绩提交',
            en_US: 'Teacher Paper Grade Submission',
          },
          hidden: true,
        },
      },
      {
        path: 'teacher/import',
        name: 'BaseTeacherImport',
        component: () => import('@/pages/base/teacher/TeacherImport.vue'),
        meta: {
          title: {
            zh_CN: '教师导入',
            en_US: 'Teacher Import',
          },
          hidden: true,
        },
      },
      {
        path: 'standard',
        name: 'BaseStandard',
        component: () => import('@/pages/base/standard/GraduationStandard.vue'),
        meta: {
          title: {
            zh_CN: '毕业标准管理',
            en_US: 'Graduation Standard Management',
          },
          icon: 'bookmark',
        },
      },
    ],
  },

  // 培养方案管理模块
  {
    path: '/training',
    name: 'Training',
    component: Layout,
    redirect: '/training/goal',
    meta: {
      title: {
        zh_CN: '培养方案管理',
        en_US: 'Training Program Management',
      },
      icon: 'education',
      orderNo: 3,
    },
    children: [
      {
        path: 'goal',
        name: 'TrainingGoal',
        component: () => import('@/pages/training/goal/index.vue'),
        meta: {
          title: {
            zh_CN: '培养目标',
            en_US: 'Training Goal',
          },
          icon: 'target',
        },
      },
      {
        path: 'requirement',
        name: 'TrainingRequirement',
        component: () => import('@/pages/training/requirement/index.vue'),
        meta: {
          title: {
            zh_CN: '毕业要求',
            en_US: 'Graduation Requirement',
          },
          icon: 'task',
        },
      },
      {
        path: 'course',
        name: 'TrainingCourse',
        component: () => import('@/pages/training/course/CourseDetail.vue'),
        meta: {
          title: {
            zh_CN: '课程体系',
            en_US: 'Course System',
          },
          icon: 'book',
        },
      },
      {
        path: 'course/target/:id',
        name: 'TrainingCourseTarget',
        component: () => import('@/pages/training/course/CourseTarget.vue'),
        meta: {
          title: {
            zh_CN: '课程目标管理',
            en_US: 'Course Target Management',
          },
          hidden: true,
        },
      },
      {
        path: 'course/assessment/:courseId',
        name: 'TrainingCourseAssessment',
        component: () => import('@/pages/training/course/CourseAssessmentPlan.vue'),
        meta: {
          title: {
            zh_CN: '考核方案配置',
            en_US: 'Assessment Plan Configuration',
          },
          hidden: true,
        },
      },
      {
        path: 'matrix',
        name: 'TrainingMatrix',
        component: () => import('@/pages/training/matrix/index.vue'),
        meta: {
          title: {
            zh_CN: '支撑矩阵',
            en_US: 'Support Matrix',
          },
          icon: 'view-module',
        },
      },
    ],
  },

  // 考核评价管理模块
  {
    path: '/assessment',
    name: 'Assessment',
    component: Layout,
    redirect: '/assessment/exam',
    meta: {
      title: {
        zh_CN: '考核评价管理',
        en_US: 'Assessment Management',
      },
      icon: 'file-paste',
      orderNo: 4,
    },
    children: [
      // {
      //   path: 'management',
      //   name: 'AssessmentManagement',
      //   component: () => import('@/pages/assessment/AssessmentManagement.vue'),
      //   meta: {
      //     title: {
      //       zh_CN: '考核管理',
      //       en_US: 'Assessment Management',
      //     },
      //     icon: 'task',
      //   },
      // },
      // {
      //   path: 'xudong',
      //   name: 'AssessmentXudong',
      //   component: () => import('@/pages/assessment/AssessmentXudong.vue'),
      //   meta: {
      //     title: {
      //       zh_CN: '评价管理',
      //       en_US: 'Evaluation Management',
      //     },
      //     icon: 'star',
      //   },
      // },
      {
        path: 'exam',
        name: 'AssessmentExam',
        component: () => import('@/pages/assessment/exam/TeacherExam.vue'),
        meta: {
          title: {
            zh_CN: '考试管理',
            en_US: 'Exam Management',
          },
          icon: 'edit',
        },
      },
      {
        path: 'exam/paper',
        name: 'AssessmentExamPaper',
        component: () => import('@/pages/assessment/exam/ExamPaper.vue'),
        meta: {
          title: {
            zh_CN: '试卷管理',
            en_US: 'Exam Paper',
          },
          hidden: true,
        },
      },
      {
        path: 'question',
        name: 'AssessmentQuestion',
        component: () => import('@/pages/assessment/question/QuestionBank.vue'),
        meta: {
          title: {
            zh_CN: '题库管理',
            en_US: 'Question Bank',
          },
          icon: 'help-circle',
        },
      },
      {
        path: 'question/course',
        name: 'AssessmentQuestionCourse',
        component: () => import('@/pages/assessment/question/CourseQuestion.vue'),
        meta: {
          title: {
            zh_CN: '课程题库',
            en_US: 'Course Question',
          },
          hidden: true,
        },
      },
      {
        path: 'question/detail/:id',
        name: 'AssessmentQuestionDetail',
        component: () => import('@/pages/assessment/question/QuestionDetail.vue'),
        meta: {
          title: {
            zh_CN: '题目详情',
            en_US: 'Question Detail',
          },
          hidden: true,
        },
      },
      {
        path: 'questionnaire',
        name: 'AssessmentQuestionnaire',
        component: () => import('@/pages/assessment/questionnaire/QuestionnaireList.vue'),
        meta: {
          title: {
            zh_CN: '问卷管理',
            en_US: 'Questionnaire Management',
          },
          icon: 'queue',
        },
      },
      {
        path: 'questionnaire/create',
        name: 'AssessmentQuestionnaireCreate',
        component: () => import('@/pages/assessment/questionnaire/QuestionnaireCreate.vue'),
        meta: {
          title: {
            zh_CN: '创建问卷',
            en_US: 'Create Questionnaire',
          },
          hidden: true,
        },
      },
      {
        path: 'questionnaire/detail/:id',
        name: 'AssessmentQuestionnaireDetail',
        component: () => import('@/pages/assessment/questionnaire/QuestionnaireDetail.vue'),
        meta: {
          title: {
            zh_CN: '问卷详情',
            en_US: 'Questionnaire Detail',
          },
          hidden: true,
        },
      },
      {
        path: 'questionnaire/management',
        name: 'AssessmentQuestionnaireManagement',
        component: () => import('@/pages/assessment/questionnaire/QuestionnaireManagement.vue'),
        meta: {
          title: {
            zh_CN: '问卷管理',
            en_US: 'Questionnaire Management',
          },
          hidden: true,
        },
      },
      {
        path: 'questionnaire/student',
        name: 'AssessmentQuestionnaireStudent',
        component: () => import('@/pages/assessment/questionnaire/StudentQuestionnaire.vue'),
        meta: {
          title: {
            zh_CN: '学生问卷',
            en_US: 'Student Questionnaire',
          },
          hidden: true,
        },
      },
      {
        path: 'class-content',
        name: 'ClassAssessmentList',
        component: () => import('@/pages/assessment/class-content/index.vue'),
        meta: {
          title: {
            zh_CN: '班级考核管理',
            en_US: 'Class Assessment Management',
          },
          icon: 'layers',
        },
      },
      {
        path: 'class-content/:classId',
        name: 'ClassAssessmentManagement',
        component: () => import('@/pages/assessment/class-content/management.vue'),
        meta: {
          title: {
            zh_CN: '班级考核内容管理',
            en_US: 'Class Assessment Content Management',
          },
          hidden: true,
        },
      },
      {
        path: 'score/management/:courseId/:assessmentId',
        name: 'AssessmentScoreManagement',
        component: () => import('@/pages/assessment/assessment-score/AssessmentScoreManagement.vue'),
        meta: {
          title: {
            zh_CN: '考核成绩管理',
            en_US: 'Assessment Score Management',
          },
          hidden: true,
        },
      },
    ],
  },

  // 教学任务管理模块
  {
    path: '/task',
    name: 'Task',
    component: Layout,
    redirect: '/task/worklist',
    meta: {
      title: {
        zh_CN: '教学任务管理',
        en_US: 'Teaching Task Management',
      },
      icon: 'assignment',
      orderNo: 5,
    },
    children: [
      {
        path: 'worklist',
        name: 'TaskWorklist',
        component: () => import('@/pages/task/worklist/WorklistManagement.vue'),
        meta: {
          title: {
            zh_CN: '教学任务',
            en_US: 'Teaching Worklist',
          },
          icon: 'calendar',
        },
      },
      {
        path: 'worklist/teacher',
        name: 'TaskWorklistTeacher',
        component: () => import('@/pages/task/worklist/TeacherTask.vue'),
        meta: {
          title: {
            zh_CN: '教师任务',
            en_US: 'Teacher Task',
          },
          hidden: true,
        },
      },
      {
        path: 'score',
        name: 'TaskScore',
        component: () => import('@/pages/task/score/ScoreManagement.vue'),
        meta: {
          title: {
            zh_CN: '成绩管理',
            en_US: 'Score Management',
          },
          icon: 'chart',
        },
      },
      {
        path: 'score/teacher',
        name: 'TaskScoreTeacher',
        component: () => import('@/pages/task/score/TeacherScoreManage.vue'),
        meta: {
          title: {
            zh_CN: '教师成绩管理',
            en_US: 'Teacher Score Management',
          },
          hidden: true,
        },
      },
      {
        path: ':courseId/semester/task',
        name: 'CourseSemesterTaskList',
        component: () => import('@/pages/task/TaskDetailList.vue'),
        meta: {
          title: {
            zh_CN: '教学任务详情',
            en_US: 'Course Semester Task Details',
          },
          hidden: true,
        },
      },
    ],
  },

  // 知识图谱管理模块
  {
    path: '/graph',
    name: 'Graph',
    component: Layout,
    redirect: '/graph/nodes',
    meta: {
      title: {
        zh_CN: '知识图谱管理',
        en_US: 'Knowledge Graph Management',
      },
      icon: 'chart-bubble',
      orderNo: 6,
    },
    children: [
      {
        path: 'nodes',
        name: 'GraphNodes',
        component: () => import('@/pages/graph/GraphAcademic.vue'),
        meta: {
          title: {
            zh_CN: '节点管理',
            en_US: 'Node Management',
          },
          icon: 'circle',
        },
      },
      {
        path: 'index',
        name: 'GraphIndex',
        component: () => import('@/pages/graph/GraphAcademic.vue'),
        meta: {
          title: {
            zh_CN: '图谱总览',
            en_US: 'Graph Overview',
          },
          icon: 'dashboard',
        },
      },
      {
        path: 'management',
        name: 'GraphManagement',
        component: () => import('@/pages/graph/GraphManagement.vue'),
        meta: {
          title: {
            zh_CN: '图谱管理',
            en_US: 'Graph Management',
          },
          icon: 'setting',
        },
      },
      {
        path: 'nodes/pro',
        name: 'GraphNodesPro',
        component: () => import('@/pages/graph/GraphAcademic.vue'),
        meta: {
          title: {
            zh_CN: '专业图谱',
            en_US: 'Professional Graph',
          },
          hidden: true,
        },
      },
      {
        path: 'academic',
        name: 'GraphAcademic',
        component: () => import('@/pages/graph/AcademicStyle.vue'),
        meta: {
          title: {
            zh_CN: '学风图谱',
            en_US: 'Academic Style Graph',
          },
          icon: 'chart-line',
        },
      },
      {
        path: 'links',
        name: 'GraphLinks',
        component: () => import('@/pages/graph/links/LinkManagement.vue'),
        meta: {
          title: {
            zh_CN: '关系管理',
            en_US: 'Link Management',
          },
          icon: 'link',
        },
      },
    ],
  },

  // 系统管理模块
  {
    path: '/system',
    name: 'System',
    component: Layout,
    redirect: '/system/home',
    meta: {
      title: {
        zh_CN: '系统管理',
        en_US: 'System Management',
      },
      icon: 'setting',
      orderNo: 7,
    },
    children: [
      {
        path: 'home',
        name: 'SystemHome',
        component: () => import('@/pages/system/home/<USER>'),
        meta: {
          title: {
            zh_CN: '系统首页',
            en_US: 'System Home',
          },
          icon: 'home',
        },
      },
      {
        path: 'home/config',
        name: 'SystemHomeConfig',
        component: () => import('@/pages/system/home/<USER>'),
        meta: {
          title: {
            zh_CN: '大屏配置',
            en_US: 'Dashboard Config',
          },
          hidden: true,
        },
      },
      {
        path: 'menu',
        name: 'SystemMenu',
        component: () => import('@/pages/system/MenuManagement.vue'),
        meta: {
          title: {
            zh_CN: '菜单管理',
            en_US: 'Menu Management',
          },
          icon: 'menu-unfold',
        },
      },
      {
        path: 'role',
        name: 'SystemRole',
        component: () => import('@/pages/system/RoleManagement.vue'),
        meta: {
          title: {
            zh_CN: '角色管理',
            en_US: 'Role Management',
          },
          icon: 'user-group',
        },
      },
      {
        path: 'user',
        name: 'SystemUser',
        component: () => import('@/pages/system/UserManagement.vue'),
        meta: {
          title: {
            zh_CN: '用户管理',
            en_US: 'User Management',
          },
          icon: 'user-avatar',
        },
      },
      {
        path: 'dict',
        name: 'SystemDict',
        component: () => import('@/pages/system/DictManagement.vue'),
        meta: {
          title: {
            zh_CN: '字典管理',
            en_US: 'Dictionary Management',
          },
          icon: 'bookmark',
        },
      },
      {
        path: 'document',
        name: 'SystemDocument',
        component: () => import('@/pages/system/document/DocumentManagement.vue'),
        meta: {
          title: {
            zh_CN: '文档管理',
            en_US: 'Document Management',
          },
          icon: 'folder-open',
        },
      },
      {
        path: 'document/review',
        name: 'SystemDocumentReview',
        component: () => import('@/pages/system/document/DocumentReview.vue'),
        meta: {
          title: {
            zh_CN: '文档审核',
            en_US: 'Document Review',
          },
          hidden: true,
        },
      },
      {
        path: 'document/directors',
        name: 'SystemDocumentDirectors',
        component: () => import('@/pages/system/document/DirectorsDocument.vue'),
        meta: {
          title: {
            zh_CN: '主管文档',
            en_US: 'Directors Document',
          },
          hidden: true,
        },
      },
      {
        path: 'document-final-review',
        name: 'SystemDocumentFinalReview',
        component: () => import('@/pages/system/DocumentFinalReview.vue'),
        meta: {
          title: {
            zh_CN: '文档终审',
            en_US: 'Document Final Review',
          },
          icon: 'file-check',
        },
      },
      {
        path: 'backup',
        name: 'SystemBackup',
        component: () => import('@/pages/system/BackupManagement.vue'),
        meta: {
          title: {
            zh_CN: '数据备份',
            en_US: 'Data Backup',
          },
          icon: 'backup',
        },
      },
      {
        path: 'monitor',
        name: 'SystemMonitor',
        component: () => import('@/pages/system/monitor/SystemMonitor.vue'),
        meta: {
          title: {
            zh_CN: '系统监控',
            en_US: 'System Monitor',
          },
          icon: 'chart',
        },
      },
      {
        path: 'profile',
        name: 'SystemProfile',
        component: () => import('@/pages/system/profile/ProfileManagement.vue'),
        meta: {
          title: {
            zh_CN: '个人资料',
            en_US: 'Profile Management',
          },
          icon: 'user-circle',
        },
      },
      {
        path: 'profile/demo',
        name: 'SystemProfileDemo',
        component: () => import('@/pages/system/profile/ProfileDemo.vue'),
        meta: {
          title: {
            zh_CN: '资料展示',
            en_US: 'Profile Demo',
          },
          hidden: true,
        },
      },
      {
        path: 'profile/student',
        name: 'SystemProfileStudent',
        component: () => import('@/pages/system/profile/StudentProfile.vue'),
        meta: {
          title: {
            zh_CN: '学生资料',
            en_US: 'Student Profile',
          },
          hidden: true,
        },
      },
      {
        path: 'notice',
        name: 'SystemNotice',
        component: () => import('@/pages/system/NoticeManagement.vue'),
        meta: {
          title: {
            zh_CN: '通知管理',
            en_US: 'Notice Management',
          },
          icon: 'notification',
        },
      },
      {
        path: 'log',
        name: 'SystemLog',
        component: () => import('@/pages/system/LogManagement.vue'),
        meta: {
          title: {
            zh_CN: '日志管理',
            en_US: 'Log Management',
          },
          icon: 'history',
        },
      },
      {
        path: 'excel',
        name: 'SystemExcel',
        component: () => import('@/pages/system/ExcelManagement.vue'),
        meta: {
          title: {
            zh_CN: 'Excel管理',
            en_US: 'Excel Management',
          },
          icon: 'file-excel',
        },
      },
    ],
  },

  // ETL数据管理模块
  {
    path: '/etl',
    name: 'ETL',
    component: Layout,
    redirect: '/etl/data',
    meta: {
      title: {
        zh_CN: '数据管理',
        en_US: 'Data Management',
      },
      icon: 'database',
      orderNo: 8,
    },
    children: [
      {
        path: 'data',
        name: 'ETLData',
        component: () => import('@/pages/etl/data/DataManagement.vue'),
        meta: {
          title: {
            zh_CN: '数据推送',
            en_US: 'Data Push',
          },
          icon: 'upload',
        },
      },
    ],
  },

  // 学生页面（保持原有结构，用于学生角色访问）
  {
    path: '/student',
    name: 'Student',
    component: () => import('@/layouts/header-only.vue'),
    redirect: '/student/home',
    meta: {
      title: {
        zh_CN: '学生页面',
        en_US: 'Student Pages',
      },
      hidden: true,
    },
    children: [
      {
        path: 'home',
        name: 'StudentHome',
        component: () => import('@/pages/base/student/StudentQuestionList.vue'),
        meta: {
          title: {
            zh_CN: '学生首页',
            en_US: 'Student Home',
          },
        },
      },
      {
        path: 'questionnaire/:id',
        name: 'StudentQuestionnaire',
        component: () => import('@/pages/base/student/StudentQuestionSuccess.vue'),
        meta: {
          title: {
            zh_CN: '问卷调查',
            en_US: 'Student Questionnaire',
          },
          hidden: true,
        },
      },
    ],
  },

  // 教师课程管理（动态侧边栏布局）
  {
    path: '/teacher/course/:courseId',
    name: 'TeacherCourse',
    component: () => import('@/layouts/dynamic-sidebar.vue'),
    redirect: '/teacher/course/:courseId/homework',
    meta: {
      title: {
        zh_CN: '课程管理',
        en_US: 'Course Management',
      },
      icon: 'book',
      hidden: true,
    },
    children: [
      {
        path: 'homework',
        name: 'TeacherCourseHomework',
        component: () => import('@/pages/base/teacher/TeacherNewTask.vue'),
        meta: {
          title: {
            zh_CN: '作业管理',
            en_US: 'Homework Management',
          },
          icon: 'edit',
        },
      },
      {
        path: 'exam',
        name: 'TeacherCourseExam',
        component: () => import('@/pages/base/teacher/TeacherPaperTask.vue'),
        meta: {
          title: {
            zh_CN: '考试管理',
            en_US: 'Exam Management',
          },
          icon: 'file-paste',
        },
      },
      {
        path: 'grade',
        name: 'TeacherCourseGrade',
        component: () => import('@/pages/base/teacher/TeacherGradeSubmission.vue'),
        meta: {
          title: {
            zh_CN: '成绩管理',
            en_US: 'Grade Management',
          },
          icon: 'chart',
        },
      },
      {
        path: 'document',
        name: 'TeacherCourseDocument',
        component: () => import('@/pages/base/teacher/TeacherDocumentSubmit.vue'),
        meta: {
          title: {
            zh_CN: '文档提交',
            en_US: 'Document Submit',
          },
          icon: 'upload',
        },
      },
    ],
  },

];


// 固定路由模块转换为路由
export function mapModuleRouterList(modules: Record<string, unknown>): Array<ObeRouteRecord> {
  const routerList: Array<ObeRouteRecord> = [];
  Object.keys(modules).forEach((key) => {
    // @ts-ignore
    const mod = modules[key].default || {};
    const modList = Array.isArray(mod) ? [...mod] : [mod];
    routerList.push(...modList);
  });
  return routerList;
}

function fatteningRoutes(routes: ObeRouteRecord[]): ObeRouteRecord[] {
  return routes.flatMap((route: ObeRouteRecord) => {
    return route.children ? fatteningRoutes(route.children) : route
  })
}

export const getActive = (): string => {
  // 非组件内调用必须通过Router实例获取当前路由
  const route = router.currentRoute.value;

  if (!route.path) {
    return '';
  }

  // 检查meta中是否有指定的activeMenu
  if (route.meta && route.meta.activeMenu) {
    return route.meta.activeMenu as string;
  }

  // 简化激活逻辑，直接返回当前路径
  return route.path;
};

const router = createRouter({
  history: isHashRouterMode
    ? createWebHashHistory(publicPath)
    : createWebHistory(publicPath),
  routes: constantRoutes as RouteRecordRaw[],
  scrollBehavior() {
    return {
      el: '#app',
      top: 0,
      behavior: 'smooth',
    };
  },
});

function addRouter(routes: ObeRouteRecord[]) {
  routes.forEach((route: ObeRouteRecord) => {
    if (!router.hasRoute(route.name)) {
      router.addRoute(route as RouteRecordRaw);
    }
    if (route.children) {
      addRouter(route.children);
    }
  });
}

export function resetRouter(routes: ObeRouteRecord[] = constantRoutes) {
  routes.map((route: ObeRouteRecord) => {
    if (route.children) route.children = fatteningRoutes(route.children)
  })
  router.getRoutes().forEach(({ name }) => {
    router.hasRoute(<RouteRecordName> name) &&
    router.removeRoute(<RouteRecordName> name)
  })

  // 添加常量路由
  addRouter(constantRoutes);

  // 添加动态路由
  if (routes !== constantRoutes) {
    addRouter(routes);
  }
}

export function setupRouter(app: any) {
  if (authentication === 'intelligence') {
    console.log('🔄 添加异步路由');
    addRouter(asyncRoutes);
    console.log('✅ 异步路由添加完成，总路由数:', router.getRoutes().length);
    console.log('📋 所有可用路由:', router.getRoutes().map(route => ({ name: route.name, path: route.path })));
  }
  setupPermissions(router)
  app.use(router)
  return router
}

export default router;








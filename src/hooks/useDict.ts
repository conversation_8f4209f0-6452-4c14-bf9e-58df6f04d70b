import { ref, computed, onMounted } from 'vue'
import {
  getDictData,
  getDictOptions,
  getDictLabel,
  getDictValue,
  getDictDataByTypeTitle,
  getDictOptionsByTypeTitle,
  getDictLabelByTypeTitle,
  getDictTypes,
  getGlobalDictState,
  initializeGlobalDict,
  clearDictCache
} from '@/utils/dictUtil'
import type { DictData, DictType } from '@/utils/dictUtil'

/**
 * 字典工具Composable
 * 提供响应式的字典数据访问和状态管理
 */
export function useDict() {
  // 响应式状态
  const dictData = ref<DictData[]>([])
  const dictOptions = ref<{label: string, value: string | number}[]>([])
  const loading = ref(false)
  const error = ref<string | null>(null)
  const globalState = ref(getGlobalDictState())

  /**
   * 加载指定类型的字典数据
   * @param typeId 字典类型ID
   * @param forceRefresh 是否强制刷新
   */
  const loadDictData = async (typeId: number, forceRefresh = false) => {
    loading.value = true
    error.value = null
    
    try {
      dictData.value = await getDictData(typeId, forceRefresh)
    } catch (err) {
      error.value = err instanceof Error ? err.message : '加载字典数据失败'
      console.error('加载字典数据失败:', err)
    } finally {
      loading.value = false
    }
  }

  /**
   * 加载指定类型的字典选项
   * @param typeId 字典类型ID
   * @param forceRefresh 是否强制刷新
   */
  const loadDictOptions = async (typeId: number, forceRefresh = false) => {
    loading.value = true
    error.value = null
    
    try {
      // 先加载字典数据，然后转换为选项
      const data = await getDictData(typeId, forceRefresh)
      dictOptions.value = data
        .filter(item => item.status === 0)
        .sort((a, b) => a.sort - b.sort)
        .map(item => ({
          label: item.label,
          value: item.value
        }))
    } catch (err) {
      error.value = err instanceof Error ? err.message : '加载字典选项失败'
      console.error('加载字典选项失败:', err)
    } finally {
      loading.value = false
    }
  }

  /**
   * 根据字典类型标题加载字典数据
   * @param typeTitle 字典类型标题
   * @param forceRefresh 是否强制刷新
   */
  const loadDictDataByTypeTitle = async (typeTitle: string, forceRefresh = false) => {
    loading.value = true
    error.value = null
    
    try {
      dictData.value = await getDictDataByTypeTitle(typeTitle)
    } catch (err) {
      error.value = err instanceof Error ? err.message : '加载字典数据失败'
      console.error('加载字典数据失败:', err)
    } finally {
      loading.value = false
    }
  }

  /**
   * 根据字典类型标题加载字典选项
   * @param typeTitle 字典类型标题
   * @param forceRefresh 是否强制刷新
   */
  const loadDictOptionsByTypeTitle = async (typeTitle: string, forceRefresh = false) => {
    loading.value = true
    error.value = null
    
    try {
      // 先加载字典数据，然后转换为选项
      const data = await getDictDataByTypeTitle(typeTitle)
      dictOptions.value = data
        .filter(item => item.status === 0)
        .sort((a, b) => a.sort - b.sort)
        .map(item => ({
          label: item.label,
          value: item.value
        }))
    } catch (err) {
      error.value = err instanceof Error ? err.message : '加载字典选项失败'
      console.error('加载字典选项失败:', err)
    } finally {
      loading.value = false
    }
  }

  /**
   * 获取字典标签
   * @param typeId 字典类型ID
   * @param value 字典值
   * @returns Promise<string>
   */
  const getLabel = async (typeId: number, value: string | number): Promise<string> => {
    return await getDictLabel(typeId, value)
  }

  /**
   * 根据字典类型标题获取字典标签
   * @param typeTitle 字典类型标题
   * @param value 字典值
   * @returns Promise<string>
   */
  const getLabelByTypeTitle = async (typeTitle: string, value: string | number): Promise<string> => {
    return await getDictLabelByTypeTitle(typeTitle, value)
  }

  /**
   * 获取字典值
   * @param typeId 字典类型ID
   * @param label 字典标签
   * @returns Promise<string | number | undefined>
   */
  const getValue = async (typeId: number, label: string): Promise<string | number | undefined> => {
    return await getDictValue(typeId, label)
  }

  /**
   * 刷新全局字典状态
   */
  const refreshGlobalState = () => {
    globalState.value = getGlobalDictState()
  }

  /**
   * 初始化全局字典数据
   * @param forceRefresh 是否强制刷新
   */
  const initGlobalDict = async (forceRefresh = false) => {
    loading.value = true
    error.value = null
    
    try {
      await initializeGlobalDict(forceRefresh)
      refreshGlobalState()
    } catch (err) {
      error.value = err instanceof Error ? err.message : '初始化字典数据失败'
      console.error('初始化字典数据失败:', err)
    } finally {
      loading.value = false
    }
  }

  /**
   * 清除缓存
   * @param typeId 字典类型ID，不传则清除所有缓存
   */
  const clearCache = (typeId?: number) => {
    clearDictCache(typeId)
    refreshGlobalState()
  }

  // 计算属性
  const isInitialized = computed(() => globalState.value.initialized)
  const isInitializing = computed(() => globalState.value.initializing)
  const dictTypes = computed(() => globalState.value.dictTypes)
  const hasError = computed(() => error.value !== null)

  return {
    // 响应式数据
    dictData,
    dictOptions,
    loading,
    error,
    globalState,
    
    // 计算属性
    isInitialized,
    isInitializing,
    dictTypes,
    hasError,
    
    // 方法
    loadDictData,
    loadDictOptions,
    loadDictDataByTypeTitle,
    loadDictOptionsByTypeTitle,
    getLabel,
    getLabelByTypeTitle,
    getValue,
    refreshGlobalState,
    initGlobalDict,
    clearCache
  }
}

/**
 * 特定字典类型的Composable
 * @param typeId 字典类型ID
 * @returns 字典相关的响应式数据和方法
 */
export function useDictByType(typeId: number) {
  const {
    dictData,
    dictOptions,
    loading,
    error,
    loadDictData,
    loadDictOptions,
    getLabel,
    getValue
  } = useDict()

  // 组件挂载时自动加载数据
  onMounted(async () => {
    try {
      // 只加载选项数据，因为选项数据包含了所有必要的信息
      await loadDictOptions(typeId)
    } catch (err) {
      console.error(`加载字典类型ID ${typeId} 失败:`, err)
    }
  })

  return {
    dictData,
    dictOptions,
    loading,
    error,
    loadDictData: () => loadDictData(typeId),
    loadDictOptions: () => loadDictOptions(typeId),
    getLabel: (value: string | number) => getLabel(typeId, value),
    getValue: (label: string) => getValue(typeId, label)
  }
}

/**
 * 根据字典类型标题使用的Composable
 * @param typeTitle 字典类型标题
 * @returns 字典相关的响应式数据和方法
 */
export function useDictByTypeTitle(typeTitle: string) {
  const {
    dictData,
    dictOptions,
    loading,
    error,
    loadDictDataByTypeTitle,
    loadDictOptionsByTypeTitle,
    getLabelByTypeTitle
  } = useDict()

  // 组件挂载时自动加载数据
  onMounted(async () => {
    try {
      // 只加载选项数据，因为选项数据包含了所有必要的信息
      await loadDictOptionsByTypeTitle(typeTitle)
    } catch (err) {
      console.error(`加载字典类型 ${typeTitle} 失败:`, err)
    }
  })

  return {
    dictData,
    dictOptions,
    loading,
    error,
    loadDictData: () => loadDictDataByTypeTitle(typeTitle),
    loadDictOptions: () => loadDictOptionsByTypeTitle(typeTitle),
    getLabel: (value: string | number) => getLabelByTypeTitle(typeTitle, value)
  }
} 
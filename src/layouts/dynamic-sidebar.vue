<template>
  <base-layout
    layout-mode="dynamicSidebar"
    :show-sidebar="true"
    :show-back-button="true"
    :on-back-to-dashboard="handleBackToDashboard"
  >
    <!-- Header组件 -->
    <template #header>
      <layout-header />
    </template>
    
    <!-- 动态侧边栏组件 -->
    <template #sidebar>
      <enhanced-side-nav
        v-if="settingStore.showSidebar"
        :show-logo="settingStore.showSidebarLogo"
        :layout="settingStore.layout"
        :is-fixed="settingStore.isSidebarFixed"
        :menu="dynamicMenu"
        :theme="settingStore.displaySideMode"
        :is-compact="settingStore.isSidebarCompact"
        :dynamic-active="activeMenu"
      />
    </template>
    
    <!-- 内容区域 -->
    <template #content>
      <layout-content />
    </template>
    
    <!-- Footer组件 -->
    <template #footer>
      <enhanced-footer />
    </template>
    
    <!-- 设置面板 -->
    <template #setting>
      <setting-panel />
    </template>
  </base-layout>
</template>

<script setup lang="ts">
import { onMounted, readonly } from 'vue'
import { useSettingStore, useTabsRouterStore } from '@/store'
import BaseLayout from './components/BaseLayout.vue'
import LayoutHeader from './components/LayoutHeader.vue'
import LayoutContent from './components/LayoutContent.vue'
import EnhancedSideNav from './components/EnhancedSideNav.vue'
import EnhancedFooter from './components/EnhancedFooter.vue'
import SettingPanel from './setting.vue'
import { useDynamicSidebarLogic } from './composables/useDynamicSidebarLogic'
import { useLayoutLogic } from './composables/useLayoutLogic'

// 使用共同的布局逻辑
const { appendNewRoute, watchRouteChange } = useLayoutLogic()

// 使用动态侧边栏特殊逻辑
const { 
  parentRoute, 
  dynamicMenu, 
  activeMenu,
  handleBackToDashboard,
  initDynamicSidebar,
  watchDynamicRouteChange 
} = useDynamicSidebarLogic()

// 设置相关
const settingStore = useSettingStore()

// 暴露方法给子组件使用
defineExpose({
  parentRoute: readonly(parentRoute)
})

onMounted(() => {
  initDynamicSidebar()
  appendNewRoute()
  watchRouteChange()
  watchDynamicRouteChange()
})
</script>

<style lang="less" scoped>
.dynamic-sidebar-layout {
  height: 100vh;
  overflow: hidden;
  background: linear-gradient(135deg, #f5f7fa 0%, #ffffff 100%);
  position: relative;
  
  // 添加与Layout相同的背景装饰
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
      radial-gradient(circle at 20% 80%, rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.02) 0%, transparent 60%),
      radial-gradient(circle at 80% 20%, rgba(var(--td-brand-color-8-rgb, 5, 148, 250), 0.015) 0%, transparent 60%),
      radial-gradient(circle at 40% 40%, rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.008) 0%, transparent 60%);
    pointer-events: none;
    z-index: 0;
  }
  
  // 顶部融合渐变
  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 80px;
    background: linear-gradient(180deg, 
      rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.01) 0%, 
      transparent 100%);
    pointer-events: none;
    z-index: 0;
  }
}

.enhanced-layout-header-fullwidth {
  background: transparent;
  border-bottom: none;
  position: sticky;
  top: 0;
  z-index: 100;
  width: 100%;
  
  :deep(.t-layout__header) {
    height: 64px;
    line-height: 64px;
    padding: 0;
    background: transparent;
    border-bottom: none;
    width: 100%;
  }
}

.enhanced-layout-main-content {
  height: calc(100vh - 64px - 60px); // 固定计算：Header(64px) + Footer(60px)
  overflow: hidden;
  background: transparent;
  position: relative;
  z-index: 1;
  
  :deep(.t-layout__content) {
    padding: 0;
    width: 100%;
    height: 100%;
    background: transparent;
  }
}

.content-with-sidebar {
  display: flex;
  height: 100%;
  width: 100%;
}

.enhanced-layout-content-sidebar {
  width: 240px;
  height: 100%;
  background: linear-gradient(135deg, 
    rgba(248, 250, 252, 0.6) 0%,
    rgba(241, 245, 249, 0.4) 50%,
    rgba(248, 250, 252, 0.3) 100%);
  backdrop-filter: blur(25px) saturate(1.1);
  overflow: visible;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  border-radius: 0 16px 16px 0;
  margin: 8px 0 8px 8px;
  flex-shrink: 0;
  
  // 折叠状态样式
  &.sidebar-collapsed {
    width: 72px; // 64px + 8px margin
  }
  
  // 现代化装饰效果
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
      radial-gradient(ellipse at top left, rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.05) 0%, transparent 50%),
      radial-gradient(ellipse at bottom right, rgba(var(--td-brand-color-8-rgb, 5, 148, 250), 0.03) 0%, transparent 50%);
    border-radius: 0 16px 16px 0;
    pointer-events: none;
    z-index: 0;
  }
  
  &::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 1px;
    height: 100%;
    background: linear-gradient(180deg,
      transparent 0%,
      rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.12) 20%,
      rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.18) 50%,
      rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.12) 80%,
      transparent 100%);
    border-radius: 0 16px 16px 0;
    box-shadow: 1px 0 8px rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.08);
    z-index: 2;
  }
}

// 返回工作台区域样式
.back-to-dashboard {
  position: relative;
  z-index: 3;
  padding: 16px 12px;
  margin-bottom: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  border-bottom: 1px solid rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.1);
  
  .back-content {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 16px;
    border-radius: 8px;
    background: linear-gradient(135deg, 
      rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.08) 0%,
      rgba(var(--td-brand-color-8-rgb, 5, 148, 250), 0.05) 100%);
    backdrop-filter: blur(8px);
    border: 1px solid rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.12);
    transition: all 0.3s ease;
    
    .back-icon {
      color: var(--td-brand-color);
      font-size: 16px;
      transition: all 0.3s ease;
    }
    
    .back-text {
      color: var(--td-brand-color);
      font-size: 14px;
      font-weight: 500;
      white-space: nowrap;
      transition: all 0.3s ease;
    }
  }
  
  &:hover {
    .back-content {
      background: linear-gradient(135deg, 
        rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.12) 0%,
        rgba(var(--td-brand-color-8-rgb, 5, 148, 250), 0.08) 100%);
      border-color: rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.2);
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.15);
      
      .back-icon {
        color: var(--td-brand-color-8);
        transform: translateX(-2px);
      }
      
      .back-text {
        color: var(--td-brand-color-8);
      }
    }
  }
  
  &:active {
    .back-content {
      transform: translateY(0);
      box-shadow: 0 2px 6px rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.12);
    }
  }
}

.enhanced-layout-content-area {
  flex: 1;
  height: 100%;
  overflow-y: auto;
  background: transparent;
  position: relative;
  
  // 优化内层TDesign Layout组件的滚动，避免双滚动条但保持内容可滚动
  :deep(.t-layout) {
    height: 100%;
    overflow: visible; // 允许内容正常滚动
  }
  
  :deep(.t-content) {
    overflow: visible;
    height: auto;
    max-height: none; // 确保内容可以正常展开
  }
  
  // 只禁用可能重复的滚动条样式，但保持滚动功能
  :deep(.tdesign-starter-content-layout) {
    overflow: visible;
    height: auto;
    
    // 隐藏可能的重复滚动条样式
    &::-webkit-scrollbar {
      display: none;
    }
    scrollbar-width: none; // Firefox
    -ms-overflow-style: none; // IE
  }
  
  // 现代化滚动条样式
  &::-webkit-scrollbar {
    width: 8px;
  }
  
  &::-webkit-scrollbar-track {
    background: rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.05);
    border-radius: 4px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, 
      rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.3) 0%, 
      rgba(var(--td-brand-color-8-rgb, 5, 148, 250), 0.2) 100%);
    border-radius: 4px;
    border: 2px solid transparent;
    background-clip: content-box;
    
    &:hover {
      background: linear-gradient(135deg, 
        rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.5) 0%, 
        rgba(var(--td-brand-color-8-rgb, 5, 148, 250), 0.4) 100%);
    }
  }
}

.enhanced-layout-footer {
  height: 60px;
  width: 100vw;
  background: transparent;
  border-top: none;
  position: relative;
  z-index: 1000;
  
  :deep(.t-layout__footer) {
    height: 60px;
    line-height: 60px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    background: transparent;
    width: 100vw;
    position: relative;
    overflow: visible;
  }
  
  // 底部融合渐变
  .bottom-gradient {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 80px;
    background: linear-gradient(0deg, 
      rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.01) 0%, 
      transparent 100%);
    pointer-events: none;
    z-index: 0;
  }
}

// 响应式适配
@media (max-width: 768px) {
  .dynamic-sidebar-layout {
    .enhanced-layout-header-fullwidth {
      :deep(.t-layout__header) {
        height: 56px;
        line-height: 56px;
      }
    }
    
    .enhanced-layout-main-content {
      height: calc(100vh - 56px - 50px); // 移动端固定高度计算：Header(56px) + Footer(50px)
    }
    
    .enhanced-layout-footer {
      height: 50px;
      
      :deep(.t-layout__footer) {
        height: 50px;
        line-height: 50px;
      }
    }
    
    .enhanced-layout-content-sidebar {
      width: 200px;
      margin: 4px 0 4px 4px;
      border-radius: 0 12px 12px 0;
      
      &.sidebar-collapsed {
        width: 60px; // 56px + 4px margin
      }
    }
    
    // 移动端返回按钮优化
    .back-to-dashboard {
      padding: 12px 8px;
      margin-bottom: 6px;
      
      .back-content {
        padding: 10px 12px;
        
        .back-text {
          font-size: 12px;
        }
      }
    }
  }
}

// 暗色主题适配
@media (prefers-color-scheme: dark) {
  .dynamic-sidebar-layout {
    background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
    
    &::before {
      background: 
        radial-gradient(circle at 20% 80%, rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.015) 0%, transparent 60%),
        radial-gradient(circle at 80% 20%, rgba(var(--td-brand-color-8-rgb, 5, 148, 250), 0.01) 0%, transparent 60%),
        radial-gradient(circle at 40% 40%, rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.005) 0%, transparent 60%);
    }
  }
  
  .enhanced-layout-content-sidebar {
    background: linear-gradient(135deg, 
      rgba(30, 41, 59, 0.6) 0%,
      rgba(15, 23, 42, 0.4) 50%,
      rgba(30, 41, 59, 0.3) 100%);
      
    &::before {
      background: 
        radial-gradient(ellipse at top left, rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.03) 0%, transparent 50%),
        radial-gradient(ellipse at bottom right, rgba(var(--td-brand-color-8-rgb, 5, 148, 250), 0.02) 0%, transparent 50%);
    }
  }
  
  // 暗色主题下的返回按钮
  .back-to-dashboard {
    border-bottom-color: rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.15);
    
    .back-content {
      background: linear-gradient(135deg, 
        rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.12) 0%,
        rgba(var(--td-brand-color-8-rgb, 5, 148, 250), 0.08) 100%);
      border-color: rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.2);
    }
    
    &:hover .back-content {
      background: linear-gradient(135deg, 
        rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.18) 0%,
        rgba(var(--td-brand-color-8-rgb, 5, 148, 250), 0.12) 100%);
      border-color: rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.3);
    }
  }
  
  .bottom-gradient {
    background: linear-gradient(0deg, 
      rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.02) 0%, 
      transparent 100%);
  }
}

// 底部折叠按钮样式
:deep(.enhanced-bottom-collapse-container) {
  
  .enhanced-bottom-collapse-button {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, 
      rgba(255, 255, 255, 0.95) 0%,
      rgba(248, 250, 252, 0.9) 100%);
    backdrop-filter: blur(12px) saturate(1.1);
    border: 1px solid rgba(var(--td-border-level-1-color), 0.3);
    border-radius: 12px;
    box-shadow: 
      0 4px 12px rgba(0, 0, 0, 0.08),
      0 2px 6px rgba(0, 0, 0, 0.04);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    
    &:hover {
      background: linear-gradient(135deg, 
        rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.05) 0%,
        rgba(var(--td-brand-color-8-rgb, 5, 148, 250), 0.03) 100%);
      border-color: rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.2);
      box-shadow: 
        0 6px 16px rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.12),
        0 3px 8px rgba(0, 0, 0, 0.06);
    
      
      :deep(.t-icon) {
        color: var(--td-brand-color);
      }
    }
    
    &:active {
      transform: translateX(-50%) translateY(0px);
      box-shadow: 
        0 2px 8px rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.08),
        0 1px 4px rgba(0, 0, 0, 0.04);
    }
    
    :deep(.t-icon) {
      color: var(--td-text-color-secondary);
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }
  }
}
</style> 
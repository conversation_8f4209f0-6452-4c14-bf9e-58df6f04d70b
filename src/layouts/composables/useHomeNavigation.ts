import { computed } from 'vue'
import { useUserStore } from '@/store'
import { getHomeRouteByRole, getRoleDescription, DEFAULT_HOME_ROUTE } from '@/config/navigation.config'

/**
 * 智能首页导航逻辑
 * 根据用户角色返回对应的首页路径
 */
export function useHomeNavigation() {
  const userStore = useUserStore()
  
  // 根据用户角色计算首页路径
  const homeRoute = computed(() => {
    try {
      const userInfo = userStore.userInfo
      const userType = userInfo?.type
      
      // 使用配置文件获取角色对应的首页路径
      const homePath = getHomeRouteByRole(userType)
      
      // 调试日志
      if (process.env.NODE_ENV === 'development') {
        console.log(`[Navigation] 用户角色: ${getRoleDescription(userType)} (${userType}), 首页路径: ${homePath}`)
      }
      
      return homePath
    } catch (error) {
      console.warn('获取用户角色失败，使用默认首页:', error)
      return DEFAULT_HOME_ROUTE
    }
  })
  
  /**
   * 跳转到智能首页
   */
  const goToHome = () => {
    return homeRoute.value
  }
  
  return {
    homeRoute,
    goToHome
  }
} 
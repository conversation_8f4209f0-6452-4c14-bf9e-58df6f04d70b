import { computed, onMounted, watch } from 'vue'
import { useRoute } from 'vue-router'
import { prefix } from '@/config/global'
import { useSettingStore, useTabsRouterStore } from '@/store'

export function useLayoutLogic() {
  const route = useRoute()
  const settingStore = useSettingStore()
  const tabsRouterStore = useTabsRouterStore()

  // 动态计算主内容区域高度
  const mainContentHeight = computed(() => {
    const headerHeight = 64;
    const footerHeight = settingStore.showFooter ? 60 : 0;
    return `calc(100vh - ${headerHeight}px - ${footerHeight}px)`;
  });

  // 移动端动态计算主内容区域高度
  const mobileMainContentHeight = computed(() => {
    const headerHeight = 56; // 移动端Header高度
    const footerHeight = settingStore.showFooter ? 50 : 0; // 移动端Footer高度
    return `calc(100vh - ${headerHeight}px - ${footerHeight}px)`;
  });

  // 添加新路由到标签页
  const appendNewRoute = () => {
    const {
      path,
      query,
      meta: { title },
      name,
    } = route;
    tabsRouterStore.appendTabRouterList({ 
      path, 
      query, 
      title: title as string, 
      name, 
      isAlive: true, 
      meta: route.meta 
    });
  };

  // 监听路由变化
  const watchRouteChange = () => {
    watch(
      () => route.path,
      () => {
        appendNewRoute();
        document.querySelector(`.${prefix}-layout`)?.scrollTo({ top: 0, behavior: 'smooth' });
      },
    );
  };

  // 初始化
  const initLayout = () => {
    appendNewRoute();
  };

  return {
    mainContentHeight,
    mobileMainContentHeight,
    appendNewRoute,
    watchRouteChange,
    initLayout,
    settingStore
  }
} 
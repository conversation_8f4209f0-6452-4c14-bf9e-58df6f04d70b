<template>
  <div :class="sideNavCls" class="enhanced-side-nav-container">
    <div class="enhanced-side-nav-wrapper">
      <t-menu
        :class="menuCls"
        :theme="theme"
        :value="active"
        :collapsed="collapsed"
        :expanded="expanded"
        :expand-mutex="menuAutoCollapsed"
        @expand="onExpanded"
        class="enhanced-menu-component"
      >
        <!-- <template #logo>
          <div v-if="showLogo" :class="`${prefix}-enhanced-side-nav-logo-wrapper`" @click="goHome">
            <div class="enhanced-logo-container">
              <component :is="getLogo()" :class="logoCls" class="enhanced-logo-icon" />
              <div v-if="!collapsed" class="enhanced-logo-text">
                <span class="brand-text">OBE系统</span>
              </div>
            </div>
          </div>
        </template> -->
        
        <enhanced-menu-content :nav-data="menu" />
      </t-menu>
      
      <!-- 底部折叠按钮 -->
      <div class="enhanced-bottom-collapse-container">
        <t-tooltip 
          :content="collapsed ? '展开菜单' : '折叠菜单'" 
          placement="right"
          theme="default"
          :show-arrow="true"
        >
          <t-button
            theme="default"
            variant="text"
            shape="square"
            size="small"
            @click="toggleCollapse"
            class="enhanced-bottom-collapse-button"
          >
            <template #icon>
              <t-icon :name="collapsed ? 'format-vertical-align-right' : 'format-vertical-align-left'" />
            </template>
          </t-button>
        </t-tooltip>
      </div>
    </div>
    
    <div :class="`${prefix}-enhanced-side-nav-placeholder${collapsed ? '-hidden' : ''}`"></div>
  </div>
</template>

<script setup lang="ts">
import { difference, remove, union } from 'lodash';
import { MenuValue } from 'tdesign-vue-next';
import type { PropType } from 'vue';
import { computed, onMounted, ref, watch, nextTick } from 'vue';
import { useRouter } from 'vue-router';

import AssetLogoFull from '@/assets/assets-logo-full.svg?component';
import AssetLogo from '@/assets/assets-t-logo.svg?component';
import { prefix } from '@/config/global';
import { getActive } from '@/router';
import { useSettingStore } from '@/store';
import type { MenuRoute, ModeType } from '@/types/interface';

import pgk from '../../../package.json';
import EnhancedMenuContent from './MenuContent.vue';
import { useHomeNavigation } from '../composables/useHomeNavigation';

const MIN_POINT = 992 - 1;

const { menu, showLogo, isFixed, layout, theme, isCompact, dynamicActive } = defineProps({
  menu: {
    type: Array as PropType<MenuRoute[]>,
    default: (): MenuRoute[] => [],
  },
  showLogo: {
    type: Boolean as PropType<boolean>,
    default: true,
  },
  isFixed: {
    type: Boolean as PropType<boolean>,
    default: true,
  },
  layout: {
    type: String as PropType<string>,
    default: '',
  },
  headerHeight: {
    type: String as PropType<string>,
    default: '64px',
  },
  theme: {
    type: String as PropType<ModeType>,
    default: 'light',
  },
  isCompact: {
    type: Boolean as PropType<boolean>,
    default: false,
  },
  dynamicActive: {
    type: String as PropType<string>,
    default: '',
  },
});

const settingStore = useSettingStore();
const collapsed = computed(() => settingStore.isSidebarCompact);
const menuAutoCollapsed = computed(() => settingStore.menuAutoCollapsed);

// 响应全局主题状态变化
const globalThemeMode = computed(() => settingStore.displayMode);

// 计算激活状态：优先使用动态传入的激活状态，其次使用getActive()
const active = computed(() => {
  try {
    // 获取当前路由路径
    const currentPath = router.currentRoute.value.path;
    
    // 如果有动态传入的激活状态，优先使用
    if (dynamicActive) {
     // console.log('🎯 使用动态传入的激活状态:', dynamicActive);
      return dynamicActive;
    }
    
    // 其次使用getActive()获取激活状态
    const defaultActive = getActive();
   // console.log('🎯 使用默认激活状态:', defaultActive, '当前路由:', currentPath);
    
    // 查找菜单中匹配当前路径的项
    const findMatchingMenuItem = (items: MenuRoute[]): string | null => {
      if (!items || !Array.isArray(items)) return null;
      
      for (const item of items) {
        if (!item || typeof item !== 'object') continue;
        
        // 检查路径是否匹配
        if (item.path === currentPath) {
          return item.path;
        }
        
        // 检查是否为当前路径的父路径
        if (currentPath.startsWith(item.path + '/')) {
          return item.path;
        }
        
        // 递归检查子菜单
        if (item.children && Array.isArray(item.children) && item.children.length > 0) {
          const found = findMatchingMenuItem(item.children);
          if (found) return found;
        }
      }
      return null;
    };
    
    // 尝试在菜单中找到匹配项
    if (menu && Array.isArray(menu)) {
      const matchedPath = findMatchingMenuItem(menu);
      if (matchedPath) {
      //  console.log('✅ 在菜单中找到匹配项:', matchedPath);
        return matchedPath;
      }
    }
    
    // 如果没有找到匹配项，返回默认激活状态
    return defaultActive;
  } catch (error) {
    console.error('计算激活菜单时出错:', error);
    return '';
  }
});

// 存储展开的菜单项
const expanded = ref<MenuValue[]>([]);

// 添加一个函数来手动设置菜单激活状态
const setActiveMenuItem = (activePath: string) => {
  // 确保DOM已加载完成
  nextTick(() => {
    setTimeout(() => {
      try {
        // 查找菜单中的所有项，手动添加激活类
        const menuItems = document.querySelectorAll('.t-menu__item');
       // console.log('🔍 查找菜单项:', menuItems.length, '个项目, 当前激活路径:', activePath);
        
        menuItems.forEach((item) => {
          const itemPath = item.getAttribute('name');
         // console.log('检查菜单项:', itemPath, '是否匹配激活路径:', activePath);
          
          if (itemPath && itemPath.includes(activePath)) {
            //console.log('✅ 找到匹配项，添加激活类:', itemPath);
            item.classList.add('t-is-active');
          } else {
            item.classList.remove('t-is-active');
          }
        });
      } catch (error) {
        console.error('设置菜单激活状态时出错:', error);
      }
    }, 200); // 添加短暂延迟确保DOM已完全渲染
  });
};

// 监听路由变化，确保当前菜单项处于激活状态
watch(
  () => active.value,
  (newActive) => {
    if (!newActive) return;
    const path = String(newActive);
    // 找到当前路径的父路径，用于展开父菜单
    const parentPath = path.substring(0, path.lastIndexOf('/'));
    if (parentPath) {
      // 如果是菜单自动折叠模式，只保留当前激活的父菜单
      if (menuAutoCollapsed.value) {
        expanded.value = [parentPath];
      } else {
        // 否则添加到已展开的菜单中（不使用lodash）
        if (!expanded.value.includes(parentPath)) {
          expanded.value = [...expanded.value, parentPath];
        }
      }
    }
    
    // 手动设置菜单激活状态
    setActiveMenuItem(path);
  },
  { immediate: true } // 确保组件挂载时就执行一次
);

// 处理菜单展开/折叠
const onExpanded = (value: MenuValue[]) => {
  // 不使用lodash函数，直接使用数组方法
  if (menuAutoCollapsed.value) {
    // 自动折叠模式：直接使用新值
    expanded.value = value;
  } else {
    // 手动模式：计算展开和折叠的菜单项
    const expandedSet = new Set(expanded.value);
    const valueSet = new Set(value);
    
    // 找出被折叠的菜单项（在expanded中但不在value中）
    expanded.value.forEach(item => {
      if (!valueSet.has(item)) {
        expandedSet.delete(item);
      }
    });
    
    // 添加新展开的菜单项（在value中但不在expanded中）
    value.forEach(item => {
      expandedSet.add(item);
    });
    
    // 更新展开状态
    expanded.value = Array.from(expandedSet);
  }
};

// 修复主题响应逻辑：优先使用全局主题状态，其次使用组件传入的theme属性
const sideMode = computed(() => {
  // 如果全局主题是暗黑模式，则使用暗黑模式
  if (globalThemeMode.value === 'dark') {
    return true;
  }
  // 否则使用组件传入的theme属性
  return theme === 'dark';
});

const sideNavCls = computed(() => [
  `${prefix}-enhanced-sidebar-layout`,
  {
    [`${prefix}-enhanced-sidebar-compact`]: collapsed.value, // 使用collapsed状态而不是isCompact
    [`${prefix}-enhanced-sidebar-dark`]: sideMode.value,
  },
]);

const logoCls = computed(() => [
  `${prefix}-enhanced-side-nav-logo-${collapsed.value ? 't' : 'tdesign'}-logo`,
  {
    [`${prefix}-enhanced-side-nav-dark`]: sideMode.value,
  },
]);

const versionCls = computed(() => [
  `enhanced-version-display`,
  {
    [`${prefix}-enhanced-side-nav-dark`]: sideMode.value,
  },
]);

const menuCls = computed(() => [
  `${prefix}-enhanced-side-nav`,
  {
    [`${prefix}-enhanced-side-nav-no-logo`]: !showLogo,
    [`${prefix}-enhanced-side-nav-no-fixed`]: !isFixed,
    [`${prefix}-enhanced-side-nav-mix-fixed`]: layout === 'mix' && isFixed,
  },
]);

const router = useRouter();
const { goToHome } = useHomeNavigation();

const autoCollapsed = () => {
  const isCompact = window.innerWidth <= MIN_POINT;
  settingStore.updateConfig({
    isSidebarCompact: isCompact,
  });
};

const goHome = () => {
  router.push(goToHome());
};

const getLogo = () => {
  if (collapsed.value) return AssetLogo;
  return AssetLogoFull;
};

const toggleCollapse = () => {
  settingStore.updateConfig({
    isSidebarCompact: !collapsed.value,
  });
};

onMounted(() => {
  // 初始化激活菜单
  const path = String(active.value || getActive());
  if (path) {
    const parentPath = path.substring(0, path.lastIndexOf('/'));
    if (parentPath && !expanded.value.includes(parentPath)) {
      expanded.value = [...expanded.value, parentPath];
    }
    
    // 页面加载时手动设置菜单激活状态
    setActiveMenuItem(path);
  }
  
  // 添加路由变化监听，确保菜单状态与路由同步
  router.afterEach((to) => {
    // 获取当前路径
    const currentPath = to.path;
    // 如果路径存在，更新激活状态
    if (currentPath) {
      // 如果有meta.activeMenu，使用它
      const activePath = to.meta.activeMenu ? String(to.meta.activeMenu) : currentPath;
      console.log('🔄 路由变化，更新激活状态:', activePath);
      
      // 找到并展开父菜单
      const parentPath = activePath.substring(0, activePath.lastIndexOf('/'));
      if (parentPath) {
        if (menuAutoCollapsed.value) {
          expanded.value = [parentPath];
        } else if (!expanded.value.includes(parentPath)) {
          expanded.value = [...expanded.value, parentPath];
        }
      }
      
      // 路由变化时手动设置菜单激活状态
      setActiveMenuItem(activePath);
    }
  });
  
  autoCollapsed();
  window.onresize = () => {
    autoCollapsed();
  };
});
</script>

<style lang="less" scoped>
// 现代化容器设计
.enhanced-side-nav-container {
  position: relative;
  height: 100%;
  padding: 12px 0 12px 12px;
  width: 232px; // 展开状态宽度
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  
  // 折叠状态
  &.tdesign-starter-enhanced-sidebar-compact {
    width: 64px; // 折叠状态宽度
    padding: 12px 0 12px 8px;
  }
}

.enhanced-side-nav-wrapper {
  position: relative;
  height: 100%;
  width: 100%; // 继承容器宽度
  background: linear-gradient(135deg, 
    rgba(var(--td-bg-color-container-rgb, 255, 255, 255), 0.98) 0%,
    rgba(var(--td-bg-color-page-rgb, 248, 250, 252), 0.95) 100%);
  backdrop-filter: blur(20px) saturate(1.1);
  border-right: 1px solid rgba(var(--td-border-level-1-color), 0.5);
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

// 增强菜单组件样式
.enhanced-menu-component {
  position: relative;
  z-index: 2;
  border: none;
  background: transparent;
  height: 100%;
  
  :deep(.t-menu__scroll) {
    &::-webkit-scrollbar {
      width: 6px;
    }
    
    &::-webkit-scrollbar-track {
      background: transparent;
      border-radius: 3px;
    }
    
    &::-webkit-scrollbar-thumb {
      background: linear-gradient(180deg, 
        rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.4) 0%, 
        rgba(var(--td-brand-color-8-rgb, 5, 148, 250), 0.3) 100%);
      border-radius: 3px;
      
      &:hover {
        background: linear-gradient(180deg, 
          rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.6) 0%, 
          rgba(var(--td-brand-color-8-rgb, 5, 148, 250), 0.5) 100%);
      }
    }
  }
}

// Logo区域简洁设计 - 参考HeaderOnly风格
:deep(.tdesign-starter-enhanced-side-nav-logo-wrapper) {
  padding: 16px 20px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  
  .enhanced-logo-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 12px;
    
    .enhanced-logo-icon {
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      filter: drop-shadow(0 2px 8px rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.2));
    }
    
    .enhanced-logo-text {
      flex: 1;
      
      .brand-text {
        font-size: 16px;
        font-weight: 600;
        color: var(--td-text-color-primary);
        letter-spacing: 0.5px;
        transition: all 0.3s ease;
      }
    }
    
    .enhanced-collapse-toggle {
      opacity: 0.7;
      transition: all 0.3s ease;
      
      &:hover {
        opacity: 1;
        background: rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.1);
      }
    }
  }
  
  &:hover {
    .enhanced-logo-icon {
      transform: scale(1.05);
      filter: drop-shadow(0 4px 12px rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.3));
    }
    
    .brand-text {
      color: var(--td-brand-color);
    }
  }
}



// 占位符样式
.tdesign-starter-enhanced-side-nav-placeholder {
  flex-shrink: 0;
  transition: all 0.3s;
  width: 232px;
  
  &-hidden {
    width: 64px;
  }
}

// 响应式适配
@media (max-width: 768px) {
  .enhanced-side-nav-container {
    padding: 8px 0 8px 8px;
  }
}

// 底部折叠按钮样式
.enhanced-bottom-collapse-container {
  position: absolute;
  bottom: 100px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1000;
  
  .enhanced-bottom-collapse-button {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, 
      rgba(var(--td-bg-color-container-rgb, 255, 255, 255), 0.95) 0%,
      rgba(var(--td-bg-color-page-rgb, 248, 250, 252), 0.9) 100%);
    backdrop-filter: blur(12px) saturate(1.1);
    border: 1px solid rgba(var(--td-border-level-1-color), 0.3);
    border-radius: 12px;
    box-shadow: 
      0 4px 12px rgba(var(--td-shadow-color-rgb, 0, 0, 0), 0.08),
      0 2px 6px rgba(var(--td-shadow-color-rgb, 0, 0, 0), 0.04);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    
    &:hover {
      background: linear-gradient(135deg, 
        rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.05) 0%,
        rgba(var(--td-brand-color-8-rgb, 5, 148, 250), 0.03) 100%);
      border-color: rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.2);
      box-shadow: 
        0 6px 16px rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.12),
        0 3px 8px rgba(var(--td-shadow-color-rgb, 0, 0, 0), 0.06);
      // transform: translateX(-50%) translateY(-2px);
      
      :deep(.t-icon) {
        color: var(--td-brand-color);
        // 移除 scale 变换，避免图标偏移
      }
    }
    
          &:active {
        transform: translateX(-50%) translateY(0px);
        box-shadow: 
          0 2px 8px rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.08),
          0 1px 4px rgba(var(--td-shadow-color-rgb, 0, 0, 0), 0.04);
      }
    
    :deep(.t-icon) {
      color: var(--td-text-color-secondary);
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }
  }
}

// 暗色主题适配 - 使用全局主题状态选择器
:root[theme-mode='dark'] {
  .enhanced-side-nav-wrapper {
    background: linear-gradient(135deg, 
      var(--td-bg-color-container) 0%,
      var(--td-bg-color-page) 50%,
      var(--td-bg-color-container) 100%) !important;
    border-right-color: var(--td-border-level-1-color) !important;
  }
  
  .enhanced-bottom-collapse-container {
    .enhanced-bottom-collapse-button {
      background: linear-gradient(135deg, 
        var(--td-bg-color-container) 0%,
        var(--td-bg-color-page) 100%) !important;
      border-color: var(--td-border-level-1-color) !important;
      
      &:hover {
        background: linear-gradient(135deg, 
          rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.08) 0%,
          rgba(var(--td-brand-color-8-rgb, 5, 148, 250), 0.05) 100%) !important;
        border-color: var(--td-brand-color-3) !important;
      }
      
      :deep(.t-icon) {
        color: var(--td-text-color-secondary) !important;
        
        &:hover {
          color: var(--td-brand-color) !important;
        }
      }
    }
  }
}

// 响应式适配
@media (max-width: 768px) {
  .enhanced-side-nav-container {
    padding: 8px 0 8px 8px;
  }
  
  .enhanced-bottom-collapse-container {
    bottom: 12px;
    
    .enhanced-bottom-collapse-button {
      width: 36px;
      height: 36px;
      border-radius: 10px;
    }
  }
}
</style> 
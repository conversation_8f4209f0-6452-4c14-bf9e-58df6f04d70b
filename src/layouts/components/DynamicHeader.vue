<template>
  <div class="dynamic-header">
    <div class="header-left">
      <!-- 侧边栏切换按钮 -->
      <t-button 
        variant="text" 
        size="small" 
        @click="toggleSidebar"
        class="sidebar-toggle"
      >
        <template #icon>
          <t-icon name="menu" size="20px" />
        </template>
      </t-button>
      
      <!-- 面包屑导航 -->
      <div class="breadcrumb-container">
        <t-breadcrumb>
          <t-breadcrumb-item to="/">
            <t-icon name="home" size="16px" />
            首页
          </t-breadcrumb-item>
          
          <t-breadcrumb-item 
            v-if="parentRouteInfo"
            :to="parentRoute"
          >
            {{ getRouteTitle(parentRouteInfo) }}
          </t-breadcrumb-item>
          
          <t-breadcrumb-item 
            v-if="currentRouteTitle && currentRouteTitle !== getRouteTitle(parentRouteInfo)"
          >
            {{ currentRouteTitle }}
          </t-breadcrumb-item>
        </t-breadcrumb>
      </div>
    </div>
    
    <div class="header-center">
      <!-- 页面标题 -->
      <div class="page-title" v-if="currentRouteTitle">
        <h1>{{ currentRouteTitle }}</h1>
        <p v-if="currentRouteDesc" class="page-description">{{ currentRouteDesc }}</p>
      </div>
    </div>
    
    <div class="header-right">
      <!-- 操作按钮 -->
      <div class="header-actions">
        <t-button 
          variant="text" 
          size="small"
          @click="goBack"
          class="action-btn"
        >
          <template #icon>
            <t-icon name="arrow-left" size="16px" />
          </template>
          返回
        </t-button>
        
        <t-button 
          variant="text" 
          size="small"
          @click="refreshPage"
          class="action-btn"
        >
          <template #icon>
            <t-icon name="refresh" size="16px" />
          </template>
          刷新
        </t-button>
        
        <!-- 更多操作 -->
        <t-dropdown 
          placement="bottom-right"
          :options="moreActions"
          @click="handleMoreAction"
        >
          <t-button variant="text" size="small" class="action-btn">
            <template #icon>
              <t-icon name="more" size="16px" />
            </template>
          </t-button>
        </t-dropdown>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { 
  Button as TButton, 
  Icon as TIcon,
  Breadcrumb as TBreadcrumb,
  BreadcrumbItem as TBreadcrumbItem,
  Dropdown as TDropdown,
  MessagePlugin
} from 'tdesign-vue-next'
import { useRoutesStore } from '@/store/modules/routes'
import type { ObeRouteRecord } from '@/types/router'

// Props
interface Props {
  parentRoute: string
}

const props = withDefaults(defineProps<Props>(), {
  parentRoute: ''
})

// Emits
const emit = defineEmits<{
  'toggle-sidebar': []
}>()

// 路由相关
const route = useRoute()
const router = useRouter()
const routesStore = useRoutesStore()

// 更多操作选项
const moreActions = ref([
  {
    content: '打印页面',
    value: 'print',
    icon: 'print'
  },
  {
    content: '全屏显示',
    value: 'fullscreen',
    icon: 'fullscreen'
  },
  {
    content: '导出数据',
    value: 'export',
    icon: 'download'
  }
])

// 父路由信息
const parentRouteInfo = computed(() => {
  if (!props.parentRoute) return null
  
  const allRoutes = routesStore.routes
  return findRouteByPath(allRoutes, props.parentRoute)
})

// 当前路由标题
const currentRouteTitle = computed(() => {
  return getRouteTitle(route.meta as any) || '未命名页面'
})

// 当前路由描述
const currentRouteDesc = computed(() => {
  return route.meta?.description || ''
})

// 递归查找路由
const findRouteByPath = (routes: ObeRouteRecord[], targetPath: string): ObeRouteRecord | null => {
  for (const routeItem of routes) {
    if (routeItem.path === targetPath || routeItem.name === targetPath) {
      return routeItem
    }
    if (routeItem.children) {
      const found = findRouteByPath(routeItem.children, targetPath)
      if (found) return routeItem
    }
  }
  return null
}

// 获取路由标题
const getRouteTitle = (routeItem: any) => {
  if (!routeItem?.title) return routeItem?.name || '未命名'
  
  const title = routeItem.title
  if (typeof title === 'string') return title
  if (typeof title === 'object' && title.zh_CN) return title.zh_CN
  
  return routeItem.name || '未命名'
}

// 切换侧边栏
const toggleSidebar = () => {
  emit('toggle-sidebar')
}

// 返回上一页
const goBack = () => {
  if (window.history.length > 1) {
    router.go(-1)
  } else {
    router.push(props.parentRoute || '/')
  }
}

// 刷新页面
const refreshPage = () => {
  window.location.reload()
}

// 处理更多操作
const handleMoreAction = (data: any) => {
  const { value } = data
  
  switch (value) {
    case 'print':
      window.print()
      break
    case 'fullscreen':
      if (document.fullscreenElement) {
        document.exitFullscreen()
      } else {
        document.documentElement.requestFullscreen()
      }
      break
    case 'export':
      MessagePlugin.info('导出功能开发中...')
      break
    default:
      console.log('未知操作:', value)
  }
}
</script>

<style lang="less" scoped>
.dynamic-header {
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24px;
  background: var(--td-bg-color-container);
  border-bottom: 1px solid var(--td-component-border);
  
  .header-left {
    display: flex;
    align-items: center;
    gap: 16px;
    flex: 1;
    min-width: 0;
    
    .sidebar-toggle {
      color: var(--td-text-color-secondary);
      
      &:hover {
        background: var(--td-bg-color-container-hover);
        color: var(--td-text-color-primary);
      }
    }
    
    .breadcrumb-container {
      flex: 1;
      min-width: 0;
      
      :deep(.t-breadcrumb) {
        .t-breadcrumb__item {
          color: var(--td-text-color-secondary);
          
          &:last-child {
            color: var(--td-text-color-primary);
            font-weight: 500;
          }
          
          .t-breadcrumb__inner {
            display: flex;
            align-items: center;
            gap: 4px;
          }
        }
        
        .t-breadcrumb__separator {
          color: var(--td-text-color-placeholder);
        }
      }
    }
  }
  
  .header-center {
    flex: 2;
    display: flex;
    justify-content: center;
    align-items: center;
    min-width: 0;
    
    .page-title {
      text-align: center;
      
      h1 {
        font-size: 18px;
        font-weight: 600;
        color: var(--td-text-color-primary);
        margin: 0;
        line-height: 1.2;
      }
      
      .page-description {
        font-size: 12px;
        color: var(--td-text-color-secondary);
        margin: 4px 0 0 0;
        line-height: 1;
      }
    }
  }
  
  .header-right {
    flex: 1;
    display: flex;
    justify-content: flex-end;
    min-width: 0;
    
    .header-actions {
      display: flex;
      align-items: center;
      gap: 8px;
      
      .action-btn {
        color: var(--td-text-color-secondary);
        
        &:hover {
          background: var(--td-bg-color-container-hover);
          color: var(--td-text-color-primary);
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .dynamic-header {
    padding: 0 16px;
    
    .header-center {
      flex: 1;
      
      .page-title {
        h1 {
          font-size: 16px;
        }
        
        .page-description {
          display: none;
        }
      }
    }
    
    .header-right {
      .header-actions {
        gap: 4px;
        
        // 隐藏部分按钮文字
        .action-btn {
          :deep(.t-button__text) {
            display: none;
          }
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .dynamic-header {
    .header-left {
      .breadcrumb-container {
        display: none;
      }
    }
  }
}

// 暗黑模式适配
:root[theme-mode='dark'] {
  .dynamic-header {
    background: var(--td-bg-color-container);
    border-color: var(--td-component-border);
  }
}
</style> 
<template>
  <div class="layout-footer">
    <div class="footer-content">
      <!-- 左侧版权信息 -->
      <div class="footer-left">
        <span class="copyright">
          © {{ currentYear }} OBE教学管理系统 · 
          <span class="company">河南财政金融学院</span>
        </span>
      </div>
      
      <!-- 中间链接 -->
      <div class="footer-center">
        <div class="footer-links">
          <a 
            v-for="link in footerLinks" 
            :key="link.name"
            :href="link.url" 
            :target="link.external ? '_blank' : '_self'"
            class="footer-link"
            @click="handleLinkClick(link)"
          >
            {{ link.name }}
          </a>
        </div>
      </div>
      
      <!-- 右侧版本信息 -->
      <div class="footer-right">
        <span class="version">
          版本 {{ systemVersion }}
        </span>
        <span class="build-info" v-if="buildInfo">
          构建 {{ buildInfo }}
        </span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'

// 路由
const router = useRouter()

// 当前年份
const currentYear = computed(() => new Date().getFullYear())

// 系统版本
const systemVersion = ref('v1.0.0')

// 构建信息
const buildInfo = ref('20240315')

// 底部链接
const footerLinks = ref([
  {
    name: '帮助中心',
    url: '/help',
    external: false
  },
  {
    name: '用户手册',
    url: '/manual',
    external: false
  },
  {
    name: '意见反馈',
    url: '/feedback',
    external: false
  },
  {
    name: '联系我们',
    url: '/contact',
    external: false
  },
  {
    name: '官方网站',
    url: 'https://www.hafu.edu.cn/',
    external: true
  }
])

// 处理链接点击
const handleLinkClick = (link: any) => {
  if (!link.external && !link.url.startsWith('http')) {
    // 内部路由
    router.push(link.url)
  }
  // 外部链接由浏览器默认处理
}
</script>

<style lang="less" scoped>
.layout-footer {
  background: var(--td-bg-color-container);
  border-top: 1px solid var(--td-component-border);
  padding: 12px 24px;
  
  .footer-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 12px;
    color: var(--td-text-color-secondary);
    max-width: 1400px;
    margin: 0 auto;
    
    .footer-left {
      flex: 1;
      
      .copyright {
        .company {
          font-weight: 500;
          color: var(--td-brand-color);
        }
      }
    }
    
    .footer-center {
      flex: 2;
      display: flex;
      justify-content: center;
      
      .footer-links {
        display: flex;
        align-items: center;
        gap: 24px;
        
        .footer-link {
          color: var(--td-text-color-secondary);
          text-decoration: none;
          transition: color 0.2s ease;
          position: relative;
          
          &:hover {
            color: var(--td-brand-color);
          }
          
          &:not(:last-child)::after {
            content: '|';
            position: absolute;
            right: -12px;
            color: var(--td-component-border);
          }
        }
      }
    }
    
    .footer-right {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      gap: 16px;
      
      .version {
        font-weight: 500;
        color: var(--td-text-color-primary);
      }
      
      .build-info {
        color: var(--td-text-color-placeholder);
        font-size: 11px;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .layout-footer {
    padding: 12px 16px;
    
    .footer-content {
      flex-direction: column;
      gap: 12px;
      text-align: center;
      
      .footer-left,
      .footer-center,
      .footer-right {
        flex: none;
      }
      
      .footer-center {
        .footer-links {
          gap: 16px;
          flex-wrap: wrap;
          justify-content: center;
          
          .footer-link {
            &:not(:last-child)::after {
              display: none;
            }
          }
        }
      }
      
      .footer-right {
        justify-content: center;
        gap: 8px;
      }
    }
  }
}

@media (max-width: 480px) {
  .layout-footer {
    .footer-content {
      font-size: 11px;
      
      .footer-center {
        .footer-links {
          gap: 12px;
          
          .footer-link {
            font-size: 11px;
          }
        }
      }
      
      .footer-right {
        .build-info {
          display: none;
        }
      }
    }
  }
}

// 暗黑模式适配
:root[theme-mode='dark'] {
  .layout-footer {
    background: var(--td-bg-color-container);
    border-color: var(--td-component-border);
  }
}
</style> 
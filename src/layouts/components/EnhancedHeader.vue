<template>
  <div class="enhanced-header" :class="layoutCls">
    <div class="header-content">
      <!-- 左侧Logo和菜单区域 -->
      <div class="header-left">
        <!-- Logo区域 -->
        <span v-if="showLogo" class="header-logo-container" @click="() => router.push(goToHome())">
          <logo-full class="t-logo" />
        </span>
        
        <!-- 非Logo模式下的操作区域 -->
        <div v-else class="header-operate-left">
          <t-button theme="default" shape="square" variant="text" @click="changeCollapsed" class="collapse-btn">
            <t-icon class="collapsed-icon" name="view-list" />
          </t-button>
          <search :layout="layout" />
        </div>
        
        <!-- 顶部布局下的菜单 -->
        <div v-if="layout !== 'side'" class="header-menu-container">
          <menu-content class="header-menu" :nav-data="menu" />
        </div>
      </div>
      
      <!-- 右侧操作区域 -->
      <div class="header-right">
        <!-- 搜索框 (在非侧边栏布局下显示) -->
        <search v-if="layout !== 'side'" :layout="layout" class="header-search" />

        <!-- 语言切换 -->
        <t-dropdown trigger="click" class="lang-dropdown">
          <t-button theme="default" shape="square" variant="text">
            <translate-icon />
          </t-button>
          <t-dropdown-menu>
            <t-dropdown-item
              v-for="(lang, index) in langList"
              :key="index"
              :value="lang.value"
              @click="(options) => changeLang(options.value as string)"
            >
              {{ lang.content }}
            </t-dropdown-item>
          </t-dropdown-menu>
        </t-dropdown>
        
        <!-- 用户菜单 -->
        <t-dropdown :min-column-width="120" trigger="click" class="user-dropdown">
          <template #dropdown>
            <t-dropdown-menu>
              <t-dropdown-item class="operations-dropdown-container-item" @click="() => handleNav()">
                <user-circle-icon />{{ t('layout.header.user') }}
              </t-dropdown-item>
              <t-dropdown-item class="operations-dropdown-container-item" @click="handleLogout">
                <poweroff-icon />{{ t('layout.header.signOut') }}
              </t-dropdown-item>
            </t-dropdown-menu>
          </template>
          <t-button class="header-user-btn" theme="default" variant="text">
            <template #icon>
              <t-icon class="header-user-avatar" name="user-circle" />
            </template>
            <div class="header-user-account">{{ user.userInfo.username }}</div>
            <template #suffix><chevron-down-icon /></template>
          </t-button>
        </t-dropdown>
        
        <!-- 设置按钮 -->
        <t-tooltip placement="bottom" :content="t('layout.header.setting')">
          <t-button theme="default" shape="square" variant="text" @click="toggleSettingPanel">
            <setting-icon />
          </t-button>
        </t-tooltip>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ChevronDownIcon, PoweroffIcon, SettingIcon, TranslateIcon, UserCircleIcon } from 'tdesign-icons-vue-next';
import type { PropType } from 'vue';
import { computed } from 'vue';
import { useRouter } from 'vue-router';

import LogoFull from '@/assets/assets-logo-full.svg?component';
import { prefix } from '@/config/global';
import { langList, t } from '@/locales';
import { useLocale } from '@/locales/useLocale';
import { useSettingStore, useUserStore } from '@/store';
import type { MenuRoute, ModeType } from '@/types/interface';

import MenuContent from './MenuContent.vue';
import Search from './Search.vue';
import { useHomeNavigation } from '../composables/useHomeNavigation';

// Props定义
const { theme, layout, showLogo, menu, isFixed, isCompact } = defineProps({
  theme: {
    type: String,
    default: 'light',
  },
  layout: {
    type: String,
    default: 'top',
  },
  showLogo: {
    type: Boolean,
    default: true,
  },
  menu: {
    type: Array as PropType<MenuRoute[]>,
    default: (): MenuRoute[] => [],
  },
  isFixed: {
    type: Boolean,
    default: false,
  },
  isCompact: {
    type: Boolean,
    default: false,
  },
  maxLevel: {
    type: Number,
    default: 3,
  },
});

// Store和路由
const userStore = useUserStore();
const router = useRouter();
const settingStore = useSettingStore();
const user = useUserStore();
const { goToHome } = useHomeNavigation();

// 布局类计算
const layoutCls = computed(() => [
  `${prefix}-enhanced-header`,
  {
    [`${prefix}-enhanced-header-fixed`]: isFixed,
    [`${prefix}-enhanced-header-fixed-side`]: layout === 'side' && isFixed,
    [`${prefix}-enhanced-header-fixed-side-compact`]: layout === 'side' && isFixed && isCompact,
  },
]);

// 设置面板切换
const toggleSettingPanel = () => {
  settingStore.updateConfig({
    showSettingPanel: true,
  });
};

// 语言切换
const { changeLocale } = useLocale();
const changeLang = (lang: string) => {
  changeLocale(lang);
};

// 侧边栏折叠切换
const changeCollapsed = () => {
  settingStore.updateConfig({
    isSidebarCompact: !settingStore.isSidebarCompact,
  });
};

// 导航处理
const handleNav = (path?: string) => {
  if (path) {
    router.push(path);
    return;
  }
  
  if (userStore.token) {
    try {
      userStore.getUserInfo();
      const roleCode = userStore.userInfo?.type;
      if (roleCode === 4) {
        router.push('/studentUser/student');
      } else if (roleCode === 3) {
        router.push('/studentUser/student');
      } else if (roleCode === 2) {
        router.push('/studentUser/student');
      }
    } catch (error) {
      console.error('跳转错误:', error);
    }
  }
};

// 退出登录
const handleLogout = async () => {
  try {
    await userStore.logout();
  } catch (error) {
    console.error('退出登录失败:', error);
    // 即使logout失败，也强制跳转到登录页
    router.push({
      path: '/login',
      query: { redirect: encodeURIComponent(router.currentRoute.value.fullPath) },
    });
  }
};
</script>

<style lang="less" scoped>
.enhanced-header {
  height: 64px;
  background: linear-gradient(180deg, 
    rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.03) 0%, 
    rgba(var(--td-brand-color-8-rgb, 5, 148, 250), 0.015) 50%,
    transparent 100%);
  backdrop-filter: blur(25px) saturate(1.2);
  position: relative;
  z-index: 1000;
  
  // 添加柔和的边缘羽化效果
  &::before {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 0;
    right: 0;
    height: 10px;
    background: linear-gradient(180deg, 
      rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.02) 0%, 
      transparent 100%);
    pointer-events: none;
  }
  
  // 添加顶部柔和光晕
  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 60%;
    height: 2px;
    background: linear-gradient(90deg, 
      transparent 0%, 
      rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.15) 50%, 
      transparent 100%);
    border-radius: 0 0 50px 50px;
    filter: blur(1px);
  }
  
  .header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 100%;
    padding: 0 32px;
    position: relative;
    z-index: 1;
  }
  
  .header-left {
    display: flex;
    align-items: center;
    flex: 1;
    
    .header-logo-container {
      width: 184px;
      height: 26px;
      display: flex;
      cursor: pointer;
      transition: all 0.3s ease;
      margin-right: 32px;
      
      &:hover {
        opacity: 0.8;
        transform: translateY(-1px);
      }
      
      .t-logo {
        width: 100%;
        height: 100%;
        filter: drop-shadow(0 2px 8px rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.2));
      }
    }
    
    .header-operate-left {
      display: flex;
      align-items: center;
      gap: 16px;
      margin-right: 32px;
      
      .collapse-btn {
        border-radius: 12px;
        background: rgba(255, 255, 255, 0.05);
        backdrop-filter: blur(15px);
        border: 1px solid rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.05);
        
        &:hover {
          background: rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.08);
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.12);
        }
      }
    }
    
    .header-menu-container {
      flex: 1;
      
      .header-menu {
        display: flex;
        
        :deep(.t-menu) {
          background: transparent;
          border: none;
          
          .t-menu__item {
            color: var(--td-text-color-primary);
            border-radius: 8px;
            margin: 0 4px;
            
            &:hover {
              background: rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.08);
            }
            
            &.t-is-active {
              background: rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.12);
              color: var(--td-brand-color);
            }
          }
        }
      }
    }
  }
  
  .header-right {
    display: flex;
    align-items: center;
    gap: 8px;
    
    .header-search {
      margin-right: 16px;
    }
    
    .t-button {
      border-radius: 12px;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      background: rgba(255, 255, 255, 0.05);
      backdrop-filter: blur(15px) saturate(1.1);
      border: 1px solid rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.05);
      box-shadow: 0 2px 8px rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.04);
      
      &:hover {
        background: rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.08);
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.12);
        border-color: rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.15);
      }
    }
    
    .header-user-btn {
      padding: 10px 16px;
      border-radius: 14px;
      background: rgba(255, 255, 255, 0.08);
      backdrop-filter: blur(20px) saturate(1.2);
      border: 1px solid rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.08);
      box-shadow: 0 3px 10px rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.06);
      
      &:hover {
        background: rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.12);
        transform: translateY(-1px);
        box-shadow: 0 5px 15px rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.15);
      }
      
      .header-user-avatar {
        font-size: 22px;
        color: var(--td-brand-color);
        filter: drop-shadow(0 2px 4px rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.3));
      }
      
      .header-user-account {
        margin: 0 12px;
        font-weight: 600;
        color: var(--td-text-color-primary);
        text-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      }
    }
  }
  
  // 固定模式样式
  &.@{starter-prefix}-enhanced-header-fixed {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1001;
    
    &.@{starter-prefix}-enhanced-header-fixed-side {
      left: 232px;
      transition: all 0.3s;
      
      &.@{starter-prefix}-enhanced-header-fixed-side-compact {
        left: 64px;
      }
    }
  }
}

.operations-dropdown-container-item {
  width: 100%;
  display: flex;
  align-items: center;

  :deep(.t-dropdown__item-text) {
    display: flex;
    align-items: center;
  }

  .t-icon {
    font-size: 16px;
    margin-right: 8px;
  }

  :deep(.t-dropdown__item) {
    width: 100%;
    margin-bottom: 0;
    padding: 8px 16px;
    border-radius: 6px;
    
    &:hover {
      background: var(--td-bg-color-container-hover);
    }
  }

  &:last-child {
    :deep(.t-dropdown__item) {
      margin-bottom: 8px;
    }
  }
}

// 响应式适配
@media (max-width: 768px) {
  .enhanced-header {
    .header-content {
      padding: 0 16px;
    }
    
    .header-left {
      .header-logo-container {
        width: 120px;
        height: 20px;
      }
    }
    
    .header-right {
      gap: 4px;
      
      .header-search {
        margin-right: 8px;
      }
    }
  }
}

// 暗色主题适配
@media (prefers-color-scheme: dark) {
  .enhanced-header {
    background: linear-gradient(180deg, 
      rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.05) 0%, 
      rgba(var(--td-brand-color-8-rgb, 5, 148, 250), 0.03) 50%,
      transparent 100%);
    
    &::before {
      background: linear-gradient(180deg, 
        rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.04) 0%, 
        transparent 100%);
    }
    
    &::after {
      background: linear-gradient(90deg, 
        transparent 0%, 
        rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.25) 50%, 
        transparent 100%);
    }
  }
}
</style> 
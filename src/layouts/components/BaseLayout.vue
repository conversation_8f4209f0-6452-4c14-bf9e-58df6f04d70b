<template>
  <div class="base-layout" :class="layoutClass">
    <t-layout>
      <!-- Header区域 -->
      <t-header class="base-layout-header">
        <slot name="header" />
      </t-header>
      
      <!-- 主内容区域 -->
      <t-content 
        class="base-layout-main-content" 
        :style="{ height: mainContentHeight }"
      >
        <!-- 有侧边栏的布局结构 -->
        <div v-if="showSidebar" class="content-with-sidebar">
          <!-- 左侧侧边栏 -->
          <div 
            class="base-layout-sidebar" 
            :class="{ 'sidebar-collapsed': settingStore.isSidebarCompact }"
          >
            <!-- 返回工作台按钮 -->
            <div v-if="showBackButton" class="back-to-dashboard" @click="handleBackToDashboard">
              <div class="back-content">
                <t-icon name="arrow-left" class="back-icon" />
                <span v-if="!settingStore.isSidebarCompact" class="back-text">返回工作台</span>
              </div>
            </div>
            
            <!-- 侧边栏内容 -->
            <slot name="sidebar" />
          </div>
          
          <!-- 右侧内容区域 -->
          <div class="base-layout-content-area">
            <slot name="content" />
          </div>
        </div>
        
        <!-- 无侧边栏的布局结构（HeaderOnly模式） -->
        <div v-else class="base-layout-content-area-headeronly">
          <slot name="content" />
        </div>
      </t-content>
      
      <!-- Footer区域 -->
      <t-footer v-if="settingStore.showFooter" class="base-layout-footer">
        <slot name="footer" />
      </t-footer>
    </t-layout>
    
    <!-- 底部融合渐变 -->
    <div class="bottom-gradient"></div>
    
    <!-- 设置面板 -->
    <slot name="setting" />
  </div>
</template>

<script setup lang="ts">
import '@/style/layout.less';
import '../styles/common-layout.less';
import { Layout as TLayout, Header as THeader, Content as TContent, Footer as TFooter } from 'tdesign-vue-next';
import { computed, onMounted } from 'vue';
import { useLayoutLogic } from '../composables/useLayoutLogic';

interface Props {
  layoutMode?: 'standard' | 'headerOnly' | 'dynamicSidebar';
  showSidebar?: boolean;
  showBackButton?: boolean;
  onBackToDashboard?: () => void;
}

const props = withDefaults(defineProps<Props>(), {
  layoutMode: 'standard',
  showSidebar: true,
  showBackButton: false,
  onBackToDashboard: undefined
});

const emit = defineEmits<{
  backToDashboard: [];
}>();

// 使用共同的布局逻辑
const { 
  mainContentHeight, 
  mobileMainContentHeight, 
  watchRouteChange, 
  initLayout,
  settingStore 
} = useLayoutLogic();

// 布局类名计算
const layoutClass = computed(() => [
  `base-layout--${props.layoutMode}`,
  {
    'base-layout--with-sidebar': props.showSidebar,
    'base-layout--with-back-button': props.showBackButton
  }
]);

// 返回工作台处理
const handleBackToDashboard = () => {
  if (props.onBackToDashboard) {
    props.onBackToDashboard();
  } else {
    emit('backToDashboard');
  }
};

// 初始化
onMounted(() => {
  initLayout();
  watchRouteChange();
});
</script>

<style lang="less" scoped>
// 返回工作台区域样式
.back-to-dashboard {
  position: relative;
  z-index: 3;
  padding: 16px 12px;
  margin-bottom: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  border-bottom: 1px solid rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.1);
  
  .back-content {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 16px;
    border-radius: 8px;
    background: linear-gradient(135deg, 
      rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.08) 0%,
      rgba(var(--td-brand-color-8-rgb, 5, 148, 250), 0.05) 100%);
    backdrop-filter: blur(8px);
    border: 1px solid rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.12);
    transition: all 0.3s ease;
    
    .back-icon {
      color: var(--td-brand-color);
      font-size: 16px;
      transition: all 0.3s ease;
    }
    
    .back-text {
      color: var(--td-brand-color);
      font-size: 14px;
      font-weight: 500;
      white-space: nowrap;
      transition: all 0.3s ease;
    }
  }
  
  &:hover {
    .back-content {
      background: linear-gradient(135deg, 
        rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.12) 0%,
        rgba(var(--td-brand-color-8-rgb, 5, 148, 250), 0.08) 100%);
      border-color: rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.2);
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.15);
      
      .back-icon {
        color: var(--td-brand-color-8);
        transform: translateX(-2px);
      }
      
      .back-text {
        color: var(--td-brand-color-8);
      }
    }
  }
  
  &:active {
    .back-content {
      transform: translateY(0);
      box-shadow: 0 2px 6px rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.12);
    }
  }
}

// 不同布局模式的特殊样式
.base-layout--headerOnly {
  // HeaderOnly模式特有样式（如果需要）
}

.base-layout--dynamicSidebar {
  // DynamicSidebar模式特有样式（如果需要）
}

.base-layout--standard {
  // 标准Layout模式特有样式（如果需要）
}

// 移动端适配
@media (max-width: 768px) {
  .back-to-dashboard {
    padding: 12px 8px;
    margin-bottom: 6px;
    
    .back-content {
      padding: 10px 12px;
      
      .back-text {
        font-size: 12px;
      }
    }
  }
}

// 暗色主题适配
@media (prefers-color-scheme: dark) {
  .back-to-dashboard {
    border-bottom-color: rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.15);
    
    .back-content {
      background: linear-gradient(135deg, 
        rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.12) 0%,
        rgba(var(--td-brand-color-8-rgb, 5, 148, 250), 0.08) 100%);
      border-color: rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.2);
    }
    
    &:hover .back-content {
      background: linear-gradient(135deg, 
        rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.18) 0%,
        rgba(var(--td-brand-color-8-rgb, 5, 148, 250), 0.12) 100%);
      border-color: rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.3);
    }
  }
}
</style> 
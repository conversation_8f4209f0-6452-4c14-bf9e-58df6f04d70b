<template>
  <div class="header-only-header">
    <div class="header-content">
      <!-- 左侧Logo和项目名称 -->
      <div class="header-left">
        <div class="logo-container" @click="goHome">
          <logo-full class="logo" />
          <span class="project-name">{{ settingConfig.shortTitle }}</span>
        </div>
      </div>
      
      <!-- 右侧操作区域 -->
      <div class="header-right">
        <!-- 语言切换 -->
        <t-dropdown trigger="click" class="lang-dropdown">
          <t-button theme="default" shape="square" variant="text">
            <translate-icon />
          </t-button>
          <t-dropdown-menu>
            <t-dropdown-item
              v-for="(lang, index) in langList"
              :key="index"
              :value="lang.value"
              @click="(options) => changeLang(options.value as string)"
            >
              {{ lang.content }}
            </t-dropdown-item>
          </t-dropdown-menu>
        </t-dropdown>
        
        <!-- 用户菜单 -->
        <t-dropdown :min-column-width="120" trigger="click" class="user-dropdown">
          <template #dropdown>
            <t-dropdown-menu>
              <t-dropdown-item class="operations-dropdown-container-item" @click="handleNav">
                <user-circle-icon />{{ t('layout.header.user') }}
              </t-dropdown-item>
              <t-dropdown-item class="operations-dropdown-container-item" @click="handleLogout">
                <poweroff-icon />{{ t('layout.header.signOut') }}
              </t-dropdown-item>
            </t-dropdown-menu>
          </template>
          <t-button class="header-user-btn" theme="default" variant="text">
            <template #icon>
              <t-icon class="header-user-avatar" name="user-circle" />
            </template>
            <div class="header-user-account">{{ user.userInfo.username }}</div>
            <template #suffix><chevron-down-icon /></template>
          </t-button>
        </t-dropdown>
        
        <!-- 设置按钮 -->
        <t-tooltip placement="bottom" :content="t('layout.header.setting')">
          <t-button theme="default" shape="square" variant="text" @click="toggleSettingPanel">
            <setting-icon />
          </t-button>
        </t-tooltip>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ChevronDownIcon, PoweroffIcon, SettingIcon, TranslateIcon, UserCircleIcon } from 'tdesign-icons-vue-next';
import { useRouter } from 'vue-router';

import LogoFull from '@/assets/assets-logo-full.svg?component';
import { langList, t } from '@/locales';
import { useLocale } from '@/locales/useLocale';
import { useSettingStore, useUserStore } from '@/store';
import { settingConfig } from '@/config/setting.config';
import { useHomeNavigation } from '../composables/useHomeNavigation';

const userStore = useUserStore();
const router = useRouter();
const settingStore = useSettingStore();
const user = useUserStore();
const { goToHome } = useHomeNavigation();

const toggleSettingPanel = () => {
  settingStore.updateConfig({
    showSettingPanel: true,
  });
};

// 切换语言
const { changeLocale } = useLocale();
const changeLang = (lang: string) => {
  changeLocale(lang);
};

const handleNav = () => {
  if (userStore.token) {
    try {
      userStore.getUserInfo();
      const userType = userStore.userInfo?.type;
      if (userType === 4) {
        router.push('/studentUser/student')
      } else if (userType === 3) {
        router.push('/studentUser/student')
      } else if (userType === 2) {
        router.push('/studentUser/student')
      }
    } catch (error) {
      console.error('跳转错误:', error);
    }
  }
};

const handleLogout = async () => {
  try {
    await userStore.logout();
  } catch (error) {
    console.error('退出登录失败:', error);
    // 即使logout失败，也强制跳转到登录页
    router.push({
      path: '/login',
      query: { redirect: encodeURIComponent(router.currentRoute.value.fullPath) },
    });
  }
};

const goHome = () => {
  router.push(goToHome());
};
</script>

<style lang="less" scoped>
.header-only-header {
  width: 100%;
  height: 64px;
  background: linear-gradient(180deg, 
    rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.03) 0%, 
    rgba(var(--td-brand-color-8-rgb, 5, 148, 250), 0.015) 50%,
    transparent 100%);
  backdrop-filter: blur(25px) saturate(1.2);
  position: relative;
  
  // 添加柔和的边缘羽化效果
  &::before {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 0;
    right: 0;
    height: 10px;
    background: linear-gradient(180deg, 
      rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.02) 0%, 
      transparent 100%);
    pointer-events: none;
  }
  
  // 添加顶部柔和光晕
  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 60%;
    height: 2px;
    background: linear-gradient(90deg, 
      transparent 0%, 
      rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.15) 50%, 
      transparent 100%);
    border-radius: 0 0 50px 50px;
    filter: blur(1px);
  }
  
  .header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 100%;
    padding: 0 32px;
    position: relative;
    z-index: 1;
  }
  
  .header-left {
    display: flex;
    align-items: center;
    
    .logo-container {
      display: flex;
      align-items: center;
      cursor: pointer;
      transition: all 0.3s ease;
      
      &:hover {
        opacity: 0.8;
        transform: translateY(-1px);
      }
      
      .logo {
        width: 140px;
        height: 24px;
        margin-right: 16px;
        filter: drop-shadow(0 2px 8px rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.2));
      }
      
      .project-name {
        font-size: 20px;
        font-weight: 700;
        background: linear-gradient(135deg, 
          var(--td-brand-color) 0%, 
          var(--td-brand-color-8) 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        letter-spacing: 0.8px;
        text-shadow: 0 2px 10px rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.1);
      }
    }
  }
  
  .header-right {
    display: flex;
    align-items: center;
    gap: 8px;
    
    .t-button {
      border-radius: 12px;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      background: rgba(255, 255, 255, 0.05);
      backdrop-filter: blur(15px) saturate(1.1);
      border: 1px solid rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.05);
      box-shadow: 0 2px 8px rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.04);
      
      &:hover {
        background: rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.08);
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.12);
        border-color: rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.15);
      }
    }
    
    .header-user-btn {
      padding: 10px 16px;
      border-radius: 14px;
      background: rgba(255, 255, 255, 0.08);
      backdrop-filter: blur(20px) saturate(1.2);
      border: 1px solid rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.08);
      box-shadow: 0 3px 10px rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.06);
      
      &:hover {
        background: rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.12);
        transform: translateY(-1px);
        box-shadow: 0 5px 15px rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.15);
      }
      
      .header-user-avatar {
        font-size: 22px;
        color: var(--td-brand-color);
        filter: drop-shadow(0 2px 4px rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.3));
      }
      
      .header-user-account {
        margin: 0 12px;
        font-weight: 600;
        color: var(--td-text-color-primary);
        text-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      }
    }
  }
}

.operations-dropdown-container-item {
  width: 100%;
  display: flex;
  align-items: center;

  :deep(.t-dropdown__item-text) {
    display: flex;
    align-items: center;
  }

  .t-icon {
    font-size: 16px;
    margin-right: 8px;
  }

  :deep(.t-dropdown__item) {
    width: 100%;
    margin-bottom: 0;
    padding: 8px 16px;
    border-radius: 6px;
    
    &:hover {
      background: var(--td-bg-color-container-hover);
    }
  }

  &:last-child {
    :deep(.t-dropdown__item) {
      margin-bottom: 8px;
    }
  }
}

// 响应式适配
@media (max-width: 768px) {
  .header-only-header {
    .header-content {
      padding: 0 16px;
    }
    
    .header-left {
      .logo-container {
        .logo {
          width: 120px;
          height: 20px;
        }
        
        .project-name {
          font-size: 16px;
        }
      }
    }
  }
}
</style>

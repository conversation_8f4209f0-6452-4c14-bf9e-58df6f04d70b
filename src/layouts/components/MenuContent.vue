<template>
  <div>
    <template v-for="item in list" :key="item.path">
      <template v-if="!item.children || !item.children.length || item.meta?.single">
        <t-menu-item v-if="getHref(item)" :name="item.path" :value="getPath(item)" @click="openHref(getHref(item)[0])">
          <template #icon>
            <component :is="menuIcon(item)" class="t-icon"></component>
          </template>
          {{ renderMenuTitle(item.title) }}
        </t-menu-item>
        <t-menu-item v-else :name="item.path" :value="getPath(item)" :to="item.path">
          <template #icon>
            <component :is="menuIcon(item)" class="t-icon"></component>
          </template>
          {{ renderMenuTitle(item.title) }}
        </t-menu-item>
      </template>
      <t-submenu v-else :name="item.path" :value="item.path" :title="renderMenuTitle(item.title)">
        <template #icon>
          <component :is="menuIcon(item)" class="t-icon"></component>
        </template>
        <menu-content v-if="item.children" :nav-data="item.children" />
      </t-submenu>
    </template>
  </div>
</template>
<script setup lang="tsx">
import type { PropType } from 'vue';
import { computed } from 'vue';
import { useRoute } from 'vue-router';

import { useLocale } from '@/locales/useLocale';
import { getActive } from '@/router';
import type { MenuRoute } from '@/types/interface';

type ListItemType = MenuRoute & { icon?: string };

const { navData } = defineProps({
  navData: {
    type: Array as PropType<MenuRoute[]>,
    default: (): MenuRoute[] => [],
  },
});

const route = useRoute();
const active = computed(() => getActive());

const { locale } = useLocale();
const list = computed(() => {
  return getMenuList(navData);
});

const menuIcon = (item: ListItemType) => {
  if (typeof item.icon === 'string') return <t-icon name={item.icon} />;
  const RenderIcon = item.icon;
  return RenderIcon;
};

const renderMenuTitle = (title: string | Record<string, string>) => {
  if (typeof title === 'string') return title;
  return title[locale.value];
};

const getMenuList = (list: MenuRoute[], basePath?: string, depth: number = 0): ListItemType[] => {
  // 防止无限递归：限制最大深度
  if (depth > 10) {
    console.warn('MenuContent: 检测到可能的无限递归，已停止处理');
    return [];
  }
  
  if (!list || list.length === 0) {
    return [];
  }
  
  // 如果meta中有orderNo则按照从小到大排序
  list.sort((a, b) => {
    return (a.meta?.orderNo || 0) - (b.meta?.orderNo || 0);
  });
  
  return list
    .map((item) => {
      // 防止路径循环引用
      if (basePath && item.path === basePath) {
        console.warn('MenuContent: 检测到路径循环引用，跳过处理:', item.path);
        return null;
      }
      
      const path = basePath && !item.path.includes(basePath) ? `${basePath}/${item.path}` : item.path;

      return {
        path,
        title: item.meta?.title,
        icon: item.meta?.icon,
        children: item.children ? getMenuList(item.children, path, depth + 1) : [],
        meta: item.meta,
        redirect: item.redirect,
      };
    })
    .filter((item) => item && item.meta && item.meta.hidden !== true);
};

const getHref = (item: MenuRoute) => {
  const { frameSrc, frameBlank } = item.meta;
  if (frameSrc && frameBlank) {
    return frameSrc.match(/(http|https):\/\/([\w.]+\/?)\S*/);
  }
  return null;
};

const getPath = (item: ListItemType) => {
  const activeLevel = active.value.split('/').length;
  const pathLevel = item.path.split('/').length;
  if (activeLevel > pathLevel && active.value.startsWith(item.path)) {
    return active.value;
  }

  if (active.value === item.path) {
    return active.value;
  }

  return item.meta?.single ? item.redirect : item.path;
};

const openHref = (url: string) => {
  window.open(url);
};

</script>

<style lang="less">
// 现代化菜单项样式 - 全局样式确保优先级
:deep(.t-default-menu),
:deep(.t-default-menu__inner),
:deep(.t-menu),
.t-default-menu,
.t-default-menu__inner,
.t-menu {
  // 菜单项样式 - 适配实际DOM结构，使用!important确保优先级
  .t-menu__item {
    margin: 8px 10px !important;
    border-radius: 16px !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    position: relative !important;
    overflow: hidden !important;
    backdrop-filter: blur(10px) !important;
    border: 1px solid transparent !important;
    
    // 添加现代化悬停渐变背景
    &::before {
      content: '' !important;
      position: absolute !important;
      top: 0 !important;
      left: 0 !important;
      right: 0 !important;
      bottom: 0 !important;
      background: linear-gradient(135deg, 
        rgba(255, 255, 255, 0.6) 0%,
        rgba(248, 250, 252, 0.4) 50%,
        rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.05) 100%) !important;
      opacity: 0 !important;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
      border-radius: 16px !important;
      z-index: 1 !important;
    }
    
    &:hover {
      transform: translateX(6px) translateY(-2px);
      box-shadow: 
        0 8px 25px rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.12),
        0 3px 10px rgba(0, 0, 0, 0.06);
      border-color: rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.15);
      
      &::before {
        opacity: 1;
      }
      
      .t-icon {
        transform: scale(1.15) rotate(5deg);
        color: var(--td-brand-color);
        filter: drop-shadow(0 2px 8px rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.3));
      }
      
      .t-menu__content {
        color: var(--td-brand-color);
        font-weight: 600;
      }
    }
    
    &.t-is-active {
      background: linear-gradient(135deg, 
        rgba(255, 255, 255, 0.9) 0%,
        rgba(248, 250, 252, 0.7) 30%,
        rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.12) 100%);
      color: var(--td-brand-color);
      border: 1px solid rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.25);
      border-left: 4px solid var(--td-brand-color);
      transform: translateX(4px);
      box-shadow: 
        0 10px 30px rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.15),
        0 4px 12px rgba(0, 0, 0, 0.08);
      
      .t-icon {
        color: var(--td-brand-color);
        transform: scale(1.1);
        filter: drop-shadow(0 3px 8px rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.4));
      }
      
      .t-menu__content {
        color: var(--td-brand-color);
        font-weight: 700;
      }
      
      &::after {
        content: '';
        position: absolute;
        right: 16px;
        top: 50%;
        transform: translateY(-50%);
        width: 6px;
        height: 6px;
        background: linear-gradient(135deg, 
          var(--td-brand-color) 0%, 
          var(--td-brand-color-8) 100%);
        border-radius: 50%;
        box-shadow: 
          0 0 12px rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.6),
          0 0 4px rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.8);
      }
    }
    
    .t-icon {
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      font-size: 19px;
      margin-right: 14px;
    }
    
    .t-menu__content {
      font-weight: 500;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      letter-spacing: 0.3px;
    }
  }
  
  // 子菜单现代化样式 - 支持完整DOM层级结构
  .t-submenu {
    margin: 8px 10px;
    border-radius: 18px;
    background: linear-gradient(135deg, 
      rgba(255, 255, 255, 0.3) 0%,
      rgba(248, 250, 252, 0.2) 100%);
    backdrop-filter: blur(15px);
    border: 1px solid rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.08);
    box-shadow: 0 2px 12px rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.04);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    
    &:hover {
      background: linear-gradient(135deg, 
        rgba(255, 255, 255, 0.5) 0%,
        rgba(248, 250, 252, 0.3) 100%);
      border-color: rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.15);
      box-shadow: 0 4px 20px rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.08);
    }
    
    .t-submenu__title {
      border-radius: 18px;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      position: relative;
      overflow: hidden;
      padding: 12px 16px;
      
      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, 
          rgba(255, 255, 255, 0.4) 0%,
          rgba(248, 250, 252, 0.3) 50%,
          rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.06) 100%);
        opacity: 0;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        border-radius: 18px;
      }
      
      &:hover {
        transform: translateX(4px) translateY(-1px);
        
        &::before {
          opacity: 1;
        }
        
        .t-icon {
          transform: scale(1.1) rotate(3deg);
          color: var(--td-brand-color);
          filter: drop-shadow(0 2px 6px rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.3));
        }
        
        .t-submenu__title-content {
          color: var(--td-brand-color);
          font-weight: 600;
        }
      }
      
      .t-icon {
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        font-size: 19px;
        margin-right: 14px;
      }
      
      .t-submenu__title-content {
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        font-weight: 500;
        letter-spacing: 0.3px;
      }
    }
    
    // 子菜单展开时的现代化动画
    &.t-is-opened {
      background: linear-gradient(135deg, 
        rgba(255, 255, 255, 0.6) 0%,
        rgba(248, 250, 252, 0.4) 50%,
        rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.08) 100%);
      border-color: rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.2);
      box-shadow: 0 6px 25px rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.12);
      
      .t-submenu__title {
        background: linear-gradient(135deg, 
          rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.08) 0%,
          rgba(var(--td-brand-color-8-rgb, 5, 148, 250), 0.06) 100%);
        
        .t-icon {
          color: var(--td-brand-color);
          transform: scale(1.05);
        }
        
        .t-submenu__title-content {
          color: var(--td-brand-color);
          font-weight: 600;
        }
      }
    }
    
    // 子菜单内容区域现代化设计
    .t-submenu__content {
      margin-top: 8px;
      padding: 8px 16px 12px;
      background: linear-gradient(135deg, 
        rgba(255, 255, 255, 0.2) 0%,
        rgba(248, 250, 252, 0.1) 100%);
      border-radius: 0 0 18px 18px;
      
      .t-menu__item {
        margin: 4px 0 4px 24px;
        border-radius: 0 14px 14px 0;
        position: relative;
        background: rgba(255, 255, 255, 0.3);
        border: 1px solid rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.06);
        
        &::before {
          content: '';
          position: absolute;
          left: -16px;
          top: 50%;
          transform: translateY(-50%);
          width: 3px;
          height: 0;
          background: linear-gradient(180deg, 
            var(--td-brand-color) 0%, 
            var(--td-brand-color-8) 100%);
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
          border-radius: 2px;
        }
        
        &:hover {
          background: rgba(255, 255, 255, 0.6);
          border-color: rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.15);
          transform: translateX(3px);
          
          &::before {
            height: 70%;
            box-shadow: 0 0 8px rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.4);
          }
        }
        
        &.t-is-active {
          background: linear-gradient(90deg, 
            rgba(255, 255, 255, 0.8) 0%,
            rgba(248, 250, 252, 0.6) 30%,
            rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.1) 100%);
          border: 1px solid rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.2);
          border-left: none;
          transform: translateX(2px);
          box-shadow: 0 4px 16px rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.12);
          
          &::before {
            height: 85%;
            width: 4px;
            box-shadow: 
              0 0 10px rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.5),
              0 0 3px rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.7);
          }
          
          .t-icon {
            color: var(--td-brand-color);
            transform: scale(1.05);
          }
          
          .t-menu__content {
            color: var(--td-brand-color);
            font-weight: 600;
          }
        }
      }
    }
  }
}

// 额外CSS选择器 - 针对TDesign菜单的完整DOM层级结构
:deep(.t-default-menu__inner),
:deep(.t-default-menu) {
  .t-menu__item {
    margin: 8px 10px;
    border-radius: 16px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);
    border: 1px solid transparent;
    
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg, 
        rgba(255, 255, 255, 0.6) 0%,
        rgba(248, 250, 252, 0.4) 50%,
        rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.05) 100%);
      opacity: 0;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      border-radius: 16px;
    }
    
    &:hover {
      transform: translateX(6px) translateY(-2px);
      box-shadow: 
        0 8px 25px rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.12),
        0 3px 10px rgba(0, 0, 0, 0.06);
      border-color: rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.15);
      
      &::before {
        opacity: 1;
      }
      
      .t-icon {
        transform: scale(1.15) rotate(5deg);
        color: var(--td-brand-color);
        filter: drop-shadow(0 2px 8px rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.3));
      }
      
      .t-menu__content {
        color: var(--td-brand-color);
        font-weight: 600;
      }
    }
    
    &.t-is-active {
      background: linear-gradient(135deg, 
        rgba(255, 255, 255, 0.9) 0%,
        rgba(248, 250, 252, 0.7) 30%,
        rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.12) 100%);
      color: var(--td-brand-color);
      border: 1px solid rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.25);
      border-left: 4px solid var(--td-brand-color);
      transform: translateX(4px);
      box-shadow: 
        0 10px 30px rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.15),
        0 4px 12px rgba(0, 0, 0, 0.08);
      
      .t-icon {
        color: var(--td-brand-color);
        transform: scale(1.1);
        filter: drop-shadow(0 3px 8px rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.4));
      }
      
      .t-menu__content {
        color: var(--td-brand-color);
        font-weight: 700;
      }
      
      &::after {
        content: '';
        position: absolute;
        right: 16px;
        top: 50%;
        transform: translateY(-50%);
        width: 6px;
        height: 6px;
        background: linear-gradient(135deg, 
          var(--td-brand-color) 0%, 
          var(--td-brand-color-8) 100%);
        border-radius: 50%;
        box-shadow: 
          0 0 12px rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.6),
          0 0 4px rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.8);
      }
    }
    
    .t-icon {
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      font-size: 19px;
      margin-right: 14px;
    }
    
    .t-menu__content {
      font-weight: 500;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      letter-spacing: 0.3px;
    }
  }
  
  // 子菜单样式 - 针对t-submenu和t-is-opened状态
  .t-submenu {
    margin: 8px 10px;
    border-radius: 18px;
    background: linear-gradient(135deg, 
      rgba(255, 255, 255, 0.3) 0%,
      rgba(248, 250, 252, 0.2) 100%);
    backdrop-filter: blur(15px);
    border: 1px solid rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.08);
    box-shadow: 0 2px 12px rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.04);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    
    &.t-is-opened {
      background: linear-gradient(135deg, 
        rgba(255, 255, 255, 0.6) 0%,
        rgba(248, 250, 252, 0.4) 50%,
        rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.08) 100%);
      border-color: rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.2);
      box-shadow: 0 6px 25px rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.12);
      
      .t-submenu__title {
        background: linear-gradient(135deg, 
          rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.08) 0%,
          rgba(var(--td-brand-color-8-rgb, 5, 148, 250), 0.06) 100%);
        
        .t-icon {
          color: var(--td-brand-color);
          transform: scale(1.05);
        }
        
        .t-submenu__title-content {
          color: var(--td-brand-color);
          font-weight: 600;
        }
      }
    }
    
    .t-submenu__title {
      border-radius: 18px;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      position: relative;
      overflow: hidden;
      padding: 12px 16px;
      
      &:hover {
        transform: translateX(4px) translateY(-1px);
        
        .t-icon {
          transform: scale(1.1) rotate(3deg);
          color: var(--td-brand-color);
          filter: drop-shadow(0 2px 6px rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.3));
        }
        
        .t-submenu__title-content {
          color: var(--td-brand-color);
          font-weight: 600;
        }
      }
      
      .t-icon {
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        font-size: 19px;
        margin-right: 14px;
      }
      
      .t-submenu__title-content {
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        font-weight: 500;
        letter-spacing: 0.3px;
      }
    }
    
    // t-menu__sub 子菜单内容区域
    .t-menu__sub {
      margin-left: 16px !important;
      padding-left: 0 !important;
      .t-submenu__content {
        margin-top: 8px;
        padding: 8px 16px 12px;
        background: linear-gradient(135deg, 
          rgba(255, 255, 255, 0.2) 0%,
          rgba(248, 250, 252, 0.1) 100%);
        border-radius: 0 0 18px 18px;
        
        .t-menu__item {
          margin: 4px 0 4px 20px;
          border-radius: 0 14px 14px 0;
          position: relative;
          background: rgba(255, 255, 255, 0.3);
          border: 1px solid rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.06);
          
          &::before {
            content: '';
            position: absolute;
            left: -16px;
            top: 50%;
            transform: translateY(-50%);
            width: 3px;
            height: 0;
            background: linear-gradient(180deg, 
              var(--td-brand-color) 0%, 
              var(--td-brand-color-8) 100%);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            border-radius: 2px;
          }
          
          &:hover {
            background: rgba(255, 255, 255, 0.6);
            border-color: rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.15);
            transform: translateX(3px);
            
            &::before {
              height: 70%;
              box-shadow: 0 0 8px rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.4);
            }
          }
          
          &.t-is-active {
            background: linear-gradient(90deg, 
              rgba(255, 255, 255, 0.8) 0%,
              rgba(248, 250, 252, 0.6) 30%,
              rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.1) 100%);
            border: 1px solid rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.2);
            border-left: none;
            transform: translateX(2px);
            box-shadow: 0 4px 16px rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.12);
            
            &::before {
              height: 85%;
              width: 4px;
              box-shadow: 
                0 0 10px rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.5),
                0 0 3px rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.7);
            }
            
            .t-icon {
              color: var(--td-brand-color);
              transform: scale(1.05);
            }
            
            .t-menu__content {
              color: var(--td-brand-color);
              font-weight: 600;
            }
          }
        }
      }
    }
  }
}

// 动画关键帧
@keyframes menuItemSlideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes iconBounce {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

// 响应式适配
@media (max-width: 768px) {
  :deep(.t-default-menu),
  :deep(.t-menu) {
    .t-menu__item {
      margin: 2px 8px;
      border-radius: 8px;
      
      .t-icon {
        font-size: 16px;
        margin-right: 8px;
      }
    }
    
    .t-submenu {
      margin: 2px 8px;
      
      .t-submenu__content {
        .t-menu__item {
          margin: 1px 0 1px 16px;
        }
      }
    }
  }
}

// 暗黑模式样式 - 使用全局主题状态选择器
:root[theme-mode='dark'] {
  /* 基础菜单项样式 */
  .enhanced-menu-item {
    background: var(--td-bg-color-container) !important;
    border-color: var(--td-border-level-1-color) !important;
    
    .t-icon {
      color: var(--td-text-color-secondary) !important;
    }
    
    .t-menu__content {
      color: var(--td-text-color-primary) !important;
    }
    
    /* 悬停状态 */
    &:hover {
      background: linear-gradient(90deg, 
        var(--td-bg-color-container-hover) 0%,
        var(--td-bg-color-page) 50%,
        rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.06) 100%) !important;
      border-color: var(--td-brand-color-3) !important;
      
      .t-icon {
        color: var(--td-brand-color) !important;
      }
      
      .t-menu__content {
        color: var(--td-brand-color) !important;
      }
    }
    
    /* 激活状态 */
    &.t-is-active {
      background: linear-gradient(90deg, 
        var(--td-bg-color-container) 0%,
        var(--td-bg-color-page) 30%,
        rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.08) 100%) !important;
      border-color: var(--td-brand-color-3) !important;
      box-shadow: 0 4px 16px rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.12) !important;
      
      &::before {
        background: linear-gradient(180deg, 
          var(--td-brand-color) 0%, 
          var(--td-brand-color-8) 100%) !important;
        box-shadow: 
          0 0 10px rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.5),
          0 0 3px rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.7) !important;
      }
      
      .t-icon {
        color: var(--td-brand-color) !important;
      }
      
      .t-menu__content {
        color: var(--td-brand-color) !important;
      }
    }
  }
  
  /* 子菜单项样式 */
  .enhanced-submenu-item {
    background: var(--td-bg-color-container) !important;
    
    &:hover {
      background: linear-gradient(90deg, 
        var(--td-bg-color-container-hover) 0%,
        rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.04) 100%) !important;
    }
    
    &.t-is-active {
      background: linear-gradient(90deg, 
        var(--td-bg-color-container-select) 0%,
        rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.06) 100%) !important;
      border-left: 3px solid var(--td-brand-color) !important;
    }
  }
  
  /* 折叠状态下的激活项 */
  .enhanced-menu-component[data-collapsed="true"] .t-menu__item.t-is-active {
    background: linear-gradient(135deg, 
      var(--td-bg-color-container) 0%,
      var(--td-bg-color-page) 30%,
      rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.10) 70%,
      rgba(var(--td-brand-color-8-rgb, 5, 148, 250), 0.06) 100%) !important;
    border: 2px solid var(--td-brand-color-3) !important;
    box-shadow: 
      0 0 16px rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.25),
      0 4px 12px var(--td-shadow-1),
      inset 0 1px 0 rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.15) !important;
  }
  
  /* 暗黑模式下的全局菜单样式增强 */
  .t-default-menu,
  .t-default-menu__inner,
  .t-menu {
    background: var(--td-bg-color-container) !important;
  }
  
  /* 暗黑模式下的全局菜单项样式 */
  .t-default-menu .t-menu__item,
  .t-default-menu__inner .t-menu__item,
  .t-menu .t-menu__item {
    color: var(--td-text-color-primary) !important;
    
    .t-icon {
      color: var(--td-text-color-secondary) !important;
    }
    
    .t-menu__content {
      color: var(--td-text-color-primary) !important;
    }
    
    &:hover {
      .t-icon {
        color: var(--td-brand-color) !important;
      }
      
      .t-menu__content {
        color: var(--td-brand-color) !important;
      }
    }
    
    &.t-is-active {
      .t-icon {
        color: var(--td-brand-color) !important;
      }
      
      .t-menu__content {
        color: var(--td-brand-color) !important;
      }
    }
  }
  
  /* 暗黑模式下的子菜单样式 */
  .t-default-menu .t-submenu,
  .t-default-menu__inner .t-submenu,
  .t-menu .t-submenu {
    .t-submenu__title {
      color: var(--td-text-color-primary) !important;
      
      .t-icon {
        color: var(--td-text-color-secondary) !important;
      }
      
      &:hover {
        color: var(--td-brand-color) !important;
        
        .t-icon {
          color: var(--td-brand-color) !important;
        }
      }
    }
  }
}

/* 优雅菜单样式 - 去边框，大气间距 */
.t-default-menu .t-menu__item,
.t-default-menu__inner .t-menu__item,
.t-menu .t-menu__item {
  margin: 8px 8px !important;
  border-radius: 12px !important;
  background: transparent !important;
  border: none !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  position: relative !important;
  overflow: hidden !important;
  padding: 12px 16px !important;
}

.t-default-menu .t-menu__item:hover,
.t-default-menu__inner .t-menu__item:hover,
.t-menu .t-menu__item:hover {
  background: linear-gradient(135deg, 
    rgba(255, 255, 255, 0.6) 0%,
    rgba(248, 250, 252, 0.4) 50%,
    rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.08) 100%) !important;
  backdrop-filter: blur(20px) !important;
  box-shadow: 0 4px 20px rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.1) !important;
}

.t-default-menu .t-menu__item:hover .t-icon,
.t-default-menu__inner .t-menu__item:hover .t-icon,
.t-menu .t-menu__item:hover .t-icon {
  color: var(--td-brand-color) !important;
  transition: all 0.3s ease !important;
}

.t-default-menu .t-menu__item:hover .t-menu__content,
.t-default-menu__inner .t-menu__item:hover .t-menu__content,
.t-menu .t-menu__item:hover .t-menu__content {
  color: var(--td-brand-color) !important;
  font-weight: 500 !important;
}

.t-default-menu .t-menu__item.t-is-active,
.t-default-menu__inner .t-menu__item.t-is-active,
.t-menu .t-menu__item.t-is-active {
  background: linear-gradient(135deg, 
    rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.1) 0%,
    rgba(var(--td-brand-color-8-rgb, 5, 148, 250), 0.08) 50%,
    rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.06) 100%) !important;
  backdrop-filter: blur(25px) !important;
  position: relative !important;
}

.t-default-menu .t-menu__item.t-is-active::before,
.t-default-menu__inner .t-menu__item.t-is-active::before,
.t-menu .t-menu__item.t-is-active::before {
  content: '' !important;
  position: absolute !important;
  left: 0 !important;
  top: 20% !important;
  bottom: 20% !important;
  width: 3px !important;
  background: linear-gradient(180deg, 
    var(--td-brand-color) 0%, 
    var(--td-brand-color-8) 50%,
    var(--td-brand-color) 100%) !important;
  border-radius: 0 3px 3px 0 !important;
  box-shadow: 0 0 12px rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.5) !important;
}

/* 暗黑模式下增强左侧指示器 */
:root[theme-mode='dark'] .t-default-menu .t-menu__item.t-is-active::before,
:root[theme-mode='dark'] .t-default-menu__inner .t-menu__item.t-is-active::before,
:root[theme-mode='dark'] .t-menu .t-menu__item.t-is-active::before {
  width: 4px !important;
  background: linear-gradient(180deg, 
    var(--td-brand-color) 0%, 
    var(--td-brand-color-8) 30%,
    var(--td-brand-color-light) 50%,
    var(--td-brand-color-8) 70%,
    var(--td-brand-color) 100%) !important;
  box-shadow: 
    0 0 16px rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.6),
    0 0 8px rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.8),
    inset 0 0 4px rgba(255, 255, 255, 0.2) !important;
  border-radius: 0 4px 4px 0 !important;
}

.t-default-menu .t-menu__item.t-is-active .t-icon,
.t-default-menu__inner .t-menu__item.t-is-active .t-icon,
.t-menu .t-menu__item.t-is-active .t-icon {
  color: var(--td-brand-color) !important;
}

.t-default-menu .t-menu__item.t-is-active .t-menu__content,
.t-default-menu__inner .t-menu__item.t-is-active .t-menu__content,
.t-menu .t-menu__item.t-is-active .t-menu__content {
  color: var(--td-brand-color) !important;
  font-weight: 600 !important;
}

/* 移除右侧指示器，保持简洁 */

.t-default-menu .t-submenu,
.t-default-menu__inner .t-submenu,
.t-menu .t-submenu {
  margin: 8px 8px !important;
  border-radius: 12px !important;
  background: transparent !important;
  border: none !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

.t-default-menu .t-submenu:hover,
.t-default-menu__inner .t-submenu:hover,
.t-menu .t-submenu:hover {
  background: linear-gradient(135deg, 
    rgba(255, 255, 255, 0.3) 0%,
    rgba(248, 250, 252, 0.2) 100%) !important;
  backdrop-filter: blur(15px) !important;
}

.t-default-menu .t-submenu.t-is-opened,
.t-default-menu__inner .t-submenu.t-is-opened,
.t-menu .t-submenu.t-is-opened {
  background: linear-gradient(135deg, 
    rgba(255, 255, 255, 0.4) 0%,
    rgba(248, 250, 252, 0.3) 50%,
    rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.06) 100%) !important;
  backdrop-filter: blur(20px) !important;
}

/* 子菜单标题样式优化 */
.t-default-menu .t-submenu .t-submenu__title,
.t-default-menu__inner .t-submenu .t-submenu__title,
.t-menu .t-submenu .t-submenu__title {
  padding: 12px 16px !important;
  border-radius: 8px !important;
  transition: all 0.3s ease !important;
}

.t-default-menu .t-submenu .t-submenu__title:hover,
.t-default-menu__inner .t-submenu .t-submenu__title:hover,
.t-menu .t-submenu .t-submenu__title:hover {
  background: rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.05) !important;
}

/* 子菜单容器样式 */
.t-default-menu .t-menu__sub,
.t-default-menu__inner .t-menu__sub,
.t-menu .t-menu__sub {
  margin-left: 16px !important;
  padding-left: 0 !important;
}

/* 子菜单项样式简化 */
.t-default-menu .t-submenu .t-menu__item,
.t-default-menu__inner .t-submenu .t-menu__item,
.t-menu .t-submenu .t-menu__item {
  // margin: 4px 8px 4px 16px !important;
  padding: 8px 12px !important;
  border-radius: 6px !important;
  background: transparent !important;
  border: none !important;
}

.t-default-menu .t-submenu .t-menu__item:hover,
.t-default-menu__inner .t-submenu .t-menu__item:hover,
.t-menu .t-submenu .t-menu__item:hover {
  background: rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.08) !important;
}

.t-default-menu .t-submenu .t-menu__item.t-is-active,
.t-default-menu__inner .t-submenu .t-menu__item.t-is-active,
.t-menu .t-submenu .t-menu__item.t-is-active {
  background: rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.12) !important;
  color: var(--td-brand-color) !important;
  font-weight: 600 !important;
}

/* 菜单容器优化 */
.t-default-menu, .t-default-menu__inner, .t-menu {
  background: transparent !important;
  padding: 8px 0 !important;
}

/* 全局菜单项文字样式 */
.t-default-menu .t-menu__content,
.t-default-menu__inner .t-menu__content,
.t-menu .t-menu__content {
  font-size: 14px !important;
  font-weight: 400 !important;
  letter-spacing: 0.2px !important;
  transition: all 0.3s ease !important;
}

/* 全局菜单图标样式 */
.t-default-menu .t-icon,
.t-default-menu__inner .t-icon,
.t-menu .t-icon {
  font-size: 16px !important;
  margin-right: 12px !important;
  transition: all 0.3s ease !important;
}

/* 折叠状态下的菜单样式优化 */
.t-default-menu--collapsed .t-menu__item,
.t-default-menu__inner--collapsed .t-menu__item,
.t-menu--collapsed .t-menu__item,
.enhanced-menu-component.t-menu--collapsed .t-menu__item,
.enhanced-menu-component[data-collapsed="true"] .t-menu__item {
  padding: 12px !important;
  justify-content: center !important;
  text-align: center !important;
  margin: 8px auto !important;
  width: 40px !important;
  height: 40px !important;
  display: flex !important;
  align-items: center !important;
}

.t-default-menu--collapsed .t-menu__item .t-icon,
.t-default-menu__inner--collapsed .t-menu__item .t-icon,
.t-menu--collapsed .t-menu__item .t-icon,
.enhanced-menu-component.t-menu--collapsed .t-menu__item .t-icon,
.enhanced-menu-component[data-collapsed="true"] .t-menu__item .t-icon {
  margin-right: 0 !important;
  margin-left: 0 !important;
  display: block !important;
  text-align: center !important;
  font-size: 18px !important;
}

/* 折叠状态下隐藏左侧激活指示器 - 强制隐藏所有伪元素 */
.t-default-menu--collapsed .t-menu__item::before,
.t-default-menu__inner--collapsed .t-menu__item::before,
.t-menu--collapsed .t-menu__item::before,
.enhanced-menu-component.t-menu--collapsed .t-menu__item::before,
.enhanced-menu-component[data-collapsed="true"] .t-menu__item::before,
.t-default-menu--collapsed .t-menu__item.t-is-active::before,
.t-default-menu__inner--collapsed .t-menu__item.t-is-active::before,
.t-menu--collapsed .t-menu__item.t-is-active::before,
.enhanced-menu-component.t-menu--collapsed .t-menu__item.t-is-active::before,
.enhanced-menu-component[data-collapsed="true"] .t-menu__item.t-is-active::before {
  display: none !important;
  content: none !important;
  visibility: hidden !important;
  opacity: 0 !important;
}

/* 折叠状态下的激活状态样式 */
.t-default-menu--collapsed .t-menu__item.t-is-active,
.t-default-menu__inner--collapsed .t-menu__item.t-is-active,
.t-menu--collapsed .t-menu__item.t-is-active,
.enhanced-menu-component.t-menu--collapsed .t-menu__item.t-is-active,
.enhanced-menu-component[data-collapsed="true"] .t-menu__item.t-is-active {
  background: linear-gradient(135deg, 
    rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.15) 0%,
    rgba(var(--td-brand-color-8-rgb, 5, 148, 250), 0.1) 100%) !important;
  border: 2px solid rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.3) !important;
  border-radius: 50% !important;
  width: 40px !important;
  height: 40px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  margin: 8px auto !important;
  position: relative !important;
  overflow: visible !important;
  text-align: center !important;
  padding: 0 !important;
  box-sizing: border-box !important;
}

.t-default-menu--collapsed .t-menu__item.t-is-active .t-icon,
.t-default-menu__inner--collapsed .t-menu__item.t-is-active .t-icon,
.t-menu--collapsed .t-menu__item.t-is-active .t-icon,
.enhanced-menu-component.t-menu--collapsed .t-menu__item.t-is-active .t-icon,
.enhanced-menu-component[data-collapsed="true"] .t-menu__item.t-is-active .t-icon {
  color: var(--td-brand-color) !important;
  font-size: 18px !important;
  margin: 0 !important;
  display: block !important;
  text-align: center !important;
  line-height: 1 !important;
  position: absolute !important;
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%) !important;
}

/* 折叠状态下的悬停效果 */
.t-default-menu--collapsed .t-menu__item:hover,
.t-default-menu__inner--collapsed .t-menu__item:hover,
.t-menu--collapsed .t-menu__item:hover,
.enhanced-menu-component.t-menu--collapsed .t-menu__item:hover,
.enhanced-menu-component[data-collapsed="true"] .t-menu__item:hover {
  background: linear-gradient(135deg, 
    rgba(255, 255, 255, 0.8) 0%,
    rgba(248, 250, 252, 0.6) 50%,
    rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.1) 100%) !important;
  border-radius: 50% !important;
  width: 40px !important;
  height: 40px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  margin: 8px auto !important;
  box-shadow: 0 4px 16px rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.15) !important;
}

.t-default-menu--collapsed .t-menu__item:hover .t-icon,
.t-default-menu__inner--collapsed .t-menu__item:hover .t-icon,
.t-menu--collapsed .t-menu__item:hover .t-icon,
.enhanced-menu-component.t-menu--collapsed .t-menu__item:hover .t-icon,
.enhanced-menu-component[data-collapsed="true"] .t-menu__item:hover .t-icon {
  color: var(--td-brand-color) !important;
  transform: scale(1.1) !important;
  font-size: 18px !important;
}

/* 折叠状态下隐藏子菜单 */
.t-default-menu--collapsed .t-submenu,
.t-default-menu__inner--collapsed .t-submenu,
.t-menu--collapsed .t-submenu,
.enhanced-menu-component.t-menu--collapsed .t-submenu,
.enhanced-menu-component[data-collapsed="true"] .t-submenu {
  display: none !important;
}

/* 折叠状态下隐藏所有可能的伪元素装饰 */
.t-default-menu--collapsed .t-menu__item::after,
.t-default-menu__inner--collapsed .t-menu__item::after,
.t-menu--collapsed .t-menu__item::after,
.enhanced-menu-component.t-menu--collapsed .t-menu__item::after,
.enhanced-menu-component[data-collapsed="true"] .t-menu__item::after,
.t-default-menu--collapsed .t-menu__item.t-is-active::after,
.t-default-menu__inner--collapsed .t-menu__item.t-is-active::after,
.t-menu--collapsed .t-menu__item.t-is-active::after,
.enhanced-menu-component.t-menu--collapsed .t-menu__item.t-is-active::after,
.enhanced-menu-component[data-collapsed="true"] .t-menu__item.t-is-active::after {
  display: none !important;
  content: none !important;
  visibility: hidden !important;
  opacity: 0 !important;
}

/* 折叠状态下强制菜单项居中布局 */
.t-default-menu--collapsed .t-menu__item .t-menu__content,
.t-default-menu__inner--collapsed .t-menu__item .t-menu__content,
.t-menu--collapsed .t-menu__item .t-menu__content,
.enhanced-menu-component.t-menu--collapsed .t-menu__item .t-menu__content,
.enhanced-menu-component[data-collapsed="true"] .t-menu__item .t-menu__content {
  display: none !important;
}

/* 折叠状态下的通用图标居中规则 */
.t-default-menu--collapsed .t-menu__item .t-icon,
.t-default-menu__inner--collapsed .t-menu__item .t-icon,
.t-menu--collapsed .t-menu__item .t-icon,
.enhanced-menu-component.t-menu--collapsed .t-menu__item .t-icon,
.enhanced-menu-component[data-collapsed="true"] .t-menu__item .t-icon {
  position: absolute !important;
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%) !important;
  margin: 0 !important;
  display: block !important;
  text-align: center !important;
  line-height: 1 !important;
}

/* 暗黑模式下的折叠状态样式优化 */
:root[theme-mode='dark'] {
  .t-default-menu--collapsed .t-menu__item,
  .t-default-menu__inner--collapsed .t-menu__item,
  .t-menu--collapsed .t-menu__item,
  .enhanced-menu-component.t-menu--collapsed .t-menu__item,
  .enhanced-menu-component[data-collapsed="true"] .t-menu__item {
    background: rgba(var(--td-bg-color-container), 0.4) !important;
    border: 1px solid rgba(var(--td-border-level-1-color), 0.2) !important;
  }
  
  .t-default-menu--collapsed .t-menu__item:hover,
  .t-default-menu__inner--collapsed .t-menu__item:hover,
  .t-menu--collapsed .t-menu__item:hover,
  .enhanced-menu-component.t-menu--collapsed .t-menu__item:hover,
  .enhanced-menu-component[data-collapsed="true"] .t-menu__item:hover {
    background: linear-gradient(135deg, 
      rgba(var(--td-bg-color-container), 0.8) 0%,
      rgba(var(--td-bg-color-page), 0.6) 50%,
      rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.15) 100%) !important;
    border-color: rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.3) !important;
    box-shadow: 0 4px 16px rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.2) !important;
  }
  
  .t-default-menu--collapsed .t-menu__item.t-is-active,
  .t-default-menu__inner--collapsed .t-menu__item.t-is-active,
  .t-menu--collapsed .t-menu__item.t-is-active,
  .enhanced-menu-component.t-menu--collapsed .t-menu__item.t-is-active,
  .enhanced-menu-component[data-collapsed="true"] .t-menu__item.t-is-active {
    background: linear-gradient(135deg, 
      rgba(var(--td-bg-color-container), 0.9) 0%,
      rgba(var(--td-bg-color-page), 0.7) 30%,
      rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.12) 70%,
      rgba(var(--td-brand-color-8-rgb, 5, 148, 250), 0.08) 100%) !important;
    border: 2px solid rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.3) !important;
    box-shadow: 
      0 0 16px rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.25),
      0 4px 12px rgba(var(--td-shadow-color-rgb, 0, 0, 0), 0.15),
      inset 0 1px 0 rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.15) !important;
  }
  
  /* 暗黑模式下的全局菜单样式增强 */
  .t-default-menu,
  .t-default-menu__inner,
  .t-menu {
    background: var(--td-bg-color-container) !important;
  }
  
  /* 暗黑模式下的全局菜单项样式 */
  .t-default-menu .t-menu__item,
  .t-default-menu__inner .t-menu__item,
  .t-menu .t-menu__item {
    color: var(--td-text-color-primary) !important;
    
    .t-icon {
      color: var(--td-text-color-secondary) !important;
    }
    
    .t-menu__content {
      color: var(--td-text-color-primary) !important;
    }
    
    &:hover {
      .t-icon {
        color: var(--td-brand-color) !important;
      }
      
      .t-menu__content {
        color: var(--td-brand-color) !important;
      }
    }
    
    &.t-is-active {
      .t-icon {
        color: var(--td-brand-color) !important;
      }
      
      .t-menu__content {
        color: var(--td-brand-color) !important;
      }
    }
  }
  
  /* 暗黑模式下的子菜单样式 */
  .t-default-menu .t-submenu,
  .t-default-menu__inner .t-submenu,
  .t-menu .t-submenu {
    .t-submenu__title {
      color: var(--td-text-color-primary) !important;
      
      .t-icon {
        color: var(--td-text-color-secondary) !important;
      }
      
      &:hover {
        color: var(--td-brand-color) !important;
        
        .t-icon {
          color: var(--td-brand-color) !important;
        }
      }
    }
  }
}
</style>

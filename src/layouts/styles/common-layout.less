// 共同的基础布局样式
.base-layout {
  height: 100vh;
  overflow: hidden;
  background: linear-gradient(135deg, #f5f7fa 0%, #ffffff 100%);
  position: relative;
  
  // 添加背景装饰
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
      radial-gradient(circle at 20% 80%, rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.02) 0%, transparent 60%),
      radial-gradient(circle at 80% 20%, rgba(var(--td-brand-color-8-rgb, 5, 148, 250), 0.015) 0%, transparent 60%),
      radial-gradient(circle at 40% 40%, rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.008) 0%, transparent 60%);
    pointer-events: none;
    z-index: 0;
  }
  
  // 顶部融合渐变
  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 80px;
    background: linear-gradient(180deg, 
      rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.01) 0%, 
      transparent 100%);
    pointer-events: none;
    z-index: 0;
  }
}

// Header共同样式
.base-layout-header {
  background: transparent;
  border-bottom: none;
  position: sticky;
  top: 0;
  z-index: 100;
  width: 100%;
  
  :deep(.t-layout__header) {
    height: 64px;
    line-height: 64px;
    padding: 0;
    background: transparent;
    border-bottom: none;
    width: 100%;
  }
}

// 主内容区域共同样式
.base-layout-main-content {
  overflow: hidden;
  background: transparent;
  position: relative;
  z-index: 1;
  
  :deep(.t-layout__content) {
    padding: 0;
    width: 100%;
    height: 100%;
    background: transparent;
  }
}

// 有侧边栏的内容结构
.content-with-sidebar {
  display: flex;
  height: 100%;
  width: 100%;
}

// 侧边栏共同样式
.base-layout-sidebar {
  width: 240px;
  height: 100%;
  background: linear-gradient(135deg, 
    rgba(248, 250, 252, 0.6) 0%,
    rgba(241, 245, 249, 0.4) 50%,
    rgba(248, 250, 252, 0.3) 100%);
  backdrop-filter: blur(25px) saturate(1.1);
  overflow: visible;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  border-radius: 0 16px 16px 0;
  margin: 8px 0 8px 8px;
  flex-shrink: 0;
  
  // 折叠状态样式
  &.sidebar-collapsed {
    width: 72px; // 64px + 8px margin
  }
  
  // 现代化装饰效果
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
      radial-gradient(ellipse at top left, rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.05) 0%, transparent 50%),
      radial-gradient(ellipse at bottom right, rgba(var(--td-brand-color-8-rgb, 5, 148, 250), 0.03) 0%, transparent 50%);
    border-radius: 0 16px 16px 0;
    pointer-events: none;
    z-index: 0;
  }
  
  &::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 1px;
    height: 100%;
    background: linear-gradient(180deg,
      transparent 0%,
      rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.12) 20%,
      rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.18) 50%,
      rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.12) 80%,
      transparent 100%);
    border-radius: 0 16px 16px 0;
    box-shadow: 1px 0 8px rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.08);
    z-index: 2;
  }
}

// 内容区域共同样式
.base-layout-content-area {
  flex: 1;
  height: 100%;
  overflow-y: auto;
  background: transparent;
  position: relative;
  
  // 优化内层TDesign Layout组件的滚动，避免双滚动条但保持内容可滚动
  :deep(.t-layout) {
    height: 100%;
    overflow: visible; // 允许内容正常滚动
  }
  
  :deep(.t-content) {
    overflow: visible;
    height: auto;
    max-height: none; // 确保内容可以正常展开
  }
  
  // 只禁用可能重复的滚动条样式，但保持滚动功能
  :deep(.tdesign-starter-content-layout) {
    overflow: visible;
    height: auto;
    
    // 隐藏可能的重复滚动条样式
    &::-webkit-scrollbar {
      display: none;
    }
    scrollbar-width: none; // Firefox
    -ms-overflow-style: none; // IE
  }
  
  // 现代化滚动条样式
  &::-webkit-scrollbar {
    width: 8px;
  }
  
  &::-webkit-scrollbar-track {
    background: rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.05);
    border-radius: 4px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, 
      rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.3) 0%, 
      rgba(var(--td-brand-color-8-rgb, 5, 148, 250), 0.2) 100%);
    border-radius: 4px;
    border: 2px solid transparent;
    background-clip: content-box;
    
    &:hover {
      background: linear-gradient(135deg, 
        rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.5) 0%, 
        rgba(var(--td-brand-color-8-rgb, 5, 148, 250), 0.4) 100%);
    }
  }
}

// HeaderOnly模式的内容区域样式
.base-layout-content-area-headeronly {
  height: calc(100vh - 64px - 60px); // 减去Header(64px)和Footer(60px)的高度
  overflow-y: auto;
  background: transparent;
  position: relative;
  z-index: 1;
  
  // 为内容区域添加现代化滚动条样式
  &::-webkit-scrollbar {
    width: 8px;
  }
  
  &::-webkit-scrollbar-track {
    background: rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.05);
    border-radius: 4px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, 
      rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.3) 0%, 
      rgba(var(--td-brand-color-8-rgb, 5, 148, 250), 0.2) 100%);
    border-radius: 4px;
    border: 2px solid transparent;
    background-clip: content-box;
    
    &:hover {
      background: linear-gradient(135deg, 
        rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.5) 0%, 
        rgba(var(--td-brand-color-8-rgb, 5, 148, 250), 0.4) 100%);
    }
  }
  
  // 内容区域的基础样式
  :deep(.t-layout__content) {
    padding: 0;
    width: 100%;
    min-height: 100%;
    background: transparent;
  }
}

// Footer共同样式
.base-layout-footer {
  height: 60px;
  width: 100vw;
  background: transparent;
  border-top: none;
  position: relative;
  z-index: 1000;
  
  :deep(.t-layout__footer) {
    height: 60px;
    line-height: 60px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    background: transparent;
    width: 100vw;
    position: relative;
    overflow: visible;
  }
}

// 底部融合渐变
.bottom-gradient {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 80px;
  background: linear-gradient(0deg, 
    rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.01) 0%, 
    transparent 100%);
  pointer-events: none;
  z-index: 0;
}

// 响应式适配
@media (max-width: 768px) {
  .base-layout-header {
    :deep(.t-layout__header) {
      height: 56px;
      line-height: 56px;
    }
  }
  
  .base-layout-footer {
    height: 50px;
    
    :deep(.t-layout__footer) {
      height: 50px;
      line-height: 50px;
    }
  }
  
  .base-layout-sidebar {
    width: 200px;
    margin: 4px 0 4px 4px;
    border-radius: 0 12px 12px 0;
    
    &.sidebar-collapsed {
      width: 60px; // 56px + 4px margin
    }
  }
  
  .base-layout-content-area-headeronly {
    height: calc(100vh - 56px - 50px); // 移动端Footer高度调整为50px
  }
}

// 暗色主题适配
@media (prefers-color-scheme: dark) {
  .base-layout {
    background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
    
    &::before {
      background: 
        radial-gradient(circle at 20% 80%, rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.015) 0%, transparent 60%),
        radial-gradient(circle at 80% 20%, rgba(var(--td-brand-color-8-rgb, 5, 148, 250), 0.01) 0%, transparent 60%),
        radial-gradient(circle at 40% 40%, rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.005) 0%, transparent 60%);
    }
  }
  
  .base-layout-sidebar {
    background: linear-gradient(135deg, 
      rgba(30, 41, 59, 0.6) 0%,
      rgba(15, 23, 42, 0.4) 50%,
      rgba(30, 41, 59, 0.3) 100%);
      
    &::before {
      background: 
        radial-gradient(ellipse at top left, rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.03) 0%, transparent 50%),
        radial-gradient(ellipse at bottom right, rgba(var(--td-brand-color-8-rgb, 5, 148, 250), 0.02) 0%, transparent 50%);
    }
  }
  
  .bottom-gradient {
    background: linear-gradient(0deg, 
      rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.02) 0%, 
      transparent 100%);
  }
  
  .base-layout-content-area::-webkit-scrollbar-track,
  .base-layout-content-area-headeronly::-webkit-scrollbar-track {
    background: rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.1);
  }
  
  .base-layout-content-area::-webkit-scrollbar-thumb,
  .base-layout-content-area-headeronly::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, 
      rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.4) 0%, 
      rgba(var(--td-brand-color-8-rgb, 5, 148, 250), 0.3) 100%);
    
    &:hover {
      background: linear-gradient(135deg, 
        rgba(var(--td-brand-color-rgb, 0, 82, 217), 0.6) 0%, 
        rgba(var(--td-brand-color-8-rgb, 5, 148, 250), 0.5) 100%);
    }
  }
} 
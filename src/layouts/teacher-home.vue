<template>
  <div class="teacher-home-layout">
    <t-layout>
      <t-header><layout-header /></t-header>
      <t-content class="teacher-content">
        <router-view />
      </t-content>
    </t-layout>
  </div>
</template>

<script setup lang="ts">
import { Layout as TLayout, Header as THeader, Content as TContent } from 'tdesign-vue-next';
import LayoutHeader from './components/LayoutHeader.vue';
</script>

<style lang="less" scoped>
.teacher-home-layout {
  height: 100vh;
}

.teacher-content {
  padding: 20px;
  background-color: #f5f5f5;
  height: calc(100vh - 64px);
  overflow-y: auto;
}
</style> 
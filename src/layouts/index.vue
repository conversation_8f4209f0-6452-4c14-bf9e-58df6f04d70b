<template>
  <base-layout
    layout-mode="standard"
    :show-sidebar="true"
  >
    <!-- Header组件 -->
    <template #header>
      <layout-header />
    </template>
    
    <!-- 侧边栏组件 -->
    <template #sidebar>
      <layout-side-nav />
    </template>
    
    <!-- 内容区域 -->
    <template #content>
      <layout-content />
    </template>
    
    <!-- Footer组件 -->
    <template #footer>
      <enhanced-footer />
    </template>
    
    <!-- 设置面板 -->
    <template #setting>
      <setting-com />
    </template>
  </base-layout>
</template>

<script setup lang="ts">
import BaseLayout from './components/BaseLayout.vue';
import LayoutContent from './components/LayoutContent.vue';
import LayoutHeader from './components/LayoutHeader.vue';
import LayoutSideNav from './components/LayoutSideNav.vue';
import EnhancedFooter from './components/EnhancedFooter.vue';
import SettingCom from './setting.vue';
</script>



<template>
  <div class="class-card" @click="handleCardClick">
    <div class="card-header">
      <div class="class-info">
        <h3 class="class-name">
          {{ classData.className }}
          <t-tag theme="primary" size="small" class="grade-tag">{{ classData.entranceYear }}级</t-tag>
          <t-tag theme="success" size="small" class="count-tag">{{ classData.studentCount }}人</t-tag>
        </h3>
      </div>
      <div class="card-actions" @click.stop>
        <t-dropdown :options="actionOptions" @click="handleActionClick">
          <t-button theme="default" shape="square" variant="text">
            <template #icon>
              <t-icon name="more" />
            </template>
          </t-button>
        </t-dropdown>
      </div>
    </div>

    <!-- 教师信息单独放在header外面，保持完整宽度 -->
    <div class="teacher-info">
      <t-avatar size="small" class="teacher-avatar">
        {{ teacherData.name.charAt(0) }}
      </t-avatar>
      <div class="teacher-details">
        <span class="teacher-name">{{ teacherData.name }}</span>
        <div class="teacher-meta">
          <span class="teacher-title">{{ teacherData.title }}</span>
          <t-tag v-if="teacherData.role" theme="primary" size="small" class="role-tag">{{ teacherData.role }}</t-tag>
        </div>
      </div>
    </div>

    <div class="card-content">
      <!-- 上课安排信息 -->
      <div class="schedule-section" v-if="scheduleData.length > 0">
        <div class="section-title">
          <t-icon name="time" size="16px" />
          <span>上课安排</span>
        </div>
        <div class="schedule-info">
          <div class="schedule-item" v-for="(item, index) in scheduleData" :key="index">
            <div class="schedule-time">{{ item.time }}</div>
            <div class="schedule-location">{{ item.location }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部区域 -->
    <div class="card-footer">
      <!-- 课程统计信息 -->
      <div class="stats-section">
        <div class="stat-item">
          <span class="stat-label">总学时</span>
          <span class="stat-value">{{ worklistData.totalHours }}h</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">周学时</span>
          <span class="stat-value">{{ worklistData.weekHours }}h</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">授课周数</span>
          <span class="stat-value">{{ worklistData.teachWeek }}周</span>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="action-buttons">
        <t-button theme="primary" variant="text" size="small" @click.stop="handleManageStudents">
          <template #icon>
            <t-icon name="user-setting" />
          </template>
          学生管理
        </t-button>
        <t-button theme="default" variant="text" size="small" @click.stop="handleImportStudents">
          <template #icon>
            <t-icon name="upload" />
          </template>
          导入学生
        </t-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import {
  Tag as TTag,
  Button as TButton,
  Icon as TIcon,
  Avatar as TAvatar,
  Dropdown as TDropdown,
  MessagePlugin
} from 'tdesign-vue-next'
import type { WorklistItem } from '@/api/base/classes'

// 定义 props
interface Props {
  worklistData: WorklistItem
}

const props = defineProps<Props>()

// 定义事件
const emit = defineEmits<{
  cardClick: [classId: string | number]
  manageStudents: [classId: string | number]
  importStudents: [classId: string | number]
  editClass: [classId: string | number]
  deleteClass: [classId: string | number]
}>()

// 计算属性 - 班级数据
const classData = computed(() => ({
  className: props.worklistData.className,
  entranceYear: props.worklistData.entranceYear || '',
  studentCount: props.worklistData.studentCount
}))

// 计算属性 - 教师数据
const teacherData = computed(() => ({
  name: props.worklistData.teacherName,
  title: props.worklistData.teacherTitle,
  academyName: props.worklistData.teacherAcademyName,
  role: props.worklistData.teacherRole,
  avatar: '' // 暂时为空，后续可以添加头像功能
}))

// 计算属性 - 上课安排数据
const scheduleData = computed(() => {
  const schedule = props.worklistData.scheduleInfo
  if (Array.isArray(schedule)) {
    return schedule
  } else if (schedule) {
    return [schedule]
  }
  return []
})

// 下拉菜单选项
const actionOptions = [
  {
    content: '编辑班级',
    value: 'edit'
  },
  {
    content: '删除班级', 
    value: 'delete'
  }
]

// 事件处理函数
const handleCardClick = () => {
  emit('cardClick', props.worklistData.classId)
}

const handleManageStudents = () => {
  emit('manageStudents', props.worklistData.classId)
}

const handleImportStudents = () => {
  emit('importStudents', props.worklistData.classId)
}

const handleActionClick = (data: any) => {
  const { value } = data
  switch (value) {
    case 'edit':
      emit('editClass', props.worklistData.classId)
      break
    case 'delete':
      emit('deleteClass', props.worklistData.classId)
      break
    default:
      break
  }
}
</script>

<style lang="less" scoped>
.class-card {
  background: var(--td-bg-color-container);
  border: 1px solid var(--td-border-level-1-color);
  border-radius: 8px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  height: 100%;
  display: flex;
  flex-direction: column;

  &:hover {
    border-color: var(--td-brand-color);
    box-shadow: 0 4px 16px rgba(0, 82, 217, 0.1);
    transform: translateY(-2px);
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 16px;

    .class-info {
      flex: 1;

      .class-name {
        margin: 0;
        font-size: 18px;
        font-weight: 600;
        color: var(--td-text-color-primary);
        line-height: 1.4;
        display: flex;
        align-items: center;
        gap: 8px;
        flex-wrap: wrap;

        .grade-tag,
        .count-tag {
          font-size: 12px;
          margin-left: 0;
        }
      }


    }

    .card-actions {
      flex-shrink: 0;
    }
  }

  .teacher-info {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 16px;
    padding: 12px;
    background: var(--td-bg-color-page);
    border-radius: 6px;
    border-left: 3px solid var(--td-brand-color);

    .teacher-avatar {
      flex-shrink: 0;
    }

    .teacher-details {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 4px;

      .teacher-name {
        color: var(--td-text-color-primary);
        font-weight: 500;
        font-size: 14px;
      }

      .teacher-meta {
        display: flex;
        align-items: center;
        gap: 8px;
        flex-wrap: wrap;

        .teacher-title {
          color: var(--td-text-color-secondary);
          font-size: 12px;
        }

        .role-tag {
          font-size: 10px;
          padding: 2px 6px;
        }
      }
    }
  }

  .card-content {
    flex: 1;
    margin-bottom: 16px;

    .schedule-section {
      .section-title {
        display: flex;
        align-items: center;
        gap: 6px;
        font-size: 14px;
        font-weight: 500;
        color: var(--td-text-color-secondary);
        margin-bottom: 8px;
      }
    }

    .schedule-info {
      .schedule-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 13px;
        color: var(--td-text-color-secondary);
        margin-bottom: 4px;
        padding: 4px 8px;
        background: var(--td-bg-color-page);
        border-radius: 4px;

        &:last-child {
          margin-bottom: 0;
        }

        .schedule-time {
          color: var(--td-text-color-primary);
          font-weight: 500;
        }

        .schedule-location {
          color: var(--td-text-color-secondary);
        }
      }
    }


  }

  .card-footer {
    border-top: 1px solid var(--td-border-level-1-color);
    padding-top: 16px;
    flex-shrink: 0;

    .stats-section {
      display: flex;
      justify-content: space-between;
      padding: 12px;
      background: var(--td-bg-color-page);
      border-radius: 6px;
      margin-bottom: 16px;

      .stat-item {
        text-align: center;

        .stat-label {
          display: block;
          font-size: 12px;
          color: var(--td-text-color-secondary);
          margin-bottom: 4px;
        }

        .stat-value {
          font-size: 14px;
          font-weight: 600;
          color: var(--td-text-color-primary);
        }
      }
    }

    .action-buttons {
      display: flex;
      justify-content: space-between;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .class-card {
    padding: 16px;

    .card-header .class-info .class-name {
      font-size: 16px;
    }

    .stats-section {
      padding: 8px;

      .stat-item .stat-value {
        font-size: 13px;
      }
    }

    .card-footer {
      .action-buttons {
        flex-direction: column;
        gap: 8px;

        .t-button {
          width: 100%;
        }
      }
    }
  }
}
</style> 
/* 全屏弹窗CSS覆盖文件 - 确保最高优先级 */

/* 强制全屏弹窗容器样式 */
.full-screen-dialog {
  position: fixed !important;
  inset: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  z-index: 3000 !important;
  margin: 0 !important;
  padding: 0 !important;
}

/* 强制TDesign Dialog包装器样式 */
.t-dialog__wrap.full-screen-dialog,
.t-dialog__wrap:has(.full-screen-dialog) {
  position: fixed !important;
  inset: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  z-index: 3000 !important;
  margin: 0 !important;
  padding: 0 !important;
  transform: none !important;
}

/* 强制Dialog本身的样式 */
.full-screen-dialog .t-dialog,
.t-dialog.full-screen-dialog {
  position: fixed !important;
  inset: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  z-index: 3000 !important;
  margin: 0 !important;
  padding: 0 !important;
  border-radius: 0 !important;
  max-width: none !important;
  max-height: none !important;
  transform: none !important;
  display: flex !important;
  flex-direction: column !important;
}

/* 覆盖层样式 */
.t-overlay {
  position: fixed !important;
  inset: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  z-index: 999998 !important;
  background: rgb(0 0 0 / 60%) !important;
}

/* 防止页面滚动 */
body.dialog-open {
  overflow: hidden !important;
  position: fixed !important;
  width: 100% !important;
  height: 100% !important;
}

/* 确保弹窗内容区域正确布局 */
.full-screen-dialog .t-dialog__header {
  flex-shrink: 0 !important;
  position: relative !important;
  z-index: 1 !important;
}

.full-screen-dialog .t-dialog__body {
  flex: 1 !important;
  overflow: hidden !important;
  position: relative !important;
  z-index: 1 !important;
  display: flex !important;
  flex-direction: column !important;
}

.full-screen-dialog .t-dialog__footer {
  flex-shrink: 0 !important;
  position: relative !important;
  z-index: 1 !important;
}

/* 确保下拉菜单能够正常显示 */
.full-screen-dialog .t-select__popup,
.full-screen-dialog .t-popup,
.full-screen-dialog .t-select-option,
.full-screen-dialog .t-dropdown,
.full-screen-dialog .t-dropdown__menu {
  z-index: 1000001 !important;
}

/* TDesign组件的弹出层 */
.t-popup__content {
  z-index: 1000001 !important;
}

.t-select__popup-reference {
  z-index: 1000001 !important;
}

/* 全局下拉菜单层级调整 */
.t-popup-container .t-popup {
  z-index: 1000001 !important;
}

.t-select-option {
  z-index: 1000001 !important;
}

/* 兜底样式 - 使用更高的特异性 */
html body .full-screen-dialog {
  position: fixed !important;
  inset: 0 !important;
  z-index: 3000 !important;
}

html body .full-screen-dialog .t-dialog {
  position: fixed !important;
  inset: 0 !important;
  z-index: 3000 !important;
}

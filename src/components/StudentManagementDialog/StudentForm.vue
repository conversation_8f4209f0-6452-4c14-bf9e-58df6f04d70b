<template>
  <t-dialog
    v-model:visible="formVisible"
    :header="isEdit ? '编辑学生' : '新增学生'"
    width="700px"
    height="auto"
    :confirm-btn="{ content: '确定', loading: submitting }"
    @confirm="handleSubmit"
    @cancel="handleCancel"
  >
    <div class="student-form-container">
      <t-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="80px"
        label-align="left"
        colon
      >
        <t-row :gutter="12">
          <t-col :span="12">
            <t-form-item label="学号" name="studentNumber">
              <t-input
                v-model="formData.studentNumber"
                placeholder="请输入学号"
                :disabled="isEdit"
                clearable
              />
            </t-form-item>
          </t-col>
          <t-col :span="12">
            <t-form-item label="姓名" name="name">
              <t-input
                v-model="formData.name"
                placeholder="请输入姓名"
                clearable
              />
            </t-form-item>
          </t-col>
        </t-row>

        <t-row :gutter="12">
          <t-col :span="12">
            <t-form-item label="性别" name="gender">
              <t-select
                v-model="formData.gender"
                placeholder="请选择性别"
                clearable
              >
                <t-option :value="1" label="男" />
                <t-option :value="2" label="女" />
              </t-select>
            </t-form-item>
          </t-col>
          <t-col :span="12">
            <t-form-item label="入学年份" name="entranceYear">
              <t-input
                v-model="formData.entranceYear"
                placeholder="请输入入学年份"
                clearable
              />
            </t-form-item>
          </t-col>
        </t-row>

        <t-row :gutter="12">
          <t-col :span="12">
            <t-form-item label="手机号" name="phone">
              <t-input
                v-model="formData.phone"
                placeholder="请输入手机号"
                clearable
              />
            </t-form-item>
          </t-col>
          <t-col :span="12">
            <t-form-item label="学籍状态" name="studentStatus">
              <t-select
                v-model="formData.studentStatus"
                placeholder="请选择学籍状态"
                clearable
              >
                <t-option :value="0" label="在读" />
                <t-option :value="1" label="休学" />
                <t-option :value="2" label="退学" />
                <t-option :value="3" label="毕业" />
              </t-select>
            </t-form-item>
          </t-col>
        </t-row>

        <t-form-item label="邮箱" name="email">
          <t-input
            v-model="formData.email"
            placeholder="请输入邮箱地址"
            clearable
          />
        </t-form-item>
      </t-form>
    </div>
  </t-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { MessagePlugin } from 'tdesign-vue-next'

// 定义 props
const props = defineProps<{
  visible: boolean
  studentData?: any
  classId: string | number
}>()

// 定义 emits
const emit = defineEmits<{
  'update:visible': [visible: boolean]
  success: []
}>()

// 表单引用
const formRef = ref()

// 是否为编辑模式
const isEdit = computed(() => !!props.studentData)

// 表单可见性
const formVisible = computed({
  get: () => props.visible,
  set: (value: boolean) => emit('update:visible', value)
})

// 提交状态
const submitting = ref(false)

// 表单数据
const formData = reactive({
  studentNumber: '',
  name: '',
  gender: 1,
  phone: '',
  email: '',
  entranceYear: '',
  studentStatus: 0
})

// 表单验证规则
const formRules = {
  studentNumber: [
    { required: true, message: '请输入学号' },
    { pattern: /^\d{7}$/, message: '学号必须为7位数字' }
  ],
  name: [
    { required: true, message: '请输入姓名' },
    { min: 2, max: 10, message: '姓名长度为2-10个字符' }
  ],
  gender: [
    { required: true, message: '请选择性别' }
  ],
  phone: [
    { required: true, message: '请输入手机号' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号' }
  ],
  email: [
    { required: true, message: '请输入邮箱' },
    { pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/, message: '请输入正确的邮箱格式' }
  ],
  entranceYear: [
    { required: true, message: '请输入入学年份' },
    { pattern: /^\d{4}$/, message: '请输入正确的年份格式' }
  ],
  studentStatus: [
    { required: true, message: '请选择学籍状态' }
  ]
}

// 监听学生数据变化，填充表单
watch(() => props.studentData, (newData) => {
  if (newData) {
    Object.assign(formData, {
      studentNumber: newData.studentNumber || '',
      name: newData.name || '',
      gender: newData.gender || 1,
      phone: newData.phone || '',
      email: newData.email || '',
      entranceYear: newData.entranceYear || '',
      studentStatus: newData.studentStatus || 0
    })
  } else {
    // 重置表单
    Object.assign(formData, {
      studentNumber: '',
      name: '',
      gender: 1,
      phone: '',
      email: '',
      entranceYear: new Date().getFullYear().toString(),
      studentStatus: 0
    })
  }
}, { immediate: true })

// 提交表单
const handleSubmit = async () => {
  try {
    const valid = await formRef.value?.validate()
    if (!valid) return

    submitting.value = true

    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))

    if (isEdit.value) {
      console.log('更新学生信息:', formData)
      MessagePlugin.success('学生信息更新成功')
    } else {
      console.log('新增学生:', formData)
      MessagePlugin.success('学生添加成功')
    }

    emit('success')
    handleCancel()
  } catch (error) {
    console.error('提交失败:', error)
    MessagePlugin.error('操作失败，请重试')
  } finally {
    submitting.value = false
  }
}

// 取消操作
const handleCancel = () => {
  formRef.value?.reset()
  emit('update:visible', false)
}
</script>

<style scoped>
.student-form-container {
  padding: 16px 0;
  overflow: hidden; /* 防止横向滚动条 */
  width: 100%;
  box-sizing: border-box;
}

.student-form-container :deep(.t-form) {
  padding: 0;
  width: 100%;
}

/* 减少表单项之间的间距 */
.student-form-container :deep(.t-form-item) {
  margin-bottom: 12px; /* 从18px减少到12px */
}

.student-form-container :deep(.t-form-item__label) {
  font-weight: 500;
  color: var(--td-text-color-primary);
  margin-bottom: 4px; /* 从6px减少到4px */
  line-height: 1.4;
}

.student-form-container :deep(.t-form-item__help) {
  margin-top: 2px; /* 从4px减少到2px */
  font-size: 12px;
}

/* 确保表单控件不会导致横向滚动 */
.student-form-container :deep(.t-input),
.student-form-container :deep(.t-select) {
  width: 100% !important;
  box-sizing: border-box;
}

.student-form-container :deep(.t-row) {
  margin-bottom: 0;
  margin-left: 0 !important;
  margin-right: 0 !important;
  width: 100%;
}

.student-form-container :deep(.t-col) {
  padding-bottom: 0;
  box-sizing: border-box;
}

/* 最后一个表单项的下边距 */
.student-form-container :deep(.t-form-item:last-child) {
  margin-bottom: 0;
}

/* 防止输入框内容溢出 */
.student-form-container :deep(.t-input__inner),
.student-form-container :deep(.t-select__single) {
  box-sizing: border-box;
}

/* 确保Dialog内容区域不会有横向滚动 */
.student-form-container :deep(.t-dialog__body) {
  overflow-x: hidden;
}
</style> 
<template>
  <t-dialog
    v-model:visible="dialogVisible"
    header="学生管理"
    width="1200px"
    :footer="false"
    :close-on-overlay-click="false"
    class="student-management-dialog"
  >
    <!-- 工具栏 -->
    <div class="toolbar">
      <div class="toolbar-left">
        <t-input
          v-model="searchKeyword"
          placeholder="搜索学生姓名或学号"
          style="width: 300px"
          clearable
          @enter="handleSearch"
        >
          <template #prefix-icon>
            <t-icon name="search" />
          </template>
        </t-input>
        <t-button theme="primary" @click="handleSearch">
          <template #icon>
            <t-icon name="search" />
          </template>
          搜索
        </t-button>
      </div>
      <div class="toolbar-right">
        <t-button theme="primary" @click="handleAddStudent">
          <template #icon>
            <t-icon name="add" />
          </template>
          新增学生
        </t-button>
        <t-button 
          theme="danger" 
          variant="outline"
          :disabled="selectedStudents.length === 0"
          @click="handleBatchDelete"
        >
          <template #icon>
            <t-icon name="delete" />
          </template>
          批量删除
        </t-button>
        <t-button 
          theme="danger" 
          variant="text"
          @click="handleClearAll"
        >
          <template #icon>
            <t-icon name="clear" />
          </template>
          清空学生
        </t-button>
      </div>
    </div>

    <!-- 学生列表表格 -->
    <div class="table-container">
      <t-table
        :data="studentList"
        :columns="tableColumns"
        :loading="loading"
        :selected-row-keys="selectedStudents"
        row-key="id"
        select-on-row-click
        @select-change="handleSelectionChange"
        :pagination="paginationConfig"
        @page-change="handlePageChange"
        @page-size-change="handlePageSizeChange"
      >
        <!-- 性别列 -->
        <template #gender="{ row }">
          <t-tag :theme="row.gender === 1 ? 'primary' : 'warning'" size="small">
            {{ row.gender === 1 ? '男' : '女' }}
          </t-tag>
        </template>

        <!-- 学籍状态列 -->
        <template #studentStatus="{ row }">
          <t-tag :theme="row.studentStatus === 0 ? 'success' : 'default'" size="small">
            {{ row.studentStatus === 0 ? '在读' : '毕业' }}
          </t-tag>
        </template>

        <!-- 操作列 -->
        <template #action="{ row }">
          <t-space>
            <t-button theme="primary" variant="text" size="small" @click="handleEditStudent(row)">
              <template #icon>
                <t-icon name="edit" />
              </template>
              编辑
            </t-button>
            <t-button theme="danger" variant="text" size="small" @click="handleDeleteStudent(row)">
              <template #icon>
                <t-icon name="delete" />
              </template>
              删除
            </t-button>
          </t-space>
        </template>
      </t-table>
    </div>

    <!-- 学生信息表单弹窗 -->
    <StudentForm
      v-model:visible="studentFormVisible"
      :student-data="currentStudent"
      :class-id="classId"
      @success="handleStudentFormSuccess"
    />
  </t-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, onMounted } from 'vue'
import {
  Dialog as TDialog,
  Table as TTable,
  Button as TButton,
  Input as TInput,
  Icon as TIcon,
  Tag as TTag,
  Space as TSpace,
  MessagePlugin,
  DialogPlugin,
  type PrimaryTableCol
} from 'tdesign-vue-next'
import StudentForm from './StudentForm.vue'

// 定义 props
interface Props {
  visible: boolean
  classId: string | number
  className: string
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  classId: '',
  className: ''
})

// 定义事件
const emit = defineEmits<{
  'update:visible': [visible: boolean]
  'success': []
}>()

// 弹窗可见性
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 学生数据接口
interface StudentItem {
  id: string | number
  studentNumber: string
  name: string
  gender: number
  phone: string
  email: string
  entranceYear: string
  studentStatus: number
  createTime: string
}

// 响应式数据
const loading = ref(false)
const searchKeyword = ref('')
const selectedStudents = ref<(string | number)[]>([])
const studentList = ref<StudentItem[]>([])
const studentFormVisible = ref(false)
const currentStudent = ref<StudentItem | null>(null)

// 分页配置
const paginationConfig = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showJumper: true,
  showSizeChanger: true,
  pageSizeOptions: [10, 20, 50, 100]
})

// 表格列配置
const tableColumns: PrimaryTableCol[] = [
  {
    colKey: 'row-select',
    type: 'multiple',
    width: 50,
  },
  {
    colKey: 'studentNumber',
    title: '学号',
    width: 120,
    align: 'center'
  },
  {
    colKey: 'name',
    title: '姓名',
    width: 100,
    align: 'center'
  },
  {
    colKey: 'gender',
    title: '性别',
    width: 80,
    align: 'center'
  },
  {
    colKey: 'phone',
    title: '手机号',
    width: 130,
    align: 'center'
  },
  {
    colKey: 'email',
    title: '邮箱',
    width: 200,
    ellipsis: true
  },
  {
    colKey: 'entranceYear',
    title: '入学年份',
    width: 100,
    align: 'center'
  },
  {
    colKey: 'studentStatus',
    title: '学籍状态',
    width: 100,
    align: 'center'
  },
  {
    colKey: 'createTime',
    title: '创建时间',
    width: 160,
    align: 'center'
  },
  {
    colKey: 'action',
    title: '操作',
    width: 120,
    align: 'center',
    fixed: 'right'
  }
]

// 模拟学生数据
const mockStudentData: StudentItem[] = [
  {
    id: 1,
    studentNumber: '2024001',
    name: '张三',
    gender: 1,
    phone: '13800138001',
    email: '<EMAIL>',
    entranceYear: '2024',
    studentStatus: 0,
    createTime: '2024-09-01 10:00:00'
  },
  {
    id: 2,
    studentNumber: '2024002',
    name: '李四',
    gender: 2,
    phone: '13800138002',
    email: '<EMAIL>',
    entranceYear: '2024',
    studentStatus: 0,
    createTime: '2024-09-01 10:05:00'
  },
  {
    id: 3,
    studentNumber: '2024003',
    name: '王五',
    gender: 1,
    phone: '13800138003',
    email: '<EMAIL>',
    entranceYear: '2024',
    studentStatus: 0,
    createTime: '2024-09-01 10:10:00'
  },
  {
    id: 4,
    studentNumber: '2024004',
    name: '赵六',
    gender: 2,
    phone: '13800138004',
    email: '<EMAIL>',
    entranceYear: '2024',
    studentStatus: 0,
    createTime: '2024-09-01 10:15:00'
  },
  {
    id: 5,
    studentNumber: '2024005',
    name: '孙七',
    gender: 1,
    phone: '13800138005',
    email: '<EMAIL>',
    entranceYear: '2024',
    studentStatus: 0,
    createTime: '2024-09-01 10:20:00'
  }
]

// 加载学生数据
const loadStudentData = async () => {
  loading.value = true
  try {
    // 模拟API调用延迟
    await new Promise(resolve => setTimeout(resolve, 500))
    
    // 模拟搜索过滤
    let filteredData = [...mockStudentData]
    if (searchKeyword.value) {
      filteredData = filteredData.filter(student => 
        student.name.includes(searchKeyword.value) || 
        student.studentNumber.includes(searchKeyword.value)
      )
    }
    
    // 模拟分页
    const start = (paginationConfig.current - 1) * paginationConfig.pageSize
    const end = start + paginationConfig.pageSize
    studentList.value = filteredData.slice(start, end)
    paginationConfig.total = filteredData.length
    
  } catch (error) {
    console.error('加载学生数据失败:', error)
    MessagePlugin.error('加载学生数据失败')
  } finally {
    loading.value = false
  }
}

// 事件处理函数
const handleSearch = () => {
  paginationConfig.current = 1
  loadStudentData()
}

const handleSelectionChange = (selectedRowKeys: (string | number)[]) => {
  selectedStudents.value = selectedRowKeys
}

const handlePageChange = (pageInfo: any) => {
  paginationConfig.current = pageInfo.current
  loadStudentData()
}

const handlePageSizeChange = (pageInfo: any) => {
  paginationConfig.pageSize = pageInfo.pageSize
  paginationConfig.current = 1
  loadStudentData()
}

const handleAddStudent = () => {
  currentStudent.value = null
  studentFormVisible.value = true
}

const handleEditStudent = (student: StudentItem) => {
  currentStudent.value = student
  studentFormVisible.value = true
}

const handleDeleteStudent = (student: StudentItem) => {
  const confirmDialog = DialogPlugin.confirm({
    header: '删除确认',
    body: `确定要删除学生"${student.name}"吗？`,
    confirmBtn: '确认删除',
    cancelBtn: '取消',
    theme: 'danger',
    onConfirm: async () => {
      try {
        // 模拟删除操作
        await new Promise(resolve => setTimeout(resolve, 300))
        MessagePlugin.success('删除成功')
        loadStudentData()
        confirmDialog.destroy()
        return true
      } catch (error) {
        MessagePlugin.error('删除失败')
        confirmDialog.destroy()
        return false
      }
    },
    onCancel: () => {
      confirmDialog.destroy()
    }
  })
}

const handleBatchDelete = () => {
  if (selectedStudents.value.length === 0) {
    MessagePlugin.warning('请选择要删除的学生')
    return
  }
  
  const confirmDialog = DialogPlugin.confirm({
    header: '批量删除确认',
    body: `确定要删除选中的 ${selectedStudents.value.length} 名学生吗？`,
    confirmBtn: '确认删除',
    cancelBtn: '取消',
    theme: 'danger',
    onConfirm: async () => {
      try {
        // 模拟批量删除操作
        await new Promise(resolve => setTimeout(resolve, 500))
        MessagePlugin.success(`成功删除 ${selectedStudents.value.length} 名学生`)
        selectedStudents.value = []
        loadStudentData()
        confirmDialog.destroy()
        return true
      } catch (error) {
        MessagePlugin.error('批量删除失败')
        confirmDialog.destroy()
        return false
      }
    },
    onCancel: () => {
      confirmDialog.destroy()
    }
  })
}

const handleClearAll = () => {
  const confirmDialog = DialogPlugin.confirm({
    header: '清空确认',
    body: `确定要清空 ${props.className} 的所有学生吗？此操作不可恢复！`,
    confirmBtn: '确认清空',
    cancelBtn: '取消',
    theme: 'danger',
    onConfirm: async () => {
      try {
        // 模拟清空操作
        await new Promise(resolve => setTimeout(resolve, 500))
        MessagePlugin.success('清空成功')
        selectedStudents.value = []
        loadStudentData()
        confirmDialog.destroy()
        return true
      } catch (error) {
        MessagePlugin.error('清空失败')
        confirmDialog.destroy()
        return false
      }
    },
    onCancel: () => {
      confirmDialog.destroy()
    }
  })
}

const handleStudentFormSuccess = () => {
  loadStudentData()
  emit('success')
}

// 监听弹窗显示状态
watch(() => props.visible, (visible) => {
  if (visible) {
    loadStudentData()
  } else {
    // 重置状态
    searchKeyword.value = ''
    selectedStudents.value = []
    paginationConfig.current = 1
  }
})
</script>

<style lang="less" scoped>
.student-management-dialog {
  .toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding: 16px;
    background: var(--td-bg-color-page);
    border-radius: 6px;

    .toolbar-left {
      display: flex;
      align-items: center;
      gap: 12px;
    }

    .toolbar-right {
      display: flex;
      align-items: center;
      gap: 8px;
    }
  }

  .table-container {
    margin-top: 16px;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .student-management-dialog {
    .toolbar {
      flex-direction: column;
      gap: 12px;
      align-items: stretch;

      .toolbar-left,
      .toolbar-right {
        justify-content: center;
      }

      .toolbar-left {
        :deep(.t-input) {
          width: 100% !important;
        }
      }
    }
  }
}
</style> 
// 导入配置接口
export interface ImportConfig {
  // 标题
  title?: string;
  // 提示信息
  tips?: string;
  // 模板文件名
  templateFileName?: string;
  // 模板数据
  templateData: string[][];
  // 支持的文件类型
  acceptTypes?: string[];
  // 最大文件大小（MB）
  maxFileSize?: number;
  // 自定义下载模板函数
  customDownloadTemplate?: () => void;
}

// 导入回调函数类型
export interface ImportCallbacks {
  // 导入API调用函数
  onImport: (file: File) => Promise<any>;
  // 导入成功回调
  onSuccess?: (result: any) => void;
  // 导入失败回调
  onError?: (error: Error) => void;
  // 导入完成回调（无论成功失败）
  onComplete?: () => void;
}

// 组件Props接口
export interface ImportDialogProps {
  // 是否显示对话框
  visible: boolean;
  // 导入配置
  config: ImportConfig;
  // 回调函数
  callbacks: ImportCallbacks;
  // 对话框属性
  dialogProps?: Record<string, any>;
}

// 组件Emits接口
export interface ImportDialogEmits {
  (e: 'update:visible', visible: boolean): void;
} 
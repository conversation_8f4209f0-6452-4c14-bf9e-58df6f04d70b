<template>
  <t-dialog
    :visible="visible"
    :header="config.title || '数据导入'"
    :width="600"
    :footer="false"
    attach='body'
    @update:visible="handleVisibleChange"
    v-bind="dialogProps || {}"
  >
    <div class="import-content">
      <!-- 提示信息 -->
      <div class="import-tips">
        <t-alert 
          theme="info" 
          :message="config.tips || '请按照模板格式填写数据信息，支持批量导入'" 
        />
      </div>

      <!-- 模板下载 -->
      <div class="template-download">
        <t-link theme="primary" @click="downloadTemplate">
          <template #prefixIcon><download-icon /></template>
          下载导入模板
        </t-link>
      </div>

      <!-- 上传区域 -->
      <div class="upload-area">
        <t-upload
          v-model="uploadFiles"
          :show-upload-list="true"
          :before-upload="beforeUpload"
          @remove="handleFileRemove"
          drag
          :accept="acceptTypesString"
          :multiple="false"
          :auto-upload="false"
          ref="uploadRef"
        >
          <div class="upload-dragger">
            <upload-icon size="48px" style="color: #0052d9;" />
            <div class="upload-text">
              <div class="upload-tip">点击上传或将文件拖拽到此区域</div>
              <div class="upload-hint">支持 {{ acceptTypesString }} 格式文件</div>
            </div>
          </div>
        </t-upload>
      </div>

      <!-- 上传进度 -->
      <div class="upload-progress" v-if="uploadProgress > 0 && uploadProgress < 100">
        <t-progress :percentage="uploadProgress" :label="true" />
        <div class="progress-text">正在上传... {{ uploadProgress }}%</div>
      </div>

      <!-- 上传结果 -->
      <div class="upload-result" v-if="uploadResult">
        <t-alert
          :theme="uploadResult.success ? 'success' : 'error'"
          :message="uploadResult.success ? uploadResult.message : '导入过程中发现以下问题：'"
        />
        
        <!-- 错误详情列表 -->
        <div class="error-details" v-if="!uploadResult.success && uploadResult.errorList">
          <div class="error-list">
            <div 
              v-for="(error, index) in uploadResult.errorList" 
              :key="index"
              class="error-item"
            >
              {{ error }}
            </div>
          </div>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="import-actions">
        <t-space>
          <t-button
            theme="primary"
            @click="handleStartUpload"
            :disabled="uploadFiles.length === 0 || uploading"
          >
            {{ uploading ? '导入中...' : '开始导入' }}
          </t-button>
          <t-button theme="default" @click="handleCancel">取消</t-button>
        </t-space>
      </div>
    </div>
  </t-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { MessagePlugin } from 'tdesign-vue-next';
import { DownloadIcon, UploadIcon } from 'tdesign-icons-vue-next';
import type { UploadInstanceFunctions } from 'tdesign-vue-next';
import * as XLSX from 'xlsx'
import type { ImportConfig, ImportCallbacks, ImportDialogEmits } from './types';

// Props定义
interface Props {
  visible: boolean;
  config: ImportConfig;
  callbacks: ImportCallbacks;
  dialogProps?: Record<string, any>;
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
});

// Emits定义
const emit = defineEmits<ImportDialogEmits>();

// 响应式数据
const uploadFiles = ref<any[]>([]);
const uploadProgress = ref(0);
const uploading = ref(false);
const uploadRef = ref<UploadInstanceFunctions>();
const uploadResult = ref<{ success: boolean; message: string; errorList?: string[] } | null>(null);

// 计算属性
const acceptTypesString = computed(() => {
  return props.config.acceptTypes?.join(', ') || '.xlsx, .xls';
});

const maxFileSize = computed(() => {
  return (props.config.maxFileSize || 5) * 1024 * 1024; // 转换为字节
});

// 监听visible变化，重置状态
watch(() => props.visible, (newVisible) => {
  if (newVisible) {
    resetState();
  }
});

// 重置状态
const resetState = () => {
  uploadFiles.value = [];
  uploadProgress.value = 0;
  uploading.value = false;
  uploadResult.value = null;
};

// 处理visible变化
const handleVisibleChange = (visible: boolean) => {
  emit('update:visible', visible);
};

// 上传前验证
const beforeUpload = (file: any) => {
  // 验证文件类型
  const acceptTypes = props.config.acceptTypes || ['.xlsx', '.xls'];
  const fileExtension = file.name.toLowerCase().substring(file.name.lastIndexOf('.'));
  
  if (!acceptTypes.includes(fileExtension)) {
    MessagePlugin.error(`只能上传 ${acceptTypesString.value} 格式的文件`);
    return false;
  }

  // 验证文件大小
  if (file.size > maxFileSize.value) {
    MessagePlugin.error(`文件大小不能超过 ${props.config.maxFileSize || 5}MB`);
    return false;
  }

  return true;
};

// 文件移除
const handleFileRemove = () => {
  uploadFiles.value = [];
  uploadProgress.value = 0;
  uploadResult.value = null;
};

// 开始上传
const handleStartUpload = async () => {
  if (uploadFiles.value.length === 0) {
    MessagePlugin.warning('请选择要上传的文件');
    return;
  }

  const file = uploadFiles.value[0].raw || uploadFiles.value[0];
  if (!file) {
    MessagePlugin.error('文件获取失败');
    return;
  }

  uploading.value = true;
  uploadProgress.value = 0;
  uploadResult.value = null;

  // 将progressInterval声明移到try-catch块外部
  let progressInterval: NodeJS.Timeout | null = null;

  try {
    // 模拟上传进度
    progressInterval = setInterval(() => {
      if (uploadProgress.value < 90) {
        uploadProgress.value += 10;
      }
    }, 100);

    // 调用导入API
    const result = await props.callbacks.onImport(file);
    
    if (progressInterval) {
      clearInterval(progressInterval);
      progressInterval = null;
    }
    uploadProgress.value = 100;

    // 检查返回结果的格式
    const importResult = result.data || result;
    
    if (importResult && typeof importResult === 'object' && 'success' in importResult) {
      // 新的响应格式：包含success字段的对象
      if (importResult.success) {
        // 导入成功
        uploadResult.value = {
          success: true,
          message: importResult.successMessage || '导入成功'
        };
        MessagePlugin.success(importResult.successMessage || '导入成功');
        
        // 调用成功回调
        if (props.callbacks.onSuccess) {
          props.callbacks.onSuccess(result);
        }
      } else {
        // 导入失败，显示错误列表
        const errorMessages = importResult.errorMessages || [];
        
        uploadResult.value = {
          success: false,
          message: `导入失败，成功：${importResult.successCount || 0}条，失败：${importResult.failCount || 0}条`,
          errorList: errorMessages
        };
        
        MessagePlugin.error(`导入失败，成功：${importResult.successCount || 0}条，失败：${importResult.failCount || 0}条`);
        
        // 调用失败回调
        if (props.callbacks.onError) {
          props.callbacks.onError(new Error(errorMessages.join('\n')));
        }
      }
    } else {
      // 兼容旧的响应格式：字符串消息
      uploadResult.value = {
        success: true,
        message: importResult || '导入成功'
      };
      MessagePlugin.success('导入成功');
      
      // 调用成功回调
      if (props.callbacks.onSuccess) {
        props.callbacks.onSuccess(result);
      }
    }

  } catch (error: any) {
    if (progressInterval) {
      clearInterval(progressInterval);
      progressInterval = null;
    }
    uploadProgress.value = 0;
    
    uploadResult.value = {
      success: false,
      message: error.message || '导入失败'
    };

    MessagePlugin.error('导入失败: ' + error.message);
    
    // 调用失败回调
    if (props.callbacks.onError) {
      props.callbacks.onError(error);
    }
  } finally {
    uploading.value = false;
    
    // 调用完成回调
    if (props.callbacks.onComplete) {
      props.callbacks.onComplete();
    }
  }
};

// 下载模板
const downloadTemplate = () => {
  try {
    // 如果有自定义下载模板函数，优先使用
    if (props.config.customDownloadTemplate) {
      props.config.customDownloadTemplate();
      return;
    }
    
    // 默认下载模板逻辑
    const ws = XLSX.utils.aoa_to_sheet(props.config.templateData);
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, '导入模板');

    // 下载文件
    const fileName = props.config.templateFileName || '导入模板.xlsx';
    XLSX.writeFile(wb, fileName);
    
    MessagePlugin.success('模板下载成功');
  } catch (error) {
    console.error('模板下载失败:', error);
    MessagePlugin.error('模板下载失败');
  }
};

// 取消导入
const handleCancel = () => {
  resetState();
  handleVisibleChange(false);
};
</script>

<style scoped lang="less">
.import-content {
  padding: 16px 0;
}

.import-tips {
  margin-bottom: 16px;
}

.template-download {
  margin-bottom: 24px;
  text-align: center;
}

.upload-area {
  margin-bottom: 16px;
  
  :deep(.t-upload__trigger) {
    width: 100%;
  }
}

.upload-dragger {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  border: 2px dashed #d0d7de;
  border-radius: 6px;
  background-color: #f6f8fa;
  transition: all 0.3s ease;
  
  &:hover {
    border-color: #0052d9;
    background-color: #f0f5ff;
  }
}

.upload-text {
  margin-top: 16px;
  text-align: center;
}

.upload-tip {
  font-size: 16px;
  color: #1d2129;
  margin-bottom: 8px;
}

.upload-hint {
  font-size: 14px;
  color: #86909c;
}

.upload-progress {
  margin-bottom: 16px;
}

.progress-text {
  text-align: center;
  margin-top: 8px;
  font-size: 14px;
  color: #0052d9;
}

.upload-result {
  margin-bottom: 16px;
}

.error-details {
  margin-top: 12px;
}

.error-list {
  max-height: 200px;
  overflow-y: auto;
  background-color: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 6px;
  padding: 12px;
}

.error-item {
  padding: 4px 0;
  font-size: 14px;
  color: #dc2626;
  border-bottom: 1px solid #fecaca;
  
  &:last-child {
    border-bottom: none;
  }
}

.import-actions {
  text-align: center;
  margin-top: 24px;
}
</style> 
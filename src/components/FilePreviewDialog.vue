<template>
  <t-dialog
    v-model:visible="visible"
    :header="fileName"
    width="80%"
    top="5%"
    :footer="false"
    class="file-preview-dialog"
    :z-index="2000"
    attach="body"
  >
    <div class="file-preview-container">
      <file-viewer
        :file-url="fileUrl"
        :file-name="fileName"
        :file-type="fileType"
      />
    </div>
  </t-dialog>
</template>

<script setup lang="ts">
import { ref, defineProps, watch } from 'vue';
import { Dialog as TDialog } from 'tdesign-vue-next';
import FileViewer from './FileViewer.vue';

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  fileUrl: {
    type: String,
    required: true
  },
  fileName: {
    type: String,
    default: '文件预览'
  },
  fileType: {
    type: String,
    default: 'other'
  }
});

const emit = defineEmits(['update:modelValue']);

const visible = ref(props.modelValue);

watch(() => props.modelValue, (newValue) => {
  visible.value = newValue;
});

watch(() => visible.value, (newValue) => {
  emit('update:modelValue', newValue);
});
</script>

<style lang="less" scoped>
.file-preview-container {
  padding: 0;
  height: 70vh;
  overflow: hidden;

  :deep(.file-viewer) {
    height: 100%;
    border: none;

    .file-viewer-content {
      height: calc(100% - 50px);
    }
  }
}

:deep(.t-dialog__body) {
  padding: 0;
}

:deep(.t-dialog__header) {
  padding: 16px;
  border-bottom: 1px solid #eee;
}
</style>
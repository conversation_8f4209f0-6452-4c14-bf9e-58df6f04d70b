<template>
  <div class="member-card">
    <div class="card-header">
      <div class="member-avatar">
        <t-avatar :image="memberData.avatar" size="large">
          {{ memberData.name.charAt(0) }}
        </t-avatar>
      </div>
      <div class="member-role-badge">
        <t-tag :theme="roleTheme" size="small">{{ memberData.role }}</t-tag>
      </div>
    </div>

    <div class="card-content">
      <!-- 基本信息 -->
      <div class="basic-info">
        <h3 class="member-name">{{ memberData.name }}</h3>
        <div class="member-title">{{ memberData.title }}</div>
        <div class="member-academy">{{ memberData.academyName }}</div>
        <div class="member-contact" v-if="memberData.email">
          <t-icon name="mail" size="14px" />
          <span>{{ memberData.email }}</span>
        </div>
      </div>

      <!-- 统计信息 -->
      <div class="stats-section">
        <div class="stats-grid">
          <div class="stat-item">
            <div class="stat-value">{{ memberData.classCount }}</div>
            <div class="stat-label">授课班级</div>
          </div>
          <div class="stat-divider"></div>
          <div class="stat-item">
            <div class="stat-value">{{ memberData.studentCount }}</div>
            <div class="stat-label">学生总数</div>
          </div>
        </div>
      </div>
    </div>

    <div class="card-footer">
      <t-button theme="default" variant="text" size="small" @click="handleViewProfile">
        <template #icon>
          <t-icon name="user" />
        </template>
        查看详情
      </t-button>
      <t-button theme="primary" variant="text" size="small" @click="handleContact">
        <template #icon>
          <t-icon name="chat" />
        </template>
        联系教师
      </t-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import {
  Tag as TTag,
  Button as TButton,
  Icon as TIcon,
  Avatar as TAvatar,
  MessagePlugin
} from 'tdesign-vue-next'
import type { TeamMemberItem } from '@/api/base/classes'

// 定义 props
interface Props {
  memberData: TeamMemberItem
}

const props = defineProps<Props>()

// 定义事件
const emit = defineEmits<{
  viewProfile: [teacherId: string | number]
  contact: [teacherId: string | number]
}>()

// 计算属性 - 角色主题
const roleTheme = computed(() => {
  const roleMap: Record<string, 'primary' | 'success' | 'warning' | 'danger' | 'default'> = {
    '课程负责人': 'primary',
    '主讲教师': 'success',
    '助教': 'warning',
    '实验教师': 'default'
  }
  return roleMap[props.memberData.role] || 'default'
})

// 事件处理函数
const handleViewProfile = () => {
  emit('viewProfile', props.memberData.teacherId)
}

const handleContact = () => {
  if (props.memberData.email) {
    // 可以实现邮件联系或其他联系方式
    MessagePlugin.info(`联系 ${props.memberData.name}：${props.memberData.email}`)
  } else {
    MessagePlugin.info('暂无联系方式')
  }
  emit('contact', props.memberData.teacherId)
}
</script>

<style lang="less" scoped>
.member-card {
  background: var(--td-bg-color-container);
  border: 1px solid var(--td-border-level-1-color);
  border-radius: 12px;
  padding: 20px;
  transition: all 0.3s ease;
  height: 100%;
  position: relative;

  &:hover {
    border-color: var(--td-brand-color);
    box-shadow: 0 8px 24px rgba(0, 82, 217, 0.12);
    transform: translateY(-4px);
  }

  .card-header {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 16px;
    position: relative;

    .member-avatar {
      margin-bottom: 8px;
    }

    .member-role-badge {
      position: absolute;
      top: -4px;
      right: -4px;
    }
  }

  .card-content {
    text-align: center;
    margin-bottom: 20px;

    .basic-info {
      margin-bottom: 16px;

      .member-name {
        margin: 0 0 6px 0;
        font-size: 16px;
        font-weight: 600;
        color: var(--td-text-color-primary);
        line-height: 1.4;
        display: inline;
      }

      .member-title {
        font-size: 14px;
        color: var(--td-brand-color);
        font-weight: 500;
        margin-bottom: 4px;
        margin-left: 8px;
        display: inline;
      }

      .member-academy {
        font-size: 13px;
        color: var(--td-text-color-secondary);
        margin-bottom: 8px;
      }

      .member-contact {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 4px;
        font-size: 12px;
        color: var(--td-text-color-placeholder);
      }
    }

    .stats-section {
      .stats-grid {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 12px;
        background: var(--td-bg-color-page);
        border-radius: 8px;

        .stat-item {
          flex: 1;
          text-align: center;

          .stat-value {
            font-size: 18px;
            font-weight: 700;
            color: var(--td-brand-color);
            line-height: 1.2;
            margin-bottom: 2px;
          }

          .stat-label {
            font-size: 11px;
            color: var(--td-text-color-secondary);
            font-weight: 400;
          }
        }

        .stat-divider {
          width: 1px;
          height: 24px;
          background: var(--td-border-level-1-color);
          margin: 0 8px;
        }
      }
    }
  }

  .card-footer {
    display: flex;
    gap: 8px;
    padding-top: 16px;
    border-top: 1px solid var(--td-border-level-1-color);

    .t-button {
      flex: 1;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .member-card {
    padding: 16px;

    .card-content .basic-info .member-name {
      font-size: 15px;
    }

    .card-content .stats-section .stats-grid {
      padding: 10px;

      .stat-item .stat-value {
        font-size: 16px;
      }
    }

    .card-footer {
      flex-direction: column;
      gap: 8px;
    }
  }
}
</style> 
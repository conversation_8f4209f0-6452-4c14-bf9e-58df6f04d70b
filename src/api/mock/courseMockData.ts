//课程负责人模拟数据

//课程列表页面
export const courseList = [
  {
    "courseId": 1,
    "courseName": "Vue 3 高级开发",
    "courseCode": "CS101",
    "semesterName": "2022-秋季学期",
    "status": "未开始",
    "credits": 4,
    "totalHours": 64,
    "courseNature": "必修课",
    "courseUnit": "计算机科学与技术学院",
    "isTeaching": true,
    "teachers": [
      {
        "name": "赵武",
        "teacherNumber": "1001",
        "isResponsible": true
      },
      {
        "name": "李四",
        "teacherNumber": "1002",
        "isResponsible": false
      }
    ],
    "isResponsible": false
  },
  {
    "courseId": 2,
    "courseName": "React 基础",
    "courseCode": "CS102",
    "semesterName": "2023-春季学期",
    "status": "已结束",
    "credits": 3,
    "totalHours": 48,
    "courseNature": "选修课",
    "courseUnit": "软件学院",
    "isTeaching": true,
    "teachers": [
      {
        "name": "张三",
        "teacherNumber": "1003",
        "isResponsible": true
      },
      {
        "name": "李四",
        "teacherNumber": "1004",
        "isResponsible": false
      }
    ],
    "isResponsible": true
  },
  {
    "courseId": 3,
    "courseName": "Node.js 应用开发",
    "courseCode": "CS103",
    "semesterName": "2023-秋季学期",
    "status": "未开始",
    "credits": 4,
    "totalHours": 64,
    "courseNature": "必修课",
    "courseUnit": "数学学院",
    "isTeaching": true,
    "teachers": [
      {
        "name": "王五",
        "teacherNumber": "1005",
        "isResponsible": false
      },
      {
        "name": "赵六",
        "teacherNumber": "1006",
        "isResponsible": false
      }
    ],
    "isResponsible": true
  },
  {
    "courseId": 4,
    "courseName": "Python 数据分析",
    "courseCode": "CS104",
    "semesterName": "2023-春季学期",
    "status": "未开始",
    "credits": 4,
    "totalHours": 60,
    "courseNature": "选修课",
    "courseUnit": "计算机科学与技术学院",
    "isTeaching": true,
    "teachers": [
      {
        "name": "钱七",
        "teacherNumber": "1007",
        "isResponsible": true
      },
      {
        "name": "孙八",
        "teacherNumber": "1008",
        "isResponsible": false
      }
    ],
    "isResponsible": true
  },
  {
    "courseId": 5,
    "courseName": "前端工程化",
    "courseCode": "CS105",
    "semesterName": "2024-春季学期",
    "status": "已结束",
    "credits": 3,
    "totalHours": 40,
    "courseNature": "选修课",
    "courseUnit": "数学学院",
    "isTeaching": true,
    "teachers": [
      {
        "name": "刘九",
        "teacherNumber": "1009",
        "isResponsible": true
      },
      {
        "name": "周十",
        "teacherNumber": "1010",
        "isResponsible": false
      }
    ],
    "isResponsible": false
  },
  {
    "courseId": 6,
    "courseName": "数据库设计",
    "courseCode": "CS106",
    "semesterName": "2024-秋季学期",
    "status": "已结束",
    "credits": 4,
    "totalHours": 56,
    "courseNature": "必修课",
    "courseUnit": "马克思主义学院",
    "isTeaching": true,
    "teachers": [
      {
        "name": "吴一",
        "teacherNumber": "1011",
        "isResponsible": true
      },
      {
        "name": "郑二",
        "teacherNumber": "1012",
        "isResponsible": false
      }
    ],
    "isResponsible": false
  },
  {
    "courseId": 7,
    "courseName": "机器学习概论",
    "courseCode": "CS107",
    "semesterName": "2024-秋季学期",
    "status": "未开始",
    "credits": 4,
    "totalHours": 72,
    "courseNature": "必修课",
    "courseUnit": "纺织学院",
    "isTeaching": true,
    "teachers": [
      {
        "name": "冯三",
        "teacherNumber": "1013",
        "isResponsible": true
      },
      {
        "name": "陈四",
        "teacherNumber": "1014",
        "isResponsible": false
      }
    ],
    "isResponsible": false
  },
  {
    "courseId": 8,
    "courseName": "移动应用开发",
    "courseCode": "CS108",
    "semesterName": "2024-秋季学期",
    "status": "已结束",
    "credits": 3,
    "totalHours": 50,
    "courseNature": "选修课",
    "courseUnit": "计算机科学与技术学院",
    "isTeaching": true,
    "teachers": [
      {
        "name": "刘一",
        "teacherNumber": "1015",
        "isResponsible": true
      },
      {
        "name": "林二",
        "teacherNumber": "1016",
        "isResponsible": false
      }
    ],
    "isResponsible": false
  },
  {
    "courseId": 9,
    "courseName": "网络安全",
    "courseCode": "CS109",
    "semesterName": "2025-春季学期",
    "status": "未开始",
    "credits": 4,
    "totalHours": 64,
    "courseNature": "必修课",
    "courseUnit": "计算机科学与技术学院",
    "isTeaching": true,
    "teachers": [
      {
        "name": "高七",
        "teacherNumber": "1017",
        "isResponsible": true
      },
      {
        "name": "黄八",
        "teacherNumber": "1018",
        "isResponsible": false
      }
    ],
    "isResponsible": false
  },
  {
    "courseId": 10,
    "courseName": "云计算技术",
    "courseCode": "CS110",
    "semesterName": "2025-春季学期",
    "status": "未开始",
    "credits": 4,
    "totalHours": 60,
    "courseNature": "必修课",
    "courseUnit": "计算机科学与技术学院",
    "isTeaching": true,
    "teachers": [
      {
        "name": "转四",
        "teacherNumber": "1019",
        "isResponsible": true
      },
      {
        "name": "宾三",
        "teacherNumber": "1020",
        "isResponsible": false
      }
    ],
    "isResponsible": true
  }
]

// //课程详情页面---时间轴
// export const courseTimeline = [
//     {
//       id: 1,
//       name: '高等数学',
//       image: '/image/course/math.jpg',
//       date: '2024-春季学期',
//       status: '进行中',
//       reach: 98
//     },
//     {
//       id: 2,
//       name: '软件工程',
//       image: '/image/course/math.jpg',
//       date: '2024-春季学期',
//       status: '进行中',
//       reach: 78
//     },
//     {
//       id: 3,
//       name: '线性代数',
//       image: '/image/course/linear.jpg',
//       date: '2023-秋季学期',
//       status: '已结束',
//       reach: 68
//     },
//     {
//       id: 4,
//       name: '概率论',
//       image: '/image/course/prob.jpg',
//       date: '2023-春季学期',
//       status: '已结束',
//       reach: 90
//     },
//     {
//       id: 5,
//       name: '离散数学',
//       image: '/image/course/discrete.jpg',
//       date: '2022-秋季学期',
//       status: '已结束',
//       reach: 80
//     },
//     {
//       id: 6,
//       name: '数值分析',
//       image: '/image/course/numerical.jpg',
//       date: '2022-春季学期',
//       status: '已结束',
//       reach: 66
//     },
//     {
//       id: 7,
//       name: '大学英语',
//       image: '/image/course/numerical.jpg',
//       date: '2021-春季学期',
//       status: '已结束',
//       reach: 77
//     },
//     {
//       id: 8,
//       name: '软件工程',
//       image: '/image/course/numerical.jpg',
//       date: '2021-春季学期',
//       status: '已结束',
//       reach: 87
//     },
//     {
//       id: 9,
//       name: 'Python 开发',
//       image: '/image/course/numerical.jpg',
//       date: '2021-春季学期',
//       status: '已结束',
//       reach: 90
//     },
//     {
//       id: 10,
//       name: '数值分析',
//       image: '/image/course/numerical.jpg',
//       date: '2021-春季学期',
//       status: '已结束',
//       reach: 98
//     },
// ]

//首页
export const homeInfo =[
  {
    "name": "李教授",
    "title": "特聘教授",
    "department": "计算机学院",
    "avatar": "/image/avatar/avatar.jpg",
    "courseCount": 12,
    "studentCount": 660,
    "years": 15,
    "introduction": "从事高等数学教学工作15年，主要研究方向为偏微分方程与数值分析。曾获得校级优秀教师称号，发表教学研究论文20余篇。致力于将数学理论与实际应用相结合，培养学生的数学思维和解决问题的能力。",
    "courseList": [
      "偏微分方程",
      "数值分析",
      "数学建模",
      "最优化理论"
    ],
    "currentSemester": "2024-春季学期",
    "courses": [
      {
      id: 1,
      name: '高等数学',
      image: '/image/course/math.jpg',
      date: '2024-春季学期',
      status: '进行中',
      reach: 98
    },
    {
      id: 2,
      name: '软件工程',
      image: '/image/course/math.jpg',
      date: '2024-春季学期',
      status: '进行中',
      reach: 78
    },
    {
      id: 3,
      name: '线性代数',
      image: '/image/course/linear.jpg',
      date: '2023-秋季学期',
      status: '已结束',
      reach: 68
    },
    {
      id: 4,
      name: '概率论',
      image: '/image/course/prob.jpg',
      date: '2023-春季学期',
      status: '已结束',
      reach: 90
    },
    {
      id: 5,
      name: '离散数学',
      image: '/image/course/discrete.jpg',
      date: '2022-秋季学期',
      status: '已结束',
      reach: 80
    },
    {
      id: 6,
      name: '数值分析',
      image: '/image/course/numerical.jpg',
      date: '2022-春季学期',
      status: '已结束',
      reach: 66
    },
    {
      id: 7,
      name: '大学英语',
      image: '/image/course/numerical.jpg',
      date: '2021-春季学期',
      status: '已结束',
      reach: 77
    },
    {
      id: 8,
      name: '软件工程',
      image: '/image/course/numerical.jpg',
      date: '2021-春季学期',
      status: '已结束',
      reach: 87
    },
    {
      id: 9,
      name: 'Python 开发',
      image: '/image/course/numerical.jpg',
      date: '2021-春季学期',
      status: '已结束',
      reach: 90
    },
    {
      id: 10,
      name: '数值分析',
      image: '/image/course/numerical.jpg',
      date: '2021-春季学期',
      status: '已结束',
      reach: 98
    }
    ]
  }
]

//题目类型
export const questionTypeList =[
  { id: 1, questionType: '单选题', count: 125},
  { id: 2, questionType: '多选题', count: 87},
  { id: 3, questionType: '判断题', count: 56},
  { id: 4, questionType: '填空题', count: 42},
  { id: 5, questionType: '简答题', count: 42},
  { id: 6, questionType: '计算题', count: 125},
  { id: 7, questionType: '论述题', count: 87},
  { id: 8, questionType: '材料分析题', count: 56},
  { id: 9, questionType: '综合分析题', count: 42},
]

//题目列表

//达成度
export const degreeByClass = {
    "2021": [
      {
        "name": "1班",
        "value": 78,
        "teacher": "张老师"
      },
      {
        "name": "2班",
        "value": 88,
        "teacher": "李老师"
      },
      {
        "name": "3班",
        "value": 70,
        "teacher": "王老师"
      },
      {
        "name": "4班",
        "value": 98,
        "teacher": "钱老师"
      }
    ],
    "2022": [
      {
        "name": "1班",
        "value": 78,
        "teacher": "张老师"
      },
      {
        "name": "2班",
        "value": 88,
        "teacher": "李老师"
      },
      {
        "name": "3班",
        "value": 70,
        "teacher": "王老师"
      },
      {
        "name": "4班",
        "value": 98,
        "teacher": "钱老师"
      }
    ],
    "2023": [
      {
        "name": "1班",
        "value": 78,
        "teacher": "张老师"
      },
      {
        "name": "2班",
        "value": 88,
        "teacher": "李老师"
      },
      {
        "name": "3班",
        "value": 70,
        "teacher": "王老师"
      },
      {
        "name": "4班",
        "value": 98,
        "teacher": "钱老师"
      },
      {
        "name": "5班",
        "value": 60,
        "teacher": "孙老师"
      },
      {
        "name": "6班",
        "value": 100,
        "teacher": "张老师"
      }
    ]
  }


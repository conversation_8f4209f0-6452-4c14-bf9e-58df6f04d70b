//教师个人信息模拟数据
import {Category, ImportedProblemFile} from "@/store/modules/indicator";

export const teacherInfoMockData =[
 {
    id: 1,
    name: '张三',
    number: '12345678901',
    gender: '男',
    title: '教授',
    college: '计算机科学与技术学院',
    //avatar: '@/assets/images/avatar.png',
    courseCount: 15,
    studentCount: 2000,
    email: '<EMAIL>',
    phone: '12345678901',
   courseList: [
            "微分方程",
            "数学建模",
            "数值分析",
            "最优化理论",
            "统计分析与应用",
            "概率论",
            "离散数学"
        ]
},
{
  id: 2,
  name: '赵六',
  number: '12345678901',
  gender: '男',
  title: '教授',
  college: '计算机科学与技术学院',
  //avatar: '@/assets/images/avatar.png',
  courseCount: 15,
  studentCount: 2000,
  email: 'z<PERSON><PERSON>@example.com',
  phone: '12345678901',
 courseList: [
          "微分方程",
          "数学建模",
          "数值分析",
          "最优化理论",
          "统计分析与应用",
          "概率论",
          "离散数学"
      ]
}

]
//添加依据id查询教师函数
export function getTeacherInfoById(id:number){
  const teacherInfo = teacherInfoMockData.find(t=>t.id===id)
  if(!teacherInfo){
    throw new Error(`教师ID为${id}不存在`)
  }
  return {
    ...teacherInfo,
  }
}

//教师教授课程模拟数据
export const teacherCourseMockData = [
    {
        id: 1,
        name: '高等数学',
        image: '/image/course/math.jpg',
        semester: '2024-春季学期',
        status: '进行中',
        reach: 98,
        assessment_method_id:1,
      },
      {
        id: 2,
        name: '软件工程',
        image: '/image/course/math.jpg',
        semester: '2024-春季学期',
        status: '进行中',
        reach: 78,
        assessment_method_id:2,
      },
      {
        id: 3,
        name: '线性代数',
        image: '/image/course/linear.jpg',
        semester: '2023-秋季学期',
        status: '已结束',
        reach: 68,
        assessment_method_id:1,
      },
      {
        id: 4,
        name: '概率论',
        image: '/image/course/prob.jpg',
        semester: '2023-春季学期',
        status: '已结束',
        reach: 90,
        assessment_method_id:3,
      },
      {
        id: 5,
        name: '离散数学',
        image: '/image/course/discrete.jpg',
        semester: '2022-秋季学期',
        status: '已结束',
        reach: 80,
        assessment_method_id:2,
      },
      {
        id: 6,
        name: '数值分析',
        image: '/image/course/numerical.jpg',
        semester: '2022-春季学期',
        status: '已结束',
        reach: 66,
        assessment_method_id:3,
      },
      {
        id: 7,
        name: '大学英语',
        image: '/image/course/numerical.jpg',
        semester: '2021-春季学期',
        status: '已结束',
        reach: 77,
        assessment_method_id:4,
      },
      {
        id: 8,
        name: '软件工程',
        image: '/image/course/numerical.jpg',
        semester: '2021-春季学期',
        status: '已结束',
        reach: 87,
        assessment_method_id:5,
      },
      {
        id: 9,
        name: 'Python 开发',
        image: '/image/course/numerical.jpg',
        semester: '2021-春季学期',
        status: '已结束',
        reach: 90,
        assessment_method_id:1,
      },
      {
        id: 10,
        name: '数值分析',
        image: '/image/course/numerical.jpg',
        semester: '2021-春季学期',
        status: '已结束',
        reach: 98,
        assessment_method_id:1,
      }
    ]
//作业模拟数据
export const teacherHomeworkMockData = [
    {
        id: 1,
        title: '数组的定义与访问（全整转换）',
        classes: 'R8软工1班241、242',
        classId: '241,242',
        startTime: '2024-03-11 10:45',
        endTime: '2024-03-11 23:55',
        submissionCount: 45,
        viewCount: 67,
        commentCount: 6,
        type: 'homework',
        status: 'finished'
      },
      {
        id: 2,
        title: '纸币找零，考察算数运算符的掌握情况',
        classes: 'R8软工1班241、242、243、244',
        classId: '241,242,243,244',
        startTime: '2024-04-10 14:00',
        endTime: '2024-04-15 23:55',
        submissionCount: 62,
        viewCount: 131,
        commentCount: 15,
        type: 'homework',
        status: 'in-progress'
      },
      {
        id: 3,
        title: '成绩分析编程练习',
        classes: 'R8软工1班241、242',
        classId: '241,242',
        startTime: '2024-04-20 10:00',
        endTime: '2024-04-25 23:59',
        submissionCount: 0,
        viewCount: 28,
        commentCount: 0,
        type: 'homework',
        status: 'not-started'
      },
      {
        id: 4,
        title: '结构体和类的基本使用',
        classes: 'R8软工1班243',
        classId: '243',
        startTime: '2024-03-25 08:30',
        endTime: '2024-03-30 23:59',
        submissionCount: 37,
        viewCount: 54,
        commentCount: 8,
        type: 'homework',
        status: 'finished'
      },
      {
        id: 5,
        title: '指针和动态内存管理',
        classes: 'R8软工1班244',
        classId: '244',
        startTime: '2024-04-12 14:00',
        endTime: '2024-04-18 23:59',
        submissionCount: 22,
        viewCount: 48,
        commentCount: 5,
        type: 'homework',
        status: 'in-progress'
      }
    ]
//试卷数据模拟
export const teacherExamMockData = [
    {
        id: 1,
        title: '数据结构期中考试',
        classes: 'R8软工1班241、242',
        classId: '241,242',
        startTime: '2024-03-20 10:45',
        endTime: '2024-03-20 12:45',
        submissionCount: 56,
        viewCount: 67,
        commentCount: 6,
        type: 'exam',
        status: 'finished' // 已结束
    },
    {
        id: 2,
        title: '算法设计与分析阶段测试',
        classes: 'R8软工1班241、242、243、244',
        classId: '241,242,243,244',
        startTime: '2024-04-15 14:00',
        endTime: '2024-04-15 16:00',
        submissionCount: 42,
        viewCount: 131,
        commentCount: 15,
        type: 'exam',
        status: 'in-progress' // 进行中
    },
    {
        id: 3,
        title: '面向对象程序设计期末考试',
        classes: 'R8软工1班243、244',
        classId: '243,244',
        startTime: '2024-05-20 09:00',
        endTime: '2024-05-20 11:00',
        submissionCount: 0,
        viewCount: 98,
        commentCount: 0,
        type: 'exam',
        status: 'not-started' // 未开始
    },
    {
        id: 4,
        title: '计算机网络阶段测试',
        classes: 'R8软工1班241',
        classId: '241',
        startTime: '2024-04-05 14:00',
        endTime: '2024-04-05 15:30',
        submissionCount: 32,
        viewCount: 45,
        commentCount: 3,
        type: 'exam',
        status: 'finished'
    },
    {
        id: 5,
        title: '操作系统原理随堂测试',
        classes: 'R8软工1班242',
        classId: '242',
        startTime: '2024-04-25 10:00',
        endTime: '2024-04-25 10:45',
        submissionCount: 0,
        viewCount: 38,
        commentCount: 0,
        type: 'exam',
        status: 'not-started'
    },
    {
        id: 6,
        title: '数据库系统概论期中考试',
        classes: 'R8软工1班243、244',
        classId: '243,244',
        startTime: '2024-04-12 14:00',
        endTime: '2024-04-12 16:00',
        submissionCount: 29,
        viewCount: 75,
        commentCount: 8,
        type: 'exam',
        status: 'in-progress'
    }
]

//课程达程度模拟数据
export const courseAchievementMockData = [
    { name: '目标1：掌握基本理论', max: 100, value: 85 },
    { name: '目标2：运用解决问题', max: 100, value: 78 },
    { name: '目标3：数据分析能力', max: 100, value: 92 },
    { name: '目标4：逻辑推理能力', max: 100, value: 80 },
    { name: '目标5：编程实现能力', max: 100, value: 75 }
]
//题库数据模拟
export const questionBankMockData = [
    {
        id: '1',
        type: 'single',
        title: '以下哪种数据结构适合用于实现队列？',
        content: '以下哪种数据结构适合用于实现队列？',
        subjectId: 'programming',
        difficulty: 'medium',
        score: 5,
        options: [
            { content: '栈', isCorrect: false },
            { content: '链表', isCorrect: true },
            { content: '二叉树', isCorrect: false },
            { content: '哈希表', isCorrect: false }
        ],
        blanks: [{ id: 1, answer: '' }],
        referenceAnswer: '链表是最适合实现队列的数据结构,因为它支持高效的头部删除和尾部插入操作。',
        answer: ''
    },
    {
        id: '2',
        type: 'multiple',
        title: '以下哪些排序算法的平均时间复杂度是O(nlogn)？',
        content: '以下哪些排序算法的平均时间复杂度是O(nlogn)？',
        subjectId: 'programming',
        difficulty: 'hard',
        score: 5,
        options: [
            { content: '冒泡排序', isCorrect: false },
            { content: '快速排序', isCorrect: true },
            { content: '归并排序', isCorrect: true },
            { content: '堆排序', isCorrect: true }
        ],
        blanks: [],
        referenceAnswer: '快速排序、归并排序和堆排序的平均时间复杂度都是O(nlogn)。',
        answer: ''
    },
]
// 模拟数据
export const mockCategories: Category[] = [
  {
    id: 'final',
    name: '期末考核',
    type: 'final',
    weight: 60,
    indicators: [
      {
        id: 'final-exam',
        name: '期末考试',
        weight: 60,
        preset: true,
        scores: [],
        categoryId: 'final',
        importedProblemFiles: []
      }
    ]
  },
  {
    id: 'regular',
    name: '平时考核',
    type: 'regular',
    weight: 40,
    indicators: [
      {
        id: 'attendance',
        name: '签到',
        weight: 10,
        preset: true,
        scores: [],
        categoryId: 'regular',
        importedProblemFiles: []
      },
      {
        id: 'homework',
        name: '平时作业',
        weight: 10,
        preset: true,
        scores: [],
        categoryId: 'regular',
        importedProblemFiles: []
      },
      {
        id: 'quiz',
        name: '课堂测验',
        weight:10,
        preset: true,
        scores: [],
        categoryId: 'regular',
        importedProblemFiles: []
      },
      {
        id: 'participation',
        name: '课堂参与',
        weight: 10,
        preset: true,
        scores: [],
        categoryId: 'regular',
        importedProblemFiles: []
      }
    ]
  }
];
//模拟文件数据
export const mockProblemFiles: Record<string, ImportedProblemFile[]> = {};


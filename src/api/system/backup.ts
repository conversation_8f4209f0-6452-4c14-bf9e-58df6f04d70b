import request, { uploadRequest } from '@/utils/request';

export interface BackupItem {
  backupId: number;
  backupName: string;
  backupType: number; // 1-手动备份，2-自动备份
  backupSize: string;
  backupPath: string;
  status: number; // 0-失败，1-成功
  description: string;
  createBy: string;
  createTime: string;
  remark: string;
}

export interface BackupConfig {
  autoBackupEnabled: boolean;
  backupTime: string;
  backupPath: string;
  maxBackupCount: number;
  compressionEnabled: boolean;
  excludeTables: string[];
}

/**
 * 获取备份记录列表
 * @param params 查询参数
 */
export function getBackupList(params: {
  current?: number;
  pageSize?: number;
  backupName?: string;
  backupType?: number;
  startTime?: string;
  endTime?: string;
}) {
  return request({
    url: '/backup/list',
    method: 'get',
    params: {
      current: params.current || 1,
      pageSize: params.pageSize || 10,
      backupName: params.backupName,
      backupType: params.backupType,
      startTime: params.startTime,
      endTime: params.endTime
    }
  });
}

/**
 * 创建数据备份
 * @param data 备份参数
 */
export function createBackup(data: {
  description: string;
  tables?: string[];
  remark?: string;
}) {
  return request({
    url: '/backup/create',
    method: 'post',
    data
  });
}

/**
 * 下载备份文件
 * @param backupId 备份ID
 */
export function downloadBackup(backupId: number) {
  return request({
    url: `/backup/download/${backupId}`,
    method: 'get',
    responseType: 'blob'
  });
}

/**
 * 恢复数据备份
 * @param backupId 备份ID
 * @param data 恢复参数
 */
export function restoreBackup(backupId: number, data: {
  tables?: string[];
  override?: boolean;
}) {
  return request({
    url: `/backup/restore/${backupId}`,
    method: 'post',
    data
  });
}

/**
 * 删除备份
 * @param backupId 备份ID
 */
export function deleteBackup(backupId: number) {
  return request({
    url: `/backup/delete/${backupId}`,
    method: 'delete'
  });
}

/**
 * 获取备份或恢复任务状态
 * @param taskId 任务ID
 */
export function getTaskStatus(taskId: string) {
  return request({
    url: `/backup/task/status/${taskId}`,
    method: 'get'
  });
}

/**
 * 获取备份配置
 */
export function getBackupConfig() {
  return request({
    url: '/backup/config',
    method: 'get'
  });
}

/**
 * 更新备份配置
 * @param config 备份配置
 */
export function updateBackupConfig(config: BackupConfig) {
  return request({
    url: '/backup/config',
    method: 'put',
    data: config
  });
}

/**
 * 上传备份文件
 * @param formData 表单数据，包含文件和描述信息
 */
export function uploadBackup(formData: FormData) {
  return uploadRequest({
    url: '/backup/upload',
    method: 'post',
    data: formData
  });
} 
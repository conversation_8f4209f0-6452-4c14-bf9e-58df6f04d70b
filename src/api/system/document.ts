import request from '@/utils/request';
import { UploadProps } from 'tdesign-vue-next';

// 文件类型
export interface DocumentItem {
  id: string;
  title: string;
  description: string;
  fileType: string;
  fileSize: number;
  createTime: string;
  updateTime: string;
  category: string;
  downloadUrl: string;
  viewUrl: string;
  permission: string[];
  uploadedBy: string;
  status: string;
}

// 文件类别
export interface DocumentCategory {
  id: string;
  name: string;
  description: string;
}

// 分页参数
export interface DocumentParams {
  page: number;
  pageSize: number;
  title?: string;
  category?: string;
  fileType?: string;
  status?: string;
  startDate?: string;
  endDate?: string;
}

// 获取文件列表
export function getDocumentList(params: DocumentParams) {
  // 模拟数据，实际应该调用接口
  return new Promise<{ list: DocumentItem[]; total: number }>((resolve) => {
    setTimeout(() => {
      const mockData: DocumentItem[] = [];
      const categories = ['教学组织', '专业认证', '质量监控', '教学计划', '课程建设', '师资队伍'];
      const fileTypes = ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx'];
      const permissions = [
        ['admin'],
        ['admin', 'teacher'],
        ['admin', 'teacher', 'student'],
        ['all']
      ];
      
      // 生成35条模拟数据
      for (let i = 1; i <= 35; i++) {
        const categoryIndex = Math.floor(Math.random() * categories.length);
        const fileTypeIndex = Math.floor(Math.random() * fileTypes.length);
        const permissionIndex = Math.floor(Math.random() * permissions.length);
        const date = new Date();
        date.setDate(date.getDate() - Math.floor(Math.random() * 365));
        
        mockData.push({
          id: `doc${i}`,
          title: `${categories[categoryIndex]}文件${i}`,
          description: `这是一份关于${categories[categoryIndex]}的重要文件，用于专业认证工作`,
          fileType: fileTypes[fileTypeIndex],
          fileSize: Math.floor(Math.random() * 10000000), // 随机文件大小
          createTime: date.toISOString(),
          updateTime: date.toISOString(),
          category: categories[categoryIndex],
          downloadUrl: `/api/document/download/${i}`,
          viewUrl: `/api/document/view/${i}`,
          permission: permissions[permissionIndex],
          uploadedBy: '管理员',
          status: Math.random() > 0.2 ? '已发布' : '草稿',
        });
      }
      
      // 过滤数据
      let filteredData = [...mockData];
      if (params.title) {
        filteredData = filteredData.filter(item => item.title.includes(params.title || ''));
      }
      if (params.category) {
        filteredData = filteredData.filter(item => item.category === params.category);
      }
      if (params.fileType) {
        filteredData = filteredData.filter(item => item.fileType === params.fileType);
      }
      if (params.status) {
        filteredData = filteredData.filter(item => item.status === params.status);
      }
      
      // 排序：最新的在前
      filteredData.sort((a, b) => {
        return new Date(b.updateTime).getTime() - new Date(a.updateTime).getTime();
      });
      
      // 分页
      const startIndex = (params.page - 1) * params.pageSize;
      const endIndex = startIndex + params.pageSize;
      const pagedData = filteredData.slice(startIndex, endIndex);
      
      resolve({
        list: pagedData,
        total: filteredData.length
      });
    }, 500);
  });
}

// 获取文件类别
export function getDocumentCategories() {
  return new Promise<DocumentCategory[]>((resolve) => {
    const categories = [
      { id: '1', name: '教学组织', description: '学校教学组织相关文件' },
      { id: '2', name: '专业认证', description: '专业认证相关文件资料' },
      { id: '3', name: '质量监控', description: '教学质量监控相关文件' },
      { id: '4', name: '教学计划', description: '各专业教学计划文件' },
      { id: '5', name: '课程建设', description: '课程建设相关文件' },
      { id: '6', name: '师资队伍', description: '师资队伍建设相关文件' },
    ];
    
    setTimeout(() => {
      resolve(categories);
    }, 300);
  });
}

// 上传文件
export function uploadDocument(options: UploadProps) {
  return new Promise<{ success: boolean; fileId: string }>((resolve) => {
    setTimeout(() => {
      resolve({
        success: true,
        fileId: `file_${Date.now()}`
      });
    }, 1000);
  });
}

// 删除文件
export function deleteDocument(id: string) {
  return new Promise<{ success: boolean }>((resolve) => {
    setTimeout(() => {
      resolve({ success: true });
    }, 500);
  });
}

// 更新文件信息
export function updateDocument(data: Partial<DocumentItem> & { id: string }) {
  return new Promise<{ success: boolean }>((resolve) => {
    setTimeout(() => {
      resolve({ success: true });
    }, 500);
  });
}

// 获取文件详情
export function getDocumentDetail(id: string) {
  return new Promise<DocumentItem>((resolve) => {
    setTimeout(() => {
      const mockItem: DocumentItem = {
        id,
        title: `教学文件${id}`,
        description: '这是一份关于教学组织的重要文件，用于专业认证工作',
        fileType: 'pdf',
        fileSize: 2356789,
        createTime: new Date().toISOString(),
        updateTime: new Date().toISOString(),
        category: '教学组织',
        downloadUrl: `/api/document/download/${id}`,
        viewUrl: `/api/document/view/${id}`,
        permission: ['admin', 'teacher'],
        uploadedBy: '管理员',
        status: '已发布',
      };
      resolve(mockItem);
    }, 300);
  });
} 
import request from '@/utils/request'

export function expireToken() {
  return request({
    url: '/user/expire-token',
    method: 'get',
  })
}

export function refreshToken() {
  return request({
    url: '/user/refresh-token',
    method: 'get',
  })
}
// 用户登录
export const login = async (data: any) => {
  return request({
    url: '/user/login',
    method: 'post',
    data,
  })
}
export function getUserInfo() {
  return request({
    url: '/user/info',
    method: 'get',
  })
}

export function logout() {
  return request({
    url: '/user/logout',
    method: 'get',
  })
}

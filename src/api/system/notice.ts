import request from '@/utils/request';

export interface NoticeItem {
  id: string;
  title: string;
  content: string;
  type: string;
  status: string;
  createTime: string;
  isRead: boolean;
}

// 分页获取通知列表
export function getNoticeList(params: any) {
  return request({
    url: '/notice/list',
    method: 'post',
    data: params
  });
}

// 获取通知详情
export function getNoticeDetail(id: string) {
  return request({
    url: `/notice/${id}`,
    method: 'get'
  });
}

// 添加通知
export function addNotice(data: Partial<NoticeItem>) {
  return request({
    url: '/notice',
    method: 'post',
    data
  });
}

// 更新通知
export function updateNotice(id: string, data: Partial<NoticeItem>) {
  return request({
    url: `/notice/${id}`,
    method: 'put',
    data
  });
}

// 删除通知
export function deleteNotice(id: string) {
  return request({
    url: `/notice/${id}`,
    method: 'delete'
  });
}

// 标记通知为已读
export function markNoticeAsRead(id: string) {
  return request({
    url: `/notice/${id}/read`,
    method: 'put'
  });
}

// 获取未读通知数量
export function getUnreadNoticeCount() {
  return request({
    url: '/notice/unread/count',
    method: 'get'
  });
}

// 标记所有通知为已读
export function markAllNoticeAsRead() {
  return request({
    url: '/notice/read/all',
    method: 'put'
  });
} 
import request from '@/utils/request';

// 获取角色列表
export function getRoleList(data: any) {
  return request({
    url: '/role/list',
    method: 'post',
    data
  });
}

// 添加角色
export function addRole(data: any) {
  return request({
    url: '/role',
    method: 'post',
    data
  });
}

// 更新角色
export function updateRole(data: any) {
  return request({
    url: '/role',
    method: 'put',
    data
  });
}

// 删除角色
export function deleteRole(id: number) {
  return request({
    url: `/role/${id}`,
    method: 'delete'
  });
}

// 获取角色详情
export function getRoleDetail(id: number) {
  return request({
    url: `/role/${id}`,
    method: 'get'
  });
}

// 更新角色状态
export function updateRoleStatus(data: any) {
  return request({
    url: '/role/status',
    method: 'put',
    data
  });
}

// 获取角色权限
export function getRolePermissions(roleId: number) {
  return request({
    url: `/role/permissions/${roleId}`,
    method: 'get'
  });
}

// 更新角色权限
export function updateRolePermissions(roleId: number, permissionIds: number[]) {
  return request({
    url: '/role/permissions',
    method: 'put',
    data: { roleId, permissionIds }
  });
}

// 更新角色菜单权限
export function updateRoleMenu(data: any) {
  return request({
    url: '/role/menu',
    method: 'put',
    data
  });
} 
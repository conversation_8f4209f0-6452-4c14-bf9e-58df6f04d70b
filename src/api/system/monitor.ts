import request from '@/utils/request';

// 获取实时监控数据
export function getRealtimeMonitorData() {
  return request({
    url: '/monitor/realtime',
    method: 'get'
  });
}

/**
 * 获取服务器信息
 * 返回服务器硬件信息、JVM信息、磁盘使用情况等
 */
export function getServerInfo() {
  return request({
    url: '/monitor/server',
    method: 'get'
  });
}

/**
 * 获取缓存监控信息
 * 返回系统缓存使用情况
 */
export function getCacheInfo() {
  return request({
    url: '/monitor/cache',
    method: 'get'
  });
}

/**
 * 获取在线用户列表
 * @param params 查询参数
 */
export function getOnlineUsers(params?: {
  current?: number;
  pageSize?: number;
  username?: string;
  ipaddr?: string;
}) {
  return request({
    url: '/monitor/online',
    method: 'get',
    params: {
      current: params?.current || 1,
      pageSize: params?.pageSize || 10,
      username: params?.username,
      ipaddr: params?.ipaddr
    }
  });
}

/**
 * 强制退出用户
 * @param tokenId 会话ID
 */
export function forceLogout(tokenId: string) {
  return request({
    url: `/monitor/online/${tokenId}`,
    method: 'delete'
  });
}

/**
 * 获取定时任务列表
 * @param params 查询参数
 */
export function getJobList(params?: {
  current?: number;
  pageSize?: number;
  jobName?: string;
  status?: number;
}) {
  return request({
    url: '/monitor/job',
    method: 'get',
    params: {
      current: params?.current || 1,
      pageSize: params?.pageSize || 10,
      jobName: params?.jobName,
      status: params?.status
    }
  });
}

/**
 * 获取定时任务详情
 * @param jobId 任务ID
 */
export function getJobDetail(jobId: number) {
  return request({
    url: `/monitor/job/${jobId}`,
    method: 'get'
  });
}

/**
 * 修改任务状态（启用/禁用）
 * @param jobId 任务ID
 * @param status 状态（0-暂停，1-正常）
 */
export function changeJobStatus(jobId: number, status: 0 | 1) {
  return request({
    url: '/monitor/job/changeStatus',
    method: 'put',
    data: {
      jobId,
      status
    }
  });
}

/**
 * 立即执行任务
 * @param jobId 任务ID
 */
export function runJob(jobId: number) {
  return request({
    url: '/monitor/job/run',
    method: 'post',
    data: {
      jobId
    }
  });
}

// 获取JVM信息
export function getJvmInfo() {
  return request({
    url: '/monitor/jvm',
    method: 'get'
  });
}

// 获取API调用统计
export function getApiStats() {
  return request({
    url: '/monitor/api/stats',
    method: 'get'
  });
}

// 获取CPU使用率时间序列数据
export function getCpuUsageSeries() {
  return request({
    url: '/monitor/cpu/usage/series',
    method: 'get'
  });
}

// 获取内存使用率时间序列数据
export function getMemoryUsageSeries() {
  return request({
    url: '/monitor/memory/usage/series',
    method: 'get'
  });
} 
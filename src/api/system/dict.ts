import request from '@/utils/request';

/**
 * 获取字典类型列表
 * @returns {Promise} 返回字典类型列表数据
 */
export function getDictTypeList() {
  return request({
    url: '/dict/type/list/all',
    method: 'get'
  });
}

/**
 * 分页获取字典类型列表
 * @returns {Promise} 返回字典类型列表数据
 */
export function getDictTypeListPage(data: any) {
  return request({
    url: '/dict/type/list/page',
    method: 'post',
    data
  });
}

/**
 * 添加字典类型
 * @param {Object} data 字典类型数据
 * @returns {Promise} 返回添加结果
 */
export function addDictType(data: any) {
  return request({
    url: '/dict/type',
    method: 'post',
    data
  });
}

/**
 * 更新字典类型
 * @param {Object} data 字典类型数据
 * @returns {Promise} 返回更新结果
 */
export function updateDictType(data: any) {
  return request({
    url: '/dict/type',
    method: 'put',
    data
  });
}

/**
 * 删除字典类型
 * @param {number} id 字典类型ID
 * @returns {Promise} 返回删除结果
 */
export function deleteDictType(id: number) {
  return request({
    url: `/dict/type/${id}`,
    method: 'delete'
  });
}

/**
 * 获取字典数据列表
 * @param {number} typeId 字典类型ID
 * @param {Object} params 分页参数
 * @returns {Promise} 返回字典数据列表
 */
export function getDictDataList(typeId: number, params?: { current: number; size: number }) {
  return request({
    url: `/dict/data/type/${typeId}`,
    method: 'get',
    params
  });
}

/**
 * 添加字典数据
 * @param {Object} data 字典数据
 * @returns {Promise} 返回添加结果
 */
export function addDictData(data: any) {
  return request({
    url: '/dict/data',
    method: 'post',
    data
  });
}

/**
 * 更新字典数据
 * @param {Object} data 字典数据
 * @returns {Promise} 返回更新结果
 */
export function updateDictData(data: any) {
  return request({
    url: '/dict/data',
    method: 'put',
    data
  });
}

/**
 * 删除字典数据
 * @param {number} id 字典数据ID
 * @returns {Promise} 返回删除结果
 */
export function deleteDictData(id: number) {
  return request({
    url: `/dict/data/${id}`,
    method: 'delete'
  });
}

/**
 * 获取所有字典数据（不分页）
 * @returns {Promise} 返回所有字典数据
 */
export function getAllDictData() {
  return request({
    url: '/dict/data/list/all',
    method: 'get'
  });
}

/**
 * 根据字典类型ID获取所有字典数据（不分页）
 * @param {number} typeId 字典类型ID
 * @returns {Promise} 返回指定类型的所有字典数据
 */
export function getDictDataByTypeId(typeId: number) {
  return request({
    url: `/dict/data/type/all/${typeId}`,
    method: 'get'
  });
} 
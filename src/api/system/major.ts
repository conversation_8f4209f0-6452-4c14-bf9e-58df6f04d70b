import { request } from '@/utils/request';

/**
 * 专业信息接口定义
 */
export interface MajorItem {
  id: string | number;       // 专业ID
  name: string;             // 专业名称
  code: string;             // 专业代码
  discipline?: string;       // 专业类型
  collegeId: string | number; // 所属学院ID
  collegeName: string;      // 学院名称
  directorId: string | number; // 专业负责人ID
  director: string;         // 专业负责人姓名
  creator: string;          // 创建人
  updater: string;          // 更新人
  createTime: string;       // 创建时间
  updateTime: string;       // 更新时间
  status: number;           // 状态：0-启用，1-停用，-1-删除
  courses: number;          // 课程数量
  classes: number;          // 班级数量
  students: number;         // 学生数量
  professionalOverview?: string; // 专业概述
}

/**
 * 获取专业列表（分页）
 */
export function getMajorList(params: any) {
  return request({
    url: '/base/major/list',
    method: 'get',
    params
  });
}

/**
 * 获取专业详情
 */
export function getMajorDetail(id: string | number) {
  return request({
    url: `/base/major/detail/${id}`,
    method: 'get'
  });
}

/**
 * 添加专业
 */
export function addMajor(data: Partial<MajorItem>) {
  return request({
    url: '/base/major/add',
    method: 'post',
    data
  });
}

/**
 * 更新专业
 */
export function updateMajor(data: Partial<MajorItem>) {
  return request({
    url: `/base/major/update`,
    method: 'put',
    data
  });
}

/**
 * 删除专业
 */
export function deleteMajor(id: string | number) {
  return request({
    url: '/base/major/delete',
    method: 'delete',
    params: { id }
  });
}

/**
 * 导入专业
 */
export function importMajors(formData: FormData) {
  return request({
    url: '/base/major/import',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
}

/**
 * 导出专业
 */
export function exportMajors(params: any) {
  return request({
    url: '/base/major/export',
    method: 'get',
    params,
    responseType: 'blob'
  });
}

/**
 * 下载导入模板
 */
export function downloadImportTemplate() {
  return request({
    url: '/base/major/export',
    method: 'get',
    params: { template: true },
    responseType: 'blob'
  });
}

/**
 * 获取专业负责人选项
 */
export function getAcademyLeaderOptions() {
  return request({
    url: '/base/major/academy-leader-options',
    method: 'get'
  });
}

import { request } from '@/utils/request';

// 毕业要求标准类型
export interface GraduationStandard {
  id: string;
  name: string;
  version: string;
  disciplineType: string; // 学科类型：工科、文科、商科等
  releaseDate: string;
  status: number; // 状态：0-正常状态，1-停用状态，-1-删除状态
  description: string;
  createTime: string;
  updateTime: string;
  createdBy: string;
  updatedBy: string;
  requirements: GraduationRequirement[];
}

// 具体毕业要求项
export interface GraduationRequirement {
  id: string;
  standardId: string;
  code: string; // 编号，如 "1"、"2"、"3" 等
  title: string;
  description: string;
  order: number;
}


// 获取毕业要求标准列表
export function getGraduationStandardList(data: any) {
  return request({
    url: '/base/standard/list',
    method: 'post',
    data
  });
}

// 获取毕业要求标准详情
export function getGraduationStandardDetail(id: string) {
  return request({
    url: `/base/standard/${id}`,
    method: 'get'
  });
}

// 创建毕业要求标准
export function createGraduationStandard(data: any) {

  return request({
    url: '/base/standard/create-with-indicators',
    method: 'post',
    data
  });
}

// 更新毕业要求标准
export function updateGraduationStandard(data: any) {
  return request({
    url: '/base/standard',
    method: 'put',
    data
  });
}

// 删除毕业要求标准
export function deleteGraduationStandard(id: string) {
  return request({
    url: `/base/standard/${id}`,
    method: 'delete'
  });
}

// 创建毕业要求
export function createGraduationRequirement(data: Partial<GraduationRequirement>) {
  // return request.post('/api/graduation-requirement', data);

  // 模拟创建成功
  return new Promise<{ success: boolean; id: string }>((resolve) => {
    setTimeout(() => {
      resolve({
        success: true,
        id: `new-req-${Date.now()}`
      });
    }, 500);
  });
}

// 更新毕业要求
export function updateGraduationRequirement(id: string, data: Partial<GraduationRequirement>) {
  // return request.put(`/api/graduation-requirement/${id}`, data);

  // 模拟更新成功
  return new Promise<{ success: boolean }>((resolve) => {
    setTimeout(() => {
      resolve({ success: true });
    }, 500);
  });
}

// 删除毕业要求
export function deleteGraduationRequirement(id: string) {
  // return request.delete(`/api/graduation-requirement/${id}`);

  // 模拟删除成功
  return new Promise<{ success: boolean }>((resolve) => {
    setTimeout(() => {
      resolve({ success: true });
    }, 500);
  });
}

// 获取学科类型列表
export function getDisciplineTypes() {
  return Promise.resolve([
    { value: '工科', label: '工科' },
    { value: '文科', label: '文科' },
    { value: '商科', label: '商科' },
    { value: '理科', label: '理科' },
    { value: '医学', label: '医学' },
    { value: '艺术', label: '艺术' },
    { value: '农学', label: '农学' },
    { value: '其他', label: '其他' }
  ]);
}

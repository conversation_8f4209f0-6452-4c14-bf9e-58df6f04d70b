import request from '@/utils/request'
import type {
  // 数据结构
  CourseBasicInfo,
  AssessmentSection,
  CourseObjective,
  AssessmentContent,
  DirectInputConfig,
  DetailedInputConfig,
  
  // API接口
  GetCourseBasicInfoRequest,
  GetCourseBasicInfoResponse,
  GetAssessmentSectionsRequest,
  GetAssessmentSectionsResponse,
  GetCourseObjectivesRequest,
  GetCourseObjectivesResponse,
  GetAssessmentContentsRequest,
  GetAssessmentContentsResponse,
  GetAssessmentConfigRequest,
  GetDirectConfigResponse,
  GetDetailedConfigResponse,
  SaveDirectConfigRequest,
  SaveDetailedConfigRequest,
  SaveConfigResponse
} from '@/types/assessment'

// 1. 课程基本信息相关API
export const courseBasicApi = {
  // 获取课程基本信息
  getCourseBasicInfo(params: GetCourseBasicInfoRequest): Promise<GetCourseBasicInfoResponse> {
    return request({
      method: 'GET',
      url: `/api/course/basic/${params.courseId}`
    })
  },

  // 更新课程基本信息
  updateCourseBasicInfo(courseId: string, data: Partial<CourseBasicInfo>) {
    return request({
      method: 'PUT',
      url: `/api/course/basic/${courseId}`,
      data
    })
  }
}

// 2. 考核环节相关API
export const assessmentSectionApi = {
  // 获取考核环节列表
  getAssessmentSections(params: GetAssessmentSectionsRequest): Promise<GetAssessmentSectionsResponse> {
    return request.get('/api/assessment/sections', { params })
  },

  // 创建考核环节
  createAssessmentSection(data: Omit<AssessmentSection, 'id' | 'createTime' | 'updateTime'>) {
    return request.post('/api/assessment/sections', data)
  },

  // 更新考核环节
  updateAssessmentSection(sectionId: string, data: Partial<AssessmentSection>) {
    return request.put(`/api/assessment/sections/${sectionId}`, data)
  },

  // 删除考核环节
  deleteAssessmentSection(sectionId: string) {
    return request.delete(`/api/assessment/sections/${sectionId}`)
  },

  // 发布考核环节
  publishAssessmentSection(sectionId: string) {
    return request.post(`/api/assessment/sections/${sectionId}/publish`)
  }
}

// 3. 课程目标相关API
export const courseObjectiveApi = {
  // 获取课程目标列表
  getCourseObjectives(params: GetCourseObjectivesRequest): Promise<GetCourseObjectivesResponse> {
    return request.get('/api/course/objectives', { params })
  },

  // 创建课程目标
  createCourseObjective(data: Omit<CourseObjective, 'id' | 'createTime' | 'updateTime'>) {
    return request.post('/api/course/objectives', data)
  },

  // 更新课程目标
  updateCourseObjective(objectiveId: string, data: Partial<CourseObjective>) {
    return request.put(`/api/course/objectives/${objectiveId}`, data)
  },

  // 删除课程目标
  deleteCourseObjective(objectiveId: string) {
    return request.delete(`/api/course/objectives/${objectiveId}`)
  },

  // 批量更新课程目标权重
  updateObjectiveWeights(courseId: string, weights: Array<{ id: string; weight: number }>) {
    return request.put(`/api/course/${courseId}/objectives/weights`, { weights })
  }
}

// 4. 考核内容相关API
export const assessmentContentApi = {
  // 获取考核内容列表
  getAssessmentContents(params: GetAssessmentContentsRequest): Promise<GetAssessmentContentsResponse> {
    return request.get('/api/assessment/contents', { params })
  },

  // 创建考核内容
  createAssessmentContent(data: Omit<AssessmentContent, 'id' | 'createTime' | 'updateTime'>) {
    return request.post('/api/assessment/contents', data)
  },

  // 更新考核内容
  updateAssessmentContent(contentId: string, data: Partial<AssessmentContent>) {
    return request.put(`/api/assessment/contents/${contentId}`, data)
  },

  // 删除考核内容
  deleteAssessmentContent(contentId: string) {
    return request.delete(`/api/assessment/contents/${contentId}`)
  },

  // 发布考核内容
  publishAssessmentContent(contentId: string) {
    return request.post(`/api/assessment/contents/${contentId}/publish`)
  },

  // 复制考核内容
  copyAssessmentContent(contentId: string, targetSectionId: string) {
    return request.post(`/api/assessment/contents/${contentId}/copy`, { targetSectionId })
  }
}

// 5. 考核内容配置相关API
export const assessmentConfigApi = {
  // 获取直接录入配置
  getDirectConfig(params: GetAssessmentConfigRequest): Promise<GetDirectConfigResponse> {
    return request.get(`/api/assessment/config/direct/${params.contentId}`)
  },

  // 获取详细录入配置
  getDetailedConfig(params: GetAssessmentConfigRequest): Promise<GetDetailedConfigResponse> {
    return request.get(`/api/assessment/config/detailed/${params.contentId}`)
  },

  // 保存直接录入配置
  saveDirectConfig(params: SaveDirectConfigRequest): Promise<SaveConfigResponse> {
    return request.post(`/api/assessment/config/direct/${params.contentId}`, params.config)
  },

  // 保存详细录入配置
  saveDetailedConfig(params: SaveDetailedConfigRequest): Promise<SaveConfigResponse> {
    return request.post(`/api/assessment/config/detailed/${params.contentId}`, params.config)
  },

  // 删除配置
  deleteConfig(contentId: string, inputMode: 'direct' | 'detailed') {
    return request.delete(`/api/assessment/config/${inputMode}/${contentId}`)
  },

  // 复制配置
  copyConfig(sourceContentId: string, targetContentId: string, inputMode: 'direct' | 'detailed') {
    return request.post(`/api/assessment/config/${inputMode}/copy`, {
      sourceContentId,
      targetContentId
    })
  },

  // 导出配置
  exportConfig(contentId: string, inputMode: 'direct' | 'detailed') {
    return request.get(`/api/assessment/config/${inputMode}/${contentId}/export`, {
      responseType: 'blob'
    })
  },

  // 导入配置
  importConfig(contentId: string, inputMode: 'direct' | 'detailed', file: File) {
    const formData = new FormData()
    formData.append('file', file)
    return request.post(`/api/assessment/config/${inputMode}/${contentId}/import`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  }
}

// 6. 统计分析相关API
export const assessmentAnalysisApi = {
  // 获取考核环节统计
  getSectionStatistics(courseId: string) {
    return request.get(`/api/assessment/analysis/sections/${courseId}`)
  },

  // 获取课程目标达成度分析
  getObjectiveAchievement(courseId: string) {
    return request.get(`/api/assessment/analysis/objectives/${courseId}`)
  },

  // 获取学生成绩分布
  getGradeDistribution(contentId: string) {
    return request.get(`/api/assessment/analysis/grades/${contentId}`)
  },

  // 获取题目难度分析
  getQuestionDifficulty(contentId: string) {
    return request.get(`/api/assessment/analysis/difficulty/${contentId}`)
  }
}

// 7. 批量操作相关API
export const assessmentBatchApi = {
  // 批量创建考核内容
  batchCreateContents(sectionId: string, contents: Array<Omit<AssessmentContent, 'id' | 'sectionId' | 'createTime' | 'updateTime'>>) {
    return request.post(`/api/assessment/contents/batch`, { sectionId, contents })
  },

  // 批量更新考核内容状态
  batchUpdateContentStatus(contentIds: string[], status: 'draft' | 'published' | 'ended') {
    return request.put('/api/assessment/contents/batch/status', { contentIds, status })
  },

  // 批量删除考核内容
  batchDeleteContents(contentIds: string[]) {
    return request.delete('/api/assessment/contents/batch', { data: { contentIds } })
  },

  // 批量导出配置
  batchExportConfigs(contentIds: string[]) {
    return request.post('/api/assessment/config/batch/export', { contentIds }, {
      responseType: 'blob'
    })
  }
}

// 8. 模板相关API
export const assessmentTemplateApi = {
  // 获取配置模板列表
  getConfigTemplates(inputMode: 'direct' | 'detailed') {
    return request.get(`/api/assessment/templates/${inputMode}`)
  },

  // 创建配置模板
  createConfigTemplate(name: string, description: string, config: DirectInputConfig | DetailedInputConfig) {
    return request.post('/api/assessment/templates', { name, description, config })
  },

  // 应用配置模板
  applyConfigTemplate(contentId: string, templateId: string) {
    return request.post(`/api/assessment/config/apply-template`, { contentId, templateId })
  },

  // 删除配置模板
  deleteConfigTemplate(templateId: string) {
    return request.delete(`/api/assessment/templates/${templateId}`)
  }
}

// 默认导出所有API
export default {
  courseBasicApi,
  assessmentSectionApi,
  courseObjectiveApi,
  assessmentContentApi,
  assessmentConfigApi,
  assessmentAnalysisApi,
  assessmentBatchApi,
  assessmentTemplateApi
} 
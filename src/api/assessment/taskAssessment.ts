import { request } from '@/utils/request';

// 定义数据结构
const BASE_URL = '/teaching/assessment/task';

// 考核VO接口定义
export interface AssessmentVO {
  id: number;
  taskId: number;
  courseId: number;
  assessmentName: string;
  description: string;
  assessmentMethod: number;
  assessmentDate: string;
  assessmentWeight: number;
  assessmentDetailList: any[];
  assessmentYear: number;
  assessmentTerm: number;
  scoreType: number;
  assessmentStatus: number;
  achievement: boolean;
  status: number;
  creator: number;
  createTime: string;
  modifier: number;
  modifyTime: string;
}

// 根据任务ID获取考核列表
export async function getAssessmentListByTaskId(taskId: number): Promise<AssessmentVO[]> {
  try {
    const res = await request({
      url: `${BASE_URL}/${taskId}`,
      method: 'get'
    });

    if (res.code === 200) {
      return res.data;
    }
    throw new Error(res?.message || '获取考核列表失败');
  } catch (error) {
    console.error('获取考核列表失败:', error);
    throw error;
  }
}

import { request } from '@/utils/request';

// 定义数据结构
const BASE_URL = '/teaching/achievement';

/**
 * 获取课程目标达成度分析
 * @param courseId 课程ID
 * @returns 课程目标达成度分析数据
 */
export const getCourseAchievement = (courseId: string) => {
  return request({
    url: `${BASE_URL}/course/${courseId}`,
    method: 'get'
  });
};
/**
 * 获取教学任务目标达成度分析
 * @param taskId 教学任务ID
 * @returns 教学任务目标达成度分析数据
 */
export const getTaskAchievement = (taskId: string) => {
  return request({
    url: `${BASE_URL}/task/${taskId}`,
    method: 'get'
  });
};

/**
 * 获取考核目标达成度分析
 * @param assessmentId 考核ID
 * @returns 考核目标达成度分析数据
 */
export const getAssessmentAchievement = (assessmentId: string) => {
  return request({
    url: `${BASE_URL}/assessment/${assessmentId}`,
    method: 'get'
  });
};

/**
 * 获取课程目标达成度分析（按课程编号）
 * @param courseCode 课程编号
 * @returns 一组课程的课程目标达成度分析数据
 */
export const getCourseAchievementByCode = (courseCode: string) => {
  return request({
    url: `${BASE_URL}/course/code/${courseCode}`,
    method: 'get'
  });
};
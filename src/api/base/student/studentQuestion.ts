import request from '@/utils/request';
import {
  StudentQuestionListResult,
  StudentQuestionModifyResult,
  StudentRepliedListResult
} from "@/api/model/student/studentQuestionl";
import {StudentPaperListResult} from "@/api/model/student/studentPaperModel";

const Api = {
  questionList: '/student/questionnaire/list',
  repliedList: '/student/replied/list',
  PaperList: '/student/questionList/list',

};


//获取问卷数据
export async function getList(): Promise<StudentPaperListResult> {
  try {
    const res = await request({
      url: 'api/student/questionList/list',
      method: 'get'
    });
    return res;
  } catch (error) {
    console.error('获取问卷数据失败:', error);
    throw error;
  }
}


//获取问卷题目数据
export async function getQuestionnaireList(pid:string): Promise<StudentQuestionListResult> {
  try {
    const res = await request({
      url: 'api/student/questionnaire/list',
      method: 'get',
      params: { pid }
    });
    return res;
  } catch (error) {
    console.error('获取问卷题目数据失败:', error);
    throw error;
  }
}


export async function putQuestionnaireList(data:StudentRepliedListResult): Promise<string> {
  try {
    const res = await request({
      url: 'api/student/replied/list',
      method: 'post',
      data
    });
    return res;
  } catch (error) {
    console.error('提交问卷数据失败:', error);
    throw error;
  }
}

//获取问卷题目和回答数据
export async function getReplyList(pid:string): Promise<StudentQuestionModifyResult> {
  try {
    const res = await request({
      url: 'api/student/questionList/modify/list',
      method: 'get',
      params: { pid }
    });
    return res;
  } catch (error) {
    console.error('获取问卷题目和回答数据失败:', error);
    throw error;
  }
}

import request from '@/utils/request';
import {
  StudentCourseAttainmentListResult,
  StudentStudiesAttainmentListResult,
  StudentCourseGraduateListResult, StudentScoreListResult
} from "@/api/model/student/studentAttainmentModel";
import {StudentHomeListResult} from "@/api/model/student/studentHomeModel";


//获取对应课程数据
export async function getCourseList(cid:string): Promise<StudentCourseAttainmentListResult> {
  try {
    const res = await request({
      url: 'api/student/Course/list',
      method: 'get',
      params: { cid }
    });
    return res;
  } catch (error) {
    console.error('获取对应课程数据失败:', error);
    throw error;
  }
}


// 获取学业达成数据
export async function getStudyList(cid:string): Promise<StudentStudiesAttainmentListResult> {
    try {
        const res = await request({
          url: '/api/student/Study/list',
          method: 'get',
          params: { cid }
        });
        return res;
    } catch (error) {
        console.error('获取学业达成数据失败:', error);
        throw error;
    }
}


// 相关关系数据获取方法
export async function getCourseGraduateList(cid:string): Promise<StudentCourseGraduateListResult> {
    try {
        const res = await request({
          url: '/api/student/CourseGraduate/list',
          method: 'get',
          params: { cid }
        });
        return res;
    } catch (error) {
        console.error('获取相关关系数据失败:', error);
        throw error;
    }
}



//成绩详情数据获取方法
export async function getStudentScoreList(cid:string): Promise<StudentScoreListResult> {
    try {
        const res = await request({
          url: '/api/student/StudentScore/list',
          method: 'get',
          params: { cid }
        });
        return res;
    } catch (error) {
        console.error('获取成绩详情数据失败:', error);
        throw error;
    }
}


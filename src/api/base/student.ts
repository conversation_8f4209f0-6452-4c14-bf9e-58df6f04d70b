import request, { uploadRequest } from '@/utils/request';

// 定义学籍状态常量映射
export const STUDENT_STATUS = {
  IN_SCHOOL: 0,   // 在读
  ON_LEAVE: 1,    // 休学
  DROPPED: 2,     // 退学
  GRADUATED: 3    // 毕业
};

// 定义状态值到文本的映射
export const STUDENT_STATUS_TEXT = {
  [STUDENT_STATUS.IN_SCHOOL]: '在读',
  [STUDENT_STATUS.ON_LEAVE]: '休学',
  [STUDENT_STATUS.DROPPED]: '退学',
  [STUDENT_STATUS.GRADUATED]: '毕业'
};

// 将状态码转换为文本显示
export function getStatusText(status: number | string | undefined | null): string {
  if (status === null || status === undefined) return '未知';

  console.log('状态值类型和值:', typeof status, status);

  // 处理数字状态值
  if (typeof status === 'number') {
    const text = STUDENT_STATUS_TEXT[status] || '未知';
    console.log(`数字状态 ${status} 转换为: ${text}`);
    return text;
  }

  // 尝试将字符串转换为数字，可能API返回的是字符串形式的数字
  if (typeof status === 'string' && !isNaN(Number(status))) {
    const numStatus = Number(status);
    if (STUDENT_STATUS_TEXT[numStatus]) {
      const text = STUDENT_STATUS_TEXT[numStatus];
      console.log(`字符串数字状态 ${status} 转换为数字 ${numStatus} 再转换为: ${text}`);
      return text;
    }
  }

  // 否则，直接返回字符串
  console.log(`直接返回状态字符串: ${status}`);
  return String(status);
}

export interface StudentItem {
  id: number | string;
  name: string;
  gender: number | string; // 可能是 "男"/"女" 或 0/1
  studentId: string;
  college: string;
  collegeId: string | number;
  major: string;
  majorId: string | number;
  className: string;
  enrollmentYear: string;
  status: string | number;
  creator?: string;
  createTime: string;
  updater?: string;
  updateTime: string;
  image?: string; // 学生照片URL
}
/**
 * 分页获取学生列表
 * @param params 分页和筛选参数
 * @returns Promise<{list: StudentItem[], total: number}>
 */

export function getStudentList(data: any) {
  return request({
    url: '/base/student/list',
    method: 'post',
    data
  });
}
/**
 * 根据ID查询学生详细信息（包含更多字段）
 * @param id 学生ID
 * @returns 学生详细信息
 */
export async function getStudentDetailById(id: string | number): Promise<StudentDetailResult> {
  try {
    const res = await request({
      url: '/base/student/detail',
      method: 'GET',
      params: { id }
    });
    return res;
  } catch (error) {
    console.error('获取学生详细信息失败:', error);
    throw error;
  }
}

/**
 * 获取学生树结构数据
 * @returns 树结构数据
 */
export async function getStudentTree(): Promise<any> {
  try {
    const res = await request({
      url: '/base/student/tree',
      method: 'GET'
    });
    return res;
  } catch (error) {
    console.error('获取学生树结构失败:', error);
    throw error;
  }
} 
/**
 * 添加学生
 */
export function addStudent(data: any) {
  return request({
    url: '/base/student',
    method: 'post',
    data
  });
}
/**
 * 根据学院ID获取学生列表
 * @param collegeId 学院ID
 * @returns Promise<StudentItem[]>
 */
export async function getStudentByCollegeId(collegeId: string | number) {
}

/**
 * 根据专业ID获取学生列表
 * @param majorId 专业ID
 * @returns Promise<StudentItem[]>
 */
export async function getStudentByMajorId(majorId: string | number) {

}
/**
 * 获取专业详情
 */
export function getStudentDetail(id: number | string) {
  return request({
    url: `/base/student/${id}`,
    method: 'get'
  });
}
/**
 * 更新学生信息
 */
export function updateStudent(data: any) {
  return request({
    url: `/base/student`,
    method: 'put',
    data
  });
}
/**
 * 删除学生
 */
export function deleteStudent(id: string | number) {
  return request({
    url: `/base/student/${id}`,
    method: 'delete',
  });
}
/**
 * 导入学生数据
 * @param formData 包含文件的FormData对象
 */
export function importStudents(formData: FormData) {
  return uploadRequest({
    url: '/base/student/import',
    method: 'post',
    data: formData
  });
}

/**
 * 导出学生数据
 * @param params 导出条件
 */
export function exportStudents(params?: {
  academyId?: string | number;
  majorId?: string | number;
  studentName?: string;
  studentNumber?: string;
  className?: string;
  gender?: number;
  entranceYear?: string;
  studentStatus?: number;
}) {
  return request({
    url: '/base/student/export',
    method: 'get',
    params,
    responseType: 'blob'
  });
}



// src/api/teachers/category.ts
import request from '@/utils/request';
import { Category, Indicator, ImportedProblemFile } from '@/store/modules/indicator';
import { mockCategories, mockProblemFiles } from '@/api/mock/teacherMockData';

// API响应类型定义
interface ApiResponse<T = any> {
  code: number;
  message: string;
  data: T;
}

// 接口类型定义
export interface AssessmentCategory {
  id: string;
  name: string;
  type: string;
  weight: number;
  indicators: Indicator[];
}

export interface ProblemFile {
  id: string;
  fileName: string;
  importDate: string;
  problems: any[];
  weight: number;
  scores?: any[];
}

// 错误处理函数
const handleApiError = (error: any, fallbackData: any) => {
  console.error('API请求失败:', error);
  return fallbackData;
};

// 判断是否使用 mock 数据
const isMock = import.meta.env.DEV;

export const categoryApi = {
  // 获取所有考核分类
  getCategories: async (): Promise<AssessmentCategory[]> => {
    if (isMock) {
      return mockCategories;
    }
    try {
      const res = await request({
        url: '/teacher/{teacher_id}/course/{course_id}/{assessment_id}',
        method: 'get'
      }) as ApiResponse<AssessmentCategory[]>;
      if (res?.code === 200) {
        return res.data;
      }
      return mockCategories;
    } catch (error) {
      return handleApiError(error, mockCategories);
    }
  },

  // 更新考核分类
  updateCategories: async (categories: AssessmentCategory[]): Promise<void> => {
    if (isMock) {
      // 在 mock 模式下，直接更新模拟数据
      mockCategories.splice(0, mockCategories.length, ...categories);
      return;
    }
    try {
      const res = await request({
        url: '/assessment/{assessment_id}',
        method: 'put',
        data: categories
      }) as ApiResponse;
      if (res?.code !== 200) {
        throw new Error('更新考核分类失败');
      }
    } catch (error) {
      handleApiError(error, null);
      throw error;
    }
  },

  // 导入题目文件
  importProblems: async (indicatorId: string, file: File): Promise<ProblemFile> => {
    if (isMock) {
      // 模拟文件上传
      const mockFile: ProblemFile = {
        id: `file_${Date.now()}`,
        fileName: file.name,
        importDate: new Date().toISOString(),
        problems: [],
        weight: 0,
        scores: []
      };

      if (!mockProblemFiles[indicatorId]) {
        mockProblemFiles[indicatorId] = [];
      }
      mockProblemFiles[indicatorId].push(mockFile);

      return mockFile;
    }

    try {
      const formData = new FormData();
      formData.append('file', file);
      const res = await request({
        url: `/api/assessment/indicators/${indicatorId}/problems`,
        method: 'post',
        data: formData,
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      }) as ApiResponse<ProblemFile>;

      if (res?.code === 200) {
        return res.data;
      }
      throw new Error('导入题目文件失败');
    } catch (error) {
      handleApiError(error, null);
      throw error;
    }
  },

  // 获取题目文件列表
  getProblemFiles: async (indicatorId: string): Promise<ProblemFile[]> => {
    if (isMock) {
      return mockProblemFiles[indicatorId] || [];
    }
    try {
      const res = await request({
        url: `/api/assessment/indicators/${indicatorId}/problems`,
        method: 'get'
      }) as ApiResponse<ProblemFile[]>;

      if (res?.code === 200) {
        return res.data;
      }
      return [];
    } catch (error) {
      return handleApiError(error, []);
    }
  },

  // 删除题目文件
  deleteProblemFile: async (indicatorId: string, fileId: string): Promise<void> => {
    if (isMock) {
      if (mockProblemFiles[indicatorId]) {
        mockProblemFiles[indicatorId] = mockProblemFiles[indicatorId].filter(
          file => file.id !== fileId
        );
      }
      return;
    }
    try {
      const res = await request({
        url: `/api/assessment/indicators/${indicatorId}/problems/${fileId}`,
        method: 'delete'
      }) as ApiResponse;

      if (res?.code !== 200) {
        throw new Error('删除题目文件失败');
      }
    } catch (error) {
      handleApiError(error, null);
      throw error;
    }
  },

  // 更新题目文件权重
  updateProblemWeight: async (indicatorId: string, fileId: string, weight: number): Promise<void> => {
    if (isMock) {
      if (mockProblemFiles[indicatorId]) {
        const file = mockProblemFiles[indicatorId].find(f => f.id === fileId);
        if (file) {
          file.weight = weight;
        }
      }
      return;
    }
    try {
      const res = await request({
        url: `/api/assessment/indicators/${indicatorId}/problems/${fileId}/weight`,
        method: 'put',
        data: { weight }
      }) as ApiResponse;

      if (res?.code !== 200) {
        throw new Error('更新题目文件权重失败');
      }
    } catch (error) {
      handleApiError(error, null);
      throw error;
    }
  },

 /* // 导入成绩
  importScores: async (indicatorId: string, fileId: string, file: File): Promise<any[]> => {
    if (isMock) {
      // 模拟成绩导入
      const mockScores = Array.from({ length: 10 }, (_, i) => ({
        studentId: `student_${i + 1}`,
        studentName: `学生${i + 1}`,
        score: Math.floor(Math.random() * 100)
      }));

      if (mockProblemFiles[indicatorId]) {
        const problemFile = mockProblemFiles[indicatorId].find(f => f.id === fileId);
        if (problemFile) {
          problemFile.scores = mockScores;
        }
      }

      return mockScores;
    }

    try {
      const formData = new FormData();
      formData.append('file', file);
      const res = await systemRequest.post(
        `/api/assessment/indicators/${indicatorId}/problems/${fileId}/scores`,
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data'
          }
        }
      ) as ApiResponse<any[]>;

      if (res?.code === 200) {
        return res.data;
      }
      throw new Error('导入成绩失败');
    } catch (error) {
      handleApiError(error, null);
      throw error;
    }
  }*/
};

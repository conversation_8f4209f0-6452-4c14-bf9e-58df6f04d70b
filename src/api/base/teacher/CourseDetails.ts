import request from '@/utils/request';

// 定义课程目标接口
interface CourseGoal {
  name: string;
  value: number;
}

// 定义课程详情接口
interface Course {
  id: number;
  name: string;
  class: string;
  code: string;
  studentCount: number;
  semester: string;
  status: string;
  goals: CourseGoal[];
}

/**
 * 获取课程详情
 * 根据OpenAPI规范，使用正确的参数格式
 * @param CourseId 课程ID
 * @returns 课程详情数据
 */
export async function getCourseDetails(CourseId: string | number): Promise<Course> {
  try {
    const res = await request({
      url: '/teacher/getCoursesDetail',
      method: 'get',
      params: { CourseId }
    });
    return res;
  } catch (error) {
    console.error('获取课程详情信息失败:', error);
    throw error;
  }
}

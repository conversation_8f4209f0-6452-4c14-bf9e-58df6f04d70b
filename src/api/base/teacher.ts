import request, {uploadRequest} from '@/utils/request';

export function getTeacherOptionsListByAcademyId(majorId: number) {
  return request({
    url: `/base/teacher/teacher-by-major`,
    method: 'get',
    params: { majorId }
  });
}

export interface TeacherItem {
  id: string | number; // 后端返回的是id字段，对应teacher_id
  number: string | number; // 工号
  title: string; // 职称
  academyId: string | number; // 学院ID
  status?: string | number; // 状态
  creator?: string;
  creatorName?: string;
  modifierName?: string;
  createTime: string;
  modifier?: string;
  modifyTime: string;
  email?: string;
  // 用户基本信息（嵌套对象）
  user?: {
    id: string | number;
    name: string; // 教师姓名
    gender: number | string; // 0女，1男 或 "男"/"女"
    phone: string;
    email: string;
    username: string;
    avatar?: string;
  };
  // 学院信息（嵌套对象）
  academy?: {
    academyId: string | number;
    academyName: string;
    academyCode?: string;
  };
  // 为了兼容现有代码，提供便捷的getter属性
  teacher_id?: string | number; // 兼容字段
  teacher_name?: string; // 兼容字段
  teacher_number?: string | number; // 兼容字段
  gender?: number | string; // 兼容字段
  teacher_title?: string; // 兼容字段
  academy_id?: string | number; // 兼容字段
  academy_name?: string; // 兼容字段
  phone?: string; // 兼容字段
  image?: string;
}

export interface TeacherListParams {
  current: number;
  size: number;
  academyId?: number | string;
  teacherName?: string;
  teacherNumber?: string;
  teacherTitle?: string;
  gender?: number;
  status?: number;
}

export interface TeacherListResult {
  records: TeacherItem[];
  total: number;
  size: number;
  current: number;
  pages: number;
}

/**
 * 分页获取教师列表
 * @param params 分页和筛选参数
 * @returns Promise<{code: number, data: TeacherListResult, msg: string}>
 */
export function getTeacherList(params: TeacherListParams) {
  return request({
    url: '/base/teacher/list',
    method: 'get',
    params: params
  });
}

/**
 * 获取教师详情
 * @param teacher_id 教师ID
 * @returns Promise<{code: number, data: TeacherItem, msg: string}>
 */
export function getTeacherDetail(teacher_id: string | number) {
  return request({
    url: `/base/teacher/detail/${teacher_id}`,
    method: 'get'
  });
}

/**
 * 添加教师
 * @param data 教师数据
 * @returns Promise<{code: number, data: boolean, msg: string}>
 */
export function addTeacher(data: any) {
  return request({
    url: '/base/teacher',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json'
    }
  });
}

/**
 * 编辑教师
 * @param data 教师数据，应包含teacherId和其他字段
 * @returns Promise<{code: number, data: boolean, msg: string}>
 */
export function updateTeacher(data: any) {
  return request({
    url: '/base/teacher',
    method: 'put',
    data,
    headers: {
      'Content-Type': 'application/json'
    }
  });
}

/**
 * 获取教师职称选项
 * @returns Promise<{code: number, data: {label: string, value: string}[], msg: string}>
 */
export function getTeacherTitleOptions() {
  return request({
    url: '/base/teacher/teacher-title-options',
    method: 'get'
  });
}

/**
 * 删除教师
 * @param id 教师ID
 * @returns Promise<{code: number, data: boolean, msg: string}>
 */
export function deleteTeacher(id: string | number) {
  return request({
    url: `/base/teacher/${id}`,
    method: 'delete'
  });
}

/**
 * 批量删除教师
 * @param ids 教师ID列表
 * @returns Promise<{code: number, data: boolean, msg: string}>
 */
export function batchDeleteTeachers(ids: (string | number)[]) {
  return request({
    url: '/base/teacher/batch',
    method: 'delete',
    data: ids,
    headers: {
      'Content-Type': 'application/json'
    }
  });
}

/**
 * 停用教师
 * @param id 教师ID
 * @returns Promise<{code: number, data: boolean, msg: string}>
 */
export function disableTeacher(id: string | number) {
  return request({
    url: `/base/teacher/disable/${id}`,
    method: 'put'
  });
}

/**
 * 启用教师
 * @param id 教师ID
 * @returns Promise<{code: number, data: boolean, msg: string}>
 */
export function enableTeacher(id: string | number) {
  return request({
    url: `/base/teacher/enable/${id}`,
    method: 'put'
  });
}

/**
 * 重置教师密码
 * @param id 教师ID
 * @returns Promise<{code: number, data: boolean, msg: string}>
 */
export function resetTeacherPassword(id: string | number) {
  return request({
    url: `/base/teacher/reset-password/${id}`,
    method: 'put'
  });
}

/**
 * 导入教师
 * @param file 文件对象
 * @returns Promise<{code: number, data: any, msg: string}>
 */
export function importTeachers(file: File) {
  const formData = new FormData();
  formData.append('file', file);

  return uploadRequest({
    url: '/base/teacher/import',
    method: 'post',
    data: formData,
    // 不设置Content-Type，让浏览器自动设置正确的multipart/form-data边界参数
  });
}

/**
 * 导出教师数据
 * @param params 查询参数
 * @returns Promise<Blob>
 */
export function exportTeacherData(params: any) {
  return request({
    url: '/base/teacher/export',
    method: 'get',
    params,
    responseType: 'blob'
  });
}

/**
 * 获取教师统计信息
 * @returns Promise<{code: number, data: any, msg: string}>
 */
export function getTeacherStatistics() {
  return request({
    url: '/base/teacher/statistics',
    method: 'get'
  });
}

/**
 * 获取教师组织结构树
 * @returns Promise<{code: number, data: any, msg: string}>
 */
export function getTeacherTree() {
  return request({
    url: '/base/teacher/tree',
    method: 'get'
  });
}





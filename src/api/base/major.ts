import request from '@/utils/request';

/**
 * 专业信息接口定义
 */
export interface MajorItem {
  id: string | number;       // 专业ID
  name: string;             // 专业名称
  code: string;             // 专业代码
  collegeId?: string | number | null; // 所属学院ID（可选）
  collegeName?: string;      // 学院名称（可选）
  directorId?: string | number | null; // 专业负责人ID（可选）
  director?: string;         // 专业负责人姓名（可选）
  creator?: string;          // 创建人（可选）
  updater?: string;          // 更新人（可选）
  createTime?: string;       // 创建时间（可选）
  updateTime?: string;       // 更新时间（可选）
  status: number;           // 状态：0-启用，1-停用，-1-删除
  courses?: number;          // 课程数量（可选）
  classes?: number;          // 班级数量（可选）
  students?: number;         // 学生数量（可选）
  professionalOverview?: string; // 专业概述
}

// 专业选项接口
export interface MajorOptionsVO {
  value: string | number;
  label: string;
  collegeId?: string | number;
}

// 专业表单数据接口
export interface MajorFormData {
  majorId?: string | number;
  majorName: string;
  majorCode?: string;
  academyId: string | number;
  academyLeaderId?: string | number;
  professionalOverview?: string;
  discipline?: string;
  status?: number;
}

/**
 * 获取专业列表（分页）
 */
export function getMajorList(params: any) {
  return request({
    url: '/base/major/list',
    method: 'get',
    params
  });
}

/**
 * 获取专业详情
 */
export function getMajorDetail(id: string | number) {
  return request({
    url: `/base/major/detail/${id}`,
    method: 'get'
  });
}

/**
 * 根据学院ID获取专业列表
 */
export function getMajorsByCollegeId(collegeId: string | number) {
  return request({
    url: `/base/major/${collegeId}/options`,
    method: 'get'
  });
}

/**
 * 获取专业选项列表
 */
export function getMajorOptions() {
  return request({
    url: '/base/major/options',
    method: 'get'
  });
}
/**
 * 根据课程ID获取专业信息
 */
export function getMajorByCourseId(courseId: string | number) {
  return request({
    url: `/base/major/course/${courseId}`,
    method: 'get'
  });
}

/**
 * 添加专业
 */
export function addMajor(data: MajorFormData) {
  return request({
    url: '/base/major/add',
    method: 'post',
    data
  });
}

/**
 * 检查专业名称是否存在
 */
export function checkMajorNameExists(majorName: string, academyId: string | number, excludeId?: string | number) {
  return request({
    url: '/base/major/check-name',
    method: 'get',
    params: { majorName, academyId, excludeId }
  });
}

/**
 * 更新专业
 */
export function updateMajor(data: Partial<MajorItem>) {
  return request({
    url: `/base/major/update`,
    method: 'put',
    data
  });
}

/**
 * 删除专业
 */
export function deleteMajor(id: string | number) {
  return request({
    url: '/base/major/delete',
    method: 'delete',
    params: { id }
  });
}

/**
 * 导入专业
 */
export function importMajors(formData: FormData) {
  return request({
    url: '/base/major/import',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
}

/**
 * 导出专业
 */
export function exportMajors(params: any) {
  return request({
    url: '/base/major/export',
    method: 'get',
    params,
    responseType: 'blob'
  });
}

/**
 * 下载导入模板
 */
export function downloadImportTemplate() {
  return request({
    url: '/base/major/export',
    method: 'get',
    params: { template: true },
    responseType: 'blob'
  });
}

/**
 * 获取专业负责人选项
 */
export function getAcademyLeaderOptions() {
  return request({
    url: '/base/major/academy-leader-options',
    method: 'get'
  });
}

export function getMyMajors() {
  return request({
    url: '/base/major/my-majors',
    method: 'get'
  });
}

/**
 * 获取教师负责的专业选择器数据
 */
export function getMajorSelectorByTeacher() {
  return request({
    url: '/base/major/teacher/selector',
    method: 'get'
  });
}

/**
 * 获取课程负责的专业选择器数据
 */
export function getMajorSelectorByCourseLeader() {
  return request({
    url: '/base/major/course-leader/selector',
    method: 'get'
  });
}

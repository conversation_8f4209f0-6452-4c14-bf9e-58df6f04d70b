import request from '@/utils/request';

// 用户信息接口定义
export interface UserInfo {
  id: number;
  name: string;
  username: string;
  phone?: string;
  email?: string;
  gender?: number;
  avatar?: string;
  status?: number;
}

// 班级信息接口定义 - 与后端ClassesDTO/ClassesVO保持一致
export interface ClassItem {
  classId: number;
  majorId: number;
  className: string;
  entranceYear: string; // LocalDate 在前端用字符串表示
  headteacherId: number;
  headteacherName?: string; // 关联查询字段
  user?: UserInfo; // 班主任用户信息
  studentNumber: number;
  classStatus: number; // 班级状态 -1:毕业 0:在读
  status: number; // 记录状态 0:正常 -1:删除 1:停用
  creator: number;
  createTime: string; // LocalDateTime 在前端用字符串表示
  modifier: number;
  modifyTime: string; // LocalDateTime 在前端用字符串表示
}

// 班级查询参数接口 - 与后端ClassesQueryDTO保持一致
export interface ClassQueryParams {
  classId?: number;
  majorId?: number;
  className?: string;
  entranceYear?: string;
  headteacherId?: number;
  studentNumber?: number;
  classStatus?: number;
  status?: number;
  creator?: number;
  createTime?: string;
  modifier?: number;
  modifyTime?: string;
  current: number;
  size: number;
}

// 班级新增/编辑参数接口 - 与后端ClassesDTO保持一致
export interface ClassFormData {
  classId?: number;
  majorId: number;
  className: string;
  entranceYear: string;
  headteacherId?: number;
  studentNumber?: number;
  classStatus?: number;
  status?: number;
}

// 教学任务信息接口定义
export interface WorklistItem {
  id: string | number;
  courseId: string | number;
  courseName: string;
  entranceYear?: string | number;
  taskNumber: number;
  classId: string | number;
  className: string;
  teacherId: string | number;
  teacherName: string;
  teacherTitle: string; // 教师职称
  teacherAcademyName: string; // 教师所属学院
  teacherRole?: string; // 教师角色
  taskYear: number;
  taskTerm: number;
  studentCount: number;
  teachWeek: number;
  weekHours: number;
  totalHours: number;
  courseLeaderId: string | number;
  courseLeaderName: string;
  scheduleInfo?: {
    time?: string; // 上课时间
    location?: string; // 教室信息
  } | Array<{
    time: string; // 上课时间
    location: string; // 教室信息
  }>;
}

// 团队成员信息接口定义
export interface TeamMemberItem {
  id: string | number;
  teacherId: string | number;
  name: string;
  title: string; // 职称
  academyName: string; // 所属学院
  avatar?: string;
  role: string; // 角色名称：课程负责人、主讲教师、助教等
  roleCode: string; // 角色代码：1、2、3等
  email?: string;
  classCount: number; // 所教授班级统计
  studentCount: number; // 所教授学生统计
}

// 学生导入接口定义
export interface ImportStudentItem {
  studentNumber: string; // 学号
  studentName: string; // 姓名
  className?: string; // 班级名称（可选）
  status?: 'success' | 'error' | 'warning'; // 导入状态
  message?: string; // 导入信息
}

// 班级选项接口
export interface ClassOptionsVO {
  value: string | number;
  label: string;
  majorId?: string | number;
}

/**
 * 获取班级列表（分页）
 */
export function getClassesList(params: ClassQueryParams) {
  return request({
    url: '/base/classes/list',
    method: 'post',
    data: params
  });
}

/**
 * 获取课程相关的教学任务列表（包含班级和教师信息）
 */
export function getCourseWorklist(courseId: string | number) {
  return request({
    url: '/training/worklist/course',
    method: 'get',
    params: { courseId }
  });
}

/**
 * 获取课程团队成员列表
 */
export function getCourseTeamMembers(courseId: string | number) {
  return request({
    url: '/training/course/team-members',
    method: 'get',
    params: { courseId }
  });
}

/**
 * 获取班级详情
 */
export function getClassDetail(classId: number) {
  return request({
    url: `/base/classes/${classId}`,
    method: 'get'
  });
}

/**
 * 添加班级
 */
export function addClass(data: ClassFormData) {
  return request({
    url: '/base/classes',
    method: 'post',
    data
  });
}

/**
 * 更新班级
 */
export function updateClass(data: ClassFormData) {
  return request({
    url: '/base/classes',
    method: 'put',
    data
  });
}

/**
 * 删除班级
 */
export function deleteClass(classId: number) {
  return request({
    url: `/base/classes/${classId}`,
    method: 'delete'
  });
}

/**
 * 停用班级
 */
export function stopClass(classId: number) {
  return request({
    url: `/base/classes/using/${classId}`,
    method: 'delete'
  });
}

/**
 * 导入学生到班级
 */
export function importStudentsToClass(data: {
  classId: string | number;
  students: ImportStudentItem[];
}) {
  return request({
    url: '/base/classes/import-students',
    method: 'post',
    data
  });
}

/**
 * 预览导入学生数据
 */
export function previewImportStudents(formData: FormData) {
  return request({
    url: '/base/classes/preview-import',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
}

/**
 * 获取教师选项列表
 */
export function getTeacherOptions() {
  return request({
    url: '/base/teacher/options',
    method: 'get'
  });
}

/**
 * 导入班级数据
 */
export function importClasses(formData: FormData) {
  return request({
    url: '/base/classes/import',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
}

/**
 * 根据专业ID获取班级列表
 */
export function getClassesByMajorId(majorId: string | number) {
  return request({
    url: `/base/classes/${majorId}/list`,
    method: 'get'
  });
}

/**
 * 获取班级选项列表
 */
export function getClassOptions() {
  return request({
    url: '/base/classes/options',
    method: 'get'
  });
}

/**
 * 检查班级名称是否存在
 */
export function checkClassNameExists(className: string, majorId: string | number, excludeId?: string | number) {
  return request({
    url: '/base/classes/check-name',
    method: 'get',
    params: { className, majorId, excludeId }
  });
}

// 同步班级人数参数接口
export interface SyncStudentCountParams {
  academyId?: number;
  majorId?: number;
  classId?: number;
  entranceYear?: string;
  studentStatus?: number;
}

// 同步班级人数结果接口
export interface SyncStudentCountResult {
  success: boolean;
  totalClasses: number;
  updatedClasses: number;
  errorCount: number;
  duration: number;
  message: string;
}

/**
 * 预览同步班级人数
 */
export function previewSyncStudentCount(params: SyncStudentCountParams) {
  return request({
    url: '/base/classes/preview-sync-student-count',
    method: 'get',
    params
  });
}

/**
 * 同步班级人数
 * 根据条件统计各班级的实际学生人数并更新班级表中的冗余字段
 */
export function syncStudentCount(params: SyncStudentCountParams) {
  return request<any>({
    url: '/base/classes/sync-student-count',
    method: 'post',
    params
  });
}

export interface StudentPaperListResult {
  code:number
  message:string
  list: Array<StudentPaperInfo>;
}
export interface StudentPaperInfo {
  pid: number;//试卷id
  paperName: string;//试卷名称
  courseName: string;//课程名称
  cid:number;//课程id
  teacher:string//老师姓名
  released:string//发布时间
  startTime:string;//开始时间
  endTime:string;//截止时间
  status:number;//状态
}

export interface StudentInformationListResult {
  code:number
  message:string
  list: Array<StudentInformationInfo>;
}
export interface StudentInformationInfo {
  paperName: string;//试卷名称
  courseName: string;//课程名称
  startTime:string;//开始时间
  endTime:string;//结束时间
  submitTime:string;//提交时间
  grade:number;//成绩
}

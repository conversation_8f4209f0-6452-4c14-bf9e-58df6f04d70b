export interface StudentInformationResult {
  code:number
  message:string
  list: Array<StudentInformationsInfo>;
}
export interface StudentInformationsInfo {
  sid:number;//学生id
  name:string;//姓名
  number:string;//学号
  semester:string;//学期
  phone:string;//电话
  school:string;//学校名称
  class:string;//班级
  specialized:string;//专业
  dormitory:string;//宿舍
  awards:string;//奖项
  posts:string;//职位
  political:string;//政治面貌
  matriculation:string;//入学
  graduate:string;//毕业
}

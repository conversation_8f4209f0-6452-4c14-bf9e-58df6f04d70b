import {getCourseObjectivesList} from "@/api/courses/questionBank";

export interface CoursePaperListResult {
  code:number
  message:string
  list: Array<CoursePaperListInfo>;
}
export interface CoursePaperListInfo {
  id:number;//试卷id
  name: string;//试卷名称
  description:string;//试卷描述
  createTime: string;//创建时间
}

export interface CourseQuestionListResult {
  code:number
  message:string
  list: Array<CourseQuestionListInfo>;
}
export interface CourseQuestionListInfo {
  id: number;
  type: number;
  title: string;
  options: string[];
  objectives:string[];
  score:number
}

export interface CourseObjectivesResult {
  code:number
  message:string
  list: Array<CourseObjectivesInfo>;
}
export interface CourseObjectivesInfo {
  id: number;//课程id
  name: string;//课程名称
}

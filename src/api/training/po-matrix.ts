import request from '@/utils/request';
import { PoVO } from '@/api/training/po'; // Assuming PoMatrix is defined in the same file or imported correctly

export interface PoMatrix {
  id?: number;
  poId?: number;
  courseId?: number;
  weight?: number;
  standardId?: number;
  planId?: number;
  majorId?: number;
  academyId?: number;
  status?: number;
  creator?: number;
  createTime?: string; // LocalDateTime in Java typically maps to ISO string in JSON
  modifier?: number;
  modifyTime?: string; // LocalDateTime in Java typically maps to ISO string in JSON
}

export interface PoMatrixPoVO extends PoMatrix {
  po?: PoVO; // Assuming PoVO is defined in the same file or imported correctly
}
export interface CoursePoVO extends PoMatrix {
  courseName?: string;
  poNumber?:number; // Number of the PoVO
  poTitle?: string; // Name of the PoVO
  poDescription?: string; // Description of the PoVO
  standardNumber:number;
  //children?: CoursePoVO[]; // Nested children for tree structure
  //poList?: CoursePoVO[]; // List of PoVO objects associated with the course
}

const BASE_URL = '/tp/po-matrix'; // Ignored /api/

/**
 * 创建 PoMatrix 记录
 * @param data PoMatrix 对象
 */
export function addPoMatrix(data: PoMatrix): Promise<PoMatrix> {
  return request<PoMatrix>({
    url: `${BASE_URL}`,
    method: 'post',
    data,
  });
}

/**
 * 根据ID获取 PoMatrix 记录
 * @param id 记录ID
 */
export function getPoMatrixById(id: number): Promise<PoMatrix> {
  return request<PoMatrix>({
    url: `${BASE_URL}/${id}`,
    method: 'get',
  });
}

/**
 * 根据课程ID和毕业要求ID获取 PoMatrix 记录
 * @param params 包含 courseId 和 poId
 */
export function getPoMatrixByCourseAndPo(params: { courseId: number; poId: number }): Promise<PoMatrix> {
  return request<PoMatrix>({
    url: `${BASE_URL}/findByCourseAndPo`,
    method: 'get',
    params,
  });
}

/**
 * 根据培养计划ID获取 PoMatrix 记录列表
 * @param planId 培养计划ID
 * TODO: 后端接口没有
 */
export function getPoMatrixByPlanId(planId: number): Promise<PoMatrix[]> {
  return request<PoMatrix[]>({
    url: `${BASE_URL}/by-plan/${planId}`,
    method: 'get',
  });
}

/**
 * 根据课程id获得该课程记录列表
 * @param data 查询参数 (PoMatrix 对象),List<RoVO>
 */
export async function getPoListFromMatrix(courseId : number): Promise<CoursePoVO[]> {
  const res = await request({
    url: `${BASE_URL}/po-tree/${courseId}`,
    method: 'get'
  });
  if (res.code === 200) {
    return res.data;
  }
  return Promise.reject(new Error('获取课程指标点列表失败'));
}

/**
 * 根据课程ID获取 PoTree 结构
 * @param courseId 课程ID
 */
// 返回值为 CoursePoVO 对象数组，表示该课程下的所有指标点树形
export async function getPoTreeFromMatrixByCourseId(courseId: number): Promise<CoursePoVO[]> {
  const res = await request({
    url: `${BASE_URL}/po-tree/${courseId}`, 
    method: 'get',
    params: { courseId }
  });
  if(res.code === 200) {
    console.log('获取课程指标点树形结构成功', res.data);
    return res.data;
  }
  return Promise.reject(new Error('获取课程指标点树形结构失败'));
}

/**
 * 根据条件查询PoMatrix记录列表
 * @param data 查询参数 (PoMatrix 对象)
 */
export async function listPoMatrix(data?: PoMatrix): Promise<PoMatrix[]> {
  const res = await request({
    url: `${BASE_URL}/list`,
    method: 'post',
    data,
  });
  if (res.code === 200) {
    return res.data;
  }
  return Promise.reject(new Error('查询 PoMatrix 记录列表失败'));
}

/**
 * 更新指定ID的 PoMatrix 记录
 * @param id 记录ID
 * @param data 更新的 PoMatrix 数据
 */
export function updatePoMatrix(id: number, data: PoMatrix): Promise<PoMatrix> {
  return request<PoMatrix>({
    url: `${BASE_URL}/${id}`,
    method: 'put',
    data,
  });
}

/**
 * 删除指定ID的 PoMatrix 记录
 * @param id 记录ID
 */
export function deletePoMatrix(id: number): Promise<boolean> {
  return request<boolean>({
    url: `${BASE_URL}/${id}`,
    method: 'delete',
  });
}



import request from '@/utils/request';
import {
    AssessmentMethodResponse,
    basicInformationInfo,
    basicSubmitFromInfo,
    GoalInfo,
} from "@/api/model/course/teachingSyllabus";

//获取毕业目标点和对应的二级指标点
export async function getCoursesGoal(cid: string): Promise<GoalInfo[]> {
  try {
    const res = await request({
      url: `/course/goal/${cid}`,
      method: 'get'
    });
    return res;
  } catch (error) {
    console.error('获取所有目标信息失败:', error);
    throw error;
  }
}

//提交毕业目标点和对应的二级指标点课程目标
export async function putCoursesGoal(data: GoalInfo[]): Promise<string> {
  try {
    const res = await systemRequest.post(`/course/courseGoal`,data) as ApiResponse;
    return res.data;
    throw new Error(res?.message || '提交目标信息失败');
  } catch (error) {
    console.error('提交所有目标信息失败:', error);
    throw error;
  }
}



//获取课程基本信息（课程大纲的表单信息）
export async function getFromData(cid: string): Promise<basicInformationInfo[]> {
  try {
    const res = await systemRequest.get(`/course/information/${cid}`) as ApiResponse;
    return res.data;
    throw new Error(res?.message || '获取课程基本信息失败');
  } catch (error) {
    console.error('获取课程基本信息失败:', error);
    throw error;
  }
}


//提交课程基本信息（课程大纲的表单信息）
export async function putFromData(data: basicSubmitFromInfo[]): Promise<string> {
  try {
    const res = await systemRequest.post(`/course/information`,data) as ApiResponse;
    return res.data;
    throw new Error(res?.message || '提交课程基本信息失败');
  } catch (error) {
    console.error('提交课程基本信息失败:', error);
    throw error;
  }
}


//获取考核占比的表头和对应的占比
export async function getPercentage(cid: string): Promise<AssessmentMethodResponse[]> {
    try {
        const res = await systemRequest.get(`/course/assessment/proportion/${cid}`) as ApiResponse;
        return res.data;
    } catch (error) {
        console.error('获取课程目标占比失败:', error);
        throw error;
    }
}
//提交考核占比的表头和对应的占比
export async function putPercentage(data: AssessmentMethodResponse[]): Promise<string> {
  try {
    const res = await systemRequest.post(`/course/assessment/proportion`,data) as ApiResponse;
    return res.data;
    throw new Error(res?.message || '提交目标信息失败');
  } catch (error) {
    console.error('提交所有目标信息失败:', error);
    throw error;
  }
}

//提交excel
export async function putFile(data: AssessmentMethodResponse[]): Promise<string> {
  try {
    const res = await systemRequest.post(`/course/file`,data) as ApiResponse;
    return res.data;
    throw new Error(res?.message || '提交目标信息失败');
  } catch (error) {
    console.error('提交所有目标信息失败:', error);
    throw error;
  }
}

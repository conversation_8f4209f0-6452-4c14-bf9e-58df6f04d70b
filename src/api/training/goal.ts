import request from '@/utils/request';
import axios, { AxiosError } from 'axios';
import {Api} from "@vitejs/plugin-vue";


// 培养目标表
export interface tp_eo {
  eoTitle: string;          // 原 eo_title
  eoDescription: string;    // 原 eo_description
  planId: number;           // 原 plan_id
  majorId: number;          // 原 major_id
  academyId: number;        // 原 academy_id
  status: number;
  creator: number;
  createTime: string;       // 原 create_time
  modifier: number;
  modifyTime: string;       // 原 modify_time
  id: number;
}

//专业表
export interface base_major {
  majorName: string;
  professionalOverview: string;
  majorCode: string;
  academyId: number;
  academyLeaderId: number;
  status: number;
  creator: number;
  createTime: string;
  modifier: number;
  modifyTime: string;
  majorId: number;
}
export const getGoal = async (params:any) =>{
  try {
    const requestData = {
      planId: params.planId
    }
    console.log("请求参数：",requestData)
    const res = await request({
      url: '/tp/eo/getList',
      method: 'get',
      params: requestData
    })
    console.log('返回数据:',res)
    return res;
  }catch (error){
    console.error('获取api失败',error)
    throw error
  }
}


/**
 * 创建培养目标
 * @param params 培养目标数据
 * @returns 创建结果
 */
export const createGoal = async (params: Partial<tp_eo>) => {
  try {
    console.log("创建培养目标请求参数：", params);

    const res = await request({
      url: '/tp/eo/add',
      method: 'post',
      data: params
    });
    console.log('创建培养目标返回数据:', res);
    return res;
  } catch (error) {
    console.error('创建培养目标失败', error);
    throw error;
  }
};

export const delGoal = async (params:any) => {
  try {
    const requestData = {
      id: params.id
    }

    console.log('请求参数:',requestData)
    const res = await systemRequest.delete(`/tp/eo/del/${requestData.id}`) as ApiResponse
    console.log('返回参数：',res)

    if(res && res.code === 200){
      return res
    }else{
      throw new Error(res.message || '删除培养目标失败')
    }
  }catch(error) {
    console.error('删除api失败',error)
  }
}

export const modGoal = async (params: {id: number, eoDescription: string}) => {
  try {
    // 构造请求数据
    const requestData = {
      'eo-description': params.eoDescription // 注意这里使用后端要求的字段名
    };

    console.log('请求参数:', {
      path: params.id,
      body: requestData
    });

    // 发送 DELETE 请求，包含路径参数和请求体
    const res = await systemRequest.delete(`/tp/eo/mod/${params.id}`, {
      data: requestData // axios 中 DELETE 请求的 body 需要通过 data 参数传递
    }) as ApiResponse;

    console.log('返回参数：', res);

    if(res && res.code === 200){
      return res;
    }else{
      throw new Error(res.message || '更新培养目标失败');
    }
  } catch(error) {
    console.error('更新api失败', error);
    throw error; // 建议将错误继续抛出，让调用方处理
  }
}

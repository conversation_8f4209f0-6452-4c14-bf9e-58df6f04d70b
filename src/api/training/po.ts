import request from '@/utils/request';
export interface PoVO {
  id: number;
  poNumber: Number;
  poTitle: string; 
  title?: string;
  supportLevel?: string;
  planId: Number;
  requirementId: Number;
  isRequirement: boolean;
  parentId: Number;
  standardId: Number;
  standardNumber:Number;
  status: Number; 
  poDescription: string;
  description?: string;
}
// 查询某毕业要求下的全部指标点
export function getPoList(planId: number) {
  return request({
    url: '/tp/po/getList',
    method: 'get',
    params: { plan_id: planId }
  });
}

// 查询某毕业要求下的全部指标点
export function getPoTree(planId: number) {
  return request({
    url: '/tp/po/tree',
    method: 'get',
    params: { plan_id: planId }
  });
}

// 新增指标点
export function addPo(data: any) {
  return request({
    url: '/tp/po/add',
    method: 'post',
    data
  });
}

// 查询专业毕业要求一级指标
export function getPoRequirement(data: any) {
  return request({
    url: '/tp/po/getRequirement',
    method: 'post',
    data
  });
}

// 修改指标点
export function updatePo(data: any) {
  return request({
    url: '/tp/po/mod',
    method: 'put',
    data
  });
}

// 删除指标点
export function deletePo(id: number) {
  return request({
    url: '/tp/po/del',
    method: 'delete',
    params: { id }
  });
}

// PoVO接口定义
// export interface PoVO {
//   id: number;
//   poTitle: string;
//   poDescription: string;
//   requirementId: string;
//   planId: number;
//   status: number;
//   createTime?: string;
//   modifyTime?: string;
// }

// 根据毕业要求ID获取指标点详情
export function getRequirementById(id: number) {
  return request({
    url: `/tp/po/getById/${id}`,
    method: 'get'
  });
}

// 根据培养方案ID获取指标点映射关系（Map<PoVO, List<PoVO>>）
export function getPoMapByPlanId(planId: number) {
  return request({
    url: `/tp/po/getPoMapByPlanId/${planId}`,  // 错误：使用路径参数
    method: 'get'
  });
}

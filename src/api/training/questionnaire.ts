import request from "@/utils/request";
import axios, { AxiosError } from "axios";

// API响应类型定义
interface ApiResponse<T = any> {
  code: number;
  message: string;
  data: T;
}

// 问卷表
export interface Questionnaire {
  questionnaire_id?: number | string;
  role_id?: number;
  teacher_id?: string;
  questionnaire_name?: string;
  graduation_year?: string;
  questionnaire_type?: number;
  create_time?: string;
  update_time?: string;
  status?: number; // 0: 草稿, 1: 已发布, -1: 已删除
  create_by?: string|number;
  update_by?: string|number;
}

// 问卷题集关系表
export interface Questionnaire_Question_Set {
  id?: number;
  questionnaire_id?: number | string;
  question_id?: number | string;
  score?: number | string;
  create_time?: string;
  update_time?: string;
  status?: number | string;
  create_by?: string|number;
  update_by?: string|number;
}

// 创建问卷请求参数
export interface CreateQuestionnaireRequest {
  questionnaire: Partial<Questionnaire>;
  questionSet: Partial<Questionnaire_Question_Set>[];
}

// 创建问卷响应
export interface CreateQuestionnaireResponse {
  id: number;
}

// 删除问卷响应
export interface DeleteQuestionnaireResponse {
  status: number;
}

// 更新问题分数响应
export interface UpdateQuestionScoreResponse {
  question_id: number;
  score: number;
  update_time: string;
  update_by: string;
}

export interface ModQuestionResponse{
  question_id:number;
  score:number;
  update_time:string;
  update_by:number;
}

/**
 * 新增问卷
 * @param data 问卷数据
 */
export async function addQuestionnaire(
  data: CreateQuestionnaireRequest
): Promise<ApiResponse<CreateQuestionnaireResponse>> {
  try {
    const res = await request({
      url: "/questionnaireQuestion/questionnaireCreate",
      method: 'post',
      data
    });
    return res;
  } catch (error) {
    handleAxiosError(error, "创建问卷失败");
    throw error;
  }
}

/**
 * 删除问卷
 * @param questionnaire_id 问卷ID
 */
export async function deleteQuestionnaire(
  questionnaire_id: number | string
): Promise<ApiResponse<DeleteQuestionnaireResponse>> {
  try {
    const res = await request({
      url: `/questionnaireSurvey/questionnaireDel/${questionnaire_id}`,
      method: 'delete'
    });
    return res;
  } catch (error) {
    handleAxiosError(error, "删除问卷失败");
    throw error;
  }
}

/**
 * 批量更新问卷问题分数
 * @param updates 更新数据 { question_id: number | string, score: number | string }[]
 */
export async function batchUpdateQuestionScores(
  updates: Array<{ question_id: number | string; score: number | string }>
): Promise<ApiResponse<UpdateQuestionScoreResponse>> {
  try {
    const res = await request({
      url: `/questionnaireSurvey/batchUpdateScores`,
      method: 'patch',
      data: { updates }
    });
    return res;
  } catch (error) {
    handleAxiosError(error, "批量更新问题分数失败");
    throw error;
  }
}

export async function modQuestionnaire(
  mods: Array<{id: number | string;questionnaire_id:number | string; question_id: string | number;
    score:number | string ; update_time: string | number; status : number}>
):Promise<ApiResponse<ModQuestionResponse>>{
  try {
    const res = await request({
      url: `/questionnaireQuestion/questionnaireMod`,
      method: 'patch',
      data: { mods }
    });
    return res;
  }catch(error){
    handleAxiosError(error,"修改失败")
  }
}

/**
 * 处理Axios错误
 * @param error 错误对象
 * @param defaultMessage 默认错误消息
 */
function handleAxiosError(error: unknown, defaultMessage: string): void {
  if (axios.isAxiosError(error)) {
    const err = error as AxiosError;
    if (err.response) {
      const message = (err.response.data as Record<string, any>)?.message || defaultMessage;
      console.error(`${defaultMessage}:`, err.response.data);
      throw new Error(message);
    } else {
      console.error(`${defaultMessage}:`, err.message);
      throw new Error(err.message || defaultMessage);
    }
  } else {
    console.error("未知错误:", error);
    throw new Error(defaultMessage);
  }
}



export const CreateQuestion = async (data: any) => {
  console.log('提交的数据:', JSON.stringify(data, null, 2));
  try {
    const response = await request({
      url: '/questionBank/addQuestion',
      method: 'post',
      data
    }) as ApiResponse<any>;
    if (response && response.code === 200) {
      return response;
    }
    throw new Error(response?.message || '创建失败');
  } catch (error) {
    console.error('创建失败:', error);
    throw error;
  }
};



export const ModQuestion = async (data: any) => {
  console.log('提交的数据:', JSON.stringify(data, null, 2));
  try {
    const response = await request({
      url: '/questionBank/modQuestion',
      method: 'post',
      data
    }) as ApiResponse<any>;
    if (response && response.code === 200) {
      return response;
    }
    throw new Error(response?.message || '修改失败');
  } catch (error) {
    console.error('修改失败:', error);
    throw error;
  }
};





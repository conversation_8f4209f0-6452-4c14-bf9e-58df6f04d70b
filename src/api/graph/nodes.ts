import request from '@/utils/request';

// 节点类型定义
export interface GraphNode {
  id: string;
  name: string;
  description?: string;
  nodeType: 'center' | 'indicator' | 'secondaryIndicator' | 'thirdIndicator' | 'fourthIndicator';
  parentId?: string;
  createTime?: string;
  updateTime?: string;
  creator?: string;
  updater?: string;
}

// 连接线类型定义
export interface GraphLink {
  id: string;
  source: string;
  target: string;
  description?: string;
}

// 知识图谱数据类型
export interface KnowledgeGraphData {
  nodes: GraphNode[];
  links: GraphLink[];
}

// API响应类型
export interface ApiResponse<T = any> {
  code: number;
  message: string;
  data: T;
}

/**
 * 获取知识图谱数据
 */
export const getKnowledgeGraph = async (): Promise<KnowledgeGraphData> => {
  try {
    const response = await request<ApiResponse<KnowledgeGraphData>>({
      url: '/academicStyle-graph/data',
      method: 'GET'
    });
    console.log('获取知识图谱数据:', response);
    if (response.code === 200) {
      return response.data;
    }
    throw new Error(response?.message || '获取知识图谱数据失败');
  } catch (error) {
    console.error('获取知识图谱数据失败:', error);
    throw error;
  }
};

/**
 * 添加节点
 */
export const addNode = async (data: Omit<GraphNode, 'id'>) => {
  try {
    const response = await request<ApiResponse>({
      url: '/academicStyle-graph/addNode',
      method: 'POST',
      data
    });
    console.log('添加节点响应:', response);
    if (response.code === 200) {
      return response.data;
    }
    throw new Error(response?.message || '添加节点失败');
  } catch (error) {
    console.error('添加节点失败:', error);
    throw error;
  }
};

/**
 * 更新节点
 */
export const updateNode = async (id: string, data: Partial<GraphNode>) => {
  try {
    const response = await request<ApiResponse>({
      url: `/academicStyle-graph/updateNode/${id}`,
      method: 'PUT',
      data
    });
    console.log('更新节点响应:', response);
    if (response.code === 200) {
      return response.data;
    }
    throw new Error(response?.message || '更新节点失败');
  } catch (error) {
    console.error('更新节点失败:', error);
    throw error;
  }
};

/**
 * 删除节点
 */
export const deleteNode = async (id: string) => {
  try {
    const response = await request<ApiResponse<void>>({
      url: `/academicStyle-graph/delNode/${id}`,
      method: 'DELETE'
    });
    console.log('删除节点响应:', response);
    if (response.code === 200) {
      console.log("删除节点成功");
      return response.data;
    }
    throw new Error(response?.message || '删除节点失败');
  } catch (error) {
    console.error('删除节点失败:', error);
    throw error;
  }
};

/**
 * 添加连接线
 */
export const addLink = async (data: Omit<GraphLink, 'id'>) => {
  try {
    const response = await request<ApiResponse<GraphLink>>({
      url: '/academicStyle-graph/addLink',
      method: 'POST',
      data
    });
    console.log('添加连接线响应:', response);
    if (response.code === 200) {
      return response.data;
    }
    throw new Error(response?.message || '添加连接失败');
  } catch (error) {
    console.error('添加连接失败:', error);
    throw error;
  }
};

/**
 * 更新连接线
 */
export const updateLink = async (id: string, data: Partial<GraphLink>) => {
  try {
    const response = await request<ApiResponse<GraphLink>>({
      url: `/academicStyle-graph/updateLink/${id}`,
      method: 'PUT',
      data
    });
    console.log('更新连接线响应:', response);
    if (response.code === 200) {
      return response.data;
    }
    throw new Error(response?.message || '更新连接失败');
  } catch (error) {
    console.error('更新连接失败:', error);
    throw error;
  }
};

/**
 * 删除连接线
 */
export const deleteLink = async (id: string) => {
  try {
    const response = await request<ApiResponse<void>>({
      url: `/academicStyle-graph/delLink/${id}`,
      method: 'DELETE'
    });
    console.log('删除连接线响应:', response);
    if (response.code === 200) {
      return response.data;
    }
    throw new Error(response?.message || '删除连接失败');
  } catch (error) {
    console.error('删除连接失败:', error);
    throw error;
  }
};

/**
 * 根据节点类型获取节点
 */
export const getNodesByType = async (nodeType: string) => {
  try {
    const response = await request<ApiResponse<GraphNode[]>>({
      url: `/academicStyle-graph/nodes/type/${nodeType}`,
      method: 'GET'
    });
    console.log('根据类型获取节点响应:', response);
    if (response.code === 200) {
      return response.data;
    }
    throw new Error(response?.message || '获取节点失败');
  } catch (error) {
    console.error('根据类型获取节点失败:', error);
    throw error;
  }
};

/**
 * 根据父节点ID获取子节点
 */
export const getNodesByParentId = async (parentId: string) => {
  try {
    const response = await request<ApiResponse<GraphNode[]>>({
      url: `/academicStyle-graph/nodes/parent/${parentId}`,
      method: 'GET'
    });
    console.log('根据父节点获取子节点响应:', response);
    if (response.code === 200) {
      return response.data;
    }
    throw new Error(response?.message || '获取子节点失败');
  } catch (error) {
    console.error('根据父节点获取子节点失败:', error);
    throw error;
  }
};

/**
 * 清理无效连接线
 */
export const cleanupInvalidLinks = async () => {
  try {
    const response = await request<ApiResponse<any>>({
      url: '/academicStyle-graph/cleanupInvalidLinks',
      method: 'POST'
    });
    console.log('清理无效连接线响应:', response);
    if (response.code === 200) {
      return response.data;
    }
    throw new Error(response?.message || '清理失败');
  } catch (error) {
    console.error('清理无效连接线失败:', error);
    throw error;
  }
};

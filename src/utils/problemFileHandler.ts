import * as XLSX from 'xlsx';
import { MessagePlugin } from 'tdesign-vue-next';
import { ImportedProblemFile, Problem, Indicator, StudentScore } from '@/store/modules/indicator';
import { useIndicatorStore } from '@/store/modules/indicator';
import { readExcelFile, processScoreData } from '@/utils/excelHandler';

// 验证文件权重
export const validateFileWeights = (indicator: Indicator, refreshCallback?: () => void): boolean => {
  if (!indicator.importedProblemFiles || indicator.importedProblemFiles.length === 0) return true;
  
  const totalWeight = indicator.importedProblemFiles.reduce((sum, file) => sum + file.weight, 0);
  if (totalWeight > indicator.weight) {
    MessagePlugin.warning(`题目文件总权重(${totalWeight}%)超过指标点权重(${indicator.weight}%)，请调整`);
    return false;
  }
  
  if (refreshCallback) {
    refreshCallback();
  }
  
  return true;
};

// 删除题目文件
export const deleteFile = (file: ImportedProblemFile, indicator: Indicator, refreshCallback?: () => void): void => {
  if (!indicator.importedProblemFiles) return;
  
  // 确认删除
  const index = indicator.importedProblemFiles.findIndex(f => f.id === file.id);
  if (index !== -1) {
    indicator.importedProblemFiles.splice(index, 1);
    // 更新存储
    const indicatorStore = useIndicatorStore();
    indicatorStore.updateCategories([...indicatorStore.categories]);
    
    if (refreshCallback) {
      refreshCallback();
    }
    
    MessagePlugin.success('题目文件已删除');
  }
};

// 导出题目模板
export const exportProblemTemplate = (): boolean => {
  // 创建工作簿
  const workbook = XLSX.utils.book_new();
  
  // 动态设置课程目标数量（默认为8个，可以根据需要增减）
  const goalCount = 5;
  
  // 创建表头数据 - 两行表头
  const headerRow1 = ['试题编号', '试题类型', '题干', '答案', '课程目标'];
  const headerRow2 = ['', '', '', ''];
  
  // 动态添加课程目标列
  for (let i = 1; i <= goalCount; i++) {
    headerRow2.push(`课程目标${i}`);
  }
  
  // 创建工作表并添加表头
  const worksheet = XLSX.utils.aoa_to_sheet([headerRow1, headerRow2]);
  
  // 添加示例数据行，从第3行开始
  const sampleRows = [];
  
  // 示例1
  const row1 = ['1-1', '选择题', '以下关于Vue的描述正确的是', 'A'];
  // 添加课程目标示例数据（转换为字符串）
  for (let i = 0; i < goalCount; i++) {
    row1.push(i === 0 ? '5' : '0');
  }
  
  // 示例2
  const row2 = ['2-1', '填空题', 'Vue的核心库是___', '渐进式框架'];
  // 添加课程目标示例数据（转换为字符串）
  for (let i = 0; i < goalCount; i++) {
    row2.push(i === 1 ? '4' : '0');
  }
  
  // 示例3
  const row3 = ['2-2', '填空题', 'Vue的核心库是___', '渐进式框架'];
  // 添加课程目标示例数据（转换为字符串）
  for (let i = 0; i < goalCount; i++) {
    row3.push(i === 2 ? '4' : '0');
  }
  
  // 示例4
  const row4 = ['3-3', '填空题', 'Vue的核心库是___', '渐进式框架'];
  // 添加课程目标示例数据（转换为字符串）
  for (let i = 0; i < goalCount; i++) {
    row4.push(i === 3 ? '8' : '0');
  }
  
  sampleRows.push(row1, row2, row3, row4);
  
  // 添加样例数据
  XLSX.utils.sheet_add_aoa(worksheet, sampleRows, { origin: 2 }); // 从第3行开始添加数据
  
  // 设置列宽
  const columns = [
    { width: 10 }, // 试题编号
    { width: 15 }, // 试题类型
    { width: 30 }, // 题干
    { width: 20 }, // 答案
  ];
  
  // 添加课程目标列宽
  for (let i = 0; i < goalCount; i++) {
    columns.push({ width: 12 }); // 课程目标列
  }
  
  worksheet['!cols'] = columns;
  
  // 合并单元格 - 第一行的课程目标
  worksheet['!merges'] = [
    { s: { r: 0, c: 4 }, e: { r: 0, c: 4 + goalCount - 1 } } // 合并第1行的课程目标列
  ];
  
  // 将工作表添加到工作簿
  XLSX.utils.book_append_sheet(workbook, worksheet, '题目模板');
  
  // 生成二进制数据并下载
  try {
    const wbout = XLSX.write(workbook, {
      bookType: 'xlsx',
      type: 'array'
    });
    
    // 创建Blob并下载
    const blob = new Blob([wbout], { type: 'application/octet-stream' });
    const url = URL.createObjectURL(blob);
    
    const link = document.createElement('a');
    link.href = url;
    link.download = '题目导入模板.xlsx';
    document.body.appendChild(link);
    link.click();
    
    // 清理资源
    setTimeout(() => {
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
    }, 100);
    
    MessagePlugin.success('题目导入模板下载成功');
    return true;
  } catch (error: any) {
    console.error('导出模板失败:', error);
    MessagePlugin.error('导出模板失败');
    return false;
  }
};

// 导入题目
export const importProblems = async (
  file: any, 
  indicator: Indicator, 
  callback: { 
    loading: (status: boolean) => void,
    refresh: () => void,
    updateStore: (categories: any[]) => void,
    showPreview?: (file: ImportedProblemFile) => void
  }
): Promise<boolean> => {
  try {
    callback.loading(true);
    console.log('开始处理Excel文件');
    const { data, fileName } = await readExcelFile(file);
    console.log('Excel文件读取完成, 数据行数:', data.length);
    
    // 验证基本数据格式
    if (!data || data.length === 0) {
      MessagePlugin.error('导入文件为空或格式不正确');
      return false;
    }
    
    // 验证必要的字段
    const firstItem = data[0];
    if (!firstItem || 
        !firstItem['试题编号'] || 
        !firstItem['试题类型'] || 
        !firstItem['题干'] || 
        !firstItem['答案']) {
      MessagePlugin.error('导入文件格式不正确，请确保包含：试题编号、试题类型、题干、答案等列');
      return false;
    }
    
    // 收集所有goalN字段，检查是否有课程目标
    const goalFields = Object.keys(firstItem).filter(key => key.startsWith('goal'));
    console.log('导入题目: 发现的课程目标字段:', goalFields);
    
    // 处理题目数据
    const problems: Problem[] = data.map((item: any, index) => {
      // 创建基本题目对象
      const problem: Problem = {
        number: item['试题编号'],
        type: item['试题类型'],
        title: item['题干'],
        answer: item['答案'],
      };
      
      // 动态处理所有课程目标列
      Object.keys(item).forEach(key => {
        if (key.startsWith('goal')) {
          problem[key] = Number(item[key]) || 0;
          if (index === 0) { // 只记录第一个题目的课程目标赋值
            console.log(`导入题目: 设置课程目标 ${key} = ${problem[key]}`);
          }
        } else if (key.startsWith('课程目标')) {
          // 如果直接是"课程目标N"格式，转换为goalN
          const match = key.match(/课程目标(\d+)/);
          if (match && match[1]) {
            const goalNumber = match[1];
            const goalKey = `goal${goalNumber}`;
            problem[goalKey] = Number(item[key]) || 0;
            if (index === 0) {
              console.log(`导入题目: 转换课程目标 ${key} -> ${goalKey} = ${problem[goalKey]}`);
            }
          }
        }
      });
      
      return problem;
    });
    
    // 检查是否所有的题目都有课程目标
    const hasGoals = problems.some(problem => {
      return Object.keys(problem).some(key => key.startsWith('goal'));
    });
    
    if (!hasGoals) {
      console.warn('导入题目: 警告 - 未检测到任何课程目标数据');
    }
    
    console.log(`处理完成，共有${problems.length}道题目`);
    if (problems.length > 0) {
      console.log('第一个题目示例:', JSON.stringify(problems[0], null, 2));
    }
    
    // 确保指标点有importedProblemFiles数组
    if (!indicator.importedProblemFiles) {
      console.log('指标点没有importedProblemFiles数组，正在创建');
      indicator.importedProblemFiles = [];
    }
    
    // 创建导入文件记录
    const importedFile: ImportedProblemFile = {
      id: `prob-${Date.now()}`,
      fileName: fileName,
      importDate: new Date().toLocaleString(),
      problems: problems,
      weight: Math.min(indicator.weight / 2, 25), // 默认权重
      scores: []
    };
    
    console.log('创建的导入文件对象:', JSON.stringify({
      id: importedFile.id,
      fileName: importedFile.fileName,
      problemCount: importedFile.problems.length
    }, null, 2));
    
    // 添加到指标点的文件列表
    indicator.importedProblemFiles.push(importedFile);
    console.log(`添加到指标点后，文件列表长度: ${indicator.importedProblemFiles.length}`);
    
    // 获取存储实例
    const indicatorStore = useIndicatorStore();
    
    // 查找当前指标点所在的分类
    const categories = indicatorStore.categories;
    let updated = false;
    
    for (let i = 0; i < categories.length; i++) {
      const category = categories[i];
      for (let j = 0; j < category.indicators.length; j++) {
        if (category.indicators[j].id === indicator.id) {
          console.log(`在分类 ${category.name} 中找到指标点 ${indicator.name}`);
          // 确保更新完整的指标点对象
          category.indicators[j] = { ...category.indicators[j], importedProblemFiles: indicator.importedProblemFiles };
          updated = true;
          break;
        }
      }
      if (updated) break;
    }
    
    if (!updated) {
      console.warn('未在存储中找到指标点，使用传入的指标点对象');
    }
    
    // 更新存储
    console.log('更新存储');
    indicatorStore.updateCategories([...categories]);
    
    // 运行回调
    callback.refresh();
    
    MessagePlugin.success(`成功导入 ${problems.length} 道题目`);
    
    // 打开预览对话框（如果提供了预览回调）
    if (callback.showPreview) {
      callback.showPreview(importedFile);
    }
    
    return true;
  } catch (error: any) {
    console.error(`导入题目失败: ${error.message}`, error);
    MessagePlugin.error(`导入题目失败: ${error.message}`);
    return false;
  } finally {
    callback.loading(false);
  }
};

// 为特定题目文件导入成绩
export const importScoreForProblemFile = async (
  file: any, 
  problemFile: ImportedProblemFile, 
  indicator: Indicator,
  callback: {
    loading: (status: boolean) => void,
    refresh: () => void,
    updateStore: (categories: any[]) => void,
    updateStudentsData: () => void
  }
): Promise<boolean> => {
  try {
    callback.loading(true);
    const { data, fileName } = await readExcelFile(file);
    const processedData = processScoreData(data, fileName);

    if (processedData) {
      problemFile.scores = processedData as StudentScore[];
      
      // 更新存储
      const indicatorStore = useIndicatorStore();
      indicatorStore.updateCategories([...indicatorStore.categories]);
      
      // 更新总成绩数据
      callback.updateStudentsData();
      callback.refresh();
      
      MessagePlugin.success(`成功导入 ${processedData.length} 名学生的成绩数据到题目文件 ${problemFile.fileName}`);
      return true;
    }
    return false;
  } catch (error: any) {
    MessagePlugin.error(`导入失败: ${error.message}`);
    return false;
  } finally {
    callback.loading(false);
  }
};
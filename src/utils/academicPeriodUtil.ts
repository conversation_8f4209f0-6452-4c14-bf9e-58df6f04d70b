// 学期学年转换工具

/**
 * 根据当前日期获取当前学年字符串（如 "2024-2025"）
 */
export function getCurrentAcademicYear(): string {
  const currentYear = new Date().getFullYear();
  const currentMonth = new Date().getMonth() + 1;
  if (currentMonth >= 8) {
    return `${currentYear}-${currentYear + 1}`;
  } else {
    return `${currentYear - 1}-${currentYear}`;
  }
}

/**
 * 获取学年选项（前一年、当前、下一年）
 */
export function getAcademicYearOptions(): string[] {
  const currentAcademicYear = getCurrentAcademicYear();
  const parts = currentAcademicYear.split('-').map(Number);
  const prevYear = `${parts[0] - 1}-${parts[1] - 1}`;
  const nextYear = `${parts[0] + 1}-${parts[1] + 1}`;
  return [prevYear, currentAcademicYear, nextYear];
}

/**
 * 格式化学年和学期，确保符合API要求
 * @param academicYear 学年字符串（如 "2024-2025"）
 * @param semester 学期字符串（"1" 春季，"2" 秋季）
 * @returns { year: number, term: number }
 */
export function formatAcademicPeriod(academicYear: string, semester: string): { year: number, term: number } {
  try {
    const years = academicYear.split('-').map(Number);
    let startYear: number;
    if (semester === '1') {
      startYear = years[1]; // 春季学期对应后一年
    } else if (semester === '2') {
      startYear = years[0]; // 秋季学期对应前一年
    } else {
      throw new Error('无效的学期');
    }
    const term = parseInt(semester);
    if (isNaN(startYear) || isNaN(term) || term < 1 || term > 2) {
      throw new Error('无效的学年或学期格式');
    }
    return { year: startYear, term };
  } catch (error) {
    return { year: new Date().getFullYear(), term: 1 };
  }
}


// 学期选项
const semesters = [
  { value: '1', label: '春季学期' },
  { value: '2', label: '秋季学期' }
];

/**
 * 获取根据数字获取学期
 */
export function getSemesterByValue(value: string): string {
  const semester = semesters.find(s => s.value === value);
  return semester ? semester.label : '未知学期';
}
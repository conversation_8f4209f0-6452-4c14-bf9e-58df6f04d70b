import { getDictDataList, getDictTypeList, getAllDictData } from '@/api/system/dict';

// 字典数据类型定义
export interface DictData {
  id: number;
  label: string;
  value: string | number;
  typeId: number;
  sort: number;
  status: number;
  remark?: string;
  createTime?: string;
  updateTime?: string;
}

// 字典类型定义
export interface DictType {
  id: number;
  title: string;
  status: number;
  remark?: string;
}

// 优化后的缓存接口 - 使用Map结构提高性能
interface DictCache {
  data: Map<number, DictData[]>; // 按typeId分组存储
  typeTitleToId: Map<string, number>; // 类型标题到ID的映射
  timestamp: number;
  loading?: boolean;
}

// 全局字典状态
interface GlobalDictState {
  initialized: boolean;
  initializing: boolean;
  error: string | null;
  lastInitTime: number;
  dictTypes: DictType[];
  totalDictCount: number; // 总字典数据条数
  initDuration: number; // 初始化耗时（毫秒）
}

// 字典工具类
class DictUtil {
  private cache: DictCache = {
    data: new Map<number, DictData[]>(),
    typeTitleToId: new Map<string, number>(),
    timestamp: 0,
    loading: false
  };
  private cacheExpireTime = 5 * 60 * 1000; // 5分钟缓存过期时间
  private globalState: GlobalDictState = {
    initialized: false,
    initializing: false,
    error: null,
    lastInitTime: 0,
    dictTypes: [],
    totalDictCount: 0,
    initDuration: 0
  };

  /**
   * 初始化全局字典数据（优化版 - 批量获取）
   * @param forceRefresh 是否强制刷新
   * @returns Promise<boolean>
   */
  async initializeGlobalDict(forceRefresh = false): Promise<boolean> {
    const startTime = Date.now();

    try {
      // 1. 检查是否正在初始化，避免重复请求
      if (this.globalState.initializing && !forceRefresh) {
        const checkInitializing = () => {
          if (this.globalState.initializing) {
            return new Promise<boolean>((resolve) => {
              setTimeout(() => resolve(checkInitializing()), 100);
            });
          }
          return Promise.resolve(this.globalState.initialized);
        };
        return await checkInitializing();
      }

      // 2. 检查缓存是否有效
      if (this.globalState.initialized && !forceRefresh && this.isCacheValid()) {
      //  console.log('字典数据已初始化，使用缓存数据');
        return true;
      }

      // 3. 开始初始化
      this.globalState.initializing = true;
      this.globalState.error = null;
     // console.log('开始初始化全局字典数据...');

      // 4. 获取字典类型列表
      console.log('正在获取字典类型列表...');
      const typeResponse = await getDictTypeList();
     // console.log('字典类型响应:', typeResponse);

      if (typeResponse.code !== 200) {
        throw new Error(`获取字典类型失败: ${typeResponse.message}`);
      }

      const dictTypes = typeResponse.data || [];
      this.globalState.dictTypes = dictTypes;
     // console.log(`获取到 ${dictTypes.length} 个字典类型:`, dictTypes);

      // 5. 批量获取所有字典数据（优化：从N次请求优化为1次请求）
     // console.log('正在获取所有字典数据...');
      const dataResponse = await getAllDictData();
     // console.log('字典数据响应:', dataResponse);

      if (dataResponse.code !== 200) {
        throw new Error(`获取字典数据失败: ${dataResponse.message}`);
      }

      const allDictData = dataResponse.data || [];
     // console.log(`获取到 ${allDictData.length} 条字典数据:`, allDictData);

      // 6. 按类型ID分组存储数据
      this.cache.data.clear();
      this.cache.typeTitleToId.clear();

      // 建立类型标题到ID的映射
      dictTypes.forEach((type: DictType) => {
      //  console.log('字典类型:', type);
        this.cache.typeTitleToId.set(type.title, type.id);
      //  console.log(`映射字典类型: ${type.title} -> ${type.id}`);
      });

      // 按类型ID分组字典数据，只保留status===0的启用数据
      allDictData.forEach((item: DictData) => {
        // 只处理status===0的启用数据
        if (item.status === 0) {
          if (!this.cache.data.has(item.typeId)) {
            this.cache.data.set(item.typeId, []);
          }
          this.cache.data.get(item.typeId)!.push(item);
        } else {
      //    console.log(`跳过禁用状态的字典数据: ${item.label} (status: ${item.status})`);
        }
      });

      // 7. 更新缓存时间戳和全局状态
      this.cache.timestamp = Date.now();
      this.globalState.initialized = true;
      this.globalState.initializing = false;
      this.globalState.lastInitTime = Date.now();
      this.globalState.totalDictCount = allDictData.length;
      this.globalState.initDuration = Date.now() - startTime;

    //  console.log(`字典数据初始化完成，耗时: ${this.globalState.initDuration}ms`);
    //  console.log(`缓存了 ${this.cache.data.size} 个类型的字典数据，共 ${allDictData.length} 条记录`);

      // 打印每个类型的字典数据统计
      this.cache.data.forEach((data, typeId) => {
     //   console.log(`类型ID ${typeId}: ${data.length} 条数据`);
      });

      return true;
    } catch (error) {
      this.globalState.initializing = false;
      this.globalState.error = error instanceof Error ? error.message : '未知错误';
      console.error('字典数据初始化失败:', error);
      return false;
    }
  }

  /**
   * 获取指定类型的字典数据
   * @param typeId 字典类型ID
   * @param forceRefresh 是否强制刷新
   * @returns Promise<DictData[]>
   */
  async getDictData(typeId: number, forceRefresh = false): Promise<DictData[]> {
    try {
    //  console.log(`getDictData - 获取类型ID: ${typeId}, forceRefresh: ${forceRefresh}`);

      // 1. 检查全局初始化状态
      if (!this.globalState.initialized) {
    //    console.log('字典数据未初始化，开始初始化...');
        await this.initializeGlobalDict();
      }

      // 2. 检查是否正在加载
      if (this.cache.loading && !forceRefresh) {
     //   console.log('字典数据正在加载中，等待...');
        const checkLoading = () => {
          if (this.cache.loading) {
            return new Promise<DictData[]>((resolve) => {
              setTimeout(() => resolve(checkLoading()), 100);
            });
          }
          return Promise.resolve(this.cache.data.get(typeId) || []);
        };
        return await checkLoading();
      }

      // 3. 检查缓存是否有效
      if (!forceRefresh && this.isCacheValid() && this.cache.data.has(typeId)) {
        const cachedData = this.cache.data.get(typeId) || [];
    //    console.log(`从缓存获取数据，类型ID: ${typeId}, 数据条数: ${cachedData.length}`);
        return cachedData;
      }

      // 4. 如果缓存无效或强制刷新，重新初始化
      if (forceRefresh || !this.isCacheValid()) {
     //   console.log('缓存无效或强制刷新，重新初始化...');
        await this.initializeGlobalDict(true);
      }

      const result = this.cache.data.get(typeId) || [];
   //   console.log(`最终获取数据，类型ID: ${typeId}, 数据条数: ${result.length}`);
      return result;
    } catch (error) {
      console.error(`获取字典数据失败 (typeId: ${typeId}):`, error);
      return [];
    }
  }

  /**
   * 根据值获取字典标签
   * @param typeId 字典类型ID
   * @param value 字典值
   * @returns Promise<string>
   */
  async getDictLabel(typeId: number, value: string | number): Promise<string> {
    try {
      const dictData = await this.getDictData(typeId);
      const item = dictData.find(d => d.value === value);
      return item ? item.label : '';
    } catch (error) {
      console.error(`获取字典标签失败 (typeId: ${typeId}, value: ${value}):`, error);
      return '';
    }
  }

  /**
   * 根据标签获取字典值
   * @param typeId 字典类型ID
   * @param label 字典标签
   * @returns Promise<string | number | undefined>
   */
  async getDictValue(typeId: number, label: string): Promise<string | number | undefined> {
    try {
      const dictData = await this.getDictData(typeId);
      const item = dictData.find(d => d.label === label);
      return item ? item.value : undefined;
    } catch (error) {
      console.error(`获取字典值失败 (typeId: ${typeId}, label: ${label}):`, error);
      return undefined;
    }
  }

  /**
   * 获取字典选项列表（用于下拉框等）
   * @param typeId 字典类型ID
   * @returns Promise<{label: string, value: string | number}[]>
   */
  async getDictOptions(typeId: number): Promise<{label: string, value: string | number}[]> {
    try {
      const dictData = await this.getDictData(typeId);
      return dictData.map(item => ({
        label: item.label,
        value: item.value
      }));
    } catch (error) {
      console.error(`获取字典选项失败 (typeId: ${typeId}):`, error);
      return [];
    }
  }

  /**
   * 批量获取多个类型的字典数据
   * @param typeIds 字典类型ID数组
   * @returns Promise<{[key: number]: DictData[]}>
   */
  async getMultipleDictData(typeIds: number[]): Promise<{[key: number]: DictData[]}> {
    try {
      const result: {[key: number]: DictData[]} = {};

      // 确保全局数据已初始化
      if (!this.globalState.initialized) {
        await this.initializeGlobalDict();
      }

      // 从缓存中批量获取
      for (const typeId of typeIds) {
        result[typeId] = this.cache.data.get(typeId) || [];
      }

      return result;
    } catch (error) {
      console.error('批量获取字典数据失败:', error);
      return {};
    }
  }

    /**
   * 根据类型标题获取字典数据
   * @param typeTitle 字典类型标题
   * @returns Promise<DictData[]>
   */
  async getDictDataByTypeTitle(typeTitle: string): Promise<DictData[]> {
    try {
      console.log(`getDictDataByTypeTitle - 查找类型: ${typeTitle}`);
      
      // 确保全局数据已初始化
      if (!this.globalState.initialized) {
      //  console.log('字典数据未初始化，开始初始化...');
        await this.initializeGlobalDict();
      }
      
     // console.log('当前类型标题到ID映射:', this.cache.typeTitleToId);
      
      // 从映射中获取类型ID
      const typeId = this.cache.typeTitleToId.get(typeTitle);
      if (!typeId) {
        console.warn(`未找到字典类型: ${typeTitle}`);
     //   console.log('可用的类型标题:', Array.from(this.cache.typeTitleToId.keys()));
        return [];
      }

    //  console.log(`找到类型ID: ${typeId} 对应类型标题: ${typeTitle}`);
      
      const result = await this.getDictData(typeId);
    //  console.log(`获取到 ${result.length} 条数据:`, result);
      
      return result;
    } catch (error) {
      console.error(`根据类型标题获取字典数据失败 (typeTitle: ${typeTitle}):`, error);
      return [];
    }
  }

  /**
   * 根据类型标题获取字典选项
   * @param typeTitle 字典类型标题
   * @returns Promise<{label: string, value: string | number}[]>
   */
  async getDictOptionsByTypeTitle(typeTitle: string): Promise<{label: string, value: string | number}[]> {
    try {
      const dictData = await this.getDictDataByTypeTitle(typeTitle);
      return dictData.map(item => ({
        label: item.label,
        value: item.value
      }));
    } catch (error) {
      console.error(`根据类型标题获取字典选项失败 (typeTitle: ${typeTitle}):`, error);
      return [];
    }
  }

  /**
   * 根据类型标题和值获取字典标签
   * @param typeTitle 字典类型标题
   * @param value 字典值
   * @returns Promise<string>
   */
  async getDictLabelByTypeTitle(typeTitle: string, value: string | number): Promise<string> {
    try {
      // 确保全局数据已初始化
      if (!this.globalState.initialized) {
     //   console.log('字典数据未初始化，开始初始化...');
        await this.initializeGlobalDict();
      }
      
      const typeId = this.cache.typeTitleToId.get(typeTitle);
      if (!typeId) {
        console.warn(`未找到字典类型: ${typeTitle}`);
        return '';
      }

      return await this.getDictLabel(typeId, value);
    } catch (error) {
   //   console.error(`根据类型标题获取字典标签失败 (typeTitle: ${typeTitle}, value: ${value}):`, error);
      return '';
    }
  }

  /**
   * 获取所有字典类型
   * @returns DictType[]
   */
  getDictTypes(): DictType[] {
    return this.globalState.dictTypes;
  }

  /**
   * 获取全局状态
   * @returns GlobalDictState
   */
  getGlobalState(): GlobalDictState {
    return { ...this.globalState };
  }

  /**
   * 清除缓存
   * @param typeId 可选的类型ID，如果不提供则清除所有缓存
   */
  clearCache(typeId?: number): void {
    if (typeId) {
      this.cache.data.delete(typeId);
      console.log(`已清除字典类型 ${typeId} 的缓存`);
    } else {
      this.cache.data.clear();
      this.cache.typeTitleToId.clear();
      this.cache.timestamp = 0;
      this.globalState.initialized = false;
      console.log('已清除所有字典缓存');
    }
  }

  /**
   * 重置全局状态
   */
  resetGlobalState(): void {
    this.globalState = {
      initialized: false,
      initializing: false,
      error: null,
      lastInitTime: 0,
      dictTypes: [],
      totalDictCount: 0,
      initDuration: 0
    };
    this.clearCache();
    console.log('已重置字典全局状态');
  }

  /**
   * 检查缓存是否有效
   * @param typeId 可选的类型ID
   * @returns boolean
   */
  private isCacheValid(typeId?: number): boolean {
    const now = Date.now();
    if (typeId) {
      return this.cache.data.has(typeId) &&
             (now - this.cache.timestamp) < this.cacheExpireTime;
    }
    return this.cache.timestamp > 0 &&
           (now - this.cache.timestamp) < this.cacheExpireTime;
  }

  /**
   * 设置缓存过期时间
   * @param expireTime 过期时间（毫秒）
   */
  setCacheExpireTime(expireTime: number): void {
    this.cacheExpireTime = expireTime;
    console.log(`字典缓存过期时间已设置为 ${expireTime}ms`);
  }

  /**
   * 获取缓存信息
   * @returns 缓存统计信息
   */
  getCacheInfo(): {cacheCount: number, cacheKeys: number[], globalState: GlobalDictState} {
    return {
      cacheCount: this.cache.data.size,
      cacheKeys: Array.from(this.cache.data.keys()),
      globalState: this.getGlobalState()
    };
  }
}

// 创建单例实例
const dictUtil = new DictUtil();

// 导出便捷方法
export const getDictData = (typeId: number, forceRefresh = false) => dictUtil.getDictData(typeId, forceRefresh);
export const getDictLabel = (typeId: number, value: string | number) => dictUtil.getDictLabel(typeId, value);
export const getDictValue = (typeId: number, label: string) => dictUtil.getDictValue(typeId, label);
export const getDictOptions = (typeId: number) => dictUtil.getDictOptions(typeId);
export const getMultipleDictData = (typeIds: number[]) => dictUtil.getMultipleDictData(typeIds);
export const clearDictCache = (typeId?: number) => dictUtil.clearCache(typeId);
export const setDictCacheExpireTime = (expireTime: number) => dictUtil.setCacheExpireTime(expireTime);
export const getDictCacheInfo = () => dictUtil.getCacheInfo();

// 全局初始化相关
export const initializeGlobalDict = (forceRefresh = false) => dictUtil.initializeGlobalDict(forceRefresh);
export const getDictDataByTypeTitle = (typeTitle: string) => dictUtil.getDictDataByTypeTitle(typeTitle);
export const getDictOptionsByTypeTitle = (typeTitle: string) => dictUtil.getDictOptionsByTypeTitle(typeTitle);
export const getDictLabelByTypeTitle = (typeTitle: string, value: string | number) => dictUtil.getDictLabelByTypeTitle(typeTitle, value);
export const getDictTypes = () => dictUtil.getDictTypes();
export const getGlobalDictState = () => dictUtil.getGlobalState();
export const resetGlobalDictState = () => dictUtil.resetGlobalState();

// 导出类型定义（避免冲突）
export type { GlobalDictState };

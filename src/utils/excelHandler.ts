import * as XLSX from 'xlsx';
import { MessagePlugin } from 'tdesign-vue-next';
import { StudentScore } from '@/store/modules/indicator';

/**
 * 读取Excel文件并转换为JSON数据
 * @param file 上传的文件对象
 * @returns Promise包含解析后的数据和文件名
 */
export const readExcelFile = async (file: File): Promise<{ data: any[], fileName: string }> => {
  return new Promise((resolve, reject) => {
    try {
      const fileName = file.name;
      const reader = new FileReader();
      
      reader.onload = (e) => {
        try {
          const data = new Uint8Array(e.target?.result as ArrayBuffer);
          const workbook = XLSX.read(data, { type: 'array' });
          
          // 获取第一个工作表
          const firstSheetName = workbook.SheetNames[0];
          const worksheet = workbook.Sheets[firstSheetName];
          
          // 转换为JSON
          const jsonData = XLSX.utils.sheet_to_json(worksheet, {
            defval: '', // 空白单元格设为空字符串
          });
          
          console.log(`成功解析文件 ${fileName}，数据行数: ${jsonData.length}`);
          resolve({ data: jsonData, fileName });
        } catch (error: any) {
          console.error('Excel文件解析失败:', error);
          reject(new Error(`Excel文件解析失败: ${error.message}`));
        }
      };
      
      reader.onerror = () => {
        reject(new Error('文件读取失败'));
      };
      
      reader.readAsArrayBuffer(file);
    } catch (error: any) {
      reject(new Error(`文件处理失败: ${error.message}`));
    }
  });
};

/**
 * 处理成绩数据，将导入的原始数据转换为标准成绩格式
 * @param data 从Excel解析的原始数据
 * @param fileName 文件名，用于日志
 * @returns 处理后的学生成绩数据数组
 */
export const processScoreData = (data: any[], fileName: string): StudentScore[] | null => {
  try {
    if (!data || data.length === 0) {
      MessagePlugin.error('导入文件为空或格式不正确');
      return null;
    }
    
    // 检查必要字段
    const firstItem = data[0];
    if (!firstItem || !firstItem['学号'] || !(firstItem['成绩'] !== undefined || firstItem['分数'] !== undefined)) {
      MessagePlugin.error('导入文件格式不正确，请确保包含"学号"和"成绩/分数"列');
      return null;
    }
    
    // 处理成绩数据
    const scores: StudentScore[] = data.map(item => {
      // 尝试获取成绩，可能是"成绩"或"分数"列
      const scoreValue = 
        item['成绩'] !== undefined ? Number(item['成绩']) : 
        item['分数'] !== undefined ? Number(item['分数']) : 0;
      
      return {
        studentId: String(item['学号']),
        studentName: String(item['姓名'] || ''),
        score: isNaN(scoreValue) ? 0 : scoreValue,
        importDate: new Date().toISOString()
      };
    }).filter(score => score.studentId && !isNaN(score.score));
    
    if (scores.length === 0) {
      MessagePlugin.error('未能找到有效的成绩数据');
      return null;
    }
    
    console.log(`成功处理 ${fileName} 中的成绩数据，共 ${scores.length} 条记录`);
    return scores;
  } catch (error: any) {
    console.error('成绩数据处理失败:', error);
    MessagePlugin.error(`成绩数据处理失败: ${error.message}`);
    return null;
  }
};

/**
 * 读取文件为ArrayBuffer
 * @param file 文件对象
 * @returns Promise包含ArrayBuffer
 */
export const readFileAsArrayBuffer = (file: File): Promise<ArrayBuffer> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = (e) => resolve(e.target?.result as ArrayBuffer);
    reader.onerror = () => reject(new Error('文件读取失败'));
    reader.readAsArrayBuffer(file);
  });
};

/**
 * 解析成绩导入Excel文件
 * @param file 上传的Excel文件
 * @returns Promise包含解析后的成绩数据
 */
export const parseGradeImportFile = async (file: File): Promise<{
  success: boolean;
  data?: any[][];
  error?: string;
  headers?: string[];
}> => {
  try {
    const arrayBuffer = await readFileAsArrayBuffer(file);
    const data = new Uint8Array(arrayBuffer);
    const workbook = XLSX.read(data, { type: 'array' });
    
    // 获取第一个工作表
    const firstSheetName = workbook.SheetNames[0];
    const worksheet = workbook.Sheets[firstSheetName];
    
    // 转换为二维数组
    const jsonData = XLSX.utils.sheet_to_json(worksheet, {
      defval: '', // 空白单元格设为空字符串
      header: 1 // 返回二维数组格式
    }) as any[][];
    
    if (!jsonData || jsonData.length < 2) {
      return {
        success: false,
        error: '文件格式错误或数据为空'
      };
    }
    
    const headers = jsonData[0];
    const dataRows = jsonData.slice(1);
    
    // 验证表头格式
    if (!headers || headers.length < 3) {
      return {
        success: false,
        error: '表头格式错误，至少需要包含学号、姓名、班级列'
      };
    }
    
    // 检查必要的列是否存在
    const requiredColumns = ['学号', '姓名', '班级'];
    const missingColumns = requiredColumns.filter(col => !headers.includes(col));
    
    if (missingColumns.length > 0) {
      return {
        success: false,
        error: `缺少必要的列：${missingColumns.join(', ')}`
      };
    }
    
    return {
      success: true,
      data: dataRows,
      headers: headers
    };
  } catch (error: any) {
    console.error('Excel文件解析失败:', error);
    return {
      success: false,
      error: `文件解析失败: ${error.message}`
    };
  }
};

/**
 * 验证成绩导入数据
 * @param data 解析后的数据行
 * @param headers 表头信息
 * @param courseObjectives 课程目标信息
 * @param existingStudents 现有学生列表
 * @returns 验证结果
 */
export const validateGradeImportData = (
  data: any[][],
  headers: string[],
  courseObjectives: any[],
  existingStudents: any[]
): {
  success: boolean;
  validData: any[];
  errors: string[];
  warnings: string[];
} => {
  const validData: any[] = [];
  const errors: string[] = [];
  const warnings: string[] = [];
  
  // 创建学生映射表
  const studentMap = new Map();
  existingStudents.forEach(student => {
    studentMap.set(student.studentNumber, student);
  });
  
  // 获取课程目标列索引
  const courseTargetIndices: number[] = [];
  courseObjectives.forEach(objective => {
    const columnName = `课程目标${objective.id}`;
    const index = headers.indexOf(columnName);
    if (index !== -1) {
      courseTargetIndices.push(index);
    }
  });
  
  // 验证每一行数据
  data.forEach((row, rowIndex) => {
    const lineNumber = rowIndex + 2; // Excel行号（从2开始，因为第1行是表头）
    const rowErrors: string[] = [];
    const rowWarnings: string[] = [];
    
    // 检查学号
    const studentNumber = String(row[0] || '').trim();
    if (!studentNumber) {
      rowErrors.push('学号不能为空');
    } else if (!studentMap.has(studentNumber)) {
      rowErrors.push(`学号 ${studentNumber} 不存在于当前班级中`);
    }
    
    // 检查姓名
    const studentName = String(row[1] || '').trim();
    if (!studentName) {
      rowErrors.push('姓名不能为空');
    }
    
    // 检查班级
    const className = String(row[2] || '').trim();
    if (!className) {
      rowWarnings.push('班级信息为空');
    }
    
    // 验证课程目标成绩
    courseTargetIndices.forEach((scoreIndex, targetIndex) => {
      const scoreValue = row[scoreIndex];
      const objective = courseObjectives[targetIndex];
      
      if (scoreValue !== null && scoreValue !== undefined && scoreValue !== '') {
        const score = Number(scoreValue);
        if (isNaN(score)) {
          rowErrors.push(`课程目标${objective.id}成绩格式错误`);
        } else if (score < 0) {
          rowErrors.push(`课程目标${objective.id}成绩不能为负数`);
        } else if (score > 100) {
          rowErrors.push(`课程目标${objective.id}成绩不能超过100分`);
        }
      }
    });
    
    // 如果有错误，添加到错误列表
    if (rowErrors.length > 0) {
      errors.push(`第${lineNumber}行：${rowErrors.join('，')}`);
    }
    
    // 如果有警告，添加到警告列表
    if (rowWarnings.length > 0) {
      warnings.push(`第${lineNumber}行：${rowWarnings.join('，')}`);
    }
    
    // 如果只有警告没有错误，数据仍然有效
    if (rowErrors.length === 0) {
      validData.push({
        rowIndex,
        studentNumber,
        studentName,
        className,
        scores: courseTargetIndices.map((scoreIndex, targetIndex) => ({
          targetId: courseObjectives[targetIndex].id,
          score: row[scoreIndex] !== null && row[scoreIndex] !== undefined && row[scoreIndex] !== '' 
            ? Number(row[scoreIndex]) 
            : null
        }))
      });
    }
  });
  
  return {
    success: errors.length === 0,
    validData,
    errors,
    warnings
  };
};

/**
 * 将验证后的成绩数据转换为批量保存格式
 * @param validData 验证后的成绩数据
 * @param assessmentId 考核ID
 * @param taskId 任务ID
 * @returns 批量保存数据格式
 */
export const convertToBatchSaveFormat = (
  validData: any[],
  assessmentId: number,
  taskId: number
): {
  assessmentId: number;
  taskId: number;
  studentScores: Array<{
    studentId: number;
    courseTargetNo: number;
    objectiveId: string;
    score: number;
  }>;
} => {
  const studentScores: Array<{
    studentId: number;
    courseTargetNo: number;
    objectiveId: string;
    score: number;
  }> = [];
  
  validData.forEach(dataItem => {
    dataItem.scores.forEach((scoreItem: any) => {
      if (scoreItem.score !== null && scoreItem.score !== undefined) {
        studentScores.push({
          studentId: Number(dataItem.studentNumber), // 假设学号就是学生ID
          courseTargetNo: scoreItem.targetId,
          objectiveId: String(scoreItem.targetId), // 转换为字符串格式
          score: scoreItem.score
        });
      }
    });
  });
  
  return {
    assessmentId,
    taskId,
    studentScores
  };
}; 
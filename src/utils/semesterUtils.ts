/**
 * 学期信息接口
 */
export interface SemesterInfo {
  /** 学年 */
  year: number;
  /** 学期类型（1: 春季, 2: 秋季） */
  semester: number;
  /** 显示名称（如"2025春季学期"） */
  displayName: string;
  /** 短名称（如"2025春"） */
  shortName: string;
}

/**
 * 学期选项接口（用于下拉选择）
 */
export interface SemesterOption {
  label: string;
  value: string;
  year: number;
  semester: number;
}

/**
 * 学期工具类
 * 提供学期相关的计算和转换功能
 */
export class SemesterUtils {
  /**
   * 学期类型枚举
   */
  static readonly SEMESTER_TYPE = {
    SPRING: 1,  // 春季
    AUTUMN: 2   // 秋季
  } as const;

  /**
   * 学期类型名称映射
   */
  static readonly SEMESTER_TYPE_NAMES = {
    [SemesterUtils.SEMESTER_TYPE.SPRING]: '春季',
    [SemesterUtils.SEMESTER_TYPE.AUTUMN]: '秋季'
  } as const;

  /**
   * 学期类型短名称映射
   */
  static readonly SEMESTER_TYPE_SHORT_NAMES = {
    [SemesterUtils.SEMESTER_TYPE.SPRING]: '春',
    [SemesterUtils.SEMESTER_TYPE.AUTUMN]: '秋'
  } as const;

  /**
   * 获取当前学期信息
   * @returns 当前学期信息
   */
  static getCurrentSemester(): SemesterInfo {
    const now = new Date();
    const currentYear = now.getFullYear();
    const currentMonth = now.getMonth() + 1;
    
    const semester = this.getSemesterByMonth(currentMonth);
    const displayName = this.formatSemesterDisplayName(currentYear, semester);
    const shortName = this.formatSemesterShortName(currentYear, semester);
    
    return {
      year: currentYear,
      semester,
      displayName,
      shortName
    };
  }

  /**
   * 根据月份判断学期类型
   * @param month 月份（1-12）
   * @returns 学期类型（1: 春季, 2: 秋季）
   */
  static getSemesterByMonth(month: number): number {
    // 2-7月为春季(1)，9-1月为秋季(2)，8月为暑假默认秋季
    if (month >= 2 && month <= 7) {
      return this.SEMESTER_TYPE.SPRING;
    } else {
      return this.SEMESTER_TYPE.AUTUMN;
    }
  }

  /**
   * 格式化学期显示名称
   * @param year 年份
   * @param semester 学期类型（1: 春季, 2: 秋季）
   * @returns 格式化后的学期名称（如"2025春季学期"）
   */
  static formatSemesterDisplayName(year: number, semester: number): string {
    const typeName = this.SEMESTER_TYPE_NAMES[semester as keyof typeof this.SEMESTER_TYPE_NAMES];
    return `${year}${typeName}学期`;
  }

  /**
   * 格式化学期短名称
   * @param year 年份
   * @param semester 学期类型（1: 春季, 2: 秋季）
   * @returns 格式化后的学期短名称（如"2025春"）
   */
  static formatSemesterShortName(year: number, semester: number): string {
    const typeName = this.SEMESTER_TYPE_SHORT_NAMES[semester as keyof typeof this.SEMESTER_TYPE_SHORT_NAMES];
    return `${year}${typeName}`;
  }

  /**
   * 根据年份和学期创建学期信息
   * @param year 年份
   * @param semester 学期类型（1: 春季, 2: 秋季）
   * @returns 学期信息
   */
  static createSemesterInfo(year: number, semester: number): SemesterInfo {
    const displayName = this.formatSemesterDisplayName(year, semester);
    const shortName = this.formatSemesterShortName(year, semester);
    
    return {
      year,
      semester,
      displayName,
      shortName
    };
  }

  /**
   * 生成学期列表
   * @param startYear 开始年份
   * @param endYear 结束年份
   * @returns 学期信息列表（从最新到最旧排序）
   */
  static getSemesterList(startYear: number, endYear: number): SemesterInfo[] {
    const semesters: SemesterInfo[] = [];
    
    // 从结束年份开始，倒序生成学期列表
    for (let year = endYear; year >= startYear; year--) {
      // 添加秋季学期（秋季在上）
      semesters.push(this.createSemesterInfo(year, this.SEMESTER_TYPE.AUTUMN));
      
      // 添加春季学期（春季在下）
      semesters.push(this.createSemesterInfo(year, this.SEMESTER_TYPE.SPRING));
    }
    
    return semesters;
  }

  /**
   * 生成学期选项列表（用于下拉选择）
   * @param startYear 开始年份（默认2010）
   * @param endYear 结束年份（默认当前年份）
   * @returns 学期选项列表（从最新到最旧排序）
   */
  static getSemesterOptions(startYear: number = 2010, endYear: number = new Date().getFullYear()): SemesterOption[] {
    const semesters = this.getSemesterList(startYear, endYear);
    
    return semesters.map(semester => ({
      label: semester.displayName,
      value: semester.shortName,
      year: semester.year,
      semester: semester.semester
    }));
  }

  /**
   * 解析学期字符串
   * @param semesterStr 学期字符串（如"2025春"或"2025春季学期"）
   * @returns 解析后的学期信息，解析失败返回null
   */
  static parseSemester(semesterStr: string): SemesterInfo | null {
    // 匹配短名称格式：2025春
    let match = semesterStr.match(/^(\d{4})(春|秋)$/);
    if (match) {
      const year = parseInt(match[1]);
      const semester = match[2] === '春' ? this.SEMESTER_TYPE.SPRING : this.SEMESTER_TYPE.AUTUMN;
      return this.createSemesterInfo(year, semester);
    }
    
    // 匹配完整名称格式：2025春季学期
    match = semesterStr.match(/^(\d{4})(春季|秋季)学期$/);
    if (match) {
      const year = parseInt(match[1]);
      const semester = match[2] === '春季' ? this.SEMESTER_TYPE.SPRING : this.SEMESTER_TYPE.AUTUMN;
      return this.createSemesterInfo(year, semester);
    }
    
    return null;
  }

  /**
   * 验证学期字符串格式
   * @param semesterStr 学期字符串
   * @returns 是否为有效的学期格式
   */
  static isValidSemesterFormat(semesterStr: string): boolean {
    return /^\d{4}(春|秋)$/.test(semesterStr) || /^\d{4}(春季|秋季)学期$/.test(semesterStr);
  }

  /**
   * 获取学期类型名称
   * @param semester 学期类型（1: 春季, 2: 秋季）
   * @returns 学期类型名称
   */
  static getSemesterTypeName(semester: number): string {
    return this.SEMESTER_TYPE_NAMES[semester as keyof typeof this.SEMESTER_TYPE_NAMES] || '';
  }

  /**
   * 获取学期类型短名称
   * @param semester 学期类型（1: 春季, 2: 秋季）
   * @returns 学期类型短名称
   */
  static getSemesterTypeShortName(semester: number): string {
    return this.SEMESTER_TYPE_SHORT_NAMES[semester as keyof typeof this.SEMESTER_TYPE_SHORT_NAMES] || '';
  }

  /**
   * 获取当前学期的学期类型（1/2）
   * @returns 当前学期类型
   */
  static getCurrentSemesterType(): number {
    const now = new Date();
    const currentMonth = now.getMonth() + 1;
    return this.getSemesterByMonth(currentMonth);
  }

  /**
   * 获取当前学期的显示名称
   * @returns 当前学期显示名称
   */
  static getCurrentSemesterDisplayName(): string {
    const currentSemester = this.getCurrentSemester();
    return currentSemester.displayName;
  }

  /**
   * 获取当前学期的短名称
   * @returns 当前学期短名称
   */
  static getCurrentSemesterShortName(): string {
    const currentSemester = this.getCurrentSemester();
    return currentSemester.shortName;
  }

  /**
   * 学期状态枚举
   */
  static readonly SEMESTER_STATUS = {
    PREPARING: 'preparing',  // 准备中
    ONGOING: 'ongoing',      // 进行中
    COMPLETED: 'completed'   // 已结课
  } as const;

  /**
   * 判断学期状态
   * @param year 学期年份
   * @param semester 学期类型（1: 春季, 2: 秋季）
   * @returns 学期状态
   */
  static getSemesterStatus(year: number, semester: number): string {
    const currentSemester = this.getCurrentSemester();
    const currentYear = currentSemester.year;
    const currentSemesterType = currentSemester.semester;

    // 比较年份和学期类型
    if (year > currentYear) {
      // 未来年份
      return this.SEMESTER_STATUS.PREPARING;
    } else if (year < currentYear) {
      // 过去年份
      return this.SEMESTER_STATUS.COMPLETED;
    } else {
      // 同一年，比较学期类型
      if (semester > currentSemesterType) {
        // 未来学期（秋季 > 春季）
        return this.SEMESTER_STATUS.PREPARING;
      } else if (semester < currentSemesterType) {
        // 过去学期（春季 < 秋季）
        return this.SEMESTER_STATUS.COMPLETED;
      } else {
        // 当前学期
        return this.SEMESTER_STATUS.ONGOING;
      }
    }
  }

  /**
   * 获取学期状态显示名称
   * @param status 学期状态
   * @returns 状态显示名称
   */
  static getSemesterStatusDisplayName(status: string): string {
    switch (status) {
      case this.SEMESTER_STATUS.PREPARING:
        return '准备中';
      case this.SEMESTER_STATUS.ONGOING:
        return '进行中';
      case this.SEMESTER_STATUS.COMPLETED:
        return '已结课';
      default:
        return '未知';
    }
  }

  /**
   * 根据年份和学期生成学年显示名称
   * @param year 学年
   * @param semester 学期类型（1: 春季, 2: 秋季）
   * @returns 学年显示名称（如"2024-2025学年"）
   */
  static formatAcademicYear(year: number, semester: number): string {
    // 春季学期属于上一学年，秋季学期属于当前学年
    const academicYear = semester === this.SEMESTER_TYPE.SPRING ? 
      `${year - 1}-${year}学年` : 
      `${year}-${year + 1}学年`;
    return academicYear;
  }

  /**
   * 根据年份和学期生成完整学期名称
   * @param year 学年
   * @param semester 学期类型（1: 春季, 2: 秋季）
   * @returns 完整学期名称（如"2024-2025学年春季学期"）
   */
  static formatFullSemesterName(year: number, semester: number): string {
    const academicYear = this.formatAcademicYear(year, semester);
    const semesterName = this.getSemesterTypeName(semester);
    return `${academicYear}${semesterName}`;
  }

  /**
   * 验证学期参数
   * @param year 年份
   * @param semester 学期类型
   * @returns 是否为有效的学期参数
   */
  static isValidSemester(year: number, semester: number): boolean {
    return year > 0 && (semester === this.SEMESTER_TYPE.SPRING || semester === this.SEMESTER_TYPE.AUTUMN);
  }

  /**
   * 获取下一个学期
   * @param year 当前年份
   * @param semester 当前学期类型（1: 春季, 2: 秋季）
   * @returns 下一个学期信息
   */
  static getNextSemester(year: number, semester: number): SemesterInfo {
    if (semester === this.SEMESTER_TYPE.SPRING) {
      // 春季的下一个学期是秋季（同一年）
      return this.createSemesterInfo(year, this.SEMESTER_TYPE.AUTUMN);
    } else {
      // 秋季的下一个学期是春季（下一年）
      return this.createSemesterInfo(year + 1, this.SEMESTER_TYPE.SPRING);
    }
  }

  /**
   * 获取上一个学期
   * @param year 当前年份
   * @param semester 当前学期类型（1: 春季, 2: 秋季）
   * @returns 上一个学期信息
   */
  static getPreviousSemester(year: number, semester: number): SemesterInfo {
    if (semester === this.SEMESTER_TYPE.AUTUMN) {
      // 秋季的上一个学期是春季（同一年）
      return this.createSemesterInfo(year, this.SEMESTER_TYPE.SPRING);
    } else {
      // 春季的上一个学期是秋季（上一年）
      return this.createSemesterInfo(year - 1, this.SEMESTER_TYPE.AUTUMN);
    }
  }

  /**
   * 获取学年列表（如[2025, 2024, ...]，从2010到当前年+1，降序）
   * @param startYear 起始年份，默认2010
   * @returns 学年年份数组
   */
  static getAcademicYearList(startYear: number = 2010): number[] {
    const currentYear = new Date().getFullYear();
    const endYear = currentYear + 1;
    const years: number[] = [];
    for (let y = endYear; y >= startYear; y--) {
      years.push(y);
    }
    return years;
  }
}

// 导出默认实例
export default SemesterUtils; 
// 获取常用时间
import dayjs from 'dayjs';

export const LAST_7_DAYS = [
  dayjs().subtract(7, 'day').format('YYYY-MM-DD'),
  dayjs().subtract(1, 'day').format('YYYY-MM-DD'),
];

export const LAST_30_DAYS = [
  dayjs().subtract(30, 'day').format('YYYY-MM-DD'),
  dayjs().subtract(1, 'day').format('YYYY-MM-DD'),
];

/**
 * 格式化日期时间
 * @param dateString ISO格式的日期字符串或Date对象
 * @param format 目标格式，默认为YYYY-MM-DD HH:mm:ss
 * @returns 格式化后的日期字符串
 */
export function formatDate(dateString: string | Date | undefined | null, format = 'YYYY-MM-DD HH:mm:ss'): string {
  if (!dateString) return '';
  return dayjs(dateString).format(format);
}
